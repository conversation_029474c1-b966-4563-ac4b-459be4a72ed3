<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="styles/mui.picker.all.css" />
    <link rel="stylesheet" href="styles/swiper.min.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="js/jquery.min.js"></script>
</head>

<body>

    <header>
        <div class="header-box">
            <a href="" class="btn-back"></a>
            <h3>求职意向</h3>
        </div>
    </header>
    <div class="g-header-space">
        <div class="header-space"></div>
    </div>

    <section class="uc-wrap uc-intention">



        <ul class="form">
            <li>
                <div class="txt">期望职位</div>
                <input type="text" class="uc-input" placeholder="请输入 最多15字符（选填）" value="" />
            </li>
            <li>
                <div class="txt">期望地点</div>
                <input type="text" class="uc-input" placeholder="请输入 最多15字符（选填）" value="" />
            </li>
            <li>
                <div class="txt">期望薪资</div>
                <div class="con">
                    <input type="text" class="uc-input" placeholder="请选择（选填）" value="" readonly id="useData001" />
                    <div class="line">——</div>
                    <input type="text" class="uc-input" placeholder="请选择（选填）" value="" readonly id="useData002" />
                    <a href="" class="btn-delete"><img src="images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
            </li>
            <li>
                <div class="txt">求职状态</div>
                <input type="text" class="uc-input" placeholder="请输入（选填）" value="" />
            </li>
        </ul>




        <div class="g-fixedOperate">
            <a href="" class="uc-btn blue">保存信息</a>
            <a href="" class="uc-btn gray">删除</a>
        </div>

    </section>



    <script src="js/swiper.min.js"></script>
    <script src="js/mui.min.js"></script>
    <script src="js/mui.picker.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        $(function() {})



        var userPicker001 = new mui.PopPicker();
        userPicker001.setData([{
                value: 'ywj0',
                text: '面议'
            },
            {
                value: 'ywj',
                text: '2k'
            },
            {
                value: 'aaa',
                text: '3k'
            },
            {
                value: 'aaa1',
                text: '5k'
            },
            {
                value: 'aaa2',
                text: '8k'
            },
            {
                value: 'aaa3',
                text: '10k'
            },
        ]);
        $("body").on("click", "#useData001", function() {
            setTimeout(function() {
                userPicker001.show(function(items) {
                    console.log(items[0]);
                    $("#useData001").val(items[0].text);
                });
            }, 200);
        });
        $("body").on("click", "#useData002", function() {
            setTimeout(function() {
                userPicker001.show(function(items) {
                    console.log(items[0]);
                    $("#useData002").val(items[0].text);
                });
            }, 200);
        });

        $("body").on('click', '.btn-delete', function(event) {
            event.preventDefault();
            $(this).siblings('.uc-input').val('')
        });
    </script>
</body>

</html>