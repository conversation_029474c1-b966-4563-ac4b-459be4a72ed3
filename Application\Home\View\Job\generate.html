<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="styles/mui.picker.all.css" />
    <link rel="stylesheet" href="styles/swiper.min.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="js/jquery.min.js"></script>
</head>

<body>
    <header>
        <div class="header-box">
            <a href="" class="btn-back"></a>
            <h3>我的简历</h3>
        </div>
    </header>
    <div class="g-header-space">
        <div class="header-space"></div>
    </div>
    <section class="uc-wrap uc-generate">
        <div class="img-box">
            <img src="images/generate-img-1.jpg" alt="" />
        </div>
        <div class="fixed-operate">
            <div class="swiper-box">
                <div class="swiper-container swiper-container1">
                    <ul class="swiper-wrapper">
                        <li class="swiper-slide"><img src="images/generate-img-1.jpg" alt="" /></li>
                        <li class="swiper-slide"><img src="images/generate-img-1.jpg" alt="" /></li>
                        <li class="swiper-slide"><img src="images/generate-img-1.jpg" alt="" /></li>
                        <li class="swiper-slide"><img src="images/generate-img-1.jpg" alt="" /></li>
                        <li class="swiper-slide"><img src="images/generate-img-1.jpg" alt="" /></li>
                        <li class="swiper-slide"><img src="images/generate-img-1.jpg" alt="" /></li>
                        <li class="swiper-slide"><img src="images/generate-img-1.jpg" alt="" /></li>
                    </ul>
                </div>
            </div>
            <div class="operate">
                <a href="" class="uc-btn white-plain">开启封面</a>
                <a href="" class="uc-btn white-plain">主题色</a>
                <a href="" class="uc-btn white-plain">重命名</a>
                <a href="" class="uc-btn white-plain">字号间距</a>
                <a href="" class="uc-btn white">下载文档</a>
            </div>
        </div>
    </section>


    <div class="uc-alert uc-alert-chooseSkin uc-show">
        <div class="over-close"></div>
        <div class="box-bottom">
            <div class="title">选择简历主题颜色 <a href="" class="btn-close"><i class="uc-font uc-close"></i></a></div>
            <div class="choose">
                <a href="" class="item" style="background:#35394b"></a>
                <a href="" class="item" style="background:#3363ab"></a>
                <a href="" class="item" style="background:#55707f"></a>
                <a href="" class="item" style="background:#ce4649"></a>
                <a href="" class="item" style="background:#558bb8"></a>
            </div>
        </div>
    </div>


    <div class="uc-alert uc-alert-rename uc-show">
        <div class="over-close"></div>
        <div class="box-bottom">
            <div class="title">重命名简历名称（不要加/符号） <a href="" class="btn-close"><i class="uc-font uc-close"></i></a></div>
            <div class="con">
                <input type="text" class="uc-input" placeholder="青输入简历名称（不要加/符号）" value="" />
            </div>
            <div class="operate">
                <a href="" class="uc-btn blue">保存信息</a>
            </div>
        </div>
    </div>


    <div class="uc-alert uc-alert-chooseParam uc-show">
        <div class="over-close"></div>
        <div class="box-bottom">
            <div class="title">字号间距 <a href="" class="btn-close"><i class="uc-font uc-close"></i></a></div>
            <div class="sub-title">字体大小</div>
            <ul class="choose">
                <li>最小</li>
                <li>较小</li>
                <li class="on">标准</li>
                <li>较大</li>
                <li>最大</li>
            </ul>
            <div class="sub-title">行间距</div>
            <ul class="choose">
                <li>最小</li>
                <li>较小</li>
                <li class="on">标准</li>
                <li>较大</li>
                <li>最大</li>
            </ul>
            <div class="sub-title">模块间距</div>
            <ul class="choose">
                <li>最小</li>
                <li>较小</li>
                <li class="on">标准</li>
                <li>较大</li>
                <li>最大</li>
            </ul>
        </div>
    </div>

    <script src="js/swiper.min.js"></script>
    <script src="js/mui.min.js"></script>
    <script src="js/mui.picker.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        $(function() {})
        new Swiper('.swiper-container1', {
            slidesPerView: 4.5,
            loop: true,
            observeParents: true,
            observer: true,
            spaceBetween: 10,
        });

        $("body").on('click', '.uc-alert-chooseParam .choose li', function(event) {
            event.preventDefault();
            $(this).addClass('on').siblings().removeClass('on')
        });
    </script>
</body>

</html>