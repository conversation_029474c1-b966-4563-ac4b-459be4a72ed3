<include file="block/hat" />

<head>
    <style>
        .bank-info-detail {
            line-height: 1.8;
            padding: 5px 0;
        }
        .bank-info-detail div {
            padding: 3px 0;
        }
        .bank-info-detail strong {
            color: #555;
            display: inline-block;
            min-width: 80px;
        }
    </style>
</head>

<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
             <!-- Main content start -->
            <div class="wrap js-check-wrap">
                <ul class="nav nav-tabs">
                    <li><a href="{:U('Withdrawal/index')}">提现审核列表</a></li>
                    <li class="active"><a href="#">提现详情与处理</a></li>
                </ul>
                
                <div class="panel panel-default">
                    <div class="panel-heading">提现申请详情</div>
                    <div class="panel-body">
                        <table class="table table-bordered">
                            <tr>
                                <th width="15%">申请ID</th><td width="35%">{$info.id}</td>
                                <th width="15%">当前状态</th><td>{$statusList[$info['status']]['text']|default=''}</td>
                            </tr>
                            <tr>
                                <th>服务站名称</th><td>{$info.service_name|default=''} (ID: {$info.service_station_id|default=''})</td>
                                <th>服务站当前余额</th><td>总额: {$info.station_total_price|default=0} / 可用: {$info.station_price|default=0} / 冻结: {$info.station_freeze_price|default=0}</td>
                            </tr>
                            <tr>
                                <th>申请用户</th><td>{$info.requester_name|default=''} (ID: {$info.user_id|default=''})</td>
                                <th>用户手机</th><td>{$info.requester_mobile|default='-'}</td>
                            </tr>
                             <tr>
                                <th>申请提现金额</th><td><strong style="color: red; font-size: 16px;">¥ {$info.amount|default=0}</strong></td>
                                <th>申请时间</th>
                                <td>
                                    <php>
                                    if(!empty($info['request_time'])) {
                                        echo date('Y-m-d H:i:s', $info['request_time']);
                                    } else {
                                        echo '-';
                                    }
                                    </php>
                                </td>
                            </tr>
                            <tr>
                                <th>技术服务费(8%)</th><td><strong style="color: #666; font-size: 14px;">¥ {$info.service_fee|default='0.00'}</strong></td>
                                <th>实际到账金额</th>
                                <td><strong style="color: #ff6b00; font-size: 16px;">¥ {$info.actual_amount|default=$info['amount']}</strong></td>
                            </tr>
                            <tr>
                                <!-- Conditional Title -->
                                <th>
                                    <if condition="$info.platform_type eq 'linggong'">
                                        灵工账户信息
                                    <elseif condition="$info.platform_type eq 'bank'" />
                                        银行卡信息
                                    <else />
                                        账户信息
                                    </if>
                                </th>
                                <td colspan="3">
                                    <div class="bank-info-detail">
                                        <if condition="$info.platform_type eq 'linggong'">
                                            <!-- Linggong Platform Info -->
                                            <div><strong>平台名称：</strong>{$linggongPlatformTypes[$info['linggong_platform_type']] ?? '未知平台'}</div>
                                            <div><strong>结算银行：</strong>{$info.linggong_bank_name|default='-'}</div>
                                            <div><strong>账号后四位：</strong>{$info.linggong_last4|default='-'}</div>
                                            <div><strong>灵工姓名：</strong>{$info.linggong_worker_name|default='-'}</div>
                                            <div><strong>手机号：</strong>{$info.linggong_phone|default='-'}</div>
                                            <div><strong>身份证号：</strong>{$info.linggong_id_card|default='-'}</div> <!-- Prime backend might show full ID -->
                                        <elseif condition="$info.platform_type eq 'bank'" />
                                            <!-- Bank Card Info (Original) -->
                                            <div><strong>开户银行：</strong>{$info.bank_name|default=''}</div>
                                            <div><strong>开户支行：</strong>{$info.bank_branch|default='-'}</div>
                                            <div><strong>银行卡号：</strong>{$info.card_number_display|default=''}</div>
                                            <div><strong>开户人姓名：</strong>{$info.account_holder|default=''}</div>
                                        <else />
                                            <!-- Unknown Type -->
                                            <div>未知的账户类型</div>
                                        </if>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th>税费处理方式</th>
                                <td colspan="3">
                                    <!-- Flex container for two columns -->
                                    <div style="display: flex; justify-content: space-between; width: 100%;">
                                        <!-- Left Column: Tax Handling Info -->
                                        <div class="tax-handling-left" style="flex-basis: 48%; padding-right: 1%;">
                                            <php>if(isset($info['tax_handling']) && isset($taxHandlingList[$info['tax_handling']])):</php>
                                            <span class="label label-{$taxHandlingList[$info['tax_handling']]['style']}">
                                                {$taxHandlingList[$info['tax_handling']]['text']}
                                            </span>
                                            
                                            <!-- 税费分配详情 -->
                                            <div style="margin-top: 10px; padding: 12px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #17a2b8;">
                                                <p><strong>税费分配详情：</strong></p>
                                                <p>平台负责完税金额：¥{$info.platform_tax_amount|default='0.00'}</p>
                                                <p>用户需开票金额：¥{$info.user_invoice_amount|default='0.00'}</p>
                                                <p>提现申请总金额：¥{$info.amount|default='0.00'}</p>
                                            </div>
                                            
                                            <!-- 申请时额度使用情况 -->
                                            <div class="quota-info-box" style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                                                <p><strong>申请时额度使用情况：</strong></p>
                                                <p>总额度：¥{$info.application_quota_total|default=$quota_info.total_quota}</p>
                                                <p>已使用：¥{$info.application_quota_used|default=$quota_info.used_amount}</p>
                                                <p>剩余额度：¥{$info.application_quota_remaining|default=$quota_info.remaining_quota}</p>
                                                <php>if($info['tax_handling'] == 2):</php>
                                                <p class="text-danger" style="margin-top: 5px;">提示：该提现申请已超出每月9.8万额度，需联系用户开具发票。</p>
                                                <php>elseif($info['tax_handling'] == 3):</php>
                                                <p class="text-warning" style="margin-top: 5px;">提示：该提现申请部分使用平台完税额度，部分需用户开具发票。</p>
                                                <php>endif;</php>
                                            </div>
                                            <php>else:</php>
                                            未设置
                                            <php>endif;</php>
                                        </div>
                                        
                                        <!-- Right Column: Money Details -->
                                        <div class="money-details-right" style="flex-basis: 48%; padding-left: 1%; border-left: 1px solid #eee; max-height: 300px; overflow-y: auto;">
                                            <p style="font-weight: bold; margin-bottom: 5px;">服务站资金明细 (全部)</p>
                                            <if condition="!empty($moneyDetails)">
                                                <table class="table table-condensed table-striped" style="margin-bottom: 0; font-size: 12px;">
                                                    <thead>
                                                        <tr>
                                                            <th style="width: 130px; padding: 4px;">时间</th>
                                                            <th style="width: 70px; padding: 4px;">类型</th>
                                                            <th style="width: 90px; padding: 4px;">金额</th>
                                                            <th style="padding: 4px;">备注</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <foreach name="moneyDetails" item="detail">
                                                        <tr>
                                                            <td style="padding: 4px;">{:date('Y-m-d H:i', $detail['create_time'])}</td>
                                                            <td style="padding: 4px;">{$stationMoneyTypes[$detail['type']]['text'] ?? '未知'}</td>
                                                            <td style="padding: 4px;">
                                                                <php>if($detail['type'] == 1): // 类型1:提现 (支出)</php>
                                                                    <span style="color: red; font-weight: bold;">- {$detail.money|number_format=2}</span>
                                                                <php>else: // 其他类型视为收入</php>
                                                                    <span style="color: green; font-weight: bold;">+ {$detail.money|number_format=2}</span>
                                                                <php>endif;</php>
                                                            </td>
                                                            <td style="padding: 4px;">{$detail.remark|default='-'}</td>
                                                        </tr>
                                                        </foreach>
                                                    </tbody>
                                                </table>
                                            <else />
                                                <p style="padding: 10px 0; color: #888;">暂无资金明细记录</p>
                                            </if>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th>审核人</th><td>{$info.reviewer_name|default='-'}</td>
                                <th>审核时间</th>
                                <td>
                                    <php>
                                    if(!empty($info['review_time'])) {
                                        echo date('Y-m-d H:i:s', $info['review_time']);
                                    } else {
                                        echo '-';
                                    }
                                    </php>
                                </td>
                            </tr>
                             <tr>
                                <th>审核备注</th><td colspan="3">{$info.review_remark|default='-'}</td>
                            </tr>
                            <php>if(!empty($info['invoice_remark'])):</php>
                            <tr>
                                <th>开票历史</th><td colspan="3" style="color:#e74c3c;">该申请曾要求开票，但已取消。备注：{$info.invoice_remark|default='-'}</td>
                            </tr>
                            <php>endif;</php>
                            
                            <!-- 打款相关信息只在状态为4(打款完成)或5(打款失败)时显示 -->
                            <php>if($info['status'] == 4 || $info['status'] == 5):</php>
                             <tr>
                                <th>打款操作人</th><td>{$info.payer_name|default='-'}</td>
                                <th>打款时间</th>
                                <td>
                                    <php>
                                    if(!empty($info['payment_time'])) {
                                        echo date('Y-m-d H:i:s', $info['payment_time']);
                                    } else {
                                        echo '-';
                                    }
                                    </php>
                                </td>
                            </tr>
                            <tr>
                                <th>打款备注/流水</th><td colspan="3">{$info.payment_remark|default='-'}</td>
                            </tr>
                            <php>endif;</php>
                        </table>
                    </div>
                </div>

                <!-- 恢复审核操作表单 - 使用PHP语法块进行条件判断 -->
                <php>if($info['status'] == 1):</php>
                <div class="panel panel-warning">
                    <div class="panel-heading">审核操作</div>
                    <div class="panel-body">
                        <form class="js-ajax-form" action="{:U('Withdrawal/do_review')}" method="post">
                            <input type="hidden" name="id" value="{$info.id}">
                            <div class="form-group">
                                <label for="review_remark">审核备注 (驳回时必填)</label>
                                <textarea name="review_remark" id="review_remark" class="form-control" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <button type="submit" name="action" value="approve" class="btn btn-success js-ajax-submit">审核通过 (设为待打款)</button>
                                <button type="submit" name="action" value="invoice" class="btn btn-primary js-ajax-submit" style="margin-left: 10px;">需要开票</button>
                                <button type="submit" name="action" value="reject" class="btn btn-danger js-ajax-submit" style="margin-left: 10px;">审核驳回</button>
                            </div>
                        </form>
                    </div>
                </div>
                <php>endif;</php>

                <!-- 恢复打款操作表单 - 使用PHP语法块进行条件判断 -->
                <php>if($info['status'] == 2):</php>
                <div class="panel panel-info">
                    <div class="panel-heading">打款操作 (财务手动打款后在此标记)</div>
                    <div class="panel-body">
                        <form class="js-ajax-form" action="{:U('Withdrawal/do_payment')}" method="post">
                            <input type="hidden" name="id" value="{$info.id}">
                            <div class="form-group">
                                <label for="payment_remark">打款备注 (打款流水号 或 失败原因)</label>
                                <textarea name="payment_remark" id="payment_remark" class="form-control" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <button type="submit" name="action" value="paid" class="btn btn-primary js-ajax-submit">标记为打款完成</button>
                                <button type="submit" name="action" value="failed" class="btn btn-warning js-ajax-submit" style="margin-left: 10px;">标记为打款失败</button>
                            </div>
                        </form>
                    </div>
                </div>
                <php>endif;</php>

                <!-- 待开票状态操作表单 - 使用PHP语法块进行条件判断 -->
                <php>if($info['status'] == 6):</php>
                <div class="panel panel-primary">
                    <div class="panel-heading">开票操作 (等待用户开具发票)</div>
                    <div class="panel-body">
                        <form class="js-ajax-form" action="{:U('Withdrawal/do_invoice')}" method="post">
                            <input type="hidden" name="id" value="{$info.id}">
                            <div class="form-group">
                                <label for="invoice_remark">开票备注</label>
                                <textarea name="invoice_remark" id="invoice_remark" class="form-control" rows="3" placeholder="可填写发票收件信息、开票金额等"></textarea>
                            </div>
                            <div class="form-group">
                                <button type="submit" name="action" value="received" class="btn btn-success js-ajax-submit">已收到发票 (转为待打款)</button>
                                <button type="submit" name="action" value="cancel" class="btn btn-danger js-ajax-submit" style="margin-left: 10px;">取消开票要求</button>
                            </div>
                        </form>
                    </div>
                </div>
                <php>endif;</php>
  
                <div style="margin-top: 20px;">
                    <a href="{:U('Withdrawal/index')}" class="btn btn-default">返回列表</a>
                </div>

            </div>
            <!-- Main content end -->
         </div>
	</div>
</div>

<include file="block/footer" /> 