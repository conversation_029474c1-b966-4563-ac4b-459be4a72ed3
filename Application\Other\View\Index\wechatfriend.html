<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>扫码添加微信好友</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <style type="text/css">
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
        }
        
        body {
            background-color: #f7f7f7;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }
        
        .qrcode-card {
            background: #fff;
            border-radius: 10px;
            padding: 30px 20px;
            margin: 20px auto;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .qrcode-img {
            margin: 0 auto 20px;
            width: 100%;
            max-width: 300px;
        }
        
        .qrcode-img img {
            width: 100%;
            height: auto;
            border-radius: 5px;
        }
        
        .instruction {
            color: #333;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .tip-text {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-top: 20px;
        }
        
        .footer {
            color: #999;
            font-size: 12px;
            margin-top: 30px;
            text-align: center;
        }
        
        /* 添加分享卡片样式 */
        .share-card {
            position: fixed;
            top: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 10px 15px;
            border-radius: 0 0 0 10px;
            font-size: 14px;
            display: none;
            z-index: 999;
        }
        
        .share-card.active {
            display: block;
            animation: fadeIn 0.5s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .share-icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            background-size: contain;
            background-repeat: no-repeat;
            vertical-align: middle;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="qrcode-card">
            <div class="qrcode-img">
                <img src="{$qrcode.image_path}" alt="微信好友二维码">
            </div>
            
            <div class="instruction">
                ▲ 长按识别二维码添加微信好友 ▲
            </div>
            
            <div class="tip-text">
                {$qrcode.tip_text}
            </div>
        </div>
        
        <div class="footer">
            中才国科就业服务
        </div>
    </div>
    

    
    <!-- 微信JS-SDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    
    <script>


        // 设置分享参数
        var shareTitle = '{$shareTitle}';
        var shareDesc = '{$shareDesc}';
        var shareLink = window.location.href;
        var shareImgUrl = '{$shareImgUrl}';
        
        wx.config({
            debug: false, // 关闭调试模式
            appId: '{$appid}',
            timestamp: {:$wxconf['timestamp']?:0},
            nonceStr: '{$wxconf.noncestr}',
            signature: '{$wxconf.signature}',
            jsApiList: [
                'updateAppMessageShareData', 
                'onMenuShareTimeline',
                'onMenuShareAppMessage',
            ]
        });
        
        wx.ready(function () {
            
            // 分享给朋友（新版）
            wx.updateAppMessageShareData({ 
                title: shareTitle,
                desc: shareDesc,
                link: shareLink,
                imgUrl: shareImgUrl,
                success: function () {
                    console.log('分享内容设置成功');
                }
            });
            
            // 分享到朋友圈（兼容旧版）
            wx.onMenuShareTimeline({
                title: shareTitle,
                link: shareLink,
                imgUrl: shareImgUrl,
                success: function () {
                    console.log('分享到朋友圈设置成功');
                }
            });
            
            // 分享给朋友（兼容旧版）
            wx.onMenuShareAppMessage({
                title: shareTitle,
                desc: shareDesc,
                link: shareLink,
                imgUrl: shareImgUrl,
                type: '', 
                dataUrl: '',
                success: function () {
                    console.log('分享给朋友设置成功');
                }
            });
            
           
        });
        
        wx.error(function (res) {
            console.error('微信JS-SDK配置失败:', res);
        });
    </script>
</body>
</html> 