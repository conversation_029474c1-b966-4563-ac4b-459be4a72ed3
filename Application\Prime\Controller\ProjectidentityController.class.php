<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 项目管理
 * Class ProjectController
 * @package Prime\Controller
 */
class ProjectidentityController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    public function index() {
        $c_kw = [
            'name' => '身份名称',
            'id' => 'ID',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);
        if ($s_status != '') $where['status'] = $s_status;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['name']))  {
                $where[$s_kw] = ['like', "%$s_val%"];
            }  else {
                $where[$s_kw] = $s_val;
            }
        }
        //公用搜索
        $contions = I('get.');
        $contionsWhere = ['group', 'is_out'];
        foreach ($contions as $whereKey => $row) {
            if (in_array($whereKey, $contionsWhere) && $row != '') {
                $where[$whereKey] = $row;
            }
        }

        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['create_time'] = ['between', [$s_start, $s_end]];
        }

        $obj = D("ProjectIdentity");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        if ($list) {
            $pidArr = array_unique(array_column($list, 'pid'));
            if ($pidArr) {
                $projectIdentityList = D("ProjectIdentity")->where(['id' => ['in', $pidArr]])->getField('id,name', true);
                $this->assign('projectIdentity', $projectIdentityList);
            }
        }

        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '0');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '0');
        $this->assign('groupList', $obj->group);
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('statusList', $obj->status);
        $this->assign('isOutList', $obj->is_out);
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 添加编辑
     */
    public function edit() {
        $id = intval(I('get.id'));
        $obj = D("ProjectIdentity");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        }
        if (IS_POST) {
            if ($data = $obj->create()) {
                if ($data['pid'] == '') {
                    $this->error('请选择正确的隶属身份');
                }
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("projectidentity/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $pidlist = $obj->where(['status' => 1])->getField('id,name', true);
        $pidlist[0] = '属于总部';
        $this->assign('pidList', $pidlist);
        $this->display();
    }
}