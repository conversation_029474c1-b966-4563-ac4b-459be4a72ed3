<?php
namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Db;
use think\facade\Session;
use app\model\BankCard;
use app\model\LinggongBinding;
use app\model\WithdrawalRequest;

class MoneyCardController extends BaseController
{
    /**
     * 显示统一提现页面
     */
    public function index()
    {
        $serviceStationId = Session::get('service_station_id');
        $userId = Session::get('user_id');
        
        // 获取银行卡信息
        $bankCards = Db::name('bank_card')
            ->where('service_station_id', $serviceStationId)
            ->where('status', 1)
            ->select()
            ->toArray();
            
        // 获取平台账户绑定信息
        $platformBindings = Db::name('linggong_binding')
            ->where('service_station_id', $serviceStationId)
            ->where('status', 1)
            ->select()
            ->toArray();
            
        // 获取可提现金额
        $availableAmount = $this->getAvailableAmount($serviceStationId);
        
        // 获取月度提现配额使用情况
        $withdrawalQuota = $this->getWithdrawalQuota($serviceStationId);
        
        View::assign([
            'bankCards' => $bankCards,
            'platformBindings' => $platformBindings,
            'availableAmount' => $availableAmount,
            'withdrawalQuota' => $withdrawalQuota
        ]);
        
        return View::fetch('money_card');
    }
    
    /**
     * 处理提现请求
     */
    public function withdraw()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $serviceStationId = Session::get('service_station_id');
        $userId = Session::get('user_id');
        $amount = $this->request->post('amount/f');
        $platformType = $this->request->post('platform_type', 'bank');
        
        // 验证提现金额
        if ($amount <= 0) {
            return json(['code' => 0, 'msg' => '提现金额必须大于0']);
        }
        
        // 获取可提现金额
        $availableAmount = $this->getAvailableAmount($serviceStationId);
        if ($amount > $availableAmount) {
            return json(['code' => 0, 'msg' => '提现金额不能大于可提现金额']);
        }
        
        // 根据提现方式处理
        if ($platformType == 'bank') {
            // 银行卡提现
            $bankCardId = $this->request->post('bank_card_id/d');
            if (empty($bankCardId)) {
                return json(['code' => 0, 'msg' => '请选择银行卡']);
            }
            
            // 检查银行卡是否存在
            $bankCard = Db::name('bank_card')
                ->where('id', $bankCardId)
                ->where('service_station_id', $serviceStationId)
                ->where('status', 1)
                ->find();
                
            if (empty($bankCard)) {
                return json(['code' => 0, 'msg' => '银行卡不存在或已被禁用']);
            }
            
            $linggongBindingId = 0;
        } else {
            // 平台提现
            $linggongBindingId = $this->request->post('linggong_binding_id/d');
            if (empty($linggongBindingId)) {
                return json(['code' => 0, 'msg' => '请选择平台账户']);
            }
            
            // 检查平台账户是否存在
            $platformBinding = Db::name('linggong_binding')
                ->where('id', $linggongBindingId)
                ->where('service_station_id', $serviceStationId)
                ->where('status', 1)
                ->find();
                
            if (empty($platformBinding)) {
                return json(['code' => 0, 'msg' => '平台账户不存在或已被禁用']);
            }
            
            $bankCardId = 0;
        }
        
        // 创建提现请求
        $requestTime = time();
        $data = [
            'service_station_id' => $serviceStationId,
            'user_id' => $userId,
            'amount' => $amount,
            'bank_card_id' => $bankCardId,
            'platform_type' => $platformType,
            'linggong_binding_id' => $linggongBindingId,
            'status' => 1, // 待审核
            'tax_handling' => 1, // 默认税务处理方式
            'request_time' => $requestTime,
            'apply_quota_total' => 98000.00, // 默认配额
            'application_quota_total' => 98000.00,
            'application_quota_remaining' => 98000.00,
        ];
        
        // 计算服务费和实际到账金额
        $serviceFeeRate = 0.08; // 8%服务费
        $serviceFee = $amount * $serviceFeeRate;
        $actualAmount = $amount - $serviceFee;
        
        $data['service_fee'] = $serviceFee;
        $data['actual_amount'] = $actualAmount;
        
        // 保存提现请求
        $withdrawalId = Db::name('withdrawal_request')->insertGetId($data);
        
        if (!$withdrawalId) {
            return json(['code' => 0, 'msg' => '提现申请失败，请稍后重试']);
        }
        
        return json(['code' => 1, 'msg' => '提现申请已提交，请等待审核']);
    }
    
    /**
     * 获取可提现金额
     */
    private function getAvailableAmount($serviceStationId)
    {
        // 这里应该根据实际业务逻辑获取可提现金额
        // 示例：从账户余额中减去已申请但未完成的提现金额
        $balance = Db::name('station_money')
            ->where('service_station_id', $serviceStationId)
            ->where('status', 1)
            ->sum('money');
            
        $pendingWithdrawal = Db::name('withdrawal_request')
            ->where('service_station_id', $serviceStationId)
            ->whereIn('status', [1, 2]) // 待审核和审核通过但未打款
            ->sum('amount');
            
        return max(0, $balance - $pendingWithdrawal);
    }
    
    /**
     * 获取月度提现配额使用情况
     */
    private function getWithdrawalQuota($serviceStationId)
    {
        // 获取当前月份的起始和结束时间
        $currentMonth = date('Y-m');
        $startTime = strtotime($currentMonth . '-01 00:00:00');
        $endTime = strtotime('+1 month', $startTime) - 1;
        
        // 获取本月已使用的提现配额
        $usedQuota = Db::name('withdrawal_request')
            ->where('service_station_id', $serviceStationId)
            ->where('request_time', '>=', $startTime)
            ->where('request_time', '<=', $endTime)
            ->where('status', '>', 0) // 非取消状态
            ->sum('amount');
            
        // 获取总配额
        $totalQuota = 98000.00; // 默认98000配额
        
        // 计算剩余配额
        $remainingQuota = max(0, $totalQuota - $usedQuota);
        
        return [
            'total' => $totalQuota,
            'used' => $usedQuota,
            'remaining' => $remainingQuota,
            'month' => $currentMonth,
        ];
    }
} 