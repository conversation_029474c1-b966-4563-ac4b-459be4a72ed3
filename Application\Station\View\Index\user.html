﻿<!DOCTYPE html>
<html >
<head>
<title>用户管理</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
<meta content="no-cache,must-revalidate" http-equiv="Cache-Control">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta content="no-cache" http-equiv="pragma">
<meta content="0" http-equiv="expires">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"> 
<link rel="stylesheet" type="text/css" href="/static/stations/css/css.css?v={:time()}" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
<script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
<script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
</head>
<body>
<include file="headers"/>

<php>if (empty($list)) {</php>
    <div class="bd">
        <ul>
    <li style="text-align: center;font-size: 13px;color: darkgray;padding: 20px 0;">暂无用户数据</li>
    </ul>
    </div>
    <php>}else{</php>   
<div class="bar03" style="margin: 10px;">
        <div class="weap">
            <div class="left"><form action="">
                <input class="input js_kwd" name="kwd" type="search" placeholder="搜微信昵称/备注" value="{$kwd}">
                <button class="inbtn"><i class="iconfont icon-31sousuo"></i></button>
            </form></div>
           
        </div>
    </div>

 
    <div class="screen01 mb10" style="margin: 10px;">
        <div class="weap">
            <ul>
                <li data-type="sortShowTime" class="{:$show_time == 2 ? 'ondown' : '' }{:$show_time == 1 ? 'onup' : '' }" >
                    查看时间<span class="arrow"><i class="iconfont icon-shangjiantou"></i><i class="iconfont icon-xiajiantou"></i></span>
                </li>
                <li data-type="sortShowNum" class="{:$show_num == 2 ? 'ondown' : '' } {:$show_num == 1 ? 'onup' : '' }">
                    查看次数<span class="arrow"><i class="iconfont icon-shangjiantou"></i><i class="iconfont icon-xiajiantou"></i></span>
                </li>
                <li style="width: 50%;"></li>
            </ul>
        </div>
    </div>
    <div class="list02 mb10" style="margin: 10px;">
        <ul id="users">
            
            <include file="list-user"/>
            
        </ul>
    </div>
    <php>}</php>

    <!--include file="footer"/-->

<script type="text/javascript">
var kwd = '{$kwd}';
var url = "{:U('index/user')}"
var sortShowTime = '{:$show_time}';
var sortShowNum = '{:$show_num}';
$(document).ready(function(){
    $(".tabs-hd02 ul li").click(function(){
        $(this).siblings().removeClass("on");
        $(this).addClass("on");
    });
    $(".tabs-hd02 ul li").click(function(){
        console.log('xxxxxxxxxx')
        $(this).siblings().removeClass("on");
        $(this).addClass("on");
    });

    $(".screen01 li").click(function(){
        var typeOnOrUp = 0;
        var dataType = $(this).attr('data-type');
        if($(this).is('.ondown')){
            $(this).removeClass('ondown');
            $(this).siblings().removeClass("ondown");
            $(this).siblings().removeClass('onup');
            $(this).addClass('onup');
            typeOnOrUp = 1;
        } else {
            $(this).removeClass('onup');
            $(this).siblings().removeClass("ondown");
            $(this).siblings().removeClass('onup');
            $(this).addClass('ondown');
            typeOnOrUp = 2;
        }
        if (dataType == 'sortShowTime') {
            sortShowNum = 0;
            sortShowTime = typeOnOrUp;
        } else {
            sortShowTime = 0;
            sortShowNum = typeOnOrUp;
        }
        window.location.href = url + '?kwd='+kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum;
        return
    });

    $('.js_kwd').blur(function () {
        var  new_kwd = $(this).val();
        if (new_kwd != kwd) {
            window.location.href = url + '?kwd='+new_kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum;
            return
        }
    })

    var noticeSwiper = new Swiper('.noticeSwiper', {
        direction: 'vertical',
        loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    })
    var noticeSwiper = new Swiper('.page0106Swiper', {
        pagination: {
            el: ".swiper-pagination",
        },

        loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    });

 
});
</script>
<script>
    $(function () {
        $('.money').on('click', '.status', function(){
            var tip = $(this).data('tip');
            if (tip) layer.open({content: tip});
        });
        $(window).trigger("scroll");
    });
    var page=1,pages=<?= (int)$page->Total_Pages ?>;
    function loadmore(){
        if(page<pages){
            page+=1;
            $.get('/index/user?p=' + page+'&kwd='+kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum, function(str){
                $('#users').append(str);
            }, 'html');
        } else if (page==pages) {
            page+=1;
            setTimeout("$('.container').append('<p class=\"nomore\">------ 没有了 ------</p>')", 500);
        }
    }
    $(window).scroll(function(){
        var scrollTop = $(this).scrollTop();     //滚动条距离顶部的高度
        var scrollHeight = $(document).height(); //当前页面的总高度
        var clientHeight = $(this).height();     //当前可视的页面高度
        // console.log("top:"+scrollTop+",doc:"+scrollHeight+",client:"+clientHeight);
        if(scrollTop + clientHeight >= scrollHeight){
            loadmore();
        }else if(scrollTop<=0){
        }
    });
</script>
</body>
</html>