<include file="block/hat" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化报价管理页面样式 */
                .quotation-index-wrapper {
                    width: 100%;
                }

                /* 现代化页面标题 */
                .quotation-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                    width: 100%;
                }

                .quotation-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .quotation-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .quotation-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .quotation-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .quotation-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .quotation-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .quotation-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .quotation-index-actions {
                    display: flex;
                    gap: 1rem;
                    align-items: center;
                }

                .quotation-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                }

                .quotation-index-search-toggle:hover {
                    background: #4b5563;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .quotation-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .quotation-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .quotation-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .quotation-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .quotation-index-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .quotation-index-nav-link.active {
                    color: #667eea;
                    background: white;
                    border-bottom-color: #667eea;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1);
                }

                .quotation-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .quotation-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 隐藏式搜索面板 */
                .quotation-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    max-height: 0;
                    opacity: 0;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .quotation-index-search-panel.show {
                    max-height: 500px;
                    opacity: 1;
                }

                .quotation-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .quotation-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .quotation-index-search-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .quotation-index-search-body {
                    padding: 2rem;
                    background: white;
                }

                .quotation-index-search-form {
                    display: flex;
                    flex-direction: column;
                    gap: 1.5rem;
                }

                .quotation-index-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .quotation-index-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0;
                }

                .quotation-index-form-input,
                .quotation-index-form-select {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                }

                .quotation-index-form-input:focus,
                .quotation-index-form-select:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .quotation-index-form-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                .quotation-index-search-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    align-items: center;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e5e7eb;
                    flex-wrap: wrap;
                }

                .quotation-index-search-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                }

                .quotation-index-search-btn.btn-primary {
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .quotation-index-search-btn.btn-primary:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    text-decoration: none;
                }

                .quotation-index-search-btn.btn-secondary {
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                }

                .quotation-index-search-btn.btn-secondary:hover {
                    background: #4b5563;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    text-decoration: none;
                }
                }

                /* 现代化列表容器 */
                .quotation-index-list-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }

                .quotation-list-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .quotation-list-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .quotation-list-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .quotation-list-body {
                    background: white;
                }

                /* 列表项样式 */
                .quotation-list-item {
                    display: flex;
                    align-items: flex-start;
                    padding: 2rem;
                    border-bottom: 1px solid #f1f5f9;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    gap: 2rem;
                }

                .quotation-list-item:last-child {
                    border-bottom: none;
                }

                .quotation-list-item:hover {
                    background: #f8fafc;
                    transform: translateX(4px);
                }

                .quotation-list-item::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 4px;
                    background: transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .quotation-list-item:hover::before {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                /* ID区域 */
                .quotation-item-id-section {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 0.5rem;
                    min-width: 6rem;
                }

                .quotation-item-id {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 0.5rem 1rem;
                    border-radius: 2rem;
                    font-size: 1.5rem;
                    font-weight: 700;
                    text-align: center;
                    min-width: 5rem;
                }

                /* 内容区域 */
                .quotation-item-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 1.5rem;
                }

                .quotation-item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                .quotation-item-title {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .quotation-project-info {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1rem;
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    color: #4c51bf;
                    font-weight: 600;
                }

                .quotation-project-info i {
                    color: #667eea;
                    font-size: 1.25rem;
                }

                /* 报价信息 */
                .quotation-pricing-section {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                }

                .quotation-section-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0 0 1rem 0;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding-bottom: 0.75rem;
                    border-bottom: 2px solid #667eea;
                }

                .quotation-section-title i {
                    color: #667eea;
                    font-size: 1.5rem;
                }

                .quotation-price-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                }

                .quotation-price-item {
                    background: white;
                    border: 1px solid #e5e7eb;
                    border-radius: 0.5rem;
                    padding: 1rem;
                    text-align: center;
                }

                .quotation-price-label {
                    display: block;
                    font-size: 1.25rem;
                    color: #6b7280;
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                }

                .quotation-price-value {
                    display: block;
                    font-size: 1.75rem;
                    font-weight: 700;
                    color: #667eea;
                }

                .quotation-price-value.zero {
                    color: #ef4444;
                    font-style: italic;
                }

                /* 操作按钮区域 */
                .quotation-item-actions {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    align-items: flex-end;
                    min-width: 120px;
                }

                .quotation-item-actions .btn {
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    padding: 0.5rem 1rem;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    text-decoration: none;
                    text-align: center;
                    min-width: 80px;
                }

                .quotation-item-actions .btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .quotation-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .quotation-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .quotation-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .quotation-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .quotation-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .quotation-price-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .quotation-list-item {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 1rem;
                        padding: 1.5rem 1rem;
                    }

                    .quotation-item-content {
                        margin-left: 0;
                        width: 100%;
                    }

                    .quotation-item-actions {
                        margin-left: 0;
                        width: 100%;
                        flex-direction: row;
                        justify-content: flex-start;
                        align-items: center;
                    }

                    .quotation-index-nav-tabs {
                        flex-direction: column;
                    }

                    .quotation-index-nav-item {
                        flex: none;
                    }

                    .quotation-index-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .quotation-index-nav-link.active {
                        border-bottom: none;
                        border-right-color: #667eea;
                    }

                    .quotation-index-title-main {
                        font-size: 1.75rem;
                    }

                    .quotation-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .quotation-index-actions {
                        flex-direction: column;
                        width: 100%;
                    }

                    .quotation-index-search-toggle {
                        width: 100%;
                        justify-content: center;
                    }
                }
            </style>

            <!-- 现代化页面标题 -->
            <div class="quotation-index-header quotation-index-fade-in">
                <div class="quotation-index-header-content">
                    <div class="quotation-index-title">
                        <div class="quotation-index-title-icon">
                            <i class="fa fa-calculator"></i>
                        </div>
                        <div class="quotation-index-title-text">
                            <h1 class="quotation-index-title-main">报价管理</h1>
                            <p class="quotation-index-title-sub">Project Quotation Management</p>
                        </div>
                    </div>
                    <div class="quotation-index-actions">
                        <button type="button" class="quotation-index-search-toggle" onclick="toggleSearchPanel()">
                            <i class="fa fa-search"></i>
                            <span>搜索筛选</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 现代化搜索面板 -->
            <div class="quotation-index-search-panel quotation-index-fade-in-delay-1" id="searchPanel">
                <div class="quotation-index-search-header">
                    <div class="quotation-index-search-icon">
                        <i class="fa fa-search"></i>
                    </div>
                    <h3 class="quotation-index-search-title">搜索筛选</h3>
                </div>
                <div class="quotation-index-search-body">
                    <form method="get" class="quotation-index-search-form" role="form">
                        <div class="quotation-index-form-group">
                            <label class="quotation-index-form-label">搜索字段</label>
                            <select class="quotation-index-form-select" name="kw">
                                <php>foreach($c_kw as $key=>$value){</php>
                                <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                <php>}</php>
                            </select>
                        </div>
                        <div class="quotation-index-form-group">
                            <label class="quotation-index-form-label">搜索内容</label>
                            <input class="quotation-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
                        </div>
                        <div class="quotation-index-form-group">
                            <label class="quotation-index-form-label">岗位状态</label>
                            <select name="status" class="quotation-index-form-select">
                                <option value="">全部状态</option>
                                <php>foreach($statusList as $key => $status) {</php>
                                <option value="{$key}" {:$_get['status'] != '' && $_get['status'] == $key ? "selected" : ''}>{$status.text}</option>
                                <php>} </php>
                            </select>
                        </div>
                        <div class="quotation-index-search-actions">
                            <button type="submit" class="quotation-index-search-btn btn-primary">
                                <i class="fa fa-search"></i>
                                <span>搜索</span>
                            </button>
                            <a href="{:U('project/quotation')}" class="quotation-index-search-btn btn-secondary">
                                <i class="fa fa-refresh"></i>
                                <span>重置</span>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 现代化导航标签 -->
            <div class="quotation-index-nav-container quotation-index-fade-in-delay-2">
                <ul class="quotation-index-nav-tabs">
                    <li class="quotation-index-nav-item">
                        <a href="{:U('Project/index')}" class="quotation-index-nav-link">
                            <i class="fa fa-briefcase quotation-index-nav-icon"></i>
                            <span>项目管理</span>
                        </a>
                    </li>
                    <li class="quotation-index-nav-item">
                        <a href="{:U('Project/edit')}" class="quotation-index-nav-link">
                            <i class="fa fa-plus quotation-index-nav-icon"></i>
                            <span>添加项目</span>
                        </a>
                    </li>
                    <li class="quotation-index-nav-item">
                        <a href="{:U('Projectpost/index')}" class="quotation-index-nav-link">
                            <i class="fa fa-users quotation-index-nav-icon"></i>
                            <span>岗位管理</span>
                        </a>
                    </li>
                    <li class="quotation-index-nav-item">
                        <a href="{:U('Projectpost/edit')}" class="quotation-index-nav-link">
                            <i class="fa fa-plus quotation-index-nav-icon"></i>
                            <span>添加岗位</span>
                        </a>
                    </li>
                    <li class="quotation-index-nav-item">
                        <a href="{:U('Project/quotation')}" class="quotation-index-nav-link active">
                            <i class="fa fa-calculator quotation-index-nav-icon"></i>
                            <span>报价管理</span>
                        </a>
                    </li>
                </ul>
            </div>


            <!-- 现代化列表容器 -->
            <form action="" method="post">
                <div class="quotation-index-list-container quotation-index-fade-in-delay-3">
                    <div class="quotation-list-header">
                        <div class="quotation-list-icon">
                            <i class="fa fa-calculator"></i>
                        </div>
                        <h3 class="quotation-list-title">报价列表</h3>
                    </div>
                    <div class="quotation-list-body">
                        <php>foreach($list as $v) { </php>
                        <div class="quotation-list-item">
                            <!-- ID显示 -->
                            <div class="quotation-item-id-section">
                                <div class="quotation-item-id">
                                    #{$v.id}
                                </div>
                            </div>

                            <!-- 内容区域 -->
                            <div class="quotation-item-content">
                                <!-- 岗位名称和项目信息 -->
                                <div class="quotation-item-header">
                                    <h4 class="quotation-item-title">
                                        {$v.job_name}
                                    </h4>
                                    <div class="quotation-project-info">
                                        <i class="fa fa-briefcase"></i>
                                        <span>{:$projectList[$v['project_id']]}</span>
                                    </div>
                                </div>

                                <!-- 报价信息 -->
                                <div class="quotation-pricing-section">
                                    <h5 class="quotation-section-title">
                                        <i class="fa fa-calculator"></i>
                                        项目报价
                                    </h5>
                                    <div class="quotation-price-grid">
                                        <php>
                                            foreach($projectIdentityList as $projectIdentitRow) {
                                                $projectIdentitJoinCost = $projectJoinIdentity[$v['id']][$projectIdentitRow['id']] ?:0;
                                                echo '<div class="quotation-price-item">';
                                                echo '<span class="quotation-price-label">' . $projectIdentitRow['name'] . '：</span>';
                                                if ($projectIdentitJoinCost > 0) {
                                                    echo '<span class="quotation-price-value">￥' . $projectIdentitJoinCost . '</span>';
                                                } else {
                                                    echo '<span class="quotation-price-value zero">未设置</span>';
                                                }
                                                echo '</div>';
                                            }
                                        </php>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="quotation-item-actions">
                                <php>echo quotationStatBtn($v['id']);</php>
                            </div>
                        </div>
                        <php>}</php>
                    </div>
                </div>

                <!-- 分页信息 -->
                <div class="quotation-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem;">
                    {$page}
                </div>
            </form>
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function() {
        // 列表项悬停效果增强
        $('.quotation-list-item').hover(
            function() {
                $(this).addClass('hover-effect');
            },
            function() {
                $(this).removeClass('hover-effect');
            }
        );

        // 报价状态按钮点击事件
        $('.js_status').click(function() {
            var that = $(this),
                url = that.attr('url');

            // 添加加载状态
            var originalText = that.html();
            that.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
            that.prop('disabled', true);

            $.get(url, function(data) {
                window.location.reload();
            }).fail(function() {
                that.html(originalText);
                that.prop('disabled', false);
                layer.msg('操作失败，请重试', {icon: 2});
            });
        });

        // 空状态处理
        var totalItems = $('.quotation-list-item').length;
        if (totalItems === 0) {
            var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-calculator"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无报价数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的岗位报价。</p></div>');
            $('.quotation-index-list-container .quotation-list-body').append($emptyState);
        }

        // 岗位ID点击复制功能
        $('.quotation-item-id').click(function() {
            var $id = $(this);
            var idText = $id.text();

            // 创建临时输入框进行复制
            var $temp = $('<input>');
            $('body').append($temp);
            $temp.val(idText).select();
            document.execCommand('copy');
            $temp.remove();

            // 显示复制成功提示
            var originalText = $id.text();
            $id.text('已复制!');
            $id.css('background', 'linear-gradient(135deg, #10b981 0%, #059669 100%)');
            $id.css('color', 'white');

            setTimeout(function() {
                $id.text(originalText);
                $id.css('background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
                $id.css('color', 'white');
            }, 1000);
        });

        // 报价信息悬停效果
        $('.quotation-price-value').hover(
            function() {
                if (!$(this).hasClass('zero')) {
                    $(this).css('color', '#5a67d8');
                    $(this).css('font-weight', '800');
                }
            },
            function() {
                if (!$(this).hasClass('zero')) {
                    $(this).css('color', '#667eea');
                    $(this).css('font-weight', '700');
                }
            }
        );

        // 未设置报价的特殊效果
        $('.quotation-price-value.zero').hover(
            function() {
                $(this).css('color', '#dc2626');
                $(this).text('点击设置');
            },
            function() {
                $(this).css('color', '#ef4444');
                $(this).text('未设置');
            }
        );

        // 项目信息点击效果
        $('.quotation-project-info').click(function() {
            var $info = $(this);
            $info.css('transform', 'scale(0.98)');
            setTimeout(function() {
                $info.css('transform', 'scale(1)');
            }, 150);
        });

        // 搜索框实时搜索提示
        $('input[name="val"]').on('input', function() {
            var $input = $(this);
            var value = $input.val();

            if (value.length > 0) {
                $input.css('border-color', '#667eea');
            } else {
                $input.css('border-color', '#e5e7eb');
            }
        });

    });

    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
        } else {
            panel.classList.add('show');
        }
    }
</script>

<include file="block/footer" />
