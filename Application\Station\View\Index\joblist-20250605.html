﻿<!DOCTYPE html>
<html>
  <head>
    <title>简历管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
      name="viewport"
    />
    <meta content="no-cache,must-revalidate" http-equiv="Cache-Control" />
    <meta content="telephone=no, address=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta content="no-cache" http-equiv="pragma" />
    <meta content="0" http-equiv="expires" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/css.css?v={:time()}"
      media="all"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/swiper-bundle.css"
      media="all"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/iconfont.css"
      media="all"
    />
    <script
      type="text/javascript"
      src="/static/stations/js/jquery-1.9.1.min.js"
    ></script>
    <script
      type="text/javascript"
      src="/static/stations/js/swiper-bundle.min.js"
    ></script>
    <script src="/static/js/layer/layer.m.js"></script>
  </head>
  <style>
    .container {
      margin: 10px;
    }
    .nomore {
      display: block;
      align-items: center;
      text-align: center;
      color: #ccc;
    }

    /* 添加按钮样式 */
    .add-btn {
      margin-top: 2px;
      padding: 10px 18px;
      background: #ff6b35;
      color: white;
      border: none;
      border-radius: 10px;
      font-size: 17px;
      cursor: pointer;
      transition: all 0.2s;
      box-shadow: 0 4px 12px rgba(0, 191, 128, 0.3);
    }

    .add-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 191, 128, 0.4);
    }

    /* 模态框遮罩层 */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    /* 模态框内容 */
    .modal-content {
      background: white;
      padding: 32px;
      border-radius: 16px;
      width: 90%;
      max-width: 500px;
      position: relative;
      animation: modalSlide 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    }

    @keyframes modalSlide {
      from {
        transform: scale(0.8);
        opacity: 0;
      }
      to {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* 操作类型选择 */
    .method-section {
      margin-bottom: 28px;
    }

    .method-title {
      color: #333;
      font-size: 20px;
      margin-bottom: 16px;
      text-align: center;
    }

    .method-options {
      display: grid;
      gap: 16px;
    }

    /* 上传区域 */
    .upload-box {
      border: 2px dashed #00bf80;
      border-radius: 12px;
      padding: 24px;
      text-align: center;
      position: relative;
      transition: all 0.3s;
    }

    .upload-box:hover {
      background: rgba(0, 191, 128, 0.05);
    }

    #file-input {
      position: absolute;
      opacity: 0;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      cursor: pointer;
    }

    .upload-icon {
      font-size: 40px;
      margin-bottom: 12px;
      color: #00bf80;
    }

    /* 进度条样式 */
    .progress-bar {
      width: 100%;
      height: 8px;
      background: #eee;
      border-radius: 4px;
      margin-top: 16px;
      overflow: hidden;
      display: none;
    }

    .progress {
      width: 0%;
      height: 100%;
      background: #00bf80;
      transition: width 0.3s ease;
    }

    /* 链接生成区域 */
    .link-section {
      margin-top: 4px;
    }

    .link-box {
      display: flex;
      gap: 8px;
      margin-top: 16px;
    }

    .link-input {
      flex: 1;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 14px;
    }

    .copy-btn {
      padding: 12px 20px;
      background: #1890ff;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .copy-btn:hover {
      background: #1472cc;
    }

    /* 关闭按钮 */
    .close-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 32px;
      height: 32px;
      cursor: pointer;
      opacity: 0.7;
      transition: all 0.2s;
    }

    .close-btn:hover {
      opacity: 1;
      transform: rotate(90deg);
    }

    /* 提示信息 */
    .hint-text {
      color: #666;
      font-size: 13px;
      margin-top: 8px;
      text-align: center;
    }

    .success-msg {
      color: #00bf80;
      display: none;
      margin-top: 12px;
    }
    /* 搜索框样式 */
    .search-bar {
      background: #fff;
      padding: 8px 15px;
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .search-input {
      width: 100%;
      height: 36px;
      border: 1px solid #e5e5e5;
      border-radius: 18px;
      padding: 0 15px;
      box-sizing: border-box;
      background: #f0f0f0;
      font-size: 14px;
    }

    /* 简历列表项 */
    .resume-item {
      background: #fff;
      border-radius: 8px;
      margin: 18px 0;
      padding: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .basic-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .left-info > div,
    .right-info > div {
      margin: 2px 0;
    }

    .position-info {
      color: #666;
      margin: 8px 0;
      padding: 8px 0;
      border-top: 1px solid #eee;
    }

    /* 按钮区域 */
    .action-area {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
    }

    .btn {
      padding: 6px 15px;
      border-radius: 15px;
      font-size: 13px;
      border: none;
    }

    .btn-primary {
      background: #07c160;
      color: white;
    }

    .btn-disabled {
      background: #ddd;
      color: #999;
    }

    .status-tag {
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 12px;
    }
    .chat-btn {
      background: #1d4051;
      color: white;
    }
    .chat-btn i {
      display: inline-block;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: #da5352;
      position: absolute;
      z-index: 2;
      margin-top: -16px;
      margin-left: -66px;
    }

    .study-btn {
      background: #f08b19;
      color: white;
    }
    /* 状态颜色 */
    .status-communicating {
      background: #e6f3ff;
      color: #007aff;
    }
    .status-training {
      background: #fff7e6;
      color: #ff9500;
    }
    .status-employed {
      background: #e6ffe6;
      color: #07c160;
    }
    .status-terminated {
      background: #ffe6e6;
      color: #ff3b30;
    }
    .status-jobdoc {
      background: #fffbfb;
      color: #115c0d;
      border: 1px dashed #606760;
    }
    .status-jobdocself {
      background: #fffbfb;
      color: #115c0d;
      border: 1px dashed #ccc;
    }
    .status-jobdoce {
      background: #fffbfb;
      color: #115c0d;
    }
    .status-jobdocerr {
      background: #f3a05c;
      color: #115c0d;
    }
  </style>
  <body>
    <include file="headers" />

    <div class="tabs-hd02" style="margin: 10px">
      <div class="weap">
        <ul>
          <li class="{:empty($type) ? 'on' : ''}" data-type="0">
            <span class="a">全部({$stateCounts.allcount})</span>
          </li>
          <li class="{:$type == 1 ? 'on' : ''}" data-type="1">
            <span class="a">沟通中({$stateCounts.communicating})</span>
          </li>
          <li class="{:$type == 2 ? 'on' : ''}" data-type="2">
            <span class="a">培训中({$stateCounts.training})</span>
          </li>
          <li class="{:$type == 3 ? 'on' : ''}" data-type="3">
            <span class="a">已入职({$stateCounts.onboarding})</span>
          </li>
          <li class="{:$type == 4 ? 'on' : ''}" data-type="4">
            <span class="a">服务终止({$stateCounts.terminated})</span>
          </li>
        </ul>
      </div>
    </div>

    <div class="container">
      <div class="search-bar">
        <input
          type="text"
          name="kwd"
          value="{:$kwd}"
          class="search-input js_kwd"
          placeholder="搜索姓名"
        />
      </div>
      <php>if (empty($list)) {</php>
      <div class="bd">
        <ul>
          <li
            style="
              text-align: center;
              font-size: 13px;
              color: darkgray;
              padding: 20px 0;
            "
          >
            无相关简历数据
          </li>
        </ul>
      </div>
      <php>} else {</php>
      <div id="resumeList">
        <include file="list-joblist" />
      </div>
      <php>}</php>
    </div>
    <div class="modal-overlay" id="modal">
      <div class="modal-content">
        <!-- 关闭按钮 -->
        <svg
          class="close-btn"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#666"
          onclick="closeModal()"
        >
          <path
            d="M18 6L6 18M6 6l12 12"
            stroke-width="2"
            stroke-linecap="round"
          />
        </svg>
        <!-- 操作方式选择 -->
        <div class="method-section" style="margin-top: 22px">
          <h2 class="method-title" id="t1">选择简历添加方式</h2>
          <h2
            class="method-title"
            style="display: none; color: red; font-weight: bold"
            id="t2"
          >
            简历诉求
          </h2>
          <div class="method-options">
            <!-- 上传区域 -->
            <div class="upload-box">
              <div id="stext">
                <div class="upload-icon">📤</div>
                <p>【您自己操作】点这里上传简历文件</p>
                <p class="hint-text">仅支持格式：Word文档/PDF</p>
                <input type="file" id="file-input" accept=".doc,.docx,.pdf" />
              </div>

              <div style="width: 100%; display: none" id="j_content">
                <!-- 分割线 -->
                <textarea
                  id="job_content"
                  class="link-input"
                  style="width: 100%; height: 88px"
                  placeholder="在这里输入该简历诉求，比如想去哪里，想找什么工作"
                ></textarea>
                <!-- 提交按钮绑定事件 -->
                <button
                  class="copy-btn"
                  onclick="submitResume()"
                  style="text-align: center; margin-top: 18px"
                >
                  提 交
                </button>
                <br />
              </div>

              <!-- 上传进度 -->
              <p class="success-msg" id="upload-success">✓ 正在保存简历……</p>
              <div class="progress-bar">
                <div class="progress" id="progress"></div>
              </div>
            </div>

            <div id="stext2">
              <!-- 分割线 -->
              <div style="text-align: center; color: #999; margin: 10px 0">
                或 生成简历创建链接复制发给求职者
              </div>

              <!-- 生成链接区域 -->
              <div class="link-section">
                <button class="copy-btn" onclick="generateLink()">
                  点这生成简历创建链接
                </button>
                <div class="link-box">
                  <input
                    type="text"
                    class="link-input"
                    id="link-text"
                    readonly
                    placeholder="点上方生成链接发给求职者"
                  />
                  <button class="copy-btn" onclick="copyLink()">复制</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // 显示模态框
      function showModal() {
        document.getElementById("modal").style.display = "flex";
        document.body.style.overflow = "hidden"; // 禁止背景滚动
      }

      // 关闭弹窗时重置状态
      function closeModal() {
        document.getElementById("modal").style.display = "none";
        document.body.style.overflow = "auto";
        resetUploadStatus();
        // 新增重置操作
        selectedFile = null;
        document.getElementById("job_content").value = "";
      }

      // 点击遮罩关闭
      document.getElementById("modal").addEventListener("click", function (e) {
        if (e.target === this) closeModal();
      });

      // 生成随机链接
      function generateLink() {
        $.ajax({
          type: "post",
          data: {},
          url: "/index/getsorturl",
          beforeSend: function () {
            //loading层
            layer.open({
              type: 2,
              shadeClose: false,
            });
          },
          complete: function () {
            // Handle the complete event
          },
          success: function (data) {
            layer.closeAll();
            ajaxsend = true;
            if (data.status == 1) {
              $("#link-text").val(data.info);
            } else {
              layer.open({
                style: "width:80%",
                shadeClose: false,
                content:
                  "<div style='text-align: left'>" + data.info + "</div>",
                btn: ["我知道了"],
                yes: function (index) {
                  layer.closeAll();
                },
              });
            }
          },
        });
      }

      // 复制链接功能
      function copyLink() {
        const link = document.getElementById("link-text").value;
        if (!link) return;
        copyToClipboardLegacyTwo(link);

        // navigator.clipboard.writeText(link).then(() => {
        //     showTempMessage('链接已复制到剪贴板');
        // });
      }

      // 新增全局变量用于存储文件对象
      let selectedFile = null;

      // 修改文件选择事件（仅保存文件不立即上传）
      document
        .getElementById("file-input")
        .addEventListener("change", function (e) {
          const file = e.target.files[0];
          if (!file) return;

          // 验证文件格式
          const fileName = file.name.toLowerCase();
          const validExtensions = [".doc", ".docx", ".pdf"];
          const isValidFile = validExtensions.some((ext) =>
            fileName.endsWith(ext)
          );

          if (!isValidFile) {
            alert("仅支持上传word文档或PDF格式简历");
            this.value = ""; // 清空选择
            return;
          }

          // 保存文件对象
          selectedFile = file;

          // 立即显示输入区域
          const jcontentstr = document.getElementById("j_content");
          const stextstr = document.getElementById("stext");
          const stextstr2 = document.getElementById("stext2");
          jcontentstr.style.display = "block";
          stextstr.style.display = "none";
          stextstr2.style.display = "none";
          document.getElementById("t1").style.display = "none";
          document.getElementById("t2").style.display = "block";
        });

      // 新增提交函数
      function submitResume() {
        if (!selectedFile) {
          alert("请先选择简历文件");
          return;
        }

        const jobContent = document.getElementById("job_content").value.trim();
        if (!jobContent) {
          alert("请输入简历求职诉求");
          return;
        }

        const progressBar = document.querySelector(".progress-bar");
        const progress = document.getElementById("progress");
        const successMsg = document.getElementById("upload-success");
        const formData = new FormData();

        // 合并文件和其他参数
        formData.append("file", selectedFile);
        formData.append("jobcontent", jobContent);

        progressBar.style.display = "block";
        successMsg.style.display = "none";

        const xhr = new XMLHttpRequest();

        xhr.upload.onprogress = function (e) {
          if (e.lengthComputable) {
            const percent = (e.loaded / e.total) * 100;
            progress.style.width = percent + "%";
          }
        };

        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4) {
            progressBar.style.display = "none";
            if (xhr.status === 200) {
              const res = JSON.parse(xhr.responseText);
              if (res.status === 1) {
                successMsg.style.display = "block";
                showTempMessage(res.msg);
                // 3秒后自动关闭弹窗
                setTimeout(() => {
                  closeModal();
                  window.location.href = res.url || '{:U("index/joblist")}'; // TP3.2路由
                }, 1000);
              } else {
                alert(res.msg);
                resetUploadStatus();
              }
            } else {
              if (xhr.status == 413) {
                alert("上传失败，简历文件过大异常！");
              } else {
                alert("上传失败，HTTP状态码：" + xhr.status);
              }
              resetUploadStatus();
            }
          }
        };

        xhr.open("POST", '{:U("index/uploadJobFile")}', true);
        xhr.send(formData);
      }
      //
      // 显示临时提示信息
      function showTempMessage(msg) {
        const tempDiv = document.createElement("div");
        tempDiv.className = "hint-text";
        tempDiv.textContent = msg;
        tempDiv.style.color = "#00bf80";
        tempDiv.style.marginTop = "12px";

        document.querySelector(".method-options").appendChild(tempDiv);
        setTimeout(() => tempDiv.remove(), 2000);
      }

      // 重置上传状态
      function resetUploadStatus(delay = 0) {
        setTimeout(() => {
          document.getElementById("file-input").value = "";
          document.getElementById("progress").style.width = "0%";
          document.getElementById("upload-success").style.display = "none";
          document.getElementById("j_content").style.display = "none";
          document.getElementById("stext").style.display = "block";
          document.getElementById("stext2").style.display = "block";
          document.getElementById("t2").style.display = "none";
          document.getElementById("t1").style.display = "block";
        }, delay);
      }
    </script>
    <div class="footernav">
      <div class="box">
        <ul>
          <li>
            <a href="javascript:void();" onclick="showModal()">
              <button class="add-btn" onclick="showModal()">+ 添加简历</button>
            </a>
          </li>
        </ul>
      </div>
    </div>
    <script type="text/javascript">
      var kwd = '{$kwd}';
      var url = "{:U('index/joblist')}"
      var sortShowTime = '{:$show_time}';
      var sortShowNum = '{:$show_num}';
      var type = '{$type}';
      $(document).ready(function(){
      $(".tabs-hd02 ul li").click(function(){
        $(this).siblings().removeClass("on");
        $(this).addClass("on");
          var newType = $(this).attr('data-type');
          $(this).siblings().removeClass("on");
          $(this).addClass("on");
          if (newType != type) {
              window.location.href = url + '?kwd='+kwd+"&type="+newType;
              return
          }
      });


      $(".screen01 li").click(function(){
              var typeOnOrUp = 0;
              var dataType = $(this).attr('data-type');
              if($(this).is('.ondown')){
                  $(this).removeClass('ondown');
                  $(this).siblings().removeClass("ondown");
                  $(this).siblings().removeClass('onup');
                  $(this).addClass('onup');
                  typeOnOrUp = 1;
              } else {
                  $(this).removeClass('onup');
                  $(this).siblings().removeClass("ondown");
                  $(this).siblings().removeClass('onup');
                  $(this).addClass('ondown');
                  typeOnOrUp = 2;
              }
              if (dataType == 'sortShowTime') {
                  sortShowNum = 0;
                  sortShowTime = typeOnOrUp;
              } else {
                  sortShowTime = 0;
                  sortShowNum = typeOnOrUp;
              }
              reloadData();
              return false;
          });


          $('.js_kwd').blur(function () {
              var  new_kwd = $(this).val();
              if (new_kwd != kwd) {
                  window.location.href = url + '?kwd='+new_kwd+"&type="+type;
                  return
              }
          })

          var noticeSwiper = new Swiper('.noticeSwiper', {
              direction: 'vertical',
              loop: true,
              autoplay: {
                  delay: 3000,
                  disableOnInteraction: false,
              }
          })
          var noticeSwiper = new Swiper('.page0106Swiper', {
              pagination: {
                  el: ".swiper-pagination",
              },

              loop: true,
              autoplay: {
              delay: 3000,
              disableOnInteraction: false,
              }
          });

      });


          var page=1,pages=<?= (int)$page->Total_Pages ?>;

          function loadmore(){
              if(page<pages){
                  page+=1;
                  $.get('/index/joblist?p=' + page+'&kwd='+kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum, function(str){
                      $('#job').append(str);
                  }, 'html');
              } else if (page==pages) {
                  page+=1;
                  setTimeout("$('.container').append('<p class=\"nomore\">------ 没有了 ------</p>')", 500);
              }
          }
          $(window).scroll(function(){
              var scrollTop = $(this).scrollTop();     //滚动条距离顶部的高度
              var scrollHeight = $(document).height(); //当前页面的总高度
              var clientHeight = $(this).height();     //当前可视的页面高度

              if(scrollTop + clientHeight >= scrollHeight){
                  loadmore();
              }else if(scrollTop<=0){
              }
          });


       function baoming(jobId) {
          if (!jobId) {
            layer.open({
              content: '简历ID不存在',
              skin: 'msg',
              time: 2
            });
            return;
          }

          // 检查学员是否已报名
          var trainingOrderList = {:json_encode($trainingOrderList)};

          // 使用 some 方法进行比较，确保类型一致
          var isAlreadyRegistered = false;
          if (trainingOrderList && trainingOrderList.length > 0) {
            isAlreadyRegistered = trainingOrderList.some(function(id) {
              return String(id) === String(jobId);
            });
          }

          if (isAlreadyRegistered) {
            layer.open({
              content: '该学员已报名培训',
              skin: 'msg',
              time: 2
            });
            return;
          }

          // 获取学员简历信息
          $.ajax({
            url: "{:U('training/getUserJobInfo')}",
            type: 'GET',
            data: {user_job_id: jobId},
            dataType: 'json',
            success: function(res) {
              if (res.status == 1) {
                // 保存学员学历和专业信息，用于后续显示
                var educationLevel = res.data.education_level || '-';
                var major = res.data.major || '-';
                console.log('学员学历:', educationLevel, '专业:', major);

                // 继续创建培训订单表单
                createTrainingOrderForm(jobId, educationLevel, major);
              } else {
                layer.open({
                  content: res.msg || '获取学员信息失败',
                  skin: 'msg',
                  time: 2
                });
              }
            },
            error: function() {
              layer.open({
                content: '网络错误，请重试',
                skin: 'msg',
                time: 2
              });
            }
          });
        }

        // 创建培训订单表单
        function createTrainingOrderForm(jobId, educationLevel, major) {

          // 先通过AJAX获取表单数据
          $.ajax({
            url: "{:U('training/getFormData')}",
            type: 'GET',
            dataType: 'json',
            success: function(res) {
              if (res.status == 1) {
                console.log('获取表单数据成功:', res.data);

                // 创建弹窗内容
                var modalContent = '<div class="training-form" style="padding: 20px; background-color: #fff;">';
                modalContent += '<form id="trainingForm">';

                // 隐藏字段 - 简历ID
                modalContent += '<input type="hidden" id="modal_job_id" value="' + jobId + '">';

                // 学员信息 - 只读，因为是从简历中获取的
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-user" style="margin-right: 5px;"></i>学员：</label>';
                modalContent += '<select name="user_id" id="modal_user_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required disabled>';

                // 根据jobId过滤出对应的学员
                var selectedUser = null;
                for (var i = 0; i < res.data.users.length; i++) {
                  if (res.data.users[i].id == jobId) {
                    selectedUser = res.data.users[i];
                    modalContent += '<option value="' + res.data.users[i].id + '" selected>' +
                                    res.data.users[i].realname + ' (' + res.data.users[i].mobile + ')' +
                                    '</option>';
                    break;
                  }
                }
                console.log('res.data:', res.data);
                console.log('jobId:', jobId);
                // 如果没有找到对应的学员，显示提示信息
                if (!selectedUser) {
                  // 检查是否是服务终止状态
                  layer.open({
                    content: '该简历无法报名培训，可能是服务已终止或简历未分析完成',
                    skin: 'msg',
                    time: 2
                  });
                  return;
                }

                modalContent += '</select>';
                modalContent += '</div>';

                // 培训项目
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-project" style="margin-right: 5px;"></i>培训项目：</label>';
                modalContent += '<select name="project_id" id="modal_project_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required>';
                modalContent += '<option value="">请选择培训项目</option>';

                // 添加项目选项
                for (var i = 0; i < res.data.projects.length; i++) {
                  modalContent += '<option value="' + res.data.projects[i].id + '">' +
                                  res.data.projects[i].name +
                                  '</option>';
                }

                modalContent += '</select>';
                modalContent += '</div>';

                // 培训岗位
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-tag" style="margin-right: 5px;"></i>培训岗位：</label>';
                modalContent += '<select name="post_id" id="modal_post_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required>';
                modalContent += '<option value="">请先选择培训项目</option>';
                modalContent += '</select>';
                modalContent += '</div>';

                // 学员学历
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-education" style="margin-right: 5px;"></i>学员学历：</label>';
                modalContent += '<p id="modal_education_level" style="color: #333; font-size: 15px; padding: 8px 0;">-</p>';
                modalContent += '</div>';

                // 学员专业
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-major" style="margin-right: 5px;"></i>学员专业：</label>';
                modalContent += '<p id="modal_major" style="color: #333; font-size: 15px; padding: 8px 0;">-</p>';
                modalContent += '</div>';

                // 报名费
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-money" style="margin-right: 5px;"></i>报名费：</label>';
                modalContent += '<p id="modal_fee_amount" style="color: #ff6b35; font-weight: bold; font-size: 16px; padding: 8px 0;">0.00 元</p>';
                modalContent += '<select id="modal_fee_select" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);">';
                modalContent += '<option value="">请选择报名费</option>';
                modalContent += '</select>';
                modalContent += '</div>';

                modalContent += '</form>';
                modalContent += '</div>';

                // 打开弹窗
                layer.open({
                  type: 1,
                  title: '<span style="font-weight: bold; font-size: 16px;">创建培训订单</span>',
                  area: ['90%', 'auto'],
                  skin: 'layui-layer-molv', // 使用墨绿色皮肤
                  content: modalContent, // 直接使用我们构建的HTML内容
                  btn: ['提交', '取消'],
                  btnAlign: 'c', // 按钮居中
                  success: function(layero) {
                    // 添加样式到弹窗
                    $(layero).find('.layui-layer-title').css({
                      'background-color': '#07c160',
                      'color': '#fff',
                      'border': 'none',
                      'height': '50px',
                      'line-height': '50px'
                    });

                    $(layero).find('.layui-layer-btn a').css({
                      'border-radius': '4px',
                      'height': '38px',
                      'line-height': '38px',
                      'padding': '0 18px',
                      'font-size': '15px'
                    });

                    $(layero).find('.layui-layer-btn .layui-layer-btn0').css({
                      'border-color': '#07c160',
                      'background-color': '#07c160',
                      'color': '#fff'
                    });

                    $(layero).find('.layui-layer-btn .layui-layer-btn1').css({
                      'border-color': '#ddd',
                      'background-color': '#f7f7f7',
                      'color': '#333'
                    });

                    console.log('弹窗创建成功');

                    // 设置学员的学历和专业信息
                    $('#modal_education_level').text(educationLevel);
                    $('#modal_major').text(major);

                    // 项目选择变化时，获取对应的岗位列表
                    $('#modal_project_id').change(function() {
                      var projectId = $(this).val();
                      if (projectId) {
                        $.ajax({
                          url: "{:U('training/getProjectPosts')}",
                          type: 'GET',
                          data: {project_id: projectId},
                          dataType: 'json',
                          success: function(res) {
                            if (res.status == 1 && res.data && res.data.length > 0) {
                              var posts = res.data;
                              var options = '<option value="">请选择培训岗位</option>';
                              for (var i = 0; i < posts.length; i++) {
                                options += '<option value="' + posts[i].id + '" ' +
                                  'data-service-price="' + posts[i].service_price_formatted + '" ' +
                                  'data-service-price-in-cents="' + posts[i].service_price_in_cents + '" ' +
                                  'data-service-price-text="' + posts[i].service_price_text + '" ' +
                                  'data-max-price="' + posts[i].max_price_formatted + '" ' +
                                  'data-max-price-in-cents="' + posts[i].max_price_in_cents + '" ' +
                                  'data-max-price-text="' + posts[i].max_price_text + '" ' +
                                  'data-price="' + posts[i].service_price_formatted + '" ' +
                                  'data-price-in-cents="' + posts[i].service_price_in_cents + '">' +
                                  posts[i].name + '</option>';
                              }
                              $('#modal_post_id').html(options);
                            } else {
                              $('#modal_post_id').html('<option value="">无</option>');
                              layer.open({
                                content: res.msg || '没有找到相关岗位',
                                skin: 'msg',
                                time: 2
                              });
                            }
                          },
                          error: function() {
                            layer.open({
                              content: '网络错误，请重试',
                              skin: 'msg',
                              time: 2
                            });
                          }
                        });
                      } else {
                        $('#modal_post_id').html('<option value="">请先选择培训项目</option>');
                        $('#modal_fee_amount').text('0.00 元');
                      }
                    });

                    // 学员选择变化时，获取学历和专业信息
                    $('#modal_job_id').change(function() {
                      var userJobId = $(this).val();
                      if (userJobId) {
                        // 获取学员简历信息
                        $.ajax({
                          url: "{:U('training/getUserJobInfo')}",
                          type: 'GET',
                          data: {user_job_id: userJobId},
                          dataType: 'json',
                          success: function(res) {
                            if (res.status == 1) {
                              // 显示学历和专业信息
                              $('#modal_education_level').text(res.data.education_level || '-');
                              $('#modal_major').text(res.data.major || '-');
                            }
                          }
                        });
                      }
                    });

                    // 岗位选择变化时，显示对应的报名费
                    $('#modal_post_id').change(function() {
                      var selectedOption = $(this).find('option:selected');

                      // 获取服务价格信息
                      var servicePrice = selectedOption.data('service-price') || 0;
                      var servicePriceInCents = selectedOption.data('service-price-in-cents') || 0;
                      var servicePriceText = selectedOption.data('service-price-text') || '';

                      // 获取最高价格信息
                      var maxPrice = selectedOption.data('max-price') || 0;
                      var maxPriceInCents = selectedOption.data('max-price-in-cents') || 0;
                      var maxPriceText = selectedOption.data('max-price-text') || '';

                      // 显示两个价格
                      var priceDisplay = '';
                      if (servicePrice && servicePriceText) {
                        priceDisplay += servicePrice + ' 元 (' + servicePriceText + ')';
                      }

                      if (maxPrice && maxPriceText) {
                        if (priceDisplay) {
                          priceDisplay += ' ~ ';
                        }
                        priceDisplay += maxPrice + ' 元 (' + maxPriceText + ')';
                      }

                      if (!priceDisplay) {
                        priceDisplay = '0.00 元';
                      }

                      $('#modal_fee_amount').html(priceDisplay);

                      // 更新报名费下拉框选项
                      var feeOptions = '<option value="">请选择报名费</option>';

                      // 添加服务价格选项（包括0元的公益项目）
                      if (servicePriceText) {
                        feeOptions += '<option value="' + servicePriceInCents + '">' +
                                      servicePrice + ' 元 (' + servicePriceText + ')</option>';
                      }

                      // 添加最高价格选项（如果与服务价格不同）
                      if (maxPriceText && maxPriceInCents !== servicePriceInCents) {
                        feeOptions += '<option value="' + maxPriceInCents + '">' +
                                      maxPrice + ' 元 (' + maxPriceText + ')</option>';
                      }

                      $('#modal_fee_select').html(feeOptions);
                    });
                  },
                  yes: function(index) {
                    try {
                      // 获取表单数据
                      var userJobId = $('#modal_job_id').val();
                      var projectId = $('#modal_project_id').val();
                      var postId = $('#modal_post_id').val();

                      console.log('提交表单，简历ID:', userJobId, '项目ID:', projectId, '岗位ID:', postId);

                      // 表单验证
                      if (!userJobId) {
                        layer.open({
                          content: '简历信息错误',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }
                      if (!projectId) {
                        layer.open({
                          content: '请选择培训项目',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }
                      if (!postId) {
                        layer.open({
                          content: '请选择培训岗位',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }

                      // 获取费用金额 - 从下拉框中获取选择的价格
                      var feeAmount = $('#modal_fee_select').val();
                      if (!feeAmount) {
                        layer.open({
                          content: '请选择报名费',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }
                      console.log('费用金额(分):', feeAmount);

                      // 提交表单
                      $.ajax({
                        url: "{:U('training/create')}", // 使用ThinkPHP U函数生成正确的URL
                        type: 'POST',
                        data: {
                          user_id: userJobId,
                          post_id: postId,
                          fee_amount: feeAmount
                        },
                        dataType: 'json',
                        beforeSend: function() {
                          console.log('正在提交表单数据:', {
                            user_id: userJobId,
                            post_id: postId,
                            fee_amount: feeAmount
                          });
                        },
                        success: function(res) {
                          console.log('表单提交响应:', res);
                          if (res.status == 1) {
                            layer.open({
                              content: '报名成功',
                              skin: 'msg',
                              time: 2
                            });
                            setTimeout(function() {
                              window.location.href = res.url || "{:U('training/index')}";
                            }, 1000);
                          } else {
                            layer.open({
                              content: res.info || '报名失败',
                              skin: 'msg',
                              time: 2
                            });
                          }
                        },
                        error: function(xhr, status, error) {
                          console.error('表单提交失败:', status, error);
                          console.log('响应文本:', xhr.responseText);
                          layer.open({
                            content: '网络错误，请重试',
                            skin: 'msg',
                            time: 2
                          });
                        }
                      });
                    } catch (e) {
                      console.error('表单提交过程中出错:', e);
                      layer.open({
                        content: '表单提交出错',
                        skin: 'msg',
                        time: 2
                      });
                      return false;
                    }
                  }
                });
              } else {
                layer.open({
                  content: '获取表单数据失败',
                  skin: 'msg',
                  time: 2
                });
              }
            },
            error: function() {
              layer.open({
                content: '网络错误，请重试',
                skin: 'msg',
                time: 2
              });
            }
          });
      }
    </script>
  </body>
</html>
