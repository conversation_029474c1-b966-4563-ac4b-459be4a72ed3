<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Userjob/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">家庭成员编辑</div>
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>简历人</label>
                            <div class="col-sm-9 col-xs-12" style="margin-top: 8px">
                                  {:$userJobRow['name']}
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>亲属关系</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="relationship" class="form-control sp_input" value="{$row.relationship}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>姓名</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="full_name" class="form-control sp_input" value="{$row.full_name}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>工作单位</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="work_unit" class="form-control sp_input" value="{$row.work_unit}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>职务</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="position" class="form-control sp_input" value="{$row.position}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>联系电话</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="contact" class="form-control sp_input" value="{$row.contact}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="form-group col-sm-12">
					<input type="hidden" name="id" value="{$row.id}"/>
					<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			    </div>
            </div>
            </form>
        </div>
    </div>
</div>
<include file="block/footer" />
<script>
    require(["datetimepicker", 'layer', 'util'], function($, layer, util){

        $(function () {
            $('.form input[name="type"]').change(function () {
                var type = $(this).val();
                $('.js_type').hide();
                $('.js_type'+type).show();
            });
        })

        $(".datetimepicker").each(function(){
            var opt = {
                language: "zh-CN",
                format: "yyyy-mm-dd",
                minView: 2,
                autoclose: true,
                format : "yyyy-mm-dd",
                minView : 2,
                minuteStep:1,
            };
            $(this).datetimepicker(opt);
        });
    });
    //function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
            u.editor($('.richtext1')[0]);

        })
    });
</script>