<php>if ($list) { foreach($list as $v) {</php>

<li>
  <div class="a">
    <a class="title">
      <php>if ($v['is_top'] == 1) {</php>
      <i style="padding: 2px 5px; font-size: 13px">置顶</i>
      <php>}</php>
      <i
        style="background-color: #10b35e; font-size: 12px; padding: 2px 5px"
        id="comtepy_{:$v['id']}"
        >{:$categoryList[$projectList[$v['project_id']]['category']]}</i
      >
      <h3>
        <div
          id="texttitle_{:$v['id']}"
          class="ui-nowrap-multi"
          style="line-height: 1.3; max-height: 2.6em"
        >
          {:$projectList[$v['project_id']]['name']} {:$v['job_name']}
        </div>
      </h3>
    </a>

    <php>if($v['service_price']>0){</php>

    <div class="prices" style="margin-top: 8px;font-size: 14px">
      <div  class="commission"  style="font-size: 14px;padding: 2px 5px;margin-right: 4px;">
         <php>if($serviceStationRow['zsb_type']==1){</php>
         对外服务收费区间
        <php>}else{</php>
          对外服务收费
          <php>}</php>
      </div>
      <div class="price">
        <i>
          <!-- 招就办用户显示对外价格 -->
          <php>if($isZsb && isset($v['zsb_sale_price'])){</php>
            ¥{:number_format($v['zsb_sale_price'], 2)}
          <php>}else{</php>
            ¥{:$v['service_price']}<php>if($v['max_price']>0){</php> ~
            ¥{:$v['max_price']}<php>}</php>
          <php>}</php>
        </i>
      </div>
    </div>

    <!-- 服务站用户显示原有价格说明 -->
    
    <php>if(!$isZsb){</php>
       <div style="font-size: 14px; color: #8a8c8b; margin-bottom: 8px">
      实际收费根据学员年龄、学历、专业等综合因素决定
    </div>
    <!--div style="font-size: 14px; color: #8a8c8b; margin-bottom: 8px">
      {:$v['service_price_text']}¥{:$v['service_price']}<php>if($v['max_price']>0){</php>，{:$v['max_price_text']}¥{:$v['max_price']}<php
        >}</php>
    </div-->
    <div style="font-size: 14px; color: #8a8c8b; margin-bottom: 8px">
      费用包含培训费、指导费、测评费、考证费（如有）等
    </div>
    <php>}else{</php>
    <!-- 招就办用户显示服务费说明（不显示对外价格） -->
           <div style="font-size: 14px; color: #8a8c8b; margin-bottom: 8px">
      实际收费根据学员年龄、学历、专业等综合因素决定
    </div>
    <div style="font-size: 14px; color: #8a8c8b; margin-bottom: 8px">
      费用包含培训费、指导费、测评费、考证费（如有）等
    </div>
    <php>}</php>

    <!-- 服务站用户显示服务站奖励 -->
    <php>if(!$isZsb){</php>
    <div class="prices">
    <div
        style="
          font-size: 14px;
          border: 1px solid #6cbe67;
          color: #6cbe67;
          margin-right: 3px;
          padding: 2px 5px;
          float: left;
        "
      >
        服务及结算预估周期{:$v['settle_day']}天
      </div>
      </div>
    <div class="prices">
       
      <div class="commission" style="font-size: 14px; padding: 3px">
        服务站收益：¥{:$v['service_price']-$projectJoinIdentity[$v['id']]}
        <php>if($v['max_price']>0){</php>
        ~
        ¥{:($v['max_price']-$v['service_price'])*0.8+($v['service_price']-$projectJoinIdentity[$v['id']])}
        <php>}</php>
      </div>
    </div>

    <!-- 添加推荐站奖励显示，仅当有下级服务站时显示
    <php>if($hasSubServiceStations && $v['reward'] > 0){</php>
    <div class="prices">
      <div class="commission" style="font-size: 14px; padding: 2px">
        推荐站奖励：¥{:$v['reward']}
      </div>
    </div>
    <php>}</php>-->
    <php>}</php>

    <!-- 招就办用户显示招就办奖励 -->
    <php>if($isZsb && isset($v['zsb_sale_price'])){</php>
    <div class="prices">
      <div class="commission" style="font-size: 14px; padding: 2px">
        招就办奖励：¥{:number_format($v['zsb_commission'], 2)}
      </div>
    </div>
    <php>}</php>

    <php>}</php>

    <div class="prices">
      <php>if($v['is_free']==1){</php>
      <div
        style="
          font-style: normal;
          background-color: #10b35e;
          color: #fff;
          padding: 4px;
          border-radius: 3px;
          margin-right: 5px;
          font-size: 13px;
        "
      >
        免费公益
      </div>
      <php>}</php>
      <div
        style="
          font-size: 14px;
          border: 1px solid #db5410;
          color: #db5410;
          margin-right: 3px;
          padding: 0 5px;
          float: left;
        "
      >
        {:$v['min_age']?:0}-{:$v['max_age']?:0}岁 {:$sexList[$v['sex']]['text']}
        {:$qualificationList[$v['qualification']]['text']} {:$v['major']?:''}
      </div>
    </div>
    <php>if (!empty($v['tag'])) {</php>
    <div class="prices">
      <php>
        $tagArr = explode(',',$v['tag']); foreach ($tagArr as $tagRows){
      </php>
      <div
        style="
          font-size: 14px;
          border: 1px solid #db5410;
          color: #db5410;
          margin-right: 3px;
          padding: 0 2px;
          float: left;
          margin-bottom: 5px;
        "
      >
        {:$tagRows}
      </div>
      <php>}</php>
    </div>
    <php>}</php>

    <php>
      if (!empty($v['img_url_one']) || !empty($v['img_url_two']) ||
      !empty($v['img_url_three'])) {</php
    >
    <div class="imgs clearfix">
      <php>if (!empty($v['img_url_one'])) {</php>
      <a href="javascript:void(0);"
        ><img
          class="thumbnail"
          style="height: 104px"
          src="http://we.zhongcaiguoke.com/{:$v['img_url_one']}"
          alt=""
      /></a>
      <php>}</php>
      <php>if (!empty($v['img_url_two'])) {</php>
      <a href="javascript:void(0);"
        ><img
          class="thumbnail"
          style="height: 104px"
          src="http://we.zhongcaiguoke.com/{:$v['img_url_two']}"
          alt=""
      /></a>
      <php>}</php>
      <php>if (!empty($v['img_url_three'])) {</php>
      <a href="javascript:void(0);"
        ><img
          class="thumbnail"
          style="height: 104px"
          src="http://we.zhongcaiguoke.com/{:$v['img_url_three']}"
          alt=""
      /></a>
      <php>}</php>
    </div>
    <php>}</php>
    <php>if (!empty(htmlspecialchars_decode($v['other_content']))){</php>

    <div class="text-container">
      <div
        class="collapsible-text collapsed js_copy"
        id="textContent_{:$v['id']}"
        data-id="{:$v['id']}"
      >
        {:trim(htmlspecialchars_decode($v['other_content']))}
      </div>
      <div class="toggle-btn" onclick="toggleText({:$v['id']})">
        <div
          class="arrow"
          style="
            width: 100%;
            text-align: center;
            color: #15461f;
            font-size: 13px;
            background-color: #fdf9f9;
            margin-top: 4px;
            padding-top: 5px;
            margin-bottom: 2px;
          "
        >
          >>展开/收起内容<<
        </div>
      </div>
    </div>

    <php>}</php>

    <div class="states">
      <div class="state"><span> </span></div>
      <div class="righta">

        <a href="javascript:void(0);" class="a2"><i class="iconfont icon-bianji"></i>报名培训</a>

        
        <a href="javascript:void(0)" data-id="{:$v['id']}" class="a2 js_copy">
          <i class="iconfont icon-24_beizhu"></i>复制岗位信息</a>
        
<php>if(!$isZsb && $v['is_free']!=1){</php>
       <a class="a2 js_share" href="{:U('index/calculator')}?postname={:urlencode($projectList[$v['project_id']]['name'].$v['job_name'])}&price={:$projectJoinIdentity[$v['id']]}&mincharge={:$v['service_price']}&maxcharge={:$v['max_price']}"
       <i class="iconfont icon-flow-determine"></i>收益计算器</a>
<php>}</php>       

      </div>
    </div>
    <div class="sr">
      <div class="p">
        <span>已报名 {:$v['sign_up_num'] ? : 0} 人</span
        ><span>服务中 {:$v['service_num'] ?:0} 人</span
        ><span>已服务 {:$v['succ_service_num'] ? : 0} 人</span>
      </div>
    </div>
  </div>
</li>
<php>}}</php>
