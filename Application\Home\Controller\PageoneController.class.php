<?php

namespace Home\Controller;

use Common\Controller\WController;

class PageoneController extends WController
{

    public function _initialize() {
        parent::_initialize();
        $this->login();
        $openid = session('openid');
        $userRow = D("User")->where(['openid' => $openid])->find();
        if (!$userRow) {
            session(null);
            return $this->redirect(U('/pageone/index'));
            die;
        }
    }

    /**
     * 单页内容显示
     */
    public function index() {
        $id = I('get.id', 1);
        $row = D("PageList")->where(['id' => $id])->find();
        $pageList = D("PageList")->where(['status' => 1])->getField('id,title', true);
        $this->assign('pageList', $pageList);
        $this->assign('row', $row);
        $this->display();
    }
}
