<include file="block/hat" />
<script type="text/javascript" src="/static/js/lib/jquery-ui-1.10.3.min.js"></script>
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 限制样式作用域到菜单管理页面 */
                .menus-page-container {
                    /* 重置可能影响全局的样式 */
                }

                /* 现代化菜单管理页面样式 */
                .menus-page-container .menus-wrapper {
                    width: 100%;
                    padding: 1.5rem;
                    background: #f7fafc;
                    min-height: 100vh;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.5;
                }

                /* 现代化页面标题 */
                .menus-page-container .menus-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                    width: 100%;
                }

                .menus-page-container .menus-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .menus-page-container .menus-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .menus-page-container .menus-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .menus-page-container .menus-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .menus-page-container .menus-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .menus-page-container .menus-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .menus-page-container .menus-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .menus-page-container .menus-actions {
                    display: flex;
                    gap: 1rem;
                    align-items: center;
                }

                .menus-page-container .menus-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                }

                .menus-page-container .menus-btn.btn-primary {
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .menus-page-container .menus-btn.btn-primary:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    text-decoration: none;
                }

                .menus-page-container .menus-btn.btn-warning {
                    background: #f59e0b;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.3);
                }

                .menus-page-container .menus-btn.btn-warning:hover {
                    background: #d97706;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(245, 158, 11, 0.4);
                    text-decoration: none;
                }

                /* 菜单卡片样式 */
                .menus-page-container .menu-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .menus-page-container .menu-card-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .menus-page-container .menu-card-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .menus-page-container .menu-card-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .menus-page-container .menu-card-body {
                    padding: 2rem;
                }

                /* 菜单项样式 */
                .menus-page-container .menu-item {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    margin-bottom: 1.5rem;
                    transition: all 0.3s ease;
                }

                .menus-page-container .menu-item:hover {
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    transform: translateY(-1px);
                }

                .menus-page-container .menu-item:last-child {
                    margin-bottom: 0;
                }

                .menus-page-container .menu-item-header {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin-bottom: 1.5rem;
                    padding-bottom: 1rem;
                    border-bottom: 2px solid #667eea;
                }

                .menus-page-container .menu-level-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    border-radius: 2rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .menus-page-container .menu-level-badge.level-1 {
                    background: #667eea;
                    color: white;
                }

                .menus-page-container .menu-level-badge.level-2 {
                    background: #10b981;
                    color: white;
                }

                .menus-page-container .menu-item-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                /* 表单样式 */
                .menus-page-container .menu-form-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 1.5rem;
                }

                .menus-page-container .menu-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .menus-page-container .menu-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0;
                }

                .menus-page-container .menu-form-input,
                .menus-page-container .menu-form-select {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                    font-family: inherit;
                }

                .menus-page-container .menu-form-input:focus,
                .menus-page-container .menu-form-select:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .menus-page-container .menu-form-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                .menus-page-container .menu-form-help {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-top: 0.25rem;
                }

                /* 子菜单样式 */
                .menus-page-container .submenu-container {
                    margin-top: 1.5rem;
                    padding-left: 2rem;
                    border-left: 3px solid #10b981;
                }

                .menus-page-container .submenu-item {
                    background: white;
                    border: 1px solid #d1d5db;
                    border-radius: 0.5rem;
                    padding: 1rem;
                    margin-bottom: 1rem;
                }

                .menus-page-container .submenu-item:last-child {
                    margin-bottom: 0;
                }

                /* 提交按钮样式 */
                .menus-page-container .menu-submit-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    padding: 2rem;
                    text-align: center;
                    margin-top: 2rem;
                }

                .menus-page-container .menu-submit-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 1rem 2rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    font-family: inherit;
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .menus-page-container .menu-submit-btn:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                }

                /* 动画效果 */
                @keyframes menusPageFadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .menus-page-container .menus-fade-in {
                    animation: menusPageFadeInUp 0.6s ease-out;
                }

                .menus-page-container .menus-fade-in-delay-1 {
                    animation: menusPageFadeInUp 0.6s ease-out 0.1s both;
                }

                .menus-page-container .menus-fade-in-delay-2 {
                    animation: menusPageFadeInUp 0.6s ease-out 0.2s both;
                }

                .menus-page-container .menus-fade-in-delay-3 {
                    animation: menusPageFadeInUp 0.6s ease-out 0.3s both;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .menus-page-container .menus-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .menus-page-container .menu-form-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .menus-page-container .menus-wrapper {
                        padding: 1rem;
                    }

                    .menus-page-container .menus-title-main {
                        font-size: 1.75rem;
                    }

                    .menus-page-container .menus-title-sub {
                        font-size: 1.25rem;
                    }

                    .menus-page-container .menus-actions {
                        flex-direction: column;
                        width: 100%;
                    }

                    .menus-page-container .menus-btn {
                        width: 100%;
                        justify-content: center;
                    }

                    .menus-page-container .submenu-container {
                        padding-left: 1rem;
                    }

                    .menus-page-container .menu-card-body {
                        padding: 1rem;
                    }
                }
            </style>

            <div class="menus-page-container">
            <div class="menus-wrapper">
            <!-- 现代化页面标题 -->
            <div class="menus-header menus-fade-in">
                <div class="menus-header-content">
                    <div class="menus-title">
                        <div class="menus-title-icon">
                            <i class="fa fa-bars"></i>
                        </div>
                        <div class="menus-title-text">
                            <h1 class="menus-title-main">{$row.name} 菜单管理</h1>
                            <p class="menus-title-sub">WeChat Menu Management</p>
                        </div>
                    </div>
                    <div class="menus-actions">
                        <a href="{:U('service/menus?clean=1&id='.$id)}" class="menus-btn btn-warning">
                            <i class="fa fa-trash"></i>
                            <span>清空菜单</span>
                        </a>
                        <a href="{:U('service/menus?id='.$id)}" class="menus-btn btn-primary">
                            <i class="fa fa-refresh"></i>
                            <span>还原菜单</span>
                        </a>
                    </div>
                </div>
            </div>

            <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
                <!-- 菜单一 -->
                <div class="menu-card menus-fade-in-delay-1">
                    <div class="menu-card-header">
                        <div class="menu-card-icon">
                            <i class="fa fa-list"></i>
                        </div>
                        <h3 class="menu-card-title">菜单一</h3>
                    </div>
                    <div class="menu-card-body">
                        <!-- 一级菜单 -->
                        <div class="menu-item">
                            <div class="menu-item-header">
                                <span class="menu-level-badge level-1">
                                    <i class="fa fa-layer-group"></i>
                                    一级菜单
                                </span>
                                <h4 class="menu-item-title">主菜单配置</h4>
                            </div>
                            <div class="menu-form-grid">
                                <div class="menu-form-group">
                                    <label class="menu-form-label">菜单名称</label>
                                    <input type="text" name="data[1][name]" class="menu-form-input" value="{:$menuData[0]['name']}" placeholder="请输入菜单名称" />
                                    <div class="menu-form-help">显示在微信底部菜单栏的名称</div>
                                </div>
                                <div class="menu-form-group">
                                    <label class="menu-form-label">菜单类型</label>
                                    <select name="data[1][type]" class="menu-form-select js_one_select">
                                        <option value="">一级栏目</option>
                                        <option value="view" {:$menuData[0]['type'] == 'view' ? "selected" : ''}>链接</option>
                                        <option value="click" {:$menuData[0]['type'] == 'click' ? "selected" : ''}>点击</option>
                                        <option value="miniprogram" {:$menuData[0]['type'] == 'miniprogram' ? "selected" : ''}>小程序</option>
                                    </select>
                                    <div class="menu-form-help">选择菜单的响应类型</div>
                                </div>
                                <div class="menu-form-group">
                                    <label class="menu-form-label">链接地址</label>
                                    <input type="text" name="data[1][url]" class="menu-form-input js_1" style="display: {:$menuData[0]['type'] == 'miniprogram' ? 'none' : 'block'}" value="{:$menuData[0]['url']}" placeholder="请输入链接地址" />
                                    <input type="text" name="data[1][pagepath]" class="menu-form-input js_2" style="display: {:$menuData[0]['type'] == 'miniprogram' ? 'block' : 'none'}; margin-top: 10px;" placeholder="小程序页面路径" value="{:$menuData[0]['pagepath']}" />
                                    <input type="text" name="data[1][appid]" class="menu-form-input js_2" style="display: {:$menuData[0]['type'] == 'miniprogram' ? 'block' : 'none'}; margin-top: 10px;" placeholder="小程序AppID" value="{:$menuData[0]['appid']}" />
                                    <div class="menu-form-help">点击菜单时的响应内容</div>
                                </div>
                            </div>
                        </div>

                        <!-- 二级菜单 -->
                        <div class="submenu-container">
                            <!-- 二级菜单1 -->
                            <div class="submenu-item">
                                <div class="menu-item-header">
                                    <span class="menu-level-badge level-2">
                                        <i class="fa fa-angle-right"></i>
                                        二级菜单
                                    </span>
                                    <h5 class="menu-item-title">子菜单 1</h5>
                                </div>
                                <div class="menu-form-grid">
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">菜单名称</label>
                                        <input type="text" name="data[1][sub_button][0][name]" class="menu-form-input" value="{:$menuData[0][sub_button][0]['name']}" placeholder="请输入子菜单名称" />
                                    </div>
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">菜单类型</label>
                                        <select name="data[1][sub_button][0][type]" class="menu-form-select js_tow_select">
                                            <option value="view" {:$menuData[0][sub_button][0]['type'] == 'view' ? "selected" : ''}>链接</option>
                                            <option value="click" {:$menuData[0][sub_button][0]['type'] == 'click' ? "selected" : ''}>点击</option>
                                            <option value="miniprogram" {:$menuData[0][sub_button][0]['type'] == 'miniprogram' ? "selected" : ''}>小程序</option>
                                        </select>
                                    </div>
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">链接地址</label>
                                        <input type="text" name="data[1][sub_button][0][url]" class="menu-form-input js_3" style="display: {:$menuData[0][sub_button][0]['type'] == 'miniprogram' ? 'none' : 'block'}" value="{:$menuData[0][sub_button][0]['url']}" placeholder="请输入链接地址" />
                                        <input type="text" name="data[1][sub_button][0][pagepath]" class="menu-form-input js_4" style="display: {:$menuData[0][sub_button][0]['type'] == 'miniprogram' ? 'block' : 'none'}; margin-top: 10px;" placeholder="小程序页面路径" value="{:$menuData[0][sub_button][0]['pagepath']}" />
                                        <input type="text" name="data[1][sub_button][0][appid]" class="menu-form-input js_4" style="display: {:$menuData[0][sub_button][0]['type'] == 'miniprogram' ? 'block' : 'none'}; margin-top: 10px;" placeholder="小程序AppID" value="{:$menuData[0][sub_button][0]['appid']}" />
                                    </div>
                                </div>

                            <!-- 二级菜单2 -->
                            <div class="submenu-item">
                                <div class="menu-item-header">
                                    <span class="menu-level-badge level-2">
                                        <i class="fa fa-angle-right"></i>
                                        二级菜单
                                    </span>
                                    <h5 class="menu-item-title">子菜单 2</h5>
                                </div>
                                <div class="menu-form-grid">
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">菜单名称</label>
                                        <input type="text" name="data[1][sub_button][1][name]" class="menu-form-input" value="{:$menuData[0][sub_button][1]['name']}" placeholder="请输入子菜单名称" />
                                    </div>
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">菜单类型</label>
                                        <select name="data[1][sub_button][1][type]" class="menu-form-select js_three_select">
                                            <option value="view" {:$menuData[0][sub_button][1]['type'] == 'view' ? "selected" : ''}>链接</option>
                                            <option value="click" {:$menuData[0][sub_button][1]['type'] == 'click' ? "selected" : ''}>点击</option>
                                            <option value="miniprogram" {:$menuData[0][sub_button][1]['type'] == 'miniprogram' ? "selected" : ''}>小程序</option>
                                        </select>
                                    </div>
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">链接地址</label>
                                        <input type="text" name="data[1][sub_button][1][url]" class="menu-form-input js_5" style="display: {:$menuData[0][sub_button][1]['type'] == 'miniprogram' ? 'none' : 'block'}" value="{:$menuData[0][sub_button][1]['url']}" placeholder="请输入链接地址" />
                                        <input type="text" name="data[1][sub_button][1][pagepath]" class="menu-form-input js_6" style="display: {:$menuData[0][sub_button][1]['type'] == 'miniprogram' ? 'block' : 'none'}; margin-top: 10px;" placeholder="小程序页面路径" value="{:$menuData[0][sub_button][1]['pagepath']}" />
                                        <input type="text" name="data[1][sub_button][1][appid]" class="menu-form-input js_6" style="display: {:$menuData[0][sub_button][1]['type'] == 'miniprogram' ? 'block' : 'none'}; margin-top: 10px;" placeholder="小程序AppID" value="{:$menuData[0][sub_button][1]['appid']}" />
                                    </div>
                                </div>

                            <!-- 二级菜单3 -->
                            <div class="submenu-item">
                                <div class="menu-item-header">
                                    <span class="menu-level-badge level-2">
                                        <i class="fa fa-angle-right"></i>
                                        二级菜单
                                    </span>
                                    <h5 class="menu-item-title">子菜单 3</h5>
                                </div>
                                <div class="menu-form-grid">
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">菜单名称</label>
                                        <input type="text" name="data[1][sub_button][2][name]" class="menu-form-input" value="{:$menuData[0][sub_button][2]['name']}" placeholder="请输入子菜单名称" />
                                    </div>
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">菜单类型</label>
                                        <select name="data[1][sub_button][2][type]" class="menu-form-select js_four_select">
                                            <option value="view" {:$menuData[0][sub_button][2]['type'] == 'view' ? "selected" : ''}>链接</option>
                                            <option value="click" {:$menuData[0][sub_button][2]['type'] == 'click' ? "selected" : ''}>点击</option>
                                            <option value="miniprogram" {:$menuData[0][sub_button][2]['type'] == 'miniprogram' ? "selected" : ''}>小程序</option>
                                        </select>
                                    </div>
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">链接地址</label>
                                        <input type="text" name="data[1][sub_button][2][url]" class="menu-form-input js_7" style="display: {:$menuData[0][sub_button][2]['type'] == 'miniprogram' ? 'none' : 'block'}" value="{:$menuData[0][sub_button][2]['url']}" placeholder="请输入链接地址" />
                                        <input type="text" name="data[1][sub_button][2][pagepath]" class="menu-form-input js_8" style="display: {:$menuData[0][sub_button][2]['type'] == 'miniprogram' ? 'block' : 'none'}; margin-top: 10px;" placeholder="小程序页面路径" value="{:$menuData[0][sub_button][2]['pagepath']}" />
                                        <input type="text" name="data[1][sub_button][2][appid]" class="menu-form-input js_8" style="display: {:$menuData[0][sub_button][2]['type'] == 'miniprogram' ? 'block' : 'none'}; margin-top: 10px;" placeholder="小程序AppID" value="{:$menuData[0][sub_button][2]['appid']}" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                <!-- 菜单二 -->
                <div class="menu-card menus-fade-in-delay-2">
                    <div class="menu-card-header">
                        <div class="menu-card-icon">
                            <i class="fa fa-list"></i>
                        </div>
                        <h3 class="menu-card-title">菜单二</h3>
                    </div>
                    <div class="menu-card-body">
                        <!-- 一级菜单 -->
                        <div class="menu-item">
                            <div class="menu-item-header">
                                <span class="menu-level-badge level-1">
                                    <i class="fa fa-layer-group"></i>
                                    一级菜单
                                </span>
                                <h4 class="menu-item-title">主菜单配置</h4>
                            </div>
                            <div class="menu-form-grid">
                                <div class="menu-form-group">
                                    <label class="menu-form-label">菜单名称</label>
                                    <input type="text" name="data[2][name]" class="menu-form-input" value="{:$menuData[1]['name']}" placeholder="请输入菜单名称" />
                                    <div class="menu-form-help">显示在微信底部菜单栏的名称</div>
                                </div>
                                <div class="menu-form-group">
                                    <label class="menu-form-label">菜单类型</label>
                                    <select name="data[2][type]" class="menu-form-select">
                                        <option value="">一级栏目</option>
                                        <option value="view" {:$menuData[1]['type'] == 'view' ? "selected" : ''}>链接</option>
                                        <option value="click" {:$menuData[1]['type'] == 'click' ? "selected" : ''}>点击</option>
                                        <option value="miniprogram" {:$menuData[1]['type'] == 'miniprogram' ? "selected" : ''}>小程序</option>
                                    </select>
                                    <div class="menu-form-help">选择菜单的响应类型</div>
                                </div>
                                <div class="menu-form-group">
                                    <label class="menu-form-label">链接地址</label>
                                    <input type="text" name="data[2][url]" class="menu-form-input" value="{:$menuData[1]['url']}" placeholder="请输入链接地址" />
                                    <div class="menu-form-help">点击菜单时的响应内容</div>
                                </div>
                            </div>
                        </div>

                        <!-- 二级菜单 -->
                        <div class="submenu-container">
                            <!-- 二级菜单1 -->
                            <div class="submenu-item">
                                <div class="menu-item-header">
                                    <span class="menu-level-badge level-2">
                                        <i class="fa fa-angle-right"></i>
                                        二级菜单
                                    </span>
                                    <h5 class="menu-item-title">子菜单 1</h5>
                                </div>
                                <div class="menu-form-grid">
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">菜单名称</label>
                                        <input type="text" name="data[2][sub_button][0][name]" class="menu-form-input" value="{:$menuData[1][sub_button][0]['name']}" placeholder="请输入子菜单名称" />
                                    </div>
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">菜单类型</label>
                                        <select name="data[2][sub_button][0][type]" class="menu-form-select">
                                            <option value="view" {:$menuData[1][sub_button][0]['type'] == 'view' ? "selected" : ''}>链接</option>
                                            <option value="click" {:$menuData[1][sub_button][0]['type'] == 'click' ? "selected" : ''}>点击</option>
                                            <option value="miniprogram" {:$menuData[1][sub_button][0]['type'] == 'miniprogram' ? "selected" : ''}>小程序</option>
                                        </select>
                                    </div>
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">链接地址</label>
                                        <input type="text" name="data[2][sub_button][0][url]" class="menu-form-input" value="{:$menuData[1][sub_button][0]['url']}" placeholder="请输入链接地址" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                <!-- 菜单三 -->
                <div class="menu-card menus-fade-in-delay-3">
                    <div class="menu-card-header">
                        <div class="menu-card-icon">
                            <i class="fa fa-list"></i>
                        </div>
                        <h3 class="menu-card-title">菜单三</h3>
                    </div>
                    <div class="menu-card-body">
                        <!-- 一级菜单 -->
                        <div class="menu-item">
                            <div class="menu-item-header">
                                <span class="menu-level-badge level-1">
                                    <i class="fa fa-layer-group"></i>
                                    一级菜单
                                </span>
                                <h4 class="menu-item-title">主菜单配置</h4>
                            </div>
                            <div class="menu-form-grid">
                                <div class="menu-form-group">
                                    <label class="menu-form-label">菜单名称</label>
                                    <input type="text" name="data[3][name]" class="menu-form-input" value="{:$menuData[2]['name']}" placeholder="请输入菜单名称" />
                                    <div class="menu-form-help">显示在微信底部菜单栏的名称</div>
                                </div>
                                <div class="menu-form-group">
                                    <label class="menu-form-label">菜单类型</label>
                                    <select name="data[3][type]" class="menu-form-select">
                                        <option value="">一级栏目</option>
                                        <option value="view" {:$menuData[2]['type'] == 'view' ? "selected" : ''}>链接</option>
                                        <option value="click" {:$menuData[2]['type'] == 'click' ? "selected" : ''}>点击</option>
                                        <option value="miniprogram" {:$menuData[2]['type'] == 'miniprogram' ? "selected" : ''}>小程序</option>
                                    </select>
                                    <div class="menu-form-help">选择菜单的响应类型</div>
                                </div>
                                <div class="menu-form-group">
                                    <label class="menu-form-label">链接地址</label>
                                    <input type="text" name="data[3][url]" class="menu-form-input" value="{:$menuData[2]['url']}" placeholder="请输入链接地址" />
                                    <div class="menu-form-help">点击菜单时的响应内容</div>
                                </div>
                            </div>
                        </div>

                        <!-- 二级菜单 -->
                        <div class="submenu-container">
                            <!-- 二级菜单1 -->
                            <div class="submenu-item">
                                <div class="menu-item-header">
                                    <span class="menu-level-badge level-2">
                                        <i class="fa fa-angle-right"></i>
                                        二级菜单
                                    </span>
                                    <h5 class="menu-item-title">子菜单 1</h5>
                                </div>
                                <div class="menu-form-grid">
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">菜单名称</label>
                                        <input type="text" name="data[3][sub_button][0][name]" class="menu-form-input" value="{:$menuData[2][sub_button][0]['name']}" placeholder="请输入子菜单名称" />
                                    </div>
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">菜单类型</label>
                                        <select name="data[3][sub_button][0][type]" class="menu-form-select">
                                            <option value="view" {:$menuData[2][sub_button][0]['type'] == 'view' ? "selected" : ''}>链接</option>
                                            <option value="click" {:$menuData[2][sub_button][0]['type'] == 'click' ? "selected" : ''}>点击</option>
                                            <option value="miniprogram" {:$menuData[2][sub_button][0]['type'] == 'miniprogram' ? "selected" : ''}>小程序</option>
                                        </select>
                                    </div>
                                    <div class="menu-form-group">
                                        <label class="menu-form-label">链接地址</label>
                                        <input type="text" name="data[3][sub_button][0][url]" class="menu-form-input" value="{:$menuData[2][sub_button][0]['url']}" placeholder="请输入链接地址" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                <!-- 提交按钮 -->
                <div class="menu-submit-container">
                    <button type="submit" name="submit" class="menu-submit-btn">
                        <i class="fa fa-save"></i>
                        <span>保存菜单配置</span>
                    </button>
                </div>
            </form>
            </div>
            </div>


        </div>
    </div>
</div>

<script type="text/javascript">
    $(function() {
        window.optionchanged = false;
        $('#myTab a').click(function (e) {
            e.preventDefault();//阻止a链接的跳转行为
            $(this).tab('show');//显示当前选中的链接及关联的content

        })

        $('.js_one_select').change(function () {
            var _this = $(this);
            var names = _this.val();
            console.log(names)
            if (names == 'miniprogram' ) {
                $('.js_1').hide();
                $('.js_2').show();
            } else {
                $('.js_2').hide();
                $('.js_1').show();
            }
        })

        $('.js_tow_select').change(function () {
            var _this = $(this);
            var names = _this.val();
            console.log(names)
            if (names == 'miniprogram' ) {
                $('.js_3').hide();
                $('.js_4').show();
            } else {
                $('.js_4').hide();
                $('.js_3').show();
            }
        })

        $('.js_three_select').change(function () {
            var _this = $(this);
            var names = _this.val();
            console.log(names)
            if (names == 'miniprogram' ) {
                $('.js_5').hide();
                $('.js_6').show();
            } else {
                $('.js_6').hide();
                $('.js_5').show();
            }
        })

        $('.js_four_select').change(function () {
            var _this = $(this);
            var names = _this.val();
            console.log(names)
            if (names == 'miniprogram' ) {
                $('.js_7').hide();
                $('.js_8').show();
            } else {
                $('.js_8').hide();
                $('.js_7').show();
            }
        })

        // 菜单卡片动画效果
        $('.menu-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });

        // 表单验证增强
        $('.menu-form-input').on('blur', function() {
            var $field = $(this);
            var value = $field.val().trim();

            if (value) {
                $field.css('border-color', '#10b981');
                $field.css('box-shadow', '0 0 0 3px rgba(16, 185, 129, 0.1)');
            } else {
                $field.css('border-color', '#e5e7eb');
                $field.css('box-shadow', 'none');
            }
        });

        // 输入框焦点效果
        $('.menu-form-input, .menu-form-select').on('focus', function() {
            $(this).css('border-color', '#667eea');
            $(this).css('box-shadow', '0 0 0 3px rgba(102, 126, 234, 0.1)');
        });

        $('.menu-form-input, .menu-form-select').on('blur', function() {
            if (!$(this).val().trim()) {
                $(this).css('border-color', '#e5e7eb');
                $(this).css('box-shadow', 'none');
            }
        });

        // 表单提交增强
        $('#form1').on('submit', function(e) {
            var $submitBtn = $('.menu-submit-btn');
            var originalText = $submitBtn.html();

            $submitBtn.html('<i class="fa fa-spinner fa-spin"></i> <span>保存中...</span>');
            $submitBtn.prop('disabled', true);

            // 模拟提交延迟（实际使用时可以删除）
            setTimeout(function() {
                $submitBtn.html(originalText);
                $submitBtn.prop('disabled', false);
            }, 2000);
        });

        // 菜单项悬停效果
        $('.menu-item, .submenu-item').hover(
            function() {
                $(this).css('transform', 'translateY(-2px)');
                $(this).css('box-shadow', '0 8px 15px -3px rgba(0, 0, 0, 0.1)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
                $(this).css('box-shadow', '');
            }
        );

        // 保留原有功能
        $("input[name='push_type']").change(function(){
            var $val = $(this).val();
            if($val == 2) {
                $("#yt").show();
            } else {
                $("#yt").hide();
            }
        })

        require(['layer', 'datetimepicker'], function(layer, datetimepicker) {
            //时间插件
            $(".datetimepicker").each(function(){
                var withtime = $(this).attr("data-withtime");
                var opt = {
                    language: "zh-CN",
                    minView: 2,
                    autoclose: true,
                    format : "hh:ii",
                    minView : 0,
                    weekStart: 1,
                    todayBtn:  1,
                    autoclose: 1,
                    todayHighlight: 1,
                    startView: 1,
                    minView: 0,
                    maxView: 1,
                    forceParse: 0
                };
                $(this).datetimepicker(opt);
            });
        })

    })
</script>
<include file="block/footer" />