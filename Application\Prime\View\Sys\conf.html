<include file="block/hat" />
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<ul class="nav nav-tabs">
				<li class="active"><a>系统设置</a></li>
			</ul>
			<style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
<div class="main">
    <div class="alert alert-warning">非开发人员请勿修改！</div>
<div class="panel panel-default">
	<div class="panel-body table-responsive">
		<table class="table table-hover">
			<thead class="navbar-inner">
				<tr>
					<th style="width:20px;">ID</th>
					<th style="width:80px;">Key</th>
					<th style="width:50px;">配置名称</th>
					<th style="width:50px;">值</th>
					<th style="width:60px;">描述</th>
					<th style="width:40px;">操作</th>
				</tr>
			</thead>
			<tbody>
				<php>foreach($list as $v) { </php>
				<tr>
                    <td>{$v.id}</td>
                    <td>{$v.name}</td>
                    <td>{$v.title}</td>
                    <td><php>if (stripos($v['name'],'url')!==false) { </php>
                    <a href="{$v.value}" target="_blank" title="{$v.value}">点击查看</a>
                    <php>} else {</php>
                    {$v.value}</td>
                    <php>}</php>
                    <td>{$v.desc}</td>
                    <td><a href="{:U('sys/conf_edit',array('id'=>$v[id]))}" class="btn btn-primary">修改</a></td>
				</tr>
				<php>}</php>
			</tbody>
		</table>
		{$page}
	</div>
	</div>
</div>

<div class="form-inline" style="margin:20px 0;">
    <a href="{:U('sys/conf_edit')}" class="btn btn-success"><span class="fa fa-plus"></span> 添加 </a>
</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	require(["daterangepicker"], function($){
		$(function(){
			$(".daterange.daterange-time").each(function(){
				var elm = this;
				$(this).daterangepicker({
					startDate: $(elm).prev().prev().val(),
					endDate: $(elm).prev().val(),
					format: "YYYY-MM-DD HH:mm",
					timePicker: true,
					timePicker12Hour : false,
					timePickerIncrement: 1,
					minuteStep: 1
				}, function(start, end){
					$(elm).find(".date-title").html(start.toDateTimeStr() + " 至 " + end.toDateTimeStr());
					$(elm).prev().prev().val(start.toDateTimeStr());
					$(elm).prev().val(end.toDateTimeStr());
				});
			});
			$('[data-toggle="tooltip"]').tooltip();
		});
	});
	$('#export').click(function(){
		url = location.href;
		url += (url.indexOf('?')>0 ? '&' : '?') + 'do=export&format='+$('#format').val();
		window.open(url);
	});
	function initback(obj) {
		$('#did').val($(obj).data('id'));
	}
</script>

<include file="block/footer" />