<?php

namespace Prime\Controller;

use Common\Controller\PrimeController;

class QuestioncategoryController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    public function index()
    {
        $c_kw = [
            'id' => 'ID',
            'title' => '名称',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        if ($s_status != '') $where['status'] = $s_status;

        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if ($s_kw == 'title') {
                $where[$s_kw] = ['like', "%$s_val%"];
            } elseif ($s_kw == 'title') {
                $where[$s_kw] = ['like', "%$s_val%"];
            } else {
                $where[$s_kw] = $s_val;
            }

        }

        $obj = D("QuestionCategory");
        $count = $obj->where($where)->count();
        $page = $this->page($count);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        $pidList = $obj->getField('id,title', true);
        $this->assign('pidList', $pidList);

        $this->assign('statusList', D("QuestionCategory")->status);
        $this->assign('_get', I("get."));
        $this->assign('list',$list);
        $this->assign('c_kw', $c_kw);
        $this->assign('page', $page->show());
        $this->display();
    }

    public function edit()
    {
        $id = intval(I('get.id'));
        $obj = D("QuestionCategory");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        }

        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['created'] = time();
                    $insertId = $obj->add($data);
                } else {
                    $data['updated'] = time();
                    $obj->save($data);
                }
                $this->success("操作成功", U("questioncategory/index"));exit;
            } else {
                $this->error($obj->getError());exit;
            }
         }
        $pidList = $obj->where(['status' => 1])->getField('id,title', true);
        $this->assign('pidList', $pidList);
         $this->display();
    }

     //状态
     public function cgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("QuestionCategory");
        if (!array_key_exists($status, [0,1])) $this->error('参数错误 ');
        $obj->save(['id' => $id, 'status' => $status]);
        $this->success('修改成功');
    }
}
