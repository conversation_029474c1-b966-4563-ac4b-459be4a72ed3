<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <title>{$quota.year_month}提现额度使用情况</title>
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <style>
        :root {
            --primary-color: #00bf80;
            --warning-color: #e67e22;
            --danger-color: #e74c3c;
            --success-color: #27ae60;
            --secondary-color: #666;
            --card-bg: #fff;
            --border-color: #eee;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, system-ui, sans-serif;
        }
        body {
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 12px auto;
            padding: 0 15px;
        }

        /* 卡片样式 */
        .quota-card {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        /* 标题样式 */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        .card-title {
            font-size: 20px;
            color: #333;
            font-weight: 600;
        }
        
        /* 额度进度条 */
        .progress-container {
            margin: 24px 0;
        }
        .progress-bar-wrapper {
            height: 16px;
            background-color: #f1f1f1;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 10px;
        }
        .progress-bar {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 8px;
            transition: width 0.5s ease;
            position: relative;
        }
        .progress-danger {
            background-color: var(--danger-color);
        }
        .progress-warning {
            background-color: var(--warning-color);
        }
        .progress-success {
            background-color: var(--success-color);
        }
        .progress-text {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: var(--secondary-color);
        }
        
        /* 额度信息 */
        .quota-details {
            margin: 30px 0;
        }
        .quota-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }
        .quota-info-item:last-child {
            border-bottom: none;
        }
        .quota-label {
            font-size: 16px;
            color: var(--secondary-color);
        }
        .quota-value {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }
        .value-success {
            color: var(--success-color);
        }
        .value-danger {
            color: var(--danger-color);
        }
        
        /* 状态提示 */
        .status-alert {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            font-size: 15px;
        }
        .alert-danger {
            background-color: #fdf2f2;
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }
        .alert-success {
            background-color: #f0fff4;
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }
        
        /* 额度说明 */
        .quota-desc {
            margin-top: 30px;
        }
        .desc-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
            font-weight: 500;
        }
        .desc-list {
            list-style-position: inside;
            padding-left: 5px;
        }
        .desc-list li {
            margin-bottom: 12px;
            color: var(--secondary-color);
            line-height: 1.5;
            position: relative;
            padding-left: 20px;
        }
        .desc-list li:before {
            content: "";
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--primary-color);
            position: absolute;
            left: 5px;
            top: 8px;
        }
        
        /* 返回按钮 */
        .action-btn {
            display: block;
            width: 100%;
            padding: 12px;
            text-align: center;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            margin-top: 20px;
        }
        .action-btn:hover {
            opacity: 0.9;
        }
        
        /* 返回箭头样式 */
        .back-arrow {
            position: fixed;
            top: 115px;
            left: 15px;
            width: 40px;
            height: 40px;
            z-index: 9999;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            transition: all 0.2s;
        }
        .back-arrow::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 50%;
            width: 12px;
            height: 12px;
            border-left: 2px solid #333;
            border-bottom: 2px solid #333;
            transform: translateY(-50%) rotate(45deg);
        }
        .back-arrow:active {
            transform: scale(0.95);
            background: rgba(255, 255, 255, 0.7);
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            .back-arrow {
                top: 200px;
                left: 10px;
                width: 36px;
                height: 36px;
            }
            .back-arrow::before {
                left: 13px;
                width: 10px;
                height: 10px;
            }
            .quota-card {
                padding: 20px 15px;
            }
            .card-title {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <!-- 返回箭头 -->
    <div class="back-arrow" onclick="goBack()"></div>
    
    <!-- 包含公共头部 -->
    <include file="headers"/>
    
    <div class="container">
        <div class="quota-card">
            <!-- 卡片标题 -->
            <div class="card-header">
                <div class="card-title">{$quota.year_month}提现额度使用情况</div>
            </div>
            
            <!-- 额度进度条 -->
            <div class="progress-container">
                <div class="progress-bar-wrapper">
                    <div class="progress-bar 
                        <if condition="$quota['is_exceed']">
                            progress-danger
                        <elseif condition="$quota['used_amount'] >= 0.8*$quota['total_quota']" />
                            progress-warning
                        <else />
                            progress-success
                        </if>" 
                        style="width: <if condition="$quota['is_exceed']">100%<else/>{:min(100, number_format($quota['used_amount']/$quota['total_quota']*100, 2))}%</if>;">
                    </div>
                </div>
                <div class="progress-text">
                    <span>已使用 ¥{$quota.used_amount}</span>
                    <span>总额度 ¥{$quota.total_quota}</span>
                </div>
            </div>
            
            <!-- 额度详情 -->
            <div class="quota-details">
                <div class="quota-info-item">
                    <div class="quota-label">总额度</div>
                    <div class="quota-value">¥{$quota.total_quota}</div>
                </div>
                <div class="quota-info-item">
                    <div class="quota-label">已使用</div>
                    <div class="quota-value <if condition="$quota['is_exceed']">value-danger</if>">¥{$quota.used_amount}</div>
                </div>
                <div class="quota-info-item">
                    <div class="quota-label">剩余额度</div>
                    <div class="quota-value <if condition="$quota['is_exceed']">value-danger<else/>value-success</if>">¥{$quota.remaining_quota}</div>
                </div>
            </div>
            
            <!-- 状态提示 -->
            <if condition="$quota['is_exceed']">
                <div class="status-alert alert-danger">
                    注意：当月额度已用完，后续提现需开具发票
                </div>
            <else />
                <div class="status-alert alert-success">
                    当月额度充足，提现由平台负责完税
                </div>
            </if>
            
            <!-- 额度说明 -->
            <div class="quota-desc">
                <div class="desc-title">额度说明</div>
                <ul class="desc-list">
                    <li>每月9.8万内平台负责完税（通过云账户灵活用工平台以个人经营形式完税）</li>
                    <li>每月9.8万超出部分的提现流程为：申请提现 --> 财务审核 --> 您开发票 --> 财务转账</li>
                    <li>如每月平均提现需求额度超出9.8万建议认证公司收款</li>
                </ul>
            </div>
            
            <!-- 返回按钮 -->
            <a href="{:U('index/money')}" class="action-btn">返回资金管理</a>
        </div>
    </div>
    
    <script>
        // 返回上一页
        function goBack() {
            window.location.href = "{:U('index/money')}";
        }
    </script>
</body>
</html> 