﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <title>中才国科服务网络</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
            background: #f8f8f8;
        }

        .header {
            background: #07c160;
            color: white;
            padding: 15px;
            font-size: 18px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .store-list {
            padding: 10px;
        }

        .store-item {
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .store-images {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .store-images img {
            width: 100px;
            height: 100px;
            border-radius: 6px;
            object-fit: cover;
        }

        .store-name {
            font-size: 18px;
            color: #333;
            margin-bottom: 4px;
        }

        .custom-name {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .store-info {
            color: #888;
            font-size: 13px;
            line-height: 1.4;
        }

        .loading {
            text-align: center;
            padding: 15px;
            color: #999;
            font-size: 14px;
        }

        .end-line {
            text-align: center;
            padding: 15px;
            color: #ccc;
            font-size: 14px;
        }
    /* 新增筛选样式 */
        .filters {
            padding: 10px;
            background: white;
            position: sticky;
            top: 45px;
            z-index: 99;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-box {
            margin-bottom: 10px;
        }
        
        .search-box input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
        }
        
        .select-row {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .select-row select {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 20px;
            font-size: 14px;
            background: white;
            -webkit-appearance: none;
        }

        /* 原有样式保持不变 */
        /* ... */
                /* 弹窗样式 */
            .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
            z-index: 999;
        }

        /* 大图样式 */
        .modal-img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }

        /* 缩略图样式 */
        .thumbnail {
            width: 200px;
            height: 200px;
            cursor: pointer;
            object-fit: cover;
        }
    </style>
</head>
<body>
     <div class="header">中才国科就业服务站</div>
    <div class="filters">
        <div class="search-box">
            <input type="text" id="searchInput" class="js_kwd" placeholder="搜索服务站...">
        </div>
        <div class="select-row">
            <select id="provinceSelect">
                <option value="">全部省份</option>
            </select>
            <select id="citySelect" disabled>
                <option value="">全部城市</option>
            </select>
        </div>
    </div>
    <div class="store-list" id="storeList">
        <include file="list-index"/>
    </div>
 
<!-- 弹窗层 -->
<div class="modal">
    <img class="modal-img">
</div>


<!--    <div class="loading" id="loading">加载中...</div>-->
<!--    <div class="end-line" id="endLine" style="display: none;">我可是有底线的</div>-->

    <script>
        
        // 获取元素
        const thumbnail = document.querySelector('.thumbnail');
        const modal = document.querySelector('.modal');
        const modalImg = document.querySelector('.modal-img');

        // 点击缩略图显示大图
        thumbnail.addEventListener('click', (e) => {
            modal.style.display = 'flex';
            modalImg.src = e.target.src;
        });

        // 点击任意位置关闭弹窗
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        //         // 数据相关
        // let allStores = [];
        // let filteredStores = [];
        // let currentPage = 1;
        // const pageSize = 50;
        // let isLoading = false;
        //
        // // 省市数据
        const provinces = {
            "北京": ["北京市"],
            "天津": ["天津市"],
            "上海": ["上海市"],
            "重庆": ["重庆市"],
            "河北": ["石家庄市", "唐山市", "秦皇岛市", "邯郸市", "邢台市", "保定市", "张家口市", "承德市", "沧州市", "廊坊市", "衡水市"],
            "山西": ["太原市", "大同市", "朔州市", "忻州市", "阳泉市", "长治市", "晋城市", "晋中市", "临汾市", "运城市", "吕梁市"],
            "内蒙古": ["呼和浩特市", "包头市", "乌海市", "赤峰市", "通辽市", "鄂尔多斯市", "呼伦贝尔市", "巴彦淖尔市", "乌兰察布市"],
            "辽宁": ["沈阳市", "大连市", "鞍山市", "抚顺市", "本溪市", "丹东市", "锦州市", "营口市", "阜新市", "辽阳市", "盘锦市", "铁岭市", "朝阳市", "葫芦岛市"],
            "吉林": ["长春市", "吉林市", "四平市", "辽源市", "通化市", "白山市", "松原市", "白城市", "延边朝鲜族自治州"],
            "黑龙江": ["哈尔滨市", "齐齐哈尔市", "鸡西市", "鹤岗市", "双鸭山市", "大庆市", "伊春市", "佳木斯市", "七台河市", "牡丹江市", "黑河市", "绥化市", "大兴安岭地区"],
            "江苏": ["南京市", "无锡市", "徐州市", "常州市", "苏州市", "南通市", "连云港市", "淮安市", "盐城市", "扬州市", "镇江市", "泰州市", "宿迁市"],
            "浙江": ["杭州市", "宁波市", "温州市", "嘉兴市", "湖州市", "绍兴市", "金华市", "衢州市", "舟山市", "台州市", "丽水市"],
            "安徽": ["合肥市", "芜湖市", "蚌埠市", "淮南市", "马鞍山市", "淮北市", "铜陵市", "安庆市", "黄山市", "滁州市", "阜阳市", "宿州市", "六安市", "亳州市", "池州市", "宣城市"],
            "福建": ["福州市", "厦门市", "莆田市", "三明市", "泉州市", "漳州市", "南平市", "龙岩市", "宁德市"],
            "江西": ["南昌市", "景德镇市", "萍乡市", "九江市", "新余市", "鹰潭市", "赣州市", "吉安市", "宜春市", "抚州市", "上饶市"],
            "山东": ["济南市", "青岛市", "淄博市", "枣庄市", "东营市", "烟台市", "潍坊市", "济宁市", "泰安市", "威海市", "日照市", "临沂市", "德州市", "聊城市", "滨州市", "菏泽市"],
            "河南": ["郑州市", "开封市", "洛阳市", "平顶山市", "安阳市", "鹤壁市", "新乡市", "焦作市", "濮阳市", "许昌市", "漯河市", "三门峡市", "南阳市", "商丘市", "信阳市", "周口市", "驻马店市"],
            "湖北": ["武汉市", "黄石市", "十堰市", "宜昌市", "襄阳市", "鄂州市", "荆门市", "孝感市", "荆州市", "黄冈市", "咸宁市", "随州市", "恩施土家族苗族自治州"],
            "湖南": ["长沙市", "株洲市", "湘潭市", "衡阳市", "邵阳市", "岳阳市", "常德市", "张家界市", "益阳市", "郴州市", "永州市", "怀化市", "娄底市", "湘西土家族苗族自治州"],
            "广东": ["广州市", "深圳市", "珠海市", "汕头市", "佛山市", "韶关市", "湛江市", "肇庆市", "江门市", "茂名市", "惠州市", "梅州市", "汕尾市", "河源市", "阳江市", "清远市", "东莞市", "中山市", "潮州市", "揭阳市", "云浮市"],
            "广西": ["南宁市", "柳州市", "桂林市", "梧州市", "北海市", "防城港市", "钦州市", "贵港市", "玉林市", "百色市", "贺州市", "河池市", "来宾市", "崇左市"],
            "海南": ["海口市", "三亚市", "三沙市", "儋州市"],
            "四川": ["成都市", "自贡市", "攀枝花市", "泸州市", "德阳市", "绵阳市", "广元市", "遂宁市", "内江市", "乐山市", "南充市", "眉山市", "宜宾市", "广安市", "达州市", "雅安市", "巴中市", "资阳市", "阿坝藏族羌族自治州", "甘孜藏族自治州", "凉山彝族自治州"],
            "贵州": ["贵阳市", "六盘水市", "遵义市", "安顺市", "毕节市", "铜仁市", "黔西南布依族苗族自治州", "黔东南苗族侗族自治州", "黔南布依族苗族自治州"],
            "云南": ["昆明市", "曲靖市", "玉溪市", "保山市", "昭通市", "丽江市", "普洱市", "临沧市", "楚雄彝族自治州", "红河哈尼族彝族自治州", "文山壮族苗族自治州", "西双版纳傣族自治州", "大理白族自治州", "德宏傣族景颇族自治州", "怒江傈僳族自治州", "迪庆藏族自治州"],
            "西藏": ["拉萨市", "日喀则市", "昌都市", "林芝市", "山南市", "那曲市", "阿里地区"],
            "陕西": ["西安市", "铜川市", "宝鸡市", "咸阳市", "渭南市", "延安市", "汉中市", "榆林市", "安康市", "商洛市"],
            "甘肃": ["兰州市", "嘉峪关市", "金昌市", "白银市", "天水市", "武威市", "张掖市", "平凉市", "酒泉市", "庆阳市", "定西市", "陇南市", "临夏回族自治州", "甘南藏族自治州"],
            "青海": ["西宁市", "海东市", "海北藏族自治州", "黄南藏族自治州", "海南藏族自治州", "果洛藏族自治州", "玉树藏族自治州", "海西蒙古族藏族自治州"],
            "宁夏": ["银川市", "石嘴山市", "吴忠市", "固原市", "中卫市"],
            "新疆": ["乌鲁木齐市", "克拉玛依市", "吐鲁番市", "哈密市", "昌吉市", "博尔塔拉蒙古自治州", "巴音郭楞蒙古自治州", "阿克苏地区", "克孜勒苏柯尔克孜自治州", "喀什地区", "和田地区", "伊犁哈萨克自治州", "塔城地区", "阿勒泰地区"],
        };
        //
        // // 初始化省份选择
        const provinceSelect = document.getElementById('provinceSelect');
        var province = "";
        var city = "";
        Object.keys(provinces).forEach(province => {
            const option = document.createElement('option');
            option.value = province;
            option.textContent = province;
            provinceSelect.appendChild(option);
        });

        // 城市选择联动
        provinceSelect.addEventListener('change', function() {
            const citySelect = document.getElementById('citySelect');
            citySelect.innerHTML = '<option value="">全部城市</option>';

            if (this.value) {
                provinces[this.value].forEach(city => {
                    const option = document.createElement('option');
                    option.value = city;
                    option.textContent = city;
                    citySelect.appendChild(option);
                });
                citySelect.disabled = false;
            } else {
                citySelect.disabled = true;
            }
            applyFilters();
        });
        //
        // 搜索和筛选功能
        let searchTimeout;
        document.getElementById('searchInput').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(applyFilters, 300);
        });
        //
        document.getElementById('citySelect').addEventListener('change', applyFilters);
        //
        //
        //
        // // 生成模拟数据
        // function generateMockData() {
        //     return Array.from({length: 300}, (_, i) => ({
        //         id: i,
        //         name: `服务站名称${i + 1}`,
        //         customName: `公司名称${String.fromCharCode(65 + i % 26)}`,
        //         address: `${Object.keys(provinces)[i % 5]}${provinces[Object.keys(provinces)[i % 5]][0]}某某路${i + 1}号`,
        //         phone: `138${Math.floor(10000000 + Math.random() * 90000000)}`,
        //         images: [
        //             'https://via.placeholder.com/100x100/FFB6C1/000000?text=Image1',
        //             'https://via.placeholder.com/100x100/87CEFA/000000?text=Image2',
        //             'https://via.placeholder.com/100x100/98FB98/000000?text=Image3'
        //         ],
        //         province: Object.keys(provinces)[i % 5],
        //         city: provinces[Object.keys(provinces)[i % 5]][0]
        //     }));
        // }
        //
        // // 过滤函数
        function applyFilters() {
            const provinces = document.getElementById('provinceSelect').value;
            const citys = document.getElementById('citySelect').value;
            province = provinces
            city = citys
            ajaxcommon();
        }
        //
        // // 加载逻辑
        // function loadMoreStores() {
        //     if (isLoading) return;
        //
        //     isLoading = true;
        //     document.getElementById('loading').style.display = 'block';
        //
        //     setTimeout(() => {
        //         const start = (currentPage - 1) * pageSize;
        //         const end = start + pageSize;
        //         const pageData = filteredStores.slice(start, end);
        //
        //         const storeList = document.getElementById('storeList');
        //         pageData.forEach(store => {
        //             storeList.insertAdjacentHTML('beforeend', renderStoreItem(store));
        //         });
        //
        //         currentPage++;
        //         isLoading = false;
        //         document.getElementById('loading').style.display = 'none';
        //
        //         if (end >= filteredStores.length) {
        //             document.getElementById('endLine').style.display = 'block';
        //             window.removeEventListener('scroll', scrollHandler);
        //         } else {
        //             window.addEventListener('scroll', scrollHandler);
        //         }
        //     }, 500);
        // }
        //
        //
        // function renderStoreItem(store) {
        //     return `
        //         <div class="store-item">
        //             <div class="store-images">
        //                 ${store.images.map(img => `<img src="${img}" alt="门店图片">`).join('')}
        //             </div>
        //             <div class="store-name">${store.name}</div>
        //             <div class="custom-name">${store.customName}</div>
        //             <div class="store-info">
        //                 <div>地址：${store.address}</div>
        //                 <div>电话：<a href="tel:${store.phone}">${store.phone}</a></div>
        //             </div>
        //         </div>
        //     `
        // }
        //
        //
        //
        // function scrollHandler() {
        //     const { scrollTop, scrollHeight, clientHeight } = document.documentElement
        //     if (scrollTop + clientHeight >= scrollHeight - 100) {
        //         loadMoreStores()
        //     }
        // }
        //
        // // 初始化
        // allStores = generateMockData();
        // filteredStores = [...allStores];
        // loadMoreStores();
        // window.addEventListener('scroll', scrollHandler);
        var page=1,pages='{:(int)$page->Total_Pages}';
        var kwd = '';

        $('.js_kwd').blur(function () {
            var  new_kwd = $(this).val();
            if (new_kwd != kwd) {
                $(this).siblings().removeClass("on");
                $(this).addClass("on");
                kwd = new_kwd
                ajaxcommon();
            }
        })
        function ajaxcommon() {
            $.get('/servicenetwork/index?n=1&p=' + page + '&kwd='+kwd + '&province='+province+'&city='+city, function(str){
                console.log(str);
                p = 1;
                $('#storeList').html(str);
            }, 'html');
        }
        function loadmore(){
            console.log(pages)
            if(page<pages){
                page+=1;
                $.get('/servicenetwork/index?n=1&p=' + page + '&kwd='+kwd + '&province='+province+'&city='+city, function(str){
                    console.log(str)
                    $('#storeList').append(str);
                }, 'html');
            } else if (page==pages) {
                page+=1;
                // setTimeout("$('#storeList').append('<p class=\"nomore\">------ 没有了 ------</p>')", 500);

                setTimeout("$('#storeList').append('<div class=\"end-line\" id=\"endLine\" style=\"display: block;\">我可是有底线的</div>')", 500);
            }
        }

        $(window).scroll(function(){
            var scrollTop = $(this).scrollTop();     //滚动条距离顶部的高度
            var scrollHeight = $(document).height(); //当前页面的总高度
            var clientHeight = $(this).height();     //当前可视的页面高度
            console.log("top:"+scrollTop+",doc:"+scrollHeight+",client:"+clientHeight);
            if(scrollTop + clientHeight >= (scrollHeight-10)) {
                loadmore();
            }else if(scrollTop<=0){
            }
        });
    </script>
</body>
</html>