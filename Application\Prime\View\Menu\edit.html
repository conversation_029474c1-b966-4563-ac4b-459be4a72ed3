<include file="block/hat" />
<script type="text/javascript" src="__ROOT__/static/js/lib/jquery-ui-1.10.3.min.js"></script>
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Menu/nav" />
			<style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
<div class="main">
	<form action="" method="post" class="form-horizontal form js-ajax-form" enctype="multipart/form-data" id="form1">
		<div class="panel panel-default">
			<div class="panel-body">
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">上级</label>
					<div class="col-sm-9 col-xs-12">
						<select class="form-control sp_input" name="parentid" autocomplete="off">
							<option value="0">作为一级菜单</option>{$select_categorys}
						</select>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>名称</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="name" class="form-control sp_input" value="{$row.name}" />
                        <span class="help-block sp_span">*</span>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>应用</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="app" class="form-control sp_input" value="{$row.app}" />
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>控制器</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="model" class="form-control sp_input" value="{$row.model}" />
                        <span class="help-block sp_span">*</span>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>方法</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="action" class="form-control sp_input" value="{$row.action}" />
                        <span class="help-block sp_span">*</span>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">参数</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="data" class="form-control sp_input" value="{$row.data}" />
                        <span class="help-block sp_span">例：id=3&p=3</span>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">图标</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="icon" class="form-control sp_input" value="{$row.icon}" />
                        <span class="help-block sp_span">不带前缀fa-，如fa-user => user</span>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">备注</label>
					<div class="col-sm-9 col-xs-12">
						<textarea rows="3" class="form-control" name="remark" style="width:60%">{$row.remark}</textarea>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">状态</label>
					<div class="col-sm-9 col-xs-12">
						<select class="form-control sp_input" id="pcate" name="status" autocomplete="off">
							<php> foreach(D('Menu')->status as $k => $v) {</php>
							<option value="{$k}" <php>if($row['status'] == $k) echo 'selected';</php>>{$v}</option>
							<php> } </php>
						</select>
					</div>
				</div>
			</div>
		</div>
		<div class="form-group col-sm-12">
            <input type="hidden" name="id" value="{$row.id}" />
			<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1 js-ajax-submit" />
		</div>
	</form>
</div>


		</div>
	</div>
</div>
<link rel="stylesheet" href="__ROOT__/static/js/app/layer/skin/layer.css" />
<script src="__ROOT__/static/js/prime/common.js"></script>
<script>

//function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}
require(['layer']);
//require(['layer'], function(layer) {
//_alert(123);
//layer.alert(123);
//layer.alert(123)
//});


$(function(){
	//function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}

	//layer.config({
		//extend: 'extend/layer.ext.js'
	//});
	//_alert(123)
})

</script>
<include file="block/footer" />