<?php

namespace Prime\Controller;

use Common\Controller\PrimeController;

class QuestionController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    public function index()
    {
        $c_kw = [
            'id' => 'ID',
            'category_id' => '所属分类ID',
            'title' => '名称',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        if ($s_status != '') $where['status'] = $s_status;

        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if ($s_kw == 'category_id') {
                $where[$s_kw] = ['like', "%,$s_val,%"];
            } elseif ($s_kw == 'title') {
                $where[$s_kw] = ['like', "%$s_val%"];
            } else {
                $where[$s_kw] = $s_val;
            }

        }

        $obj = M("Question");
        $count = $obj->where($where)->count();
        $page = $this->page($count);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        if ($list) {
            $category_array_id = array_column($list, 'category_id');
            $category_array_id = $category_array_id ?: [];
            $categoryList = D("QuestionCategory")->getField('id,title');
            foreach ($list as &$row) {
                if (strpos($row['category_id'], ',') !== false) {
                    $category_array = explode(',', $row['category_id']);
                    $string = '';
                    foreach ($category_array as $category_id) {
                        $string .= $categoryList[$category_id]." &nbsp;";

                    }
                    $row['category_title']  = $string;
                } else {
                    $row['category_title'] = $categoryList[$row['category_id']];
                }
            }
        }
        $this->assign('statusList', D("Question")->status);
        $this->assign('_get', I("get."));
        $this->assign('list', $list);
        $this->assign('c_kw', $c_kw);
        $this->assign('page', $page->show());
        $this->display();
    }

    public function edit()
    {
        $id = intval(I('get.id'));
        $obj = D("Question");
        if ($id) {
            $row = $obj->where("id=" . $id)->find();
            if (!$row) $this->error('参数错误');
            if (strpos($row['category_id'], ',') !== false) {
                $row['category_array'] = explode(',', $row['category_id']);
            } else {
                $row['category_array'] = [$row['category_id']];
            }

            $this->assign('row', $row);
        }

        if (IS_POST) {
            if ($data = $obj->create()) {
                $category = I('post.category_id');
                if (!$category) $this->error('请选择分类');
                $category_id = implode(',', $category);
                $category_id = ','.$category_id.",";
                $data['category_id'] = $category_id;
                if (!$id) {
                    $insertId = $obj->add($data);
                } else {
                    $obj->save($data);
                }
                $this->success("操作成功", U("question/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $category = M("QuestionCategory")->select();
        $this->assign('category', $category);
        $this->display();
    }

    //状态
    public function cgstat()
    {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("Question");
        if (!array_key_exists($status, $obj->status)) $this->error('参数错误 ');
        $obj->save(['id' => $id, 'status' => $status]);
        $this->success('修改成功');
    }
}
