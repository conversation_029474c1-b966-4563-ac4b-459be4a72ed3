<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Userjob/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">简历编辑</div>
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>姓名</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="name" class="form-control sp_input" value="{$row.name}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>求职诉求</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="remark" class="form-control sp_input" value="{$row.remark}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">简历状态</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="job_state" class="form-control sp_input">
                                    <option value="0" {:  $row['job_state'] == '0' ? 'selected' : ''} >沟通中</option>
                                    <option value="1" {:  $row['job_state'] == '1' ? 'selected' : ''} >培训中</option>
                                    <option value="2" {:  $row['job_state'] == '2' ? 'selected' : ''} >已入职</option>
                                    <option value="3" {:  $row['job_state'] == '3' ? 'selected' : ''} >服务终止</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">所属服务站</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="service_station_id" class="form-control sp_input">
                                    <php>foreach($serviceStationList as $serviceStationId => $serviceName) {</php>
                                    <option value="{:$serviceStationId}" {:  $row['service_station_id'] == $serviceStationId ? 'selected' : ''} >{:$serviceStationId}-{:$serviceName}</option>
                                    <php>}</php>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">服务说明</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="job_state_text" class="form-control sp_input" value="{$row.job_state_text}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                             
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">头像</label>
                            <div class="col-sm-9 col-xs-12">
                                <php>echo tpl_form_field_image('photo_path', $row['photo_path'], '', ['type'=>4,'extras' => ['text' => 'readonly']])</php>
                                <span class="help-block">头像</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">性别</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="gender" class="form-control sp_input">
                                    <option value="男" {:  $row['gender'] == '男' ? 'selected' : ''} >男</option>
                                    <option value="女" {:  $row['gender'] == '女' ? 'selected' : ''} >女</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">出生日期</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="birthdate"    value="{: $row['birthdate']}" data-withtime="true" placeholder="请选择时间" autocomplete="off" class="datetimepicker form-control sp_input">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>身份证</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="id_number" class="form-control sp_input" value="{$row.id_number}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>民族</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="nation" class="form-control sp_input" value="{$row.nation}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>政治面貌</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="political_status" class="form-control sp_input" value="{$row.political_status}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>婚姻状况</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="marital_status" class="form-control sp_input" value="{$row.marital_status}"/>
                                <span class="help-block sp_span">*已婚/未婚</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>健康状况</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="health_status" class="form-control sp_input" value="{$row.health_status}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>身高(cm)</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="height" class="form-control sp_input" value="{$row.height}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>体重(kg)</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="weight" class="form-control sp_input" value="{$row.weight}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>视力</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="vision" class="form-control sp_input" value="{$row.vision}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">是否恐高</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="is_afraid_heights" class="form-control sp_input">
                                    <option value="1" {:  $row['is_afraid_heights'] == 1 ? 'selected' : ''} >恐高</option>
                                    <option value="2" {:  $row['is_afraid_heights'] ==  2 ? 'selected' : ''} >不恐高</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>应聘职位</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="applied_position" class="form-control sp_input" value="{$row.applied_position}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group col-sm-12">
					<input type="hidden" name="id" value="{$row.id}"/>
					<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			    </div>
            </div>
            </form>
        </div>
    </div>
</div>
<include file="block/footer" />
<script>
    require(["datetimepicker", 'layer', 'util'], function($, layer, util){

        $(function () {
            $('.form input[name="type"]').change(function () {
                var type = $(this).val();
                $('.js_type').hide();
                $('.js_type'+type).show();
            });
        })

        $(".datetimepicker").each(function(){
            var opt = {
                language: "zh-CN",
                format: "yyyy-mm-dd",
                minView: 2,
                autoclose: true,
                format : "yyyy-mm-dd",
                minView : 2,
                minuteStep:1,
            };
            $(this).datetimepicker(opt);
        });
    });
    //function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
            u.editor($('.richtext1')[0]);

        })
    });
</script>