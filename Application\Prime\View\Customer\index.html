<include file="block/hat" />
<script type="text/javascript" src="__ROOT__/static/js/lib/jquery-ui-1.10.3.min.js"></script>
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<ul class="nav nav-tabs">
				<li class="active"><a href="{:U('customer/index')}">客服列表</a></li>
				<li><a href="{:U('customer/edit')}">添加客服</a></li>
			</ul>
			<style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
<style>
.label{cursor:pointer;}
</style>
<div class="panel panel-info">
	<!--<div class="panel-heading">筛选</div>-->
	<div class="panel-body">
		<form method="get" class="form-inline" role="form" id="form1">
			<div class="form-group">
				<select class="form-control" name="kw">
					<php>foreach($c_kw as $key=>$value){</php>
						<option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
					<php>}</php>
				</select>=<input class="form-control" type="text" name="val" value="{$_get.val}" />
			</div>
			<input type="hidden" name="type" value="{$type}">
			<input name="time[start]" type="hidden" value="{$s_start}" />
			<input name="time[end]" type="hidden" value="{$s_end}" />
			<button title="加入时间" class="btn btn-default daterange daterange-time" type="button">
				<span class="date-title"><php>echo !$s_start && !$s_end  ? '加入时间' : $s_start?:'-'; echo ' 至 '; echo $s_end?:'-';</php></span>
				<i class="fa fa-calendar"></i></button>

			<button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> 搜索</button>
			<a href="{:U('customer/index')}" class="btn btn-warning" >重置</a>
		</form>
	</div>
</div>
<form action="" method="post" id="form" >
<div class="panel panel-default">
	<div class="panel-body table-responsive">
		<table class="table table-hover">
			<thead class="navbar-inner">
				<tr>
					<th width="120"><label class="form-inline" style="margin-top: 0px;margin-right: 5px; vertical-align: middle"><input type="checkbox" class="all" style="margin-right: 5px">全选</label>ID</th>
					<th>昵称</th>
					<th>二维码</th>
					<th><a href="<php>echo getSortUrl('bind_num')</php>">绑定量</a><i class="fa fa-sort"></i></th>
					<th>
						<a href="<php>echo getSortUrl('create_time')</php>">加入时间</a><br>
					</th>
					<th>状态</th>
				</tr>
			</thead>
			<tbody>
				<php>foreach($list as $row) { </php>
				<tr>
					<td><input type="checkbox" class="form-inline js_checkbox" value="{$row.id}" style="margin-top: -3px;margin-right: 5px; vertical-align: middle">
						{$row.id}</td>
					<td>
						{$row.name}<br>
					</td>
					<td><img src="{:$row['qrcode']}" width="80"></td>
					<td>{$row.bind_num} </td>
					<td>
						{:$typeList[$row['type']]['text']}
					</td>
					<td>
						<?=date('m-d H:i', $row['create_time'])?><br>
					</td>
					<td>
						<php>echo customerStatBtn($row['id'], $row['status']);</php>
						<php> if ($row['status']==1) { </php>
                            <span class="label label-success">正常</span>
                            <a href="#" url="{:U('customer/cgstat', ['id' => $row['id'], 'status' => 0])}" class="js_ajax">禁用</a>
                        <php> } else { </php>
                            <span class="label label-danger">禁用</span>
                            <a href="#" url="{:U('customer/cgstat', ['id' => $row['id'], 'status' => 1])}" class="js_ajax">启用</a>
                        <php> } </php><br>

					</td>
				</tr>
				<php>}</php>
			</tbody>
		</table>
		<php> if ($page) { </php>
		{$page}
		<php> } else { </php>
		<div style="text-align:center;padding:20px;border-top:1px solid #ccc;">共 <?=count($list)?> 条记录</div>
		<php>}</php>
	</div>
	</div>
</div>
</form>
		</div>
	</div>
</div>
<include file="block/footer" />
<script>
    require(["daterangepicker"], function($){

        $('.js_status').click(function() {
            var that = $(this),
                url = that.attr('url');
            $.get(url, function(data) {
                window.location.reload();
            });

        })

        $(function(){
            //批量点击审核
            $('.all').click(function () {
                var $form = $("#form");
                if ($(this).is(":checked")) {
                    $('.all').prop('checked', true);
                    $form.find(".js_checkbox").prop('checked', true);
                } else {
                    $('.all').prop('checked', false);
                    $form.find(".js_checkbox").prop('checked', false);
                }
            });

            $(".js_batch_promotion").on('click', function () {
                var $form = $("#form"),
					status = $(this).attr('data-status'),
                    checkedArray = [];
                $form.find(".js_checkbox").each(function () {
                    if ($(this).is(":checked")) {
                        checkedArray.push($(this).val())
                    }
                });
                if (checkedArray.length == 0) {
                    layer.msg("请选择要升级的推广人");
                    return
                }
                console.log(checkedArray);

                $.ajax({
                    dataType : 'json',
                    url : "{:U('agent/batchpromotion')}",
                    type : 'post',
                    data : {ids : checkedArray, 'status' : status},
                    beforeSend : function() {
                        layer.load();
                    },
                    success : function(data){
                        layer.closeAll();
                        layer.msg(data.info, { time : 2000});
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000)

                    },
                    error : function() {
                        layer.msg('网络出错，休息一会', { time : 2000});
                        me.send = true;
                    }
                });
            });

            $(".daterange.daterange-time").each(function(){
                var elm = this;
                $(this).daterangepicker({
                    startDate: $(elm).prev().prev().val(),
                    endDate: $(elm).prev().val(),
                    format: "YYYY-MM-DD HH:mm",
                    timePicker: false,
                    timePicker12Hour : false,
                    timePickerIncrement: 1,
                    minuteStep: 1
                }, function(start, end){
                    $(elm).find(".date-title").html(start.toDateTimeStr() + " 至 " + end.toDateTimeStr());
                    $(elm).prev().prev().val(start.toDateTimeStr());
                    $(elm).prev().val(end.toDateTimeStr());
                });
            });
            $('[data-toggle="tooltip"]').tooltip();
            $('.poster').click(function(){

                var id = $(this).data('id');
                $.getJSON('{:U("sell/poster")}?id='+id, function(res){
                    layer.open({
                        type: 1,
                        title: false,
                        closeBtn: 0,
                        shadeClose: true,
                        content: '<div style="display: block;width: 350px;height: 500px;overflow-y: scroll;"><img style="max-width:350px;" src="'+res.url+'" /></div>'
                    });
                });
            });
        });

    });
</script>
