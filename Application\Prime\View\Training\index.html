<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化培训订单管理页面样式 */
                .training-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .training-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .training-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .training-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .training-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .training-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .training-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .training-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .training-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .training-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .training-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .training-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                    text-decoration: none;
                }

                .training-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .training-index-stats-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
                    text-decoration: none;
                }

                .training-index-stats-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .training-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .training-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .training-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .training-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .training-index-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .training-index-nav-link.active {
                    color: #667eea !important;
                    background: white !important;
                    border-bottom-color: #667eea !important;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1) !important;
                    font-weight: 700 !important;
                }

                .training-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .training-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 搜索面板 - 默认隐藏 */
                .training-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    display: none;
                    animation: slideDown 0.3s ease-out;
                }

                .training-index-search-panel.show {
                    display: block;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .training-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .training-index-search-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .training-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .training-index-search-close {
                    background: none;
                    border: none;
                    color: #718096;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                }

                .training-index-search-close:hover {
                    background: #f1f5f9;
                    color: #374151;
                }

                .training-index-search-body {
                    padding: 2rem;
                }

                /* 现代化培训订单卡片 */
                .training-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .training-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .training-card-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .training-card-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .training-card-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin: 0;
                }

                .training-card-id {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                /* 订单ID区域样式 */
                .training-card-id-section {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 0.5rem;
                }

                /* 订单类型标签样式 */
                .training-order-type-tag {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 0.875rem;
                    font-weight: 600;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                }

                .training-order-type-tag.type-station {
                    background: rgba(59, 130, 246, 0.9);
                    color: white;
                    border-color: rgba(59, 130, 246, 0.5);
                }

                .training-order-type-tag.type-zsb {
                    background: rgba(34, 197, 94, 0.9);
                    color: white;
                    border-color: rgba(34, 197, 94, 0.5);
                }

                .training-card-user-name {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .training-card-mobile {
                    font-size: 1.25rem;
                    color: rgba(255, 255, 255, 0.9);
                    margin: 0.25rem 0 0 0;
                    font-weight: 400;
                }

                .training-card-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    flex-direction: column;
                }

                .training-status-badge {
                    padding: 0.5rem 1rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    margin-bottom: 0.5rem;
                }

                .training-status-badge.status-new {
                    background: rgba(251, 191, 36, 0.9);
                    border-color: rgba(251, 191, 36, 0.5);
                }

                .training-status-badge.status-approved {
                    background: rgba(16, 185, 129, 0.9);
                    border-color: rgba(16, 185, 129, 0.5);
                }

                .training-status-badge.status-rejected {
                    background: rgba(239, 68, 68, 0.9);
                    border-color: rgba(239, 68, 68, 0.5);
                }

                .training-status-badge.status-paid {
                    background: rgba(59, 130, 246, 0.9);
                    border-color: rgba(59, 130, 246, 0.5);
                }

                .training-status-badge.status-training {
                    background: rgba(168, 85, 247, 0.9);
                    border-color: rgba(168, 85, 247, 0.5);
                }

                .training-status-badge.status-completed {
                    background: rgba(34, 197, 94, 0.9);
                    border-color: rgba(34, 197, 94, 0.5);
                }

                .training-card-body {
                    padding: 2rem;
                }

                .training-info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                .training-info-section {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    border: 1px solid #e2e8f0;
                }

                .training-info-section-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1rem 0;
                }

                .training-info-section-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .training-info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                }

                .training-info-item:last-child {
                    margin-bottom: 0;
                }

                .training-info-label {
                    color: #6b7280;
                    font-weight: 500;
                    min-width: 80px;
                    flex-shrink: 0;
                    font-size: 1.5rem;
                }

                .training-info-value {
                    color: #374151;
                    font-weight: 600;
                    flex: 1;
                    word-break: break-all;
                    font-size: 1.5rem;
                }

                /* 一行显示的培训信息样式 */
                .training-info-inline {
                    display: block;
                    padding: 0.75rem;
                    background: rgba(255, 255, 255, 0.7);
                    border-radius: 0.5rem;
                    border-left: 4px solid #667eea;
                    text-align: left;
                }

                .training-info-inline-item {
                    display: inline-block;
                    margin-right: 2rem;
                    margin-bottom: 0.5rem;
                    text-align: left;
                }

                .training-info-inline-item .training-info-label {
                    font-size: 1.5rem;
                    color: #6b7280;
                    font-weight: 500;
                    display: inline;
                }

                .training-info-inline-item .training-info-value {
                    font-size: 1.5rem;
                    color: #374151;
                    font-weight: 600;
                    display: inline;
                }

                /* 服务终止订单的展开/收起样式 */
                .training-terminated-toggle {
                    padding: 1rem;
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    color: white;
                    text-align: center;
                    cursor: pointer;
                    border-radius: 0.5rem;
                    margin-bottom: 1rem;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                }

                .training-terminated-toggle:hover {
                    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
                }

                .training-terminated-toggle i {
                    transition: transform 0.3s ease;
                }

                .training-terminated-toggle.expanded i {
                    transform: rotate(180deg);
                }

                .training-terminated-details {
                    animation: slideDown 0.3s ease;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        max-height: 0;
                    }
                    to {
                        opacity: 1;
                        max-height: 1000px;
                    }
                }

                /* 费用统计区域 */
                .training-fee-section {
                    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
                    border: 1px solid #10b981;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    margin-bottom: 2rem;
                }

                .training-fee-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #065f46;
                    margin: 0 0 1rem 0;
                }

                .training-fee-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 1rem;
                }

                .training-fee-item {
                    text-align: center;
                    padding: 1rem;
                    background: rgba(16, 185, 129, 0.1);
                    border-radius: 0.5rem;
                }

                .training-fee-label {
                    font-size: 1.5rem;
                    color: #065f46;
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                }

                .training-fee-value {
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: #047857;
                    margin: 0;
                }

                .training-reward-status {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    margin-top: 0.5rem;
                }

                .training-reward-status.status-pending {
                    background: rgba(251, 191, 36, 0.1);
                    color: #d97706;
                    border: 1px solid #f59e0b;
                }

                .training-reward-status.status-paid {
                    background: rgba(16, 185, 129, 0.1);
                    color: #047857;
                    border: 1px solid #10b981;
                }

                /* 操作按钮区域 */
                .training-actions {
                    display: flex;
                    gap: 0.75rem;
                    margin-top: 2rem;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                    flex-wrap: wrap;
                }

                .training-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .training-action-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .training-action-btn.btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .training-action-btn.btn-primary:hover {
                    color: white;
                }

                .training-action-btn.btn-success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                }

                .training-action-btn.btn-success:hover {
                    color: white;
                }

                .training-action-btn.btn-warning {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    color: white;
                }

                .training-action-btn.btn-warning:hover {
                    color: white;
                }

                .training-action-btn.btn-danger {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    color: white;
                }

                .training-action-btn.btn-danger:hover {
                    color: white;
                }

                .training-action-btn.btn-info {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                }

                .training-action-btn.btn-info:hover {
                    color: white;
                }

                /* 状态操作按钮样式 */
                .status-action-btn.btn-default:disabled {
                    background-color: #999 !important;
                    border-color: #999 !important;
                    color: #fff !important;
                    cursor: not-allowed !important;
                    opacity: 0.8;
                }

                .status-action-btn.btn-default:disabled:hover {
                    background-color: #999 !important;
                    border-color: #999 !important;
                    color: #fff !important;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .training-index-container {
                        padding: 1.5rem;
                    }

                    .training-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .training-info-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .training-index-container {
                        padding: 1rem;
                    }

                    .training-index-nav-tabs {
                        flex-direction: column;
                    }

                    .training-index-nav-item {
                        flex: none;
                    }

                    .training-index-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .training-index-nav-link.active {
                        border-bottom: none !important;
                        border-right-color: #667eea !important;
                    }

                    .training-index-title-main {
                        font-size: 1.75rem;
                    }

                    .training-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .training-actions {
                        flex-direction: column;
                    }

                    .training-card-header {
                        flex-direction: column;
                        gap: 1rem;
                        text-align: center;
                    }

                    .training-card-title {
                        flex-direction: column;
                        gap: 0.5rem;
                        align-items: center;
                    }

                    .training-card-id-section {
                        align-items: center;
                    }

                    .training-order-type-tag {
                        font-size: 0.75rem;
                        padding: 0.2rem 0.6rem;
                    }

                    .training-fee-grid {
                        grid-template-columns: repeat(2, 1fr);
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .training-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .training-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .training-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .training-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }

                /* 现代化状态操作弹窗样式 */
                .status-modal-container {
                    padding: 25px;
                    background: white;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                }

                .status-modal-fee-info {
                    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                    border-radius: 12px;
                    padding: 20px;
                    margin-bottom: 25px;
                    border: 1px solid #90caf9;
                    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
                }

                .fee-details-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 15px;
                    margin-top: 15px;
                }

                .fee-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 12px;
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 8px;
                    border: 1px solid rgba(33, 150, 243, 0.2);
                }

                .fee-label {
                    font-size: 12px;
                    color: #666;
                    margin-bottom: 5px;
                    font-weight: 500;
                }

                .fee-value {
                    font-size: 16px;
                    font-weight: 600;
                    color: #1976D2;
                }

                .fee-paid {
                    color: #4CAF50;
                }

                .fee-remaining {
                    color: #FF9800;
                }

                .status-form {
                    background: white;
                }

                .form-group-modern {
                    margin-bottom: 20px;
                }

                .form-label-modern {
                    display: flex;
                    align-items: center;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 8px;
                    font-size: 14px;
                }

                .form-control-modern {
                    width: 100%;
                    padding: 12px 16px;
                    border: 2px solid #e5e7eb;
                    border-radius: 8px;
                    font-size: 14px;
                    transition: all 0.3s ease;
                    background: #f9fafb;
                }

                .form-control-modern:focus {
                    outline: none;
                    border-color: #667eea;
                    background: white;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .form-control-modern:hover {
                    border-color: #d1d5db;
                    background: white;
                }

                .form-help-text {
                    font-size: 12px;
                    color: #6b7280;
                    margin-top: 5px;
                    display: block;
                }

                .form-error-text {
                    font-size: 12px;
                    color: #ef4444;
                    margin-top: 5px;
                    display: block;
                }

                /* Layer弹窗自定义皮肤 */
                .modern-layer-skin .layui-layer-title {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                    color: white !important;
                    border-radius: 12px 12px 0 0 !important;
                    font-weight: 500 !important;
                    padding: 15px 20px !important;
                    border: none !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                }

                .modern-layer-skin .layui-layer-content {
                    padding: 0 !important;
                    background: #f8f9fa !important;
                    border-radius: 0 0 12px 12px !important;
                }

                .modern-layer-skin .layui-layer-btn {
                    text-align: center !important;
                    padding: 20px !important;
                    background: #f8f9fa !important;
                    border-radius: 0 0 12px 12px !important;
                    min-width: 400px !important;
                    width: auto !important;
                    overflow: visible !important;
                }

                .modern-layer-skin .layui-layer-btn a {
                    border-radius: 8px !important;
                    font-weight: 500 !important;
                    padding: 15px 50px !important;
                    margin: 0 20px !important;
                    transition: all 0.3s ease !important;
                    min-width: 140px !important;
                    max-width: none !important;
                    width: auto !important;
                    height: auto !important;
                    text-align: center !important;
                    white-space: nowrap !important;
                    overflow: visible !important;
                    display: inline-block !important;
                    box-sizing: border-box !important;
                    font-size: 14px !important;
                    line-height: 1.4 !important;
                    text-overflow: clip !important;
                    word-wrap: normal !important;
                }

                .modern-layer-skin .layui-layer-btn0 {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                    border: none !important;
                    color: white !important;
                }

                .modern-layer-skin .layui-layer-btn0:hover {
                    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%) !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
                }

                .modern-layer-skin .layui-layer-btn1 {
                    background: #f8f9fa !important;
                    border: 1px solid #dee2e6 !important;
                    color: #6c757d !important;
                }

                .modern-layer-skin .layui-layer-btn1:hover {
                    background: #e9ecef !important;
                    border-color: #adb5bd !important;
                    transform: translateY(-1px) !important;
                }

                /* 二维码弹窗样式 */
                .qrcode-container {
                    background: #f8f9fa;
                }

                .qrcode-container img {
                    max-width: 100%;
                    height: auto;
                }

                /* 隐藏number输入框的spinner控件 */
                input[type="number"]::-webkit-outer-spin-button,
                input[type="number"]::-webkit-inner-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }

                /* Firefox */
                input[type="number"] {
                    -moz-appearance: textfield;
                }
            </style>

            <div class="training-index-wrapper">
                <div class="training-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="training-index-header training-index-fade-in">
                        <div class="training-index-header-content">
                            <div class="training-index-title">
                                <div class="training-index-title-icon">
                                    <i class="fa fa-graduation-cap"></i>
                                </div>
                                <div class="training-index-title-text">
                                    <h1 class="training-index-title-main">培训订单管理</h1>
                                    <p class="training-index-title-sub">Training Order Management</p>
                                </div>
                            </div>
                            <div class="training-index-actions">
                                <button type="button" class="training-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>
                                <if condition="$is_admin">
                                <a href="javascript:void(0)" class="training-index-stats-btn" onclick="showTrainingStats()">
                                    <i class="fa fa-bar-chart"></i>
                                    <span>统计报表</span>
                                </a>
                                </if>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="training-index-nav-container training-index-fade-in-delay-1">
                        <ul class="training-index-nav-tabs">
                            <li class="training-index-nav-item">
                                <a href="javascript:void(0)" class="training-index-nav-link active all-orders-filter">
                                    <i class="fa fa-list training-index-nav-icon"></i>
                                    <span>全部订单</span>
                                </a>
                            </li>
                            <li class="training-index-nav-item">
                                <a href="javascript:void(0)" class="training-index-nav-link quick-filter" data-filter="communication">
                                    <i class="fa fa-comments training-index-nav-icon"></i>
                                    <span>沟通流程</span>
                                </a>
                            </li>
                            <li class="training-index-nav-item">
                                <a href="javascript:void(0)" class="training-index-nav-link quick-filter" data-filter="training">
                                    <i class="fa fa-graduation-cap training-index-nav-icon"></i>
                                    <span>培训流程</span>
                                </a>
                            </li>
                            <li class="training-index-nav-item">
                                <a href="javascript:void(0)" class="training-index-nav-link quick-filter" data-filter="employment">
                                    <i class="fa fa-briefcase training-index-nav-icon"></i>
                                    <span>入职流程</span>
                                </a>
                            </li>
                            <li class="training-index-nav-item">
                                <a href="javascript:void(0)" class="training-index-nav-link quick-filter" data-filter="service_review">
                                    <i class="fa fa-check training-index-nav-icon"></i>
                                    <span>服务审查</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 搜索面板 - 默认隐藏 -->
                    <div class="training-index-search-panel" id="searchPanel">
                        <div class="training-index-search-header">
                            <div class="training-index-search-title">
                                <div class="training-index-search-icon">
                                    <i class="fa fa-search"></i>
                                </div>
                                <span>搜索筛选</span>
                            </div>
                            <button type="button" class="training-index-search-close" onclick="toggleSearchPanel()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                        <div class="training-index-search-body">
                            <form method="get" class="search-form" role="form">
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="control-label">搜索条件：</label>
                                            <select class="form-control" name="kw">
                                                <php>foreach($c_kw as $key=>$value){</php>
                                                <option value="{$key}" <php> if(($_get['kw'] && $key == $_get['kw']) || (!$_get['kw'] && $key == 'user_name')){</php>selected<php>}</php>>{$value}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">搜索内容：</label>
                                            <input class="form-control" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容" />
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="control-label">业务阶段：</label>
                                            <select name="main_status" class='form-control'>
                                                <option value="">全部阶段</option>
                                                <php> foreach($main_status_list as $k => $v) {</php>
                                                <option value="{$k}" <php>if($_get['main_status'] != '' && $_get['main_status']==$k) echo 'selected';</php> >{$v.text}</option>
                                                <php> }</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">详细状态：</label>
                                            <select name="sub_status" class='form-control'>
                                                <option value="">全部状态</option>
                                                <php> foreach($sub_status_list as $k => $v) {</php>
                                                <option value="{$k}" <php>if($_get['sub_status'] != '' && $_get['sub_status']==$k) echo 'selected';</php> >{$v.text}</option>
                                                <php> }</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <div>
                                                <button type="submit" class="btn btn-primary btn-block">
                                                    <i class="fa fa-search"></i> 搜索
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="alert alert-info" style="margin-top: 10px; margin-bottom: 0;">
                                            <i class="fa fa-info-circle"></i>
                                            <strong>提示：</strong>取消报名的订单默认不显示，但关键词搜索时会显示所有匹配的订单；如需单独查看取消报名的订单，请在"详细状态"中选择"服务终止"
                                        </div>
                                    </div>
                                </div>

                            </form>
                        </div>
                    </div>
                    <!-- 培训订单列表 -->
                    <div class="training-index-fade-in-delay-2">
                        <php>if(!empty($list)) { foreach($list as $v) { </php>
                        <div class="training-card">
                            <!-- 卡片头部 -->
                            <div class="training-card-header">
                                <div class="training-card-title">
                                    <div class="training-card-id-section">
                                        <!-- 订单类型标签 -->
                                        <php>if($v['is_zsb_order']) {</php>
                                        <span class="training-order-type-tag type-zsb">
                                            <i class="fa fa-building"></i>
                                            招就办订单
                                        </span>
                                        <php>} else {</php>
                                        <span class="training-order-type-tag type-station">
                                            <i class="fa fa-home"></i>
                                            服务站订单
                                        </span>
                                        <php>}</php>
                                        <span class="training-card-id">#{$v.id}</span>
                                    </div>
                                    <div>
                                        <h3 class="training-card-user-name">{$v.user_name}</h3>
                                        <p class="training-card-mobile">{$v.user_mobile}</p>
                                    </div>
                                </div>
                                <div class="training-card-status">
                                    <php>
                                        $mainStatusClass = '';
                                        switch($v['main_status_style']) {
                                            case 'info':
                                                $mainStatusClass = 'status-new';
                                                break;
                                            case 'warning':
                                                $mainStatusClass = 'status-training';
                                                break;
                                            case 'success':
                                                $mainStatusClass = 'status-completed';
                                                break;
                                            case 'danger':
                                                $mainStatusClass = 'status-rejected';
                                                break;
                                            default:
                                                $mainStatusClass = 'status-new';
                                        }
                                    </php>
                                    <span class="training-status-badge {$mainStatusClass}">
                                        <i class="{$v.main_status_icon}"></i>
                                        {$v.main_status_text}
                                    </span>
                                    <span class="training-status-badge">
                                        <i class="fa fa-info-circle"></i>
                                        {$v.sub_status_text}
                                    </span>
                                </div>
                            </div>

                            <!-- 卡片主体 -->
                            <php>if($v['sub_status'] == 'terminated') {</php>
                            <!-- 服务终止订单的展开/收起控制 -->
                            <div class="training-terminated-toggle" onclick="toggleTerminatedDetails({$v.id})">
                                <i class="fa fa-chevron-down" id="toggle-icon-{$v.id}"></i>
                                <span>点击查看详细信息</span>
                            </div>
                            <div class="training-card-body training-terminated-details" id="terminated-details-{$v.id}" style="display: none;">
                            <php>} else {</php>
                            <div class="training-card-body">
                            <php>}</php>
                                <div class="training-info-grid">
                                    <!-- 培训项目信息 -->
                                    <div class="training-info-section">
                                        <h4 class="training-info-section-title">
                                            <div class="training-info-section-icon">
                                                <i class="fa fa-graduation-cap"></i>
                                            </div>
                                            培训项目
                                        </h4>
                                        <div class="training-info-inline">
                                            <span class="training-info-inline-item">
                                                <span class="training-info-label">项目：</span>
                                                <span class="training-info-value">{$v.project_name}</span>
                                            </span>
                                            <span class="training-info-inline-item">
                                                <span class="training-info-label">岗位：</span>
                                                <span class="training-info-value">{$v.post_name}</span>
                                            </span>
                                            <span class="training-info-inline-item">
                                                <span class="training-info-label">服务站：</span>
                                                <span class="training-info-value">{$v.station_name}</span>
                                            </span>
                                            <php>if($v['is_zsb_order']) {</php>
                                            <span class="training-info-inline-item">
                                                <span class="training-info-label">招就办：</span>
                                                <span class="training-info-value">{$v.zsb_name}</span>
                                            </span>
                                            <php>}</php>
                                            <span class="training-info-inline-item">
                                                <span class="training-info-label">创建：</span>
                                                <span class="training-info-value">{:date('Y-m-d H:i', $v['create_time'])}</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 费用统计 -->
                                <div class="training-fee-section">
                                    <h4 class="training-fee-title">
                                        <i class="fa fa-money"></i>
                                        费用统计
                                    </h4>
                                    <div class="training-fee-grid">
                                        <div class="training-fee-item">
                                            <div class="training-fee-label">报名费</div>
                                            <div class="training-fee-value">¥{:number_format($v['fee_amount'] / 100, 2)}</div>
                                        </div>
                                        
                                        <!-- 部分付款显示区域 -->
                                        <php>if(isset($v['paid_amount']) && $v['paid_amount'] > 0 && $v['paid_amount'] < $v['fee_amount']) {</php>
                                        <div class="training-fee-item partial-payment">
                                            <div class="training-fee-label">已付款</div>
                                            <div class="training-fee-value" style="color: #16a34a;">¥{:number_format($v['paid_amount'] / 100, 2)}</div>
                                        </div>
                                        <div class="training-fee-item partial-payment">
                                            <div class="training-fee-label">待付款</div>
                                            <div class="training-fee-value" style="color: #dc2626;">¥{:number_format($v['remaining_amount'] / 100, 2)}</div>
                                        </div>
                                        <php>} elseif(isset($v['paid_amount']) && $v['paid_amount'] >= $v['fee_amount']) {</php>
                                        <div class="training-fee-item">
                                            <div class="training-fee-label">付款状态</div>
                                            <div class="training-fee-value" style="color: #16a34a;">
                                                <i class="fa fa-check-circle"></i> 已全额付款
                                            </div>
                                        </div>
                                        <php>}</php>
                                        <php>if($v['is_zsb_order']) {</php>
                                        <div class="training-fee-item">
                                            <div class="training-fee-label">服务站收益</div>
                                            <div class="training-fee-value">¥{:number_format($v['station_profit_detail'] / 100, 2)}</div>
                                        </div>
                                        <div class="training-fee-item">
                                            <div class="training-fee-label">招就办收益</div>
                                            <div class="training-fee-value">¥{:number_format($v['zsb_commission_detail'] / 100, 2)}</div>
                                        </div>
                                        <php>} else {</php>
                                        <div class="training-fee-item">
                                            <div class="training-fee-label">服务站奖励</div>
                                            <div class="training-fee-value">¥{:number_format($v['reward_station_amt'] / 100, 2)}</div>
                                        </div>
                                        <php>}</php>

                                        <!-- 平台收入（仅管理员可见） -->
                                        <if condition="$is_admin">
                                        <div class="training-fee-item" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; padding: 12px;">
                                            <div class="training-fee-label" style="color: rgba(255,255,255,0.9); font-weight: 600;">
                                                <i class="fa fa-bank" style="margin-right: 5px;"></i>
                                                平台收入
                                            </div>
                                            <div class="training-fee-value" style="color: white; font-weight: 700; font-size: 1.1em;">
                                                ¥{:number_format(($v['zcgk_commission'] ?: 0) / 100, 2)}
                                            </div>
                                        </div>
                                        </if>

                                        <div class="training-fee-item">
                                            <div class="training-fee-label">奖励状态</div>
                                            <div class="training-fee-value" style="font-size: 1.25rem;">
                                                <php>
                                                    $rewardStatusClass = '';
                                                    switch($v['reward_status_style']) {
                                                        case 'warning':
                                                            $rewardStatusClass = 'status-pending';
                                                            break;
                                                        case 'success':
                                                            $rewardStatusClass = 'status-paid';
                                                            break;
                                                        default:
                                                            $rewardStatusClass = 'status-pending';
                                                    }
                                                </php>
                                                <span class="training-reward-status {$rewardStatusClass}">
                                                    <i class="fa fa-circle"></i>
                                                    {$v.reward_status_text}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="training-actions">
                                    <a href="{:U('training/detail', ['id' => $v['id']])}" class="training-action-btn btn-info">
                                        <i class="fa fa-eye"></i>
                                        <span>查看详情</span>
                                    </a>

                                    <!-- 新的状态操作按钮（基于二级状态） -->
                                    <php>if(session('admin_id')) {</php>
                                        <button type="button" class="training-action-btn btn-primary status-action-btn" data-order-id="{$v.id}" data-sub-status="{$v.sub_status}" onclick="handleStatusAction(this, {$v.id}, '{$v.sub_status}')">
                                            <i class="fa fa-cog"></i>
                                            <span>状态操作</span>
                                        </button>
                                        <!-- 取消订单按钮 -->
                                        <php>if(!in_array($v['sub_status'], ['completed', 'terminated'])) {</php>
                                        <button type="button" class="training-action-btn btn-danger" onclick="cancelOrder({$v.id}, '{$v.sub_status}')">
                                            <i class="fa fa-ban"></i>
                                            <span>取消订单</span>
                                        </button>
                                        <php>}</php>
                                    <php>}</php>
                                </div>
                            </div>
                        </div>
                        <php>}} else {</php>
                        <div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;">
                            <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;">
                                <i class="fa fa-graduation-cap"></i>
                            </div>
                            <h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无培训订单</h3>
                            <p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 1rem;">请尝试调整搜索条件或联系管理员。</p>
                            <p style="font-size: 1.25rem; color: #9ca3af; margin-bottom: 2rem;">
                                <i class="fa fa-info-circle"></i>
                                提示：取消报名的订单默认不显示，但关键词搜索时会显示所有匹配的订单；如需单独查看取消报名的订单，请在筛选器中选择"服务终止"状态
                            </p>
                        </div>
                        <php>}</php>

                        <!-- 分页 -->
                        <div class="training-index-fade-in-delay-3" style="text-align: center; margin-top: 30px;">
                            {$page}
                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />



<script>
    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
            setTimeout(function() {
                panel.style.display = 'none';
            }, 300);
        } else {
            panel.style.display = 'block';
            setTimeout(function() {
                panel.classList.add('show');
            }, 10);
        }
    }

    require(["daterangepicker", "layer"], function ($) {
        $(function () {
            // 原有的日期范围选择器功能
            $(".daterange.daterange-time").each(function () {
                var elm = this;
                $(this).daterangepicker({
                    startDate: $(elm).prev().prev().val(),
                    endDate: $(elm).prev().val(),
                    format: "YYYY-MM-DD HH:mm",
                    timePicker: false,
                    timePicker12Hour: false,
                    timePickerIncrement: 1,
                    minuteStep: 1
                }, function (start, end) {
                    $(elm).find(".date-title").html(start.toDateTimeStr() + " 至 " + end.toDateTimeStr());
                    $(elm).prev().prev().val(start.toDateTimeStr());
                    $(elm).prev().val(end.toDateTimeStr());
                });
            });

            // 工具提示
            $('[data-toggle="tooltip"]').tooltip();

            // 二维码显示功能
            $('.qrcode').click(function () {
                var ticket = $(this).data('ticket');
                var desc = $(this).data('desc');

                layer.open({
                    type: 1,
                    title: '<div style="text-align: center;"><i class="fa fa-qrcode"></i> ' + desc + '</div>',
                    closeBtn: 1,
                    area: ['480px', 'auto'],
                    maxHeight: '80%',
                    offset: 'auto',
                    shade: 0.3,
                    shadeClose: true,
                    skin: 'modern-layer-skin',
                    content: '<div class="qrcode-container">' +
                        '<div style="text-align: center; padding: 25px;">' +
                        '<div style="background: white; padding: 20px; border-radius: 12px; display: inline-block; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">' +
                        '<img width="400" style="border-radius: 8px; display: block;" src="https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=' + ticket + '" />' +
                        '</div>' +
                        '<p style="margin-top: 15px; color: #6b7280; font-size: 14px;">请使用微信扫描上方二维码</p>' +
                        '</div>' +
                        '</div>',
                    success: function(layero, index) {
                        // 添加现代化样式
                        layero.css({
                            'border-radius': '12px',
                            'box-shadow': '0 10px 40px rgba(0,0,0,0.15)',
                            'border': 'none'
                        });

                        // 标题栏样式 - 使用绿色渐变
                        layero.find('.layui-layer-title').css({
                            'background': 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                            'color': 'white',
                            'border-radius': '12px 12px 0 0',
                            'font-weight': '500',
                            'padding': '15px 20px',
                            'border': 'none',
                            'display': 'flex',
                            'align-items': 'center',
                            'justify-content': 'center'
                        });

                        // 确保标题居中
                        layero.find('.layui-layer-title').find('div').css({
                            'width': '100%',
                            'text-align': 'center',
                            'display': 'flex',
                            'align-items': 'center',
                            'justify-content': 'center',
                            'gap': '8px'
                        });

                        // 内容区域样式
                        layero.find('.layui-layer-content').css({
                            'padding': '0',
                            'background': '#f8f9fa',
                            'border-radius': '0 0 12px 12px'
                        });

                        // 关闭按钮样式
                        layero.find('.layui-layer-close').css({
                            'color': 'white',
                            'font-size': '18px',
                            'font-weight': 'bold'
                        });
                    }
                });
            });

            // 全部订单筛选功能
            $('.all-orders-filter').click(function(e) {
                e.preventDefault();

                var url = new URL(window.location.href);
                // 清除所有筛选参数，显示全部订单
                url.searchParams.delete('main_status');
                url.searchParams.delete('sub_status');

                window.location.href = url.toString();
            });

            // 快速筛选功能
            $('.quick-filter').click(function(e) {
                e.preventDefault();
                var filter = $(this).data('filter');

                var url = new URL(window.location.href);

                // 清除现有的筛选参数
                url.searchParams.delete('main_status');
                url.searchParams.delete('sub_status');

                // 根据筛选类型设置参数
                switch(filter) {
                    case 'communication':
                        url.searchParams.set('main_status', 'communication');
                        break;
                    case 'training':
                        url.searchParams.set('main_status', 'training');
                        break;
                    case 'employment':
                        url.searchParams.set('main_status', 'employment');
                        break;
                    case 'service_review':
                        url.searchParams.set('main_status', 'service_review');
                        break;
                }

                // 跳转到新URL
                window.location.href = url.toString();
            });

            // 根据当前URL参数设置active状态
            function setActiveNavTab() {
                var urlParams = new URLSearchParams(window.location.search);
                var mainStatus = urlParams.get('main_status');

                // 移除所有active类
                $('.training-index-nav-link').removeClass('active');

                // 根据main_status参数设置对应的active类
                if (!mainStatus) {
                    // 没有筛选参数，显示全部订单为active
                    $('.all-orders-filter').addClass('active');
                } else {
                    var targetFilter = '';
                    switch(mainStatus) {
                        case 'communication':
                            targetFilter = 'communication';
                            break;
                        case 'training':
                            targetFilter = 'training';
                            break;
                        case 'employment':
                            targetFilter = 'employment';
                            break;
                        case 'service_review':
                            targetFilter = 'service_review';
                            break;
                    }
                    if (targetFilter) {
                        $('.quick-filter[data-filter="' + targetFilter + '"]').addClass('active');
                    }
                }
            }

            // 页面加载时设置active状态
            setActiveNavTab();
        });
    });

    // 页面加载时检查所有状态操作按钮
    $(document).ready(function() {
        checkAllStatusButtons();
    });

    // 检查所有状态操作按钮的可用性
    function checkAllStatusButtons() {
        var $buttons = $('.status-action-btn');
        if ($buttons.length === 0) return;

        // 批量检查，减少请求数量
        var orderIds = [];
        $buttons.each(function() {
            orderIds.push($(this).data('order-id'));
        });

        // 为每个按钮单独检查（保持原有逻辑，确保准确性）
        $buttons.each(function() {
            var $btn = $(this);
            var orderId = $btn.data('order-id');
            var subStatus = $btn.data('sub-status');

            // 先设置加载状态
            $btn.prop('disabled', true).text('检查中...');

            // 检查该订单是否有可操作选项
            $.get('{:U("training/getStatusOptions")}', {order_id: orderId})
                .done(function(res) {
                    if (res.status == 1 && res.data.length > 0) {
                        // 有可操作选项，保持正常样式
                        $btn.removeClass('btn-default').addClass('btn-primary');
                        $btn.prop('disabled', false);
                        $btn.text('状态操作');
                    } else {
                        // 无可操作选项，变成灰色
                        $btn.removeClass('btn-primary').addClass('btn-default');
                        $btn.prop('disabled', true);
                        $btn.text('状态操作');
                        $btn.css({
                            'background-color': '#999',
                            'border-color': '#999',
                            'cursor': 'not-allowed',
                            'color': '#fff'
                        });
                    }
                })
                .fail(function() {
                    // 请求失败时恢复默认状态
                    $btn.removeClass('btn-default').addClass('btn-primary');
                    $btn.prop('disabled', false);
                    $btn.text('状态操作');
                });
        });
    }

    // 全局变量存储订单信息
    var currentOrderInfo = null;

    // 切换终止服务订单的详细信息显示
    function toggleTerminatedDetails(orderId) {
        var detailsDiv = document.getElementById('terminated-details-' + orderId);
        var toggleIcon = document.getElementById('toggle-icon-' + orderId);
        var toggleDiv = detailsDiv.previousElementSibling;

        if (detailsDiv.style.display === 'none' || detailsDiv.style.display === '') {
            // 显示详细信息
            detailsDiv.style.display = 'block';
            toggleIcon.classList.remove('fa-chevron-down');
            toggleIcon.classList.add('fa-chevron-up');
            toggleDiv.classList.add('expanded');
            toggleDiv.querySelector('span').textContent = '点击隐藏详细信息';
        } else {
            // 隐藏详细信息
            detailsDiv.style.display = 'none';
            toggleIcon.classList.remove('fa-chevron-up');
            toggleIcon.classList.add('fa-chevron-down');
            toggleDiv.classList.remove('expanded');
            toggleDiv.querySelector('span').textContent = '点击查看详细信息';
        }
    }

    // 处理状态操作按钮点击，防止重复点击
    function handleStatusAction(button, orderId, currentStatus) {
        // 检查按钮是否已经被禁用
        if ($(button).hasClass('btn-disabled')) {
            return false;
        }

        // 禁用按钮并改变样式
        $(button).addClass('btn-disabled')
                 .removeClass('btn-primary')
                 .addClass('btn-secondary')
                 .prop('disabled', true);

        // 更改按钮文字和图标
        $(button).find('i').removeClass('fa-cog').addClass('fa-spinner fa-spin');
        $(button).find('span').text('处理中...');

        // 调用原始的状态模态框函数
        showStatusModal(orderId, currentStatus);

        // 5秒后重新启用按钮（防止用户长时间无法操作）
        setTimeout(function() {
            resetStatusButton(button);
        }, 5000);
    }

    // 重置状态操作按钮
    function resetStatusButton(button) {
        $(button).removeClass('btn-disabled btn-secondary')
                 .addClass('btn-primary')
                 .prop('disabled', false);
        $(button).find('i').removeClass('fa-spinner fa-spin').addClass('fa-cog');
        $(button).find('span').text('状态操作');
    }

    // 新的状态管理功能
    function showStatusModal(orderId, currentStatus) {
        // 获取可用的状态转换选项
        $.get('{:U("training/getStatusOptions")}', {order_id: orderId}, function(res) {
            if (res.status == 1) {
                var options = res.data;
                var orderInfo = res.order_info;
                currentOrderInfo = orderInfo; // 保存到全局变量

                if (options.length == 0) {
                    layer.msg('当前状态无可操作选项');
                    return;
                }

                var optionHtml = '';
                for (var i = 0; i < options.length; i++) {
                    var option = options[i];
                    optionHtml += '<option value="' + option.value + '" data-has-amount="' + (option.has_amount ? '1' : '0') + '" data-amount-field="' + (option.amount_field || '') + '">' + option.text + '</option>';
                }

                // 构建费用信息提示
                var feeInfoHtml = '';
                if (orderInfo.fee_amount_yuan > 0) {
                    feeInfoHtml = '<div class="alert alert-info" style="margin-bottom: 15px; font-size: 12px;">' +
                        '<strong>费用信息：</strong><br>' +
                        '报名费：¥' + orderInfo.fee_amount_yuan.toFixed(2) + '<br>' +
                        '已支付：¥' + orderInfo.paid_amount_yuan.toFixed(2) + '<br>' +
                        '剩余金额：¥' + orderInfo.remaining_amount_yuan.toFixed(2) +
                        '</div>';
                }

                // 构建支付方式选项
                var payChannelOptions = [
                    {value: 'offline', text: '线下支付'},
                    {value: 'alipay', text: '支付宝'},
                    {value: 'wechat', text: '微信支付'},
                    {value: 'bank', text: '银行转账'},
                    {value: 'manual', text: '手动录入'}
                ];

                var payChannelHtml = '';
                for (var i = 0; i < payChannelOptions.length; i++) {
                    var channel = payChannelOptions[i];
                    payChannelHtml += '<option value="' + channel.value + '">' + channel.text + '</option>';
                }

                var formHtml = '<div class="status-modal-container">' +
                    // 费用信息区域
                    (orderInfo.fee_amount_yuan > 0 ?
                        '<div class="status-modal-fee-info">' +
                        '<div style="display: flex; align-items: center; margin-bottom: 15px;">' +
                        '<i class="fa fa-info-circle" style="color: #2196F3; margin-right: 8px; font-size: 18px;"></i>' +
                        '<h4 style="margin: 0; color: #1976D2; font-weight: 600;">费用信息</h4>' +
                        '</div>' +
                        '<div class="fee-details-grid">' +
                        '<div class="fee-item">' +
                        '<span class="fee-label">报名费</span>' +
                        '<span class="fee-value">¥' + orderInfo.fee_amount_yuan.toFixed(2) + '</span>' +
                        '</div>' +
                        '<div class="fee-item">' +
                        '<span class="fee-label">已支付</span>' +
                        '<span class="fee-value fee-paid">¥' + orderInfo.paid_amount_yuan.toFixed(2) + '</span>' +
                        '</div>' +
                        '<div class="fee-item">' +
                        '<span class="fee-label">剩余金额</span>' +
                        '<span class="fee-value fee-remaining">¥' + orderInfo.remaining_amount_yuan.toFixed(2) + '</span>' +
                        '</div>' +
                        '</div>' +
                        '</div>' : '') +

                    // 表单区域
                    '<form id="statusForm" class="status-form">' +
                    '<div class="form-group-modern">' +
                    '<label class="form-label-modern">' +
                    '<i class="fa fa-exchange" style="margin-right: 8px; color: #4CAF50;"></i>' +
                    '选择新状态' +
                    '</label>' +
                    '<select name="status" class="form-control-modern" onchange="toggleAmountInput(this)">' +
                    '<option value="">请选择状态</option>' +
                    optionHtml +
                    '</select>' +
                    '</div>' +

                    '<div class="form-group-modern" id="amountGroup" style="display:none;">' +
                    '<label class="form-label-modern" id="amountLabel">' +
                    '<i class="fa fa-money" style="margin-right: 8px; color: #FF9800;"></i>' +
                    '金额' +
                    '</label>' +
                    '<input type="number" name="amount" class="form-control-modern" step="0.01" placeholder="请输入金额（元）" oninput="validateAmount(this)" onkeydown="preventArrowKeys(event)" onwheel="preventWheel(event)">' +
                    '<small id="amountHelp" class="form-help-text"></small>' +
                    '<div id="amountError" class="form-error-text" style="display:none;"></div>' +
                    '</div>' +

                    '<div class="form-group-modern" id="payChannelGroup" style="display:none;">' +
                    '<label class="form-label-modern">' +
                    '<i class="fa fa-credit-card" style="margin-right: 8px; color: #9C27B0;"></i>' +
                    '支付方式' +
                    '</label>' +
                    '<select name="pay_channel" class="form-control-modern">' +
                    '<option value="">请选择支付方式</option>' +
                    payChannelHtml +
                    '</select>' +
                    '</div>' +

                    '<div class="form-group-modern">' +
                    '<label class="form-label-modern">' +
                    '<i class="fa fa-comment" style="margin-right: 8px; color: #607D8B;"></i>' +
                    '备注' +
                    '</label>' +
                    '<textarea name="remark" class="form-control-modern" rows="3" placeholder="请输入操作备注"></textarea>' +
                    '</div>' +
                    '</form>' +
                    '</div>';

                layer.open({
                    type: 1,
                    title: '<div style="text-align: center;"><i class="fa fa-cog"></i> 状态操作</div>',
                    area: ['700px', 'auto'],
                    maxHeight: '80%',
                    offset: 'auto',
                    shade: 0.3,
                    shadeClose: false,
                    content: formHtml,
                    btn: ['确定', '取消'],
                    btnAlign: 'c',
                    skin: 'modern-layer-skin',
                    yes: function(index) {
                        submitStatusChange(orderId, index);
                    },
                    success: function(layero, index) {
                        // 添加现代化样式
                        layero.css({
                            'border-radius': '12px',
                            'box-shadow': '0 10px 40px rgba(0,0,0,0.15)',
                            'border': 'none'
                        });

                        // 标题栏样式
                        layero.find('.layui-layer-title').css({
                            'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            'color': 'white',
                            'border-radius': '12px 12px 0 0',
                            'font-weight': '500',
                            'padding': '15px 20px',
                            'border': 'none',
                            'display': 'flex',
                            'align-items': 'center',
                            'justify-content': 'center'
                        });

                        // 确保标题居中
                        layero.find('.layui-layer-title').find('div').css({
                            'width': '100%',
                            'text-align': 'center',
                            'display': 'flex',
                            'align-items': 'center',
                            'justify-content': 'center',
                            'gap': '8px'
                        });

                        // 内容区域样式
                        layero.find('.layui-layer-content').css({
                            'padding': '0',
                            'background': '#f8f9fa',
                            'border-radius': '0 0 12px 12px'
                        });

                        // 按钮样式
                        layero.find('.layui-layer-btn a').css({
                            'border-radius': '8px',
                            'font-weight': '500',
                            'padding': '15px 50px',
                            'margin': '0 20px',
                            'transition': 'all 0.3s ease',
                            'min-width': '140px',
                            'max-width': 'none',
                            'width': 'auto',
                            'height': 'auto',
                            'text-align': 'center',
                            'white-space': 'nowrap',
                            'overflow': 'visible',
                            'display': 'inline-block',
                            'box-sizing': 'border-box',
                            'font-size': '14px',
                            'line-height': '1.4',
                            'text-overflow': 'clip',
                            'word-wrap': 'normal'
                        });

                        layero.find('.layui-layer-btn0').css({
                            'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            'border': 'none',
                            'color': 'white'
                        });

                        layero.find('.layui-layer-btn1').css({
                            'background': '#f8f9fa',
                            'border': '1px solid #dee2e6',
                            'color': '#6c757d'
                        });

                        // 关闭按钮样式
                        layero.find('.layui-layer-close').css({
                            'color': 'white',
                            'font-size': '18px',
                            'font-weight': 'bold'
                        });
                    }
                });
            } else {
                layer.msg(res.info || '获取状态选项失败');
            }
        });
    }

    function toggleAmountInput(select) {
        var selectedOption = $(select).find('option:selected');
        var hasAmount = selectedOption.data('has-amount') == '1';
        var amountField = selectedOption.data('amount-field');

        if (hasAmount) {
            $('#amountGroup').show();
            $('#payChannelGroup').show(); // 显示支付方式选择
            $('#amountLabel').text(getAmountLabel(amountField) + '：');

            // 设置金额提示和限制
            var $amountInput = $('input[name="amount"]');
            var $amountHelp = $('#amountHelp');
            var $amountError = $('#amountError');
            var $payChannelSelect = $('select[name="pay_channel"]');
            var maxAmount = 0;

            // 清除之前的错误提示
            $amountError.hide();

            // 重置支付方式选择
            $payChannelSelect.val('');

            if (currentOrderInfo) {
                switch(amountField) {
                    case 'intent_amount':
                        maxAmount = currentOrderInfo.remaining_amount_yuan;
                        $amountHelp.text('最大可输入金额：¥' + maxAmount.toFixed(2));
                        $amountInput.val(''); // 清空输入框
                        $amountInput.prop('readonly', false); // 移除只读状态
                        $amountInput.css('background-color', ''); // 恢复背景色
                        break;
                    case 'partial_amount':
                        maxAmount = currentOrderInfo.remaining_amount_yuan;
                        $amountHelp.text('最大可输入金额：¥' + maxAmount.toFixed(2));
                        $amountInput.val(''); // 清空输入框
                        $amountInput.prop('readonly', false); // 移除只读状态
                        $amountInput.css('background-color', ''); // 恢复背景色
                        break;
                    case 'fee_amount':
                        maxAmount = currentOrderInfo.remaining_amount_yuan;
                        $amountHelp.text('剩余应付金额：¥' + maxAmount.toFixed(2) + '（全额付款时金额固定）');
                        // 全额付款时自动填入剩余金额并设为只读
                        $amountInput.val(maxAmount.toFixed(2));
                        $amountInput.prop('readonly', true);
                        $amountInput.css('background-color', '#f5f5f5');
                        break;
                    default:
                        maxAmount = currentOrderInfo.remaining_amount_yuan;
                        $amountHelp.text('最大可输入金额：¥' + maxAmount.toFixed(2));
                        $amountInput.val(''); // 清空输入框
                        $amountInput.prop('readonly', false); // 移除只读状态
                        $amountInput.css('background-color', ''); // 恢复背景色
                }

                $amountInput.attr('max', maxAmount);
                $amountInput.data('max-amount', maxAmount);
            }
        } else {
            $('#amountGroup').hide();
            $('#payChannelGroup').hide(); // 隐藏支付方式选择
        }
    }

    function getAmountLabel(field) {
        switch(field) {
            case 'intent_amount': return '意向金金额';
            case 'partial_amount': return '部分付款金额';
            case 'fee_amount': return '全额付款金额';
            default: return '金额';
        }
    }

    // 防止方向键上下调整金额
    function preventArrowKeys(event) {
        // 阻止上箭头键（keyCode 38）和下箭头键（keyCode 40）
        if (event.keyCode === 38 || event.keyCode === 40) {
            event.preventDefault();
            return false;
        }
    }

    // 防止鼠标滚轮调整金额
    function preventWheel(event) {
        event.preventDefault();
        return false;
    }

    // 金额验证函数
    function validateAmount(input) {
        var $input = $(input);
        var $error = $('#amountError');
        var amount = parseFloat($input.val());
        var maxAmount = parseFloat($input.data('max-amount'));

        $error.hide();

        if (isNaN(amount) || amount <= 0) {
            return true; // 空值或0不显示错误，由必填验证处理
        }

        if (amount > maxAmount) {
            $error.text('付款金额不能超过剩余应付金额 ¥' + maxAmount.toFixed(2)).show();
            return false;
        }

        return true;
    }

    function submitStatusChange(orderId, layerIndex) {
        var form = $('#statusForm');
        var status = form.find('[name="status"]').val();
        var amount = form.find('[name="amount"]').val();
        var payChannel = form.find('[name="pay_channel"]').val();
        var remark = form.find('[name="remark"]').val();

        if (!status) {
            layer.msg('请选择状态');
            return;
        }

        var selectedOption = form.find('[name="status"] option:selected');
        var hasAmount = selectedOption.data('has-amount') == '1';
        var amountField = selectedOption.data('amount-field');

        if (hasAmount && !amount) {
            layer.msg('请输入金额');
            return;
        }

        if (hasAmount && !payChannel) {
            layer.msg('请选择支付方式');
            return;
        }

        // 验证金额
        if (hasAmount && amount) {
            var $amountInput = form.find('[name="amount"]');
            if (!validateAmount($amountInput[0])) {
                layer.msg('付款金额超出限制，请检查输入');
                return;
            }

            var amountValue = parseFloat(amount);
            var maxAmount = parseFloat($amountInput.data('max-amount'));

            if (amountValue > maxAmount) {
                layer.msg('付款金额不能超过剩余应付金额 ¥' + maxAmount.toFixed(2));
                return;
            }
        }

        var postData = {
            order_id: orderId,
            status: status,
            remark: remark,
            amount_info: {},
            pay_channel: payChannel
        };

        if (hasAmount && amount) {
            postData.amount_info[amountField] = parseFloat(amount);
        }

        $.post('{:U("training/updateStatus")}', postData, function(res) {
            if (res.status == 1) {
                layer.close(layerIndex);
                layer.msg('状态更新成功', {icon: 1, time: 650}, function() {
                    location.reload();
                });
            } else if (res.status === 'payment_required') {
                // 需要付款确认
                layer.close(layerIndex);
                showPaymentConfirmModal(orderId, res.payment_info, res.target_status, res.remark);
            } else if (res.status === 'fund_operation_confirm') {
                // 需要资金操作确认
                layer.close(layerIndex);
                showFundOperationConfirmModal(orderId, res.fund_info, res.target_status, res.remark);
            } else {
                layer.msg(res.info || '状态更新失败');
            }
        });
    }

    // 取消订单功能
    function cancelOrder(orderId, currentStatus) {
        var confirmText = '确定要取消该订单吗？';

        // 根据当前状态给出不同的提示
        if (currentStatus == 'full_paid' || currentStatus.indexOf('training') !== -1 || currentStatus.indexOf('employment') !== -1) {
            confirmText = '该订单已付款，取消后将进行退款处理。确定要取消吗？';
        }

        layer.confirm(confirmText, {
            btn: ['确定取消', '我再想想'],
            title: '<i class="fa fa-exclamation-triangle"></i> 取消订单确认',
            skin: 'modern-layer-skin',
            area: ['500px', '280px'],
            shade: 0.5,
            success: function(layero, index) {
                // 确保弹窗居中显示
                var windowWidth = $(window).width();
                var windowHeight = $(window).height();
                var layerWidth = layero.outerWidth();
                var layerHeight = layero.outerHeight();

                // 计算居中位置
                var left = (windowWidth - layerWidth) / 2;
                var top = (windowHeight - layerHeight) / 2;

                // 添加现代化样式
                layero.css({
                    'border-radius': '16px',
                    'box-shadow': '0 20px 60px rgba(0,0,0,0.2)',
                    'border': 'none',
                    'position': 'fixed',
                    'left': left + 'px',
                    'top': top + 'px',
                    'transform': 'none'
                });

                // 标题栏样式 - 使用警告色
                layero.find('.layui-layer-title').css({
                    'background': 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
                    'color': 'white',
                    'border-radius': '16px 16px 0 0',
                    'font-weight': '600',
                    'padding': '20px 25px',
                    'border': 'none',
                    'text-align': 'center',
                    'font-size': '18px',
                    'box-shadow': '0 2px 10px rgba(251,191,36,0.3)'
                });

                // 内容区域样式
                layero.find('.layui-layer-content').css({
                    'padding': '40px 30px',
                    'background': 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                    'font-size': '16px',
                    'line-height': '1.6',
                    'color': '#374151',
                    'text-align': 'center',
                    'min-height': '80px'
                });

                // 按钮区域样式
                layero.find('.layui-layer-btn').css({
                    'padding': '30px',
                    'text-align': 'center',
                    'background': 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
                    'border-radius': '0 0 16px 16px',
                    'border-top': '1px solid #e9ecef'
                });

                // 按钮样式
                layero.find('.layui-layer-btn a').css({
                    'border-radius': '8px',
                    'font-weight': '500',
                    'padding': '12px 30px',
                    'margin': '0 15px',
                    'transition': 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    'min-width': '120px',
                    'font-size': '15px',
                    'line-height': '1.4',
                    'text-align': 'center',
                    'display': 'inline-block',
                    'box-sizing': 'border-box',
                    'cursor': 'pointer'
                });

                // 确定取消按钮样式
                layero.find('.layui-layer-btn0').css({
                    'background': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
                    'border': 'none',
                    'color': 'white',
                    'box-shadow': '0 4px 15px rgba(255,107,107,0.4)'
                });

                // 取消按钮样式
                layero.find('.layui-layer-btn1').css({
                    'background': 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                    'border': '1px solid #dee2e6',
                    'color': '#6c757d',
                    'box-shadow': '0 2px 8px rgba(0,0,0,0.1)'
                });
            }
        }, function(index) {
            // 使用现代化弹窗输入取消原因
            var formHtml = '<div class="status-modal-container" style="padding: 30px;">' +
                '<div class="form-group-modern" style="margin-bottom: 0;">' +
                '<label class="form-label-modern" style="margin-bottom: 15px; font-size: 16px;">' +
                '<i class="fa fa-exclamation-triangle" style="margin-right: 10px; color: #f56565;"></i>' +
                '请输入取消原因' +
                '</label>' +
                '<textarea id="cancelReason" class="form-control-modern" rows="6" placeholder="请详细说明取消原因" style="resize: vertical; min-height: 120px; font-size: 14px; line-height: 1.5;"></textarea>' +
                '</div>' +
                '</div>';

            layer.open({
                type: 1,
                title: '<div style="text-align: center;"><i class="fa fa-ban"></i> 取消报名</div>',
                area: ['500px', '400px'],
                offset: 'auto',
                shade: 0.5,
                shadeClose: false,
                content: formHtml,
                btn: ['确定取消', '我再想想'],
                btnAlign: 'c',
                skin: 'modern-layer-skin',
                resize: false,
                success: function(layero, index) {
                    // 确保弹窗居中显示
                    var windowWidth = $(window).width();
                    var windowHeight = $(window).height();
                    var layerWidth = layero.outerWidth();
                    var layerHeight = layero.outerHeight();

                    // 计算居中位置
                    var left = (windowWidth - layerWidth) / 2;
                    var top = (windowHeight - layerHeight) / 2;

                    // 添加现代化样式
                    layero.css({
                        'border-radius': '16px',
                        'box-shadow': '0 20px 60px rgba(0,0,0,0.2)',
                        'border': 'none',
                        'position': 'fixed',
                        'left': left + 'px',
                        'top': top + 'px',
                        'transform': 'none'
                    });

                    // 标题栏样式 - 使用红色渐变表示危险操作
                    layero.find('.layui-layer-title').css({
                        'background': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
                        'color': 'white',
                        'border-radius': '16px 16px 0 0',
                        'font-weight': '600',
                        'padding': '20px 25px',
                        'border': 'none',
                        'text-align': 'center',
                        'font-size': '18px',
                        'box-shadow': '0 2px 10px rgba(255,107,107,0.3)'
                    });

                    // 确保标题居中
                    layero.find('.layui-layer-title').find('div').css({
                        'width': '100%',
                        'text-align': 'center',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center',
                        'gap': '10px'
                    });

                    // 内容区域样式
                    layero.find('.layui-layer-content').css({
                        'padding': '0',
                        'background': 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
                        'border-radius': '0'
                    });

                    // 按钮区域样式
                    layero.find('.layui-layer-btn').css({
                        'padding': '25px 30px',
                        'text-align': 'center',
                        'background': 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
                        'border-radius': '0 0 16px 16px',
                        'border-top': '1px solid #e9ecef'
                    });

                    // 按钮样式
                    layero.find('.layui-layer-btn a').css({
                        'border-radius': '12px',
                        'font-weight': '600',
                        'padding': '14px 28px',
                        'margin': '0 10px',
                        'transition': 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        'min-width': '120px',
                        'font-size': '15px',
                        'line-height': '1.4',
                        'text-align': 'center',
                        'display': 'inline-block',
                        'box-sizing': 'border-box',
                        'cursor': 'pointer',
                        'position': 'relative',
                        'overflow': 'hidden'
                    });

                    // 确定取消按钮 - 红色危险样式
                    layero.find('.layui-layer-btn0').css({
                        'background': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
                        'border': 'none',
                        'color': 'white',
                        'box-shadow': '0 4px 15px rgba(255,107,107,0.4)'
                    });

                    // 取消按钮样式
                    layero.find('.layui-layer-btn1').css({
                        'background': 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                        'border': '1px solid #dee2e6',
                        'color': '#6c757d',
                        'box-shadow': '0 2px 8px rgba(0,0,0,0.1)'
                    });

                    // 关闭按钮样式
                    layero.find('.layui-layer-close').css({
                        'color': 'white',
                        'font-size': '18px',
                        'font-weight': 'bold'
                    });

                    // 聚焦到文本框
                    setTimeout(function() {
                        $('#cancelReason').focus();
                    }, 100);
                },
                yes: function(promptIndex) {
                    var reason = $('#cancelReason').val();
                    if (!reason || reason.trim() === '') {
                        layer.msg('请输入取消原因');
                        return;
                    }

                    // 调用取消接口
                    $.post('{:U("training/cancelOrder")}', {
                        order_id: orderId,
                        reason: reason.trim()
                    }, function(res) {
                        if (res.status == 1) {
                            layer.close(promptIndex);
                            layer.close(index);
                            layer.msg('订单取消成功', {icon: 1, time: 650}, function() {
                                location.reload();
                            });
                        } else {
                            layer.msg(res.info || '取消失败');
                        }
                    });
                }
            });
        });
    }

// 显示付款确认弹窗（与详情页面相同的函数）
function showPaymentConfirmModal(orderId, paymentInfo, targetStatus, remark) {
    // 先获取支付历史记录
    $.get('{:U("training/getPaymentHistory")}', {order_id: orderId}, function(res) {
        var orderInfo = paymentInfo.order_info;
        var remainingAmount = paymentInfo.remaining;
        var remainingAmountYuan = (remainingAmount / 100).toFixed(2);
        var feeAmountYuan = (paymentInfo.fee_amount / 100).toFixed(2);
        var paidAmountYuan = (paymentInfo.paid_amount / 100).toFixed(2);
        var payments = res.status == 1 ? (res.data || []) : [];

        // 构建支付记录HTML
        var paymentHistoryHtml = '';
        if (payments.length > 0) {
            paymentHistoryHtml = `
                <div style="margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <i class="fa fa-history" style="color: #667eea; margin-right: 8px; font-size: 18px;"></i>
                        <h4 style="margin: 0; color: #374151; font-weight: 600;">支付历史记录</h4>
                    </div>
                    <div style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); max-height: 200px; overflow-y: auto;">
                        <table style="width: 100%; border-collapse: collapse; margin: 0; font-size: 13px;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                    <th style="padding: 10px 8px; text-align: left; font-weight: 600; border: none;">时间</th>
                                    <th style="padding: 10px 8px; text-align: left; font-weight: 600; border: none;">金额</th>
                                    <th style="padding: 10px 8px; text-align: left; font-weight: 600; border: none;">方式</th>
                                    <th style="padding: 10px 8px; text-align: left; font-weight: 600; border: none;">类型</th>
                                </tr>
                            </thead>
                            <tbody>`;

            payments.forEach(function(payment, index) {
                var payTypeText = payment.pay_type_text || payment.pay_type;
                var payChannelText = payment.pay_channel_text || payment.pay_channel;
                var amountYuan = (payment.pay_amount / 100).toFixed(2);
                var rowBg = index % 2 === 0 ? '#f8f9fa' : 'white';

                var amountColor = '#4CAF50';
                if (payment.pay_type === 'refund') {
                    amountColor = '#f44336';
                }

                var typeColor = '#2196F3';
                if (payment.pay_type === 'intent') typeColor = '#FF9800';
                else if (payment.pay_type === 'partial') typeColor = '#9C27B0';
                else if (payment.pay_type === 'full') typeColor = '#4CAF50';
                else if (payment.pay_type === 'refund') typeColor = '#f44336';

                paymentHistoryHtml += `
                    <tr style="background: ${rowBg};">
                        <td style="padding: 8px; border: none; color: #374151;">${payment.pay_time_text}</td>
                        <td style="padding: 8px; border: none; color: ${amountColor}; font-weight: 600;">¥${amountYuan}</td>
                        <td style="padding: 8px; border: none; color: #374151;">${payChannelText}</td>
                        <td style="padding: 8px; border: none;">
                            <span style="background: ${typeColor}; color: white; padding: 2px 6px; border-radius: 8px; font-size: 11px; font-weight: 500;">${payTypeText}</span>
                        </td>
                    </tr>`;
            });

            paymentHistoryHtml += `
                            </tbody>
                        </table>
                    </div>
                </div>`;
        } else {
            paymentHistoryHtml = `
                <div style="margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <i class="fa fa-history" style="color: #667eea; margin-right: 8px; font-size: 18px;"></i>
                        <h4 style="margin: 0; color: #374151; font-weight: 600;">支付历史记录</h4>
                    </div>
                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; border: 2px dashed #e5e7eb;">
                        <i class="fa fa-credit-card" style="font-size: 32px; color: #d1d5db; margin-bottom: 10px;"></i>
                        <p style="color: #6b7280; margin: 0; font-size: 13px;">暂无支付记录</p>
                    </div>
                </div>`;
        }

        var content = `
            <div class="status-modal-container">
                <div style="text-align: center; margin-bottom: 25px;">
                    <i class="fa fa-exclamation-triangle" style="font-size: 48px; color: #f39c12; margin-bottom: 15px;"></i>
                    <h3 style="color: #e74c3c; margin: 0; font-weight: 600;">付款确认提醒</h3>
                </div>

                <div class="status-modal-fee-info">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <i class="fa fa-info-circle" style="color: #2196F3; margin-right: 8px; font-size: 18px;"></i>
                        <h4 style="margin: 0; color: #1976D2; font-weight: 600;">该培训订单还需支付款项</h4>
                    </div>
                    <div class="fee-details-grid">
                        <div class="fee-item">
                            <span class="fee-label">培训项目</span>
                            <span class="fee-value" style="color: #374151;">${orderInfo.project_name || '未知项目'}</span>
                        </div>
                        <div class="fee-item">
                            <span class="fee-label">学员姓名</span>
                            <span class="fee-value" style="color: #374151;">${orderInfo.user_name || '未知学员'}</span>
                        </div>
                        <div class="fee-item">
                            <span class="fee-label">报名费总额</span>
                            <span class="fee-value">¥${feeAmountYuan}</span>
                        </div>
                        <div class="fee-item">
                            <span class="fee-label">已支付金额</span>
                            <span class="fee-value fee-paid">¥${paidAmountYuan}</span>
                        </div>
                        <div class="fee-item" style="grid-column: 1 / -1; background: rgba(255, 152, 0, 0.1); border: 2px solid #FF9800;">
                            <span class="fee-label">剩余应付</span>
                            <span class="fee-value fee-remaining" style="font-size: 20px; font-weight: 700;">¥${remainingAmountYuan}</span>
                        </div>
                    </div>
                </div>

                ${paymentHistoryHtml}

                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin-bottom: 20px;">
                    <p style="margin: 0; color: #856404; font-size: 14px;">
                        <i class="fa fa-info-circle" style="margin-right: 8px;"></i>
                        请确认是否已收到学员付款？确认后将自动创建支付记录并执行状态跳转。
                    </p>
                </div>

                <div class="form-group-modern">
                    <label class="form-label-modern">
                        <i class="fa fa-credit-card" style="margin-right: 8px; color: #667eea;"></i>
                        收款方式
                    </label>
                    <select id="confirmPayChannel" class="form-control-modern">
                        <option value="">请选择收款方式</option>
                        <option value="offline">线下支付</option>
                        <option value="alipay">支付宝</option>
                        <option value="wechat">微信支付</option>
                        <option value="bank">银行转账</option>
                    </select>
                    <span class="form-help-text">选择学员实际使用的付款方式</span>
                </div>
            </div>
        `;

        layer.open({
            type: 1,
            title: '<i class="fa fa-credit-card" style="margin-right: 8px;"></i>付款确认',
            content: content,
            area: ['600px', 'auto'],
            skin: 'modern-layer-skin',
            btn: ['确认已收款', '取消操作'],
            btn1: function(index) {
                var payChannel = $('#confirmPayChannel').val();
                if (!payChannel) {
                    layer.msg('请选择收款方式', {icon: 2});
                    return false;
                }
                confirmPayment(orderId, remainingAmount, targetStatus, remark, payChannel);
                layer.close(index);
            },
            btn2: function(index) {
                layer.close(index);
            },
            success: function(layero, index) {
                // 应用现代化样式
                layero.css({
                    'border-radius': '12px',
                    'overflow': 'hidden',
                    'box-shadow': '0 10px 25px rgba(0,0,0,0.15)'
                });
            }
        });
    }).fail(function() {
        layer.msg('获取支付记录失败，但仍可进行付款确认', {icon: 2});
        // 如果获取支付记录失败，显示简化版本
        showSimplePaymentConfirmModal(orderId, paymentInfo, targetStatus, remark);
    });
}

// 显示资金操作确认弹窗（与详情页面相同的函数）
function showFundOperationConfirmModal(orderId, fundInfo, targetStatus, remark) {
    var orderInfo = fundInfo.order_info;
    var fundAmountYuan = (fundInfo.amount / 100).toFixed(2);
    var operationType = fundInfo.operation_type;
    var description = fundInfo.description;

    var operationText = operationType === 'freeze' ? '冻结' : (operationType === 'refund' ? '退款' : '解冻');
    var operationColor = operationType === 'freeze' ? '#e74c3c' : (operationType === 'refund' ? '#f39c12' : '#27ae60');
    var operationIcon = operationType === 'freeze' ? 'fa-lock' : (operationType === 'refund' ? 'fa-undo' : 'fa-unlock');

    var content = `
        <div class="fund-operation-modal" style="padding: 20px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <i class="fa ${operationIcon}" style="font-size: 48px; color: ${operationColor}; margin-bottom: 10px;"></i>
                <h3 style="color: ${operationColor}; margin: 0;">资金操作确认</h3>
            </div>

            <div class="fund-info" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #2c3e50;">本次操作将${operationType === 'refund' ? '扣除冻结资金' : operationText + '服务站资金'}：</h4>
                <div style="margin: 10px 0;">
                    <div style="display: flex; justify-content: space-between; margin: 8px 0; border-top: 1px solid #dee2e6; padding-top: 8px;">
                        <span>${operationType === 'refund' ? '扣除' : operationText}金额：</span>
                        <strong style="color: ${operationColor}; font-size: 18px;">${fundAmountYuan}元</strong>
                    </div>
                </div>
            </div>

            <div style="background: ${operationType === 'freeze' ? '#f8d7da' : (operationType === 'refund' ? '#fff3cd' : '#d4edda')}; padding: 15px; border-radius: 8px; border-left: 4px solid ${operationColor};">
                <p style="margin: 0; color: ${operationType === 'freeze' ? '#721c24' : (operationType === 'refund' ? '#856404' : '#155724')};">
                    <i class="fa fa-info-circle" style="margin-right: 8px;"></i>
                    ${description}${operationType === 'unfreeze' ? '，资金将转为可提现状态' : (operationType === 'refund' ? '，冻结资金将被扣除' : '')}
                </p>
            </div>
        </div>
    `;

    layer.open({
        type: 1,
        title: '资金操作确认',
        content: content,
        area: ['450px', 'auto'],
        btn: ['确认执行', '取消操作'],
        btn1: function(index) {
            confirmFundOperation(orderId, targetStatus, remark);
            layer.close(index);
        },
        btn2: function(index) {
            layer.close(index);
        }
    });
}

// 确认收款（与详情页面相同的函数）
function confirmPayment(orderId, remainingAmount, targetStatus, remark, payChannel) {
    var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

    $.post('{:U("training/confirmRemainingPayment")}', {
        order_id: orderId,
        remaining_amount: remainingAmount,
        target_status: targetStatus,
        remark: remark,
        pay_channel: payChannel
    }, function(res) {
        layer.close(loadingIndex);
        if (res.status == 1) {
            layer.msg('确认收款成功', {icon: 1, time: 1000}, function() {
                location.reload();
            });
        } else {
            layer.msg(res.info || '确认收款失败');
        }
    }).fail(function() {
        layer.close(loadingIndex);
        layer.msg('网络请求失败');
    });
}


// 确认资金操作（与详情页面相同的函数）
function confirmFundOperation(orderId, targetStatus, remark) {
    var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

    $.post('{:U("training/confirmFundOperation")}', {
        order_id: orderId,
        target_status: targetStatus,
        remark: remark
    }, function(res) {
        layer.close(loadingIndex);
        if (res.status == 1) {
            layer.msg('操作成功', {icon: 1, time: 1000}, function() {
                location.reload();
            });
        } else {
            layer.msg(res.info || '操作失败');
        }
    }).fail(function() {
        layer.close(loadingIndex);
        layer.msg('网络请求失败');
    });
}

// 显示培训订单统计报表
function showTrainingStats() {
    layer.open({
        type: 1,
        title: '<i class="fa fa-bar-chart" style="margin-right: 8px; color: #10b981;"></i>培训订单统计报表',
        area: ['50%', '90%'], // 调整宽度为80%，高度保持90%
        maxmin: true, // 允许最大化和最小化
        content: '<div style="padding: 20px; text-align: center;"><i class="fa fa-spinner fa-spin" style="font-size: 2rem; color: #10b981;"></i><br><br>统计数据加载中...</div>',
        skin: 'modern-layer-skin',
        success: function(layero, index) {
            // 应用现代化样式
            layero.css({
                'border-radius': '12px',
                'overflow': 'hidden',
                'box-shadow': '0 10px 25px rgba(0,0,0,0.15)'
            });

            // 加载统计数据
            loadTrainingStats(layero);
        }
    });
}

// 加载培训订单统计数据
function loadTrainingStats(layero) {
    $.ajax({
        url: '{:U("Training/getStats")}',
        type: 'GET',
        dataType: 'json',
        success: function(res) {
            if (res.status == 1) {
                var data = res.data;
                var statsHtml = generateStatsHtml(data);
                layero.find('.layui-layer-content').html(statsHtml);
            } else {
                layero.find('.layui-layer-content').html(
                    '<div style="padding: 40px; text-align: center; color: #ef4444;">' +
                    '<i class="fa fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i><br>' +
                    '<h3>获取统计数据失败</h3>' +
                    '<p>' + (res.info || '请稍后重试') + '</p>' +
                    '</div>'
                );
            }
        },
        error: function() {
            layero.find('.layui-layer-content').html(
                '<div style="padding: 40px; text-align: center; color: #ef4444;">' +
                '<i class="fa fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i><br>' +
                '<h3>网络请求失败</h3>' +
                '<p>请检查网络连接后重试</p>' +
                '</div>'
            );
        }
    });
}

// 生成统计报表HTML
function generateStatsHtml(data) {
    var normalOrderPercent = data.total_orders > 0 ? ((data.normal_orders / data.total_orders) * 100).toFixed(1) : 0;
    var zsbOrderPercent = data.total_orders > 0 ? ((data.zsb_orders / data.total_orders) * 100).toFixed(1) : 0;
    var completedPercent = data.total_orders > 0 ? ((data.completed_orders / data.total_orders) * 100).toFixed(1) : 0;
    var incompletePercent = data.total_orders > 0 ? ((data.incomplete_orders / data.total_orders) * 100).toFixed(1) : 0;

    var html = `
        <div style="padding: 20px; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); min-height: 100%; overflow-y: auto;">
            <!-- 筛选器 -->
            <div style="background: white; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <span style="color: #6b7280; font-weight: 500; white-space: nowrap;">筛选岗位:</span>
                    <div style="position: relative; flex: 1; max-width: 600px;">
                        <input type="text" id="postFilterInput" placeholder="全部岗位" readonly
                               style="width: 100%; padding: 8px 40px 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white; cursor: pointer; font-size: 14px;"
                               onclick="togglePostDropdown()">
                        <i class="fa fa-chevron-down" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #6b7280; pointer-events: none;"></i>

                        <!-- 下拉选项 -->
                        <div id="postDropdown" style="display: none; position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #d1d5db; border-top: none; border-radius: 0 0 6px 6px; max-height: 300px; overflow-y: auto; z-index: 1000; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                            <div class="post-option" data-value="" style="padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f3f4f6;" onmouseover="this.style.background='#f9fafb'" onmouseout="this.style.background='white'" onclick="selectPost('', '全部岗位')">
                                全部岗位
                            </div>
                            ${generatePostDropdownOptions(data.post_list, data.current_post_id)}
                        </div>
                    </div>
                    <button onclick="applyPostFilter()" style="padding: 8px 16px; background: #10b981; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 500; white-space: nowrap;">
                        筛选
                    </button>
                </div>
            </div>

            <!-- 核心指标卡片 -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1.5rem; border-radius: 12px; text-align: center; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
                    <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">${data.total_orders}</div>
                    <div style="font-size: 1.1rem; opacity: 0.9;">有效订单总数</div>
                    <div style="font-size: 0.9rem; opacity: 0.7; margin-top: 0.5rem;">（排除取消）</div>
                </div>
                <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 1.5rem; border-radius: 12px; text-align: center; box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);">
                    <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">${data.completed_orders}</div>
                    <div style="font-size: 1.1rem; opacity: 0.9;">已完成订单</div>
                    <div style="font-size: 0.9rem; opacity: 0.7; margin-top: 0.5rem;">${completedPercent}%</div>
                </div>
                <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 1.5rem; border-radius: 12px; text-align: center; box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);">
                    <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">${data.incomplete_orders}</div>
                    <div style="font-size: 1.1rem; opacity: 0.9;">进行中订单</div>
                    <div style="font-size: 0.9rem; opacity: 0.7; margin-top: 0.5rem;">${incompletePercent}%</div>
                </div>
                <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 1.5rem; border-radius: 12px; text-align: center; box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);">
                    <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">${data.cancelled_orders}</div>
                    <div style="font-size: 1.1rem; opacity: 0.9;">取消订单</div>
                    <div style="font-size: 0.9rem; opacity: 0.7; margin-top: 0.5rem;">（不计入统计）</div>
                </div>
            </div>

            <!-- 收入统计对比 -->
            <div style="background: white; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h3 style="margin: 0 0 1.5rem 0; color: #374151; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fa fa-money" style="color: #10b981;"></i>
                    收入统计对比
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                    <!-- 报名费统计 -->
                    <div style="background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); padding: 1.5rem; border-radius: 8px; border-left: 4px solid #3b82f6;">
                        <h4 style="margin: 0 0 1rem 0; color: #3b82f6; font-size: 1.1rem;">报名费统计</h4>
                        <div style="margin-bottom: 0.8rem;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                                <span style="color: #6b7280;">总计：</span>
                                <span style="font-weight: bold; color: #3b82f6;">¥${data.total_fee.toLocaleString()}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                                <span style="color: #6b7280;">已完成：</span>
                                <span style="font-weight: bold; color: #10b981;">¥${data.completed_fee.toLocaleString()}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem;">
                                <span style="color: #6b7280;">进行中：</span>
                                <span style="font-weight: bold; color: #f59e0b;">¥${data.incomplete_fee.toLocaleString()}</span>
                            </div>
                            <div style="border-top: 1px solid #cbd5e1; padding-top: 0.5rem; margin-top: 0.5rem;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                                    <span style="color: #374151; font-weight: 600;">已收款：</span>
                                    <span style="font-weight: bold; color: #10b981;">¥${data.paid_amount.toLocaleString()}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span style="color: #374151; font-weight: 600;">待收款：</span>
                                    <span style="font-weight: bold; color: #ef4444;">¥${data.pending_amount.toLocaleString()}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 平台收入统计 -->
                    <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 1.5rem; border-radius: 8px; border-left: 4px solid #10b981;">
                        <h4 style="margin: 0 0 1rem 0; color: #10b981; font-size: 1.1rem;">平台收入统计</h4>
                        <div style="margin-bottom: 0.8rem;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                                <span style="color: #6b7280;">预计总收入（按报名培训金额）：</span>
                                <span style="font-weight: bold; color: #10b981;">¥${data.expected_total_commission.toLocaleString()}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                                <span style="color: #6b7280;">实收收入（全付金额订单）：</span>
                                <span style="font-weight: bold; color: #10b981;">¥${data.actual_commission.toLocaleString()}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem;">
                                <span style="color: #6b7280;">待收统计（部分付款订单平台收入）：</span>
                                <span style="font-weight: bold; color: #f59e0b;">¥${data.pending_commission.toLocaleString()}</span>
                            </div>

                            <!-- 订单状态收入统计 -->
                            <div style="border-top: 1px solid #d1fae5; padding-top: 0.8rem;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                                    <span style="color: #6b7280;">已完成：</span>
                                    <span style="font-weight: bold; color: #10b981;">¥${data.completed_commission.toLocaleString()}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span style="color: #6b7280;">进行中：</span>
                                    <span style="font-weight: bold; color: #3b82f6;">¥${data.ongoing_commission.toLocaleString()}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 服务站收益统计 -->
                    <div style="background: linear-gradient(135deg, #fef3e2 0%, #fed7aa 100%); padding: 1.5rem; border-radius: 8px; border-left: 4px solid #f59e0b;">
                        <h4 style="margin: 0 0 1rem 0; color: #f59e0b; font-size: 1.1rem;">服务站收益统计</h4>
                        <div style="margin-bottom: 0.8rem;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                                <span style="color: #6b7280;">总计：</span>
                                <span style="font-weight: bold; color: #f59e0b;">¥${data.total_station_profit.toLocaleString()}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                                <span style="color: #6b7280;">已发放：</span>
                                <span style="font-weight: bold; color: #10b981;">¥${data.completed_station_profit.toLocaleString()}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span style="color: #6b7280;">待发放：</span>
                                <span style="font-weight: bold; color: #f59e0b;">¥${data.incomplete_station_profit.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单类型分布 -->
            <div style="background: white; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h3 style="margin: 0 0 1.5rem 0; color: #374151; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fa fa-pie-chart" style="color: #10b981;"></i>
                    订单类型分布
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                    <div style="background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); padding: 1.5rem; border-radius: 8px; border-left: 4px solid #3b82f6;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-size: 1.1rem; color: #6b7280; margin-bottom: 0.5rem;">普通订单</div>
                                <div style="font-size: 2rem; font-weight: bold; color: #3b82f6;">${data.normal_orders}</div>
                            </div>
                            <div style="font-size: 1.5rem; color: #3b82f6; font-weight: bold;">${normalOrderPercent}%</div>
                        </div>
                    </div>
                    <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 1.5rem; border-radius: 8px; border-left: 4px solid #10b981;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-size: 1.1rem; color: #6b7280; margin-bottom: 0.5rem;">招就办订单</div>
                                <div style="font-size: 2rem; font-weight: bold; color: #10b981;">${data.zsb_orders}</div>
                            </div>
                            <div style="font-size: 1.5rem; color: #10b981; font-weight: bold;">${zsbOrderPercent}%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单状态统计 -->
            <div style="background: white; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h3 style="margin: 0 0 1.5rem 0; color: #374151; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fa fa-tasks" style="color: #f59e0b;"></i>
                    订单状态分布
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">`;

    // 添加状态统计
    data.status_stats.forEach(function(stat) {
        var statusColor = getStatusColor(stat.style);
        html += `
                    <div style="background: ${statusColor.bg}; padding: 1rem; border-radius: 8px; text-align: center; border: 1px solid ${statusColor.border};">
                        <div style="font-size: 1.8rem; font-weight: bold; color: ${statusColor.text}; margin-bottom: 0.5rem;">${stat.count}</div>
                        <div style="font-size: 0.9rem; color: ${statusColor.text};">${stat.name}</div>
                    </div>`;
    });

    html += `
                </div>
            </div>

            <!-- 其他统计信息 -->
            <div style="background: white; border-radius: 12px; padding: 1.5rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h3 style="margin: 0 0 1.5rem 0; color: #374151; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fa fa-info-circle" style="color: #6366f1;"></i>
                    其他信息
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="background: #f8fafc; padding: 1rem; border-radius: 8px; border-left: 4px solid #6366f1;">
                        <div style="font-size: 0.9rem; color: #6b7280; margin-bottom: 0.5rem;">最近30天新增</div>
                        <div style="font-size: 1.5rem; font-weight: bold; color: #6366f1;">${data.recent_orders} 个订单</div>
                    </div>
                    <div style="background: #f8fafc; padding: 1rem; border-radius: 8px; border-left: 4px solid #64748b;">
                        <div style="font-size: 0.9rem; color: #6b7280; margin-bottom: 0.5rem;">数据更新时间</div>
                        <div style="font-size: 1.1rem; font-weight: bold; color: #64748b;">${data.update_time}</div>
                    </div>
                </div>
            </div>

            <!-- 说明信息 -->
            <div style="margin-top: 1.5rem; padding: 1rem; background: #fef3c7; border-radius: 8px; border-left: 4px solid #f59e0b;">
                <p style="margin: 0; color: #92400e; font-size: 0.9rem;">
                    <i class="fa fa-info-circle" style="margin-right: 0.5rem;"></i>
                    统计说明：已排除取消报名的订单和指定测试服务站的数据。已完成订单指服务状态为"服务完成"的订单，进行中订单指其他有效状态的订单。
                </p>
            </div>
        </div>
    `;

    return html;
}

// 获取状态对应的颜色
function getStatusColor(style) {
    switch(style) {
        case 'info':
            return {
                bg: 'rgba(59, 130, 246, 0.1)',
                border: 'rgba(59, 130, 246, 0.3)',
                text: '#1d4ed8'
            };
        case 'warning':
            return {
                bg: 'rgba(245, 158, 11, 0.1)',
                border: 'rgba(245, 158, 11, 0.3)',
                text: '#d97706'
            };
        case 'success':
            return {
                bg: 'rgba(16, 185, 129, 0.1)',
                border: 'rgba(16, 185, 129, 0.3)',
                text: '#059669'
            };
        case 'danger':
            return {
                bg: 'rgba(239, 68, 68, 0.1)',
                border: 'rgba(239, 68, 68, 0.3)',
                text: '#dc2626'
            };
        default:
            return {
                bg: 'rgba(107, 114, 128, 0.1)',
                border: 'rgba(107, 114, 128, 0.3)',
                text: '#374151'
            };
    }
}

// 生成岗位下拉选项HTML
function generatePostDropdownOptions(postList, currentPostId) {
    var html = '';

    for (var projectName in postList) {
        var posts = postList[projectName];
        for (var i = 0; i < posts.length; i++) {
            var post = posts[i];
            var isSelected = (currentPostId == post.id);
            var selectedStyle = isSelected ? 'background: #3b82f6; color: white;' : '';

            // 格式：[项目名称] 岗位名称 (订单数量个订单)
            var displayText = `[${projectName}] ${post.name} (${post.order_count}个订单)`;
            var displayName = `[${projectName}] ${post.name}`;

            html += `<div class="post-option" data-value="${post.id}" data-display-name="${displayName}" style="padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f3f4f6; ${selectedStyle}"
                          onmouseover="if(!this.dataset.selected) this.style.background='#f9fafb'"
                          onmouseout="if(!this.dataset.selected) this.style.background='white'"
                          onclick="selectPost('${post.id}', '${displayName}')"
                          ${isSelected ? 'data-selected="true"' : ''}>
                        ${displayText}
                     </div>`;
        }
    }

    return html;
}

// 切换下拉菜单显示/隐藏
function togglePostDropdown() {
    var dropdown = document.getElementById('postDropdown');
    var isVisible = dropdown.style.display !== 'none';

    if (isVisible) {
        dropdown.style.display = 'none';
    } else {
        dropdown.style.display = 'block';
        // 点击其他地方关闭下拉菜单
        setTimeout(function() {
            document.addEventListener('click', closeDropdownOnClickOutside);
        }, 0);
    }
}

// 选择岗位
function selectPost(postId, postName) {
    document.getElementById('postFilterInput').value = postName;
    document.getElementById('postFilterInput').dataset.postId = postId;
    document.getElementById('postDropdown').style.display = 'none';

    // 移除点击外部关闭的监听器
    document.removeEventListener('click', closeDropdownOnClickOutside);

    // 更新选中状态
    var options = document.querySelectorAll('.post-option');
    options.forEach(function(option) {
        option.removeAttribute('data-selected');
        option.style.background = 'white';
        option.style.color = '#374151';
    });

    if (postId) {
        var selectedOption = document.querySelector(`.post-option[data-value="${postId}"]`);
        if (selectedOption) {
            selectedOption.setAttribute('data-selected', 'true');
            selectedOption.style.background = '#3b82f6';
            selectedOption.style.color = 'white';
        }
    }
}

// 点击外部关闭下拉菜单
function closeDropdownOnClickOutside(event) {
    var dropdown = document.getElementById('postDropdown');
    var input = document.getElementById('postFilterInput');

    if (!dropdown.contains(event.target) && !input.contains(event.target)) {
        dropdown.style.display = 'none';
        document.removeEventListener('click', closeDropdownOnClickOutside);
    }
}

// 应用岗位筛选
function applyPostFilter() {
    var postId = document.getElementById('postFilterInput').dataset.postId || '';
    var currentUrl = new URL(window.location.href);

    if (postId) {
        currentUrl.searchParams.set('post_id', postId);
    } else {
        currentUrl.searchParams.delete('post_id');
    }

    // 重新加载统计数据
    var layero = $('.layui-layer:visible').last();
    if (layero.length > 0) {
        layero.find('.layui-layer-content').html(
            '<div style="padding: 40px; text-align: center;">' +
            '<i class="fa fa-spinner fa-spin" style="font-size: 2rem; color: #10b981;"></i><br><br>' +
            '正在重新加载统计数据...' +
            '</div>'
        );

        // 使用新的URL参数重新加载数据
        $.ajax({
            url: '{:U("Training/getStats")}',
            type: 'GET',
            data: { post_id: postId },
            dataType: 'json',
            success: function(res) {
                if (res.status == 1) {
                    var statsHtml = generateStatsHtml(res.data);
                    layero.find('.layui-layer-content').html(statsHtml);

                    // 重新初始化筛选器状态
                    setTimeout(function() {
                        initPostFilterState(res.data.current_post_id, res.data.post_list);
                    }, 100);
                } else {
                    layero.find('.layui-layer-content').html(
                        '<div style="padding: 40px; text-align: center; color: #ef4444;">' +
                        '<i class="fa fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i><br>' +
                        '<h3>获取统计数据失败</h3>' +
                        '<p>' + (res.info || '请稍后重试') + '</p>' +
                        '</div>'
                    );
                }
            },
            error: function() {
                layero.find('.layui-layer-content').html(
                    '<div style="padding: 40px; text-align: center; color: #ef4444;">' +
                    '<i class="fa fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i><br>' +
                    '<h3>网络错误</h3>' +
                    '<p>请检查网络连接后重试</p>' +
                    '</div>'
                );
            }
        });
    }
}

// 清除岗位筛选
function clearPostFilter() {
    document.getElementById('postFilterInput').value = '全部岗位';
    document.getElementById('postFilterInput').dataset.postId = '';

    // 清除所有选中状态
    var options = document.querySelectorAll('.post-option');
    options.forEach(function(option) {
        option.removeAttribute('data-selected');
        option.style.background = 'white';
        option.style.color = '#374151';
    });

    applyPostFilter();
}

// 初始化筛选器状态
function initPostFilterState(currentPostId, postList) {
    var input = document.getElementById('postFilterInput');
    if (!input) return;

    if (currentPostId) {
        // 查找当前选中的岗位名称
        var selectedPostName = '全部岗位';
        for (var projectName in postList) {
            var posts = postList[projectName];
            for (var i = 0; i < posts.length; i++) {
                if (posts[i].id == currentPostId) {
                    selectedPostName = `[${projectName}] ${posts[i].name}`;
                    break;
                }
            }
        }

        input.value = selectedPostName;
        input.dataset.postId = currentPostId;

        // 更新选中状态
        setTimeout(function() {
            var selectedOption = document.querySelector(`.post-option[data-value="${currentPostId}"]`);
            if (selectedOption) {
                selectedOption.setAttribute('data-selected', 'true');
                selectedOption.style.background = '#3b82f6';
                selectedOption.style.color = 'white';
            }
        }, 50);
    } else {
        input.value = '全部岗位';
        input.dataset.postId = '';
    }
}

</script>