<?php
namespace Common\Model;

use Think\Model;

class ConfModel extends Model
{

    protected $_validate = [
       ['name', 'require', 'Key不能为空'],
       ['title', 'require', '配置名称不能为空'],
       ['value', 'require', '值不能为空'],
    ];


    //充值方式
    public $payment = [
    ];

    /**
     * 初始化配置
     * @param bool $f 强制刷新
     */
    public function init($f = false)
    {
        $data = F("conf");
        if (!$data || $f) {
            $res = $this->select();
            foreach ($res as $v) {
                $data[$v['name']] = $v['value'];
            }
            F("conf", $data);
        }
        return $data;
    }

    /**
     * 获取配置
     * @param string $param 配置的键名
     */
    public function C($param)
    {
        $conf = $this->init();
        return isset($conf[$param]) ? $conf[$param] : null;
    }

	public function set($name, $value) {
		return $this->where(['name' => $name])->save(['value' => $value]);
	}

	public function get($name) {
		return $this->where(['name' => $name])->getField('value');
	}

    protected function _before_write(&$data)
    {
		parent::_before_write($data);
		if(!empty($data['value'])) {
            $data['value'] = str_replace(__ROOT__.C("UPLOADPATH").'/', '', $data['value']);
		}
	}

    /*
     * 返回性别数组
    */
    public function sexArr()
    {
        $sys_conf = "[".$this->C("SYS_PRICE_SEX")."]";
        $sexPrice =  json_decode($sys_conf);
        return $sexPrice;
    }

    /*
     * 基础价
     * */
    public function priceBase()
    {
        return $this->C("SYS_PRICE_BASE");
    }

    /*
     *性别价格
     *  */
    public function priceSex($sex)
    {
        $sex_id = intval($sex);
        $sexPrice = $this->sexArr();
        return $sexPrice[$sex_id];
    }

    /*
     *地区价格
    * */
    public function priceRegion($region_id)
    {
        $region_id = intval($region_id);
        $regionPrice = D("Region")->where("id=".$region_id)->getField('price');
        return $regionPrice;
    }


}