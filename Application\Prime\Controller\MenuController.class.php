<?php

/**
 * Menu(菜单管理)
 */
namespace Prime\Controller;
use Common\Controller\PrimeController;

class MenuController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     *  显示菜单
     */
    public function index()
    {
        $res = D('Menu')->order(["sort" => "ASC"])->select();
        import("Util.Tree");
        $tree = new \Tree();
        $tree->icon = array('&nbsp;&nbsp;&nbsp;│ ', '&nbsp;&nbsp;&nbsp;├─ ', '&nbsp;&nbsp;&nbsp;└─ ');
        $tree->nbsp = '&nbsp;&nbsp;&nbsp;';
        
        $newmenus = [];
        foreach ($res as $m) {
        	$newmenus[$m['id']] = $m;
        }
        foreach ($res as $n=> $r) {
        	$res[$n]['level'] = $this->_get_level($r['id'], $newmenus);
        	$res[$n]['parentid_node'] = ($r['parentid']) ? ' class="child-of-node-' . $r['parentid'] . '"' : '';
            $res[$n]['str_manage'] = '<a href="' . U("menu/edit", ["parentid" => $r['id'], "menuid" => $_GET['menuid']]) . '">添加子菜单</a> 
                | <a href="' . U("menu/edit", array("id" => $r['id'], "menuid" => $_GET['menuid'])) . '">编辑</a> 
                | <a href="' . U("menu/delete", array("id" => $r['id'], "menuid" => I("get.menuid")) ). '" onclick="return confirm(\'确认要删除吗\')">删除</a> ';
            $res[$n]['status'] = D('Menu')->status[$r['status']];
            if(APP_DEBUG){
                $data = $r['data'] ? '?'.$r['data'] : '';
            	$res[$n]['app'] = $r['app']."/".$r['model']."/".$r['action'].$data;
            }
        }

        $tree->init($res);
        $str = "<tr id='node-\$id' \$parentid_node>
					<td style='padding-left:20px;display:flex;'><input name='sort[\$id]' type='text' style='width:55px' maxlength='3' value='\$sort' class='form-control'></td>
					<td>\$id</td>
        			<td>\$app</td>
					<td>\$spacer\$name</td>
				    <td>\$status</td>
					<td>\$str_manage</td>
				</tr>";
        $list = $tree->get_tree(0, $str);
        $this->assign("list", $list);
        $this->display();
    }

    /**
     * 批量排序
     */
    public function dosort()
    {
        if (! IS_POST) $this->redirect('index');
        $status = parent::_sort(M('Menu'));
        if ($status) {
            $this->success("排序更新成功！");
        } else {
            $this->error("排序更新失败！");
        }
    }

    /**
     *  添加/编辑
     */
    public function edit()
    {
    	if (IS_POST) {
    		if (D('Menu')->create()) {
                $id = intval(I("request.id"));
                $act = $id ? 'save' : 'add';
                $res = D('Menu')->$act();
    			if ($res === false) {
    				$this->error("更新失败！");
                } else {
    				$app=I("post.app");
    				$model=I("post.model");
    				$action=I("post.action");
    				$name=strtolower("$app/$model/$action");
    				$menu_name=I("post.name");
    				$mwhere=array("name"=>$name);
    				$rule = M('AuthRule')->where($mwhere)->find();
                    $data_r = ["name"=>$name,"module"=>$app,"type"=>"prime_url","title"=>$menu_name];
    				if(!$rule) {
    					M('AuthRule')->add($data_r);//type 1-admin rule;2-user rule
    				} else {
    					M('AuthRule')->where($mwhere)->save($data_r);//type 1-admin rule;2-user rule
    				}
                    $url = $id ? '' : U('menu/index');
    				$this->success("更新成功！", $url);
                }
    		} else {
    			$this->error(D('Menu')->getError());
    		}
    	} else {
            import("Util.Tree");
            $tree = new \Tree();
            $id = intval(I("get.id"));
            $parentid = intval(I("get.parentid"));
            if ($id) {
                $row = D('Menu')->where(array("id" => $id))->find();
                $pid = $row['parentid'];
                $this->assign("row", $row);
            } else if ($parentid) {
                $pid = $parentid;
            }
            $res = D('Menu')->order(["sort" => "ASC"])->select();
            foreach ($res as $r) {
                $r['selected'] = $r['id'] == $pid ? 'selected' : '';
                $array[] = $r;
            }
            $str = "<option value='\$id' \$selected>\$spacer \$name</option>";
            $tree->init($array);
            $select_categorys = $tree->get_tree(0, $str);
            $this->assign("select_categorys", $select_categorys);
            $this->display();
    	}
    }
    
    /**
     * 获取菜单深度
     * @param $id
     * @param $array
     * @param $i
     */
    protected function _get_level($id, $array = array(), $i = 0) {
    
    	if ($array[$id]['parentid']==0 || empty($array[$array[$id]['parentid']]) || $array[$id]['parentid']==$id){
    		return  $i;
    	}else{
    		$i++;
    		return $this->_get_level($array[$id]['parentid'],$array,$i);
    	}
    }
    
    /**
     *  删除
     */
    public function delete()
    {
        $id = intval(I("get.id"));
        $count = D('Menu')->where(["parentid" => $id])->count();
        if ($count > 0) $this->error("该菜单下还有子菜单，无法删除！");
        if (D('Menu')->delete($id) !== false) {
            $this->success("删除菜单成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    
    /**
     *  编辑
     */
    public function edit_post() {
    	if (IS_POST) {
    		if (D('Menu')->create()) {
    			if (D('Menu')->save() !== false) {
    				$app=I("post.app");
    				$model=I("post.model");
    				$action=I("post.action");
    				$name=strtolower("$app/$model/$action");
    				$menu_name=I("post.name");
    				$mwhere=array("name"=>$name);
    				
    				$find_rule=$this->auth_rule_model->where($mwhere)->find();
    				if(!$find_rule){
    					$this->auth_rule_model->add(array("name"=>$name,"module"=>$app,"type"=>"admin_url","title"=>$menu_name));//type 1-admin rule;2-user rule
    				}else{
    					$this->auth_rule_model->where($mwhere)->save(array("name"=>$name,"module"=>$app,"type"=>"admin_url","title"=>$menu_name));//type 1-admin rule;2-user rule
    				}
    				
    				$this->success("更新成功！");
    			} else {
    				$this->error("更新失败！");
    			}
    		} else {
    			$this->error(D('Menu')->getError());
    		}
    	}
    }

    //排序
    public function listorders() {
        $status = parent::_listorders(D('Menu'));
        if ($status) {
            $this->success("排序更新成功！");
        } else {
            $this->error("排序更新失败！");
        }
    }
    
    public function spmy_export_menu(){
    	$menus=D('Menu')->get_menu_tree(0);
    	
    	$menus_str= var_export($menus,true);
    	$menus_str=preg_replace("/\s+\d+\s=>\s(\n|\r)/", "\n", $menus_str);

    	foreach ($menus as $m){
    		$app=$m['app'];
    		$menudir=SPAPP.$app."/Menu";
    		if(!file_exists($menudir)){
    			mkdir($menudir);
    		}
    		$model=strtolower($m['model']);
    		
    		$menus_str= var_export($m,true);
    		$menus_str=preg_replace("/\s+\d+\s=>\s(\n|\r)/", "\n", $menus_str);
    		
    		file_put_contents($menudir."/admin_$model.php", "<?php\nreturn $menus_str;");
    		
    	}
    	$this->display("export_menu");
    }
    
    public function spmy_export_menu_lang(){
    	$apps=sp_scan_dir(SPAPP."*",GLOB_ONLYDIR);
    	foreach ($apps as $app){
    		if(is_dir(SPAPP.$app)){
    			$lang_dirs=sp_scan_dir(SPAPP."$app/Lang/*",GLOB_ONLYDIR);
    			
    			$menus = D('Menu')->where(array("app"=>$app))->order(array("listorder"=>"ASC","app" => "ASC","model" => "ASC","action" => "ASC"))->select();
    			foreach ($lang_dirs as $lang_dir){
    				$admin_menu_lang_file=SPAPP.$app."/Lang/".$lang_dir."/admin_menu.php";
    				$lang=array();
    				if(is_file($admin_menu_lang_file)){
    					$lang=include $admin_menu_lang_file;
    				}
    				
    				foreach ($menus as $menu){
    					$lang_key=strtoupper($menu['app'].'_'.$menu['model'].'_'.$menu['action']);
    					if(!isset($lang[$lang_key])){
    						$lang[$lang_key]=$menu['name'];
    					}
    				}
    				
    				$lang_str= var_export($lang,true);
    				$lang_str=preg_replace("/\s+\d+\s=>\s(\n|\r)/", "\n", $lang_str);
    		
    				file_put_contents($admin_menu_lang_file, "<?php\nreturn $lang_str;");
    			}
    			
    		}
    	}
    	
    	echo "success!";
    }
    /* public function dev_import_menu(){
    	$menus=F("Menu");
    	if(!empty($menus)){
    		$table_menu=C('DB_PREFIX')."menu";
    		D('Menu')->execute("TRUNCATE TABLE $table_menu;");
    		 
    		foreach($menus as $menu){
    			D('Menu')->add($menu);
    		}
    	}
    	
    	$this->display();
    } */
    
    private function _import_menu($menus,$parentid=0,&$error_menus=array()){
    	foreach ($menus as $menu){
    	
    		$app=$menu['app'];
    		$model=$menu['model'];
    		$action=$menu['action'];
    			
    		$where['app']=$app;
    		$where['model']=$model;
    		$where['action']=$action;
    		$children=isset($menu['children'])?$menu['children']:false;
    		unset($menu['children']);
    		$find_menu=D('Menu')->where($where)->find();
    		if($find_menu){
    			$newmenu=array_merge($find_menu,$menu);
    			$result=D('Menu')->save($newmenu);
    			if($result===false){
    				$error_menus[]="$app/$model/$action";
    				$parentid2=false;
    			}else{
    				$parentid2=$find_menu['id'];
    			}
    		}else{
    			$menu['parentid']=$parentid;
    			$result=D('Menu')->add($menu);
    			if($result===false){
    				$error_menus[]="$app/$model/$action";
    				$parentid2=false;
    			}else{
    				$parentid2=$result;
    			}
    		}
    		
    		$name=strtolower("$app/$model/$action");
    		$mwhere=array("name"=>$name);
    		
    		$find_rule=$this->auth_rule_model->where($mwhere)->find();
    		if(!$find_rule){
    			$this->auth_rule_model->add(array("name"=>$name,"module"=>$app,"type"=>"admin_url","title"=>$menu['name']));//type 1-admin rule;2-user rule
    		}else{
    			$this->auth_rule_model->where($mwhere)->save(array("module"=>$app,"type"=>"admin_url","title"=>$menu['name']));//type 1-admin rule;2-user rule
    		}
    		
    		if($children && $parentid!==false){
    			$this->_import_menu($children,$parentid2,$error_menus);
    		}
    	}
    	
    }
    
    public function spmy_import_menu(){
    	
    	$apps=sp_scan_dir(SPAPP."*",GLOB_ONLYDIR);
    	$error_menus=array();
    	foreach ($apps as $app){
    		if(is_dir(SPAPP.$app)){
    			$menudir=SPAPP.$app."/Menu";
    			$menu_files=sp_scan_dir($menudir."/admin_*.php",null);
    			if(count($menu_files)){
    				foreach ($menu_files as $mf){
    					//是php文件
    					$mf_path=$menudir."/$mf";
    					if(file_exists($mf_path)){
    						$menudatas=include   $mf_path;
    						if(is_array($menudatas) && !empty($menudatas)){
    							$this->_import_menu(array($menudatas),0,$error_menus);
    						}
    					}
    						
    						
    				}
    			}
    			 
    		}
    	}
		$this->assign("errormenus",$error_menus);
    	$this->display("import_menu");
    }
    
    private function _import_submenu($submenus,$parentid){
    	foreach($submenus as $sm){
    		$data=$sm;
    		$data['parentid']=$parentid;
    		unset($data['items']);
    		$id=D('Menu')->add($data);
    		if(!empty($sm['items'])){
    				$this->_import_submenu($sm['items'],$id);
    		}else{
    			return;
    		}
    	}
    }
    
    private function _generate_submenu(&$rootmenu,$m){
    	$parentid=$m['id'];
    	$rm=D('Menu')->menu($parentid);
    	unset($rootmenu['id']);
    	unset($rootmenu['parentid']);
    	if(count($rm)){
    		
    		$count=count($rm);
    		for($i=0;$i<$count;$i++){
    			$this->_generate_submenu($rm[$i],$rm[$i]);
    			
    		}
    		$rootmenu['items']=$rm;
    		
    	}else{
    		return ;
    	}
    	
    }
    
    
    public function spmy_getactions(){
    	$apps_r=array("Comment");
    	$groups=C("MODULE_ALLOW_LIST");
    	$group_count=count($groups);
    	$newmenus=array();
    	$g=I("get.app");
    	if(empty($g)){
    		$g=$groups[0];
    	}
    	
    	if(in_array($g, $groups)){
    		if(is_dir(SPAPP.$g)){
    			if(!(strpos($g, ".") === 0)){
    				$actiondir=SPAPP.$g."/Controller";
    				$actions=sp_scan_dir($actiondir."/*");
    				if(count($actions)){
    					foreach ($actions as $mf){
    						if(!(strpos($mf, ".") === 0)){
    							if($g=="Admin"){
    								$m=str_replace("Controller.class.php", "",$mf);
    								$noneed_models=array("Public","Index","Main");
    								if(in_array($m, $noneed_models)){
    									continue;
    								}
    							}else{
    								if(strpos($mf,"adminController.class.php") || strpos($mf,"Admin")===0){
    									$m=str_replace("Controller.class.php", "",$mf);
    								}else{
    									continue;
    								}
    							}
    							$class=A($g."/".$m);
    							$adminbaseaction=new \Common\Controller\AdminbaseController();
    							$base_methods=get_class_methods($adminbaseaction);
    							$methods=get_class_methods($class);
    							$methods=array_diff($methods, $base_methods);
    							
    							foreach ($methods as $a){
    								if(!(strpos($a, "_") === 0) && !(strpos($a, "spmy_") === 0)){
    									$where['app']=$g;
    									$where['model']=$m;
    									$where['action']=$a;
    									$count=D('Menu')->where($where)->count();
    									if(!$count){
    										$data['parentid']=0;
    										$data['app']=$g;
    										$data['model']=$m;
    										$data['action']=$a;
    										$data['type']="1";
    										$data['status']="0";
    										$data['name']="未知";
    										$data['listorder']="0";
    										$result=D('Menu')->add($data);
    										if($result!==false){
    											$newmenus[]=   $g."/".$m."/".$a."";
    										}
    									}
    									
    									$name=strtolower("$g/$m/$a");
    									$mwhere=array("name"=>$name);
    									
    									$find_rule=$this->auth_rule_model->where($mwhere)->find();
    									if(!$find_rule){
    										$this->auth_rule_model->add(array("name"=>$name,"module"=>$g,"type"=>"admin_url","title"=>""));//type 1-admin rule;2-user rule
    									}
    								}
    							}
    						}
    						 
    		
    					}
    				}
    			}
    		}
    		
    		$index=array_search($g, $groups);
    		$nextindex=$index+1;
    		$nextindex=$nextindex>=$group_count?0:$nextindex;
    		if($nextindex){
    			$this->assign("nextapp",$groups[$nextindex]);
    		}
    		$this->assign("app",$g);
    	}
    	 
    	$this->assign("newmenus",$newmenus);
    	$this->display("getactions");
    	
    }

}

