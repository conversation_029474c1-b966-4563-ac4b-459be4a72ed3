<!DOCTYPE html>
<html>
<head>
    <title>测试岗位要求配置</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/layer@3.5.1/dist/layer.js"></script>
</head>
<body>
    <h1>测试岗位要求配置</h1>
    
    <form id="testForm">
        <div>
            <label>毕业年限:</label>
            <input type="number" name="graduation_years" value="3" min="0" max="20">
        </div>
        
        <div>
            <label>是否应届生:</label>
            <input type="radio" name="is_fresh_graduate" value="0" checked> 不限
            <input type="radio" name="is_fresh_graduate" value="1"> 仅限应届生
            <input type="radio" name="is_fresh_graduate" value="2"> 非应届生
        </div>
        
        <div>
            <label>政治面貌:</label>
            <input type="text" name="political_status" value="党员">
        </div>
        
        <button type="submit">保存配置</button>
    </form>

    <script>
    $(function() {
        $('#testForm').on('submit', function(e) {
            e.preventDefault();
            
            var formData = $(this).serialize();
            console.log('提交数据:', formData);
            
            $.ajax({
                url: '/Prime/RecruitmentNotice/requirements?notice_id=1&post_id=1',
                type: 'POST',
                data: formData,
                dataType: 'json',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    console.log('成功响应:', response);
                    if (response.status == 1) {
                        layer.msg(response.info, {icon: 1});
                    } else {
                        layer.msg(response.info, {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    console.log('错误:', xhr.responseText);
                    layer.msg('网络错误：' + error, {icon: 2});
                }
            });
        });
    });
    </script>
</body>
</html>
