<?php
/**
 * 水印检测日志记录器
 * 提供结构化的日志记录和错误处理
 */

class WatermarkLogger {
    
    const LOG_LEVEL_INFO = 'INFO';
    const LOG_LEVEL_WARNING = 'WARNING';
    const LOG_LEVEL_ERROR = 'ERROR';
    const LOG_LEVEL_DEBUG = 'DEBUG';
    
    private static $logFile = 'Application/logs/watermark_validation.log';
    private static $errorLogFile = 'Application/logs/watermark_errors.log';
    
    /**
     * 记录检测结果
     * @param array $result 检测结果
     * @param int $userId 用户ID
     * @param string $fileName 文件名
     * @param array $additionalData 额外数据
     */
    public static function logDetectionResult($result, $userId, $fileName, $additionalData = []) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => $userId,
            'file_name' => $fileName,
            'file_size' => isset($additionalData['file_size']) ? $additionalData['file_size'] : 0,
            'validation_status' => $result['is_official_template'] ? 'AUTHENTIC' : 'INVALID',
            'validation_level' => $result['validation_level'],
            'validation_score' => $result['validation_score'],
            'detection_method' => $result['detection_method'],
            'ip_address' => self::getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'session_id' => session_id(),
            'additional_data' => $additionalData
        ];
        
        self::writeLog(self::$logFile, $logData, self::LOG_LEVEL_INFO);
        
        // 如果检测到官方模板，记录到PHP错误日志
        if ($result['is_official_template']) {
            $message = sprintf(
                '简历模板检测 - 用户ID: %s, 文件: %s, 检测结果: 官方模板, 方法: %s, 分数: %d',
                $userId,
                $fileName,
                $result['detection_method'],
                $result['validation_score']
            );
            error_log($message);
        }
    }
    
    /**
     * 记录错误信息
     * @param string $message 错误消息
     * @param Exception $exception 异常对象
     * @param array $context 上下文信息
     */
    public static function logError($message, $exception = null, $context = []) {
        $errorData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => self::LOG_LEVEL_ERROR,
            'message' => $message,
            'exception' => $exception ? [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ] : null,
            'context' => $context,
            'ip_address' => self::getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ];
        
        self::writeLog(self::$errorLogFile, $errorData, self::LOG_LEVEL_ERROR);
        
        // 同时记录到PHP错误日志
        $errorMessage = "水印检测错误: {$message}";
        if ($exception) {
            $errorMessage .= " - " . $exception->getMessage();
        }
        error_log($errorMessage);
    }
    
    /**
     * 记录警告信息
     * @param string $message 警告消息
     * @param array $context 上下文信息
     */
    public static function logWarning($message, $context = []) {
        $warningData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => self::LOG_LEVEL_WARNING,
            'message' => $message,
            'context' => $context,
            'ip_address' => self::getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        self::writeLog(self::$errorLogFile, $warningData, self::LOG_LEVEL_WARNING);
    }
    
    /**
     * 记录调试信息
     * @param string $message 调试消息
     * @param array $context 上下文信息
     */
    public static function logDebug($message, $context = []) {
        // 只在调试模式下记录
        if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
            return;
        }
        
        $debugData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => self::LOG_LEVEL_DEBUG,
            'message' => $message,
            'context' => $context,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
        
        self::writeLog(self::$errorLogFile, $debugData, self::LOG_LEVEL_DEBUG);
    }
    
    /**
     * 写入日志文件
     * @param string $logFile 日志文件路径
     * @param array $data 日志数据
     * @param string $level 日志级别
     */
    private static function writeLog($logFile, $data, $level) {
        try {
            // 确保日志目录存在
            $logDir = dirname($logFile);
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            // 添加日志级别
            $data['level'] = $level;
            
            // 转换为JSON格式
            $logEntry = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . "\n";
            
            // 写入文件
            file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
            
            // 日志文件大小控制（超过10MB时轮转）
            self::rotateLogIfNeeded($logFile);
            
        } catch (Exception $e) {
            // 日志写入失败，记录到PHP错误日志
            error_log("水印日志写入失败: " . $e->getMessage());
        }
    }
    
    /**
     * 日志文件轮转
     * @param string $logFile 日志文件路径
     */
    private static function rotateLogIfNeeded($logFile) {
        if (!file_exists($logFile)) {
            return;
        }
        
        $maxSize = 10 * 1024 * 1024; // 10MB
        if (filesize($logFile) > $maxSize) {
            $backupFile = $logFile . '.' . date('Y-m-d-H-i-s') . '.bak';
            rename($logFile, $backupFile);
            
            // 只保留最近5个备份文件
            self::cleanupOldBackups(dirname($logFile), basename($logFile));
        }
    }
    
    /**
     * 清理旧的备份文件
     * @param string $logDir 日志目录
     * @param string $logFileName 日志文件名
     */
    private static function cleanupOldBackups($logDir, $logFileName) {
        $pattern = $logDir . '/' . $logFileName . '.*.bak';
        $backupFiles = glob($pattern);
        
        if (count($backupFiles) > 5) {
            // 按修改时间排序
            usort($backupFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // 删除最旧的文件
            $filesToDelete = array_slice($backupFiles, 0, count($backupFiles) - 5);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * 获取客户端IP地址
     * @return string
     */
    private static function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // 处理多个IP的情况（X-Forwarded-For可能包含多个IP）
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // 验证IP格式
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * 获取最近的日志记录
     * @param int $limit 记录数量
     * @param string $logFile 日志文件路径
     * @return array
     */
    public static function getRecentLogs($limit = 10, $logFile = null) {
        if ($logFile === null) {
            $logFile = self::$logFile;
        }
        
        if (!file_exists($logFile)) {
            return [];
        }
        
        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $recentLines = array_slice($lines, -$limit);
        
        $logs = [];
        foreach ($recentLines as $line) {
            $logData = json_decode($line, true);
            if ($logData) {
                $logs[] = $logData;
            }
        }
        
        return array_reverse($logs);
    }
    
    /**
     * 获取统计信息
     * @param int $days 统计天数
     * @return array
     */
    public static function getStatistics($days = 7) {
        $stats = [
            'total_detections' => 0,
            'authentic_count' => 0,
            'invalid_count' => 0,
            'detection_methods' => [],
            'daily_stats' => []
        ];
        
        if (!file_exists(self::$logFile)) {
            return $stats;
        }
        
        $lines = file(self::$logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $cutoffDate = date('Y-m-d', strtotime("-{$days} days"));
        
        foreach ($lines as $line) {
            $logData = json_decode($line, true);
            if (!$logData || $logData['timestamp'] < $cutoffDate) {
                continue;
            }
            
            $stats['total_detections']++;
            
            if ($logData['validation_status'] === 'AUTHENTIC') {
                $stats['authentic_count']++;
            } else {
                $stats['invalid_count']++;
            }
            
            $method = $logData['detection_method'];
            if (!isset($stats['detection_methods'][$method])) {
                $stats['detection_methods'][$method] = 0;
            }
            $stats['detection_methods'][$method]++;
            
            $date = substr($logData['timestamp'], 0, 10);
            if (!isset($stats['daily_stats'][$date])) {
                $stats['daily_stats'][$date] = ['total' => 0, 'authentic' => 0];
            }
            $stats['daily_stats'][$date]['total']++;
            if ($logData['validation_status'] === 'AUTHENTIC') {
                $stats['daily_stats'][$date]['authentic']++;
            }
        }
        
        return $stats;
    }
}
?>
