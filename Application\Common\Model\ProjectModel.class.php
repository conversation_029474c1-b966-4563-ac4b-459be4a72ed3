<?php
namespace Common\Model;

use Think\Model;

class ProjectModel extends Model
{
    protected $_auto = [ 
        ['create_time', 'time', 1, 'function'],
        ['upadte_time', 'time', 2, 'function'],
    ];

    public $status = [
        '0' => ['text' => '下架', 'style' => 'info'],
        '1' => ['text' => '上架', 'style' => 'success'],
    ];

    public  $category = [
        '1' => '央企',
        '2' => '国企',
        '3' => '事业单位',
        '4' => '上市公司',
        '5' => '民营企业',
        '6' => '出国',
        '7' => '其他',
    ];

    function enHash($id)
    {
        $r = $id * 499 % 900 + 100;
        $code = strrev(numIdEncode(intval($r . $id), 3));
        return $code;
    }

    function deHash($code)
    {
        $id = substr(numIdDecode(strrev($code)), 3);
        return $id;
    }
}