<?php
namespace Common\Model;

use Think\Model;

class ServiceStationPlatformMessageModel extends Model
{
    protected $_auto = [ 
        ['created', 'time', 1, 'function'],
        ['updated', 'time', 2, 'function'],
    ];

    public $type = [
        '1' => ['text' => '服务站', 'style' => 'success'],
        '2' => ['text' => '平台', 'style' => 'danger'],
    ];



    public function _save($data)
    {
        $ret = false;
        $row = $this->where()->find($data['id']);
        if ($row) {
            $data['id'] = $row['id'];
            $this->create($data);
            $ret = $this->save();
        } else {
            $this->create($data);
            $ret = $this->add();
        }
        return $ret;
    }

    /**
     * 根据openid 和 access_token 初始化用户信息
     */
    public function init($openid = null, $access_token = null, $type = 1)
    {
        if (! $openid || !$access_token) return;
        $data = [];
        $row = $this->where(['openid'=>$openid])->find();
        if (! $row) {
        }
        vendor('LaneWeChat.lanewechat');
        $wx_userinfo = \LaneWeChat\Core\WeChatOAuth::getUserInfo($access_token, $openid);
        if ($wx_userinfo['errcode']) {
            dolog('error/wx/getuserinfo', 'failed to getuserinfo, return data:'.serialize($wx_userinfo)); // 日志记录
            return;
        } else {
            $data += [
                'nickname' => $wx_userinfo['nickname'],
                'headimgurl' => substr($wx_userinfo['headimgurl'], 0, -1),
                'sex' => $wx_userinfo['sex'],
                'province' => $wx_userinfo['province'],
                'city' => $wx_userinfo['city'],
                'country' => $wx_userinfo['country'],
            ];
            if ($row) {
                $data['id'] = $row['id'];
                if (empty($row['unionid'])) {
                    $data['unionid'] = $wx_userinfo['unionid'];
                }
                $act = 'save';
            } else {
                $data['subscribe_status'] = 0;
                $data['service_id'] = $type ? : 1;
                $data['unionid'] = $wx_userinfo['unionid'];
                $act = 'add';
            }
            $this->create($data);
            $res = $this->$act();
            $user_id = !empty($row['id']) ? $row['id'] : $res;
            if ($type == 1) {
                if (!session("openid")) session('openid', $openid);
                if ($res && !session("wx.user_id")) session('wx.user_id', $user_id);
            } else {
                if (!session("openid2")) session('openid', $openid);
                if ($res && !session("wx.user_id2")) session('wx.user_id2', $user_id);
            }
            return true;
        }
    }
}