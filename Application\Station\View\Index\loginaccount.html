﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <title>账号登录管理</title>
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <style>
        :root {
            --primary-color: #00bf80;
            --secondary-color: #666;
            --card-bg: #fff;
            --border-color: #eee;
        }
 
        .container {
            max-width: 760px;
            margin: 12px;
        }

        /* 账号列表样式 */
        .account-list {
            background: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .account-item {
            display: flex;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .account-item:last-child {
            border-bottom: none;
        }

        /* 头像样式 */
        .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin-right: 16px;
            object-fit: cover;
        }

        /* 用户信息 */
        .user-info {
            flex: 1;
        }
        .nickname {
            font-size: 16px;
            color: #000;
            margin-bottom: 4px;
        }
        .login-status {
            font-size: 12px;
            color: var(--secondary-color);
        }

        /* 退出按钮 */
        .logout-btn {
            padding: 8px;
            border-radius: 8px;
            border: 1px solid var(--primary-color);
            background: none;
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.2s;
        }
        .logout-btn:hover {
            background: var(--primary-color);
            color: #fff;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 40px;
            color: var(--secondary-color);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .account-item {
                padding: 12px;
                /*flex-wrap: wrap;*/
            }
            .avatar {
                width: 40px;
                height: 40px;
            }
            .logout-btn {
                /*width: 100%;*/
                /*margin-top: 12px;*/
            }
        }

                 /* 返回箭头样式 */
.back-arrow {
    position: fixed;
    top: 115px;
    left: 15px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.2s;
}

.back-arrow::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    width: 12px;
    height: 12px;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: translateY(-50%) rotate(45deg);
}

/* 点击反馈效果 */
.back-arrow:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.7);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .back-arrow {
        top: 170px;
        left: 10px;
        width: 36px;
        height: 36px;
    }
    
    .back-arrow::before {
        left: 13px;
        width: 10px;
        height: 10px;
    }
}
    </style>
</head>
<body>
<!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
<include file="headers"/>
    <div class="container">
        <h1 style="margin-bottom: 24px; font-size: 24px;text-align: center;">账号登录管理</h1>
        <div class="account-list">
            <!-- 登录账号示例 -->
            <php>foreach ($userLists as $userRows) {</php>
            <div class="account-item">
                <img src="{:$userRows['headimgurl']."2"}" class="avatar" alt="用户头像">
                <div class="user-info">
                    <div class="nickname">{:$userRows['nickname'] ?: ''} <a href="{:U('index/userlog')}" title="操作日志" style="font-size: 12px;"><i class="iconfont icon-flow-determine"></i>操作日志</a></div>
                   
                    <div class="login-status">最后登录时间：{:$userRows['last_login_time'] > 0 ? date("Y年m月d日 H:i:s", $userRows['last_login_time']) : '--'}</div>
                </div>
                <a class="logout-btn" href="{:U('index/outloginaccount', ['id' => $userRows['id']])}">退出登录</a>
            </div>
            <php>}</php>



            <!-- 更多账号项... -->
            <div class="empty-state">没有更多登录账号</div>
        </div>
    </div>

    <script>
        
        var noticeSwiper = new Swiper('.noticeSwiper', {
            direction: 'vertical',
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            }
        })
        var noticeSwiper = new Swiper('.page0106Swiper', {
            pagination: {
                el: ".swiper-pagination",
            },

            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            }
        });
        
        // 退出登录逻辑示例
        document.querySelectorAll('.logout-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const accountItem = this.closest('.account-item')
                if(confirm('确定要退出此账号吗？')) {
                    accountItem.remove()
                    // 这里可以添加真实退出逻辑
                }
            })
        })
        // 返回逻辑（优先返回历史记录，失败则跳转首页）
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/'; // 修改为你的默认回退地址
            }
        }
    </script>
</body>
</html>