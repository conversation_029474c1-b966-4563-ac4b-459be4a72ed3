<include file="block/hat" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化项目管理页面样式 */
                .project-index-wrapper {
                    width: 100%;
                }

                /* 现代化页面标题 */
                .project-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                    width: 100%;
                }

                .project-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #5a67d8 100%);
                }

                .project-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .project-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .project-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .project-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .project-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .project-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .project-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .project-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .project-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .project-index-add-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                    text-decoration: none;
                }

                .project-index-add-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化搜索面板 */
                .project-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    max-height: 0;
                    opacity: 0;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    width: 100%;
                }

                .project-index-search-panel.show {
                    max-height: 500px;
                    opacity: 1;
                }

                .project-index-search-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .project-index-search-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .project-index-search-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .project-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .project-index-search-body {
                    padding: 2rem;
                }

                .project-index-search-form {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 1.5rem;
                    align-items: end;
                }

                .project-index-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .project-index-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                }

                .project-index-form-select,
                .project-index-form-input {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-family: inherit;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: #f8fafc;
                }

                .project-index-form-select:focus,
                .project-index-form-input:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    background: white;
                }

                .project-index-search-actions {
                    display: flex;
                    gap: 1rem;
                    margin-top: 1rem;
                }

                .project-index-search-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .project-index-search-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .project-index-search-btn.btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .project-index-search-btn.btn-primary:hover {
                    color: white;
                }

                .project-index-search-btn.btn-secondary {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                }

                .project-index-search-btn.btn-secondary:hover {
                    color: white;
                }

                /* 现代化导航标签 */
                .project-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    width: 100%;
                }

                .project-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .project-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .project-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .project-index-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .project-index-nav-link.active {
                    color: #667eea !important;
                    background: white !important;
                    border-bottom-color: #667eea !important;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1) !important;
                    font-weight: 700 !important;
                }

                .project-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                    z-index: 1;
                }

                .project-index-nav-link.active .project-index-nav-icon {
                    color: #667eea !important;
                    transform: scale(1.1);
                }

                .project-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化列表布局 */
                .project-index-list-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    margin-bottom: 2rem;
                    width: 100%;
                }

                .project-list-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .project-list-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .project-list-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .project-list-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .project-list-body {
                    padding: 0;
                }

                .project-list-item {
                    display: flex;
                    align-items: center;
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #f1f5f9;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .project-list-item:last-child {
                    border-bottom: none;
                }

                .project-list-item:hover {
                    background: #f8fafc;
                    transform: translateX(4px);
                }

                .project-list-item::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 4px;
                    background: transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .project-list-item:hover::before {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .project-item-id {
                    width: 80px;
                    flex-shrink: 0;
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: #667eea;
                    text-align: center;
                    background: #e0e7ff;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    border: 2px solid #c7d2fe;
                }

                .project-item-content {
                    flex: 1;
                    margin-left: 2rem;
                    min-width: 0;
                }

                .project-item-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1f2937;
                    margin: 0 0 0.5rem 0;
                    line-height: 1.4;
                    word-break: break-word;
                }

                .project-item-meta {
                    display: flex;
                    align-items: center;
                    gap: 1.5rem;
                    flex-wrap: wrap;
                }

                .project-item-category {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.25rem;
                    color: #6b7280;
                }

                .project-item-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .project-status-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    color: white;
                }

                .project-status-badge.label-success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                }

                .project-status-badge.label-warning {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                }

                .project-status-badge.label-danger {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                }

                .project-status-badge.label-info {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                }

                .project-status-badge.label-default {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                }

                .project-item-time {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.25rem;
                    color: #6b7280;
                }

                .project-item-actions {
                    margin-left: 2rem;
                    flex-shrink: 0;
                    display: flex;
                    gap: 0.75rem;
                    flex-wrap: wrap;
                }

                .project-item-actions .btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    white-space: nowrap;
                }

                .project-item-actions .btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .project-index-header-content {
                        flex-direction: column;
                        align-items: flex-start;
                    }

                    .project-index-actions {
                        width: 100%;
                        justify-content: flex-start;
                    }

                    .project-list-item {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 1rem;
                        padding: 1.5rem 1rem;
                    }

                    .project-item-content {
                        margin-left: 0;
                        width: 100%;
                    }

                    .project-item-meta {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 0.75rem;
                    }

                    .project-item-actions {
                        margin-left: 0;
                        width: 100%;
                        justify-content: flex-start;
                    }

                    .project-item-actions .btn {
                        flex: 1;
                        justify-content: center;
                        min-width: 0;
                    }

                    .project-index-nav-tabs {
                        flex-direction: column;
                    }

                    .project-index-nav-item {
                        flex: none;
                    }

                    .project-index-nav-link {
                        border-bottom: 1px solid #e2e8f0;
                        border-right: none;
                    }

                    .project-index-nav-item:last-child .project-index-nav-link {
                        border-bottom: none;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .project-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .project-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .project-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .project-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }



                /* 搜索面板 */
                .project-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .project-search-panel::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .project-search-body {
                    padding: 2rem;
                }

                .project-search-form {
                    display: flex;
                    gap: 1rem;
                    align-items: end;
                    flex-wrap: wrap;
                }

                .project-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    min-width: 150px;
                }

                .project-form-label {
                    font-weight: 600;
                    color: #2d3748;
                    font-size: 1.5rem;
                }

                .project-form-control {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: white;
                    font-family: inherit;
                }

                .project-form-control:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .project-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                /* 现代化按钮 */
                .project-btn {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    font-size: 1.5rem;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                    font-family: inherit;
                }

                .project-btn:before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .project-btn:hover:before {
                    left: 100%;
                }

                .project-btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .project-btn-primary:hover {
                    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    color: white;
                    text-decoration: none;
                }

                .project-btn-warning {
                    background: #ed8936;
                    color: white;
                }

                .project-btn-warning:hover {
                    background: #d69e2e;
                    transform: translateY(-1px);
                    color: white;
                    text-decoration: none;
                }
            </style>
 
 <style type='text/css'>
                /* 现代化表格样式 */
                .project-table-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    position: relative;
                }

                .project-table-panel::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .project-table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 1.5rem;
                }

                .project-table th {
                    background: #f7fafc;
                    color: #2d3748;
                    font-weight: 600;
                    padding: 1rem;
                    text-align: left;
                    border-bottom: 2px solid #e2e8f0;
                    font-size: 1.5rem;
                }

                .project-table td {
                    padding: 1rem;
                    border-bottom: 1px solid #f1f5f9;
                    vertical-align: middle;
                    font-size: 1.5rem;
                }

                .project-table tr:hover {
                    background: #f7fafc;
                }

                .project-table tr:last-child td {
                    border-bottom: none;
                }

                /* 项目ID样式 */
                .project-id {
                    background: #f1f5f9;
                    color: #4a5568;
                    padding: 0.25rem 0.75rem;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    display: inline-block;
                    font-size: 1.5rem;
                }

                /* 项目名称样式 */
                .project-name {
                    font-weight: 600;
                    color: #1a202c;
                    font-size: 1.5rem;
                }

                /* 项目归类样式 */
                .project-category {
                    color: #4a5568;
                    font-size: 1.5rem;
                }

                /* 时间样式 */
                .project-time {
                    color: #718096;
                    font-size: 1.5rem;
                    line-height: 1.4;
                }

                /* 状态标签样式 */
                .project-status-label {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.025em;
                }

                .project-status-label.label-success {
                    background: rgba(72, 187, 120, 0.1);
                    color: #48bb78;
                }

                .project-status-label.label-warning {
                    background: rgba(237, 137, 54, 0.1);
                    color: #ed8936;
                }

                .project-status-label.label-info {
                    background: rgba(66, 153, 225, 0.1);
                    color: #4299e1;
                }

                .project-status-label.label-danger {
                    background: rgba(245, 101, 101, 0.1);
                    color: #f56565;
                }

                /* 操作按钮样式 */
                .project-action-btns {
                    display: flex;
                    gap: 0.5rem;
                    flex-wrap: wrap;
                }

                .project-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.5rem 0.75rem;
                    border: none;
                    border-radius: 0.375rem;
                    font-weight: 500;
                    font-size: 1.5rem;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    font-family: inherit;
                }

                .project-action-btn:hover {
                    transform: translateY(-1px);
                    text-decoration: none;
                }

                .project-action-btn.btn-primary {
                    background: #667eea;
                    color: white;
                }

                .project-action-btn.btn-primary:hover {
                    background: #5a67d8;
                    color: white;
                }

                .project-action-btn.btn-success {
                    background: #48bb78;
                    color: white;
                }

                .project-action-btn.btn-success:hover {
                    background: #38a169;
                    color: white;
                }

                .project-action-btn.btn-warning {
                    background: #ed8936;
                    color: white;
                }

                .project-action-btn.btn-warning:hover {
                    background: #d69e2e;
                    color: white;
                }

                .project-action-btn.btn-danger {
                    background: #f56565;
                    color: white;
                }

                .project-action-btn.btn-danger:hover {
                    background: #e53e3e;
                    color: white;
                }

                /* 分页样式 */
                .project-pagination {
                    padding: 1.5rem 2rem;
                    background: #f7fafc;
                    border-top: 1px solid #e2e8f0;
                    text-align: center;
                }

                .project-pagination .pagination {
                    margin: 0;
                    justify-content: center;
                }

                .project-pagination .pagination a,
                .project-pagination .pagination span {
                    font-size: 1.5rem;
                    padding: 0.5rem 0.75rem;
                    margin: 0 0.25rem;
                    border-radius: 0.375rem;
                    transition: all 0.3s ease;
                }

                .project-pagination .pagination a:hover {
                    background: #667eea;
                    color: white;
                    text-decoration: none;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .project-search-form {
                        flex-direction: column;
                        align-items: stretch;
                    }

                    .project-form-group {
                        min-width: auto;
                    }
                }

                @media (max-width: 768px) {
                    .project-page-container {
                        padding: 1.5rem;
                    }

                    .project-tab-list {
                        flex-direction: column;
                    }

                    .project-tab-item {
                        flex: none;
                    }

                    .project-tab-link {
                        border-right: none;
                        border-bottom: 1px solid #e2e8f0;
                    }

                    .project-tab-item:last-child .project-tab-link {
                        border-bottom: none;
                    }

                    .project-table {
                        font-size: 1.5rem;
                    }

                    .project-table th,
                    .project-table td {
                        padding: 0.75rem 0.5rem;
                        font-size: 1.5rem;
                    }

                    .project-action-btns {
                        flex-direction: column;
                    }

                    .project-action-btn {
                        justify-content: center;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .project-fade-in {
                    animation: fadeInUp 0.5s ease-out;
                }

                .project-fade-in-delay-1 {
                    animation: fadeInUp 0.5s ease-out 0.1s both;
                }

                .project-fade-in-delay-2 {
                    animation: fadeInUp 0.5s ease-out 0.2s both;
                }
            </style>

            <!-- 现代化页面标题 -->
            <div class="project-index-header project-index-fade-in">
                <div class="project-index-header-content">
                    <div class="project-index-title">
                        <div class="project-index-title-icon">
                            <i class="fa fa-briefcase"></i>
                        </div>
                        <div class="project-index-title-text">
                            <h1 class="project-index-title-main">项目管理</h1>
                            <p class="project-index-title-sub">Project Management</p>
                        </div>
                    </div>
                    <div class="project-index-actions">
                        <button type="button" class="project-index-search-toggle" onclick="toggleSearchPanel()">
                            <i class="fa fa-search"></i>
                            <span>搜索筛选</span>
                        </button>
                        <a href="{:U('Project/edit')}" class="project-index-add-btn">
                            <i class="fa fa-plus"></i>
                            <span>项目添加</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 现代化搜索面板 -->
            <div class="project-index-search-panel project-index-fade-in-delay-1" id="searchPanel">
                <div class="project-index-search-header">
                    <div class="project-index-search-icon">
                        <i class="fa fa-search"></i>
                    </div>
                    <h3 class="project-index-search-title">搜索筛选</h3>
                </div>
                <div class="project-index-search-body">
                    <form method="get" class="project-index-search-form" role="form">
                        <div class="project-index-form-group">
                            <label class="project-index-form-label">搜索字段</label>
                            <select class="project-index-form-select" name="kw">
                                <php>foreach($c_kw as $key=>$value){</php>
                                <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                <php>}</php>
                            </select>
                        </div>
                        <div class="project-index-form-group">
                            <label class="project-index-form-label">搜索内容</label>
                            <input class="project-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
                        </div>
                        <div class="project-index-form-group">
                            <label class="project-index-form-label">项目状态</label>
                            <select name="status" class="project-index-form-select">
                                <option value="">全部状态</option>
                                <php>foreach($statusList as $key => $status) {</php>
                                <option value="{$key}" {:$_get['status'] != '' && $_get['status'] == $key ? "selected" : ''}>{$status.text}</option>
                                <php>} </php>
                            </select>
                        </div>
                        <div class="project-index-search-actions">
                            <button type="submit" class="project-index-search-btn btn-primary">
                                <i class="fa fa-search"></i>
                                <span>搜索</span>
                            </button>
                            <a href="{:U('Project/index')}" class="project-index-search-btn btn-secondary">
                                <i class="fa fa-refresh"></i>
                                <span>重置</span>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 现代化导航标签 -->
            <div class="project-index-nav-container project-index-fade-in-delay-2">
                <ul class="project-index-nav-tabs">
                    <li class="project-index-nav-item">
                        <a href="{:U('Project/index')}" class="project-index-nav-link <php>if(CONTROLLER_NAME=='Project' && ACTION_NAME=='index') echo 'active'</php>">
                            <i class="fa fa-briefcase project-index-nav-icon"></i>
                            <span>项目管理</span>
                        </a>
                    </li>
                    <li class="project-index-nav-item">
                        <a href="{:U('Project/edit')}" class="project-index-nav-link <php>if(CONTROLLER_NAME=='Project' && ACTION_NAME=='edit') echo 'active'</php>">
                            <i class="fa fa-plus project-index-nav-icon"></i>
                            <span>添加项目</span>
                        </a>
                    </li>
                    <li class="project-index-nav-item">
                        <a href="{:U('Projectpost/index')}" class="project-index-nav-link <php>if(CONTROLLER_NAME=='Projectpost' && ACTION_NAME=='index') echo 'active'</php>">
                            <i class="fa fa-users project-index-nav-icon"></i>
                            <span>岗位管理</span>
                        </a>
                    </li>
                    <li class="project-index-nav-item">
                        <a href="{:U('Projectpost/edit')}" class="project-index-nav-link <php>if(CONTROLLER_NAME=='Projectpost' && ACTION_NAME=='edit') echo 'active'</php>">
                            <i class="fa fa-plus project-index-nav-icon"></i>
                            <span>添加岗位</span>
                        </a>
                    </li>
                    <li class="project-index-nav-item">
                        <a href="{:U('Project/quotation')}" class="project-index-nav-link <php>if(ACTION_NAME=='quotation') echo 'active'</php>">
                            <i class="fa fa-calculator project-index-nav-icon"></i>
                            <span>报价管理</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 现代化列表容器 -->
            <form action="" method="post">
                <div class="project-index-list-container project-index-fade-in-delay-3">
                    <div class="project-list-header">
                        <div class="project-list-icon">
                            <i class="fa fa-briefcase"></i>
                        </div>
                        <h3 class="project-list-title">项目列表</h3>
                    </div>
                    <div class="project-list-body">
                        <php>foreach($list as $v) { </php>
                        <div class="project-list-item">
                            <!-- ID显示 -->
                            <div class="project-item-id">
                                #{$v.id}
                            </div>

                            <!-- 内容区域 -->
                            <div class="project-item-content">
                                <!-- 项目名称 -->
                                <h4 class="project-item-title">
                                    {$v.name}
                                </h4>

                                <!-- 元信息 -->
                                <div class="project-item-meta">
                                    <!-- 项目归类 -->
                                    <div class="project-item-category">
                                        <i class="fa fa-folder-open"></i>
                                        <span>{:$categoryList[$v['category']] ?: '未分类'}</span>
                                    </div>

                                    <!-- 状态 -->
                                    <div class="project-item-status">
                                        <span class="project-status-badge label-{:$statusList[$v['status']]['style']}">
                                            <i class="fa fa-circle"></i>
                                            <span>{:$statusList[$v['status']]['text']}</span>
                                        </span>
                                    </div>

                                    <!-- 创建时间 -->
                                    <div class="project-item-time">
                                        <i class="fa fa-clock-o"></i>
                                        <span>{:date("Y-m-d H:i:s", $v['create_time'])}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="project-item-actions">
                                <php>echo ProjectStatBtn($v['id'], $v['status']);</php>
                            </div>
                        </div>
                        <php>}</php>
                    </div>
                </div>

                <!-- 分页信息 -->
                <div class="project-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem;">
                    {$page}
                </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script>
	$(document).ready(function() {
		// 设置导航标签active状态的函数
		function setActiveNavTab() {
			// 移除所有active状态
			$('.project-index-nav-link').removeClass('active');

			// 获取当前URL参数
			var urlParams = new URLSearchParams(window.location.search);
			var status = urlParams.get('status');

			// 根据status参数设置对应的active状态
			if (status === '1') {
				// 已发布状态
				$('.quick-filter[data-filter="published"]').addClass('active');
			} else if (status === '0') {
				// 草稿状态
				$('.quick-filter[data-filter="draft"]').addClass('active');
			} else if (status === '2') {
				// 已禁用状态
				$('.quick-filter[data-filter="disabled"]').addClass('active');
			} else {
				// 默认状态（全部）
				$('.project-index-nav-link').first().addClass('active');
			}
		}

		// 快速筛选功能
		$('.quick-filter').click(function(e) {
			e.preventDefault();
			var $this = $(this);
			var filter = $this.data('filter');
			var currentUrl = window.location.href.split('?')[0];
			var newUrl = currentUrl;

			// 立即更新视觉状态，提供即时反馈
			$('.project-index-nav-link').removeClass('active');
			$this.addClass('active');

			// 添加加载状态
			var originalHtml = $this.html();
			$this.html('<i class="fa fa-spinner fa-spin project-index-nav-icon"></i><span>加载中...</span>');

			// 获取当前的搜索参数
			var urlParams = new URLSearchParams(window.location.search);
			var searchParams = new URLSearchParams();

			// 保留搜索关键词等其他参数
			if (urlParams.get('kw')) {
				searchParams.set('kw', urlParams.get('kw'));
			}
			if (urlParams.get('val')) {
				searchParams.set('val', urlParams.get('val'));
			}

			// 添加状态参数
			switch(filter) {
				case 'published':
					searchParams.set('status', '1');
					break;
				case 'draft':
					searchParams.set('status', '0');
					break;
				case 'disabled':
					searchParams.set('status', '2');
					break;
				default:
					// 不添加status参数，显示全部
					break;
			}

			// 构建最终URL
			var paramString = searchParams.toString();
			if (paramString) {
				newUrl += '?' + paramString;
			}

			// 延迟跳转，让用户看到加载状态
			setTimeout(function() {
				window.location.href = newUrl;
			}, 300);
		});

		// 处理"项目管理"标签的点击事件
		$('.project-index-nav-link').not('.quick-filter').click(function(e) {
			var $this = $(this);
			var href = $this.attr('href');

			// 如果是指向当前页面的链接，处理active状态
			if (href && href.indexOf('Project/index') !== -1) {
				e.preventDefault();

				// 立即更新视觉状态
				$('.project-index-nav-link').removeClass('active');
				$this.addClass('active');

				// 添加加载状态
				var originalHtml = $this.html();
				$this.html('<i class="fa fa-spinner fa-spin project-index-nav-icon"></i><span>加载中...</span>');

				// 延迟跳转到不带参数的页面
				setTimeout(function() {
					window.location.href = href;
				}, 300);
			}
		});

		// 列表项悬停效果增强
		$('.project-list-item').hover(
			function() {
				$(this).addClass('hover-effect');
			},
			function() {
				$(this).removeClass('hover-effect');
			}
		);

		// 项目状态按钮点击事件
		$('.js_status').click(function() {
			var that = $(this),
				url = that.attr('url');

			// 添加加载状态
			var originalText = that.html();
			that.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
			that.prop('disabled', true);

			$.get(url, function(data) {
				window.location.reload();
			}).fail(function() {
				that.html(originalText);
				that.prop('disabled', false);
				layer.msg('操作失败，请重试', {icon: 2});
			});
		});

		// 空状态处理
		var totalItems = $('.project-list-item').length;
		if (totalItems === 0) {
			var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-briefcase"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无项目数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的项目。</p><a href="{:U(\'Project/edit\')}" class="btn btn-primary" style="padding: 0.75rem 1.5rem; font-size: 1.5rem;"><i class="fa fa-plus"></i> 添加项目</a></div>');
			$('.project-index-list-container .project-list-body').append($emptyState);
		}

		// 延迟执行active状态设置，确保DOM完全加载
		setTimeout(function() {
			setActiveNavTab();
		}, 100);
	});

	// 搜索面板切换功能
	function toggleSearchPanel() {
		var panel = document.getElementById('searchPanel');
		if (panel.classList.contains('show')) {
			panel.classList.remove('show');
		} else {
			panel.classList.add('show');
		}
	}


</script>

<include file="block/footer" />
