<?php

namespace Other\Controller;

use Common\Controller\BaseController;
use Common\Controller\WController;
use Common\Controller\WstationController;
use Util\AliSms;
use Util\HwSms;

class IndexController extends BaseController
{
    public function _initialize()
    {

    }

    /**
     * 服务站list
     * @return void
     */
    public function index() {
        $kwd = I('get.kwd', '');
        $city = I('get.city', '');
        $province = I('get.province', '');
        $obj = D("ServiceStation");
        $where = [
            'status' => 1,
            'show' => 1,
            'type' => 1,
        ];
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%'.$kwd.'%'];
        }
        if (!empty($province)) {
            $where['mail_address'] = ['like', '%'.$province.'%'];
        }

        if (!empty($city)) {
            $where['mail_address'] = ['like', '%'.$city.'%'];
        }
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('qualificationList', $obj->qualification);
        $this->assign('sexList', $obj->sex);
        $this->assign('categoryList', D("Project")->category);
        $this->assign('list', $list);
        
        // 设置微信分享信息
        $shareTitle = '中才国科就业服务站';
        $shareDesc = '中才国科全国就业服务站网络，提供专业就业服务与支持';
        $this->assign('shareTitle', $shareTitle);
        $this->assign('shareDesc', $shareDesc);
        
        // 添加微信分享配置
        $conf = [
            'number' => '2',
            'WX_ID' => 'zhongcaiguoke',
            'WX_TOKEN' => 'zcgk123567',
            'WX_APPID' => 'wx6fddd8b81169ea8c',
            'WX_APPSECRET' => 'e9107e262a4b7ab677acd3ab0f072fca',
        ];
        
        // 获取完整的URL，使用https协议
        $protocol = "https://";
        $currentUrl = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        $currentUrl = preg_replace('/#.*$/', '', $currentUrl);
        
        if (!C('LOCAL_DEBUG')) {
            $wxconf = getWxConfig($currentUrl, $conf);
            $this->assign('wxconf', $wxconf);
            $this->assign('appid', $conf['WX_APPID']);
        }
        
        $n = I('get.n', 0);
        if ($page->Current_page > 1 || $n == 1) {
            $this->display('list-index');
            exit;
        }
        $this->assign("page", $page);
        $this->display();
    }

    public function jobcontent() {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $idcode = D("Project")->dehash($id);
        $userJobDocRow = D("UserJobDoc")->where(['id' => $idcode])->find();
        if (!$userJobDocRow) $this->error('当前简历不存在');
        if (empty($userJobDocRow['html_content'])) $this->error('当前简历未分析成功，请稍后再来!!');
        $this->assign('userJobDocRow', $userJobDocRow);
        $this->display();
    }


    /**
     * 岗位内容
     * @return void
     */
    public function postcontent()
    {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $obj = D("Project");
        $row = $obj->where(['id' => $id])->find();
        if (!$row) $this->error('当前岗位无法找到！！！！');
        $projectList = D("Project")->where(['status' => 1])->field('id,name,content')->select();
        
        // 从内容中提取前100个字符作为描述
        if (empty($row['desc'])) {
            $contentText = htmlspecialchars_decode($row['content']); // 先解码HTML实体
            $contentText = strip_tags($contentText); // 移除HTML标签
            $contentText = html_entity_decode($contentText, ENT_QUOTES | ENT_HTML5, 'UTF-8'); // 再次解码所有HTML实体
            $contentText = preg_replace('/\s+/', ' ', $contentText); // 替换多个空白字符为单个空格
            $contentText = preg_replace('/&[a-zA-Z0-9#]+;/', '', $contentText); // 移除任何剩余的HTML实体
            $description = mb_substr($contentText, 0, 100, 'UTF-8'); // 截取前100个字符
            if (mb_strlen($contentText, 'UTF-8') > 100) {
                $description .= '...'; // 如果内容超过100个字符，添加省略号
            }
        } else {
            $description = $row['desc'];
        }
        $this->assign('description', $description);
        
        // 检测用户来源
        $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
        $backUrl = '';
        
        // 判断是否来自station模块
        if (strpos($referer, 'station.') !== false || strpos($referer, '/station/') !== false) {
            // 来自station模块，生成完整的station域名URL
            $backUrl = 'https://station.zhongcaiguoke.com/';
        } else {
            // 来自其他来源，返回other模块的index
            $backUrl = U('Other/Index/index');
        }
        
        $this->assign('backUrl', $backUrl);
        
        // 添加微信分享配置
        $conf = [
            'number' => '2',
            'WX_ID' => 'zhongcaiguoke',
            'WX_TOKEN' => 'zcgk123567',
            'WX_APPID' => 'wx6fddd8b81169ea8c',
            'WX_APPSECRET' => 'e9107e262a4b7ab677acd3ab0f072fca',
        ];
        
        // 获取完整的URL，使用https协议
        $protocol = "https://";
        $currentUrl = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        $currentUrl = preg_replace('/#.*$/', '', $currentUrl);
        
        if (!C('LOCAL_DEBUG')) {
            $wxconf = getWxConfig($currentUrl, $conf);
            $this->assign('wxconf', $wxconf);
            $this->assign('appid', $conf['WX_APPID']);
        }
        
        $this->assign('projectList', $projectList);
        $this->assign('row', $row);
        $this->display();
    }

    /**
     * 中才国科相关媒体报道
     * @return void
     */
    public function mediareport()
    {
        $id = 6; // 媒体报道在z_page_ad表中的ID
        $obj = D("PageAd"); // 使用PageAd模型而不是PageList
        $row = $obj->where(['id' => $id])->find();
        if (!$row) $this->error('内容不存在');
        
        // 从内容中提取前100个字符作为描述
        $contentText = htmlspecialchars_decode($row['content']); // 先解码HTML实体
        $contentText = strip_tags($contentText); // 移除HTML标签
        $contentText = html_entity_decode($contentText, ENT_QUOTES | ENT_HTML5, 'UTF-8'); // 再次解码所有HTML实体
        $contentText = preg_replace('/\s+/', ' ', $contentText); // 替换多个空白字符为单个空格
        $contentText = preg_replace('/&[a-zA-Z0-9#]+;/', '', $contentText); // 移除任何剩余的HTML实体
        $description = mb_substr($contentText, 0, 100, 'UTF-8'); // 截取前100个字符
        if (mb_strlen($contentText, 'UTF-8') > 100) {
            $description .= '...'; // 如果内容超过100个字符，添加省略号
        }
        $this->assign('description', $description);
        
        // 添加微信分享配置
        $conf = [
            'number' => '2',
            'WX_ID' => 'zhongcaiguoke',
            'WX_TOKEN' => 'zcgk123567',
            'WX_APPID' => 'wx6fddd8b81169ea8c',
            'WX_APPSECRET' => 'e9107e262a4b7ab677acd3ab0f072fca',
        ];
        
        // 获取完整的URL，使用https协议
        $protocol = "https://";
        $currentUrl = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        $currentUrl = preg_replace('/#.*$/', '', $currentUrl);
        
        if (!C('LOCAL_DEBUG')) {
            $wxconf = getWxConfig($currentUrl, $conf);
            $this->assign('wxconf', $wxconf);
            $this->assign('appid', $conf['WX_APPID']);
        }
        
        $this->assign('row', $row);
        $this->display();
    }

    /**
     * 微信好友二维码
     * @return void
     */
    public function wechatFriend()
    {
        // 获取用户唯一标识，优先使用session
        $qrcodeId = session('wechat_friend_qrcode_id');
        
        // session中没有，则随机获取一个并保存到session
        if (!$qrcodeId) {
            $qrcodeRow = D("WechatQrcode")->getRandomQrcode();
            if ($qrcodeRow) {
                $qrcodeId = $qrcodeRow['id'];
                session('wechat_friend_qrcode_id', $qrcodeId);
            }
        } else {
            // 从session中获取id后再查询完整信息
            $qrcodeRow = D("WechatQrcode")->getQrcode($qrcodeId);
            
            // 如果记录不存在或已禁用，则重新随机选择
            if (!$qrcodeRow) {
                $qrcodeRow = D("WechatQrcode")->getRandomQrcode();
                if ($qrcodeRow) {
                    $qrcodeId = $qrcodeRow['id'];
                    session('wechat_friend_qrcode_id', $qrcodeId);
                }
            }
        }
        
        // 如果没有找到有效的二维码记录，显示提示信息
        if (!$qrcodeRow) {
            $this->error('暂无可用的微信好友二维码');
        }
        
        // 添加微信分享配置
        $conf = [
            'number' => '2',
            'WX_ID' => 'zhongcaiguoke',
            'WX_TOKEN' => 'zcgk123567',
            'WX_APPID' => 'wx6fddd8b81169ea8c',
            'WX_APPSECRET' => 'e9107e262a4b7ab677acd3ab0f072fca',
        ];
        
        // 获取完整的URL，使用https协议
        $protocol = "https://";
        $currentUrl = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        $currentUrl = preg_replace('/#.*$/', '', $currentUrl);
        
        if (!C('LOCAL_DEBUG')) {
            $wxconf = getWxConfig($currentUrl, $conf);
            $this->assign('wxconf', $wxconf);
            $this->assign('appid', $conf['WX_APPID']);
        }
        
        // 设置分享信息
        $shareTitle = '扫码添加微信好友';
        $shareDesc = $qrcodeRow['tip_text'] ? $qrcodeRow['tip_text'] : '扫描二维码，立即添加微信好友';
        
        // 获取完整的图片URL
        $protocol = "https://";
        $shareImgUrl = 'https://c.zhongcaiguoke.com/static/stations/images/logozcgk.png';
        
        $this->assign('shareTitle', $shareTitle);
        $this->assign('shareDesc', $shareDesc);
        $this->assign('shareImgUrl', $shareImgUrl);
        
        // 传递二维码数据到视图
        $this->assign('qrcode', $qrcodeRow);
        $this->display();
    }
}