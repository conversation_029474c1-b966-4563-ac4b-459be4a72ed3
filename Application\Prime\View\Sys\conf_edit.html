<include file="block/hat" />
<script type="text/javascript" src="/static/js/lib/jquery-ui-1.10.3.min.js"></script>
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<ul class="nav nav-tabs">
				<li class="active"><a href="">编辑设置</a></li>
			</ul>
			<style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
<div class="main">
	<form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
		<div class="panel panel-default">
			<div class="panel-heading">编辑设置</div>
			<div class="panel-body">
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>配置名</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="title" class="form-control" value="{$row.title}" />
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>Key</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="name" class="form-control" value="{$row.name}" />
					</div>
				</div>
				<php>if($row['id'] == 7) {</php>
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style='color:red'>*</span>商品图片</label>
                    <div class="col-sm-9 col-xs-12">
                        <php>echo tpl_form_field_image('value', $row['value'], '', ['type'=>4,'extras' => ['text' => 'readonly']])</php>
                        <span class="help-block">链接地址的二维码将嵌入此图片中</span>
                    </div>
                </div>
				<php>} else {</php>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>值</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="value" class="form-control" value="{$row.value}" />
					</div>
				</div>
				<php>} </php>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>描述</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="desc" class="form-control" value="{$row.desc}"  />
					</div>
				</div>
			</div>
		</div>
		<div class="form-group col-sm-12">
			<input type="submit" value="提交" class="btn btn-primary col-lg-1" />
		</div>
	</form>
</div>


		</div>
	</div>
</div>

<script type="text/javascript">
$(function() {
	$('#preview').click(function() {
		var img = $("#form1 input[name=img]");
		var url = $("#form1 input[name=url]");
		if (!img.val() || !url.val()) {
			alert('请先设置好图片和网址');
			return false;
		}
	});
})
</script>
<include file="block/footer" />