<include file='block/header' />
<link rel="stylesheet" href="/static/css/modern-admin.css">
<body>
<style>
	/* 现代化登录页面样式 */
	.modern-login {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		padding: var(--spacing-xl);
		position: relative;
		overflow: hidden;
	}

	.modern-login::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
		opacity: 0.3;
	}

	.login-container {
		background: white;
		border-radius: var(--radius-xl);
		box-shadow: var(--shadow-xl);
		overflow: hidden;
		width: 100%;
		max-width: 450px;
		position: relative;
		z-index: 1;
		animation: slideInUp 0.6s ease-out;
	}

	@keyframes slideInUp {
		from {
			opacity: 0;
			transform: translateY(30px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.login-header {
		background: var(--primary-gradient);
		color: white;
		text-align: center;
		padding: var(--spacing-2xl) var(--spacing-xl);
		position: relative;
	}

	.login-header::after {
		content: '';
		position: absolute;
		bottom: -10px;
		left: 50%;
		transform: translateX(-50%);
		width: 0;
		height: 0;
		border-left: 10px solid transparent;
		border-right: 10px solid transparent;
		border-top: 10px solid var(--primary-color);
	}

	.login-logo {
		font-size: 2.5rem;
		font-weight: 700;
		margin-bottom: var(--spacing-sm);
		text-shadow: 0 2px 4px rgba(0,0,0,0.2);
	}

	.login-subtitle {
		font-size: 1.6rem;
		opacity: 0.9;
		font-weight: 400;
	}

	.login-body {
		padding: var(--spacing-2xl);
	}

	.login-form {
		display: flex;
		flex-direction: column;
		gap: var(--spacing-lg);
	}

	.form-field {
		position: relative;
	}

	.form-field-icon {
		position: absolute;
		left: var(--spacing-md);
		top: 50%;
		transform: translateY(-50%);
		color: var(--gray-400);
		font-size: 1.5rem;
		z-index: 2;
	}

	.form-field input {
		width: 100%;
		padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-lg) 3rem;
		border: 2px solid var(--gray-200);
		border-radius: var(--radius-lg);
		font-size: 1.5rem;
		transition: var(--transition-normal);
		background: var(--gray-50);
	}

	.form-field input:focus {
		outline: none;
		border-color: var(--primary-color);
		background: white;
		box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
	}

	.form-field input:focus + .form-field-icon {
		color: var(--primary-color);
	}

	.verify-section {
		display: flex;
		gap: var(--spacing-md);
		align-items: center;
	}

	.verify-input {
		flex: 1;
	}

	.verify-code {
		border: 2px solid var(--gray-200);
		border-radius: var(--radius-lg);
		overflow: hidden;
		cursor: pointer;
		transition: var(--transition-normal);
	}

	.verify-code:hover {
		border-color: var(--primary-color);
		transform: scale(1.05);
	}

	.verify-code img {
		display: block;
		width: 120px;
		height: 50px;
	}

	.login-button {
		background: var(--primary-gradient);
		color: white;
		border: none;
		padding: var(--spacing-lg);
		border-radius: var(--radius-lg);
		font-size: 1.5rem;
		font-weight: 600;
		cursor: pointer;
		transition: var(--transition-normal);
		position: relative;
		overflow: hidden;
		margin-top: var(--spacing-md);
	}

	.login-button:before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
		transition: var(--transition-normal);
	}

	.login-button:hover {
		transform: translateY(-2px);
		box-shadow: var(--shadow-lg);
	}

	.login-button:hover:before {
		left: 100%;
	}

	.login-button:active {
		transform: translateY(0);
	}

	.login-footer {
		text-align: center;
		padding: var(--spacing-lg);
		background: var(--gray-50);
		color: var(--gray-600);
		font-size: 1.2rem;
	}

	.floating-shapes {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
		overflow: hidden;
	}

	.shape {
		position: absolute;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
		animation: float 6s ease-in-out infinite;
	}

	.shape:nth-child(1) {
		width: 80px;
		height: 80px;
		top: 20%;
		left: 10%;
		animation-delay: 0s;
	}

	.shape:nth-child(2) {
		width: 60px;
		height: 60px;
		top: 60%;
		right: 10%;
		animation-delay: 2s;
	}

	.shape:nth-child(3) {
		width: 40px;
		height: 40px;
		bottom: 20%;
		left: 20%;
		animation-delay: 4s;
	}

	@keyframes float {
		0%, 100% { transform: translateY(0px) rotate(0deg); }
		50% { transform: translateY(-20px) rotate(180deg); }
	}

	@media (max-width: 768px) {
		.modern-login {
			padding: var(--spacing-lg);
		}

		.login-container {
			max-width: 100%;
		}

		.login-header {
			padding: var(--spacing-xl);
		}

		.login-body {
			padding: var(--spacing-xl);
		}

		.login-logo {
			font-size: 2rem;
		}
	}
</style>

<div class="modern-login">
	<!-- 浮动装饰元素 -->
	<div class="floating-shapes">
		<div class="shape"></div>
		<div class="shape"></div>
		<div class="shape"></div>
	</div>

	<div class="login-container">
		<!-- 登录头部 -->
		<div class="login-header">
			<div class="login-logo">
				<i class="fa fa-cube"></i>
				中才国科
			</div>
			<div class="login-subtitle">管理后台登录</div>
		</div>

		<!-- 登录表单 -->
		<div class="login-body">
			<form action="" method="post" class="login-form">
				<div class="form-field">
					<i class="fa fa-user form-field-icon"></i>
					<input name="username" type="text" placeholder="请输入用户名" required>
				</div>

				<div class="form-field">
					<i class="fa fa-lock form-field-icon"></i>
					<input name="pwd" type="password" placeholder="请输入密码" required>
				</div>

				<div class="verify-section">
					<div class="form-field verify-input">
						<i class="fa fa-shield form-field-icon"></i>
						<input name="verifycode" type="text" placeholder="验证码" required>
					</div>
					<div class="verify-code">
						<img src="{:U('public/verifycode')}?{:mt_rand()}"
							 onclick="this.src='{:U('public/verifycode')}?'+Math.random();"
							 alt="验证码"
							 title="点击刷新验证码">
					</div>
				</div>

				<button type="submit" name="submit" class="login-button">
					<i class="fa fa-sign-in"></i>
					登录系统
				</button>

				<input name="token" value="" type="hidden" />
			</form>
		</div>

		<!-- 登录页脚 -->
		<div class="login-footer">
			<p>© {:date('Y')} 中才国科 - 管理后台系统</p>
		</div>
	</div>
</div>

<script>
require(['jquery'], function($) {
	// 自适应高度
	var h = document.documentElement.clientHeight;
	$(".modern-login").css('min-height', h);

	// 表单验证和提交效果
	$('.login-form').on('submit', function(e) {
		var $btn = $('.login-button');
		var originalText = $btn.html();

		$btn.html('<i class="fa fa-spinner fa-spin"></i> 登录中...');
		$btn.prop('disabled', true);

		// 如果表单验证失败，恢复按钮状态
		setTimeout(function() {
			if (!$('.login-form')[0].checkValidity()) {
				$btn.html(originalText);
				$btn.prop('disabled', false);
			}
		}, 100);
	});

	// 输入框焦点效果
	$('.form-field input').on('focus', function() {
		$(this).closest('.form-field').addClass('focused');
	}).on('blur', function() {
		$(this).closest('.form-field').removeClass('focused');
	});
});
</script>
</body>
</html>
