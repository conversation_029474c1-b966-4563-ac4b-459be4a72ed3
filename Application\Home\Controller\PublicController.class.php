<?php

namespace Home\Controller;

use Common\Controller\WController;
use \LaneWeChat\Core as WE;

class PublicController extends WController
{
    public function _initialize() {

    }

    /**
     * 获取上架的岗位api
     */
    public function getprojectpost() {
        $projectList = D("ProjectPost")->where(['status' => 1])->select();
        $returnData = [];
        $qualificationList = D("ProjectPost")->qualification;
        $sexList = D("ProjectPost")->sex;
        $projectRowList = [];
        if ($projectList) {
            $projectArrId = array_unique(array_column($projectList, 'project_id'));
            if ($projectArrId) {
                $projectRowList = D("Project")->where(['id' => ['in', $projectArrId]])->getField('id,name', true);
            }
        }
        foreach ($projectList as $projectRow) {
            $projectName = $projectRowList[$projectRow['project_id']];
            $height = $projectRow['height'] ? : '无要求' ;
            $returnData[] = $projectName.$projectRow['job_name'].'（要求：学历：'.$qualificationList[$projectRow['qualification']]['text'].'，专业：'.$projectRow['major'].'，年龄：'.$projectRow['min_age'].'-'.$projectRow['max_age'].'，性别：'.$sexList[$projectRow['sex']]['text'].'） 身高：'.$height.';';
        }
        echo join(PHP_EOL, $returnData);die;
    }
}