<include file="block/hat" />
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化银行卡管理页面样式 */
                .bankcard-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .bankcard-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .bankcard-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .bankcard-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .bankcard-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .bankcard-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .bankcard-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .bankcard-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .bankcard-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .bankcard-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .bankcard-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .bankcard-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                    text-decoration: none;
                }

                .bankcard-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .bankcard-index-add-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
                    text-decoration: none;
                }

                .bankcard-index-add-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .bankcard-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .bankcard-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .bankcard-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .bankcard-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .bankcard-index-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .bankcard-index-nav-link.active {
                    color: #667eea !important;
                    background: white !important;
                    border-bottom-color: #667eea !important;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1) !important;
                    font-weight: 700 !important;
                }

                .bankcard-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .bankcard-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 搜索面板 - 默认隐藏 */
                .bankcard-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    display: none;
                    animation: slideDown 0.3s ease-out;
                }

                .bankcard-index-search-panel.show {
                    display: block;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .bankcard-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .bankcard-index-search-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .bankcard-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .bankcard-index-search-close {
                    background: none;
                    border: none;
                    color: #718096;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                }

                .bankcard-index-search-close:hover {
                    background: #f1f5f9;
                    color: #374151;
                }

                .bankcard-index-search-body {
                    padding: 2rem;
                }

                /* 现代化银行卡卡片 */
                .bankcard-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .bankcard-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .bankcard-card-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .bankcard-card-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .bankcard-card-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin: 0;
                }

                .bankcard-card-id {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .bankcard-card-bank-name {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .bankcard-card-service-name {
                    font-size: 1.25rem;
                    color: rgba(255, 255, 255, 0.9);
                    margin: 0.25rem 0 0 0;
                    font-weight: 400;
                }

                .bankcard-card-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    flex-direction: column;
                }

                .bankcard-status-badge {
                    padding: 0.5rem 1rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    margin-bottom: 0.5rem;
                }

                .bankcard-status-badge.status-active {
                    background: rgba(16, 185, 129, 0.9);
                    border-color: rgba(16, 185, 129, 0.5);
                }

                .bankcard-status-badge.status-disabled {
                    background: rgba(239, 68, 68, 0.9);
                    border-color: rgba(239, 68, 68, 0.5);
                }

                .bankcard-default-badge {
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1rem;
                    font-weight: 600;
                    background: rgba(251, 191, 36, 0.9);
                    color: white;
                    border: 1px solid rgba(251, 191, 36, 0.5);
                }

                .bankcard-card-body {
                    padding: 2rem;
                }

                .bankcard-info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                .bankcard-info-section {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    border: 1px solid #e2e8f0;
                }

                .bankcard-info-section-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1rem 0;
                }

                .bankcard-info-section-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .bankcard-info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                }

                .bankcard-info-item:last-child {
                    margin-bottom: 0;
                }

                .bankcard-info-label {
                    color: #6b7280;
                    font-weight: 500;
                    min-width: 80px;
                    flex-shrink: 0;
                }

                .bankcard-info-value {
                    color: #374151;
                    font-weight: 600;
                    flex: 1;
                    word-break: break-all;
                }

                .bankcard-card-number {
                    font-family: 'Courier New', monospace;
                    font-size: 1.75rem;
                    letter-spacing: 0.1em;
                    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
                    border: 1px solid #0ea5e9;
                    border-radius: 0.5rem;
                    padding: 0.75rem 1rem;
                    text-align: center;
                    color: #0c4a6e;
                    font-weight: 700;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .bankcard-card-number:hover {
                    background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
                    transform: scale(1.02);
                }

                /* 操作按钮区域 */
                .bankcard-actions {
                    display: flex;
                    gap: 0.75rem;
                    margin-top: 2rem;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                    flex-wrap: wrap;
                }

                .bankcard-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .bankcard-action-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .bankcard-action-btn.btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .bankcard-action-btn.btn-primary:hover {
                    color: white;
                }

                .bankcard-action-btn.btn-success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                }

                .bankcard-action-btn.btn-success:hover {
                    color: white;
                }

                .bankcard-action-btn.btn-warning {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    color: white;
                }

                .bankcard-action-btn.btn-warning:hover {
                    color: white;
                }

                .bankcard-action-btn.btn-danger {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    color: white;
                }

                .bankcard-action-btn.btn-danger:hover {
                    color: white;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .bankcard-index-container {
                        padding: 1.5rem;
                    }

                    .bankcard-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .bankcard-info-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .bankcard-index-container {
                        padding: 1rem;
                    }

                    .bankcard-index-nav-tabs {
                        flex-direction: column;
                    }

                    .bankcard-index-nav-item {
                        flex: none;
                    }

                    .bankcard-index-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .bankcard-index-nav-link.active {
                        border-bottom: none !important;
                        border-right-color: #667eea !important;
                    }

                    .bankcard-index-title-main {
                        font-size: 1.75rem;
                    }

                    .bankcard-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .bankcard-actions {
                        flex-direction: column;
                    }

                    .bankcard-card-header {
                        flex-direction: column;
                        gap: 1rem;
                        text-align: center;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .bankcard-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .bankcard-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .bankcard-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .bankcard-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="bankcard-index-wrapper">
                <div class="bankcard-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="bankcard-index-header bankcard-index-fade-in">
                        <div class="bankcard-index-header-content">
                            <div class="bankcard-index-title">
                                <div class="bankcard-index-title-icon">
                                    <i class="fa fa-credit-card"></i>
                                </div>
                                <div class="bankcard-index-title-text">
                                    <h1 class="bankcard-index-title-main">银行卡管理</h1>
                                    <p class="bankcard-index-title-sub">Bank Card Management</p>
                                </div>
                            </div>
                            <div class="bankcard-index-actions">
                                <button type="button" class="bankcard-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>
                                <a href="{:U('BankCard/add')}" class="bankcard-index-add-btn">
                                    <i class="fa fa-plus"></i>
                                    <span>添加银行卡</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="bankcard-index-nav-container bankcard-index-fade-in-delay-1">
                        <ul class="bankcard-index-nav-tabs">
                            <li class="bankcard-index-nav-item">
                                <a href="{:U('BankCard/index')}" class="bankcard-index-nav-link active">
                                    <i class="fa fa-list bankcard-index-nav-icon"></i>
                                    <span>银行卡列表</span>
                                </a>
                            </li>
                            <li class="bankcard-index-nav-item">
                                <a href="javascript:void(0)" class="bankcard-index-nav-link quick-filter" data-filter="active">
                                    <i class="fa fa-check-circle bankcard-index-nav-icon"></i>
                                    <span>启用状态</span>
                                </a>
                            </li>
                            <li class="bankcard-index-nav-item">
                                <a href="javascript:void(0)" class="bankcard-index-nav-link quick-filter" data-filter="disabled">
                                    <i class="fa fa-times-circle bankcard-index-nav-icon"></i>
                                    <span>禁用状态</span>
                                </a>
                            </li>
                            <li class="bankcard-index-nav-item">
                                <a href="javascript:void(0)" class="bankcard-index-nav-link quick-filter" data-filter="default">
                                    <i class="fa fa-star bankcard-index-nav-icon"></i>
                                    <span>默认银行卡</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 搜索面板 - 默认隐藏 -->
                    <div class="bankcard-index-search-panel" id="searchPanel">
                        <div class="bankcard-index-search-header">
                            <div class="bankcard-index-search-title">
                                <div class="bankcard-index-search-icon">
                                    <i class="fa fa-search"></i>
                                </div>
                                <span>搜索筛选</span>
                            </div>
                            <button type="button" class="bankcard-index-search-close" onclick="toggleSearchPanel()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                        <div class="bankcard-index-search-body">
                            <form method="get" action="{:U('BankCard/index')}" class="search-form">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">银行名称：</label>
                                            <input type="text" name="bank_name" class="form-control" value="{:I('get.bank_name')}" placeholder="请输入银行名称" />
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">卡号：</label>
                                            <input type="text" name="card_number" class="form-control" value="{:I('get.card_number')}" placeholder="请输入银行卡号" />
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">状态：</label>
                                            <select name="status" class="form-control">
                                                <option value="">全部状态</option>
                                                <option value="1" <eq name="Think.get.status" value="1">selected</eq>>启用</option>
                                                <option value="0" <eq name="Think.get.status" value="0">selected</eq>>禁用</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">服务站：</label>
                                            <input type="text" name="service_station" class="form-control" value="{:I('get.service_station')}" placeholder="请输入服务站名称" />
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-top: 15px;">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-search"></i> 搜索
                                            </button>
                                            <a href="{:U('BankCard/index')}" class="btn btn-default">
                                                <i class="fa fa-refresh"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 银行卡列表 -->
                    <div class="bankcard-index-fade-in-delay-2">
                        <foreach name="list" item="vo">
                        <div class="bankcard-card">
                            <!-- 卡片头部 -->
                            <div class="bankcard-card-header">
                                <div class="bankcard-card-title">
                                    <span class="bankcard-card-id">#{$vo.id}</span>
                                    <div>
                                        <h3 class="bankcard-card-bank-name">{$vo.bank_name}</h3>
                                        <p class="bankcard-card-service-name">{$vo.service_name|default='未绑定服务站'} (ID: {$vo.service_station_id})</p>
                                    </div>
                                </div>
                                <div class="bankcard-card-status">
                                    <if condition="$vo['status'] eq 1">
                                        <span class="bankcard-status-badge status-active">
                                            <i class="fa fa-check-circle"></i>
                                            {$statusList[$vo['status']]['text']|default='启用'}
                                        </span>
                                    <else />
                                        <span class="bankcard-status-badge status-disabled">
                                            <i class="fa fa-times-circle"></i>
                                            {$statusList[$vo['status']]['text']|default='禁用'}
                                        </span>
                                    </if>
                                    <if condition="$vo['is_default'] eq 1">
                                        <span class="bankcard-default-badge">
                                            <i class="fa fa-star"></i>
                                            默认银行卡
                                        </span>
                                    </if>
                                </div>
                            </div>

                            <!-- 卡片主体 -->
                            <div class="bankcard-card-body">
                                <!-- 银行卡号显示 -->
                                <div class="bankcard-card-number" onclick="toggleCardNumber(this)" data-original="{$vo.card_number_masked}" title="点击查看完整卡号">
                                    {$vo.card_number_masked}
                                </div>

                                <div class="bankcard-info-grid">
                                    <!-- 基本信息 -->
                                    <div class="bankcard-info-section">
                                        <h4 class="bankcard-info-section-title">
                                            <div class="bankcard-info-section-icon">
                                                <i class="fa fa-user"></i>
                                            </div>
                                            基本信息
                                        </h4>
                                        <div class="bankcard-info-item">
                                            <span class="bankcard-info-label">开户人：</span>
                                            <span class="bankcard-info-value">{$vo.account_holder}</span>
                                        </div>
                                        <div class="bankcard-info-item">
                                            <span class="bankcard-info-label">开户支行：</span>
                                            <span class="bankcard-info-value">{$vo.bank_branch|default='-'}</span>
                                        </div>
                                        <div class="bankcard-info-item">
                                            <span class="bankcard-info-label">添加人：</span>
                                            <span class="bankcard-info-value">{$vo.added_by_admin_name|default='-'}</span>
                                        </div>
                                        <div class="bankcard-info-item">
                                            <span class="bankcard-info-label">添加时间：</span>
                                            <span class="bankcard-info-value">{:date('Y-m-d H:i', $vo['created_at'])}</span>
                                        </div>
                                    </div>

                                    <!-- 状态信息 -->
                                    <div class="bankcard-info-section">
                                        <h4 class="bankcard-info-section-title">
                                            <div class="bankcard-info-section-icon">
                                                <i class="fa fa-info-circle"></i>
                                            </div>
                                            状态信息
                                        </h4>
                                        <div class="bankcard-info-item">
                                            <span class="bankcard-info-label">使用状态：</span>
                                            <span class="bankcard-info-value">
                                                <if condition="$vo['status'] eq 1">
                                                    <span style="color: #10b981; font-weight: 600;">
                                                        <i class="fa fa-check-circle"></i> {$statusList[$vo['status']]['text']|default='启用'}
                                                    </span>
                                                <else />
                                                    <span style="color: #ef4444; font-weight: 600;">
                                                        <i class="fa fa-times-circle"></i> {$statusList[$vo['status']]['text']|default='禁用'}
                                                    </span>
                                                </if>
                                            </span>
                                        </div>
                                        <div class="bankcard-info-item">
                                            <span class="bankcard-info-label">默认状态：</span>
                                            <span class="bankcard-info-value">
                                                <if condition="$vo['is_default'] eq 1">
                                                    <span style="color: #f59e0b; font-weight: 600;">
                                                        <i class="fa fa-star"></i> 默认银行卡
                                                    </span>
                                                <else />
                                                    <span style="color: #6b7280;">
                                                        <i class="fa fa-circle-o"></i> 普通银行卡
                                                    </span>
                                                </if>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="bankcard-actions">
                                    <a href="{:U('BankCard/edit',array('id'=>$vo['id']))}" class="bankcard-action-btn btn-primary">
                                        <i class="fa fa-edit"></i>
                                        <span>编辑</span>
                                    </a>

                                    <if condition="$vo['status'] eq 1">
                                        <a href="{:U('BankCard/enable',array('id'=>$vo['id'], 'status'=>0))}" class="bankcard-action-btn btn-warning js-ajax-dialog-btn" data-msg="确定要禁用该银行卡吗？">
                                            <i class="fa fa-pause"></i>
                                            <span>禁用</span>
                                        </a>
                                    <else />
                                        <a href="{:U('BankCard/enable',array('id'=>$vo['id']))}" class="bankcard-action-btn btn-success js-ajax-dialog-btn" data-msg="确定要启用该银行卡吗？">
                                            <i class="fa fa-play"></i>
                                            <span>启用</span>
                                        </a>
                                    </if>

                                    <a href="javascript:void(0);" onclick="if(confirm('警告：您确定要删除该银行卡吗？删除后数据将无法恢复！')) window.location.href='{:U('BankCard/delete',array('id'=>$vo['id']))}';" class="bankcard-action-btn btn-danger">
                                        <i class="fa fa-trash"></i>
                                        <span>删除</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                        </foreach>

                        <if condition="empty($list)">
                        <div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;">
                            <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;">
                                <i class="fa fa-credit-card"></i>
                            </div>
                            <h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无银行卡</h3>
                            <p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的银行卡。</p>
                            <a href="{:U('BankCard/add')}" class="btn btn-primary" style="padding: 0.75rem 1.5rem; font-size: 1.5rem;">
                                <i class="fa fa-plus"></i> 添加银行卡
                            </a>
                        </div>
                        </if>

                        <!-- 分页 -->
                        <div class="bankcard-index-fade-in-delay-3" style="text-align: center; margin-top: 30px;">
                            {$page}
                        </div>
                    </div>
                </div>
            </div>
            </div>
            <!-- Main content end -->
        </div>
	</div>
</div>

<include file="block/footer" />

<script>
    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
            setTimeout(function() {
                panel.style.display = 'none';
            }, 300);
        } else {
            panel.style.display = 'block';
            setTimeout(function() {
                panel.classList.add('show');
            }, 10);
        }
    }

    // 银行卡号显示切换功能
    function toggleCardNumber(element) {
        var originalNumber = element.getAttribute('data-original');
        var currentText = element.textContent.trim();

        if (currentText.includes('****')) {
            // 显示完整卡号（如果有的话）
            element.textContent = originalNumber;
            element.setAttribute('title', '点击隐藏卡号');
        } else {
            // 隐藏卡号
            if (originalNumber.length >= 8) {
                var maskedNumber = originalNumber.substring(0, 4) + ' **** **** ' + originalNumber.substring(originalNumber.length - 4);
                element.textContent = maskedNumber;
            }
            element.setAttribute('title', '点击查看完整卡号');
        }
    }

    $(document).ready(function() {
        // 根据当前URL参数设置active状态
        function setActiveNavTab() {
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');
            var isDefault = urlParams.get('is_default');

            // 移除所有active类
            $('.bankcard-index-nav-link').removeClass('active');

            // 根据URL参数设置对应的active类
            if (status === '1') {
                // 启用状态筛选
                $('.quick-filter[data-filter="active"]').addClass('active');
            } else if (status === '0') {
                // 禁用状态筛选
                $('.quick-filter[data-filter="disabled"]').addClass('active');
            } else if (isDefault === '1') {
                // 默认银行卡筛选
                $('.quick-filter[data-filter="default"]').addClass('active');
            } else {
                // 默认全部银行卡
                $('.bankcard-index-nav-link').first().addClass('active');
            }
        }

        // 页面加载时设置active状态
        setActiveNavTab();

        // 调试信息 - 显示当前URL参数
        var urlParams = new URLSearchParams(window.location.search);
        console.log('当前URL参数:', Object.fromEntries(urlParams));
        console.log('当前URL:', window.location.href);

        // 快速筛选功能
        $('.quick-filter').click(function(e) {
            e.preventDefault();
            var filter = $(this).data('filter');
            var currentUrl = window.location.href.split('?')[0];
            var newUrl = currentUrl + '?';

            // 移除当前active类并添加到点击的元素
            $('.bankcard-index-nav-link').removeClass('active');
            $(this).addClass('active');

            switch(filter) {
                case 'active':
                    newUrl += 'status=1'; // 启用状态
                    break;
                case 'disabled':
                    newUrl += 'status=0'; // 禁用状态
                    break;
                case 'default':
                    newUrl += 'is_default=1'; // 默认银行卡
                    break;
                default:
                    newUrl = currentUrl;
            }

            window.location.href = newUrl;
        });

        // "银行卡列表"链接点击处理
        $('.bankcard-index-nav-link').first().click(function(e) {
            if ($(this).attr('href') !== 'javascript:void(0)') {
                $('.bankcard-index-nav-link').removeClass('active');
                $(this).addClass('active');
            }
        });

        // 卡片悬停效果增强
        $('.bankcard-card').hover(
            function() {
                $(this).find('.bankcard-card-header').css('transform', 'scale(1.02)');
            },
            function() {
                $(this).find('.bankcard-card-header').css('transform', 'scale(1)');
            }
        );

        // 搜索表单增强
        $('.search-form').on('submit', function() {
            var $submitBtn = $(this).find('button[type="submit"]');
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 搜索中...');

            setTimeout(function() {
                $submitBtn.prop('disabled', false).html(originalText);
            }, 3000);
        });

        // 滚动到顶部功能
        var $scrollToTop = $('<button class="scroll-to-top" style="position: fixed; bottom: 2rem; right: 2rem; width: 3rem; height: 3rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 50%; font-size: 1.25rem; cursor: pointer; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; display: none; transition: all 0.3s ease;"><i class="fa fa-arrow-up"></i></button>');
        $('body').append($scrollToTop);

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $scrollToTop.fadeIn();
            } else {
                $scrollToTop.fadeOut();
            }
        });

        $scrollToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 600);
        });

        // 添加加载动画
        $('.bankcard-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });

        // 搜索结果统计
        var totalCards = $('.bankcard-card').length;
        if (totalCards > 0) {
            var $statsInfo = $('<div class="search-stats" style="background: white; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; color: #6b7280; font-size: 1.25rem;"><i class="fa fa-info-circle"></i> 共找到 ' + totalCards + ' 张银行卡</div>');
            $('.bankcard-card').first().before($statsInfo);
        }

        // 状态统计
        var statusCounts = {};
        $('.bankcard-status-badge').each(function() {
            var status = $(this).text().trim();
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });

        console.log('状态统计:', statusCounts);

        // 延迟执行active状态设置，确保DOM完全加载
        setTimeout(function() {
            setActiveNavTab();
        }, 100);
    });

    // 添加自定义样式
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .search-stats {
                animation: fadeInUp 0.6s ease-out;
            }

            .empty-state {
                animation: fadeInUp 0.6s ease-out;
            }

            .bankcard-card {
                animation: fadeInUp 0.6s ease-out both;
            }

            .scroll-to-top:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 15px rgba(0,0,0,0.2);
            }

            .bankcard-action-btn:not(.qrcode) {
                margin-right: 0.5rem;
                margin-bottom: 0.5rem;
            }

            .bankcard-card-header {
                transition: all 0.3s ease;
            }

            /* 增强active状态的视觉效果 */
            .bankcard-index-nav-link.active {
                background: white !important;
                color: #667eea !important;
                border-bottom-color: #667eea !important;
                box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1) !important;
                font-weight: 700 !important;
            }

            .bankcard-index-nav-link.active .bankcard-index-nav-icon {
                color: #667eea !important;
            }

            /* 确保active状态在hover时保持 */
            .bankcard-index-nav-link.active:hover {
                background: white !important;
                color: #667eea !important;
            }
        `)
        .appendTo('head');
</script>