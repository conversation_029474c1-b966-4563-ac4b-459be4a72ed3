<include file="block/hat" />
<style>
    img {
  width: 80px;
}
</style>
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Message/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="panel panel-info">
                    <div class="panel-body">
                        <form method="get" class="form-inline" role="form" id="">
                            <div class="form-group">
                                <select class="form-control" name="kw">
                                    <php>foreach($c_kw as $key=>$value){</php>
                                    <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                    <php>}</php>
                                </select>=<input class="form-control" type="text" name="val" value="{$_get.val}" />
                            </div>
                            <div class="form-group">
                                <select name="type" class='form-control'>
                                    <option value="">类型</option>
                                    <php> foreach($typeList as  $k => $v) { </php>
                                    <option value="{$k}" <php>if(is_numeric($_get['type']) && $_get['type']==$k) echo 'selected';</php>>{$v.text}</option>
                                    <php> } </php>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> 搜索</button>
                            <a href="{:U('message/index')}" class="btn btn-warning" >重置</a>
                        </form>
                    </div>
                </div>
                <form action="" method="post" >
                    <div class="panel panel-default">
                        <div class="panel-body table-responsive">
                            <table class="table table-hover">
                                <thead class="navbar-inner">
                                <tr>
                                    <th width="75px">#ID</th>
                                    <th>服务站</th>
                                    <th>简历人</th>
                                    <th>类型</th>
                                    <th>留言内容</th>
                                    <th>留言时间</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <php>foreach($list as $v) { </php>
                                <tr>
                                    <td>{$v.id}</td>
                                    <td>{:$v['service_station_id']}<br>{$serviceStationList[$v['service_station_id']]}</td>
                                    <td>{:$v['user_job_id']}<br>{$jobList[$v['user_job_id']]}</td>
                                    <td>{$typeList[$v['type']]['text']}</td>
                                    <td>{:htmlspecialchars_decode($v['content'])}</td>
                                    <td>{:date('Y-m-d H:i:s', $v['create_time'])}</td>
                                    <td>
                                        <a href="{:U('message/edit', ['id' => $v['id']])}" class="btn btn-primary">编辑</a>
                                        <a href="{:U('message/reply', ['user_job_id' => $v['user_job_id'], 'service_station_id' => $v['service_station_id']])}" class="btn btn-success">添加回复</a>
                                    </td>
                                </tr>
                                <php>}</php>
                                </tbody>
                            </table>
                            {$page}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />