<include file="block/hat" />
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<ul class="nav nav-tabs">
				<li class="active"><a>用户详情</a></li>
			</ul>
			<style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
<div class="main">
	<form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
	<div class="panel panel-default">
		<div class="panel-heading">会员详情</div>
		<div class="panel-body">
			<div class="form-group">
				<label class="col-xs-12 col-sm-3 col-md-2 control-label">基本信息</label>
				<div class="col-sm-9 col-xs-12">
					<p class="form-control-static">用户ID：{$row.id}，OpenId：{$row.openid}</p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-xs-12 col-sm-3 col-md-2 control-label">昵称</label>
				<div class="col-sm-9 col-xs-12">
					<p class="form-control-static">{$row.nickname}</p>
				</div>
			</div>
			<div class="form-group">
			<label class="col-xs-12 col-sm-3 col-md-2 control-label">微信头像</label>
				<div class="col-sm-9 col-xs-12">
					<div class="input-group " style="margin-top:.5em;">
						<img src="<php>echo $row['headimgurl'] ? $row['headimgurl'].'132' :'/static/images/nopic.jpg';</php>" onerror="this.src='/static/images/nopic.jpg';" class="img-responsive img-thumbnail" width="150">
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-xs-12 col-sm-3 col-md-2 control-label">手机号</label>
				<div class="col-sm-9 col-xs-12">
					<p class="form-control-static">{$row.mobile}</p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-xs-12 col-sm-3 col-md-2 control-label">地址</label>
				<div class="col-sm-9 col-xs-12">
					<p class="form-control-static">{$row.country}&nbsp;&nbsp;{$row.province}&nbsp;&nbsp;{$row.city}</p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-xs-12 col-sm-3 col-md-2 control-label">邀请人信息</label>
				<div class="col-sm-9 col-xs-12">
					<p class="form-control-static">邀请人ID：<span class="label label-info">{$ref_user.id}</span> ， OpendId：<span class="label label-info">{$ref_user.openid}</span> ， 昵称<span class="label label-info">{$ref_user.nickname}</span> </p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-xs-12 col-sm-3 col-md-2 control-label">积分概况</label>
				<div class="col-sm-9 col-xs-12">
					<p class="form-control-static">总积分：<span class="label label-info">{$row.point}</span> 积分， 累计签到收益：<span class="label label-info">{$row.point_sign}</span> ， 累计邀请积分收益：<span class="label label-info">{$row.point_ref}</span> </p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-xs-12 col-sm-3 col-md-2 control-label">签到概况</label>
				<div class="col-sm-9 col-xs-12">
					<p class="form-control-static">累计签到次数：<span class="label label-info">{$row.sign_num}</span> ，
					上次签到时间：<span class="label label-info"><php>formatTime($last_sign_time);</php></span></p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-xs-12 col-sm-3 col-md-2 control-label">创建时间</label>
				<div class="col-sm-9 col-xs-12">
					<p class="form-control-static"><php>formatTime($row['created']);</php></p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-xs-12 col-sm-3 col-md-2 control-label"> * </label>
				<div class="col-sm-9 col-xs-12">
                    <a  class="btn btn-success" href="{:U('prime/user/pchg',['id'=>$row['id']])}">积分异动</a>
				</div>
			</div>
            <div class="form-group col-sm-12">
                <input type="hidden" name="id" value="{$row.id}"/>
                <input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
            </div>
		</div>
	</div>
	</form>
	<!--<div class="alert alert-info" role="alert">一些说明</div>-->
</div>

		</div>
	</div>
</div>


<include file="block/footer" />