<include file="block/hat" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 重置和基础样式 */
                * {
                    box-sizing: border-box;
                }

                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    color: #2d3748;
                }

                .project-edit-container {
                    padding: 2rem;
                    background: #f7fafc;
                    min-height: 100vh;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                }

                /* 现代化标签页 */
                .project-tabs {
                    background: white;
                    border-radius: 1rem 1rem 0 0;
                    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    border-bottom: none;
                    overflow: hidden;
                    margin-bottom: 0;
                }

                .project-tab-list {
                    display: flex;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    flex-wrap: wrap;
                }

                .project-tab-item {
                    flex: 1;
                    min-width: 120px;
                }

                .project-tab-link {
                    display: block;
                    padding: 1.5rem 1rem;
                    color: #4a5568;
                    text-decoration: none;
                    font-weight: 600;
                    font-size: 1.5rem;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    border-right: 1px solid #e2e8f0;
                    position: relative;
                    text-align: center;
                }

                .project-tab-item:last-child .project-tab-link {
                    border-right: none;
                }

                .project-tab-link:hover {
                    background: #f7fafc;
                    color: #667eea;
                    text-decoration: none;
                }

                .project-tab-link.active {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .project-tab-link.active::after {
                    content: '';
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                    right: 0;
                    height: 2px;
                    background: white;
                }

                /* 页面头部 */
                .project-page-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 2rem 0;
                    flex-wrap: wrap;
                    gap: 1.5rem;
                }

                .project-page-title-section {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .project-page-title {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .project-page-subtitle {
                    color: #4a5568;
                    font-size: 1.5rem;
                    margin: 0;
                }

                .project-page-actions {
                    display: flex;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                /* 现代化表单卡片 */
                .project-form-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    margin-bottom: 1.5rem;
                }

                .project-form-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .project-form-header {
                    padding: 2rem 2rem 1rem 2rem;
                    border-bottom: 1px solid #f1f5f9;
                }

                .project-form-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .project-form-description {
                    color: #4a5568;
                    font-size: 1.5rem;
                    margin: 0.5rem 0 0 0;
                }

                .project-form-body {
                    padding: 2rem;
                }

                /* 现代化表单组 */
                .project-form-group {
                    margin-bottom: 2rem;
                }

                .project-form-label {
                    display: block;
                    font-weight: 600;
                    color: #2d3748;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .project-form-label .required {
                    color: #f56565;
                    font-weight: 700;
                }

                .project-form-control {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: white;
                    font-family: inherit;
                }

                .project-form-control:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    background: #fafafa;
                }

                .project-form-control:hover {
                    border-color: #cbd5e0;
                }

                .project-textarea {
                    resize: vertical;
                    min-height: 120px;
                }

                .project-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                /* 表单帮助文本 */
                .project-help-text {
                    font-size: 1.5rem;
                    color: #718096;
                    margin-top: 0.5rem;
                }

                .project-error-text {
                    font-size: 1.5rem;
                    color: #f56565;
                    margin-top: 0.5rem;
                }

                /* 现代化按钮 */
                .project-btn {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    font-size: 1.5rem;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                    font-family: inherit;
                }

                .project-btn:before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .project-btn:hover:before {
                    left: 100%;
                }

                .project-btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .project-btn-primary:hover {
                    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    color: white;
                    text-decoration: none;
                }

                .project-btn-secondary {
                    background: #f1f5f9;
                    color: #4a5568;
                    border: 1px solid #e2e8f0;
                }

                .project-btn-secondary:hover {
                    background: #e2e8f0;
                    transform: translateY(-1px);
                    color: #2d3748;
                    text-decoration: none;
                }

                .project-btn-lg {
                    padding: 1rem 2rem;
                    font-size: 1.5rem;
                }

                /* 表单操作区域 */
                .project-form-actions {
                    padding: 1.5rem 2rem 2rem 2rem;
                    background: #f7fafc;
                    border-top: 1px solid #e2e8f0;
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    flex-wrap: wrap;
                }

                /* 富文本编辑器样式 */
                .project-richtext-container {
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .project-richtext-container:focus-within {
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .project-richtext-container .richtext {
                    border: none;
                    font-size: 1.5rem;
                    font-family: inherit;
                    padding: 0.75rem 1rem;
                    width: 100%;
                    resize: vertical;
                    min-height: 120px;
                }

                .project-richtext-container .richtext:focus {
                    outline: none;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .project-edit-container {
                        padding: 1.5rem;
                    }

                    .project-page-header {
                        flex-direction: column;
                        align-items: flex-start;
                    }

                    .project-page-title {
                        font-size: 1.5rem;
                    }

                    .project-form-header,
                    .project-form-body,
                    .project-form-actions {
                        padding: 1.5rem;
                    }

                    .project-form-actions {
                        flex-direction: column;
                    }

                    .project-btn {
                        width: 100%;
                        justify-content: center;
                    }

                    .project-tab-list {
                        flex-direction: column;
                    }

                    .project-tab-item {
                        flex: none;
                    }

                    .project-tab-link {
                        border-right: none;
                        border-bottom: 1px solid #e2e8f0;
                    }

                    .project-tab-item:last-child .project-tab-link {
                        border-bottom: none;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .project-fade-in {
                    animation: fadeInUp 0.5s ease-out;
                }

                .project-fade-in-delay-1 {
                    animation: fadeInUp 0.5s ease-out 0.1s both;
                }

                .project-fade-in-delay-2 {
                    animation: fadeInUp 0.5s ease-out 0.2s both;
                }

                /* 表单验证状态 */
                .project-form-control.error {
                    border-color: #f56565;
                    box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
                }

                .project-form-control.success {
                    border-color: #48bb78;
                    box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
                }

                /* 加载状态 */
                .project-btn.loading {
                    pointer-events: none;
                    opacity: 0.7;
                }

                .project-btn.loading::after {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    margin: auto;
                    border: 2px solid transparent;
                    border-top-color: currentColor;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>

            <div class="project-edit-container project-fade-in">
                <!-- 现代化标签页 -->
                <div class="project-tabs">
                    <ul class="project-tab-list">
                        <li class="project-tab-item">
                            <a href="{:U('Project/index')}" class="project-tab-link <php>if(CONTROLLER_NAME=='Project' && ACTION_NAME=='index') echo 'active'</php>">
                                <i class="fa fa-briefcase"></i>
                                项目管理
                            </a>
                        </li>
                        <li class="project-tab-item">
                            <a href="{:U('Project/edit')}" class="project-tab-link <php>if(CONTROLLER_NAME=='Project' && ACTION_NAME=='edit') echo 'active'</php>">
                                <i class="fa fa-<php>echo !I('get.id') ? 'plus' : 'edit';</php>"></i>
                                <php>echo !I('get.id') ? '添加' : '编辑';</php>项目
                            </a>
                        </li>
                        <li class="project-tab-item">
                            <a href="{:U('Projectpost/index')}" class="project-tab-link <php>if(CONTROLLER_NAME=='Projectpost' && ACTION_NAME=='index') echo 'active'</php>">
                                <i class="fa fa-users"></i>
                                岗位管理
                            </a>
                        </li>
                        <li class="project-tab-item">
                            <a href="{:U('Projectpost/edit')}" class="project-tab-link <php>if(CONTROLLER_NAME=='Projectpost' && ACTION_NAME=='edit') echo 'active'</php>">
                                <i class="fa fa-<php>echo !I('get.id') ? 'plus' : 'edit';</php>"></i>
                                <php>echo !I('get.id') ? '添加' : '编辑';</php>岗位
                            </a>
                        </li>
                        <li class="project-tab-item">
                            <a href="{:U('Project/quotation')}" class="project-tab-link <php>if(ACTION_NAME=='quotation') echo 'active'</php>">
                                <i class="fa fa-calculator"></i>
                                报价管理
                            </a>
                        </li>
                        <php>if (ACTION_NAME=='setquotation') {</php>
                        <li class="project-tab-item">
                            <a href="#" class="project-tab-link <php>if(ACTION_NAME=='setquotation') echo 'active'</php>">
                                <i class="fa fa-<php>echo !I('get.id') ? 'plus' : 'edit';</php>"></i>
                                <php>echo !I('get.id') ? '添加' : '编辑';</php>报价
                            </a>
                        </li>
                        <php>}</php>
                    </ul>
                </div>

                <!-- 页面头部 -->
                <div class="project-page-header" style="margin-top: 2rem;">
                    <div class="project-page-title-section">
                        <h1 class="project-page-title">
                            <i class="fa fa-<php>echo I('get.id') ? 'edit' : 'plus';</php>"></i>
                            <php>echo I('get.id') ? '编辑项目' : '添加项目';</php>
                        </h1>
                        <p class="project-page-subtitle">
                            <php>echo I('get.id') ? '修改项目信息和配置' : '创建新的项目并设置基本信息';</php>
                        </p>
                    </div>
                    <div class="project-page-actions">
                        <a href="{:U('Project/index')}" class="project-btn project-btn-secondary">
                            <i class="fa fa-arrow-left"></i>
                            返回列表
                        </a>
                    </div>
                </div>

                <!-- 现代化表单 -->
                <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="project-form">
                    <!-- 基本信息卡片 -->
                    <div class="project-form-card project-fade-in-delay-1">
                        <div class="project-form-header">
                            <h2 class="project-form-title">
                                <i class="fa fa-info-circle"></i>
                                项目基本信息
                            </h2>
                            <p class="project-form-description">
                                请填写项目的基本信息，所有字段都是必填项
                            </p>
                        </div>

                        <div class="project-form-body">
                            <!-- 项目名称 -->
                            <div class="project-form-group">
                                <label class="project-form-label">
                                    <i class="fa fa-briefcase"></i>
                                    项目名称
                                    <span class="required">*</span>
                                </label>
                                <input type="text"
                                       name="name"
                                       class="project-form-control"
                                       value="{$row.name}"
                                       placeholder="请输入项目名称"
                                       required />
                                <div class="project-help-text">项目的完整名称，将显示在项目列表中</div>
                            </div>

                            <!-- 项目归类 -->
                            <div class="project-form-group">
                                <label class="project-form-label">
                                    <i class="fa fa-tag"></i>
                                    项目归类
                                    <span class="required">*</span>
                                </label>
                                <select name="category" class="project-form-control project-select" required>
                                    <option value="0">请选择项目的归类</option>
                                    <php>foreach($categoryList as $key => $val) {</php>
                                    <option value="{$key}" {: $key== $row['category'] ? 'selected' : ''}>{$val}</option>
                                    <php>}</php>
                                </select>
                                <div class="project-help-text">选择项目所属的分类，便于管理和筛选</div>
                            </div>

                            <!-- 项目描述 -->
                            <div class="project-form-group">
                                <label class="project-form-label">
                                    <i class="fa fa-file-text-o"></i>
                                    项目描述
                                </label>
                                <input type="text"
                                       name="desc"
                                       class="project-form-control"
                                       value="{$row.desc}"
                                       placeholder="请输入项目简要描述" />
                                <div class="project-help-text">项目的简要描述，概括项目的主要内容</div>
                            </div>

                            <!-- 项目说明 -->
                            <div class="project-form-group">
                                <label class="project-form-label">
                                    <i class="fa fa-file-text"></i>
                                    项目说明
                                </label>
                                <div class="project-richtext-container">
                                    <textarea name="content"
                                              rows="6"
                                              class="richtext project-textarea"
                                              placeholder="请输入详细的项目说明...">{$row.content}</textarea>
                                </div>
                                <div class="project-help-text">详细的项目说明，支持富文本格式，可以包含项目目标、要求、流程等信息</div>
                            </div>
                        </div>
                    </div>

                    <!-- 表单操作区域 -->
                    <div class="project-form-actions">
                        <input type="hidden" name="id" value="{$row.id}" />
                        <button type="button" onclick="history.back()" class="project-btn project-btn-secondary">
                            <i class="fa fa-times"></i>
                            取消
                        </button>
                        <button type="submit" name="submit" class="project-btn project-btn-primary project-btn-lg">
                            <i class="fa fa-save"></i>
                            <php>echo I('get.id') ? '保存修改' : '创建项目';</php>
                        </button>
                    </div>
                </form>

                <!-- 操作提示卡片 -->
                <div class="project-form-card project-fade-in-delay-2" style="margin-top: 1.5rem;">
                    <div class="project-form-header">
                        <h2 class="project-form-title">
                            <i class="fa fa-lightbulb-o"></i>
                            操作提示
                        </h2>
                    </div>
                    <div class="project-form-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                            <div style="padding: 1rem; background: #f0fff4; border-left: 4px solid #48bb78; border-radius: 0.5rem;">
                                <h4 style="margin: 0 0 0.5rem 0; color: #2f855a; font-size: 1.5rem; font-weight: 600;">
                                    <i class="fa fa-check-circle"></i>
                                    创建成功后
                                </h4>
                                <p style="margin: 0; color: #2f855a; font-size: 1.5rem;">
                                    项目创建成功后，您可以为该项目添加岗位信息和设置报价
                                </p>
                            </div>
                            <div style="padding: 1rem; background: #fffbf0; border-left: 4px solid #ed8936; border-radius: 0.5rem;">
                                <h4 style="margin: 0 0 0.5rem 0; color: #c05621; font-size: 1.5rem; font-weight: 600;">
                                    <i class="fa fa-exclamation-triangle"></i>
                                    注意事项
                                </h4>
                                <p style="margin: 0; color: #c05621; font-size: 1.5rem;">
                                    项目名称和归类一旦设置建议不要频繁修改，以免影响相关岗位配置
                                </p>
                            </div>
                            <div style="padding: 1rem; background: #f0f9ff; border-left: 4px solid #3b82f6; border-radius: 0.5rem;">
                                <h4 style="margin: 0 0 0.5rem 0; color: #1e40af; font-size: 1.5rem; font-weight: 600;">
                                    <i class="fa fa-info-circle"></i>
                                    项目状态
                                </h4>
                                <p style="margin: 0; color: #1e40af; font-size: 1.5rem;">
                                    新创建的项目默认为停止状态，需要在项目列表中手动上架
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__ROOT__/static/js/prime/common.js"></script>
<link rel="stylesheet" href="__ROOT__/static/js/app/layer/skin/layer.css" />

<script>
	require(['layer', 'util'], function(layer, u) {
		$(function(){
			// 初始化富文本编辑器
			u.editor($('.richtext')[0]);
			u.editor($('.richtext1')[0]);

			// 表单验证和交互
			var $form = $('#project-form');
			var $submitBtn = $('button[type="submit"]');

			// 表单字段实时验证
			$('.project-form-control').on('input blur', function() {
				var $field = $(this);
				var value = $field.val().trim();
				var isRequired = $field.prop('required');
				var fieldName = $field.attr('name');

				// 移除之前的验证状态
				$field.removeClass('error success');
				$field.siblings('.project-error-text').remove();

				// 必填字段验证
				if (isRequired && !value) {
					$field.addClass('error');
					$field.after('<div class="project-error-text"><i class="fa fa-exclamation-circle"></i> 此字段为必填项</div>');
					return;
				}

				// 特定字段验证
				if (value) {
					var isValid = true;
					var errorMsg = '';

					switch(fieldName) {
						case 'name':
							if (value.length < 2) {
								isValid = false;
								errorMsg = '项目名称至少2个字符';
							}
							break;
						case 'category':
							if (value == '0') {
								isValid = false;
								errorMsg = '请选择项目归类';
							}
							break;
					}

					if (!isValid) {
						$field.addClass('error');
						$field.after('<div class="project-error-text"><i class="fa fa-exclamation-circle"></i> ' + errorMsg + '</div>');
					} else {
						$field.addClass('success');
					}
				}
			});

			// 表单提交处理
			$form.on('submit', function(e) {
				e.preventDefault();

				// 验证必填字段
				var hasError = false;
				$('.project-form-control[required]').each(function() {
					var $field = $(this);
					var value = $field.val().trim();

					if (!value || (value == '0' && $field.attr('name') == 'category')) {
						$field.addClass('error');
						$field.siblings('.project-error-text').remove();
						$field.after('<div class="project-error-text"><i class="fa fa-exclamation-circle"></i> 此字段为必填项</div>');
						hasError = true;
					}
				});

				// 检查是否有验证错误
				if ($('.project-form-control.error').length > 0) {
					hasError = true;
				}

				if (hasError) {
					layer.msg('请修正表单中的错误信息', {icon: 2});
					// 滚动到第一个错误字段
					var $firstError = $('.project-form-control.error').first();
					if ($firstError.length) {
						$('html, body').animate({
							scrollTop: $firstError.offset().top - 100
						}, 500);
						$firstError.focus();
					}
					return false;
				}

				// 设置提交按钮为加载状态
				$submitBtn.addClass('loading');
				$submitBtn.prop('disabled', true);
				var originalText = $submitBtn.html();
				$submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

				// 提交表单
				$.ajax({
					url: $form.attr('action') || window.location.href,
					type: 'POST',
					data: $form.serialize(),
					dataType: 'json',
					success: function(response) {
						if (response.status === 1 || response.code === 1) {
							layer.msg(response.info || '操作成功', {icon: 1}, function() {
								window.location.href = "{:U('Project/index')}";
							});
						} else {
							layer.msg(response.info || '操作失败', {icon: 2});
						}
					},
					error: function() {
						layer.msg('网络错误，请重试', {icon: 2});
					},
					complete: function() {
						// 恢复提交按钮状态
						$submitBtn.removeClass('loading');
						$submitBtn.prop('disabled', false);
						$submitBtn.html(originalText);
					}
				});
			});

			// 页面加载动画
			setTimeout(function() {
				$('.project-form-card').addClass('project-fade-in');
			}, 100);

			// 表单字段聚焦效果
			$('.project-form-control').on('focus', function() {
				$(this).closest('.project-form-group').addClass('focused');
			}).on('blur', function() {
				$(this).closest('.project-form-group').removeClass('focused');
			});

			// 键盘快捷键
			$(document).on('keydown', function(e) {
				// Ctrl+S 保存
				if (e.ctrlKey && e.keyCode === 83) {
					e.preventDefault();
					$form.submit();
				}
				// Esc 取消
				if (e.keyCode === 27) {
					if (confirm('确定要取消编辑吗？未保存的更改将丢失。')) {
						history.back();
					}
				}
			});

			// 表单数据变化检测
			var originalFormData = $form.serialize();
			var hasUnsavedChanges = false;

			$('.project-form-control, .richtext').on('input change', function() {
				hasUnsavedChanges = ($form.serialize() !== originalFormData);
			});

			// 页面离开提醒
			$(window).on('beforeunload', function() {
				if (hasUnsavedChanges) {
					return '您有未保存的更改，确定要离开吗？';
				}
			});

			// 成功提交后清除未保存标记
			$form.on('submit', function() {
				hasUnsavedChanges = false;
			});

			// 项目归类选择提示
			$('select[name="category"]').on('change', function() {
				var $select = $(this);
				var value = $select.val();
				var $helpText = $select.siblings('.project-help-text');

				if (value && value !== '0') {
					$helpText.html('<i class="fa fa-check-circle" style="color: #48bb78;"></i> 已选择归类：' + $select.find('option:selected').text());
				} else {
					$helpText.text('选择项目所属的分类，便于管理和筛选');
				}
			});

			// 字符计数功能
			$('input[name="name"]').on('input', function() {
				var $input = $(this);
				var value = $input.val();
				var $helpText = $input.siblings('.project-help-text');
				var length = value.length;

				if (length > 0) {
					$helpText.html('项目名称长度：' + length + ' 个字符 <i class="fa fa-check-circle" style="color: #48bb78;"></i>');
				} else {
					$helpText.text('项目的完整名称，将显示在项目列表中');
				}
			});
		});
	});
</script>

<include file="block/footer" />