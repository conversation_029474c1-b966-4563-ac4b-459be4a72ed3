<?php

namespace Home\Controller;

use Think\Controller;
use \LaneWeChat\Core as WE;
use Think\Log;


//带参二维码关注
class WxcallController extends Controller
{

    public $conf = '';

    private $request;

    public function _initialize()
    {

        $id = I('get.id');
        if (!$id) return;
        $this->id = $id;
        $row = D("Service")->where(['id' => $id, 'status' => 1])->find();
        if (!$row) return;
        $this->conf = [
            'WX_TOKEN' => $row['wx_token'],
            'WX_APPID' => $row['appid'],
            'WX_APPSECRET' => $row['secret'],
        ];

        vendor('LaneWeChat.lanewechat');
        $raw = file_get_contents('php://input');
        $xml = (array)simplexml_load_string($raw, 'SimpleXMLElement', LIBXML_NOCDATA);
        $this->request = array_change_key_case($xml, CASE_LOWER);
//        $this->request = json_decode('{"tousername":"gh_6e846fb99cd1","fromusername":"ohdUz6iv45LGArjmVUk4aCQLL19Y","createtime":"1630120293","msgtype":"event","event":"SCAN","eventkey":"6","ticket":"gQF_8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyZEd5ZlU1NWxjUEcxanJRaGh4MWUAAgTbpylhAwQAjScA"}', 1);
        log::record(json_encode($this->request));
        dolog('error/wx/call1', json_encode($this->request));
        We\Base::init($this->conf);
        if (!WE\Wechat::validateSignature($this->conf['WX_TOKEN'])) {
            dolog('error/wx/call1', 'failed to validateSignature, raw:' . $raw . '; get:' . serialize(I("get.")));
        }
    }

    /**
     * @descrpition 入口
     */
    public function index()
    {
        if (empty($this->request)) return;
        $this->switchType();
    }

    /**
     * @descrpition 分发请求
     */
    private function switchType()
    {
        $request = $this->request;
        switch ($request['msgtype']) {
            case 'event':
                $request['event'] = strtolower($request['event']);
                switch ($request['event']) {
                    case 'scan':
                    case 'subscribe':
                        $this->subscribe();
                        break;
                    case 'unsubscribe':
                        $this->unSubscribe();
                        break;
                    case 'click':
                        $kw = $this->request['eventkey'];
                        break;
                    case 'user_get_card' :  //用户领取会员卡事件
                        dolog('error/wx/home_call2', json_encode($this->request). $this->id);
                        $content = D("WxCard", "Service")->userGetCard($request,$this->id);
                        break;
                    case 'update_member_card' : //会员卡更新和添加积分
                        //$res = D("WxCard", "Service")->IntegralReplyContent($request, $this->id, $this->conf, $this->serviceRow['temp_account_id']);
                        break;
                    case 'user_consume_card' : //核销事件
                        break;
                    default:
                        break;
                }
                break;
            case 'text':
                $openid = $this->request['fromusername'];
                $userJoinOpenid = D('UserJoinOpenid')
                    ->field('id')
                    ->where(['openid' => $openid])
                    ->find();
                if ($userJoinOpenid) {
                    D('UserJoinOpenid')->save(['id' => $userJoinOpenid['id'],
                        'last_reply_time' => time(),
                    ]);
                }
                // 关键字回复
                $kw = $request['content'];
                $textType = 0;//0普通回复 1.素材包回复  2.临时带参二维码回复 3.推荐页面 4.随便逛逛
                $typeId = 0;
                $typeEncode = '';
                $stringKw = '';
                $stringKwLeng = 0;
                if (preg_match('/^([a-zA-Z0-9]+)$/', $kw, $stringArr)) { //如果是纯字母和数字
                    $stringKw = trim($stringArr[1]);
                    $stringKwLeng = strlen($stringKw);
                    if (strpos($stringKw, 'TJ') === 0) { //推荐页面
                        $textType = 3;
                    } elseif (strpos($stringKw, 'GJ') === 0) { //逛街页面
                        $textType = 4;
                    } elseif (strpos($stringKw, 'CQ') === 0) { //详情页
                        $textType = 2;
                    } elseif (strpos($stringKw, 'SC') === 0) { // 素材包
                        $textType = 1;
                    }
                }
                if ($kw == '测试小程序') {
                    // <a data-miniprogram-appid="小程序appid" data-miniprogram-path="小程序路径" href="备用网址" data-miniprogram-type="text">文字内容</a>
                   $str = "<a data-miniprogram-appid='wx2672757b4553d5d7'  data-miniprogram-path='packageActivity/pages/myLuckyLottery/myLuckyLottery.html?lottery_id=LTI2111012019000002480000026857528144&paramsFromServer=wxActivityId&ref_share_uid=2177480848&ref_share_user_no=Ko+Xr9V+O2xEmFyy5YwKBw==&ref_share_channel=message&ref_share_id=07a9505e-6d3e-4eea-a680-447d4a504319&custom_ticket=ZL_iMLJJC'  data-miniprogram-type='text'>点击购买</a>";
                    echo WE\ResponsePassive::text($this->request['fromusername'], $this->request['tousername'], $str);
                }
                if ($kw == '测试小程序1') {
                    // <a data-miniprogram-appid="小程序appid" data-miniprogram-path="小程序路径" href="备用网址" data-miniprogram-type="text">文字内容</a>
                    $str = "<a data-miniprogram-appid='wx2672757b4553d5d7'  data-miniprogram-path='pages/activity/activity?collection_activity_no=070v8mil1-wpDGIzJsAe91Fzd9OOk1pw&_x_custom_ticket=ZL_sdnKvq'  data-miniprogram-type='text'>点击购买</a>";
                    echo WE\ResponsePassive::text($this->request['fromusername'], $this->request['tousername'], $str);
                }
                if ($kw == '测试文字内容') {
                    $title = '<a href="weixin://bizmsgmenu?msgmenuid=1&msgmenucontent=领取优惠券">点击领取</a>';
                    echo WE\ResponsePassive::text($this->request['fromusername'], $this->request['tousername'], $title);
                }
                if ($textType > 0) {
                    $typeEncode = mb_substr($stringKw, 2, $stringKwLeng);
                    $typeId = deHash($typeEncode) - 1000000;
                    dolog('error/wx/call2', 'failed to validateSignature,get:' . $typeId . "sss:" . $textType);
                }
                if (in_array($textType, [2, 3, 4])) { //针对临时带参二维码
                    if (in_array($this->id, [2, 4, 6])) {
                        $http = 'http://m.zigouba.cn/createShop/index?accountauth=1&qrcodeid=' . $typeId . "&scenetype=%s";
                    } else {
                        $http = 'http://m.zigouba.cn/createShop/index?qrcodeid=' . $typeId . "&scenetype=%s";
                    }
                    $sceneRow = D("WxServiceTempSceneQrcode")->where(['id' => $typeId])->find();
                    if ($sceneRow) {
//                        $agentRow = D("Agent")->where(['id' => $sceneRow['agent_id']])->field('headimg,new_headimg')->find();
//                        $agentImg = !empty($agentRow['new_headimg']) ? qnimg($agentRow['new_headimg']) : $agentRow['headimg'];
                        $items = [];
                        $priceString = '';
                        if ($sceneRow['coupon_price'] > 0) {
                            $priceString = '券后价：' . number_format($sceneRow['coupon_price'] / 100, 2);
                        }
                        switch ($textType) {
                            case 3 ://场景id  1推荐  2 逛街   3详情 4店主主页
                                $items[] = WE\ResponsePassive::newsItem($sceneRow['title'], $priceString, $sceneRow['img'], sprintf($http, 1));
//                                $items[] = WE\ResponsePassive::newsItem('商品详情', '', $sceneRow['img'], sprintf($http, 3));
//                                $items[] = WE\ResponsePassive::newsItem('店主主页', '', $agentImg, sprintf($http, 4));
                                break;
                            case 4 :
                                $items[] = WE\ResponsePassive::newsItem($sceneRow['title'], $priceString, $sceneRow['img'], sprintf($http, 2));
//                                $items[] = WE\ResponsePassive::newsItem('商品详情', '', $sceneRow['img'], sprintf($http, 3));
//                                $items[] = WE\ResponsePassive::newsItem('店主推荐', '', $agentImg, sprintf($http, 1));
                                break;
                            case 2 :
                                $items[] = WE\ResponsePassive::newsItem($sceneRow['title'], $priceString, $sceneRow['img'], sprintf($http, 3));
//                                $items[] = WE\ResponsePassive::newsItem('商品详情2', '商品详情2', $sceneRow['img'], sprintf($http, 1));
//                                $items[] = WE\ResponsePassive::newsItem($sceneRow['title'], '', $sceneRow['img'], sprintf($http, 3));
//                                $items[] = WE\ResponsePassive::newsItem('店主推荐', '', $sceneRow['img'], sprintf($http, 1));
                                break;
                        }
                        if ($items) {
                            dolog('error/wx/call2', 'failed to validateSignature,get:' . json_encode(WE\ResponsePassive::news($this->request['fromusername'], $this->request['tousername'], $items)));
                            echo WE\ResponsePassive::news($this->request['fromusername'], $this->request['tousername'], $items);
                            die;
                        }
                    }
                } elseif ($textType == 1) { //素材包
                    $items = [];
                    $matereialPackageRow = D("MaterialPackage")->where(['id' => $typeId])->find();
                    if ($matereialPackageRow) { //素材为真
                        $openid = $this->request['fromusername'];
                        $userJoinOpenid = D('UserJoinOpenid')->where(['openid' => $openid])->find();
                        if ($userJoinOpenid) {
                            $http = 'http://m.zigouba.cn/createShop/index?qrcodeid=' . $typeId . "&scenetype=%s";
                            $priceString = '';
                            $img = qnimg($matereialPackageRow['send_img']);
                            if ($matereialPackageRow['coupon_price'] > 0) {
                                $priceString = '券后价：' . number_format($matereialPackageRow['coupon_price'] / 100, 2);
                            }
                            $items[] = WE\ResponsePassive::newsItem($matereialPackageRow['send_title'], $priceString, $img, sprintf($http, 5));
                        }
                    }
                    if ($items) {
                        echo WE\ResponsePassive::news($this->request['fromusername'], $this->request['tousername'], $items);
                    }
                } else { //其他内容
                    //关键词回复
                    $this->keyWordReply();
                }
                break;
            default:
                break;
        }
    }

    /**
     * 关注
     */
    private function subscribe()
    {
        $this->defaultReply();
    }


    /**
     * 默认关注回复
     */
    public function defaultReply()
    {
        $id = $this->id;
        $sub_content = D("Service")->where(['id' => $id])->getField('sub_content');
        $userJoinOpenRow = D('UserJoinOpenid')->where(['openid' => $this->request['fromusername']])->find();
        $row = [];
        if ($userJoinOpenRow || !$userJoinOpenRow['subscribe_status']) {
            D('UserJoinOpenid')->save(['id' => $userJoinOpenRow['id'], 'subscribe_status' => 1, 'subscribe_time' => time()]);
        }
        if ($row) {
            $nickname = $row['nickname'];
        } else {
            $wx_userinfo = WE\UserManage::getUserInfo($this->request['fromusername']);
            $nickname = (string)$wx_userinfo['nickname'];
        }
        $content = htmlspecialchars_decode(preg_replace('/\r\n/', "\n", $sub_content));
        $content = str_replace('%nickname%', $nickname, $content);
        if (!empty($content)) {
            echo WE\ResponsePassive::text($this->request['fromusername'], $this->request['tousername'], $content);
        }
    }

    /**
     * 添加用户关注信息
     */
    private function addSubscribeUser()
    {
        $openid = $this->request['fromusername'];
        $serviceId = $this->id;
        $serviceSubUserRow = D('ServiceSubUser')->where(['openid' => $openid, 'service_id' => $serviceId])->find();
        $update = true;
        if ($serviceSubUserRow) {
            if (!$serviceSubUserRow['is_sub']) {
                D('ServiceSubUser')->save(['id' => $serviceSubUserRow['id'], 'is_sub' => 1, 'update_time' => time()]);
            } else {
                $update = false;
            }
        } else {
            //获取unionid并写入表结构
            try {
                $wx_userinfo = WE\UserManage::getUserInfo($openid);
                if (isset($wx_userinfo['errcode']) and $wx_userinfo['errcode']) {
                    dolog('error/wx/call', "failed to getuserinfo, return data:" . json_encode($wx_userinfo, JSON_UNESCAPED_UNICODE)); // 日志记录
                    exit;
                } else {
                    dolog('error/wx/call', "failed to getuserinfo, return data:" . json_encode($wx_userinfo, JSON_UNESCAPED_UNICODE)); // 日志记录
                }
                $unionid = (string)$wx_userinfo['unionid'];
                if (!empty($unionid)) {
                    $insertId = D('ServiceSubUser')->add([
                        'is_sub' => 1,
                        'service_id' => $serviceId,
                        'openid' => $openid,
                        'unionid' => $unionid,
                        'create_time' => time(),
                        'update_time' => time(),
                    ]);
                    if (!$insertId) {
                        dolog('error/wx/call2', "failed add user:" . json_encode([
                                'is_sub' => 1,
                                'openid' => $openid,
                                'service_id' => $serviceId,
                                'unionid' => $unionid,
                                'create_time' => time(),
                                'update_time' => time(),
                            ], JSON_UNESCAPED_UNICODE)); // 日志记录
                    }
                }
            } catch (\Exception $e) {

            }

        }
    }

    /**
     * 取消关注
     */
    private function unSubscribe()
    {
        $openid = $this->request['fromusername'];
//        $sub_content = D("Service")->where(['id' => $this->id])->getField('sub_content');
        $userJoinOpenRow = D('UserJoinOpenid')->where(['openid' => $this->request['fromusername']])->find();
        if ($userJoinOpenRow) {
            D('UserJoinOpenid')->save(['id' => $userJoinOpenRow['id'], 'subscribe_status' => 0, 'unsubscribe' => time()]);
        }

        $openid = $this->request['fromusername'];
        $serviceId = $this->id;
        $serviceSubUserRow = D('ServiceSubUser')->where(['openid' => $openid, 'service_id' => $serviceId])->find();
        $unsubAll = false;
        if ($serviceSubUserRow) {
            D('ServiceSubUser')->save(['id' => $serviceSubUserRow['id'], 'is_sub' => 0, 'last_sub_time' => time(), 'update_time' => time()]);
            if(!D('ServiceSubUser')->where(['unionid' => $serviceSubUserRow['unionid'], 'is_sub' => 1])->find()) {
                $unsubAll = true;
            }
        }
    }

    /**
     * 关键词回复
     */
    public function keyWordReply(){
        $kwd = $this->request['content'];
//        if($kwd == '谢渊测试谢渊测试'){
            $service_id = $this->id;
            $replyConfRow = $this->matchingKeyWord($kwd,$service_id,$type=2);
//        //判断关键词,根据关键词去匹配对应服务号所设置的关键词回复
//        $replyWhere['service_id'] = $service_id;
//        $replyWhere['status'] = 0;
//        $replyWhere['key_word'] = ['like','%'.$kwd.'%'];
//        $replyConfRow = D('ServiceReplyConf')->where($replyWhere)->find();
            if($replyConfRow){
                //判断回复类型
                if($replyConfRow['reply_type'] == 1){ //文本回复
                    $str = htmlspecialchars_decode($replyConfRow['reply_content']);
                    echo WE\ResponsePassive::text($this->request['fromusername'], $this->request['tousername'], $str);
                }else if($replyConfRow['reply_type'] == 2){ //图片回复
                    //获取服务号对应的素材id
                    $serviceMediaInfo = D('ServiceReplyConfMedia')->where(['relation_id'=>$replyConfRow['id'],'service_id'=>$service_id])->find();
                    if($serviceMediaInfo){
                        $mediaId = $serviceMediaInfo['media_id'];
                        echo WE\ResponsePassive::image($this->request['fromusername'], $this->request['tousername'], $mediaId);
                    }else{
                        echo '';
                    }
                }
            }else{
                echo '';
            }
//        }else{
//            $service_id = $this->id;
//            $replyConfRow = $this->matchingKeyWord($kwd,$service_id);
////        //判断关键词,根据关键词去匹配对应服务号所设置的关键词回复
////        $replyWhere['service_id'] = $service_id;
////        $replyWhere['status'] = 0;
////        $replyWhere['key_word'] = ['like','%'.$kwd.'%'];
////        $replyConfRow = D('ServiceReplyConf')->where($replyWhere)->find();
//            if($replyConfRow){
//                //判断回复类型
//                if($replyConfRow['reply_type'] == 1){ //文本回复
//                    $str = htmlspecialchars_decode($replyConfRow['reply_content']);
//                    echo WE\ResponsePassive::text($this->request['fromusername'], $this->request['tousername'], $str);
//                }else if($replyConfRow['reply_type'] == 2){ //图片回复
//                    $mediaId = $replyConfRow['media_id'];
//                    echo WE\ResponsePassive::image($this->request['fromusername'], $this->request['tousername'], $mediaId);
//
//                }
//            }else{
//                echo '';
//            }
//        }

    }


    //反向关键词匹配
    private function matchingKeyWord($kwd,$service_id,$type = 1){
        //获取所有的关键词
        $Where['status'] = 0;

        if($type == 1){
            $Where['service_id'] = $service_id;
        }else{
            $Where['service_list'] = ['like','%'.$service_id.'%'];
        }
        $keyWordList = D('ServiceReplyConf')->where($Where)->select();
        $isBreak = false;
        $matchResult = [];
        if(!empty($keyWordList)){
            foreach ($keyWordList as $key => $val){
                $keyArr = explode(',',$val['key_word']);
                $keyArr = array_filter($keyArr);
                if($keyArr){
                    foreach ($keyArr as $Akey => $Aval){
                        $result = mb_strpos($kwd,$Aval);
                        if($result !== false){
                            $isBreak = true;
                            break;
                        }
                    }
                }
                if($isBreak){
                    $matchResult = $val;
                    break;
                }
            }
            return $matchResult;
        }
        return false;
    }


    public function __destruct()
    {
        parent::__destruct();
        if (WE\Wechat::isValid() && WE\Wechat::validateSignature($this->conf['WX_TOKEN'])) {
            echo $_GET['echostr'];
        } else {
            echo ""; // 默认返回，临时方案
        }
    }
}

// EOF
