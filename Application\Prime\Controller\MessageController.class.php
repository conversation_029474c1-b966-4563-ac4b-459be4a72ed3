<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;
use Util\AliSms;

/**
 * 服务站和平台简历沟通管理
 * Class MessageController
 * @package Prime\Controller
 */
class MessageController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    public function index() {
        $c_kw = [
            'name' => '姓名',
            'id' => 'ID',
            'phone' => '电话',
            'applied_position' => '应聘职位',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);

        // 回复状态筛选
        $is_reply = I("get.is_reply");
        if ($is_reply !== '') $where['is_reply'] = $is_reply;

        // 服务站筛选
        $service_station_id = I("get.service_station_id");
        if ($service_station_id) $where['service_station_id'] = $service_station_id;

        // 基础搜索条件
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['name', 'applied_position']))  {
                $where[$s_kw] = ['like', "%$s_val%"];
            }  else {
                $where[$s_kw] = $s_val;
            }
        }

        //公用搜索
        $contions = I('get.');
        $contionsWhere = ['group', 'is_out'];
        foreach ($contions as $whereKey => $row) {
            if (in_array($whereKey, $contionsWhere) && $row != '') {
                $where[$whereKey] = $row;
            }
        }

        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['msg_reply_time'] = ['between', [$s_start, $s_end]];
        }

        $obj = D("UserJob");

        // 获取所有服务站列表用于筛选下拉框
        $allServiceStationList = D("ServiceStation")->getField('id,service_name', true);

        // 新增子查询：获取 ServiceStationPlatformMessage 表中所有 user_job_id
        $subQuery = D("ServiceStationPlatformMessage")
        ->field('user_job_id')
        ->group('user_job_id')  // 去重
        ->buildSql();

        $count = $obj->where($where)->where("id IN {$subQuery}")->count();
        $page = $this->page($count, 20); // 减少每页显示数量，因为卡片式布局占用更多空间

        // 按最后沟通时间和是否需要回复排序
        $sort1 = sortParam('is_reply', 'desc');
        $sort2 = sortParam('msg_reply_time', 'desc'); // 改为降序，最新的在前
        $orderStr = "{$sort1['field']} {$sort1['order']}, {$sort2['field']} {$sort2['order']}";

        $list = $obj
            ->order($orderStr)
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->where("id IN {$subQuery}")
            ->select();

        if ($list) {
            $userList = [];
            $userArrId = array_unique(array_column($list, 'user_id'));
            if ($userArrId) {
                $userList = D("User")->where(['id' => ['in', $userArrId]])->getField('id,nickname', true);
            }
            $serviceStationList = [];
            $serviceStationArrId = array_unique(array_column($list, 'service_station_id'));
            if ($serviceStationArrId) {
                $serviceStationList = D("ServiceStation")->where(['id' => ['in', $serviceStationArrId]])->getField('id,service_name', true);
            }

            $userJobDocList = [];
            $userJobArrId = array_unique(array_column($list, 'id'));
            if ($userJobArrId) {
                $userJobDocList = D("UserJobDoc")->where(['user_job_id' => ['in', $userJobArrId]])->getField('user_job_id,content,is_html,status', true);
            }

            // 获取最近的消息记录（每个简历最新的一条）
            $userMessageList = [];
            $recentMessagesList = [];
            $userMessageArrId = array_unique(array_column($list, 'id'));
            if ($userMessageArrId) {
                // 获取最新的一条消息用于列表显示
                $latestMessages = D("ServiceStationPlatformMessage")
                    ->where(['user_job_id' => ['in', $userMessageArrId]])
                    ->order('create_time DESC')
                    ->select();

                // 按 user_job_id 分组，每个简历只保留最新的一条消息
                foreach ($latestMessages as $message) {
                    if (!isset($userMessageList[$message['user_job_id']])) {
                        $userMessageList[$message['user_job_id']] = $message;
                    }
                }

                // 获取每个简历的最近5条消息记录
                foreach ($userMessageArrId as $userJobId) {
                    $recentMessages = D("ServiceStationPlatformMessage")
                        ->where(['user_job_id' => $userJobId])
                        ->order('create_time DESC')
                        ->limit(5)
                        ->select();
                    $recentMessagesList[$userJobId] = $recentMessages;
                }

                // 获取消息中涉及的所有服务站信息，用于判断身份
                $allMessages = [];
                foreach ($recentMessagesList as $messages) {
                    $allMessages = array_merge($allMessages, $messages);
                }
                $messageServiceStationIds = array_unique(array_column($allMessages, 'service_station_id'));
                $messageServiceStationMap = [];
                if ($messageServiceStationIds) {
                    $messageServiceStations = D("ServiceStation")->where(['id' => ['in', $messageServiceStationIds]])->select();
                    foreach ($messageServiceStations as $station) {
                        $messageServiceStationMap[$station['id']] = $station;
                    }
                }
            }

            $this->assign('userJobDocList', $userJobDocList);
            $this->assign('serviceStationList', $serviceStationList);
            $this->assign('userMessageList', $userMessageList);
            $this->assign('recentMessagesList', $recentMessagesList);
            $this->assign('messageServiceStationMap', $messageServiceStationMap);
            $this->assign('userList', $userList);
        }

        // 分配所有服务站列表给模板
        $this->assign('allServiceStationList', $allServiceStationList);

        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '');
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('c_kw', $c_kw);
        $this->display();
    }


    /**
     * 添加编辑
     */
    public function edit() {
        $id = intval(I('get.id'));
        $obj = D("ServiceStationPlatformMessage");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        } else {
           $this->error('参数错误');
        }
        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("message/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $jobRow = D("UserJob")->where(['id' => $row['user_job_id']])->find();
        $this->assign('jobRow', $jobRow);
        //服务站ID
        $serviceStationRow = D("ServiceStation")->where(['id' => $row['service_station_id']])->find();
        $this->assign('serviceStationRow', $serviceStationRow);

        $this->display();
    }

    /**
     * 回复功能
     */
    public function reply() {
        $user_job_id = intval(I('get.user_job_id'));
        $obj = D("ServiceStationPlatformMessage");

        // 参数验证
        if (!$user_job_id) {
            $this->error('参数错误!!');
        }

        // 获取简历信息
        $jobRow = D("UserJob")->where(['id' => $user_job_id])->find();
        if (!$jobRow) {
            $this->error('简历不存在!!');
        }

        // 获取消息列表
        $jobList = D("ServiceStationPlatformMessage")->where(['user_job_id' => $user_job_id])->order('create_time DESC')->select();

        // 更新简历回复状态
        D("UserJob")->where(['id' => $user_job_id])->save(['is_reply' => 0]);

        // 获取服务站信息
        $serviceStationRow = D("ServiceStation")->where(['id' => $jobRow['service_station_id']])->find();

        // 获取消息中涉及的所有服务站信息，用于判断身份
        $serviceStationIds = array_unique(array_column($jobList, 'service_station_id'));
        $serviceStationMap = [];
        if ($serviceStationIds) {
            $serviceStations = D("ServiceStation")->where(['id' => ['in', $serviceStationIds]])->select();
            foreach ($serviceStations as $station) {
                $serviceStationMap[$station['id']] = $station;
            }
        }

        // 传递变量给模板
        $this->assign('jobList', $jobList);
        $this->assign('jobRow', $jobRow);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('serviceStationMap', $serviceStationMap);
        $this->assign('userJobId', $user_job_id);

        // 处理POST请求
        if (IS_POST) {
            // 记录请求日志
            dolog('message/reply_post', 'Reply POST request: user_job_id=' . $user_job_id . ', content_length=' . strlen(I('post.content')));

            if ($data = $obj->create()) {
                // 设置消息数据
                $data['create_time'] = time();
                $data['type'] = 2; // 平台回复
                $data['service_station_id'] = $jobRow['service_station_id'];
                $data['user_job_id'] = $user_job_id;

                // 添加消息记录
                $messageId = $obj->add($data);

                if ($messageId) {
                    // 记录成功日志
                    dolog('message/reply_success', 'Message added successfully with ID: ' . $messageId);

                    // 更新简历状态
                    $updateData = [
                        'is_reply' => 1,
                        'msg_reply_time' => time()
                    ];

                    // 处理提醒回复功能
                    if (I('post.needreply') == '1') {
                        $updateData['need_reply'] = 1;

                        // 发送短信提醒给服务站
                        if ($serviceStationRow && !empty($serviceStationRow['mobile']) && !empty($jobRow['name'])) {
                            try {
                                $aliSms = new \Util\AliSms();
                                // 使用现有的短信模板发送提醒，与UserjobController保持一致
                                $smsText = "您好，简历用户【{$jobRow['name']}】有新的平台消息需要回复，请及时查看处理。";
                                $smsResult = $aliSms->sendSms($smsText, $serviceStationRow['mobile'], 'SMS_479770174', '{"code":"' . $smsText . '"}');

                                if ($smsResult) {
                                    dolog('sms/reply_reminder_success', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 时间: " . date('Y-m-d H:i:s'));
                                } else {
                                    dolog('sms/reply_reminder_failed', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 时间: " . date('Y-m-d H:i:s'));
                                }
                            } catch (Exception $e) {
                                dolog('sms/reply_reminder_exception', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 错误: " . $e->getMessage());
                            }
                        }
                    } else {
                        $updateData['need_reply'] = 0;
                    }

                    // 更新简历状态
                    $updateResult = D("UserJob")->where(['id' => $user_job_id])->save($updateData);

                    if ($updateResult !== false) {
                        // 处理微信通知功能
                        if (I('post.send_wechat') == '1') {
                            // 构建微信通知内容
                                                    // 根据服务站类型获取微信群聊配置
                        $fwzservice_name = $serviceStationRow['service_name'];                            
                        $chatroom = $serviceStationRow['chatroom'];
                        $wxatuserlist = $serviceStationRow['wxatuserlist'];
                        $zjbname = '';
                        $tzzjb = '';
                        if ($serviceStationRow['zsb_type'] == 2) {
                            $zjbname = '【招就办-'.$serviceStationRow['contract_name'].'】';
                            $tzzjb = '请及时告知【招就办-'.$serviceStationRow['contract_name'].'】';
                        }

                        // 如果当前服务站类型为2，则从关联的服务站获取微信配置
                        if ($serviceStationRow['zsb_type'] == 2 && !empty($serviceStationRow['zsb_ref_station'])) {
                            $refStationRow = D("ServiceStation")->where(['id' => $serviceStationRow['zsb_ref_station']])->find();
                            if ($refStationRow) {
                                $fwzservice_name =  $refStationRow['service_name'];
                                $chatroom = $refStationRow['chatroom'];
                                $wxatuserlist = $refStationRow['wxatuserlist'];
                            }
                        }

                            $smscontent = $fwzservice_name."，平台回复了".$zjbname."【{$jobRow['name']}】简历，请及时".$tzzjb."查看处理。\n\n回复内容如下：\n\n{$data['content']}";
                            $this->sendWechatNotification($smscontent, $chatroom, $wxatuserlist);
                        }

                        // 成功回复，重定向到当前页面
                        $this->success("留言回复成功", U("message/reply", ['user_job_id' => $user_job_id]) . "?success=1");
                    } else {
                        $this->error("更新简历状态失败，请重试");
                    }
                } else {
                    // 消息添加失败
                    dolog('message/reply_error', 'Failed to add message');
                    $this->error("回复失败，请重试");
                }
            } else {
                // 数据验证失败
                $this->error($obj->getError() ?: "数据验证失败，请检查输入内容");
            }
            exit;
        }

        $this->display();
    }

    /**
     * 快速回复功能
     */
    public function quickReply() {
        if (!IS_POST) {
            $this->error('请求方式错误');
        }

        $user_job_id = intval(I('post.user_job_id'));
        $content = trim(I('post.content'));
        $needreply = I('post.needreply');
        $send_wechat = I('post.send_wechat');

        // 参数验证
        if (!$user_job_id) {
            $this->error('参数错误');
        }

        if (!$content) {
            $this->error('请输入回复内容');
        }

        if (strlen($content) > 1000) {
            $this->error('回复内容不能超过1000个字符');
        }

        // 获取相关数据
        $jobRow = D("UserJob")->where(['id' => $user_job_id])->find();
        if (!$jobRow) {
            $this->error('简历不存在');
        }

        $serviceStationRow = D("ServiceStation")->where(['id' => $jobRow['service_station_id']])->find();
        if (!$serviceStationRow) {
            $this->error('服务站不存在');
        }

        // 创建消息记录
        $obj = D("ServiceStationPlatformMessage");
        $data = [
            'content' => $content,
            'type' => 2, // 平台回复
            'service_station_id' => $jobRow['service_station_id'],
            'user_job_id' => $user_job_id,
            'create_time' => time(),
            'update_time' => time()
        ];

        // 添加消息记录
        $messageId = $obj->add($data);

        if ($messageId) {
            // 记录成功日志
            dolog('message/quick_reply_success', 'Quick reply added successfully with ID: ' . $messageId);

            // 更新简历状态
            $updateData = [
                'is_reply' => 1,
                'msg_reply_time' => time()
            ];

            // 处理提醒回复功能
            if ($needreply == '1') {
                $updateData['need_reply'] = 1;

                // 发送短信提醒给服务站
                if ($serviceStationRow && !empty($serviceStationRow['mobile']) && !empty($jobRow['name'])) {
                    try {
                        $aliSms = new \Util\AliSms();
                        // 使用现有的短信模板发送提醒
                        $smsText = "您好，简历用户【{$jobRow['name']}】有新的平台消息需要回复，请及时查看处理。";
                        $smsResult = $aliSms->sendSms($smsText, $serviceStationRow['mobile'], 'SMS_479770174', '{"code":"' . $smsText . '"}');

                        if ($smsResult) {
                            dolog('sms/quick_reply_reminder_success', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 时间: " . date('Y-m-d H:i:s'));
                        } else {
                            dolog('sms/quick_reply_reminder_failed', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 时间: " . date('Y-m-d H:i:s'));
                        }
                    } catch (Exception $e) {
                        dolog('sms/quick_reply_reminder_exception', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 错误: " . $e->getMessage());
                    }
                }
            } else {
                $updateData['need_reply'] = 0;
            }

            // 更新简历状态
            $updateResult = D("UserJob")->where(['id' => $user_job_id])->save($updateData);

            if ($updateResult !== false) {
                // 如果用户选择发送微信通知，则发送
                // 根据服务站类型获取微信群聊配置
                $fwzservice_name = $serviceStationRow['service_name'];  
                $chatroom = $serviceStationRow['chatroom'];
                $wxatuserlist = $serviceStationRow['wxatuserlist'];
                $zjbname = '';
                $tzzjb = '';
                if ($serviceStationRow['zsb_type'] == 2) {
                            $zjbname = '【招就办-'.$serviceStationRow['contract_name'].'】';
                            $tzzjb = '请及时告知【招就办-'.$serviceStationRow['contract_name'].'】';
                }

                // 如果当前服务站类型为2，则从关联的服务站获取微信配置
                if ($serviceStationRow['zsb_type'] == 2 && !empty($serviceStationRow['zsb_ref_station'])) {
                    $refStationRow = D("ServiceStation")->where(['id' => $serviceStationRow['zsb_ref_station']])->find();
                      if ($refStationRow) {
                                $fwzservice_name =  $refStationRow['service_name'];
                                $chatroom = $refStationRow['chatroom'];
                                $wxatuserlist = $refStationRow['wxatuserlist'];
                            }
                        }

                      $smscontent = $fwzservice_name."，平台回复了".$zjbname."【{$jobRow['name']}】简历，请及时".$tzzjb."查看处理。\n\n回复内容如下：\n\n{$content}";

                if ($send_wechat == '1') {
                    $this->sendWechatNotification($smscontent,$chatroom,$wxatuserlist);
                }

                // 返回成功响应
                $this->success("快速回复发送成功");
            } else {
                $this->error("更新简历状态失败，请重试");
            }
        } else {
            // 消息添加失败
            dolog('message/quick_reply_error', 'Failed to add quick reply message');
            $this->error("快速回复失败，请重试");
        }
    }

    /**
     * 发送微信消息通知
     * @param string $content 回复内容
     */
    private function sendWechatNotification($content,$chatroom,$wxatuserlist) {
        try {
            // 构建POST数据
            $postData = [
                'WxID' => 'zcgk666',
                'Data' => [
                    'SendTo' => $chatroom,
                    'Msg' => '@' . $content,
                    'AtUserList' => $wxatuserlist,
                    'ArgList' => ['SendTo', 'Msg', 'AtUserList'],
                    'AnyCallName' => 'SendMessageAt',
                    'TimeOut' => -1,
                    'CallName' => 'Any'
                ],
                'CallBackUrl' => null
            ];

            // 转换为JSON格式
            $jsonData = json_encode($postData, JSON_UNESCAPED_UNICODE);

            // 记录发送日志
            dolog('wechat_notification/send', 'Sending notification: ' . $jsonData);

            // 初始化cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://43.139.30.237:1080/HTTPManagement.aspx?Item=AddMessage');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);


            // 执行请求
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // 记录响应日志
            if ($error) {
                dolog('wechat_notification/error', 'cURL error: ' . $error);
            } else {
                dolog('wechat_notification/response', 'HTTP Code: ' . $httpCode . ', Response: ' . $response);
            }

            return $response;

        } catch (Exception $e) {
            // 记录异常日志
            dolog('wechat_notification/exception', 'Exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除消息功能
     */
    public function deleteMessage() {
        // 设置响应头为JSON格式
        header('Content-Type: application/json; charset=utf-8');

        if (!IS_POST) {
            echo json_encode(['status' => 0, 'message' => '请求方式错误']);
            exit;
        }

        $message_id = intval(I('post.message_id'));

        // 参数验证
        if (!$message_id) {
            echo json_encode(['status' => 0, 'message' => '消息ID不能为空']);
            exit;
        }

        // 记录删除请求日志
        dolog('message/delete_request', 'Delete message request: message_id=' . $message_id);

        try {
            $obj = D("ServiceStationPlatformMessage");

            // 检查消息是否存在
            $messageRow = $obj->where(['id' => $message_id])->find();
            if (!$messageRow) {
                echo json_encode(['status' => 0, 'message' => '消息不存在或已被删除']);
                exit;
            }

            // 记录删除前的消息信息（用于日志）
            $logInfo = [
                'message_id' => $message_id,
                'user_job_id' => $messageRow['user_job_id'],
                'type' => $messageRow['type'],
                'content_preview' => mb_substr($messageRow['content'], 0, 50) . '...',
                'create_time' => $messageRow['create_time']
            ];

            // 执行删除操作
            $deleteResult = $obj->where(['id' => $message_id])->delete();

            if ($deleteResult) {
                // 记录成功日志
                dolog('message/delete_success', 'Message deleted successfully: ' . json_encode($logInfo, JSON_UNESCAPED_UNICODE));

                echo json_encode([
                    'status' => 1,
                    'success' => true,
                    'message' => '消息删除成功'
                ]);
            } else {
                // 记录失败日志
                dolog('message/delete_failed', 'Failed to delete message: ' . json_encode($logInfo, JSON_UNESCAPED_UNICODE));

                echo json_encode(['status' => 0, 'message' => '删除失败，请重试']);
            }

        } catch (Exception $e) {
            // 记录异常日志
            dolog('message/delete_exception', 'Delete message exception: message_id=' . $message_id . ', error=' . $e->getMessage());

            echo json_encode(['status' => 0, 'message' => '系统错误，请重试']);
        }

        exit;
    }
}

