<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化微信二维码管理页面样式 */
                .wechatqrcode-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .wechatqrcode-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .wechatqrcode-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .wechatqrcode-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
                }

                .wechatqrcode-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .wechatqrcode-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .wechatqrcode-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .wechatqrcode-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .wechatqrcode-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .wechatqrcode-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .wechatqrcode-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .wechatqrcode-index-add-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
                    text-decoration: none;
                }

                .wechatqrcode-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .wechatqrcode-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .wechatqrcode-index-add-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化搜索面板 */
                .wechatqrcode-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    max-height: 0;
                    opacity: 0;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .wechatqrcode-index-search-panel.show {
                    max-height: 500px;
                    opacity: 1;
                }

                .wechatqrcode-index-search-header {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .wechatqrcode-index-search-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .wechatqrcode-index-search-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .wechatqrcode-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .wechatqrcode-index-search-body {
                    padding: 2rem;
                }

                .wechatqrcode-index-search-form {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 1.5rem;
                    align-items: end;
                }

                .wechatqrcode-index-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .wechatqrcode-index-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                }

                .wechatqrcode-index-form-select,
                .wechatqrcode-index-form-input {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-family: inherit;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: #f8fafc;
                }

                .wechatqrcode-index-form-select:focus,
                .wechatqrcode-index-form-input:focus {
                    outline: none;
                    border-color: #10b981;
                    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
                    background: white;
                }

                .wechatqrcode-index-search-actions {
                    display: flex;
                    gap: 1rem;
                    margin-top: 1rem;
                }

                .wechatqrcode-index-search-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .wechatqrcode-index-search-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .wechatqrcode-index-search-btn.btn-primary {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                }

                .wechatqrcode-index-search-btn.btn-primary:hover {
                    color: white;
                }

                .wechatqrcode-index-search-btn.btn-secondary {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                }

                .wechatqrcode-index-search-btn.btn-secondary:hover {
                    color: white;
                }

                /* 现代化导航标签 */
                .wechatqrcode-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .wechatqrcode-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .wechatqrcode-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .wechatqrcode-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .wechatqrcode-index-nav-link:hover {
                    color: #10b981;
                    background: rgba(16, 185, 129, 0.05);
                    text-decoration: none;
                }

                .wechatqrcode-index-nav-link.active {
                    color: #10b981 !important;
                    background: white !important;
                    border-bottom-color: #10b981 !important;
                    box-shadow: 0 -2px 4px rgba(16, 185, 129, 0.1) !important;
                    font-weight: 700 !important;
                }

                .wechatqrcode-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
                    z-index: 1;
                }

                .wechatqrcode-index-nav-link.active .wechatqrcode-index-nav-icon {
                    color: #10b981 !important;
                    transform: scale(1.1);
                }

                .wechatqrcode-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化卡片布局 */
                .wechatqrcode-index-cards-container {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                .wechatqrcode-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .wechatqrcode-card:hover {
                    transform: translateY(-4px);
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                }

                .wechatqrcode-card-header {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .wechatqrcode-card-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .wechatqrcode-card-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    margin: 0;
                }

                .wechatqrcode-card-icon {
                    width: 2rem;
                    height: 2rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .wechatqrcode-card-id {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .wechatqrcode-card-body {
                    padding: 2rem;
                }

                .wechatqrcode-content-section {
                    margin-bottom: 2rem;
                    padding-bottom: 2rem;
                    border-bottom: 1px solid #f1f5f9;
                }

                .wechatqrcode-content-section:last-child {
                    margin-bottom: 0;
                    padding-bottom: 0;
                    border-bottom: none;
                }

                .wechatqrcode-content-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1rem 0;
                }

                .wechatqrcode-content-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }
            </style>

            <div class="wechatqrcode-index-wrapper">
                <div class="wechatqrcode-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="wechatqrcode-index-header wechatqrcode-index-fade-in">
                        <div class="wechatqrcode-index-header-content">
                            <div class="wechatqrcode-index-title">
                                <div class="wechatqrcode-index-title-icon">
                                    <i class="fa fa-qrcode"></i>
                                </div>
                                <div class="wechatqrcode-index-title-text">
                                    <h1 class="wechatqrcode-index-title-main">微信二维码管理</h1>
                                    <p class="wechatqrcode-index-title-sub">WeChat QR Code Management</p>
                                </div>
                            </div>
                            <div class="wechatqrcode-index-actions">
                                <button type="button" class="wechatqrcode-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>
                                <a href="{:U('Wechatqrcode/edit')}" class="wechatqrcode-index-add-btn">
                                    <i class="fa fa-plus"></i>
                                    <span>添加二维码</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化搜索面板 -->
                    <div class="wechatqrcode-index-search-panel wechatqrcode-index-fade-in-delay-1" id="searchPanel">
                        <div class="wechatqrcode-index-search-header">
                            <div class="wechatqrcode-index-search-icon">
                                <i class="fa fa-search"></i>
                            </div>
                            <h3 class="wechatqrcode-index-search-title">搜索筛选</h3>
                        </div>
                        <div class="wechatqrcode-index-search-body">
                            <form method="get" class="wechatqrcode-index-search-form" role="form">
                                <div class="wechatqrcode-index-form-group">
                                    <label class="wechatqrcode-index-form-label">搜索字段</label>
                                    <select class="wechatqrcode-index-form-select" name="kw">
                                        <php>foreach($c_kw as $key=>$value){</php>
                                        <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                        <php>}</php>
                                    </select>
                                </div>
                                <div class="wechatqrcode-index-form-group">
                                    <label class="wechatqrcode-index-form-label">搜索内容</label>
                                    <input class="wechatqrcode-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
                                </div>
                                <div class="wechatqrcode-index-search-actions">
                                    <button type="submit" class="wechatqrcode-index-search-btn btn-primary">
                                        <i class="fa fa-search"></i>
                                        <span>搜索</span>
                                    </button>
                                    <a href="{:U('Wechatqrcode/index')}" class="wechatqrcode-index-search-btn btn-secondary">
                                        <i class="fa fa-refresh"></i>
                                        <span>重置</span>
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="wechatqrcode-index-nav-container wechatqrcode-index-fade-in-delay-1">
                        <ul class="wechatqrcode-index-nav-tabs">
                            <li class="wechatqrcode-index-nav-item">
                                <a href="{:U('Wechatqrcode/index')}" class="wechatqrcode-index-nav-link active">
                                    <i class="fa fa-list wechatqrcode-index-nav-icon"></i>
                                    <span>全部二维码</span>
                                </a>
                            </li>
                            <li class="wechatqrcode-index-nav-item">
                                <a href="javascript:void(0)" class="wechatqrcode-index-nav-link quick-filter" data-filter="enabled">
                                    <i class="fa fa-check-circle wechatqrcode-index-nav-icon"></i>
                                    <span>已启用</span>
                                </a>
                            </li>
                            <li class="wechatqrcode-index-nav-item">
                                <a href="javascript:void(0)" class="wechatqrcode-index-nav-link quick-filter" data-filter="disabled">
                                    <i class="fa fa-times-circle wechatqrcode-index-nav-icon"></i>
                                    <span>已禁用</span>
                                </a>
                            </li>
                            <li class="wechatqrcode-index-nav-item">
                                <a href="javascript:void(0)" class="wechatqrcode-index-nav-link quick-filter" data-filter="high-weight">
                                    <i class="fa fa-star wechatqrcode-index-nav-icon"></i>
                                    <span>高权重</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 现代化卡片容器 -->
                    <div class="wechatqrcode-index-cards-container wechatqrcode-index-fade-in-delay-2">
                        <php>foreach($list as $v) {</php>
                        <div class="wechatqrcode-card">
                            <!-- 卡片头部 -->
                            <div class="wechatqrcode-card-header">
                                <div class="wechatqrcode-card-title">
                                    <div class="wechatqrcode-card-icon">
                                        <i class="fa fa-qrcode"></i>
                                    </div>
                                    <span>微信二维码</span>
                                </div>
                                <div class="wechatqrcode-card-id">
                                    ID: {$v.id}
                                </div>
                            </div>

                            <!-- 卡片主体 -->
                            <div class="wechatqrcode-card-body">
                                <!-- 二维码图片展示 -->
                                <div class="wechatqrcode-content-section">
                                    <h4 class="wechatqrcode-content-title">
                                        <div class="wechatqrcode-content-icon">
                                            <i class="fa fa-image"></i>
                                        </div>
                                        二维码图片
                                    </h4>
                                    <div class="wechatqrcode-image-container" style="text-align: center; margin-bottom: 1rem;">
                                        <img src="{$v.image_path}"
                                             alt="微信二维码"
                                             class="wechatqrcode-image"
                                             style="max-width: 200px; max-height: 200px; border-radius: 0.75rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); cursor: pointer;"
                                             onclick="showImageModal('{$v.image_path}')">
                                    </div>
                                </div>

                                <!-- 提示文字 -->
                                <div class="wechatqrcode-content-section">
                                    <h4 class="wechatqrcode-content-title">
                                        <div class="wechatqrcode-content-icon">
                                            <i class="fa fa-comment"></i>
                                        </div>
                                        提示文字
                                    </h4>
                                    <div class="wechatqrcode-tip-text" style="font-size: 1.5rem; color: #374151; line-height: 1.6; padding: 1rem; background: #f8fafc; border-radius: 0.5rem; border-left: 4px solid #10b981;">
                                        {$v.tip_text|default="暂无提示文字"}
                                    </div>
                                </div>

                                <!-- 状态和权重信息 -->
                                <div class="wechatqrcode-info-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 2rem;">
                                    <!-- 权重信息 -->
                                    <div class="wechatqrcode-info-section">
                                        <h4 class="wechatqrcode-info-section-title" style="display: flex; align-items: center; gap: 0.5rem; font-size: 1.25rem; font-weight: 600; color: #6b7280; margin: 0 0 0.75rem 0;">
                                            <div class="wechatqrcode-info-section-icon" style="width: 1.5rem; height: 1.5rem; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); border-radius: 0.25rem; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem;">
                                                <i class="fa fa-weight"></i>
                                            </div>
                                            权重等级
                                        </h4>
                                        <div class="wechatqrcode-weight-badge" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.5rem 1rem; background: linear-gradient(135deg, #{$weightLevels[$v['weight']]['color']} 0%, #{$weightLevels[$v['weight']]['color']}dd 100%); color: white; border-radius: 1rem; font-size: 1.25rem; font-weight: 600;">
                                            <i class="fa fa-star"></i>
                                            <span>{$weightLevels[$v['weight']]['text']}</span>
                                        </div>
                                    </div>

                                    <!-- 状态信息 -->
                                    <div class="wechatqrcode-info-section">
                                        <h4 class="wechatqrcode-info-section-title" style="display: flex; align-items: center; gap: 0.5rem; font-size: 1.25rem; font-weight: 600; color: #6b7280; margin: 0 0 0.75rem 0;">
                                            <div class="wechatqrcode-info-section-icon" style="width: 1.5rem; height: 1.5rem; background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%); border-radius: 0.25rem; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem;">
                                                <i class="fa fa-toggle-on"></i>
                                            </div>
                                            使用状态
                                        </h4>
                                        <if condition="$v.status eq 1">
                                            <div class="wechatqrcode-status-badge" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.5rem 1rem; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; border-radius: 1rem; font-size: 1.25rem; font-weight: 600;">
                                                <i class="fa fa-check-circle"></i>
                                                <span>已启用</span>
                                            </div>
                                        <else />
                                            <div class="wechatqrcode-status-badge" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.5rem 1rem; background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); color: white; border-radius: 1rem; font-size: 1.25rem; font-weight: 600;">
                                                <i class="fa fa-times-circle"></i>
                                                <span>已禁用</span>
                                            </div>
                                        </if>
                                    </div>
                                </div>

                                <!-- 创建时间 -->
                                <div class="wechatqrcode-content-section">
                                    <h4 class="wechatqrcode-content-title">
                                        <div class="wechatqrcode-content-icon">
                                            <i class="fa fa-clock-o"></i>
                                        </div>
                                        创建时间
                                    </h4>
                                    <div class="wechatqrcode-create-time" style="font-size: 1.5rem; color: #6b7280; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fa fa-calendar"></i>
                                        <span>{$v.create_time|date="Y-m-d H:i:s",###}</span>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="wechatqrcode-actions" style="display: flex; gap: 1rem; flex-wrap: wrap; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e2e8f0;">
                                    <a href="{:U('wechatqrcode/edit', array('id' => $v['id']))}" class="wechatqrcode-action-btn btn-primary" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 600; text-decoration: none; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);">
                                        <i class="fa fa-edit"></i>
                                        <span>编辑</span>
                                    </a>
                                    <a href="{:U('wechatqrcode/toggle', array('id' => $v['id'], 'status' => $v['status']))}" class="wechatqrcode-action-btn <if condition="$v.status eq 1">btn-warning<else />btn-success</if>" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; background: linear-gradient(135deg, <if condition="$v.status eq 1">#f59e0b 0%, #d97706 100%<else />#10b981 0%, #059669 100%</if>); color: white; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 600; text-decoration: none; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(<if condition="$v.status eq 1">245, 158, 11<else />16, 185, 129</if>, 0.3);">
                                        <i class="fa fa-<if condition="$v.status eq 1">pause<else />play</if>"></i>
                                        <span><if condition="$v.status eq 1">禁用<else />启用</if></span>
                                    </a>
                                    <a href="{:U('wechatqrcode/delete', array('id' => $v['id']))}" class="wechatqrcode-action-btn btn-danger" onclick="return confirm('确定要删除这个二维码吗？删除后无法恢复！')" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 600; text-decoration: none; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);">
                                        <i class="fa fa-trash"></i>
                                        <span>删除</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <php>}</php>
                    </div>

                    <!-- 分页信息 -->
                    <div class="wechatqrcode-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem;">
                        {$page}
                    </div>
                </div>
            </div>

            <!-- 图片预览模态框 -->
            <div class="wechatqrcode-image-modal" id="imageModal" onclick="hideImageModal()" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.9); display: none; align-items: center; justify-content: center; z-index: 9999; backdrop-filter: blur(5px);">
                <div class="wechatqrcode-image-modal-close" style="position: absolute; top: 2rem; right: 2rem; width: 3rem; height: 3rem; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; cursor: pointer; transition: all 0.3s ease;">
                    <i class="fa fa-times"></i>
                </div>
                <img id="modalImage" src="" alt="预览图片" style="max-width: 90%; max-height: 90%; border-radius: 1rem; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);">
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // 设置导航标签active状态的函数
        function setActiveNavTab() {
            // 移除所有active状态
            $('.wechatqrcode-index-nav-link').removeClass('active');

            // 获取当前URL参数
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');
            var weight = urlParams.get('weight');

            // 根据参数设置对应的active状态
            if (status === '1') {
                // 已启用状态
                $('.quick-filter[data-filter="enabled"]').addClass('active');
            } else if (status === '0') {
                // 已禁用状态
                $('.quick-filter[data-filter="disabled"]').addClass('active');
            } else if (weight === 'high') {
                // 高权重
                $('.quick-filter[data-filter="high-weight"]').addClass('active');
            } else {
                // 默认状态（全部）
                $('.wechatqrcode-index-nav-link').first().addClass('active');
            }
        }

        // 快速筛选功能
        $('.quick-filter').click(function(e) {
            e.preventDefault();
            var $this = $(this);
            var filter = $this.data('filter');
            var currentUrl = window.location.href.split('?')[0];
            var newUrl = currentUrl;

            // 立即更新视觉状态，提供即时反馈
            $('.wechatqrcode-index-nav-link').removeClass('active');
            $this.addClass('active');

            // 添加加载状态
            var originalHtml = $this.html();
            $this.html('<i class="fa fa-spinner fa-spin wechatqrcode-index-nav-icon"></i><span>加载中...</span>');

            // 获取当前的搜索参数
            var urlParams = new URLSearchParams(window.location.search);
            var searchParams = new URLSearchParams();

            // 保留搜索关键词等其他参数
            if (urlParams.get('kw')) {
                searchParams.set('kw', urlParams.get('kw'));
            }
            if (urlParams.get('val')) {
                searchParams.set('val', urlParams.get('val'));
            }

            // 添加筛选参数
            switch(filter) {
                case 'enabled':
                    searchParams.set('status', '1');
                    break;
                case 'disabled':
                    searchParams.set('status', '0');
                    break;
                case 'high-weight':
                    searchParams.set('weight', 'high');
                    break;
                default:
                    // 不添加筛选参数，显示全部
                    break;
            }

            // 构建最终URL
            var paramString = searchParams.toString();
            if (paramString) {
                newUrl += '?' + paramString;
            }

            // 延迟跳转，让用户看到加载状态
            setTimeout(function() {
                window.location.href = newUrl;
            }, 300);
        });

        // 处理"全部"标签的点击事件
        $('.wechatqrcode-index-nav-link').not('.quick-filter').click(function(e) {
            var $this = $(this);
            var href = $this.attr('href');

            // 如果是指向当前页面的链接，处理active状态
            if (href && href.indexOf('Wechatqrcode/index') !== -1) {
                e.preventDefault();

                // 立即更新视觉状态
                $('.wechatqrcode-index-nav-link').removeClass('active');
                $this.addClass('active');

                // 添加加载状态
                var originalHtml = $this.html();
                $this.html('<i class="fa fa-spinner fa-spin wechatqrcode-index-nav-icon"></i><span>加载中...</span>');

                // 延迟跳转到不带参数的页面
                setTimeout(function() {
                    window.location.href = href;
                }, 300);
            }
        });

        // 卡片悬停效果增强
        $('.wechatqrcode-card').hover(
            function() {
                $(this).css('transform', 'translateY(-8px)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
            }
        );

        // 操作按钮悬停效果
        $('.wechatqrcode-action-btn').hover(
            function() {
                $(this).css('transform', 'translateY(-2px)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
            }
        );

        // 空状态处理
        var totalCards = $('.wechatqrcode-card').length;
        if (totalCards === 0) {
            var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-qrcode"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无二维码数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整筛选条件或添加新的微信二维码。</p><a href="' + '{:U("Wechatqrcode/edit")}' + '" class="btn btn-primary" style="padding: 0.75rem 1.5rem; font-size: 1.5rem;"><i class="fa fa-plus"></i> 添加二维码</a></div>');
            $('.wechatqrcode-index-cards-container').append($emptyState);
        }

        // 延迟执行active状态设置，确保DOM完全加载
        setTimeout(function() {
            setActiveNavTab();
        }, 100);
    });

    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
        } else {
            panel.classList.add('show');
        }
    }

    // 图片预览功能
    function showImageModal(imageSrc) {
        var modal = document.getElementById('imageModal');
        var modalImage = document.getElementById('modalImage');
        modalImage.src = imageSrc;
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    function hideImageModal() {
        var modal = document.getElementById('imageModal');
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideImageModal();
        }
    });

    // 添加动画效果
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .wechatqrcode-index-fade-in {
                animation: fadeInUp 0.6s ease-out;
            }

            .wechatqrcode-index-fade-in-delay-1 {
                animation: fadeInUp 0.6s ease-out 0.1s both;
            }

            .wechatqrcode-index-fade-in-delay-2 {
                animation: fadeInUp 0.6s ease-out 0.2s both;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .wechatqrcode-action-btn:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
            }

            .wechatqrcode-image:hover {
                transform: scale(1.05);
                transition: transform 0.3s ease;
            }

            .wechatqrcode-image-modal-close:hover {
                background: rgba(255, 255, 255, 0.3) !important;
                transform: scale(1.1);
            }
        `)
        .appendTo('head');
</script>

<include file="block/footer" />