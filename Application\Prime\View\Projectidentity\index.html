<include file="block/hat" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化身份管理页面样式 */
                .identity-index-wrapper {
                    width: 100%;
                    padding: 1.5rem;
                    background: #f7fafc;
                    min-height: 100vh;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.5;
                }

                /* 现代化页面标题 */
                .identity-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                    width: 100%;
                }

                .identity-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .identity-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .identity-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .identity-index-title-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .identity-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .identity-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .identity-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .identity-index-actions {
                    display: flex;
                    gap: 1rem;
                    align-items: center;
                }

                .identity-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                }

                .identity-index-search-toggle:hover {
                    background: #4b5563;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    text-decoration: none;
                }

                .identity-index-add-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .identity-index-add-btn:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    text-decoration: none;
                }

                /* 隐藏式搜索面板 */
                .identity-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    max-height: 0;
                    opacity: 0;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .identity-index-search-panel.show {
                    max-height: 500px;
                    opacity: 1;
                }

                .identity-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .identity-index-search-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .identity-index-search-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .identity-index-search-body {
                    padding: 1.5rem;
                    background: white;
                }

                .identity-index-search-form {
                    display: flex;
                    flex-direction: column;
                    gap: 1rem;
                }

                .identity-index-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .identity-index-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0;
                }

                .identity-index-form-input,
                .identity-index-form-select {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                }

                .identity-index-form-input:focus,
                .identity-index-form-select:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .identity-index-form-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                .identity-index-search-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    align-items: center;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e5e7eb;
                    flex-wrap: wrap;
                }

                .identity-index-search-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                }

                .identity-index-search-btn.btn-primary {
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .identity-index-search-btn.btn-primary:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    text-decoration: none;
                }

                .identity-index-search-btn.btn-secondary {
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                }

                .identity-index-search-btn.btn-secondary:hover {
                    background: #4b5563;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    text-decoration: none;
                }

                /* 现代化列表容器 */
                .identity-index-list-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }

                .identity-list-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .identity-list-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .identity-list-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .identity-list-body {
                    background: white;
                }

                /* 列表项样式 */
                .identity-list-item {
                    display: flex;
                    align-items: flex-start;
                    padding: 2rem;
                    border-bottom: 1px solid #f1f5f9;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    gap: 2rem;
                }

                .identity-list-item:last-child {
                    border-bottom: none;
                }

                .identity-list-item:hover {
                    background: #f8fafc;
                    transform: translateX(4px);
                }

                .identity-list-item::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 4px;
                    background: transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .identity-list-item:hover::before {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                /* ID区域 */
                .identity-item-id-section {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 0.5rem;
                    min-width: 6rem;
                }

                .identity-item-id {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 0.5rem 1rem;
                    border-radius: 2rem;
                    font-size: 1.5rem;
                    font-weight: 700;
                    text-align: center;
                    min-width: 5rem;
                }

                /* 内容区域 */
                .identity-item-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 1rem;
                }

                .identity-item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                .identity-item-title {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .identity-item-title i {
                    color: #667eea;
                    font-size: 1.75rem;
                }

                /* 身份信息 */
                .identity-info-section {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 0.5rem;
                    padding: 1rem;
                }

                .identity-section-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0 0 1rem 0;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding-bottom: 0.75rem;
                    border-bottom: 2px solid #667eea;
                }

                .identity-section-title i {
                    color: #667eea;
                    font-size: 1.5rem;
                }

                .identity-info-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                }

                .identity-info-item {
                    background: white;
                    border: 1px solid #e5e7eb;
                    border-radius: 0.375rem;
                    padding: 0.75rem;
                }

                .identity-info-label {
                    display: block;
                    font-size: 1.25rem;
                    color: #6b7280;
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                }

                .identity-info-value {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #1a202c;
                }

                .identity-info-value.headquarters {
                    color: #059669;
                }

                .identity-info-value.sub-identity {
                    color: #3b82f6;
                }

                .identity-info-value i {
                    font-size: 1.25rem;
                }

                .identity-time-value {
                    font-size: 1.5rem;
                    color: #6b7280;
                    line-height: 1.4;
                }

                /* 操作按钮区域 */
                .identity-item-actions {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    align-items: flex-end;
                    min-width: 100px;
                }

                .identity-item-actions .btn {
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    padding: 0.5rem 1rem;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    text-decoration: none;
                    text-align: center;
                    min-width: 80px;
                }

                .identity-item-actions .btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .identity-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .identity-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .identity-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .identity-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .identity-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .identity-info-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .identity-list-item {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 1rem;
                        padding: 1.5rem 1rem;
                    }

                    .identity-item-content {
                        margin-left: 0;
                        width: 100%;
                    }

                    .identity-item-actions {
                        margin-left: 0;
                        width: 100%;
                        flex-direction: row;
                        justify-content: flex-start;
                        align-items: center;
                    }

                    .identity-index-title-main {
                        font-size: 1.75rem;
                    }

                    .identity-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .identity-index-actions {
                        flex-direction: column;
                        width: 100%;
                    }

                    .identity-index-search-toggle,
                    .identity-index-add-btn {
                        width: 100%;
                        justify-content: center;
                    }
                }
            </style>

            <div class="identity-index-wrapper">
            <!-- 现代化页面标题 -->
            <div class="identity-index-header identity-index-fade-in">
                <div class="identity-index-header-content">
                    <div class="identity-index-title">
                        <div class="identity-index-title-icon">
                            <i class="fa fa-id-card"></i>
                        </div>
                        <div class="identity-index-title-text">
                            <h1 class="identity-index-title-main">身份管理</h1>
                            <p class="identity-index-title-sub">Project Identity Management</p>
                        </div>
                    </div>
                    <div class="identity-index-actions">
                        <button type="button" class="identity-index-search-toggle" onclick="toggleSearchPanel()">
                            <i class="fa fa-search"></i>
                            <span>搜索筛选</span>
                        </button>
                        <a href="{:U('Projectidentity/edit')}" class="identity-index-add-btn">
                            <i class="fa fa-plus"></i>
                            <span>添加身份</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 现代化搜索面板 -->
            <div class="identity-index-search-panel identity-index-fade-in-delay-1" id="searchPanel">
                <div class="identity-index-search-header">
                    <div class="identity-index-search-icon">
                        <i class="fa fa-search"></i>
                    </div>
                    <h3 class="identity-index-search-title">搜索筛选</h3>
                </div>
                <div class="identity-index-search-body">
                    <form method="get" class="identity-index-search-form" role="form">
                        <div class="identity-index-form-group">
                            <label class="identity-index-form-label">搜索字段</label>
                            <select class="identity-index-form-select" name="kw">
                                <php>foreach($c_kw as $key=>$value){</php>
                                <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                <php>}</php>
                            </select>
                        </div>
                        <div class="identity-index-form-group">
                            <label class="identity-index-form-label">搜索内容</label>
                            <input class="identity-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
                        </div>
                        <div class="identity-index-search-actions">
                            <button type="submit" class="identity-index-search-btn btn-primary">
                                <i class="fa fa-search"></i>
                                <span>搜索</span>
                            </button>
                            <a href="{:U('projectidentity/index')}" class="identity-index-search-btn btn-secondary">
                                <i class="fa fa-refresh"></i>
                                <span>重置</span>
                            </a>
                        </div>
                    </form>
                </div>
            </div>


            <!-- 现代化列表容器 -->
            <form action="" method="post">
                <div class="identity-index-list-container identity-index-fade-in-delay-2">
                    <div class="identity-list-header">
                        <div class="identity-list-icon">
                            <i class="fa fa-id-card"></i>
                        </div>
                        <h3 class="identity-list-title">身份列表</h3>
                    </div>
                    <div class="identity-list-body">
                        <php>foreach($list as $v) { </php>
                        <div class="identity-list-item">
                            <!-- ID显示 -->
                            <div class="identity-item-id-section">
                                <div class="identity-item-id">
                                    #{$v.id}
                                </div>
                            </div>

                            <!-- 内容区域 -->
                            <div class="identity-item-content">
                                <!-- 身份名称 -->
                                <div class="identity-item-header">
                                    <h4 class="identity-item-title">
                                        <i class="fa fa-user-circle"></i>
                                        {$v.name}
                                    </h4>
                                </div>

                                <!-- 身份信息 -->
                                <div class="identity-info-section">
                                    <h5 class="identity-section-title">
                                        <i class="fa fa-info-circle"></i>
                                        身份信息
                                    </h5>
                                    <div class="identity-info-grid">
                                        <div class="identity-info-item">
                                            <span class="identity-info-label">隶属身份：</span>
                                            <div class="identity-info-value <php>echo $projectIdentity[$v['pid']] ? 'sub-identity' : 'headquarters';</php>">
                                                <i class="fa fa-<php>echo $projectIdentity[$v['pid']] ? 'users' : 'building';</php>"></i>
                                                <span>{:$projectIdentity[$v['pid']] ?: '总部'}</span>
                                            </div>
                                        </div>
                                        <div class="identity-info-item">
                                            <span class="identity-info-label">创建时间：</span>
                                            <div class="identity-time-value">
                                                <i class="fa fa-calendar"></i>
                                                {$v.create_time|date='Y-m-d H:i:s',###}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="identity-item-actions">
                                <php>echo projectidentityStatBtn($v['id'], $v['status']);</php>
                            </div>
                        </div>
                        <php>}</php>
                    </div>
                </div>

                <!-- 分页信息 -->
                <div class="identity-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem; font-size: 1.5rem;">
                    {$page}
                </div>
            </form>
            </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function() {
        // 列表项悬停效果增强
        $('.identity-list-item').hover(
            function() {
                $(this).addClass('hover-effect');
            },
            function() {
                $(this).removeClass('hover-effect');
            }
        );

        // 身份状态按钮点击事件
        $('.js_status').click(function() {
            var that = $(this),
                url = that.attr('url');

            // 添加加载状态
            var originalText = that.html();
            that.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
            that.prop('disabled', true);

            $.get(url, function(data) {
                window.location.reload();
            }).fail(function() {
                that.html(originalText);
                that.prop('disabled', false);
                layer.msg('操作失败，请重试', {icon: 2});
            });
        });

        // 空状态处理
        var totalItems = $('.identity-list-item').length;
        if (totalItems === 0) {
            var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-id-card"></i></div><h3 style="font-size: 2rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无身份数据</h3><p style="font-size: 2rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的身份信息。</p></div>');
            $('.identity-index-list-container .identity-list-body').append($emptyState);
        }

        // 身份ID点击复制功能
        $('.identity-item-id').click(function() {
            var $id = $(this);
            var idText = $id.text();

            // 创建临时输入框进行复制
            var $temp = $('<input>');
            $('body').append($temp);
            $temp.val(idText).select();
            document.execCommand('copy');
            $temp.remove();

            // 显示复制成功提示
            var originalText = $id.text();
            $id.text('已复制!');
            $id.css('background', 'linear-gradient(135deg, #10b981 0%, #059669 100%)');
            $id.css('color', 'white');

            setTimeout(function() {
                $id.text(originalText);
                $id.css('background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
                $id.css('color', 'white');
            }, 1000);
        });

        // 身份名称点击效果
        $('.identity-item-title').click(function() {
            var $title = $(this);
            $title.css('transform', 'scale(0.98)');
            setTimeout(function() {
                $title.css('transform', 'scale(1)');
            }, 150);
        });

        // 隶属身份悬停效果
        $('.identity-info-value').hover(
            function() {
                if ($(this).hasClass('headquarters')) {
                    $(this).css('color', '#047857');
                } else if ($(this).hasClass('sub-identity')) {
                    $(this).css('color', '#1d4ed8');
                }
            },
            function() {
                if ($(this).hasClass('headquarters')) {
                    $(this).css('color', '#059669');
                } else if ($(this).hasClass('sub-identity')) {
                    $(this).css('color', '#3b82f6');
                }
            }
        );

        // 搜索框实时搜索提示
        $('input[name="val"]').on('input', function() {
            var $input = $(this);
            var value = $input.val();

            if (value.length > 0) {
                $input.css('border-color', '#667eea');
            } else {
                $input.css('border-color', '#e5e7eb');
            }
        });
    });

    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
        } else {
            panel.classList.add('show');
        }
    }
</script>

<include file="block/footer" />
