<include file="block/hat" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 重置和基础样式 */
                * {
                    box-sizing: border-box;
                }

                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    color: #2d3748;
                }

                .setquotation-edit-container {
                    padding: 2rem;
                    background: #f7fafc;
                    min-height: 100vh;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                }

                /* 现代化标签页 */
                .setquotation-tabs {
                    background: white;
                    border-radius: 1rem 1rem 0 0;
                    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    border-bottom: none;
                    overflow: hidden;
                    margin-bottom: 0;
                }

                .setquotation-tab-list {
                    display: flex;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    flex-wrap: wrap;
                }

                .setquotation-tab-item {
                    flex: 1;
                    min-width: 120px;
                }

                .setquotation-tab-link {
                    display: block;
                    padding: 1.5rem 1rem;
                    color: #4a5568;
                    text-decoration: none;
                    font-weight: 600;
                    font-size: 1.5rem;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    border-right: 1px solid #e2e8f0;
                    position: relative;
                    text-align: center;
                }

                .setquotation-tab-item:last-child .setquotation-tab-link {
                    border-right: none;
                }

                .setquotation-tab-link:hover {
                    background: #f7fafc;
                    color: #667eea;
                    text-decoration: none;
                }

                .setquotation-tab-link.active {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .setquotation-tab-link.active::after {
                    content: '';
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                    right: 0;
                    height: 2px;
                    background: white;
                }

                /* 页面头部 */
                .setquotation-page-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 2rem 0;
                    flex-wrap: wrap;
                    gap: 1.5rem;
                }

                .setquotation-page-title-section {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .setquotation-page-title {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .setquotation-page-subtitle {
                    color: #4a5568;
                    font-size: 1.5rem;
                    margin: 0;
                }

                .setquotation-page-actions {
                    display: flex;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                /* 现代化表单卡片 */
                .setquotation-form-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    margin-bottom: 1.5rem;
                }

                .setquotation-form-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .setquotation-form-header {
                    padding: 2rem 2rem 1rem 2rem;
                    border-bottom: 1px solid #f1f5f9;
                }

                .setquotation-form-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .setquotation-form-description {
                    color: #4a5568;
                    font-size: 1.5rem;
                    margin: 0.5rem 0 0 0;
                }

                .setquotation-form-body {
                    padding: 2rem;
                }

                /* 现代化表单组 */
                .setquotation-form-group {
                    margin-bottom: 2rem;
                }

                .setquotation-form-label {
                    display: block;
                    font-weight: 600;
                    color: #2d3748;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .setquotation-form-control {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: white;
                    font-family: inherit;
                }

                .setquotation-form-control:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    background: #fafafa;
                }

                .setquotation-form-control:hover {
                    border-color: #cbd5e0;
                }

                /* 信息展示样式 */
                .setquotation-info-display {
                    background: #f7fafc;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    padding: 0.75rem 1rem;
                    font-size: 1.5rem;
                    color: #2d3748;
                    font-weight: 600;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .setquotation-info-display.project-name {
                    background: #e6fffa;
                    border-color: #81e6d9;
                    color: #234e52;
                }

                .setquotation-info-display.job-name {
                    background: #fef5e7;
                    border-color: #f6ad55;
                    color: #744210;
                }

                .setquotation-info-display.cost-price {
                    background: #f0fff4;
                    border-color: #9ae6b4;
                    color: #22543d;
                }

                /* 表单帮助文本 */
                .setquotation-help-text {
                    font-size: 1.5rem;
                    color: #718096;
                    margin-top: 0.5rem;
                }

                .setquotation-error-text {
                    font-size: 1.5rem;
                    color: #f56565;
                    margin-top: 0.5rem;
                }

                /* 现代化按钮 */
                .setquotation-btn {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    font-size: 1.5rem;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                    font-family: inherit;
                }

                .setquotation-btn:before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .setquotation-btn:hover:before {
                    left: 100%;
                }

                .setquotation-btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .setquotation-btn-primary:hover {
                    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    color: white;
                    text-decoration: none;
                }

                .setquotation-btn-secondary {
                    background: #f1f5f9;
                    color: #4a5568;
                    border: 1px solid #e2e8f0;
                }

                .setquotation-btn-secondary:hover {
                    background: #e2e8f0;
                    transform: translateY(-1px);
                    color: #2d3748;
                    text-decoration: none;
                }

                .setquotation-btn-lg {
                    padding: 1rem 2rem;
                    font-size: 1.5rem;
                }

                /* 表单操作区域 */
                .setquotation-form-actions {
                    padding: 1.5rem 2rem 2rem 2rem;
                    background: #f7fafc;
                    border-top: 1px solid #e2e8f0;
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    flex-wrap: wrap;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .setquotation-edit-container {
                        padding: 1.5rem;
                    }

                    .setquotation-page-header {
                        flex-direction: column;
                        align-items: flex-start;
                    }

                    .setquotation-page-title {
                        font-size: 1.5rem;
                    }

                    .setquotation-form-header,
                    .setquotation-form-body,
                    .setquotation-form-actions {
                        padding: 1.5rem;
                    }

                    .setquotation-form-actions {
                        flex-direction: column;
                    }

                    .setquotation-btn {
                        width: 100%;
                        justify-content: center;
                    }

                    .setquotation-tab-list {
                        flex-direction: column;
                    }

                    .setquotation-tab-item {
                        flex: none;
                    }

                    .setquotation-tab-link {
                        border-right: none;
                        border-bottom: 1px solid #e2e8f0;
                    }

                    .setquotation-tab-item:last-child .setquotation-tab-link {
                        border-bottom: none;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .setquotation-fade-in {
                    animation: fadeInUp 0.5s ease-out;
                }

                .setquotation-fade-in-delay-1 {
                    animation: fadeInUp 0.5s ease-out 0.1s both;
                }

                .setquotation-fade-in-delay-2 {
                    animation: fadeInUp 0.5s ease-out 0.2s both;
                }

                /* 表单验证状态 */
                .setquotation-form-control.error {
                    border-color: #f56565;
                    box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
                }

                .setquotation-form-control.success {
                    border-color: #48bb78;
                    box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
                }

                /* 加载状态 */
                .setquotation-btn.loading {
                    pointer-events: none;
                    opacity: 0.7;
                }

                .setquotation-btn.loading::after {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    margin: auto;
                    border: 2px solid transparent;
                    border-top-color: currentColor;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                /* 价格输入框特殊样式 */
                .setquotation-form-control[name*="projectidentity"] {
                    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
                    border-left: 4px solid #667eea;
                }

                .setquotation-form-control[name*="projectidentity"]:focus {
                    background: white;
                    border-left-color: #48bb78;
                }

                /* 信息展示卡片悬停效果 */
                .setquotation-info-display:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                }
            </style>

            <div class="setquotation-edit-container">
                <!-- 现代化标签页 -->
                <div class="setquotation-tabs">
                    <ul class="setquotation-tab-list">
                        <li class="setquotation-tab-item">
                            <a href="{:U('Project/index')}" class="setquotation-tab-link <php>if(CONTROLLER_NAME=='Project' && ACTION_NAME=='index') echo 'active'</php>">
                                <i class="fa fa-briefcase"></i>
                                项目管理
                            </a>
                        </li>
                        <li class="setquotation-tab-item">
                            <a href="{:U('Project/edit')}" class="setquotation-tab-link <php>if(CONTROLLER_NAME=='Project' && ACTION_NAME=='edit') echo 'active'</php>">
                                <i class="fa fa-<php>echo !I('get.id') ? 'plus' : 'edit';</php>"></i>
                                <php>echo !I('get.id') ? '添加' : '编辑';</php>项目
                            </a>
                        </li>
                        <li class="setquotation-tab-item">
                            <a href="{:U('Projectpost/index')}" class="setquotation-tab-link <php>if(CONTROLLER_NAME=='Projectpost' && ACTION_NAME=='index') echo 'active'</php>">
                                <i class="fa fa-users"></i>
                                岗位管理
                            </a>
                        </li>
                        <li class="setquotation-tab-item">
                            <a href="{:U('Projectpost/edit')}" class="setquotation-tab-link <php>if(CONTROLLER_NAME=='Projectpost' && ACTION_NAME=='edit') echo 'active'</php>">
                                <i class="fa fa-<php>echo !I('get.id') ? 'plus' : 'edit';</php>"></i>
                                <php>echo !I('get.id') ? '添加' : '编辑';</php>岗位
                            </a>
                        </li>
                        <li class="setquotation-tab-item">
                            <a href="{:U('Project/quotation')}" class="setquotation-tab-link <php>if(ACTION_NAME=='quotation') echo 'active'</php>">
                                <i class="fa fa-calculator"></i>
                                报价管理
                            </a>
                        </li>
                        <li class="setquotation-tab-item">
                            <a href="#" class="setquotation-tab-link <php>if(ACTION_NAME=='setquotation') echo 'active'</php>">
                                <i class="fa fa-edit"></i>
                                设置报价
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 页面头部 -->
                <div class="setquotation-page-header" style="margin-top: 2rem;">
                    <div class="setquotation-page-title-section">
                        <h1 class="setquotation-page-title">
                            <i class="fa fa-calculator"></i>
                            设置岗位报价
                        </h1>
                        <p class="setquotation-page-subtitle">
                            为不同身份类型设置岗位的报价信息
                        </p>
                    </div>
                    <div class="setquotation-page-actions">
                        <a href="{:U('Project/quotation')}" class="setquotation-btn setquotation-btn-secondary">
                            <i class="fa fa-arrow-left"></i>
                            返回报价管理
                        </a>
                    </div>
                </div>

                <!-- 现代化表单 -->
                <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="setquotation-form">
                    <!-- 基本信息卡片 -->
                    <div class="setquotation-form-card">
                        <div class="setquotation-form-header">
                            <h2 class="setquotation-form-title">
                                <i class="fa fa-info-circle"></i>
                                岗位基本信息
                            </h2>
                            <p class="setquotation-form-description">
                                查看当前岗位的基本信息
                            </p>
                        </div>

                        <div class="setquotation-form-body">
                            <!-- 项目名称 -->
                            <div class="setquotation-form-group">
                                <label class="setquotation-form-label">
                                    <i class="fa fa-briefcase"></i>
                                    项目名称
                                </label>
                                <div class="setquotation-info-display project-name">
                                    <i class="fa fa-folder"></i>
                                    {:$projectList[$row['project_id']]}
                                </div>
                                <div class="setquotation-help-text">当前岗位所属的项目</div>
                            </div>

                            <!-- 岗位名称 -->
                            <div class="setquotation-form-group">
                                <label class="setquotation-form-label">
                                    <i class="fa fa-user-circle"></i>
                                    岗位名称
                                </label>
                                <div class="setquotation-info-display job-name">
                                    <i class="fa fa-user"></i>
                                    {$row.job_name}
                                </div>
                                <div class="setquotation-help-text">当前设置报价的岗位</div>
                            </div>

                            <!-- 成本价 -->
                            <div class="setquotation-form-group">
                                <label class="setquotation-form-label">
                                    <i class="fa fa-money"></i>
                                    内部成本价
                                </label>
                                <div class="setquotation-info-display cost-price">
                                    <i class="fa fa-calculator"></i>
                                    ￥{$row.internal_costs}
                                </div>
                                <div class="setquotation-help-text">该岗位的内部成本价格，作为报价参考</div>
                            </div>
                        </div>
                    </div>

                    <!-- 报价设置卡片 -->
                    <div class="setquotation-form-card">
                        <div class="setquotation-form-header">
                            <h2 class="setquotation-form-title">
                                <i class="fa fa-tags"></i>
                                身份报价设置
                            </h2>
                            <p class="setquotation-form-description">
                                为不同身份类型设置相应的报价金额
                            </p>
                        </div>

                        <div class="setquotation-form-body">
                            <php>
                                foreach($projectIdentityList as $projectIdentitRow) {
                                $projectIdentitJoinCost = $projectJoinIdentity[$row['id']][$projectIdentitRow['id']] ?:0;
                            </php>
                            <!-- 身份报价项 -->
                            <div class="setquotation-form-group">
                                <label class="setquotation-form-label">
                                    <i class="fa fa-tag"></i>
                                    {:$projectIdentitRow['name']}报价
                                </label>
                                <input type="text"
                                       name="projectidentity[{:$projectIdentitRow['id']}]"
                                       class="setquotation-form-control"
                                       value="{$projectIdentitJoinCost}"
                                       placeholder="请输入{:$projectIdentitRow['name']}的报价金额" />
                                <div class="setquotation-help-text">为{:$projectIdentitRow['name']}身份设置的报价金额（单位：元）</div>
                            </div>
                            <php>}</php>
                        </div>
                    </div>

                    <!-- 表单操作区域 -->
                    <div class="setquotation-form-actions">
                        <input type="hidden" name="id" value="{$row.id}" />
                        <button type="button" onclick="history.back()" class="setquotation-btn setquotation-btn-secondary">
                            <i class="fa fa-times"></i>
                            取消
                        </button>
                        <button type="submit" name="submit" class="setquotation-btn setquotation-btn-primary setquotation-btn-lg">
                            <i class="fa fa-save"></i>
                            保存报价设置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="__ROOT__/static/js/prime/common.js"></script>
<link rel="stylesheet" href="__ROOT__/static/js/app/layer/skin/layer.css" />

<script>
	require(['layer', 'util'], function(layer, u) {
		$(function(){
			// 初始化富文本编辑器（如果需要）
			u.editor($('.richtext')[0]);
			u.editor($('.richtext1')[0]);

			// 表单验证和交互
			var $form = $('#setquotation-form');
			var $submitBtn = $('button[type="submit"]');

			// 表单字段实时验证
			$('.setquotation-form-control').on('input blur', function() {
				var $field = $(this);
				var value = $field.val().trim();
				var fieldName = $field.attr('name');

				// 移除之前的验证状态
				$field.removeClass('error success');
				$field.siblings('.setquotation-error-text').remove();

				// 报价字段验证
				if (fieldName && fieldName.indexOf('projectidentity') !== -1) {
					var isValid = true;
					var errorMsg = '';

					if (value) {
						// 验证是否为有效的金额格式
						if (!/^\d+(\.\d{1,2})?$/.test(value)) {
							isValid = false;
							errorMsg = '请输入有效的金额格式（如：100 或 100.50）';
						} else if (parseFloat(value) < 0) {
							isValid = false;
							errorMsg = '报价金额不能为负数';
						} else if (parseFloat(value) > 999999) {
							isValid = false;
							errorMsg = '报价金额不能超过999999元';
						}
					}

					if (!isValid) {
						$field.addClass('error');
						$field.after('<div class="setquotation-error-text"><i class="fa fa-exclamation-circle"></i> ' + errorMsg + '</div>');
					} else if (value) {
						$field.addClass('success');
					}
				}
			});

			// 表单提交处理
			$form.on('submit', function(e) {
				e.preventDefault();

				// 验证所有报价字段
				var hasError = false;
				var hasValue = false;

				$('input[name*="projectidentity"]').each(function() {
					var $field = $(this);
					var value = $field.val().trim();

					if (value) {
						hasValue = true;
						// 验证金额格式
						if (!/^\d+(\.\d{1,2})?$/.test(value) || parseFloat(value) < 0 || parseFloat(value) > 999999) {
							$field.addClass('error');
							$field.siblings('.setquotation-error-text').remove();
							$field.after('<div class="setquotation-error-text"><i class="fa fa-exclamation-circle"></i> 请输入有效的金额格式</div>');
							hasError = true;
						}
					}
				});

				// 检查是否有验证错误
				if ($('.setquotation-form-control.error').length > 0) {
					hasError = true;
				}

				// 检查是否至少设置了一个报价
				if (!hasValue) {
					layer.msg('请至少设置一个身份的报价', {icon: 2});
					return false;
				}

				if (hasError) {
					layer.msg('请修正表单中的错误信息', {icon: 2});
					// 滚动到第一个错误字段
					var $firstError = $('.setquotation-form-control.error').first();
					if ($firstError.length) {
						$('html, body').animate({
							scrollTop: $firstError.offset().top - 100
						}, 500);
						$firstError.focus();
					}
					return false;
				}

				// 设置提交按钮为加载状态
				$submitBtn.addClass('loading');
				$submitBtn.prop('disabled', true);
				var originalText = $submitBtn.html();
				$submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 保存中...');

				// 提交表单
				$.ajax({
					url: $form.attr('action') || window.location.href,
					type: 'POST',
					data: $form.serialize(),
					dataType: 'json',
					success: function(response) {
						if (response.status === 1 || response.code === 1) {
							layer.msg(response.info || '报价设置成功', {icon: 1}, function() {
								window.location.href = "{:U('Project/quotation')}";
							});
						} else {
							layer.msg(response.info || '操作失败', {icon: 2});
						}
					},
					error: function() {
						layer.msg('网络错误，请重试', {icon: 2});
					},
					complete: function() {
						// 恢复提交按钮状态
						$submitBtn.removeClass('loading');
						$submitBtn.prop('disabled', false);
						$submitBtn.html(originalText);
					}
				});
			});

			// 页面加载动画
			setTimeout(function() {
				$('.setquotation-form-card').addClass('setquotation-fade-in');
			}, 100);

			// 表单字段聚焦效果
			$('.setquotation-form-control').on('focus', function() {
				$(this).closest('.setquotation-form-group').addClass('focused');
			}).on('blur', function() {
				$(this).closest('.setquotation-form-group').removeClass('focused');
			});

			// 键盘快捷键
			$(document).on('keydown', function(e) {
				// Ctrl+S 保存
				if (e.ctrlKey && e.keyCode === 83) {
					e.preventDefault();
					$form.submit();
				}
				// Esc 取消
				if (e.keyCode === 27) {
					if (confirm('确定要取消设置吗？未保存的更改将丢失。')) {
						history.back();
					}
				}
			});

			// 表单数据变化检测
			var originalFormData = $form.serialize();
			var hasUnsavedChanges = false;

			$('.setquotation-form-control').on('input change', function() {
				hasUnsavedChanges = ($form.serialize() !== originalFormData);
			});

			// 页面离开提醒
			$(window).on('beforeunload', function() {
				if (hasUnsavedChanges) {
					return '您有未保存的更改，确定要离开吗？';
				}
			});

			// 成功提交后清除未保存标记
			$form.on('submit', function() {
				hasUnsavedChanges = false;
			});

			// 报价输入框特殊效果
			$('input[name*="projectidentity"]').on('input', function() {
				var $input = $(this);
				var value = $input.val();
				var $helpText = $input.siblings('.setquotation-help-text');

				if (value && /^\d+(\.\d{1,2})?$/.test(value)) {
					var amount = parseFloat(value);
					if (amount > 0) {
						$helpText.html('报价金额：￥' + amount.toFixed(2) + ' <i class="fa fa-check-circle" style="color: #48bb78;"></i>');
					}
				} else if (value) {
					$helpText.html('请输入有效的金额格式 <i class="fa fa-exclamation-triangle" style="color: #ed8936;"></i>');
				} else {
					// 恢复原始帮助文本
					var labelText = $input.closest('.setquotation-form-group').find('.setquotation-form-label').text().trim();
					$helpText.text('为' + labelText.replace('报价', '') + '身份设置的报价金额（单位：元）');
				}
			});

			// 信息展示卡片点击效果
			$('.setquotation-info-display').click(function() {
				var $display = $(this);
				$display.css('transform', 'scale(0.98)');
				setTimeout(function() {
					$display.css('transform', 'scale(1)');
				}, 150);
			});

			// 自动格式化金额输入
			$('input[name*="projectidentity"]').on('blur', function() {
				var $input = $(this);
				var value = $input.val().trim();

				if (value && /^\d+(\.\d{1,2})?$/.test(value)) {
					var amount = parseFloat(value);
					if (amount > 0) {
						$input.val(amount.toFixed(2));
					}
				}
			});

			// 计算总报价
			function calculateTotalQuotation() {
				var total = 0;
				var count = 0;

				$('input[name*="projectidentity"]').each(function() {
					var value = $(this).val().trim();
					if (value && /^\d+(\.\d{1,2})?$/.test(value)) {
						total += parseFloat(value);
						count++;
					}
				});

				if (count > 0) {
					console.log('报价统计：');
					console.log('设置身份数：' + count);
					console.log('总报价金额：￥' + total.toFixed(2));
					console.log('平均报价：￥' + (total / count).toFixed(2));
				}
			}

			// 实时计算报价统计
			$('input[name*="projectidentity"]').on('input', function() {
				setTimeout(calculateTotalQuotation, 100);
			});

			// 初始计算
			calculateTotalQuotation();
		});
	});
</script>

<include file="block/footer" />