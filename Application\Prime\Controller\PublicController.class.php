<?php

namespace Prime\Controller;
use Common\Controller\PrimeController;

class PublicController extends PrimeController
{
    public function _initialize() {

    }

    public function login()
    {
    	if($this->loggedIn()) {
            $this->redirect('index/index');
    	} else {
            if (IS_POST) {
                $this->dologin();
            } else {
                $this->display();
            }
    	}
    }

    private function dologin()
    {
//        if (! $this->checkCode(I('post.verifycode'))) $this->error("验证码错误");
    	$Musers = D("Users");
        $username = I('post.username');
        $pwd = I('post.pwd');
        if(empty($username) || empty($pwd)) {
            $this->error('请输入用户名或密码');
        }
        $where['username'] = $username;
        $res = $Musers->where($where)->find();
        if(! empty($res) && $res['status'] > 0) {
            if(comparePwd($pwd, $res['pwd'], $res['salt'])) {
                $Musers->login($res);
                $this->redirect('index/index');
            } else {
                $this->error('用户名或密码有误');
            }
        } else {
            $this->error('用户名或密码有误');
        }
    }

    public function logout()
    {
        D('Users')->logout();
        $this->redirect("public/login");
    }

    /**
     * 字段验证是否存在
     */
    public function check()
    {
        if ($val = I('post.nickname')) {
            $field = 'username';
        } else if ($val = I('post.mailbox')) {
            $field = 'email';
        }
        if (empty($field) || empty($val)) die('0');

        $m_admin = D('Admin');
        $res = $m_admin->getByField($field, $val);
        echo $res ? '0' : 'yes';
    }
    
}