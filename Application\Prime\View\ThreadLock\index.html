<extend name="Public/base"/>

<block name="content">
<div class="row">
    <div class="col-xs-12">
        <div class="box">
            <div class="box-header">
                <h3 class="box-title">线程锁管理</h3>
                <div class="box-tools">
                    <a href="{:U('clean')}" class="btn btn-sm btn-primary"><i class="fa fa-trash"></i> 清理过期锁</a>
                </div>
            </div>
            <div class="box-body table-responsive">
                <notempty name="cleaned">
                    <div class="alert alert-success">
                        自动清理了 {$cleaned} 个过期锁
                    </div>
                </notempty>
                
                <table class="table table-hover">
                    <tr>
                        <th>ID</th>
                        <th>锁名称</th>
                        <th>锁定时间</th>
                        <th>过期时间</th>
                        <th>创建时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                    <volist name="locks" id="lock">
                        <tr class="<eq name='lock.is_expired' value='1'>text-muted</eq>">
                            <td>{$lock.id}</td>
                            <td>{$lock.lock_name}</td>
                            <td>{$lock.lock_time_format}</td>
                            <td>{$lock.expire_time_format}</td>
                            <td>{$lock.created_at_format}</td>
                            <td>
                                <eq name="lock.is_expired" value="1">
                                    <span class="label label-danger">已过期</span>
                                <else />
                                    <span class="label label-success">有效</span>
                                </eq>
                            </td>
                            <td>
                                <a href="{:U('release', array('id'=>$lock['id']))}" class="btn btn-xs btn-danger" onclick="return confirm('确定要释放该锁？')">释放锁</a>
                            </td>
                        </tr>
                    </volist>
                </table>
                
                <empty name="locks">
                    <div class="alert alert-info">
                        暂无线程锁记录
                    </div>
                </empty>
            </div>
            <div class="box-footer clearfix">
                <div class="pull-right">
                    {$page}
                </div>
            </div>
        </div>
    </div>
</div>
</block> 