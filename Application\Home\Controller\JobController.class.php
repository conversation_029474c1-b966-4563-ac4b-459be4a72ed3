<?php

namespace Home\Controller;

use Common\Controller\WController;

class JobController extends WController
{

    public $userJobRows = [];

    public function _initialize()
    {
        parent::_initialize();
        $this->login();
        $openid = session('openid');
        $userRow = D("User")->where(['openid' => $openid, 'service' => 2])->find();
        if (!$userRow) {
            session(null);
            return $this->redirect(U('/job/index'));
            die;
        }
        $this->userRow = $userRow;
        if ((ACTION_NAME != 'index' && ACTION_NAME != 'setjob') && CONTROLLER_NAME == 'Job') {
            $userJobRows = D("UserJob")->where(['user_id' => $this->userRow['id']])->find();
            if (!$userJobRows) $this->error('系统繁忙，请稍后再试！！', U('/job/index'));
            $this->userJobRows = $userJobRows;
        }
    }

    /**
     * 简历显示
     */
    public function index()
    {
        $userJobRow = D("UserJob")->where(['user_id' => $this->userRow['id']])->find();
        $this->assign('userJobRow', $userJobRow);

        $userModulesList = D("ModulesContent")->where(['user_id' => $this->userRow['id']])->select();
        $this->assign('userModulesList', $userModulesList);
        $typeList = [
            '1' => '教育经历',
            '2' => '家庭成员',
            '3' => '工作经历',
            '4' => '职业技能',
        ];
        $this->assign('typeList', $typeList);
        $this->display();
    }

    /**
     * 某块列表
     */
    public function modules()
    {
        $userModulesList = D("ModulesContent")->where(['user_id' => $this->userRow['id']])->select();
        $this->assign('userModulesList', $userModulesList);
        $typeList = [
            '1' => '教育经历',
            '2' => '家庭成员',
            '3' => '工作经历',
            '4' => '职业技能',
        ];
        $this->assign('typeList', $typeList);
        $this->display();
    }

    /**
     * 更新某块位置
     */
    public function updatemodules()
    {
        if (IS_POST) {
            $modulesList = I('post.');
            foreach ($modulesList as $row) {
                D("UserModules")->where(['user_id' => $this->userRow['id'], 'id' => $row['id']])->save(['num' => $row['num']]);
            }
            return $this->success('更新成功');
        }
    }

    /**
     * 添加简历
     */
    public function setjob()
    {
        $userJobRow = D("UserJob")->where(['user_id' => $this->userRow['id']])->find();
        $this->assign('userJobRow', $userJobRow);
        if (IS_POST) {
            $data = I('post.');
            if (!empty($_FILES['photo_path']['name'])) {
                $upload = new \Think\Upload();
                $upload->maxSize = 1048576; // 1MB
                $upload->exts = array('jpg', 'png', 'jpeg');
                $upload->rootPath = SITE_PATH . '/data/';
                $upload->savePath = 'Job/';
                $info = $upload->uploadOne($_FILES['photo_path']);
                if ($info) {
                    $data['photo_path'] = "/data/" . $info['savepath'] . $info['savename'];
                } else {
                    $this->error($upload->getError());
                }
            }
            $updateData = [
                'photo_path' => $data['photo_path'] ?: '',
                'name' => $data['name'] ?: '',
                'gender' => $data['gender'],
                'phone' => $data['phone'] ?: '',
                'birthdate' => $data['birthdate'] ? $data['birthdate'] . "-01" : '',
                'marital_status' => $data['marital_status'] ?: '',
                'id_number' => $data['id_number'] ?: '',
                'registered' => $data['registered'] ?: '',
                'nation' => $data['nation'] ?: '',
                'political_status' => $data['political_status'] ?: '',
                'health_status' => $data['health_status'] ?: '',
                'height' => (int)$data['height'] ?: 0,
                'weight' => (int)$data['weight'] ?: 0,
                'vision' => $data['vision'] ?: '',
                'hearing' => $data['hearing'] ?: '',
                'is_afraid_heights' => $data['is_afraid_heights'] == '恐高' ? 1 : 0,
                'applied_position' => $data['applied_position'] ?: '',
                'education_level' => $data['education_level'] ?: '',
                'professional' => $data['professional'] ?: '',
                'graduate_school' => $data['graduate_school'] ?: '',
                'major' => $data['major'] ?: '',
                'graduation_time' => $data['graduation_time'] ? $data['graduation_time'] . "-01" : '',
                'time_to_work' => $data['time_to_work'] ? $data['time_to_work'] . "-01" : '',
                'update_time' => time(),
            ];
            $row = D("UserJob")->where(['user_id' => $this->userRow['id']])->find();
            try {
                if (!$row) {
                    $updateData['create_time'] = time();
                    $updateData['service_station_id'] = $this->userRow['service_station_id'];
                    $updateData['user_id'] = $this->userRow['id'];
                    D("UserJob")->add($updateData);
                } else {
                    $updateData['id'] = $row['id'];
                    D("UserJob")->save($updateData);
                }
                return $this->success('保存成功', U('job/index'));
            } catch (\Exception $e) {
                return $this->error('保存失败', U('job/index'));
            }
        }
        $this->display();
    }


    /**
     * 设置期望
     */
    public function setexpected()
    {
        $userJobRows = $this->userJobRows;
        if (IS_POST) {
            $data = I('post.');
            $updateData = [
                'expected_position' => $data['expected_position'] ?: '',
                'expected_address' => $data['expected_address'] ?: '',
                'expected_end_salary' => $data['expected_end_salary'] ?: '',
                'job_seeking_status' => $data['job_seeking_status'] ?: '',
                'id' => $userJobRows['id'],
            ];
            try {
                D("UserJob")->save($updateData);
                return $this->success('保存成功');
            } catch (\Exception $e) {
                return $this->error('保存失败');
            }
        }
    }

    /**
     * 添加模块内容
     */
    public function addmodulesbox()
    {
        $type = I('get.type', 1);
        $id = I('get.id', 0);

        $typeList = [
            '1' => '教育经历',
            '2' => '家庭成员',
            '3' => '工作经历',
            '4' => '职业技能',
        ];
        $this->assign('type', $type);
        $this->assign('typeList', $typeList);
        switch ($type) {
            case 1 :
                $obj = D("Education");
                break;
            case 2 :
                $obj = D("FamilyMembers");
                break;
            case 3 :
                $obj = D("WorkExperience");
                break;
            case 4 :
                $obj = D("SkillsCertificates");
                break;
        }
        $userJobContent = D("UserJob")->where(['user_id' => $this->userRow['id']])->find();
        $userJobRow = [];
        if ($id) {
            $userJobRow = $obj->where(['id' => $id, 'user_id' => $this->userRow['id']])->find();
            $this->assign('userJobRow', $userJobRow);
        }

        if (IS_POST) {
            $data = I('post.');
            $dbdata = [];
            $modulesName = '';
            switch ($type) {
                case 1 :
                    $dbdata = [
                        'start_date' => $data['start_date'] ? $data['start_date']."-01" : '',
                        'end_date' => $data['end_date'] ? $data['end_date']."-01" : '',
                        'school_name' => $data['school_name']?:'',
                        'major' => $data['major']?:'',
                        'witness' => $data['witness'] ? : '',
                    ];
                    $modulesName = $data['school_name']?:'';
                    break;
                case 2 :
                    $dbdata = [
                        'relationship' => $data['relationship']?:'',
                        'full_name' => $data['full_name']?:'',
                        'work_unit' => $data['work_unit'] ? : '',
                        'position' => $data['position'] ? : '',
                        'contact' => $data['contact'] ? : '',
                    ];
                    $modulesName = $data['full_name']?:'';
                    break;
                case 3 :
                    $dbdata = [
                        'start_date' => $data['start_date'] ? $data['start_date']."-01" : '',
                        'end_date' => $data['end_date'] ? $data['end_date']."-01" : '',
                        'company_name' => $data['company_name']?:'',
                        'position' => $data['position']?:'',
                        'leave_reason' => $data['leave_reason'] ? : '',
                    ];
                    $modulesName = $data['company_name']?:'';
                    break;
                case 4 :
                    $dbdata = [
                        'certificate_type' => $data['certificate_type']?:'',
                        'certificate_name' => $data['certificate_name']?:'',
                        'level' => $data['level'] ? : '',
                        'get_date' => $data['get_date'] ? $data['get_date']."-01" : '',
                    ];
                    $modulesName = $data['certificate_name']?:'';
                    break;
            }
            $modulesRows = [];
            if ($userJobRow) {
                $dbdata['id'] = $userJobRow['id'];
                $insertId = $userJobRow['id'];
            } else {
                $dbdata['user_id'] = $this->userRow['id'];
                $dbdata['user_job_id'] = $userJobContent['id'];
                $dbdata['create_time'] = time();
                $insertId = $obj->add($dbdata);
            }
            $modulesContentRow = D("ModulesContent")->where(['modules_type' => $type, 'user_id' => $this->userRow['id'], 'relation_id' => $insertId])->find();
            if ($modulesContentRow) {
                $updateData = [
                    'id' => $modulesContentRow['id'],
                    'modules_type' => $type,
                    'content1' => $modulesName ?: '',
                ];
                if (D("ModulesContent")->save($updateData)) {
                    return $this->success('添加成功', U('job/index'));
                } else {
                    return $this->success('添加失败', U('job/index'));
                }
            } else {
                $updateData = [
                    'modules_type' => $type,
                    'relation_id' => $insertId,
                    'user_id' => $this->userRow['id'],
                    'content1' => $modulesName ?: '',
                    'user_job_id' => $userJobContent['id'],
                    'create_time' => time(),
                ];
                if (D("ModulesContent")->add($updateData)) {
                    return $this->success('添加成功', U('job/index'));
                } else {
                    return $this->success('添加失败', U('job/index'));
                }
            }
        }
        $this->display();
    }

    /**
     * 更新模块
     */
    public function updatemodulesbox()
    {
        $id = I('get.id');
        $modulesContentRow = D("ModulesContent")->where(['user_id' => $this->userRow['id'], 'id' => $id])->find();
        if (!$modulesContentRow) {
            return $this->error('参数错误', U('job/index'));
        }
        if (IS_POST) {
            $userJobRows = $this->userJobRows;
            $data = I('post.');
            $updateData = [
                'id' => $modulesContentRow['id'],
                'modules_type' => $data['modules_type'],
                'user_id' => $this->userRow['id'],
                'start_time' => $data['start_time'] ?: '',
                'end_time' => $data['end_time'] ?: '',
                'content1' => $data['content1'] ?: '',
                'content2' => $data['content2'] ?: '',
                'content3' => $data['content3'] ?: '',
                'content_text' => $data['content_text'] ?: '',
                'create_time' => time(),
            ];
            if (D("ModulesContent")->add($updateData)) {
                return $this->success('更新成功');
            } else {
                return $this->error('更新失败');
            }
        }
        $this->assign('modulesContentRow', $modulesContentRow);
        $this->display();
    }

    /**
     * other 添加爱好
     */
    public function addotherjob()
    {
        $type = I('get.type', 0);
        if (!$type) $this->error('参数错误');
        $userJobRows = $this->userJobRows;
        $typeList = [
            '1' => '兴趣爱好',
            '2' => '奖励证书',
            '3' => '职业技能',
        ];
        $this->assign('typeList', $typeList);
        $this->assign('type', $type);
        if (IS_POST) {
            $data = I('post.');
            $insertData = [
                'user_id' => $this->userRow['id'],
                'user_job_id' => $userJobRows['id'],
                'type' => $type ?: 0,
                'content' => $data['content'] ?: '',
                'create_time' => time(),
            ];
            if (D("UserJobOther")->add($insertData)) {
                return $this->success('添加成功', U('/job/index'));
            } else {
                return $this->success('添加失败', U('/job/index'));
            }
        }
        $this->display();
    }

    /**
     *  other 更新爱好
     */
    public function upotherjob()
    {
        $userJobRows = $this->userJobRows;
        $id = I('get.id');
        $userJobOtherRow = D("UserJobOther")->where(['user_id' => $this->userRow['id'], 'id' => $id])->find();
        if (!$userJobOtherRow) {
            return $this->error('参数错误', U('job/index'));
        }
        if (IS_POST) {
            $data = I('post.');
            $udpateData = [
                'content' => $data['content'] ?: '',
                'id' => $userJobOtherRow['id'],
            ];
            if (D("UserJobOther")->save($udpateData)) {
                return $this->success('更新成功');
            } else {
                return $this->success('更新失败');
            }
        }
        $this->display();
    }

    /**
     * 自我评价
     */
    public function setselfevaluation()
    {
        $userJobRows = $this->userJobRows;
        if (IS_POST) {
            $data = I('post.');
            $self_evaluation = $data['self_evaluation'] ?: '';
            D("UserJob")->save(['id' => $userJobRows, 'self_evaluation' => $self_evaluation]);
            return $this->success('保存评价成功', U('job/index'));
        }
        $this->display();
    }


}
