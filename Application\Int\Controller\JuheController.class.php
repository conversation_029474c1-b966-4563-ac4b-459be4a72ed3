<?php

namespace Int\Controller;

use Think\Controller;

class JuheController extends Controller
{
    public function _initialize()
    {

    }

    /**
     * 流量充值回调
     */
    public function flow()
    {
        extract(I('post.')); // sporder_id orderid sta sign err_msg
        $obj = new \Api\Juhe\Flow();
        if (! $obj->chkCallback($sporder_id, $orderid, $sign)) {
            dolog('error/int_juhe/flow', "data:".serialize(I('post.')));
            exit;
        }
        $row = D('Order')->find($orderid);
        dolog('info/int_juhe/flow', serialize(I('post.'))); // 前期记录推送
        if (! $row) {
            dolog('error/int_juhe/flow', "order not exist, $sporder_id");
            exit;
        }
        if ($row['status'] != $sta) {
            $data = ['id'=>$orderid, 'status'=>$sta, 'updated'=>time()];
            D('Order')->save($data);
        }
        echo 'success';
    }

    /**
     * 话费充值回调
     */
    public function mobile()
    {
        extract(I('post.')); // sporder_id orderid sta sign err_msg
        $obj = new \Api\Juhe\Mobile();
        if (! $obj->chkCallback($sporder_id, $orderid, $sign)) {
            dolog('error/int_juhe/mobile', "data:".serialize(I('post.')));
            exit;
        }
        $row = D('Order')->find($orderid);
        dolog('info/int_juhe/mobile', serialize(I('post.'))); // 前期记录推送
        if (! $row) {
            dolog('error/int_juhe/mobile', "order not exist, $sporder_id");
            exit;
        }
        if ($row['status'] != $sta) {
            $data = ['id'=>$orderid, 'status'=>$sta, 'updated'=>time()];
            D('Order')->save($data);
        }
        echo 'success';
    }

}
