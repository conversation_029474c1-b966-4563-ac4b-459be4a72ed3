<include file="block/hat" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 重置和基础样式 */
                * {
                    box-sizing: border-box;
                }

                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    color: #2d3748;
                }

                .identity-edit-container {
                    padding: 2rem;
                    background: #f7fafc;
                    min-height: 100vh;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                }

                /* 现代化标签页 */
                .identity-edit-tabs {
                    background: white;
                    border-radius: 1rem 1rem 0 0;
                    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    border-bottom: none;
                    overflow: hidden;
                    margin-bottom: 0;
                }

                .identity-edit-tab-list {
                    display: flex;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    flex-wrap: wrap;
                }

                .identity-edit-tab-item {
                    flex: 1;
                    min-width: 120px;
                }

                .identity-edit-tab-link {
                    display: block;
                    padding: 1.5rem 1rem;
                    color: #4a5568;
                    text-decoration: none;
                    font-weight: 600;
                    font-size: 1.5rem;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    border-right: 1px solid #e2e8f0;
                    position: relative;
                    text-align: center;
                }

                .identity-edit-tab-item:last-child .identity-edit-tab-link {
                    border-right: none;
                }

                .identity-edit-tab-link:hover {
                    background: #f7fafc;
                    color: #667eea;
                    text-decoration: none;
                }

                .identity-edit-tab-link.active {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .identity-edit-tab-link.active::after {
                    content: '';
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                    right: 0;
                    height: 2px;
                    background: white;
                }

                /* 页面头部 */
                .identity-edit-page-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 2rem 0;
                    flex-wrap: wrap;
                    gap: 1.5rem;
                }

                .identity-edit-page-title-section {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .identity-edit-page-title {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .identity-edit-page-subtitle {
                    color: #4a5568;
                    font-size: 1.5rem;
                    margin: 0;
                }

                .identity-edit-page-actions {
                    display: flex;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                /* 现代化表单卡片 */
                .identity-edit-form-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    margin-bottom: 1.5rem;
                }

                .identity-edit-form-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .identity-edit-form-header {
                    padding: 2rem 2rem 1rem 2rem;
                    border-bottom: 1px solid #f1f5f9;
                }

                .identity-edit-form-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .identity-edit-form-description {
                    color: #4a5568;
                    font-size: 1.5rem;
                    margin: 0.5rem 0 0 0;
                }

                .identity-edit-form-body {
                    padding: 2rem;
                }

                /* 现代化表单组 */
                .identity-edit-form-group {
                    margin-bottom: 2rem;
                }

                .identity-edit-form-label {
                    display: block;
                    font-weight: 600;
                    color: #2d3748;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .required {
                    color: #f56565;
                    font-weight: 700;
                }

                .identity-edit-form-control {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: white;
                    font-family: inherit;
                }

                .identity-edit-form-control:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    background: #fafafa;
                }

                .identity-edit-form-control:hover {
                    border-color: #cbd5e0;
                }

                .identity-edit-form-control.error {
                    border-color: #f56565;
                    box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
                }

                .identity-edit-form-control.success {
                    border-color: #48bb78;
                    box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
                }

                /* 下拉选择框样式 */
                .identity-edit-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                /* 富文本编辑器容器 */
                .identity-edit-richtext-container {
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .identity-edit-richtext-container:focus-within {
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .identity-edit-textarea {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: none;
                    font-size: 1.5rem;
                    font-family: inherit;
                    resize: vertical;
                    min-height: 120px;
                }

                .identity-edit-textarea:focus {
                    outline: none;
                }

                /* 表单帮助文本 */
                .identity-edit-help-text {
                    font-size: 1.5rem;
                    color: #718096;
                    margin-top: 0.5rem;
                }

                .identity-edit-error-text {
                    font-size: 1.5rem;
                    color: #f56565;
                    margin-top: 0.5rem;
                }

                /* 现代化按钮 */
                .identity-edit-btn {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    font-size: 1.5rem;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                    font-family: inherit;
                }

                .identity-edit-btn:before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .identity-edit-btn:hover:before {
                    left: 100%;
                }

                .identity-edit-btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .identity-edit-btn-primary:hover {
                    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    color: white;
                    text-decoration: none;
                }

                .identity-edit-btn-secondary {
                    background: #f1f5f9;
                    color: #4a5568;
                    border: 1px solid #e2e8f0;
                }

                .identity-edit-btn-secondary:hover {
                    background: #e2e8f0;
                    transform: translateY(-1px);
                    color: #2d3748;
                    text-decoration: none;
                }

                .identity-edit-btn-lg {
                    padding: 1rem 2rem;
                    font-size: 1.5rem;
                }

                /* 表单操作区域 */
                .identity-edit-form-actions {
                    padding: 1.5rem 2rem 2rem 2rem;
                    background: #f7fafc;
                    border-top: 1px solid #e2e8f0;
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    flex-wrap: wrap;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .identity-edit-container {
                        padding: 1.5rem;
                    }

                    .identity-edit-page-header {
                        flex-direction: column;
                        align-items: flex-start;
                    }

                    .identity-edit-page-title {
                        font-size: 1.5rem;
                    }

                    .identity-edit-form-header,
                    .identity-edit-form-body,
                    .identity-edit-form-actions {
                        padding: 1.5rem;
                    }

                    .identity-edit-form-actions {
                        flex-direction: column;
                    }

                    .identity-edit-btn {
                        width: 100%;
                        justify-content: center;
                    }

                    .identity-edit-tab-list {
                        flex-direction: column;
                    }

                    .identity-edit-tab-item {
                        flex: none;
                    }

                    .identity-edit-tab-link {
                        border-right: none;
                        border-bottom: 1px solid #e2e8f0;
                    }

                    .identity-edit-tab-item:last-child .identity-edit-tab-link {
                        border-bottom: none;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .identity-edit-fade-in {
                    animation: fadeInUp 0.5s ease-out;
                }

                .identity-edit-fade-in-delay-1 {
                    animation: fadeInUp 0.5s ease-out 0.1s both;
                }

                .identity-edit-fade-in-delay-2 {
                    animation: fadeInUp 0.5s ease-out 0.2s both;
                }

                /* 表单验证状态 */
                .identity-edit-form-group.focused .identity-edit-form-label {
                    color: #667eea;
                }

                .identity-edit-form-group.error .identity-edit-form-label {
                    color: #f56565;
                }

                .identity-edit-form-group.success .identity-edit-form-label {
                    color: #48bb78;
                }

                /* 加载状态 */
                .identity-edit-btn.loading {
                    pointer-events: none;
                    opacity: 0.7;
                }

                .identity-edit-btn.loading::after {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    margin: auto;
                    border: 2px solid transparent;
                    border-top-color: currentColor;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                /* 身份层级预览 */
                .identity-edit-hierarchy-preview {
                    background: #f0fff4;
                    border: 1px solid #9ae6b4;
                    border-radius: 0.5rem;
                    padding: 1rem;
                    margin-top: 0.5rem;
                    font-size: 1.5rem;
                    color: #22543d;
                }

                .identity-edit-hierarchy-preview .hierarchy-path {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    flex-wrap: wrap;
                }

                .identity-edit-hierarchy-preview .hierarchy-item {
                    background: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 0.375rem;
                    border: 1px solid #c6f6d5;
                }

                .identity-edit-hierarchy-preview .hierarchy-arrow {
                    color: #38a169;
                }
            </style>

            <div class="identity-edit-container">
                <!-- 现代化标签页 -->
                <div class="identity-edit-tabs">
                    <ul class="identity-edit-tab-list">
                        <li class="identity-edit-tab-item">
                            <a href="{:U('Projectidentity/index')}" class="identity-edit-tab-link <php>if(ACTION_NAME=='index') echo 'active'</php>">
                                <i class="fa fa-users"></i>
                                身份管理
                            </a>
                        </li>
                        <li class="identity-edit-tab-item">
                            <a href="{:U('Projectidentity/edit')}" class="identity-edit-tab-link <php>if(ACTION_NAME=='edit') echo 'active'</php>">
                                <i class="fa fa-<php>echo !I('get.id') ? 'plus' : 'edit';</php>"></i>
                                <php>echo !I('get.id') ? '添加' : '编辑';</php>身份
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 页面头部 -->
                <div class="identity-edit-page-header" style="margin-top: 2rem;">
                    <div class="identity-edit-page-title-section">
                        <h1 class="identity-edit-page-title">
                            <i class="fa fa-<php>echo $row ? 'edit' : 'plus-circle';</php>"></i>
                            <php>echo $row ? '编辑身份' : '添加身份';</php>
                        </h1>
                        <p class="identity-edit-page-subtitle">
                            <php>echo $row ? '修改身份信息和层级关系' : '创建新的身份类型和层级关系';</php>
                        </p>
                    </div>
                    <div class="identity-edit-page-actions">
                        <a href="{:U('Projectidentity/index')}" class="identity-edit-btn identity-edit-btn-secondary">
                            <i class="fa fa-arrow-left"></i>
                            返回身份管理
                        </a>
                    </div>
                </div>

                <!-- 现代化表单 -->
                <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="identity-edit-form">
                    <!-- 身份信息卡片 -->
                    <div class="identity-edit-form-card">
                        <div class="identity-edit-form-header">
                            <h2 class="identity-edit-form-title">
                                <i class="fa fa-user-circle"></i>
                                身份基本信息
                            </h2>
                            <p class="identity-edit-form-description">
                                设置身份的名称、层级关系和详细说明
                            </p>
                        </div>

                        <div class="identity-edit-form-body">
                            <!-- 身份名称 -->
                            <div class="identity-edit-form-group">
                                <label class="identity-edit-form-label">
                                    <i class="fa fa-user"></i>
                                    身份名称
                                    <span class="required">*</span>
                                </label>
                                <input type="text"
                                       name="name"
                                       class="identity-edit-form-control"
                                       value="{$row.name}"
                                       placeholder="请输入身份名称，如：管理员、普通用户等"
                                       required />
                                <div class="identity-edit-help-text">身份的完整名称，将用于权限管理和用户分类</div>
                            </div>

                            <!-- 隶属身份 -->
                            <div class="identity-edit-form-group">
                                <label class="identity-edit-form-label">
                                    <i class="fa fa-sitemap"></i>
                                    隶属身份
                                    <span class="required">*</span>
                                </label>
                                <select name="pid" class="identity-edit-form-control identity-edit-select" required>
                                    <option value="">请选择隶属的身份</option>
                                    <php>foreach($pidList as $key => $val) {</php>
                                    <option value="{$key}" {:$row && $key== $row['pid'] ? 'selected' : ''}>{$val}</option>
                                    <php>}</php>
                                </select>
                                <div class="identity-edit-help-text">选择该身份的上级身份，用于构建身份层级关系</div>
                            </div>

                            <!-- 身份说明 -->
                            <div class="identity-edit-form-group">
                                <label class="identity-edit-form-label">
                                    <i class="fa fa-file-text"></i>
                                    身份说明
                                </label>
                                <div class="identity-edit-richtext-container">
                                    <textarea name="desc"
                                              rows="6"
                                              class="richtext identity-edit-textarea"
                                              placeholder="请输入身份的详细说明...">{$row.desc}</textarea>
                                </div>
                                <div class="identity-edit-help-text">详细描述该身份的职责、权限范围和使用场景</div>
                            </div>
                        </div>
                    </div>

                    <!-- 表单操作区域 -->
                    <div class="identity-edit-form-actions">
                        <input type="hidden" name="id" value="{$row.id}" />
                        <button type="button" onclick="history.back()" class="identity-edit-btn identity-edit-btn-secondary">
                            <i class="fa fa-times"></i>
                            取消
                        </button>
                        <button type="submit" name="submit" class="identity-edit-btn identity-edit-btn-primary identity-edit-btn-lg">
                            <i class="fa fa-save"></i>
                            <php>echo $row ? '保存修改' : '创建身份';</php>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="__ROOT__/static/js/prime/common.js"></script>
<link rel="stylesheet" href="__ROOT__/static/js/app/layer/skin/layer.css" />

<script>
	require(['layer', 'util'], function(layer, u) {
		$(function(){
			// 初始化富文本编辑器
			u.editor($('.richtext')[0]);
			u.editor($('.richtext1')[0]);

			// 表单验证和交互
			var $form = $('#identity-edit-form');
			var $submitBtn = $('button[type="submit"]');

			// 表单字段实时验证
			$('.identity-edit-form-control').on('input blur', function() {
				var $field = $(this);
				var value = $field.val().trim();
				var isRequired = $field.prop('required');
				var fieldName = $field.attr('name');

				// 移除之前的验证状态
				$field.removeClass('error success');
				$field.closest('.identity-edit-form-group').removeClass('error success');
				$field.siblings('.identity-edit-error-text').remove();

				// 必填字段验证
				if (isRequired && !value) {
					$field.addClass('error');
					$field.closest('.identity-edit-form-group').addClass('error');
					$field.after('<div class="identity-edit-error-text"><i class="fa fa-exclamation-circle"></i> 此字段为必填项</div>');
					return;
				}

				// 特定字段验证
				if (value) {
					var isValid = true;
					var errorMsg = '';

					switch(fieldName) {
						case 'name':
							if (value.length < 2) {
								isValid = false;
								errorMsg = '身份名称至少2个字符';
							} else if (value.length > 50) {
								isValid = false;
								errorMsg = '身份名称不能超过50个字符';
							}
							break;
						case 'pid':
							if (value === '') {
								isValid = false;
								errorMsg = '请选择隶属身份';
							}
							break;
					}

					if (!isValid) {
						$field.addClass('error');
						$field.closest('.identity-edit-form-group').addClass('error');
						$field.after('<div class="identity-edit-error-text"><i class="fa fa-exclamation-circle"></i> ' + errorMsg + '</div>');
					} else {
						$field.addClass('success');
						$field.closest('.identity-edit-form-group').addClass('success');
					}
				}
			});

			// 表单提交处理
			$form.on('submit', function(e) {
				e.preventDefault();

				// 验证必填字段
				var hasError = false;
				$('.identity-edit-form-control[required]').each(function() {
					var $field = $(this);
					var value = $field.val().trim();

					if (!value || (value === '' && $field.attr('name') === 'pid')) {
						$field.addClass('error');
						$field.closest('.identity-edit-form-group').addClass('error');
						$field.siblings('.identity-edit-error-text').remove();
						$field.after('<div class="identity-edit-error-text"><i class="fa fa-exclamation-circle"></i> 此字段为必填项</div>');
						hasError = true;
					}
				});

				// 检查是否有验证错误
				if ($('.identity-edit-form-control.error').length > 0) {
					hasError = true;
				}

				if (hasError) {
					layer.msg('请修正表单中的错误信息', {icon: 2});
					// 滚动到第一个错误字段
					var $firstError = $('.identity-edit-form-control.error').first();
					if ($firstError.length) {
						$('html, body').animate({
							scrollTop: $firstError.offset().top - 100
						}, 500);
						$firstError.focus();
					}
					return false;
				}

				// 设置提交按钮为加载状态
				$submitBtn.addClass('loading');
				$submitBtn.prop('disabled', true);
				var originalText = $submitBtn.html();
				$submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 保存中...');

				// 提交表单
				$.ajax({
					url: $form.attr('action') || window.location.href,
					type: 'POST',
					data: $form.serialize(),
					dataType: 'json',
					success: function(response) {
						if (response.status === 1 || response.code === 1) {
							layer.msg(response.info || '操作成功', {icon: 1}, function() {
								window.location.href = "{:U('Projectidentity/index')}";
							});
						} else {
							layer.msg(response.info || '操作失败', {icon: 2});
						}
					},
					error: function() {
						layer.msg('网络错误，请重试', {icon: 2});
					},
					complete: function() {
						// 恢复提交按钮状态
						$submitBtn.removeClass('loading');
						$submitBtn.prop('disabled', false);
						$submitBtn.html(originalText);
					}
				});
			});

			// 页面加载动画
			setTimeout(function() {
				$('.identity-edit-form-card').addClass('identity-edit-fade-in');
			}, 100);

			// 表单字段聚焦效果
			$('.identity-edit-form-control').on('focus', function() {
				$(this).closest('.identity-edit-form-group').addClass('focused');
			}).on('blur', function() {
				$(this).closest('.identity-edit-form-group').removeClass('focused');
			});

			// 富文本编辑器聚焦效果
			$('.richtext').on('focus', function() {
				$(this).closest('.identity-edit-richtext-container').addClass('focused');
			}).on('blur', function() {
				$(this).closest('.identity-edit-richtext-container').removeClass('focused');
			});

			// 键盘快捷键
			$(document).on('keydown', function(e) {
				// Ctrl+S 保存
				if (e.ctrlKey && e.keyCode === 83) {
					e.preventDefault();
					$form.submit();
				}
				// Esc 取消
				if (e.keyCode === 27) {
					if (confirm('确定要取消编辑吗？未保存的更改将丢失。')) {
						history.back();
					}
				}
			});

			// 表单数据变化检测
			var originalFormData = $form.serialize();
			var hasUnsavedChanges = false;

			$('.identity-edit-form-control, .richtext').on('input change', function() {
				hasUnsavedChanges = ($form.serialize() !== originalFormData);
			});

			// 页面离开提醒
			$(window).on('beforeunload', function() {
				if (hasUnsavedChanges) {
					return '您有未保存的更改，确定要离开吗？';
				}
			});

			// 成功提交后清除未保存标记
			$form.on('submit', function() {
				hasUnsavedChanges = false;
			});

			// 隶属身份选择变化处理
			$('select[name="pid"]').on('change', function() {
				var $select = $(this);
				var value = $select.val();
				var $helpText = $select.siblings('.identity-edit-help-text');
				var $hierarchyPreview = $select.siblings('.identity-edit-hierarchy-preview');

				// 移除之前的层级预览
				$hierarchyPreview.remove();

				if (value && value !== '') {
					var selectedText = $select.find('option:selected').text();
					$helpText.html('<i class="fa fa-check-circle" style="color: #48bb78;"></i> 已选择隶属身份：' + selectedText);

					// 创建层级预览
					var currentName = $('input[name="name"]').val() || '当前身份';
					var hierarchyHtml = '<div class="identity-edit-hierarchy-preview">' +
						'<strong>身份层级预览：</strong>' +
						'<div class="hierarchy-path">' +
						'<span class="hierarchy-item">' + selectedText + '</span>' +
						'<i class="fa fa-arrow-right hierarchy-arrow"></i>' +
						'<span class="hierarchy-item">' + currentName + '</span>' +
						'</div>' +
						'</div>';
					$select.after(hierarchyHtml);
				} else {
					$helpText.text('选择该身份的上级身份，用于构建身份层级关系');
				}
			});

			// 身份名称变化时更新层级预览
			$('input[name="name"]').on('input', function() {
				var $input = $(this);
				var value = $input.val();
				var $helpText = $input.siblings('.identity-edit-help-text');
				var length = value.length;

				if (length > 0) {
					$helpText.html('身份名称长度：' + length + ' 个字符 <i class="fa fa-check-circle" style="color: #48bb78;"></i>');

					// 更新层级预览中的当前身份名称
					var $hierarchyItem = $('.hierarchy-path .hierarchy-item:last');
					if ($hierarchyItem.length) {
						$hierarchyItem.text(value);
					}
				} else {
					$helpText.text('身份的完整名称，将用于权限管理和用户分类');
				}
			});

			// 富文本编辑器字符计数
			$('.richtext').on('input', function() {
				var $textarea = $(this);
				var value = $textarea.val();
				var $helpText = $textarea.closest('.identity-edit-form-group').find('.identity-edit-help-text');
				var length = value.length;

				if (length > 0) {
					$helpText.html('说明长度：' + length + ' 个字符。详细描述该身份的职责、权限范围和使用场景');
				} else {
					$helpText.text('详细描述该身份的职责、权限范围和使用场景');
				}
			});

			// 表单字段自动完成建议
			var commonIdentityNames = ['管理员', '普通用户', '审核员', '编辑员', '访客', '超级管理员', '部门主管', '财务人员', '技术人员', '客服人员'];

			$('input[name="name"]').on('input', function() {
				var $input = $(this);
				var value = $input.val().toLowerCase();

				if (value.length > 0) {
					var suggestions = commonIdentityNames.filter(function(name) {
						return name.toLowerCase().indexOf(value) !== -1;
					});

					if (suggestions.length > 0 && suggestions.length < 5) {
						console.log('身份名称建议：', suggestions);
					}
				}
			});

			// 初始化时触发隶属身份变化事件
			$('select[name="pid"]').trigger('change');
		});
	});
</script>

<include file="block/footer" />