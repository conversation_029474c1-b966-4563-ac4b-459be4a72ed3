<?php

function tpl_form_field_image($name, $value = '', $default = '', $options = array()) {

	$s = '';
	if (!defined('TPL_INIT_IMAGE')) {
		$s = '
		<script type="text/javascript">
			function showImageDialog(elm, opts, options) {
				require(["util"], function(util){
					var btn = $(elm);
					var ipt = btn.parent().prev();
					var val = ipt.val();
					var img = ipt.parent().next().children();
					util.image(val, function(url){
						if(url.url){
							if(img.length > 0){
								img.get(0).src = url.url;
							}
							ipt.val(url.filename);
							ipt.attr("filename",url.filename);
							ipt.attr("url",url.url);
						}
						if(url.media_id){
							if(img.length > 0){
								img.get(0).src = "";
							}
							ipt.val(url.media_id);
						}
					}, opts, options);
				});
			}
		</script>';

		define('TPL_INIT_IMAGE', true);
	}
	if(empty($default)) {
		$default = __ROOT__.'/static/images/nopic.jpg';
	}
	$val = $default;
	if(!empty($value)) {
		$val = imgPath($value);
	}
	if(empty($options['tabs'])){
		$options['tabs'] = array('browser'=>'', 'upload'=>'active');
	}
	if(!empty($options['global'])){
		$options['global'] = true;
	} else {
		$options['global'] = false;
	}
	if(empty($options['class_extra'])) {
		$options['class_extra'] = '';
	}
	if (isset($options['dest_dir']) && !empty($options['dest_dir'])) {
		if (!preg_match('/^\w+([\/]\w+)?$/i', $options['dest_dir'])) {
			exit('图片上传目录错误,只能指定最多两级目录,如: "we7_store","we7_store/d1"');
		}
	}

	if(isset($options['thumb'])){
		$options['thumb'] = !empty($options['thumb']);
	}

	$s .= '
<div class="input-group '. $options['class_extra'] .'">
	<input type="text" name="'.$name.'" value="'.$value.'"'.($options['extras']['text'] ? $options['extras']['text'] : '').' class="form-control" autocomplete="off">
	<span class="input-group-btn">
		<button class="btn btn-default" type="button" onclick="showImageDialog(this, \'' . base64_encode(serialize($options)) . '\', '. str_replace('"','\'', json_encode($options)).');">选择图片</button>
	</span>
</div>';
	if(!empty($options['tabs']['browser']) || !empty($options['tabs']['upload'])){
		$s .=
		'<div class="input-group '. $options['class_extra'] .'" style="margin-top:.5em;">
	<img src="' . $val . '" onerror="this.src=\''.$default.'\'; this.title=\'图片未找到.\'" class="img-responsive img-thumbnail" '.($options['extras']['image'] ? $options['extras']['image'] : '').' width="150" />
</div>';
	}
	return $s;
}

function frameCallback($callback, $val) {
	echo '<script type="text/javascript">window.parent.' . $callback . '(' . $val . ');</script>';
	exit;
}

/**
 * 连续签到天数对应的积分数
 * @param int $days
 */
function days2point($days)
{
    $ret = 0;
    $sign_conf = D('Conf')->C('SYS_SIGN');
    foreach (explode(',', $sign_conf) as $val) {
        $arr = explode(':', $val);
        if ($days >= $arr[0]) {
            $ret = $arr[1];
            break;
        }
    }
    return $ret;
}

/**
 * 获取流量列表
 */
function flowList($refresh = false)
{
    $f_key = 'flow_list';
    $expire = 600; // 缓存过期时间
    $list = S($f_key);
    if (!$list || $refresh) {
        $list = [];
        $obj = new \Api\Juhe\Flow();
        $res = $obj->getList();
        if (!$res) return false;
        foreach ($res as $v) {
            $list[$v['companytype']] = $v;
        }
        S($f_key, $list, $expire);
    }
    return $list;
}

/**
 * 获取手机号码运营商
 * 1联通，2移动，3电信
 */
function telOp($mobile)
{
    $ret = false;
    $arr = [
            1 => [186, 185, 156, 155, 130, 131, 132, 176],
            2 => [134, 135, 136, 137, 138, 139, 147, 150, 151, 152, 157, 158, 159, 170, 182, 183, 187, 188],
            3 => [177, 180, 189, 181, 153, 133],
        ];
    $pre = substr($mobile, 0, 3);
    foreach ($arr as $k => $v) {
        if (in_array($pre, $v)) {
            $ret = $k;
            break;
        }
    }
    return $ret;
}

// 新浪短网址接口
function sinaShortUrl($long_url) {
    $url = 'http://api.t.sina.com.cn/short_url/shorten.json?source='.C('SINA_APPKEY').'&url_long=' . urlencode($long_url);
    $curl_obj = curl_init();
    curl_setopt($curl_obj, CURLOPT_URL, $url);
    curl_setopt($curl_obj, CURLOPT_HEADER, 0);
    curl_setopt($curl_obj, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl_obj, CURLOPT_TIMEOUT, 15);
    $result = curl_exec($curl_obj);
    curl_close($curl_obj);
    $json = json_decode($result);
    if (isset($json->error) || empty($json[0]->url_short)) {
        return false;
    } else {
        return $json[0]->url_short;
    }
}


