<include file="block/hat" />
<script type="text/javascript" src="__ROOT__/static/js/lib/jquery-ui-1.10.3.min.js"></script>

<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<style type='text/css'>
				/* 重置和基础样式 */
				* {
					box-sizing: border-box;
				}

				body {
					font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
					line-height: 1.6;
					color: #2d3748;
				}

				.users-page-container {
					padding: 2rem;
					background: #f7fafc;
					min-height: 100vh;
					font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
				}

				/* 现代化标签页 */
				.users-tabs {
					background: white;
					border-radius: 1rem 1rem 0 0;
					box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					border-bottom: none;
					overflow: hidden;
					margin-bottom: 0;
				}

				.users-tab-list {
					display: flex;
					margin: 0;
					padding: 0;
					list-style: none;
				}

				.users-tab-item {
					flex: 1;
				}

				.users-tab-link {
					display: block;
					padding: 1.5rem 2rem;
					color: #4a5568;
					text-decoration: none;
					font-weight: 600;
					font-size: 1.5rem;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					border-right: 1px solid #e2e8f0;
					position: relative;
					text-align: center;
				}

				.users-tab-item:last-child .users-tab-link {
					border-right: none;
				}

				.users-tab-link:hover {
					background: #f7fafc;
					color: #667eea;
					text-decoration: none;
				}

				.users-tab-link.active {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.users-tab-link.active::after {
					content: '';
					position: absolute;
					bottom: -1px;
					left: 0;
					right: 0;
					height: 2px;
					background: white;
				}

				/* 页面头部 */
				.users-page-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin: 2rem 0;
					flex-wrap: wrap;
					gap: 1.5rem;
				}

				.users-page-title-section {
					display: flex;
					flex-direction: column;
					gap: 0.5rem;
				}

				.users-page-title {
					font-size: 2rem;
					font-weight: 700;
					color: #1a202c;
					margin: 0;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.users-page-subtitle {
					color: #4a5568;
					font-size: 1.5rem;
					margin: 0;
				}

				.users-page-actions {
					display: flex;
					gap: 1rem;
					flex-wrap: wrap;
				}

				/* 统计卡片 */
				.users-stats-grid {
					display: grid;
					grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
					gap: 1.5rem;
					margin-bottom: 2rem;
				}

				.users-stat-card {
					background: white;
					border-radius: 1rem;
					padding: 1.5rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					overflow: hidden;
				}

				.users-stat-card:hover {
					transform: translateY(-4px);
					box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
				}

				.users-stat-card::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4px;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				}

				.users-stat-icon {
					width: 60px;
					height: 60px;
					border-radius: 0.75rem;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 1.5rem;
					color: white;
					margin-bottom: 1rem;
				}

				.users-stat-number {
					font-size: 2rem;
					font-weight: 700;
					color: #1a202c;
					margin-bottom: 0.25rem;
				}

				.users-stat-label {
					color: #4a5568;
					font-weight: 500;
					margin-bottom: 0.5rem;
					font-size: 1.5rem;
				}

				.users-stat-change {
					display: flex;
					align-items: center;
					gap: 0.25rem;
					font-size: 1.5rem;
					font-weight: 600;
					color: #48bb78;
				}

				/* 用户卡片样式 */
				.users-user-card {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 1.5rem;
					overflow: hidden;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
				}

				.users-user-card:hover {
					transform: translateY(-4px);
					box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
				}

				.users-user-card::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4px;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				}

				.users-user-header {
					padding: 1.5rem 2rem;
					border-bottom: 1px solid #f1f5f9;
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex-wrap: wrap;
					gap: 1rem;
				}

				.users-user-basic {
					display: flex;
					align-items: center;
					gap: 1rem;
				}

				.users-user-avatar {
					width: 80px;
					height: 80px;
					border-radius: 50%;
					border: 4px solid #f1f5f9;
					object-fit: cover;
					transition: all 0.3s ease;
				}

				.users-user-avatar:hover {
					border-color: #667eea;
					transform: scale(1.05);
				}

				.users-user-info {
					display: flex;
					flex-direction: column;
					gap: 0.5rem;
				}

				.users-user-name {
					font-size: 1.5rem;
					font-weight: 600;
					color: #1a202c;
					margin: 0;
				}

				.users-user-id {
					background: #f1f5f9;
					color: #4a5568;
					padding: 0.25rem 0.75rem;
					border-radius: 0.5rem;
					font-size: 1.5rem;
					font-weight: 600;
					display: inline-block;
					width: fit-content;
				}

				.users-user-status {
					display: flex;
					align-items: center;
					gap: 0.5rem;
				}

				.users-status-indicator {
					width: 12px;
					height: 12px;
					border-radius: 50%;
					animation: pulse 2s infinite;
				}

				.users-status-indicator.active {
					background: #48bb78;
				}

				.users-status-indicator.inactive {
					background: #f56565;
				}

				@keyframes pulse {
					0% { opacity: 1; }
					50% { opacity: 0.5; }
					100% { opacity: 1; }
				}

				.users-user-body {
					padding: 2rem;
					display: grid;
					grid-template-columns: 1fr 200px 200px;
					gap: 2rem;
					align-items: start;
				}

				.users-user-details {
					display: flex;
					flex-direction: column;
					gap: 1rem;
				}

				.users-detail-item {
					display: flex;
					align-items: center;
					gap: 0.75rem;
					padding: 0.75rem 0;
				}

				.users-detail-icon {
					width: 32px;
					height: 32px;
					border-radius: 0.5rem;
					background: #f1f5f9;
					color: #4a5568;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 2rem;
				}

				.users-detail-content {
					flex: 1;
				}

				.users-detail-label {
					font-size: 1.5rem;
					color: #718096;
					font-weight: 500;
					margin-bottom: 0.25rem;
				}

				.users-detail-value {
					font-size: 1.5rem;
					color: #1a202c;
					font-weight: 600;
				}

				/* 图片预览区域 */
				.users-image-section {
					display: flex;
					flex-direction: column;
					gap: 1rem;
				}

				.users-image-item {
					background: #f7fafc;
					border-radius: 0.75rem;
					padding: 1rem;
					border: 1px solid #e2e8f0;
					text-align: center;
				}

				.users-image-label {
					font-size: 1.5rem;
					color: #4a5568;
					font-weight: 600;
					margin-bottom: 0.75rem;
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 0.5rem;
				}

				.users-image-preview {
					width: 100%;
					max-width: 150px;
					height: 150px;
					border-radius: 0.5rem;
					object-fit: cover;
					border: 2px solid #e2e8f0;
					transition: all 0.3s ease;
					cursor: pointer;
				}

				.users-image-preview:hover {
					border-color: #667eea;
					transform: scale(1.02);
				}

				.users-no-image {
					width: 100%;
					height: 150px;
					background: #f1f5f9;
					border: 2px dashed #cbd5e0;
					border-radius: 0.5rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #a0aec0;
					font-size: 1.5rem;
				}

				/* 操作区域 */
				.users-user-actions {
					display: flex;
					flex-direction: column;
					gap: 0.75rem;
				}

				.users-action-group {
					background: #f7fafc;
					border-radius: 0.75rem;
					padding: 1.5rem;
					border: 1px solid #e2e8f0;
				}

				.users-action-group h4 {
					margin: 0 0 1rem 0;
					font-size: 2rem;
					font-weight: 600;
					color: #2d3748;
					display: flex;
					align-items: center;
					gap: 0.5rem;
				}

				.users-action-buttons {
					display: flex;
					flex-direction: column;
					gap: 0.75rem;
				}

				.users-action-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 0.5rem;
					padding: 0.75rem 1rem;
					border: none;
					border-radius: 0.5rem;
					font-weight: 600;
					font-size: 1.5rem;
					text-decoration: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					overflow: hidden;
				}

				.users-action-btn:before {
					content: '';
					position: absolute;
					top: 0;
					left: -100%;
					width: 100%;
					height: 100%;
					background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.users-action-btn:hover:before {
					left: 100%;
				}

				.users-action-btn.primary {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.users-action-btn.primary:hover {
					background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
					transform: translateY(-1px);
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					color: white;
					text-decoration: none;
				}

				.users-action-btn.warning {
					background: #ed8936;
					color: white;
				}

				.users-action-btn.warning:hover {
					background: #d69e2e;
					transform: translateY(-1px);
					color: white;
					text-decoration: none;
				}

				.users-action-btn.danger {
					background: #f56565;
					color: white;
				}

				.users-action-btn.danger:hover {
					background: #e53e3e;
					transform: translateY(-1px);
					color: white;
					text-decoration: none;
				}

				/* 现代化徽章 */
				.users-badge {
					display: inline-flex;
					align-items: center;
					gap: 0.25rem;
					padding: 0.25rem 0.75rem;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-weight: 600;
					text-transform: uppercase;
					letter-spacing: 0.025em;
				}

				.users-badge-success {
					background: rgba(72, 187, 120, 0.1);
					color: #48bb78;
				}

				.users-badge-error {
					background: rgba(245, 101, 101, 0.1);
					color: #f56565;
				}

				.users-badge-info {
					background: rgba(66, 153, 225, 0.1);
					color: #4299e1;
				}

				/* 现代化按钮 */
				.users-btn {
					display: inline-flex;
					align-items: center;
					justify-content: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					border: none;
					border-radius: 0.5rem;
					font-weight: 600;
					font-size: 1.5rem;
					text-decoration: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					overflow: hidden;
					font-family: inherit;
				}

				.users-btn-primary {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.users-btn-primary:hover {
					background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
					transform: translateY(-1px);
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					color: white;
					text-decoration: none;
				}

				/* 部门标签 */
				.users-department-tags {
					display: flex;
					flex-wrap: wrap;
					gap: 0.5rem;
				}

				.users-department-tag {
					background: #667eea;
					color: white;
					padding: 0.25rem 0.75rem;
					border-radius: 0.5rem;
					font-size: 1.5rem;
					font-weight: 500;
					transition: all 0.3s ease;
				}

				.users-department-tag:hover {
					background: #5a67d8;
					transform: scale(1.05);
				}

				/* 响应式设计 */
				@media (max-width: 1024px) {
					.users-user-body {
						grid-template-columns: 1fr 180px;
					}

					.users-image-section {
						grid-column: 1 / -1;
						display: grid;
						grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
						gap: 1rem;
					}
				}

				@media (max-width: 768px) {
					.users-page-container {
						padding: 1.5rem;
					}

					.users-stats-grid {
						grid-template-columns: 1fr;
					}

					.users-user-body {
						grid-template-columns: 1fr;
						gap: 1.5rem;
					}

					.users-user-header {
						flex-direction: column;
						text-align: center;
					}

					.users-action-buttons {
						flex-direction: row;
						flex-wrap: wrap;
					}

					.users-action-btn {
						flex: 1;
						min-width: 120px;
					}

					.users-page-header {
						flex-direction: column;
						align-items: flex-start;
					}

					.users-page-title {
						font-size: 1.5rem;
					}

					.users-image-section {
						grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
					}
				}

				/* 动画效果 */
				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(20px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}

				.users-fade-in {
					animation: fadeInUp 0.5s ease-out;
				}

				.users-fade-in-delay-1 {
					animation: fadeInUp 0.5s ease-out 0.1s both;
				}

				.users-fade-in-delay-2 {
					animation: fadeInUp 0.5s ease-out 0.2s both;
				}
			</style>

			<div class="users-page-container users-fade-in">
				<!-- 现代化标签页 -->
				<div class="users-tabs">
					<ul class="users-tab-list">
						<li class="users-tab-item">
							<a href="{:U('users/edit')}" class="users-tab-link <php>if(ACTION_NAME=='edit' && !I('get.id')) echo 'active'</php>">
								<i class="fa fa-plus"></i>
								添加人员
							</a>
						</li>
						<li class="users-tab-item">
							<a href="{:U('users/index')}" class="users-tab-link <php>if(ACTION_NAME=='index') echo 'active'</php>">
								<i class="fa fa-users"></i>
								人员列表
							</a>
						</li>
					</ul>
				</div>

				<!-- 页面头部 -->
				<div class="users-page-header" style="margin-top: 2rem;">
					<div class="users-page-title-section">
						<h1 class="users-page-title">
							<i class="fa fa-users"></i>
							人员管理
						</h1>
						<p class="users-page-subtitle">管理系统用户信息、权限分配和组织架构</p>
					</div>
					<div class="users-page-actions">
						<a href="{:U('users/edit')}" class="users-btn users-btn-primary">
							<i class="fa fa-plus"></i>
							新增人员
						</a>
					</div>
				</div>

				<!-- 统计概览 -->
				<div class="users-stats-grid" style="margin-bottom: 2rem;">
					<div class="users-stat-card users-fade-in-delay-1">
						<div class="users-stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
							<i class="fa fa-users"></i>
						</div>
						<div class="users-stat-number"><php>echo count($list);</php></div>
						<div class="users-stat-label">总人员数</div>
						<div class="users-stat-change">
							<i class="fa fa-arrow-up"></i>
							<span>系统用户</span>
						</div>
					</div>

					<div class="users-stat-card users-fade-in-delay-1">
						<div class="users-stat-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
							<i class="fa fa-check-circle"></i>
						</div>
						<div class="users-stat-number">
							<php>
							$activeCount = 0;
							foreach($list as $item) {
								if($item['status'] == 1) $activeCount++;
							}
							echo $activeCount;
							</php>
						</div>
						<div class="users-stat-label">活跃用户</div>
						<div class="users-stat-change">
							<i class="fa fa-check"></i>
							<span>正常状态</span>
						</div>
					</div>

					<div class="users-stat-card users-fade-in-delay-1">
						<div class="users-stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
							<i class="fa fa-building"></i>
						</div>
						<div class="users-stat-number">
							<php>
							$departmentCount = 0;
							$departments = array();
							foreach($list as $item) {
								if($item['role_id']) {
									$roleIds = explode(',', $item['role_id']);
									foreach($roleIds as $roleId) {
										if(!in_array($roleId, $departments)) {
											$departments[] = $roleId;
											$departmentCount++;
										}
									}
								}
							}
							echo count($departments);
							</php>
						</div>
						<div class="users-stat-label">涉及部门</div>
						<div class="users-stat-change">
							<i class="fa fa-sitemap"></i>
							<span>组织架构</span>
						</div>
					</div>

					<div class="users-stat-card users-fade-in-delay-1">
						<div class="users-stat-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
							<i class="fa fa-mobile"></i>
						</div>
						<div class="users-stat-number">
							<php>
							$mobileCount = 0;
							foreach($list as $item) {
								if(!empty($item['mobile'])) $mobileCount++;
							}
							echo $mobileCount;
							</php>
						</div>
						<div class="users-stat-label">已绑定手机</div>
						<div class="users-stat-change">
							<i class="fa fa-phone"></i>
							<span>联系方式</span>
						</div>
					</div>
				</div>

				<!-- 用户列表 -->
				<div class="users-content-section users-fade-in-delay-2">
					<div class="users-section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
						<h2 style="margin: 0; font-size: 1.5rem; font-weight: 600; color: #1a202c; display: flex; align-items: center; gap: 0.5rem;">
							<i class="fa fa-list"></i>
							用户列表
						</h2>
						<div>
							<span class="users-badge users-badge-info">
								共 <php>echo count($list);</php> 个用户
							</span>
						</div>
					</div>

					<form action="" method="post">
						<php>foreach($list as $row) { </php>
						<div class="users-user-card">
							<!-- 用户头部 -->
							<div class="users-user-header">
								<div class="users-user-basic">
									<php>if ($row['identification']) {</php>
									<img src="{:$row['identification']}" alt="{$row.username}" class="users-user-avatar">
									<php>} else {</php>
									<div class="users-user-avatar" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: bold;">
										<php>echo strtoupper(substr($row['username'], 0, 1));</php>
									</div>
									<php>}</php>

									<div class="users-user-info">
										<h3 class="users-user-name">{$row.username}</h3>
										<span class="users-user-id">ID: #{$row.id}</span>
									</div>
								</div>
								<div class="users-user-status">
									<div class="users-status-indicator <php>echo $row['status'] == 1 ? 'active' : 'inactive';</php>"></div>
									<span class="users-badge <php>echo $row['status'] == 1 ? 'users-badge-success' : 'users-badge-error';</php>">
										<i class="fa fa-<php>echo $row['status'] == 1 ? 'check-circle' : 'times-circle';</php>"></i>
										{:D('Role')->status[$row['status']]}
									</span>
								</div>
							</div>

							<!-- 用户详情 -->
							<div class="users-user-body">
								<div class="users-user-details">
									<div class="users-detail-item">
										<div class="users-detail-icon">
											<i class="fa fa-code"></i>
										</div>
										<div class="users-detail-content">
											<div class="users-detail-label">用户代码</div>
											<div class="users-detail-value">
												<php>echo $row['letter'] ?: '未设置';</php>
											</div>
										</div>
									</div>

									<div class="users-detail-item">
										<div class="users-detail-icon">
											<i class="fa fa-id-card"></i>
										</div>
										<div class="users-detail-content">
											<div class="users-detail-label">身份证号</div>
											<div class="users-detail-value">
												<php>echo $row['card'] ? substr($row['card'], 0, 6) . '****' . substr($row['card'], -4) : '未设置';</php>
											</div>
										</div>
									</div>

									<div class="users-detail-item">
										<div class="users-detail-icon">
											<i class="fa fa-envelope"></i>
										</div>
										<div class="users-detail-content">
											<div class="users-detail-label">邮箱地址</div>
											<div class="users-detail-value">
												<php>echo $row['emails'] ?: '未设置';</php>
											</div>
										</div>
									</div>

									<div class="users-detail-item">
										<div class="users-detail-icon">
											<i class="fa fa-phone"></i>
										</div>
										<div class="users-detail-content">
											<div class="users-detail-label">手机号码</div>
											<div class="users-detail-value">
												<php>echo $row['mobile'] ?: '未设置';</php>
											</div>
										</div>
									</div>

									<div class="users-detail-item">
										<div class="users-detail-icon">
											<i class="fa fa-briefcase"></i>
										</div>
										<div class="users-detail-content">
											<div class="users-detail-label">职位信息</div>
											<div class="users-detail-value">
												<php>echo $row['position'] ?: '未设置';</php>
											</div>
										</div>
									</div>

									<div class="users-detail-item">
										<div class="users-detail-icon">
											<i class="fa fa-building"></i>
										</div>
										<div class="users-detail-content">
											<div class="users-detail-label">所属部门</div>
											<div class="users-detail-value">
												<div class="users-department-tags">
													<php>
													if(!$row['role_id']) $row['role_id'] = 1;
													foreach(explode(',',$row['role_id']) as $v) {
														if(isset($roles[$v]['name'])) {
															echo '<span class="users-department-tag">' . $roles[$v]['name'] . '</span>';
														}
													}
													</php>
												</div>
											</div>
										</div>
									</div>
								</div>

								<!-- 图片预览区域 -->
								<div class="users-image-section">
									<div class="users-image-item">
										<div class="users-image-label">
											<i class="fa fa-camera"></i>
											证件照
										</div>
										<php>if ($row['identification']) {</php>
										<a href="{:$row['identification']}" target="_blank">
											<img src="{:$row['identification']}" alt="证件照" class="users-image-preview">
										</a>
										<php>} else {</php>
										<div class="users-no-image">
											<i class="fa fa-image"></i>
											<span>暂无图片</span>
										</div>
										<php>}</php>
									</div>

									<div class="users-image-item">
										<div class="users-image-label">
											<i class="fa fa-qrcode"></i>
											企微码
										</div>
										<php>if ($row['qrcode']) {</php>
										<a href="{:$row['qrcode']}" target="_blank">
											<img src="{:$row['qrcode']}" alt="企微码" class="users-image-preview">
										</a>
										<php>} else {</php>
										<div class="users-no-image">
											<i class="fa fa-qrcode"></i>
											<span>暂无二维码</span>
										</div>
										<php>}</php>
									</div>
								</div>

								<!-- 操作区域 -->
								<div class="users-user-actions">
									<div class="users-action-group">
										<h4>
											<i class="fa fa-cogs"></i>
											管理操作
										</h4>
										<div class="users-action-buttons">
											<php>if($row['id'] != 1) {</php>
											<a href="{:U('prime/users/addservicestation', ['id' => $row['id']])}" class="users-action-btn primary">
												<i class="fa fa-plus"></i>
												新增服务站
											</a>
											<a href="{:U('users/edit', ['id' => $row['id']])}" class="users-action-btn warning">
												<i class="fa fa-edit"></i>
												编辑用户
											</a>
											<a href="{:U('users/del', ['id' => $row['id']])}"
											   class="users-action-btn danger"
											   onclick="return confirm('确认要删除这个用户吗？此操作不可恢复！')">
												<i class="fa fa-trash"></i>
												删除用户
											</a>
											<php>} else {</php>
											<div style="padding: 1rem; background: #fff3cd; border-radius: 0.5rem; color: #856404; text-align: center; font-size: 1.5rem;">
												<i class="fa fa-shield"></i>
												系统管理员账户
											</div>
											<php>}</php>
										</div>
									</div>
								</div>
							</div>
						</div>
						<php>}</php>

						<!-- 分页 -->
						<div style="text-align: center; margin-top: 2rem;">
							{$page}
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	require(["daterangepicker"], function($){
		$(function(){
			// 用户卡片动画效果
			$('.users-user-card').each(function(index) {
				$(this).css('animation-delay', (index * 0.1) + 's');
				$(this).addClass('users-fade-in');
			});

			// 用户卡片悬停效果
			$('.users-user-card').hover(
				function() {
					$(this).find('.users-user-avatar').css('transform', 'scale(1.05)');
					$(this).find('.users-status-indicator').css('animation-duration', '0.5s');
				},
				function() {
					$(this).find('.users-user-avatar').css('transform', 'scale(1)');
					$(this).find('.users-status-indicator').css('animation-duration', '2s');
				}
			);

			// 操作按钮点击效果
			$('.users-action-btn').click(function(e) {
				var $btn = $(this);
				var originalText = $btn.html();

				// 如果是删除按钮，需要确认
				if ($btn.hasClass('danger')) {
					if (!confirm('确认要删除这个用户吗？此操作不可恢复！')) {
						e.preventDefault();
						return false;
					}
				}

				// 添加加载状态
				if (!$btn.attr('href').includes('#')) {
					$btn.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
					$btn.css('pointer-events', 'none');

					// 模拟加载延迟
					setTimeout(function() {
						$btn.html(originalText);
						$btn.css('pointer-events', 'auto');
					}, 1000);
				}
			});

			// 图片预览点击效果
			$('.users-image-preview').click(function() {
				var $img = $(this);
				$img.css('transform', 'scale(0.95)');
				setTimeout(function() {
					$img.css('transform', 'scale(1)');
				}, 150);
			});

			// 部门标签悬停效果
			$('.users-department-tag').hover(
				function() {
					$(this).css('transform', 'scale(1.05)');
				},
				function() {
					$(this).css('transform', 'scale(1)');
				}
			);

			// 状态指示器点击切换
			$('.users-status-indicator').click(function() {
				var $indicator = $(this);
				var $badge = $indicator.next('.users-badge');

				if ($indicator.hasClass('active')) {
					$indicator.removeClass('active').addClass('inactive');
					$badge.removeClass('users-badge-success').addClass('users-badge-error');
					$badge.find('i').removeClass('fa-check-circle').addClass('fa-times-circle');
				} else {
					$indicator.removeClass('inactive').addClass('active');
					$badge.removeClass('users-badge-error').addClass('users-badge-success');
					$badge.find('i').removeClass('fa-times-circle').addClass('fa-check-circle');
				}
			});

			// 统计卡片动画
			$('.users-stat-card').each(function(index) {
				var $card = $(this);
				setTimeout(function() {
					$card.find('.users-stat-number').each(function() {
						var $number = $(this);
						var target = parseInt($number.text());
						var current = 0;
						var increment = target / 20;

						var timer = setInterval(function() {
							current += increment;
							if (current >= target) {
								current = target;
								clearInterval(timer);
							}
							$number.text(Math.floor(current));
						}, 50);
					});
				}, index * 200);
			});

			// 搜索功能（如果需要的话）
			$('#users-search').on('input', function() {
				var searchTerm = $(this).val().toLowerCase();
				$('.users-user-card').each(function() {
					var userName = $(this).find('.users-user-name').text().toLowerCase();
					var userDetails = $(this).find('.users-detail-value').text().toLowerCase();

					if (userName.includes(searchTerm) || userDetails.includes(searchTerm)) {
						$(this).show();
					} else {
						$(this).hide();
					}
				});
			});

			// 工具提示
			$('[data-toggle="tooltip"]').tooltip();

			// 图片懒加载
			$('.users-image-preview').each(function() {
				var $img = $(this);
				$img.on('load', function() {
					$img.css('opacity', '1');
				}).on('error', function() {
					$img.closest('.users-image-item').find('.users-image-label').after(
						'<div class="users-no-image"><i class="fa fa-exclamation-triangle"></i><span>加载失败</span></div>'
					);
					$img.hide();
				});
			});

			// 页面加载完成后的初始化动画
			setTimeout(function() {
				$('.users-stat-card, .users-user-card').addClass('users-fade-in');
			}, 100);

			// 用户头像错误处理
			$('.users-user-avatar').on('error', function() {
				var $avatar = $(this);
				var username = $avatar.closest('.users-user-card').find('.users-user-name').text();
				var initial = username.charAt(0).toUpperCase();

				$avatar.replaceWith(
					'<div class="users-user-avatar" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: bold;">' +
					initial +
					'</div>'
				);
			});

			// 键盘快捷键
			$(document).on('keydown', function(e) {
				// Ctrl+F 搜索
				if (e.ctrlKey && e.keyCode === 70) {
					e.preventDefault();
					$('#users-search').focus();
				}
			});
		});
	});
</script>

<include file="block/footer" />