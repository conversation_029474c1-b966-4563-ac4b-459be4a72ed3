<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 项目岗位
 * Class ProjectController
 * @package Prime\Controller
 */
class ProjectpostController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    public function index() {
        $c_kw = [
            'name' => '岗位名称',
            'id' => 'ID',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);
        if ($s_status != '') $where['status'] = $s_status;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['name']))  {
                $where[$s_kw] = ['like', "%$s_val%"];
            }  else {
                $where[$s_kw] = $s_val;
            }
        }
        //公用搜索
        $contions = I('get.');
        $contionsWhere = ['group', 'status'];
        foreach ($contions as $whereKey => $row) {
            if (in_array($whereKey, $contionsWhere) && $row != '') {
                $where[$whereKey] = $row;
            }
        }

        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['create_time'] = ['between', [$s_start, $s_end]];
        }

        $obj = D("ProjectPost");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        if($list){
            $projectArrId = [];
            $projectArrId = array_unique(array_column($list, 'project_id'));
            $projectList = D("Project")->where(['id' => ['in', $projectArrId]])->getField('id,name,category', true);
            $this->assign('projectList', $projectList);
            $this->assign('categoryList', D("Project")->category);

            // 获取渠道信息
            $channelArrId = array_unique(array_column($list, 'channel_id'));
            $channelArrId = array_filter($channelArrId); // 过滤空值
            $channelList = [];
            if ($channelArrId) {
                $channelList = D("Channel")->where(['id' => ['in', $channelArrId]])->getField('id,name', true);
            }
            $this->assign('channelList', $channelList);
         }
        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '0');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '0');
        $this->assign('groupList', $obj->group);
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('sexList', $obj->sex);
        $this->assign('qualificationList', $obj->qualification);
        $this->assign('statusList', $obj->status);
        $this->assign('isOutList', $obj->is_out);
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 添加编辑
     */
    public function edit() {
        $id = intval(I('get.id'));
        $obj = D("ProjectPost");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        }
        $projectList = D("Project")->where(['status' => 0])->getField('id,name', true);
        $this->assign('projectList', $projectList);

        $projectJoinIdentity = [];
        if ($id) {
            // 获取岗位信息以确定项目ID
            $post = $obj->where("id=".$id)->find();
            if ($post) {
                // 使用项目ID和岗位ID双重条件查询，确保数据一致性
                $projectJoinIdentityList = D("ProjectJoinIdentity")->where([
                    'project_id' => $post['project_id'],
                    'project_post_id' => $id
                ])->select();
                foreach ($projectJoinIdentityList as $projectJoinIdentityRow) {
                    $projectJoinIdentity[$projectJoinIdentityRow['project_post_id']][$projectJoinIdentityRow['project_identity_id']] = $projectJoinIdentityRow['cost'];
                }
            }
        }
        $this->assign('projectJoinIdentity', $projectJoinIdentity);
        $projectIdentityList = D("ProjectIdentity")->where(['status' => 1])->Field('id,name')->select();
        $this->assign('projectIdentityList', $projectIdentityList);

        if (IS_POST) {
            if ($data = $obj->create()) {
                if ($data['project_id'] == 0) {
                    $this->error('请选择正确的项目');
                }

                // 自动添加免责声明到other_content字段
                $disclaimerText = '【以上为企业招聘提供的招聘信息，具体如工作时长、福利待遇等以用人单位确立为准，企业统一对待，无个例特殊，敬请放心！】';
                if (isset($data['other_content'])) {
                    $currentContent = trim($data['other_content']);
                    // 检查是否已包含免责声明
                    if (strpos($currentContent, $disclaimerText) === false) {
                        // 如果内容不为空，先添加换行
                        if (!empty($currentContent)) {
                            $currentContent .= "\n\n";
                        }
                        $currentContent .= $disclaimerText;
                        $data['other_content'] = $currentContent;
                    }
                }

                // 检查价格变动（仅编辑时）
                $priceChanged = false;
                $changeInfo = [];
                if ($id) {
                    $oldData = $obj->where("id=".$id)->find();
                    if ($oldData) {
                        \Think\Log::write('检查岗位价格变动: post_id=' . $id . ', job_name=' . $oldData['job_name'], 'DEBUG');

                        // 检查服务报价变动
                        if ($oldData['service_price'] != $data['service_price']) {
                            $priceChanged = true;
                            $changeInfo['service_price'] = [
                                'old' => $oldData['service_price'],
                                'new' => $data['service_price']
                            ];
                            \Think\Log::write('检测到服务报价变动: post_id=' . $id . ', old=' . $oldData['service_price'] . ', new=' . $data['service_price'], 'INFO');
                        }
                        // 检查最高报价变动
                        if ($oldData['max_price'] != $data['max_price']) {
                            $priceChanged = true;
                            $changeInfo['max_price'] = [
                                'old' => $oldData['max_price'],
                                'new' => $data['max_price']
                            ];
                            \Think\Log::write('检测到最高报价变动: post_id=' . $id . ', old=' . $oldData['max_price'] . ', new=' . $data['max_price'], 'INFO');
                        }

                        if (!$priceChanged) {
                            \Think\Log::write('岗位价格无变动: post_id=' . $id, 'DEBUG');
                        }
                    }
                }

                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);

                    // 如果价格发生变动，触发价格变动处理
                    if ($priceChanged) {
                        \Think\Log::write('触发岗位价格变动处理: post_id=' . $id . ', change_info=' . json_encode($changeInfo, JSON_UNESCAPED_UNICODE), 'INFO');
                        $priceChangeService = new \Common\Service\PostPriceChangeService();

                        $result = $priceChangeService->handlePriceChange($id, $changeInfo);
                        \Think\Log::write('岗位价格变动处理结果: post_id=' . $id . ', result=' . ($result ? 'success' : 'failed'), 'INFO');
                    }
                }
                $this->success("操作成功", U("projectpost/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->assign('qualificationList', $obj->qualification);
        $this->assign('sexList', $obj->sex);
        $this->assign('freeList', $obj->free);

        $projectList = D("Project")->getField('id,name', true);
        $this->assign('projectList', $projectList);

        $channelList = D("Channel")->getField('id,name', true);
        $this->assign('channelList', $channelList);
        $this->display();
    }

    /**
     * 状态变更
     */
    public function cgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("ProjectPost");
        if (!array_key_exists($status, $obj->status)) $this->error('参数错误 ');

        // 获取原状态
        $oldData = $obj->where("id=".$id)->find();
        if (!$oldData) {
            \Think\Log::write('岗位状态变更失败，岗位不存在: post_id=' . $id, 'ERROR');
            $this->error('岗位不存在');
        }

        \Think\Log::write('岗位状态变更: post_id=' . $id . ', job_name=' . $oldData['job_name'] . ', old_status=' . $oldData['status'] . ', new_status=' . $status, 'INFO');

        $obj->save(['id' => $id, 'status' => $status]);

        // 如果从上架变为下架，触发价格变动处理
        if ($oldData && $oldData['status'] == 1 && $status == 0) {
            \Think\Log::write('触发岗位下架价格变动处理: post_id=' . $id . ', job_name=' . $oldData['job_name'], 'INFO');
            $changeInfo = ['type' => 'status_change', 'from' => 1, 'to' => 0];
            $priceChangeService = new \Common\Service\PostPriceChangeService();

            $result = $priceChangeService->handlePriceChange($id, $changeInfo);
            \Think\Log::write('岗位下架价格变动处理结果: post_id=' . $id . ', result=' . ($result ? 'success' : 'failed'), 'INFO');
        } else {
            \Think\Log::write('岗位状态变更不触发价格处理: post_id=' . $id . ', old_status=' . $oldData['status'] . ', new_status=' . $status, 'DEBUG');
        }

        $this->success('修改成功');
    }



    /**
     * 状态变更
     */
    public function cgtop() {
        $id = I('get.id');
        $status = I('get.is_top');
        if (!$id) $this->error('参数错误');
        $obj = D("ProjectPost");
        if (!array_key_exists($status, $obj->is_top)) $this->error('参数错误 ');
        
        // 状态为1(置顶)时，记录当前时间戳到top_time字段
        $data = ['id' => $id, 'is_top' => $status];
        if($status == 1) {
            $data['top_time'] = time();
        }
        
        $obj->save($data);
        $this->success('修改成功');
    }
}