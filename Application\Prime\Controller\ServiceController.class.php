<?php
/**
 * 服务号
 * Created by PhpStorm.
 * User: yangbin
 * Date: 2019/5/20
 * Time: 14:44
 */

namespace Prime\Controller;


use Common\Controller\PrimeController;
use \LaneWeChat\Core as WE;

class ServiceController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    public function index()
    {
        $c_kw = [
            'id' => 'ID',
            'name' => '账号名称',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        if ($s_status != '') $where['status'] = $s_status;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if ($s_kw == 'name') {
                $where[$s_kw] = ['like', "%$s_val%"];
            } else {
                $where[$s_kw] = $s_val;
            }
        }
        $obj = D("Service");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        $this->assign('_get', I('get.'));
        $this->assign('list', $list);
        $this->assign("page", $page->show());
        $this->assign('statusList', $obj->status);
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 添加编辑
     */
    public function edit()
    {
        $id = intval(I('get.id'));
        $type = I('get.type', 1);
        $obj = D("Service");
        if ($id) {
            $row = $obj->where("id=" . $id)->find();
            if (!$row) $this->erorr('参数错误');
            $this->assign('row', $row);
        }
        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("service/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->display();
    }

    /**
     * 状态变更
     */
    public function cgstat()
    {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("Service");
        if (!array_key_exists($status, $obj->status)) $this->error('参数错误 ');
        $obj->save(['id' => $id, 'status' => $status]);
        $this->success('修改成功');
    }

    /**
     * 关注回复
     */
    public function subreply()
    {
        $id = I('get.id');
        if (!$id) $this->error('参数错误');
        $obj = D("Service");
        $row = $obj->where(['id' => $id])->find();
        if (!$row) $this->error('项目不存在');
        if (IS_POST) {
            $sub_content = I('post.sub_content');
            $obj->save(['id' => $id, 'sub_content' => htmlspecialchars_decode($sub_content)]);
            echo $this->success('修改成功', U('service/index'));
            die;
        }
        $this->assign('row', $row);
        $this->display();
    }

    /**
     * 菜单设置
     */
    public function menus()
    {
        $id = I('get.id');
        if (!$id) $this->erorr('参数错误');
        $obj = D("Service");
        $row = $obj->where("id=" . $id)->find();
        if (!$row) $this->erorr('参数错误');

        if (IS_POST) {
            $data = I('post.data');
            $menuList = $this->getbatchmenu($data);
//            if (!$menuList) $this->error('菜单填写错误,菜单不能为空');
            D("Conf")->set('WX_BATCH_MENU', json_encode($menuList));
            $obj->where(['id' => $row['id']])->save(['menu_content' => json_encode($menuList)]);
            $this->success("操作成功", U("service/index", ['type' => $row['type']]));
            exit;
        }
        $data = $row['menu_content'];
        if (!empty($data)) {
            if (I('get.clean')) {
                $menuData = [];
            } else {
                $menuData = json_decode($data, true);
                $menuData = $menuData['button'];
            }
            $this->assign('menuData', $menuData);
        }
        $this->assign('row', $row);
        $this->assign('id', $id);
        $this->display();

    }

    /**
     * 分析二级菜单
     * */
    public function getbatchmenu($data)
    {
        $menuList = [];
        foreach ($data as $val) {

            $array = [];
            foreach ($val['sub_button'] as $v) {
                if ($v['type'] == 'miniprogram') {
                    $array[] = [
                        'type' => $v['type'] ?: 'view',
                        'name' => !empty($v['name']) ? $v['name'] : '参数错误',
                        'pagepath' => !empty($v['pagepath']) ? $v['pagepath'] : '参数错误',
                        'appid' => !empty($v['appid']) ? $v['appid'] : '参数错误',
                    ];

                } else {
                    if (empty($v['name']) && empty($v['url'])) continue;
                    $array[] = [
                        'type' => $v['type'] ?: 'view',
                        'name' => !empty($v['name']) ? $v['name'] : '参数错误',
                        'url' => !empty($v['url']) ? htmlspecialchars_decode($v['url']) : '参数错误',
                    ];
                }
            }
            if (!$array) {
                if ($val['type'] == 'miniprogram') {
                    $data = [
                        'type' => $val['type'] ?: 'view',
                        'name' => !empty($val['name']) ? $val['name'] : '参数错误',
                        'pagepath' => !empty($val['pagepath']) ? $val['pagepath'] : '参数错误',
                        'appid' => !empty($val['appid']) ? $val['appid'] : '参数错误',
                        'url' => 'http://m.zigouba.cn/outer',
                    ];
                } else {
                    if (empty($val['name']) && empty($val['url'])) continue;
                    $data = [
                        'type' => $val['type'] ?: 'view',
                        'name' => !empty($val['name']) ? $val['name'] : '参数错误',
                        'url' => !empty($val['url']) ? htmlspecialchars_decode($val['url']) : '参数错误',
                    ];
                }
            } else {
                $data = [
                    'name' => !empty($val['name']) ? $val['name'] : '参数错误',
                    'sub_button' => $array,
                ];
            }
            $menuList['button'][] = $data;
        }
        return $menuList;
    }

    /**
     * 推送菜单
     * */
    public function taskmenu()
    {
        header('Content-type:text/html;charset=utf-8');
        $id = I('get.id');
        if (!$id) $this->erorr('参数错误');
        $obj = D("Service");
        $row = $obj->where("id=" . $id)->find();
        if (!$row) $this->erorr('参数错误');
        $conf = [
            'WX_TOKEN' => $row['wx_token'],
            'WX_APPID' => $row['appid'],
            'WX_APPSECRET' => $row['secret'],
        ];
        vendor('LaneWeChat.lanewechat');
        WE\Base::init($conf);

        $menuList = json_decode($row['menu_content'], true);

        $menuList = $this->ckmenulist($menuList);
        if (!$menuList) {
            $this->error('菜单不能为空', U('service/index'));
        }
        $menuList = json_encode($menuList, JSON_UNESCAPED_UNICODE);
        $data = WE\Menu::setMenuJson($menuList);
        if ($data == true) {
            $this->success('菜单推送成功', U('service/index', ['type' => $row['type']]));
            exit;
        }
        $this->error('菜单推送失败', U('service/index', ['type' => $row['type']]));
    }


    public function ckmenulist($data)
    {
        $return = true;
        foreach ($data['button'] as &$val) {
            if (strpos($val['name'], '参数错误') !== false || strpos($val['val'], '参数错误') !== false) {
                $return = false;
                echo 'errr';
                break;
            }
            if ($val['type'] == 'click') {
                $val['key'] = htmlspecialchars_decode($val['url']);
                unset($val['url']);
            }
            if (isset($val['sub_button'])) {
                foreach ($val['sub_button'] as &$v) {
                    if (strpos($v['name'], '参数错误') !== false || strpos($v['val'], '参数错误') !== false) {
                        $return = false;
                        echo 'errr';
                        break;
                    }
                    if ($v['type'] == 'click') {
                        $v['key'] = htmlspecialchars_decode($v['url']);
                        unset($v['url']);
                    }
                }

                if (!$return) break;
            }
        }
        return $data;
    }

    /**
     * id 批量删除
     * */
    public function delmenu()
    {
        header('Content-type:text/html;charset=utf-8');
        $id = I('get.id');
        if (!$id) $this->erorr('参数错误');
        $obj = D("Service");
        $row = $obj->where("id=" . $id)->find();
        if (!$row) $this->erorr('参数错误');
        $conf = [
            'WX_TOKEN' => $row['wx_token'],
            'WX_APPID' => $row['appid'],
            'WX_APPSECRET' => $row['secret'],
        ];
        vendor('LaneWeChat.lanewechat');
        WE\Base::init($conf);
        $data = WE\Menu::delMenu();
        if ($data['errcode'] == 0 && $data['errmsg'] == 'ok') {
            $this->success('删除成功', U('service/index', ['type' => $row['type']]));
            exit;
        }
        $this->error('删除失败', U('service/index', ['type' => $row['type']]));
    }

    /**
     * 上传图片
     */
    public function uploadimg($row, $imgUrl)
    {
        $conf = [
            'WX_TOKEN' => $row['wx_token'],
            'WX_APPID' => $row['appid'],
            'WX_APPSECRET' => $row['secret'],
        ];
        vendor('LaneWeChat.lanewechat');
        WE\Base::init($conf);
        $img = SITE_PATH . $imgUrl;
        $simg = file_get_contents($img);

        if (!$simg) {

        }
        $finfo = getimagesizefromstring($simg);
        $ext = substr($finfo['mime'], 6);
        if ($ext == 'jpeg') $ext = 'jpg';
        $name = md5($simg) . ".$ext";
        $path = SITE_PATH . C("UPLOADPATH") . '/' . md5($simg) . $name;
        $res = file_put_contents($path, $simg);
        $resRow = WE\Media::uploadimgs($path);
        @unlink($path);
        if (isset($resRow['errcode']) && $resRow['errcode'] || empty($resRow['url'])) {
            return ['code' => 1, 'msg' => '上传永久素材错误', 'data' => $resRow];
        } else {
            return ['code' => 0, 'data' => ['url' => $resRow['url']]];
        }
    }

}
