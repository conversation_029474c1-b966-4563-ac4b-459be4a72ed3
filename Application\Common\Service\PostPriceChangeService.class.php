<?php
namespace Common\Service;

use Think\Model;

/**
 * 岗位价格变动处理服务类
 * 处理岗位价格变动时的自动化逻辑：下架招就办岗位配置并清零价格
 */
class PostPriceChangeService
{
    /**
     * 处理岗位价格变动
     * @param int $postId 岗位ID
     * @param array $changeInfo 变动信息
     * @return bool
     */
    public function handlePriceChange($postId, $changeInfo = [])
    {
        \Think\Log::write('开始处理岗位价格变动: post_id=' . $postId . ', change_info=' . json_encode($changeInfo, JSON_UNESCAPED_UNICODE), 'INFO');

        try {
            // 开启事务
            $model = new Model();
            $model->startTrans();
            \Think\Log::write('数据库事务已开启: post_id=' . $postId, 'DEBUG');

            // 获取岗位信息
            $postInfo = D('ProjectPost')->where(['id' => $postId])->find();
            if (!$postInfo) {
                \Think\Log::write('岗位不存在: post_id=' . $postId, 'ERROR');
                throw new \Exception('岗位不存在');
            }
            \Think\Log::write('获取岗位信息成功: post_id=' . $postId . ', job_name=' . $postInfo['job_name'], 'DEBUG');

            // 获取受影响的招就办配置
            $affectedConfigs = $this->getAffectedZsbConfigs($postId);

            if (empty($affectedConfigs)) {
                \Think\Log::write('没有受影响的招就办配置: post_id=' . $postId, 'INFO');
                $model->commit();
                return true; // 没有受影响的配置，直接返回成功
            }
            \Think\Log::write('找到受影响的招就办配置: post_id=' . $postId . ', 数量=' . count($affectedConfigs), 'INFO');

            // 下架并清零价格配置
            $clearResult = $this->disableAndClearPrices($postId);
            \Think\Log::write('价格配置清零结果: post_id=' . $postId . ', result=' . ($clearResult ? 'success' : 'failed'), 'INFO');

            // 收集通知信息
            $notificationData = $this->prepareNotificationData($postInfo, $affectedConfigs, $changeInfo);
            \Think\Log::write('准备通知数据完成: post_id=' . $postId . ', 通知数量=' . count($notificationData), 'DEBUG');

            // 发送通知
            $this->sendNotifications($notificationData);

            $model->commit();
            \Think\Log::write('数据库事务提交成功: post_id=' . $postId, 'DEBUG');

            // 记录日志
            \Think\Log::write('岗位价格变动处理完成: post_id=' . $postId . ', 受影响配置数量=' . count($affectedConfigs), 'INFO');

            return true;

        } catch (\Exception $e) {
            $model->rollback();
            \Think\Log::write('岗位价格变动处理失败，事务已回滚: post_id=' . $postId . ', error=' . $e->getMessage() . ', file=' . $e->getFile() . ', line=' . $e->getLine(), 'ERROR');
            return false;
        }
    }



    /**
     * 获取受影响的招就办配置
     * @param int $postId 岗位ID
     * @return array
     */
    private function getAffectedZsbConfigs($postId)
    {
        \Think\Log::write('开始查询受影响的招就办配置: post_id=' . $postId, 'DEBUG');

        $zsbPriceModel = D('ZsbPostPrice');

        // 查询该岗位的所有有效配置
        $configs = $zsbPriceModel->alias('zpp')
            ->join('LEFT JOIN __SERVICE_STATION__ ss ON zpp.zsb_id = ss.id')
            ->join('LEFT JOIN __SERVICE_STATION__ ref_ss ON ss.zsb_ref_station = ref_ss.id')
            ->where([
                'zpp.post_id' => $postId,
                'zpp.status' => 1,
                'ss.status' => 1
            ])
            ->field('zpp.id, zpp.zsb_id, zpp.cost_price, zpp.sale_price, zpp.commission, zpp.fee,
                     ss.service_name as zsb_name, ss.contract_name, ss.zsb_ref_station,
                     ref_ss.service_name as station_name, ref_ss.chatroom, ref_ss.wxatuserlist')
            ->select();

        $configCount = is_array($configs) ? count($configs) : 0;
        \Think\Log::write('查询招就办配置完成: post_id=' . $postId . ', 找到配置数量=' . $configCount, 'DEBUG');

        if ($configCount > 0) {
            $zsbIds = array_column($configs, 'zsb_id');
            \Think\Log::write('受影响的招就办ID列表: post_id=' . $postId . ', zsb_ids=' . implode(',', $zsbIds), 'DEBUG');
        }

        return $configs ?: [];
    }

    /**
     * 下架并清零价格配置
     * @param int $postId 岗位ID
     * @return bool
     */
    private function disableAndClearPrices($postId)
    {
        \Think\Log::write('开始下架并清零价格配置: post_id=' . $postId, 'DEBUG');

        $zsbPriceModel = D('ZsbPostPrice');

        // 查询需要清零的配置：只处理有价格数据的配置
        $needClearWhere = [
            'post_id' => $postId,
            '_complex' => [
                'cost_price' => ['neq', 0],
                'sale_price' => ['neq', 0],
                'commission' => ['neq', 0],
                'fee' => ['neq', 0],
                '_logic' => 'OR'
            ]
        ];

        $affectedCount = $zsbPriceModel->where($needClearWhere)->count();

        \Think\Log::write('准备更新的价格配置数量: post_id=' . $postId . ', count=' . $affectedCount . ' (仅包含有价格数据的配置)', 'DEBUG');

        if ($affectedCount == 0) {
            \Think\Log::write('没有需要清零的价格配置: post_id=' . $postId, 'DEBUG');
            return true;
        }

        // 批量更新：下架并清零价格（只处理有价格数据的配置）
        $updateData = [
            'status' => 0,
            'cost_price' => 0,
            'sale_price' => 0,
            'commission' => 0,
            'fee' => 0,
            'update_time' => time()
        ];

        \Think\Log::write('执行批量更新操作: post_id=' . $postId . ', update_data=' . json_encode($updateData), 'DEBUG');

        $result = $zsbPriceModel->where($needClearWhere)->save($updateData);

        $success = $result !== false;
        \Think\Log::write('价格配置更新结果: post_id=' . $postId . ', success=' . ($success ? 'true' : 'false') . ', affected_rows=' . ($result ?: 0), 'INFO');

        return $success;
    }

    /**
     * 准备通知数据
     * @param array $postInfo 岗位信息
     * @param array $affectedConfigs 受影响的配置
     * @param array $changeInfo 变动信息
     * @return array
     */
    private function prepareNotificationData($postInfo, $affectedConfigs, $changeInfo)
    {
        \Think\Log::write('开始准备通知数据: post_id=' . $postInfo['id'] . ', config_count=' . count($affectedConfigs), 'DEBUG');

        // 获取项目信息
        $projectInfo = D('Project')->where(['id' => $postInfo['project_id']])->find();
        if (!$projectInfo) {
            \Think\Log::write('获取项目信息失败: project_id=' . $postInfo['project_id'], 'ERROR');
        } else {
            \Think\Log::write('获取项目信息成功: project_id=' . $postInfo['project_id'] . ', project_name=' . $projectInfo['name'], 'DEBUG');
        }

        // 按服务站分组通知数据
        $notificationGroups = [];
        $skippedCount = 0;

        foreach ($affectedConfigs as $config) {
            $stationId = $config['zsb_ref_station'] ?: $config['zsb_id'];
            $stationName = $config['station_name'] ?: $config['zsb_name'];
            $chatroom = $config['chatroom'];
            $wxatuserlist = $config['wxatuserlist'];

            \Think\Log::write('处理配置: zsb_id=' . $config['zsb_id'] . ', station_id=' . $stationId . ', has_chatroom=' . (!empty($chatroom) ? 'yes' : 'no') . ', has_wxatuserlist=' . (!empty($wxatuserlist) ? 'yes' : 'no'), 'DEBUG');

            // 如果没有微信配置，跳过通知
            if (empty($chatroom) || empty($wxatuserlist)) {
                $skippedCount++;
                \Think\Log::write('跳过通知（缺少微信配置）: zsb_id=' . $config['zsb_id'] . ', station_id=' . $stationId, 'WARN');
                continue;
            }

            if (!isset($notificationGroups[$stationId])) {
                $notificationGroups[$stationId] = [
                    'station_name' => $stationName,
                    'chatroom' => $chatroom,
                    'wxatuserlist' => $wxatuserlist,
                    'affected_zsbs' => []
                ];
                \Think\Log::write('创建新的通知组: station_id=' . $stationId . ', station_name=' . $stationName, 'DEBUG');
            }

            $notificationGroups[$stationId]['affected_zsbs'][] = [
                'zsb_name' => $config['zsb_name'],
                'contact_name' => $config['contract_name'],
                'original_cost_price' => intval($config['cost_price']) / 100, // 转换为元
                'original_sale_price' => intval($config['sale_price']) / 100, // 转换为元
            ];
        }

        \Think\Log::write('通知数据分组完成: post_id=' . $postInfo['id'] . ', 通知组数量=' . count($notificationGroups) . ', 跳过数量=' . $skippedCount, 'INFO');
        
        // 为每个服务站准备通知内容
        $notifications = [];
        foreach ($notificationGroups as $stationId => $group) {
            $notifications[] = [
                'station_id' => $stationId,
                'station_name' => $group['station_name'],
                'chatroom' => $group['chatroom'],
                'wxatuserlist' => $group['wxatuserlist'],
                'post_name' => $postInfo['job_name'],
                'project_name' => $projectInfo['name'],
                'change_info' => $changeInfo,
                'affected_zsbs' => $group['affected_zsbs'],
                'change_time' => date('Y-m-d H:i:s')
            ];
        }
        
        return $notifications;
    }

    /**
     * 发送通知
     * @param array $notifications 通知数据
     * @return void
     */
    private function sendNotifications($notifications)
    {
        \Think\Log::write('开始发送通知: 通知数量=' . count($notifications), 'INFO');

        $successCount = 0;
        $failCount = 0;

        foreach ($notifications as $notification) {
            try {
                \Think\Log::write('准备发送通知: station_id=' . $notification['station_id'] . ', station_name=' . $notification['station_name'], 'DEBUG');

                $content = $this->buildNotificationContent($notification);
                \Think\Log::write('通知内容构建完成: station_id=' . $notification['station_id'] . ', content_length=' . strlen($content), 'DEBUG');

                $this->sendWechatNotification($content, $notification['chatroom'], $notification['wxatuserlist']);

                $successCount++;
                \Think\Log::write('价格变动通知发送成功: station_id=' . $notification['station_id'] . ', station_name=' . $notification['station_name'], 'INFO');

            } catch (\Exception $e) {
                $failCount++;
                \Think\Log::write('价格变动通知发送失败: station_id=' . $notification['station_id'] . ', station_name=' . $notification['station_name'] . ', error=' . $e->getMessage() . ', file=' . $e->getFile() . ', line=' . $e->getLine(), 'ERROR');
            }
        }

        \Think\Log::write('通知发送完成: 成功=' . $successCount . ', 失败=' . $failCount, 'INFO');
    }

    /**
     * 构建通知内容
     * @param array $notification 通知数据
     * @return string
     */
    private function buildNotificationContent($notification)
    {
        \Think\Log::write('开始构建通知内容: station_id=' . $notification['station_id'] . ', post_name=' . $notification['post_name'], 'DEBUG');

        $changeInfo = $notification['change_info'];
        $isStatusChange = isset($changeInfo['type']) && $changeInfo['type'] === 'status_change';

        \Think\Log::write('通知类型判断: station_id=' . $notification['station_id'] . ', is_status_change=' . ($isStatusChange ? 'true' : 'false'), 'DEBUG');
        
        if ($isStatusChange) {
            // 岗位下架通知模板
            $content = $notification['station_name'] . "\n";
            $content .= "【岗位下架通知】\n\n";
            
            $content .= "以下岗位已被平台下架，相关招就办的岗位配置已自动清零：\n\n";
            $content .= "岗位名称：" . $notification['post_name'] . "\n";
            $content .= "项目名称：" . $notification['project_name'] . "\n";
            $content .= "下架原因：平台管理员操作\n\n";
            
            // 受影响招就办
            $zsbContactList = $this->formatZsbContactList($notification['affected_zsbs']);
            $content .= "受影响招就办：" . $zsbContactList ;
            
        } else {
            // 岗位价格变动通知模板
            $content = $notification['station_name']."\n";
            $content .= "【岗位价格变动通知】\n\n";
    
            $content .= "以下岗位价格发生变动，相关招就办的岗位配置已自动清零下架：\n\n";
            $content .= "岗位名称：" . $notification['post_name'] . "\n";
            $content .= "项目名称：" . $notification['project_name'] . "\n";
            
            // 显示价格变动信息
            if (isset($changeInfo['service_price'])) {
                $content .= "服务报价：" . $changeInfo['service_price']['old'] . "元 → " . $changeInfo['service_price']['new'] . "元\n";
            }
            if (isset($changeInfo['max_price'])) {
                $content .= "最高报价：" . $changeInfo['max_price']['old'] . "元 → " . $changeInfo['max_price']['new'] . "元\n";
            }
            if (isset($changeInfo['identity_cost'])) {
                $content .= "服务站成本：" . $changeInfo['identity_cost']['old'] . "元 → " . $changeInfo['identity_cost']['new'] . "元\n";
            }
            
            // 受影响招就办
            $zsbContactList = $this->formatZsbContactList($notification['affected_zsbs']);
            $content .= "\n受影响招就办：" . $zsbContactList . "\n\n";
            $content .= "请及时登录管理后台重新配置相关岗位价格。\n";
        }

        

        \Think\Log::write('通知内容构建完成: station_id=' . $notification['station_id'] . ', content_preview=' . substr($content, 0, 100) . '...', 'DEBUG');

        return $content;
    }

    /**
     * 格式化招就办联系人列表显示
     * @param array $affectedZsbs 受影响的招就办数组
     * @param int $maxDisplay 最大显示数量，超过此数量将使用省略格式
     * @return string 格式化后的招就办联系人字符串
     */
    private function formatZsbContactList($affectedZsbs, $maxDisplay = 10)
    {
        $zsbNames = [];
        foreach ($affectedZsbs as $zsb) {
            $zsbNames[] = $zsb['contact_name'];
        }

        $totalCount = count($zsbNames);

        if ($totalCount <= $maxDisplay) {
            // 数量不超过阈值，直接显示所有联系人
            return implode('、', $zsbNames);
        } else {
            // 数量超过阈值，使用省略格式
            $displayNames = array_slice($zsbNames, 0, $maxDisplay);
            return implode('、', $displayNames) . '等' . $totalCount . '个招就办';
        }
    }

    /**
     * 发送微信通知
     * @param string $content 通知内容
     * @param string $chatroom 群聊ID
     * @param string $wxatuserlist 用户列表
     * @return void
     */
    private function sendWechatNotification($content, $chatroom, $wxatuserlist)
    {
        \Think\Log::write('开始发送微信通知: chatroom=' . $chatroom . ', wxatuserlist=' . $wxatuserlist, 'DEBUG');

        // 构建POST数据
        $postData = [
            'WxID' => 'zcgk666',
            'Data' => [
                'SendTo' => $chatroom,
                'Msg' => '@' . $content,
                'AtUserList' => $wxatuserlist,
                'ArgList' => ['SendTo', 'Msg', 'AtUserList'],
                'AnyCallName' => 'SendMessageAt',
                'TimeOut' => -1,
                'CallName' => 'Any'
            ],
            'CallBackUrl' => null
        ];

        // 转换为JSON格式
        $jsonData = json_encode($postData, JSON_UNESCAPED_UNICODE);

        // 记录发送日志（不包含完整内容，避免日志过长）
        \Think\Log::write('微信通知请求数据: chatroom=' . $chatroom . ', msg_length=' . strlen($content) . ', wxatuserlist=' . $wxatuserlist, 'DEBUG');

        // 初始化cURL
        $ch = curl_init();
        $url = 'http://43.139.30.237:1080/HTTPManagement.aspx?Item=AddMessage';
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        \Think\Log::write('发送微信通知请求: url=' . $url . ', timeout=10s', 'DEBUG');

        // 执行请求
        $startTime = microtime(true);
        $response = curl_exec($ch);
        $endTime = microtime(true);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        $duration = round(($endTime - $startTime) * 1000, 2); // 转换为毫秒
        \Think\Log::write('微信通知请求完成: http_code=' . $httpCode . ', duration=' . $duration . 'ms, response_length=' . strlen($response), 'DEBUG');

        if ($error) {
            \Think\Log::write('微信通知cURL错误: ' . $error, 'ERROR');
            throw new \Exception('微信通知发送失败: ' . $error);
        }

        if ($httpCode !== 200) {
            \Think\Log::write('微信通知HTTP错误: code=' . $httpCode . ', response=' . $response, 'ERROR');
            throw new \Exception('微信通知发送失败: HTTP ' . $httpCode);
        }

        \Think\Log::write('微信通知发送成功: chatroom=' . $chatroom . ', response=' . $response, 'INFO');
    }


}
