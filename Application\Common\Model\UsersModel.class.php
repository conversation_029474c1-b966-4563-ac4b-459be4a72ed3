<?php
// 管理员模型
namespace Common\Model;

use Think\Model;

class UsersModel extends Model
{

	protected $_validate = [
		//[验证字段,验证规则,错误提示,验证条件,附加规则,验证时间)
		['username', 'require', '用户名不能为空！', 1, 'regex', CommonModel::MODEL_INSERT],
		['pwd', 'require', '密码不能为空！', 1, 'regex', CommonModel::MODEL_INSERT],
        //['pwd', 'require', '密码不能为空！', 0, 'regex', CommonModel::MODEL_UPDATE],
		['username','','用户名已经存在！', 0, 'unique', CommonModel::MODEL_BOTH], // 验证username字段是否唯一
	];

    protected $_auto = [
        ['created', 'time', 1, 'function'],
        ['updated', 'time', 2, 'function'],
    ];

    public function login($user)
    {
        $role_id = intval($user['role_id']);
        if (!$role_id) $role_id = 1;
    	$rolename = M('Role')->where("id=$role_id")->getField('name');
        session('admin_id', $user['id']);
        session('admin_name', $user['username']);
        session('admin_role', $rolename?:'管理员');
        session('admin_role_id', $user['role_id']);
    }

    public function logLogin($user)
    {
        $data = [
            'login_count' => ['exp', 'login_count+1'],
            'last_login' => time(),
            'last_ip' => ip2long(get_client_ip()),
        ];
        $this->where("id={$user['id']}")->save($data);
    }

    /**
     * 登录记录
     * @param array $user 用户数组
     */
    public function loginLog($user)
    {
        $data = [
            'login_count' => ['exp', 'login_count+1'],
            'last_login' => $user[ 'cur_login'],
            'last_ip' => $user['cur_ip'],
            'cur_login' => time(),
            'cur_ip' => ip2long(get_client_ip()),
        ];
        $this->where("id={$user['id']}")->save($data);
    }

    /**
     * 登出
     */
    public function logout()
    {
        session(null);
    }

    protected function _before_write(&$data)
    {
		parent::_before_write($data);
		if(empty($data['pwd'])) {
            unset($data['pwd']);
		}
	}

}
