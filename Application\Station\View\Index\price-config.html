<!DOCTYPE html>
<html>
<head>
<title>招就办价格配置</title>
<meta name="Keywords" content="关键字">
<meta name="Description" content="内容">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
<meta content="no-cache,must-revalidate" http-equiv="Cache-Control">
<meta content="telephone=no, address=no" name="format-detection">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta content="no-cache" http-equiv="pragma">
<meta content="0" http-equiv="expires">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"> 
<link rel="stylesheet" type="text/css" href="/static/stations/css/css.css?v={:time()}" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
<script src="/static/stations/js/jquery-1.9.1.min.js"></script>
<script src="/static/stations/js/swiper-bundle.min.js"></script>
<style>
/* 重置和基础样式 - 覆盖主站的宽度限制 */
body {
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    max-width: none !important; /* 覆盖主站的750px限制 */
    width: 100% !important;
}

/* 确保页面容器能够使用全宽，但保持头部组件的正常显示 */
.bg01, .topbar, .nav {
    max-width: none !important;
}

/* 确保隐藏元素保持隐藏状态 */
[style*="display: none"], [style*="display:none"] {
    display: none !important;
}

.hidden, .hide {
    display: none !important;
}

/* 价格配置页面专用样式 */
body.price-config-page {
    overflow-x: hidden;
}

/* 确保页面底部清洁 */
body.price-config-page::after {
    content: "";
    display: block;
    clear: both;
    height: 0;
}

/* 头部组件的weap容器保持原有样式 */
.topbar .weap, .nav .weap {
    max-width: 750px;
    margin: 0 auto;
    padding-left: 10px;
    padding-right: 10px;
}

/* 公告栏特殊处理 */
.notice {
    max-width: none !important;
    height: auto;
    overflow: visible;
}

.notice .weap {
    max-width: 750px;
    margin: 0 auto;
    padding-left: 10px;
    padding-right: 10px;
    height: auto;
}

/* 只有价格配置容器使用更大的宽度 */
.price-config-container .weap {
    max-width: 1400px;
    margin: 0 auto;
    padding-left: 20px;
    padding-right: 20px;
}

/* 确保公告栏的Swiper正常显示 */
.noticeSwiper {
    width: 100%;
    max-width: none;
    height: 40px !important;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 5px rgba(0,0,0,.1);
    overflow: hidden !important; /* 关键：隐藏超出容器的内容 */
    position: relative;
}

.noticeSwiper .swiper-wrapper {
    width: 100%;
    height: 40px;
}

.noticeSwiper .swiper-slide {
    width: 100%;
    height: 40px !important;
    display: flex;
    align-items: center;
    flex-shrink: 0; /* 防止slide被压缩 */
}

/* 确保公告栏文字正确显示 */
.noticeSwiper .swiper-slide .a {
    font-size: 13px;
    color: #f36722;
    margin-left: 35px;
    display: block;
    line-height: 40px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding-right: 50px;
    position: relative;
    width: 100%;
}

/* 公告图标 */
.noticeSwiper .icon-gonggao {
    position: absolute;
    z-index: 2;
    left: 5px;
    top: 0;
    font-size: 24px;
    display: block;
    line-height: 40px;
    color: #f36722;
}

/* 箭头图标 */
.noticeSwiper .swiper-slide .icon-jinrujiantouxiao {
    font-size: 12px;
    color: #999;
    position: absolute;
    display: block;
    z-index: 2;
    right: 10px;
    top: 0;
    line-height: 40px;
}

.price-config-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 200px);
    background-color: #f5f5f5;
    width: 95%;
    position: relative;
    z-index: 1;
}
.selector-card, .price-config-card {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.selector-card:hover, .price-config-card:hover {
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.selector-card h3, .price-config-card h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
}

.selector-card h3 i, .price-config-card h3 i {
    color: #007bff;
    font-size: 20px;
}

.selector-card select {
    width: 100%;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    background-color: #fff;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 12px;
}

.selector-card select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}


/* 价格配置表单样式 */
.price-form {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.post-info {
    background: #e3f2fd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border-left: 4px solid #2196f3;
}

.post-info h4 {
    margin: 0 0 10px 0;
    color: #1976d2;
    font-size: 16px;
}

.post-info p {
    margin: 5px 0;
    color: #666;
    font-size: 14px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-col {
    flex: 1;
}

.form-col label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: bold;
    font-size: 14px;
}

.form-col input, .form-col select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-col input:focus, .form-col select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.commission-info {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
}

.commission-info .commission-amount {
    font-size: 24px;
    font-weight: bold;
    color: #155724;
    margin-bottom: 5px;
}

.commission-info .commission-label {
    font-size: 14px;
    color: #155724;
}

.btn-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.btn-primary {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

/* 筛选区域样式 */
.filter-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.filter-container {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 300px;
}

.search-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
    outline: none;
}

.search-btn {
    padding: 10px 15px;
    background: #007bff;
    color: white;
    border: 1px solid #007bff;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-btn:hover {
    background: #0056b3;
}

.project-filter select, .status-filter select {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-width: 150px;
}

/* 筛选加载提示样式 */
.filter-loading {
    text-align: center;
    padding: 10px;
    color: #666;
    font-size: 14px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-top: 10px;
}

.filter-loading span {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.filter-loading span::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid #e3e3e3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 批量操作工具栏样式 - 临时隐藏 */
.batch-toolbar {
    background: #ffffff;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e5e7eb;
    display: none !important; /* 隐藏批量操作工具栏 */
}

.batch-toolbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.batch-selection-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.batch-select-all {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 500;
    color: #333;
    user-select: none;
}

.batch-select-all input[type="checkbox"] {
    display: none;
}

.batch-select-all .checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    background: white;
}

.batch-select-all .checkmark:hover {
    border-color: #3b82f6;
}

.batch-select-all input[type="checkbox"]:checked + .checkmark {
    background: #3b82f6;
    border-color: #3b82f6;
}

.batch-select-all input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 半选状态样式 */
.batch-select-all input[type="checkbox"]:indeterminate + .checkmark {
    background: #f59e0b;
    border-color: #f59e0b;
}

.batch-select-all input[type="checkbox"]:indeterminate + .checkmark::after {
    content: '−';
    color: white;
    font-size: 14px;
    font-weight: bold;
}

.selected-count {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.batch-actions {
    display: flex;
    gap: 10px;
}

.batch-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
}

.batch-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.batch-btn-primary {
    background: #3b82f6;
    color: white;
}

.batch-btn-primary:hover:not(:disabled) {
    background: #2563eb;
}

.batch-btn-secondary {
    background: #6b7280;
    color: white;
}

.batch-btn-secondary:hover {
    background: #4b5563;
}

/* 岗位卡片容器样式 */
.posts-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 24px;
}

/* 岗位卡片样式 */
.post-card {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    background: #ffffff;
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.post-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #d1d5db;
}

.post-card.configured {
    border-left: 4px solid #10b981;
}

.post-card.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59,130,246,0.1);
}

/* 岗位卡片复选框 - 临时隐藏 */
.post-card-checkbox {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 10;
    display: none !important; /* 隐藏岗位卡片复选框 */
}

.post-card-checkbox input[type="checkbox"] {
    display: none;
}

.post-card-checkbox .checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    transition: all 0.2s ease;
    cursor: pointer;
}

.post-card-checkbox .checkmark:hover {
    border-color: #3b82f6;
}

.post-card-checkbox input[type="checkbox"]:checked + .checkmark {
    background: #3b82f6;
    border-color: #3b82f6;
}

.post-card-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.post-card-header {
    margin-bottom: 16px;
    margin-left: 0px; /* 隐藏复选框后移除左边距 */
}

.post-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.post-project {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
    font-weight: 400;
}

.post-meta {
    margin-top: 8px;
}

.post-card-body {
    margin-bottom: 20px;
    margin-top: 16px;
}



.price-info {
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.price-info.configured {
    background: #f0fdf4;
    border-color: #bbf7d0;
}

.price-info.not-configured {
    background: #fffbeb;
    border-color: #fed7aa;
    text-align: center;
    color: #92400e;
    font-weight: 500;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.price-row:last-child {
    margin-bottom: 0;
}

.price-label {
    font-size: 14px;
    color: #6b7280;
}

.price-value {
    font-size: 14px;
    font-weight: 600;
    color: #111827;
}

.post-card-footer {
    text-align: center;
}

.config-btn {
    width: 100%;
    padding: 12px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.config-btn:hover {
    background: #2563eb;
}

.config-btn.configured {
    background: #10b981;
}

.config-btn.configured:hover {
    background: #059669;
}

/* 状态标签 */
.status-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    z-index: 5;
}

.status-badge.configured {
    background: #dcfce7;
    color: #166534;
}

.status-badge.not-configured {
    background: #fef3c7;
    color: #92400e;
}

/* 弹窗样式 */
.price-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #333;
}

/* 批量价格配置弹窗样式 - 临时隐藏 */
.batch-modal-content {
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    display: none !important; /* 隐藏批量价格配置弹窗 */
}

.batch-posts-preview {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    max-height: 200px;
    overflow-y: auto;
}

.batch-posts-preview h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.batch-post-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.batch-post-item:last-child {
    border-bottom: none;
}

.batch-post-name {
    font-weight: 500;
    color: #333;
    flex: 1;
}

.batch-post-project {
    font-size: 12px;
    color: #666;
    margin-left: 10px;
}

.batch-post-service-price {
    font-size: 12px;
    color: #28a745;
    font-weight: 500;
    min-width: 80px;
    text-align: right;
}

.batch-price-form {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.batch-price-mode {
    margin-bottom: 20px;
}

.batch-price-mode label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: #333;
}

.batch-price-mode input[type="radio"] {
    margin-right: 8px;
}

.batch-price-inputs {
    display: none;
}

.batch-price-inputs.active {
    display: block;
}

/* 价格格式化提示样式 */
.price-input-group {
    position: relative;
    display: flex;
    flex-direction: column;
}

.price-format-hint {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    line-height: 1.3;
}

.price-format-hint.active {
    color: #007bff;
    font-weight: 500;
}

.formatted-result {
    font-size: 11px;
    color: #28a745;
    margin-top: 2px;
    font-weight: 500;
}

.formatted-result.warning {
    color: #ffc107;
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 15px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
}

.pagination-nav {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.pagination-btn:hover:not(:disabled) {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.page-size-selector select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.modal-body {
    padding: 20px;
}

/* 表单样式 */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.form-col {
    flex: 1;
}

.form-col label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
}

.form-col input, .form-col select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        max-width: 750px !important; /* 移动端恢复原有限制 */
    }

    .price-config-container {
        padding: 10px;
        width: 100%;
    }

    .price-config-container .weap {
        max-width: none;
        padding: 0 10px;
    }

    /* 移动端头部组件保持原样 */
    .topbar .weap, .nav .weap, .notice .weap {
        padding-left: 10px;
        padding-right: 10px;
    }

    .selector-card, .price-config-card {
        padding: 15px;
        margin-bottom: 15px;
    }

    .selector-card h3, .price-config-card h3 {
        font-size: 16px;
    }

    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .btn-group {
        flex-direction: column;
        gap: 10px;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        padding: 15px;
        font-size: 16px;
    }

    .commission-info .commission-amount {
        font-size: 20px;
    }

    .posts-grid {
        grid-template-columns: 1fr;
    }

    .filter-container {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .search-box {
        min-width: auto;
    }

    .project-filter select, .status-filter select {
        min-width: auto;
        width: 100%;
    }

    /* 手机端批量操作工具栏 */
    .batch-toolbar-content {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .batch-selection-info {
        justify-content: space-between;
    }

    .batch-actions {
        justify-content: center;
    }

    .batch-btn {
        flex: 1;
        min-width: auto;
    }

    /* 手机端岗位卡片 */
    .post-card-header {
        margin-left: 30px; /* 手机端稍微减少左边距 */
    }

    .post-card-checkbox .checkmark {
        width: 18px;
        height: 18px;
    }



    /* 手机端分页样式 */
    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }

    .pagination-controls {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .pagination-nav {
        justify-content: center;
    }

    .page-size-selector {
        justify-content: center;
    }

    #pageNumbers {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 5px;
    }

    .pagination-btn {
        min-width: 40px;
        padding: 8px 10px;
    }
}

/* 批量配置按钮样式 */
.batch-config-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white !important;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.batch-config-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    color: white !important;
}

.batch-config-btn:active {
    transform: translateY(0);
}

.batch-config-btn .btn-icon {
    font-size: 16px;
    animation: rocket 2s ease-in-out infinite;
}

.batch-config-btn .btn-text {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.batch-config-btn .btn-badge {
    background: #ff4757;
    color: white;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 10px;
    position: absolute;
    top: -5px;
    right: -5px;
    animation: pulse 2s ease-in-out infinite;
}

/* 动画效果 */
@keyframes rocket {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

/* 响应式优化 */
@media (max-width: 768px) {
    .batch-config-btn {
        padding: 10px 16px;
        font-size: 13px;
        border-radius: 20px;
    }

    .batch-config-btn .btn-icon {
        font-size: 14px;
    }
}

@media (min-width: 1200px) {
    .price-config-container {
        max-width: 1600px;
    }
}
</style>
</head>
<body class="price-config-page">
<include file="headers"/>

<div class="price-config-container">
    <!-- 功能导航 -->
    <div class="selector-card" style="margin-bottom: 15px;">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
            <h3 style="margin: 0;">💰 价格配置管理</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap; align-items: center;">
                <a href="{:U('index/batchPostPriceConfig')}" class="batch-config-btn" style="text-decoration: none; display: inline-flex; align-items: center; gap: 8px;">
                    <span class="btn-icon">🚀</span>
                    <span class="btn-text">进入批量岗位配置</span>
                </a>
            </div>
        </div>
        <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">
            💡 <strong>功能说明：</strong>当前页面用于给单个招就办配置多个岗位价格；如需给单个岗位配置多个招就办价格，请使用"批量岗位配置"功能。
        </p>
    </div>

    <!-- 第一步：选择招就办 -->
    <div class="selector-card">
        <h3>📋 选择招就办</h3>
        <select id="zsbSelect" onchange="onZsbChange()">
            <option value="">请选择招就办</option>
            <php>foreach($zsbList as $zsb) {</php>
            <option value="{$zsb.id}">{$zsb.service_name} - {$zsb.contract_name}</option>
            <php>}</php>
        </select>
    </div>

    <!-- 岗位筛选区域 -->
    <div class="filter-section" id="filterSection" style="display: none;">
        <div class="filter-container">
            <!-- 搜索框 -->
            <div class="search-box">
                <input type="text" id="keywordInput" placeholder="搜索岗位关键词" class="search-input">
                <button class="search-btn" onclick="filterPosts()">搜索</button>
            </div>

            <!-- 项目筛选 -->
            <div class="project-filter">
                <select id="projectFilter" onchange="filterPosts()">
                    <option value="">全部项目</option>
                </select>
            </div>

            <!-- 配置状态筛选 -->
            <div class="status-filter">
                <select id="statusFilter" onchange="filterPosts()">
                    <option value="">全部状态</option>
                    <option value="configured">已配置</option>
                    <option value="not-configured">未配置</option>
                </select>
            </div>
        </div>

        <!-- 筛选加载提示 -->
        <div class="filter-loading" id="filterLoading" style="display: none;">
            <span>筛选中...</span>
        </div>
    </div>

    <!-- 批量操作工具栏 - 临时隐藏 -->
    <div class="batch-toolbar" id="batchToolbar" style="display: none !important;">
        <div class="batch-toolbar-content">
            <div class="batch-selection-info">
                <label class="batch-select-all">
                    <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                    <span class="checkmark"></span>
                    全选
                </label>
                <span class="selected-count" id="selectedCount">已选择 0 个岗位</span>
            </div>
            <div class="batch-actions" >
                <button class="batch-btn batch-btn-primary" onclick="openBatchPriceModal()" id="batchSetPriceBtn" disabled>
                    批量设置价格
                </button>
                <button class="batch-btn batch-btn-secondary" onclick="clearSelection()">
                    清空选择
                </button>
            </div>
        </div>
    </div>

    <!-- 岗位卡片容器 -->
    <div class="posts-container" id="postsContainer" style="display: none;">
        <div class="posts-grid" id="postsGrid">
            <!-- 岗位卡片将通过AJAX动态加载 -->
        </div>

        <!-- 分页控件 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info">
                <span id="paginationInfo">显示第 1-10 条，共 0 条记录</span>
            </div>
            <div class="pagination-controls">
                <div class="page-size-selector">
                    <span>每页显示：</span>
                    <select id="pageSizeSelect" onchange="changePageSize()">
                        <option value="6" selected>6条</option>
                        <option value="12">12条</option>
                        <option value="18">18条</option>
                        <option value="24">24条</option>
                    </select>
                </div>
                <div class="pagination-nav">
                    <button class="pagination-btn" id="prevPageBtn" onclick="goToPage(currentPage - 1)" disabled>上一页</button>
                    <div id="pageNumbers"></div>
                    <button class="pagination-btn" id="nextPageBtn" onclick="goToPage(currentPage + 1)" disabled>下一页</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 价格配置弹窗 -->
<div class="price-modal" id="priceModal" style="display: none;">
    <div class="modal-overlay" onclick="closePriceModal()"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">价格配置</h3>
            <button class="close-btn" onclick="closePriceModal()">×</button>
        </div>
        <div class="modal-body" id="modalBody">
            <!-- 价格配置表单将动态加载 -->
        </div>
    </div>
</div>

<!-- 批量价格配置弹窗 - 临时隐藏 -->
<div class="price-modal" id="batchPriceModal" style="display: none !important;">
    <div class="modal-overlay" onclick="closeBatchPriceModal()"></div>
    <div class="modal-content batch-modal-content">
        <div class="modal-header">
            <h3 id="batchModalTitle">批量价格配置</h3>
            <button class="close-btn" onclick="closeBatchPriceModal()">×</button>
        </div>
        <div class="modal-body" id="batchModalBody">
            <!-- 批量价格配置表单将动态加载 -->
        </div>
    </div>
</div>



<script>
var currentZsbId = '';
var currentProjectId = '';
var currentPostId = '';
var dynamicPlatformRate = 0.30; // 动态平台费率，默认值，页面加载时会更新

// 获取动态平台费率
function getDynamicPlatformRate() {
    $.ajax({
        url: '/index/get_platform_rate',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                dynamicPlatformRate = response.rate;
                console.log('动态平台费率已更新:', dynamicPlatformRate);

                // 更新页面上显示费率的地方
                updatePlatformRateDisplay();
            } else {
                console.warn('获取平台费率失败，使用默认值:', dynamicPlatformRate);
            }
        },
        error: function() {
            console.warn('获取平台费率网络错误，使用默认值:', dynamicPlatformRate);
        }
    });
}

// 更新页面上的平台费率显示
function updatePlatformRateDisplay() {
    var ratePercent = (dynamicPlatformRate * 100).toFixed(1) + '%';

    // 更新所有显示平台费率的地方
    $('.platform-rate-display').text(ratePercent);

    // 如果当前有模态框打开，重新计算显示
    if ($('#priceModal').is(':visible')) {
        updateModalCalculation();
    }
}

// 初始化Swiper组件
$(document).ready(function(){
    // 获取动态平台费率
    getDynamicPlatformRate();

    // 初始化公告栏轮播
    var noticeSwiper = new Swiper('.noticeSwiper', {
        direction: 'vertical',
        loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    });

    // 清理页面底部可能的异常元素 - 更彻底的方法
    setTimeout(function() {
        // 定义需要保留的元素选择器
        var keepElements = [
            '.bg01', '.topbar', '.nav', '.notice',
            '.price-config-container', '#priceModal',
            'script', 'style', 'link', 'meta', 'title'
        ];

        // 遍历body的所有直接子元素
        $('body').children().each(function() {
            var $this = $(this);
            var shouldKeep = false;

            // 检查是否是需要保留的元素
            for (var i = 0; i < keepElements.length; i++) {
                if ($this.is(keepElements[i]) || $this.hasClass(keepElements[i].replace('.', ''))) {
                    shouldKeep = true;
                    break;
                }
            }

            // 如果不是需要保留的元素，强制隐藏
            if (!shouldKeep) {
                $this.css({
                    'display': 'none !important',
                    'visibility': 'hidden !important',
                    'opacity': '0 !important',
                    'position': 'absolute !important',
                    'left': '-9999px !important',
                    'top': '-9999px !important'
                });
            }
        });

        // 额外清理：移除可能的浮动元素
        $('body').append('<div style="clear: both; height: 0; overflow: hidden;"></div>');

        console.log('页面清理完成');
    }, 200);
});







// 选择招就办
function onZsbChange() {
    var zsbId = $('#zsbSelect').val();
    currentZsbId = zsbId;

    // 隐藏筛选、批量工具栏和卡片区域
    $('#filterSection').hide();
    $('#batchToolbar').hide();
    $('#postsContainer').hide();

    if (!zsbId) {
        return;
    }

    // 加载所有岗位及价格信息
    loadAllPostsWithPrice();
}

// 全局变量存储岗位数据
var allPosts = [];
var allProjects = [];

// 批量选择相关变量
var selectedPosts = new Set(); // 使用Set存储选中的岗位ID
var isSelectAllMode = false;

// 分页相关变量
var currentPage = 1;
var pageSize = 6;
var totalPosts = 0;
var filteredPosts = [];

// 格式化金额显示（添加千分符）
function formatMoney(amount) {
    if (amount === null || amount === undefined || amount === '') {
        return '0';
    }

    var num = parseFloat(amount);
    if (isNaN(num)) {
        return '0';
    }

    // 转换为整数（因为价格都是整数）
    num = Math.round(num);

    // 添加千分符
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 价格格式化核心函数：将价格向下取整到千位
function formatPriceToThousands(value) {
    if (!value || value === '') {
        return 0;
    }

    var num = parseInt(value);
    if (isNaN(num) || num <= 0) {
        return 0;
    }

    // 向下取整到千位（保留千位以上的数字，去除百位、十位、个位）
    return Math.floor(num / 1000) * 1000;
}

// 显示格式化提示信息
function showFormattedPriceHint(inputElement, originalValue, formattedValue) {
    var $inputGroup = $(inputElement).closest('.price-input-group');
    var $hint = $inputGroup.find('.price-format-hint');
    var $result = $inputGroup.find('.formatted-result');

    if (originalValue != formattedValue) {
        $hint.addClass('active');
        $result.text('格式化后：' + formatMoney(formattedValue) + ' 元').show();
        if (formattedValue === 0) {
            $result.addClass('warning').text('格式化后：0 元（原价格小于1000）');
        } else {
            $result.removeClass('warning');
        }
    } else {
        $hint.removeClass('active');
        $result.hide();
    }
}

// 更新价格输入框并应用格式化
function updatePriceInputWithFormatting(inputElement) {
    var originalValue = parseInt(inputElement.value) || 0;
    var formattedValue = formatPriceToThousands(originalValue);

    // 更新输入框值，使用千分符格式显示
    if (formattedValue > 0) {
        inputElement.value = formatMoney(formattedValue).replace(/,/g, ''); // 先移除千分符，因为number类型输入框不支持千分符
        // 为了显示千分符，我们需要在输入框失去焦点时显示格式化的值
        $(inputElement).data('formatted-value', formattedValue);
    } else {
        inputElement.value = '';
    }

    // 显示格式化提示
    showFormattedPriceHint(inputElement, originalValue, formattedValue);

    return formattedValue;
}

// 格式化输入框显示（添加千分符）
function formatInputDisplay(inputElement) {
    var value = parseInt(inputElement.value) || 0;
    if (value > 0) {
        // 临时改变输入框类型以支持千分符显示
        var $input = $(inputElement);
        $input.attr('type', 'text');
        $input.val(formatMoney(value));

        // 当获得焦点时，恢复为数字格式
        $input.one('focus', function() {
            $(this).attr('type', 'number');
            $(this).val(value);
        });
    }
}

// 在输入框失去焦点时格式化显示
function formatInputOnBlur(inputElement) {
    if (inputElement.value && inputElement.value !== '') {
        var value = parseInt(inputElement.value) || 0;
        if (value > 0) {
            // 显示千分符格式
            $(inputElement).attr('type', 'text').val(formatMoney(value));
        }
    }
}

// 在输入框获得焦点时恢复数字格式
function restoreInputOnFocus(inputElement) {
    var $input = $(inputElement);
    var displayValue = $input.val();

    // 如果当前显示的是千分符格式，转换回数字
    if (displayValue && displayValue.indexOf(',') !== -1) {
        var numericValue = displayValue.replace(/,/g, '');
        $input.attr('type', 'number').val(numericValue);
    }
}

// 格式化报价区间显示
function formatPriceRange(servicePrice, maxPrice, prefix) {
    prefix = prefix || '服务费报价区间：';
    var minPrice = parseInt(servicePrice || 0);
    var maxPrice = parseInt(maxPrice || 0);

    if (maxPrice > 0 && maxPrice !== minPrice) {
        return prefix + formatMoney(minPrice) + '-' + formatMoney(maxPrice) + '元';
    } else if (minPrice > 0) {
        return prefix + formatMoney(minPrice) + '元';
    } else {
        return prefix + '待定';
    }
}

// 加载所有岗位及价格信息
function loadAllPostsWithPrice() {
    $.ajax({
        url: '{:U("index/getAllPostsWithPriceAjax")}',
        type: 'GET',
        data: { zsb_id: currentZsbId },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                allPosts = response.data.posts;
                allProjects = response.data.projects;

                // 渲染项目筛选选项
                renderProjectFilter();

                // 重置筛选条件
                resetFilterConditions();

                // 应用筛选并渲染岗位卡片
                applyFiltersAndRender();

                // 显示筛选和卡片区域（隐藏批量工具栏）
                $('#filterSection').show();
                // $('#batchToolbar').show(); // 临时隐藏批量工具栏
                $('#postsContainer').show();

                // 重置批量选择状态
                resetBatchSelection();
            } else {
                alert('加载岗位数据失败：' + (response.msg || '未知错误'));
            }
        },
        error: function() {
            alert('网络错误，请重试');
        }
    });
}

// 渲染项目筛选选项
function renderProjectFilter() {
    var html = '<option value="">全部项目</option>';
    for (var i = 0; i < allProjects.length; i++) {
        var project = allProjects[i];
        html += '<option value="' + project.id + '">' + project.name + '</option>';
    }
    $('#projectFilter').html(html);
}

// 重置筛选条件
function resetFilterConditions() {
    // 清空搜索框
    $('#keywordInput').val('');

    // 重置项目筛选下拉框
    $('#projectFilter').val('');

    // 重置配置状态筛选下拉框
    $('#statusFilter').val('');
}

// 应用筛选并渲染
function applyFiltersAndRender() {
    // 应用筛选逻辑
    filterPosts();

    // 如果没有筛选结果，显示所有岗位
    if (filteredPosts.length === 0 && allPosts.length > 0) {
        filteredPosts = allPosts;
        totalPosts = allPosts.length;
    }

    // 重置到第一页
    currentPage = 1;

    // 更新分页显示
    updatePagination();

    // 显示分页控件（如果有多页）
    if (totalPosts > pageSize) {
        $('#paginationContainer').show();
    } else {
        $('#paginationContainer').hide();
    }
}



// 筛选岗位
function filterPosts() {
    // 显示加载提示
    showFilterLoading();

    // 使用setTimeout来让加载提示有时间显示
    setTimeout(function() {
        var keyword = $('#keywordInput').val().trim().toLowerCase();
        var projectId = $('#projectFilter').val();
        var statusFilter = $('#statusFilter').val();

        filteredPosts = allPosts.filter(function(post) {
            var matchKeyword = !keyword || post.job_name.toLowerCase().indexOf(keyword) !== -1;
            var matchProject = !projectId || post.project_id == projectId;
            var matchStatus = !statusFilter ||
                (statusFilter === 'configured' && post.is_configured) ||
                (statusFilter === 'not-configured' && !post.is_configured);
            return matchKeyword && matchProject && matchStatus;
        });

        totalPosts = filteredPosts.length;
        currentPage = 1; // 重置到第一页

        updatePagination();
        updateBatchToolbar(); // 筛选后更新工具栏状态

        // 隐藏加载提示
        hideFilterLoading();
    }, 100); // 短暂延迟让用户看到加载效果
}

// 显示筛选加载提示
function showFilterLoading() {
    $('#filterLoading').show();
    $('#postsContainer').css('opacity', '0.6');
}

// 隐藏筛选加载提示
function hideFilterLoading() {
    $('#filterLoading').hide();
    $('#postsContainer').css('opacity', '1');
}

// 渲染岗位卡片（渲染当前页的岗位）
function renderPostCards() {
    // 获取当前页应该显示的岗位
    var currentPagePosts = getCurrentVisiblePosts();
    var html = '';

    if (currentPagePosts.length === 0) {
        if (filteredPosts.length === 0) {
            html = '<div style="text-align: center; padding: 40px; color: #666;">暂无符合条件的岗位数据</div>';
        } else {
            html = '<div style="text-align: center; padding: 40px; color: #666;">当前页暂无数据</div>';
        }
    } else {
        for (var i = 0; i < currentPagePosts.length; i++) {
            var post = currentPagePosts[i];
            var isConfigured = post.is_configured;
            var isSelected = selectedPosts.has(post.id);
            var cardClass = isConfigured ? 'post-card configured' : 'post-card';
            if (isSelected) {
                cardClass += ' selected';
            }
            var statusBadge = isConfigured ? 'configured' : 'not-configured';
            var statusText = isConfigured ? '已配置' : '未配置';
            var btnClass = isConfigured ? 'config-btn configured' : 'config-btn';
            var btnText = isConfigured ? '修改配置' : '设置价格';

            html += '<div class="' + cardClass + '">'; // 移除点击选择功能

            // 隐藏复选框
            // html += '<div class="post-card-checkbox" onclick="event.stopPropagation()">';
            // html += '<input type="checkbox" id="post_' + post.id + '" ' + (isSelected ? 'checked' : '') + ' onchange="togglePostSelection(' + post.id + ', event)">';
            // html += '<label for="post_' + post.id + '" class="checkmark"></label>';
            // html += '</div>';

            html += '<div class="status-badge ' + statusBadge + '">' + statusText + '</div>';

            // 卡片头部
            html += '<div class="post-card-header">';
            html += '<h3 class="post-title">' + post.job_name + '</h3>';
            html += '<div class="post-meta">';
            html += '<p class="post-project">项目：' + (post.project_name || '未知') + '</p>';
            html += '<p class="post-price-range" style="margin: 2px 0; color: #007bff; font-size: 12px;">' + formatPriceRange(post.service_price, post.max_price) + '</p>';

            // 显示成本价信息
            if (post.base_cost && post.base_cost > 0) {
                html += '<p class="post-base-cost" style="font-size: 12px; color: #e67e22; margin: 2px 0;">服务站（您）成本价：' + formatMoney(post.base_cost) + ' 元</p>';
            }

            html += '</div>';
            html += '</div>';

            // 卡片主体
            html += '<div class="post-card-body">';

            if (isConfigured) {
                html += '<div class="price-info configured">';
                html += '<div class="price-row">';
                html += '<span class="price-label">您给招就办的成本价</span>';
                html += '<span class="price-value">' + formatMoney(post.cost_price) + ' 元</span>';
                html += '</div>';
                html += '<div class="price-row">';
                html += '<span class="price-label">招就办对外展示服务费</span>';
                html += '<span class="price-value">' + formatMoney(post.sale_price) + ' 元</span>';
                html += '</div>';

                // 显示平台服务费
                if (post.platform_fee && post.platform_fee > 0) {
                    html += '<div class="price-row">';
                    html += '<span class="price-label">平台服务费</span>';
                    html += '<span class="price-value" style="color: #9b59b6;">' + formatMoney(post.platform_fee) + ' 元</span>';
                    html += '</div>';
                }

                html += '<div class="price-row">';
                html += '<span class="price-label">招就办佣金提成</span>';
                var commissionColor = post.commission >= 0 ? '#28a745' : '#dc3545';
                html += '<span class="price-value" style="color: ' + commissionColor + ';">' + formatMoney(post.commission) + ' 元</span>';
                html += '</div>';

                // 显示服务站收益
                if (post.base_cost && post.base_cost > 0 && post.cost_price > 0) {
                    var stationProfit = post.cost_price - post.base_cost;
                    html += '<div class="price-row">';
                    html += '<span class="price-label">服务站（您）的收益</span>';
                    var profitColor = stationProfit >= 0 ? '#28a745' : '#dc3545';
                    html += '<span class="price-value" style="color: ' + profitColor + ';">' + formatMoney(stationProfit) + ' 元</span>';
                    html += '</div>';
                }

                html += '</div>';
            } else {
                html += '<div class="price-info not-configured">';
                html += '尚未配置价格';
                html += '</div>';
            }
            html += '</div>';

            // 卡片底部
            html += '<div class="post-card-footer">';
            html += '<button class="' + btnClass + '" onclick="openPriceModal(' + post.id + ');">' + btnText + '</button>';
            html += '</div>';

            html += '</div>';
        }
    }

    $('#postsGrid').html(html);
}

// 打开价格配置弹窗
function openPriceModal(postId) {
    currentPostId = postId;

    $.ajax({
        url: '{:U("index/getPostPriceAjax")}',
        type: 'GET',
        data: {
            zsb_id: currentZsbId,
            post_id: postId
        },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                renderPriceModal(response.data);
                $('#priceModal').show();
            } else {
                alert('加载价格配置失败：' + (response.msg || '未知错误'));
            }
        },
        error: function() {
            alert('网络错误，请重试');
        }
    });
}

// 关闭价格配置弹窗
function closePriceModal() {
    $('#priceModal').hide();
    currentPostId = '';
}

// 渲染价格配置弹窗
function renderPriceModal(data) {
    // 新的数据结构直接包含所有信息
    var isConfigured = data.is_configured;

    // 保存当前岗位的服务价格到全局变量
    currentPostServicePrice = parseInt(data.service_price) || 0;

    // 保存最高价格到全局变量
    currentPostMaxPrice = parseInt(data.max_price) || 0;

    // 保存基准成本价到全局变量
    currentBaseCost = parseFloat(data.base_cost) || 0;

    // 设置弹窗标题
    $('#modalTitle').text(data.job_name + ' - 价格配置');

    var html = '';

    // 岗位信息
    html += '<div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px;">';
    html += '<h4 style="margin: 0 0 10px 0; color: #333;">' + data.job_name + '</h4>';
    html += '<p style="margin: 5px 0; color: #666;"><strong>所属项目：</strong>' + (data.project_name || '-') + '</p>';
    html += '<p style="margin: 5px 0; color: #666;"><strong>' + formatPriceRange(data.service_price, data.max_price) + '</strong></p>';

    // 显示成本价信息
    if (currentBaseCost > 0) {
        html += '<p style="margin: 5px 0; color: #e67e22;"><strong>服务站（您）成本价：</strong>' + formatMoney(currentBaseCost) + ' 元</p>';
    }

    // 显示平台服务费信息
    if (isConfigured && data.platform_fee > 0) {
        html += '<p style="margin: 5px 0; color: #9b59b6;"><strong>平台服务费：</strong>' + formatMoney(data.platform_fee) + ' 元 <span style="font-size: 12px; color: #7f8c8d;">(超出最低报价部分的<span class="platform-rate-display">' + (dynamicPlatformRate * 100).toFixed(1) + '%</span>)</span></p>';
        html += '<p style="margin: 5px 0; font-size: 12px; color: #f39c12; line-height: 1.4;">服务费由招就办承担，超出报价区间最低部分的20%，由招就办主任承担</p>';
    }

    html += '</div>';

    // 价格配置表单
    html += '<form id="modalPriceForm">';

    // 第一行：成本价和对外价
    html += '<div class="form-row">';
    html += '<div class="form-col">';
    html += '<label>您给招就办的成本价（元）：</label>';
    html += '<div class="price-input-group">';
    var costPriceValue = isConfigured ? data.cost_price : 0;
    html += '<input type="text" id="modalCostPrice" name="cost_price" value="' + (costPriceValue > 0 ? formatMoney(costPriceValue) : '') + '" onchange="calculateModalCommission()" oninput="validatePositiveIntegerWithFormatting(this)" placeholder="请输入成本价，不得低于服务站成本价">';
    html += '<div class="price-format-hint"> <span style="font-size: 12px; color: #7f8c8d;">· 成本价不得低于服务站（您）的成本价</span><BR>· 价格将自动调整为千的整数倍（如：12345 → 12000）</div>';
    html += '<div class="formatted-result" style="display: none;"></div>';
    html += '</div>';
    html += '</div>';
    html += '<div class="form-col">';
    html += '<label>您给招就办设置的对外展示服务费（元）：</label>';
    html += '<div class="price-input-group">';
    var salePriceValue = isConfigured ? data.sale_price : 0;
    html += '<input type="text" id="modalSalePrice" name="sale_price" value="' + (salePriceValue > 0 ? formatMoney(salePriceValue) : '') + '" onchange="calculateModalCommission()" oninput="validatePositiveIntegerWithFormatting(this)" placeholder="请输入对外价，不得超过报价区间的最大报价">';
    html += '<div class="price-format-hint">· 价格将自动调整为千的整数倍（如：12345 → 12000）</div>';
    html += '<div class="formatted-result" style="display: none;"></div>';
    html += '</div>';
    html += '</div>';
    html += '</div>';

    // 价格信息显示区域
    html += '<div class="price-info-panel" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 15px; margin: 15px 0;">';

    // 佣金显示
    html += '<div class="commission-info" style="margin-bottom: 10px;">';
    var initialCommission = 0;
    if (isConfigured && data.cost_price > 0 && data.sale_price > 0) {
        // 使用新的佣金计算公式：对外价 - 成本价 - 平台服务费
        var platformFee = Math.max(0, (data.sale_price - data.service_price)) * dynamicPlatformRate;
        initialCommission = data.sale_price - data.cost_price - platformFee;
    }
    var commissionColor = initialCommission >= 0 ? '#28a745' : '#dc3545';
    html += '<div class="commission-amount" id="modalCommissionAmount" style="font-size: 18px; font-weight: bold; color: ' + commissionColor + ';">' + formatMoney(initialCommission) + '</div>';
    html += '<div class="commission-label" style="font-size: 14px; color: #6c757d;">招就办主任提成（元）</div>';
    html += '</div>';

    // 平台服务费显示
    html += '<div class="platform-fee-info" style="margin-bottom: 10px;">';
    var initialPlatformFee = isConfigured ? data.platform_fee : 0;
    html += '<div class="platform-fee-amount" id="modalPlatformFeeAmount" style="font-size: 16px; color: #9b59b6;">' + formatMoney(initialPlatformFee) + '</div>';
    html += '<div class="platform-fee-label" style="font-size: 14px; color: #6c757d;">平台服务费（元）</div>';

    html += '</div>';

    // 您的收益显示
    html += '<div class="station-profit-info" style="margin-bottom: 10px;">';
    var initialStationProfit = 0;
    if (isConfigured && data.cost_price > 0 && currentBaseCost > 0) {
        initialStationProfit = data.cost_price - currentBaseCost;
    }
    var profitColor = initialStationProfit >= 0 ? '#28a745' : '#dc3545';
    html += '<div class="station-profit-amount" id="modalStationProfitAmount" style="font-size: 16px; color: ' + profitColor + ';">' + (currentBaseCost > 0 ? formatMoney(initialStationProfit) : '--') + '</div>';
    html += '<div class="station-profit-label" style="font-size: 14px; color: #6c757d;">您的收益（元）</div>';
    html += '</div>';

    // 计算说明
    html += '<div class="calculation-note" style="font-size: 12px; color: #6c757d; border-top: 1px solid #dee2e6; padding-top: 10px;">';
    html += '<strong>计算公式：</strong><br>';
    html += '招就办主任提成 = 对外价 - 成本价 - 服务费<br>';
    html += '您的收益 = 成本价 - 服务站成本价<br>';
    html += '服务费 = max(0, (对外价 - 报价区间最低报价)) × <span class="platform-rate-display">' + (dynamicPlatformRate * 100).toFixed(1) + '%</span>';
    html += '</div>';

    html += '</div>';

    // 状态选择
    html += '<div class="form-row">';
    html += '<div class="form-col">';
    html += '<label>状态设置（是否在招就办后台显示该岗位）：</label>';
    html += '<select id="modalPriceStatus" name="status">';
    var statusValue = isConfigured ? data.status : 1;
    html += '<option value="1"' + (statusValue == 1 ? ' selected' : '') + '>显示</option>';
    html += '<option value="0"' + (statusValue == 0 ? ' selected' : '') + '>不显示</option>';
    html += '</select>';
    html += '</div>';
    html += '<div class="form-col"></div>'; // 占位
    html += '</div>';

    // 按钮组
    html += '<div class="btn-group">';
    html += '<button type="button" class="btn-secondary" onclick="resetModalForm()">重置</button>';
    html += '<button type="button" class="btn-primary" onclick="saveModalPriceConfig()">' + (isConfigured ? '更新配置' : '保存配置') + '</button>';
    html += '</div>';

    html += '</form>';

    $('#modalBody').html(html);
}

// 全局变量存储当前岗位的服务价格、最高价格和基准成本价
var currentPostServicePrice = 0;
var currentPostMaxPrice = 0;
var currentBaseCost = 0;

// 验证正整数输入（原版本，保持兼容性）
function validatePositiveInteger(input) {
    var value = input.value;

    // 移除非数字字符（除了数字）
    value = value.replace(/[^\d]/g, '');

    // 如果为空或者为0，设置为空字符串
    if (value === '' || value === '0') {
        input.value = '';
        return;
    }

    // 移除前导零
    value = value.replace(/^0+/, '');

    // 如果移除前导零后为空，设置为空
    if (value === '') {
        input.value = '';
        return;
    }

    // 设置处理后的值
    input.value = value;

    // 验证是否为正整数
    var intValue = parseInt(value);
    if (isNaN(intValue) || intValue <= 0) {
        input.setCustomValidity('请输入正整数');
        input.style.borderColor = '#dc3545';
        return;
    }

    // 如果是成本价输入框，验证是否不低于基准成本价
    if (input.id === 'modalCostPrice') {
        if (currentBaseCost > 0 && intValue < currentBaseCost) {
            input.setCustomValidity('成本价不得低于您的成本价（' + currentBaseCost + '元）');
            input.style.borderColor = '#dc3545';
            return;
        }
    }

    input.setCustomValidity('');
    input.style.borderColor = '#28a745';
}

// 验证正整数输入（带格式化功能和实时千分符）
function validatePositiveIntegerWithFormatting(input) {
    var value = input.value;

    // 移除千分符和非数字字符，只保留数字
    var numericValue = value.replace(/[^\d]/g, '');

    // 如果为空或者为0，设置为空字符串
    if (numericValue === '' || numericValue === '0') {
        input.value = '';
        // 隐藏格式化提示
        var $inputGroup = $(input).closest('.price-input-group');
        $inputGroup.find('.price-format-hint').removeClass('active');
        $inputGroup.find('.formatted-result').hide();
        return;
    }

    // 移除前导零
    numericValue = numericValue.replace(/^0+/, '');

    // 如果移除前导零后为空，设置为空
    if (numericValue === '') {
        input.value = '';
        return;
    }

    // 转换为整数
    var intValue = parseInt(numericValue);
    if (isNaN(intValue) || intValue <= 0) {
        input.setCustomValidity('请输入正整数');
        input.style.borderColor = '#dc3545';
        return;
    }

    // 实时添加千分符显示
    var formattedDisplay = formatMoney(intValue);

    // 保存光标位置
    var cursorPosition = input.selectionStart;
    var oldLength = input.value.length;

    // 更新输入框值为千分符格式
    input.value = formattedDisplay;

    // 恢复光标位置（考虑千分符的影响）
    var newLength = input.value.length;
    var lengthDiff = newLength - oldLength;
    var newCursorPosition = cursorPosition + lengthDiff;

    // 确保光标位置在有效范围内
    if (newCursorPosition < 0) newCursorPosition = 0;
    if (newCursorPosition > newLength) newCursorPosition = newLength;

    // 设置光标位置
    setTimeout(function() {
        input.setSelectionRange(newCursorPosition, newCursorPosition);
    }, 0);

    // 显示格式化预览
    var formattedValue = formatPriceToThousands(intValue);
    showFormattedPriceHint(input, intValue, formattedValue);

    // 如果是成本价输入框，验证是否不低于基准成本价（使用格式化后的值）
    if (input.id === 'modalCostPrice') {
        if (currentBaseCost > 0 && formattedValue < currentBaseCost) {
            input.setCustomValidity('格式化后的成本价不得低于您的成本价（' + currentBaseCost + '元）');
            input.style.borderColor = '#dc3545';
            return;
        }
    }

    // 如果是对外价输入框，验证是否不超过最高报价
    if (input.id === 'modalSalePrice') {
        if (currentPostMaxPrice > 0 && formattedValue > currentPostMaxPrice) {
            input.setCustomValidity('对外价不能超过最高报价（' + formatMoney(currentPostMaxPrice) + '元）');
            input.style.borderColor = '#dc3545';
            return;
        }
    }

    input.setCustomValidity('');
    input.style.borderColor = '#28a745';
}

// 应用价格格式化（失去焦点时触发）
function applyPriceFormatting(input) {
    if (input.value && input.value !== '') {
        var formattedValue = updatePriceInputWithFormatting(input);

        // 显示千分符格式
        formatInputOnBlur(input);

        // 重新触发验证
        if (input.id === 'modalCostPrice' || input.id === 'modalSalePrice') {
            validatePositiveIntegerWithFormatting(input);
            // 重新计算佣金
            calculateModalCommission();
        }
    }
}

// 计算弹窗中的佣金和平台服务费
function calculateModalCommission() {
    var costPrice = parseInt(getNumericValue(document.getElementById('modalCostPrice'))) || 0;
    var salePrice = parseInt(getNumericValue(document.getElementById('modalSalePrice'))) || 0;

    // 只有当两个价格都大于0时才计算佣金
    if (costPrice <= 0 || salePrice <= 0) {
        $('#modalCommissionAmount').text('请输入完整价格信息');
        $('#modalPlatformFeeAmount').text('--');
        return;
    }

    // 计算平台服务费：max(0, (对外价 - 最低报价)) × 动态平台费率
    var platformFee = Math.max(0, (salePrice - currentPostServicePrice)) * dynamicPlatformRate;

    // 计算招就办主任提成：对外价 - 成本价 - 平台服务费
    var commission = salePrice - costPrice - platformFee;

    // 计算服务站收益：成本价 - 基准成本价
    var stationProfit = 0;
    if (currentBaseCost > 0 && costPrice > 0) {
        stationProfit = costPrice - currentBaseCost;
    }

    // 更新平台服务费显示
    $('#modalPlatformFeeAmount').text(formatMoney(platformFee));

    // 更新佣金显示
    if (commission < 0) {
        $('#modalCommissionAmount').html('<span style="color: #dc3545;">' + formatMoney(commission) + '</span>');
        $('#modalCommissionAmount').parent().find('.commission-label').html('<span style="color: #dc3545;">招就办主任提成（元）- 提成为负数，请调整价格</span>');
    } else {
        $('#modalCommissionAmount').html('<span style="color: #28a745;">' + formatMoney(commission) + '</span>');
        $('#modalCommissionAmount').parent().find('.commission-label').html('招就办主任提成（元）');
    }

    // 更新服务站收益显示
    if (currentBaseCost > 0 && costPrice > 0) {
        var profitColor = stationProfit >= 0 ? '#28a745' : '#dc3545';
        $('#modalStationProfitAmount').html('<span style="color: ' + profitColor + ';">' + formatMoney(stationProfit) + '</span>');
    } else {
        $('#modalStationProfitAmount').text('--');
    }
}

// 重置弹窗表单
function resetModalForm() {
    $('#modalCostPrice').val(0);
    $('#modalSalePrice').val(0);
    $('#modalPriceStatus').val(1);
    calculateModalCommission();
}

// 获取输入框的纯数字值（移除千分符）
function getNumericValue(inputElement) {
    var value = inputElement.value || '';
    return value.replace(/[^\d]/g, '');
}

// 保存弹窗中的价格配置
function saveModalPriceConfig() {
    // 获取纯数字值（移除千分符）
    var costPriceValue = getNumericValue(document.getElementById('modalCostPrice'));
    var salePriceValue = getNumericValue(document.getElementById('modalSalePrice'));

    // 应用格式化到纯数字值
    if (costPriceValue) {
        costPriceValue = formatPriceToThousands(parseInt(costPriceValue));
    }
    if (salePriceValue) {
        salePriceValue = formatPriceToThousands(parseInt(salePriceValue));
    }

    var formData = {
        zsb_id: currentZsbId,
        post_id: currentPostId,
        cost_price: costPriceValue,
        sale_price: salePriceValue,
        status: $('#modalPriceStatus').val()
    };

    // 验证
    if (!formData.cost_price || !formData.sale_price) {
        alert('请填写招就办成本价和对外价');
        return;
    }

    // 验证是否为正整数
    var costPrice = parseInt(formData.cost_price);
    var salePrice = parseInt(formData.sale_price);

    if (isNaN(costPrice) || costPrice <= 0) {
        alert('成本价必须为正整数');
        return;
    }

    if (isNaN(salePrice) || salePrice <= 0) {
        alert('对外价必须为正整数');
        return;
    }

    // 验证成本价不得低于基准成本价
    if (currentBaseCost > 0 && costPrice < currentBaseCost) {
        alert('成本价不得低于您的成本价（' + currentBaseCost + '元）');
        return;
    }

    if (salePrice < costPrice) {
        alert('对外价不能低于您的成本价');
        return;
    }

    // 验证对外价不能超过最高报价
    if (currentPostMaxPrice > 0 && salePrice > currentPostMaxPrice) {
        alert('对外价不能超过最高报价（' + formatMoney(currentPostMaxPrice) + '元）');
        return;
    }

    // 验证招就办主任提成不能为负数
    var platformFee = Math.max(0, (salePrice - currentPostServicePrice)) * dynamicPlatformRate;
    var commission = salePrice - costPrice - platformFee;
    if (commission < 0) {
        alert('招就办主任提成不能为负数。当前平台服务费为' + formatMoney(platformFee) + '元，请调整价格配置');
        return;
    }

    // 显示加载状态
    var $btn = $('#modalBody .btn-primary');
    var originalText = $btn.text();
    $btn.text('保存中...').prop('disabled', true);

    $.ajax({
        url: '{:U("index/setPriceAjax")}',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                alert('保存成功');
                // 关闭弹窗
                closePriceModal();
                // 重新加载岗位数据
                loadAllPostsWithPrice();
            } else {
                alert(response.msg || '保存失败');
            }
        },
        error: function() {
            alert('网络错误，请重试');
        },
        complete: function() {
            // 恢复按钮状态
            $btn.text(originalText).prop('disabled', false);
        }
    });
}

// 批量选择相关函数

// 重置批量选择状态
function resetBatchSelection() {
    selectedPosts.clear();
    isSelectAllMode = false;
    updateBatchToolbar();
}

// 切换全选状态
function toggleSelectAll() {
    var checkbox = $('#selectAllCheckbox');
    var isChecked = checkbox.prop('checked');
    var visiblePosts = getCurrentVisiblePosts();

    if (isChecked) {
        // 全选：添加当前显示的所有岗位
        visiblePosts.forEach(function(post) {
            selectedPosts.add(post.id);
            // 同步每个岗位的复选框状态
            var postCheckbox = document.getElementById('post_' + post.id);
            if (postCheckbox) {
                postCheckbox.checked = true;
            }
            // 更新卡片样式
            var cardElement = postCheckbox ? postCheckbox.closest('.post-card') : null;
            if (cardElement) {
                cardElement.classList.add('selected');
            }
        });
        isSelectAllMode = true;
    } else {
        // 取消全选
        visiblePosts.forEach(function(post) {
            selectedPosts.delete(post.id);
            // 同步每个岗位的复选框状态
            var postCheckbox = document.getElementById('post_' + post.id);
            if (postCheckbox) {
                postCheckbox.checked = false;
            }
            // 更新卡片样式
            var cardElement = postCheckbox ? postCheckbox.closest('.post-card') : null;
            if (cardElement) {
                cardElement.classList.remove('selected');
            }
        });
        isSelectAllMode = false;
    }

    updateBatchToolbar();
}

// 切换单个岗位选择状态
function togglePostSelection(postId, event) {
    if (event) {
        event.stopPropagation();
    }

    // 检查是否是复选框触发的事件
    var isCheckboxEvent = event && event.target && event.target.type === 'checkbox';

    if (isCheckboxEvent) {
        // 复选框事件：根据复选框状态设置选择状态
        if (event.target.checked) {
            selectedPosts.add(postId);
        } else {
            selectedPosts.delete(postId);
        }
    } else {
        // 卡片点击事件：切换选择状态
        if (selectedPosts.has(postId)) {
            selectedPosts.delete(postId);
        } else {
            selectedPosts.add(postId);
        }
    }

    // 总是同步复选框状态
    var checkbox = document.getElementById('post_' + postId);
    if (checkbox) {
        checkbox.checked = selectedPosts.has(postId);
    }

    // 更新卡片的选中样式
    var cardElement = checkbox ? checkbox.closest('.post-card') : null;
    if (cardElement) {
        if (selectedPosts.has(postId)) {
            cardElement.classList.add('selected');
        } else {
            cardElement.classList.remove('selected');
        }
    }

    updateBatchToolbar();
}

// 获取当前显示的岗位列表
function getCurrentVisiblePosts() {
    var keyword = $('#keywordInput').val().trim().toLowerCase();
    var projectId = $('#projectFilter').val();

    return allPosts.filter(function(post) {
        var matchKeyword = !keyword || post.job_name.toLowerCase().indexOf(keyword) !== -1;
        var matchProject = !projectId || post.project_id == projectId;
        return matchKeyword && matchProject;
    });
}

// 更新批量操作工具栏状态
function updateBatchToolbar() {
    var selectedCount = selectedPosts.size;
    var visiblePosts = getCurrentVisiblePosts();
    var visibleCount = visiblePosts.length;

    // 更新选择计数
    $('#selectedCount').text('已选择 ' + selectedCount + ' 个岗位');

    // 更新全选复选框状态
    var selectAllCheckbox = $('#selectAllCheckbox');
    if (selectedCount === 0) {
        selectAllCheckbox.prop('checked', false);
        selectAllCheckbox.prop('indeterminate', false);
    } else if (selectedCount === visibleCount && visibleCount > 0) {
        selectAllCheckbox.prop('checked', true);
        selectAllCheckbox.prop('indeterminate', false);
    } else {
        selectAllCheckbox.prop('checked', false);
        selectAllCheckbox.prop('indeterminate', true);
    }

    // 更新批量操作按钮状态
    var batchSetPriceBtn = $('#batchSetPriceBtn');
    if (selectedCount > 0) {
        batchSetPriceBtn.prop('disabled', false);
    } else {
        batchSetPriceBtn.prop('disabled', true);
    }
}

// 清空选择
function clearSelection() {
    // 清除所有复选框状态和卡片样式
    selectedPosts.forEach(function(postId) {
        var postCheckbox = document.getElementById('post_' + postId);
        if (postCheckbox) {
            postCheckbox.checked = false;
            var cardElement = postCheckbox.closest('.post-card');
            if (cardElement) {
                cardElement.classList.remove('selected');
            }
        }
    });

    resetBatchSelection();
}

// 获取选中的岗位信息
function getSelectedPostsInfo() {
    var selectedPostsInfo = [];
    selectedPosts.forEach(function(postId) {
        var post = allPosts.find(function(p) { return p.id == postId; });
        if (post) {
            selectedPostsInfo.push(post);
        }
    });
    return selectedPostsInfo;
}

// 批量价格配置相关函数

// 打开批量价格配置弹窗
function openBatchPriceModal() {
    var selectedPostsInfo = getSelectedPostsInfo();
    if (selectedPostsInfo.length === 0) {
        alert('请先选择要配置的岗位');
        return;
    }

    renderBatchPriceModal(selectedPostsInfo);
    $('#batchPriceModal').show();
}

// 关闭批量价格配置弹窗
function closeBatchPriceModal() {
    $('#batchPriceModal').hide();
}

// 渲染批量价格配置弹窗
function renderBatchPriceModal(selectedPostsInfo) {
    $('#batchModalTitle').text('批量价格配置 (' + selectedPostsInfo.length + '个岗位)');

    var html = '';

    // 选中岗位预览
    html += '<div class="batch-posts-preview">';
    html += '<h4>选中的岗位 (' + selectedPostsInfo.length + '个)：</h4>';
    for (var i = 0; i < selectedPostsInfo.length; i++) {
        var post = selectedPostsInfo[i];
        html += '<div class="batch-post-item">';
        html += '<span class="batch-post-name">' + post.job_name + '</span>';
        html += '<span class="batch-post-project">' + (post.project_name || '未知项目') + '</span>';
        html += '<span class="batch-post-service-price">' + formatPriceRange(post.service_price, post.max_price, '报价: ') + '</span>';
        html += '</div>';
    }
    html += '</div>';

    // 批量配置表单
    html += '<div class="batch-price-form">';
    html += '<form id="batchPriceForm">';

    // 配置模式选择
    html += '<div class="batch-price-mode">';
    html += '<h4 style="margin-bottom: 15px;">配置模式：</h4>';
    html += '<label><input type="radio" name="batch_mode" value="uniform" checked onchange="toggleBatchMode()"> 统一设置价格</label>';
    html += '<label><input type="radio" name="batch_mode" value="ratio" onchange="toggleBatchMode()"> 按比例调整现有价格</label>';
    html += '</div>';

    // 统一设置模式
    html += '<div class="batch-price-inputs uniform-mode active" id="uniformMode">';
    html += '<div class="form-row">';
    html += '<div class="form-col">';
    html += '<label>统一成本价（元）：</label>';
    html += '<div class="price-input-group">';
    html += '<input type="text" id="batchCostPrice" name="cost_price" placeholder="请输入成本价" oninput="validateBatchPriceWithFormatting(this)">';
    html += '<div class="price-format-hint">价格将自动调整为千的整数倍（如：12345 → 12000）</div>';
    html += '<div class="formatted-result" style="display: none;"></div>';
    html += '</div>';
    html += '</div>';
    html += '<div class="form-col">';
    html += '<label>统一对外价（元）：</label>';
    html += '<div class="price-input-group">';
    html += '<input type="text" id="batchSalePrice" name="sale_price" placeholder="请输入对外价" oninput="validateBatchPriceWithFormatting(this)">';
    html += '<div class="price-format-hint">价格将自动调整为千的整数倍（如：12345 → 12000）</div>';
    html += '<div class="formatted-result" style="display: none;"></div>';
    html += '</div>';
    html += '</div>';
    html += '</div>';
    html += '</div>';

    // 比例调整模式
    html += '<div class="batch-price-inputs ratio-mode" id="ratioMode">';
    html += '<div class="form-row">';
    html += '<div class="form-col">';
    html += '<label>成本价调整比例（%）：</label>';
    html += '<input type="number" id="costPriceRatio" name="cost_price_ratio" min="-50" max="200" step="1" placeholder="如：10表示增加10%" oninput="validateBatchRatio(this)">';
    html += '</div>';
    html += '<div class="form-col">';
    html += '<label>对外价调整比例（%）：</label>';
    html += '<input type="number" id="salePriceRatio" name="sale_price_ratio" min="-50" max="200" step="1" placeholder="如：10表示增加10%" oninput="validateBatchRatio(this)">';
    html += '</div>';
    html += '</div>';
    html += '<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin-top: 10px; font-size: 14px; color: #856404;">';
    html += '<strong>说明：</strong>比例调整仅对已配置价格的岗位生效，未配置的岗位将被跳过。';
    html += '</div>';
    html += '</div>';

    // 状态设置
    html += '<div class="form-row" style="margin-top: 20px;">';
    html += '<div class="form-col">';
    html += '<label>批量设置状态：</label>';
    html += '<select id="batchStatus" name="status">';
    html += '<option value="1">显示</option>';
    html += '<option value="0">不显示</option>';
    html += '</select>';
    html += '</div>';
    html += '<div class="form-col"></div>';
    html += '</div>';

    // 按钮组
    html += '<div class="btn-group" style="margin-top: 25px;">';
    html += '<button type="button" class="btn-secondary" onclick="closeBatchPriceModal()">取消</button>';
    html += '<button type="button" class="btn-primary" onclick="saveBatchPriceConfig()">批量保存</button>';
    html += '</div>';

    html += '</form>';
    html += '</div>';

    $('#batchModalBody').html(html);
}

// 切换批量配置模式
function toggleBatchMode() {
    var mode = $('input[name="batch_mode"]:checked').val();
    $('.batch-price-inputs').removeClass('active');
    if (mode === 'uniform') {
        $('#uniformMode').addClass('active');
    } else if (mode === 'ratio') {
        $('#ratioMode').addClass('active');
    }
}

// 验证批量价格输入（原版本，保持兼容性）
function validateBatchPrice(input) {
    var value = input.value;

    // 移除非数字字符
    value = value.replace(/[^\d]/g, '');

    if (value === '' || value === '0') {
        input.value = '';
        return;
    }

    // 移除前导零
    value = value.replace(/^0+/, '');
    if (value === '') {
        input.value = '';
        return;
    }

    input.value = value;

    var intValue = parseInt(value);
    if (isNaN(intValue) || intValue <= 0) {
        input.setCustomValidity('请输入正整数');
        input.style.borderColor = '#dc3545';
        return;
    }

    input.setCustomValidity('');
    input.style.borderColor = '#28a745';
}

// 验证批量价格输入（带格式化功能和实时千分符）
function validateBatchPriceWithFormatting(input) {
    var value = input.value;

    // 移除千分符和非数字字符，只保留数字
    var numericValue = value.replace(/[^\d]/g, '');

    if (numericValue === '' || numericValue === '0') {
        input.value = '';
        // 隐藏格式化提示
        var $inputGroup = $(input).closest('.price-input-group');
        $inputGroup.find('.price-format-hint').removeClass('active');
        $inputGroup.find('.formatted-result').hide();
        return;
    }

    // 移除前导零
    numericValue = numericValue.replace(/^0+/, '');
    if (numericValue === '') {
        input.value = '';
        return;
    }

    // 转换为整数
    var intValue = parseInt(numericValue);
    if (isNaN(intValue) || intValue <= 0) {
        input.setCustomValidity('请输入正整数');
        input.style.borderColor = '#dc3545';
        return;
    }

    // 实时添加千分符显示
    var formattedDisplay = formatMoney(intValue);

    // 保存光标位置
    var cursorPosition = input.selectionStart;
    var oldLength = input.value.length;

    // 更新输入框值为千分符格式
    input.value = formattedDisplay;

    // 恢复光标位置（考虑千分符的影响）
    var newLength = input.value.length;
    var lengthDiff = newLength - oldLength;
    var newCursorPosition = cursorPosition + lengthDiff;

    // 确保光标位置在有效范围内
    if (newCursorPosition < 0) newCursorPosition = 0;
    if (newCursorPosition > newLength) newCursorPosition = newLength;

    // 设置光标位置
    setTimeout(function() {
        input.setSelectionRange(newCursorPosition, newCursorPosition);
    }, 0);

    // 显示格式化预览
    var formattedValue = formatPriceToThousands(intValue);
    showFormattedPriceHint(input, intValue, formattedValue);

    input.setCustomValidity('');
    input.style.borderColor = '#28a745';
}

// 应用批量价格格式化（失去焦点时触发）
function applyBatchPriceFormatting(input) {
    if (input.value && input.value !== '') {
        updatePriceInputWithFormatting(input);
        // 显示千分符格式
        formatInputOnBlur(input);
        // 重新触发验证
        validateBatchPriceWithFormatting(input);
    }
}

// 验证批量比例输入
function validateBatchRatio(input) {
    var value = input.value;

    // 允许负号和数字
    value = value.replace(/[^\d-]/g, '');

    // 确保负号只在开头
    if (value.indexOf('-') > 0) {
        value = value.replace(/-/g, '');
    }

    input.value = value;

    if (value === '' || value === '-') {
        input.setCustomValidity('');
        input.style.borderColor = '#ddd';
        return;
    }

    var intValue = parseInt(value);
    if (isNaN(intValue) || intValue < -50 || intValue > 200) {
        input.setCustomValidity('比例范围：-50% 到 200%');
        input.style.borderColor = '#dc3545';
        return;
    }

    input.setCustomValidity('');
    input.style.borderColor = '#28a745';
}

// 保存批量价格配置
function saveBatchPriceConfig() {
    var mode = $('input[name="batch_mode"]:checked').val();
    var selectedPostsInfo = getSelectedPostsInfo();
    var priceList = [];

    if (mode === 'uniform') {
        // 统一设置模式 - 获取纯数字值并应用格式化
        var costPriceInput = document.getElementById('batchCostPrice');
        var salePriceInput = document.getElementById('batchSalePrice');

        // 获取纯数字值（移除千分符）
        var costPrice = getNumericValue(costPriceInput);
        var salePrice = getNumericValue(salePriceInput);

        // 应用格式化到纯数字值
        if (costPrice) {
            costPrice = formatPriceToThousands(parseInt(costPrice));
        }
        if (salePrice) {
            salePrice = formatPriceToThousands(parseInt(salePrice));
        }
        var status = $('#batchStatus').val();

        if (!costPrice || !salePrice) {
            alert('请填写成本价和对外价');
            return;
        }

        var costPriceInt = parseInt(costPrice);
        var salePriceInt = parseInt(salePrice);

        if (isNaN(costPriceInt) || costPriceInt <= 0) {
            alert('成本价必须为正整数');
            return;
        }

        if (isNaN(salePriceInt) || salePriceInt <= 0) {
            alert('对外价必须为正整数');
            return;
        }

        if (salePriceInt < costPriceInt) {
            alert('对外价不能低于成本价');
            return;
        }



        // 生成价格列表
        for (var i = 0; i < selectedPostsInfo.length; i++) {
            var post = selectedPostsInfo[i];
            priceList.push({
                post_id: post.id,
                cost_price: costPriceInt,
                sale_price: salePriceInt,
                status: parseInt(status)
            });
        }

    } else if (mode === 'ratio') {
        // 比例调整模式
        var costPriceRatio = $('#costPriceRatio').val();
        var salePriceRatio = $('#salePriceRatio').val();
        var status = $('#batchStatus').val();

        if (!costPriceRatio && !salePriceRatio) {
            alert('请至少填写一个调整比例');
            return;
        }

        var costRatio = costPriceRatio ? parseInt(costPriceRatio) : 0;
        var saleRatio = salePriceRatio ? parseInt(salePriceRatio) : 0;

        // 生成价格列表（仅对已配置的岗位）
        for (var i = 0; i < selectedPostsInfo.length; i++) {
            var post = selectedPostsInfo[i];
            if (post.is_configured) {
                var newCostPrice = costPriceRatio ? Math.round(post.cost_price * (1 + costRatio / 100)) : post.cost_price;
                var newSalePrice = salePriceRatio ? Math.round(post.sale_price * (1 + saleRatio / 100)) : post.sale_price;

                // 验证调整后的价格
                if (newCostPrice <= 0 || newSalePrice <= 0) {
                    alert('调整后的价格不能为负数或零。岗位：' + post.job_name);
                    return;
                }

                if (newSalePrice < newCostPrice) {
                    alert('调整后的对外价不能低于成本价。岗位：' + post.job_name);
                    return;
                }



                priceList.push({
                    post_id: post.id,
                    cost_price: newCostPrice,
                    sale_price: newSalePrice,
                    status: parseInt(status)
                });
            }
        }

        if (priceList.length === 0) {
            alert('所选岗位中没有已配置价格的岗位，无法进行比例调整');
            return;
        }
    }

    // 显示加载状态
    var $btn = $('#batchModalBody .btn-primary');
    var originalText = $btn.text();
    $btn.text('保存中...').prop('disabled', true);

    // 提交批量设置
    $.ajax({
        url: '{:U("index/batchSetPriceAjax")}',
        type: 'POST',
        data: {
            zsb_id: currentZsbId,
            price_list: priceList
        },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                alert('批量设置成功');
                closeBatchPriceModal();
                // 重新加载岗位数据
                loadAllPostsWithPrice();
            } else {
                alert(response.msg || '批量设置失败');
            }
        },
        error: function() {
            alert('网络错误，请重试');
        },
        complete: function() {
            $btn.text(originalText).prop('disabled', false);
        }
    });
}

// 分页相关函数

// 更新分页显示
function updatePagination() {
    var totalPages = Math.ceil(totalPosts / pageSize);
    var startIndex = (currentPage - 1) * pageSize;
    var endIndex = Math.min(startIndex + pageSize, totalPosts);

    // 渲染当前页的岗位卡片
    renderPostCards();

    // 更新分页信息
    $('#paginationInfo').text('显示第 ' + (startIndex + 1) + '-' + endIndex + ' 条，共 ' + totalPosts + ' 条记录');

    // 更新分页按钮状态
    $('#prevPageBtn').prop('disabled', currentPage <= 1);
    $('#nextPageBtn').prop('disabled', currentPage >= totalPages);

    // 渲染页码按钮
    renderPageNumbers(totalPages);

    // 显示或隐藏分页控件
    if (totalPages > 1) {
        $('#paginationContainer').show();
    } else {
        $('#paginationContainer').hide();
    }
}

// 渲染页码按钮
function renderPageNumbers(totalPages) {
    var html = '';
    var maxVisiblePages = 5;
    var startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    var endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    // 调整起始页，确保显示足够的页码
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // 第一页
    if (startPage > 1) {
        html += '<button class="pagination-btn" onclick="goToPage(1)">1</button>';
        if (startPage > 2) {
            html += '<span style="padding: 0 8px; color: #666;">...</span>';
        }
    }

    // 页码按钮
    for (var i = startPage; i <= endPage; i++) {
        var activeClass = i === currentPage ? ' active' : '';
        html += '<button class="pagination-btn' + activeClass + '" onclick="goToPage(' + i + ')">' + i + '</button>';
    }

    // 最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += '<span style="padding: 0 8px; color: #666;">...</span>';
        }
        html += '<button class="pagination-btn" onclick="goToPage(' + totalPages + ')">' + totalPages + '</button>';
    }

    $('#pageNumbers').html(html);
}

// 跳转到指定页
function goToPage(page) {
    var totalPages = Math.ceil(totalPosts / pageSize);
    if (page < 1 || page > totalPages) {
        return;
    }

    currentPage = page;
    updatePagination();
    updateBatchToolbar();
}

// 改变每页显示数量
function changePageSize() {
    pageSize = parseInt($('#pageSizeSelect').val());
    currentPage = 1; // 重置到第一页
    updatePagination();
    updateBatchToolbar();
}

// 修改getCurrentVisiblePosts函数，返回当前页的岗位
function getCurrentVisiblePosts() {
    var startIndex = (currentPage - 1) * pageSize;
    var endIndex = Math.min(startIndex + pageSize, filteredPosts.length);
    return filteredPosts.slice(startIndex, endIndex);
}

// 获取所有符合筛选条件的岗位（用于批量选择）
function getAllFilteredPosts() {
    var keyword = $('#keywordInput').val().trim().toLowerCase();
    var projectId = $('#projectFilter').val();
    var statusFilter = $('#statusFilter').val();

    return allPosts.filter(function(post) {
        var matchKeyword = !keyword || post.job_name.toLowerCase().indexOf(keyword) !== -1;
        var matchProject = !projectId || post.project_id == projectId;
        var matchStatus = !statusFilter ||
            (statusFilter === 'configured' && post.is_configured) ||
            (statusFilter === 'not-configured' && !post.is_configured);
        return matchKeyword && matchProject && matchStatus;
    });
}

// 支持回车键搜索
$(document).ready(function() {
    $('#keywordInput').on('keypress', function(e) {
        if (e.which === 13) { // 回车键
            filterPosts();
        }
    });
});
</script>

</body>
</html>
