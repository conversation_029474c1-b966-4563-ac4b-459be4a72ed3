<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化公告管理页面样式 */
                .pagead-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .pagead-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .pagead-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .pagead-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
                }

                .pagead-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .pagead-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .pagead-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .pagead-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .pagead-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .pagead-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .pagead-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .pagead-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .pagead-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .pagead-index-add-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.3);
                    text-decoration: none;
                }

                .pagead-index-add-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(139, 92, 246, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化搜索面板 */
                .pagead-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    max-height: 0;
                    opacity: 0;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .pagead-index-search-panel.show {
                    max-height: 500px;
                    opacity: 1;
                }

                .pagead-index-search-header {
                    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .pagead-index-search-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .pagead-index-search-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .pagead-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .pagead-index-search-body {
                    padding: 2rem;
                }

                .pagead-index-search-form {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 1.5rem;
                    align-items: end;
                }

                .pagead-index-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .pagead-index-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                }

                .pagead-index-form-select,
                .pagead-index-form-input {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-family: inherit;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: #f8fafc;
                }

                .pagead-index-form-select:focus,
                .pagead-index-form-input:focus {
                    outline: none;
                    border-color: #8b5cf6;
                    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
                    background: white;
                }

                .pagead-index-search-actions {
                    display: flex;
                    gap: 1rem;
                    margin-top: 1rem;
                }

                .pagead-index-search-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .pagead-index-search-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .pagead-index-search-btn.btn-primary {
                    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
                    color: white;
                }

                .pagead-index-search-btn.btn-primary:hover {
                    color: white;
                }

                .pagead-index-search-btn.btn-secondary {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                }

                .pagead-index-search-btn.btn-secondary:hover {
                    color: white;
                }

                /* 现代化导航标签 */
                .pagead-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .pagead-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .pagead-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .pagead-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .pagead-index-nav-link:hover {
                    color: #8b5cf6;
                    background: rgba(139, 92, 246, 0.05);
                    text-decoration: none;
                }

                .pagead-index-nav-link.active {
                    color: #8b5cf6 !important;
                    background: white !important;
                    border-bottom-color: #8b5cf6 !important;
                    box-shadow: 0 -2px 4px rgba(139, 92, 246, 0.1) !important;
                    font-weight: 700 !important;
                }

                .pagead-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #8b5cf6 0%, #7c3aed 100%);
                    z-index: 1;
                }

                .pagead-index-nav-link.active .pagead-index-nav-icon {
                    color: #8b5cf6 !important;
                    transform: scale(1.1);
                }

                .pagead-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化列表布局 */
                .pagead-index-list-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    margin-bottom: 2rem;
                }

                .pagead-list-header {
                    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .pagead-list-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .pagead-list-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .pagead-list-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .pagead-list-body {
                    padding: 0;
                }

                .pagead-list-item {
                    display: flex;
                    align-items: center;
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #f1f5f9;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .pagead-list-item:last-child {
                    border-bottom: none;
                }

                .pagead-list-item:hover {
                    background: #f8fafc;
                    transform: translateX(4px);
                }

                .pagead-list-item::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 4px;
                    background: transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .pagead-list-item:hover::before {
                    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
                }

                .pagead-item-id {
                    width: 80px;
                    flex-shrink: 0;
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: #8b5cf6;
                    text-align: center;
                    background: #f3f4f6;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    border: 2px solid #e5e7eb;
                }

                .pagead-item-content {
                    flex: 1;
                    margin-left: 2rem;
                    min-width: 0;
                }

                .pagead-item-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1f2937;
                    margin: 0 0 0.5rem 0;
                    line-height: 1.4;
                    word-break: break-word;
                }

                .pagead-item-meta {
                    display: flex;
                    align-items: center;
                    gap: 1.5rem;
                    flex-wrap: wrap;
                }

                .pagead-item-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .pagead-status-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    color: white;
                }

                .pagead-status-badge.label-success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                }

                .pagead-status-badge.label-warning {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                }

                .pagead-status-badge.label-danger {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                }

                .pagead-status-badge.label-default {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                }

                .pagead-item-time {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.25rem;
                    color: #6b7280;
                }

                .pagead-item-actions {
                    margin-left: 2rem;
                    flex-shrink: 0;
                    display: flex;
                    gap: 0.75rem;
                    flex-wrap: wrap;
                }

                .pagead-item-actions .btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    white-space: nowrap;
                }

                .pagead-item-actions .btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .pagead-index-container {
                        padding: 1rem;
                    }

                    .pagead-list-item {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 1rem;
                        padding: 1.5rem 1rem;
                    }

                    .pagead-item-content {
                        margin-left: 0;
                        width: 100%;
                    }

                    .pagead-item-meta {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 0.75rem;
                    }

                    .pagead-item-actions {
                        margin-left: 0;
                        width: 100%;
                        justify-content: flex-start;
                    }

                    .pagead-item-actions .btn {
                        flex: 1;
                        justify-content: center;
                        min-width: 0;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .pagead-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .pagead-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .pagead-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .pagead-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="pagead-index-wrapper">
                <div class="pagead-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="pagead-index-header pagead-index-fade-in">
                        <div class="pagead-index-header-content">
                            <div class="pagead-index-title">
                                <div class="pagead-index-title-icon">
                                    <i class="fa fa-file-text"></i>
                                </div>
                                <div class="pagead-index-title-text">
                                    <h1 class="pagead-index-title-main">公告管理</h1>
                                    <p class="pagead-index-title-sub">Page Advertisement Management</p>
                                </div>
                            </div>
                            <div class="pagead-index-actions">
                                <button type="button" class="pagead-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>
                                <a href="{:U('Pagead/edit')}" class="pagead-index-add-btn">
                                    <i class="fa fa-plus"></i>
                                    <span>添加公告</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化搜索面板 -->
                    <div class="pagead-index-search-panel pagead-index-fade-in-delay-1" id="searchPanel">
                        <div class="pagead-index-search-header">
                            <div class="pagead-index-search-icon">
                                <i class="fa fa-search"></i>
                            </div>
                            <h3 class="pagead-index-search-title">搜索筛选</h3>
                        </div>
                        <div class="pagead-index-search-body">
                            <form method="get" class="pagead-index-search-form" role="form">
                                <div class="pagead-index-form-group">
                                    <label class="pagead-index-form-label">搜索字段</label>
                                    <select class="pagead-index-form-select" name="kw">
                                        <php>foreach($c_kw as $key=>$value){</php>
                                        <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                        <php>}</php>
                                    </select>
                                </div>
                                <div class="pagead-index-form-group">
                                    <label class="pagead-index-form-label">搜索内容</label>
                                    <input class="pagead-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
                                </div>
                                <div class="pagead-index-form-group">
                                    <label class="pagead-index-form-label">公告状态</label>
                                    <select name="status" class="pagead-index-form-select">
                                        <option value="">全部状态</option>
                                        <php> foreach($statusList as  $k => $v) { </php>
                                        <option value="{$k}" <php>if(is_numeric($_get['status']) && $_get['status']==$k) echo 'selected';</php>>{$v.text}</option>
                                        <php> } </php>
                                    </select>
                                </div>
                                <div class="pagead-index-search-actions">
                                    <button type="submit" class="pagead-index-search-btn btn-primary">
                                        <i class="fa fa-search"></i>
                                        <span>搜索</span>
                                    </button>
                                    <a href="{:U('Pagead/index')}" class="pagead-index-search-btn btn-secondary">
                                        <i class="fa fa-refresh"></i>
                                        <span>重置</span>
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="pagead-index-nav-container pagead-index-fade-in-delay-2">
                        <ul class="pagead-index-nav-tabs">
                            <li class="pagead-index-nav-item">
                                <a href="{:U('Pagead/index')}" class="pagead-index-nav-link active">
                                    <i class="fa fa-list pagead-index-nav-icon"></i>
                                    <span>全部页面</span>
                                </a>
                            </li>
                            <li class="pagead-index-nav-item">
                                <a href="javascript:void(0)" class="pagead-index-nav-link quick-filter" data-filter="published">
                                    <i class="fa fa-check-circle pagead-index-nav-icon"></i>
                                    <span>已发布</span>
                                </a>
                            </li>
                            <li class="pagead-index-nav-item">
                                <a href="javascript:void(0)" class="pagead-index-nav-link quick-filter" data-filter="draft">
                                    <i class="fa fa-edit pagead-index-nav-icon"></i>
                                    <span>草稿</span>
                                </a>
                            </li>
                            <li class="pagead-index-nav-item">
                                <a href="javascript:void(0)" class="pagead-index-nav-link quick-filter" data-filter="disabled">
                                    <i class="fa fa-times-circle pagead-index-nav-icon"></i>
                                    <span>已禁用</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 现代化列表容器 -->
                    <form action="" method="post">
                        <div class="pagead-index-list-container pagead-index-fade-in-delay-3">
                            <div class="pagead-list-header">
                                <div class="pagead-list-icon">
                                    <i class="fa fa-list"></i>
                                </div>
                                <h3 class="pagead-list-title">公告列表</h3>
                            </div>
                            <div class="pagead-list-body">
                                <php>foreach($list as $v) { </php>
                                <div class="pagead-list-item">
                                    <!-- ID显示 -->
                                    <div class="pagead-item-id">
                                        #{$v.id}
                                    </div>

                                    <!-- 内容区域 -->
                                    <div class="pagead-item-content">
                                        <!-- 公告标题 -->
                                        <h4 class="pagead-item-title">
                                            {$v.title}
                                        </h4>

                                        <!-- 元信息 -->
                                        <div class="pagead-item-meta">
                                            <!-- 状态 -->
                                            <div class="pagead-item-status">
                                                <span class="pagead-status-badge label-{:$statusList[$v['status']]['style']}">
                                                    <i class="fa fa-circle"></i>
                                                    <span>{:$statusList[$v['status']]['text']}</span>
                                                </span>
                                            </div>

                                            <!-- 添加时间 -->
                                            <div class="pagead-item-time">
                                                <i class="fa fa-clock-o"></i>
                                                <span>{:date('Y-m-d H:i:s', $v['create_time'])}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="pagead-item-actions">
                                        <php>echo adStatBtn($v['id'], $v['status']);</php>
                                    </div>
                                </div>
                                <php>}</php>
                            </div>
                        </div>

                        <!-- 分页信息 -->
                        <div class="pagead-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem;">
                            {$page}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // 设置导航标签active状态的函数
        function setActiveNavTab() {
            // 移除所有active状态
            $('.pagead-index-nav-link').removeClass('active');

            // 获取当前URL参数
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');

            // 根据status参数设置对应的active状态
            if (status === '1') {
                // 已发布状态
                $('.quick-filter[data-filter="published"]').addClass('active');
            } else if (status === '0') {
                // 草稿状态
                $('.quick-filter[data-filter="draft"]').addClass('active');
            } else if (status === '2') {
                // 已禁用状态
                $('.quick-filter[data-filter="disabled"]').addClass('active');
            } else {
                // 默认状态（全部）
                $('.pagead-index-nav-link').first().addClass('active');
            }
        }

        // 快速筛选功能
        $('.quick-filter').click(function(e) {
            e.preventDefault();
            var $this = $(this);
            var filter = $this.data('filter');
            var currentUrl = window.location.href.split('?')[0];
            var newUrl = currentUrl;

            // 立即更新视觉状态，提供即时反馈
            $('.pagead-index-nav-link').removeClass('active');
            $this.addClass('active');

            // 添加加载状态
            var originalHtml = $this.html();
            $this.html('<i class="fa fa-spinner fa-spin pagead-index-nav-icon"></i><span>加载中...</span>');

            // 获取当前的搜索参数
            var urlParams = new URLSearchParams(window.location.search);
            var searchParams = new URLSearchParams();

            // 保留搜索关键词等其他参数
            if (urlParams.get('kw')) {
                searchParams.set('kw', urlParams.get('kw'));
            }
            if (urlParams.get('val')) {
                searchParams.set('val', urlParams.get('val'));
            }

            // 添加状态参数
            switch(filter) {
                case 'published':
                    searchParams.set('status', '1');
                    break;
                case 'draft':
                    searchParams.set('status', '0');
                    break;
                case 'disabled':
                    searchParams.set('status', '2');
                    break;
                default:
                    // 不添加status参数，显示全部
                    break;
            }

            // 构建最终URL
            var paramString = searchParams.toString();
            if (paramString) {
                newUrl += '?' + paramString;
            }

            // 延迟跳转，让用户看到加载状态
            setTimeout(function() {
                window.location.href = newUrl;
            }, 300);
        });

        // 处理"全部页面"标签的点击事件
        $('.pagead-index-nav-link').not('.quick-filter').click(function(e) {
            var $this = $(this);
            var href = $this.attr('href');

            // 如果是指向当前页面的链接，处理active状态
            if (href && href.indexOf('Pagead/index') !== -1) {
                e.preventDefault();

                // 立即更新视觉状态
                $('.pagead-index-nav-link').removeClass('active');
                $this.addClass('active');

                // 添加加载状态
                var originalHtml = $this.html();
                $this.html('<i class="fa fa-spinner fa-spin pagead-index-nav-icon"></i><span>加载中...</span>');

                // 延迟跳转到不带参数的页面
                setTimeout(function() {
                    window.location.href = href;
                }, 300);
            }
        });

        // 列表项悬停效果增强
        $('.pagead-list-item').hover(
            function() {
                $(this).addClass('hover-effect');
            },
            function() {
                $(this).removeClass('hover-effect');
            }
        );

        // 空状态处理
        var totalItems = $('.pagead-list-item').length;
        if (totalItems === 0) {
            var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-file-text"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无公告数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的公告。</p><a href="' + '{:U("Pagead/edit")}' + '" class="btn btn-primary" style="padding: 0.75rem 1.5rem; font-size: 1.5rem;"><i class="fa fa-plus"></i> 添加公告</a></div>');
            $('.pagead-index-list-container .pagead-list-body').append($emptyState);
        }

        // 延迟执行active状态设置，确保DOM完全加载
        setTimeout(function() {
            setActiveNavTab();
        }, 100);

        // 保持原有的状态切换功能
        $('.js_status').click(function() {
            var that = $(this),
                url = that.attr('url');
            $.get(url, function(data) {
                window.location.reload();
            });
        });
    });

    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
        } else {
            panel.classList.add('show');
        }
    }
</script>

<include file="block/footer" />
