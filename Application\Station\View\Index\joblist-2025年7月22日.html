﻿<!DOCTYPE html>
<html>
  <head>
    <title>简历管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
      name="viewport"
    />
    <meta content="no-cache,must-revalidate" http-equiv="Cache-Control" />
    <meta content="telephone=no, address=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta content="no-cache" http-equiv="pragma" />
    <meta content="0" http-equiv="expires" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/css.css?v={:time()}"
      media="all"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/swiper-bundle.css"
      media="all"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/iconfont.css"
      media="all"
    />
    <script
      type="text/javascript"
      src="/static/stations/js/jquery-1.9.1.min.js"
    ></script>
    <script
      type="text/javascript"
      src="/static/stations/js/swiper-bundle.min.js"
    ></script>
    <script src="/static/js/layer/layer.m.js"></script>
  </head>
  <style>
    .container {
      margin: 10px;
    }
    .nomore {
      display: block;
      align-items: center;
      text-align: center;
      color: #ccc;
    }

    /* 添加按钮样式 */
    .add-btn {
      margin-top: 2px;
      padding: 10px 18px;
      background: #ff6b35;
      color: white;
      border: none;
      border-radius: 10px;
      font-size: 17px;
      cursor: pointer;
      transition: all 0.2s;
      box-shadow: 0 4px 12px rgba(0, 191, 128, 0.3);
    }

    .add-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 191, 128, 0.4);
    }

    /* 模态框遮罩层 */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    /* 模态框内容 */
    .modal-content {
      background: white;
      padding: 28px;
      border-radius: 12px;
      width: 90%;
      max-width: 480px;
      position: relative;
      animation: modalSlide 0.4s cubic-bezier(0.23, 1, 0.32, 1);
      box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    @keyframes modalSlide {
      from {
        transform: scale(0.8);
        opacity: 0;
      }
      to {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* 操作类型选择 */
    .method-section {
      margin-bottom: 24px;
    }

    .method-title {
      color: #1a1a1a;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 20px;
      text-align: center;
    }

    .method-options {
      display: grid;
      gap: 20px;
    }

    /* 上传区域 */
    .upload-box {
      border: 2px dashed #00bf80;
      border-radius: 12px;
      padding: 24px;
      text-align: center;
      position: relative;
      transition: all 0.3s;
    }

    .upload-box:hover {
      background: rgba(0, 191, 128, 0.05);
    }

    #file-input {
      position: absolute;
      opacity: 0;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      cursor: pointer;
    }

    .upload-icon {
      font-size: 40px;
      margin-bottom: 12px;
      color: #00bf80;
    }

    /* 进度条样式 */
    .progress-bar {
      width: 100%;
      height: 8px;
      background: #eee;
      border-radius: 4px;
      margin-top: 16px;
      overflow: hidden;
      display: none;
    }

    .progress {
      width: 0%;
      height: 100%;
      background: #00bf80;
      transition: width 0.3s ease;
    }

    /* 多阶段进度指示器 */
    .multi-stage-progress {
      display: none;
      margin-top: 20px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 12px;
      border: 1px solid #e9ecef;
    }

    .stage-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .stage-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    .stage-subtitle {
      font-size: 14px;
      color: #666;
    }

    .stages-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      position: relative;
    }

    .stage-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      position: relative;
      z-index: 2;
    }

    .stage-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      margin-bottom: 8px;
      transition: all 0.3s ease;
      background: #e9ecef;
      color: #6c757d;
      border: 2px solid #e9ecef;
    }

    .stage-icon.pending {
      background: #e9ecef;
      color: #6c757d;
      border-color: #e9ecef;
    }

    .stage-icon.active {
      background: #00bf80;
      color: white;
      border-color: #00bf80;
      animation: pulse 2s infinite;
    }

    .stage-icon.completed {
      background: #28a745;
      color: white;
      border-color: #28a745;
    }

    .stage-icon.error {
      background: #dc3545;
      color: white;
      border-color: #dc3545;
    }

    .stage-label {
      font-size: 12px;
      text-align: center;
      color: #666;
      font-weight: 500;
      max-width: 80px;
      line-height: 1.2;
    }

    .stage-item.active .stage-label {
      color: #00bf80;
      font-weight: bold;
    }

    .stage-item.completed .stage-label {
      color: #28a745;
    }

    .stage-item.error .stage-label {
      color: #dc3545;
    }

    /* 连接线 */
    .stages-container::before {
      content: '';
      position: absolute;
      top: 20px;
      left: 10%;
      right: 10%;
      height: 2px;
      background: #e9ecef;
      z-index: 1;
    }

    .stage-progress-line {
      position: absolute;
      top: 20px;
      left: 10%;
      height: 2px;
      background: #00bf80;
      z-index: 1;
      width: 0%;
      transition: width 0.5s ease;
    }

    /* 脉冲动画 */
    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(0, 191, 128, 0.7);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(0, 191, 128, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(0, 191, 128, 0);
      }
    }

    /* 旋转动画 */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .stage-icon.active .icon-spinner {
      animation: spin 1s linear infinite;
    }

    /* 当前状态描述 */
    .current-stage-info {
      text-align: center;
      margin-top: 15px;
      padding: 12px;
      background: white;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .current-stage-text {
      font-size: 14px;
      color: #333;
      margin-bottom: 5px;
    }

    .current-stage-detail {
      font-size: 12px;
      color: #666;
    }

    /* 链接生成区域 */
    .link-section {
      margin-top: 8px;
    }

    /* 按钮组容器 */
    .link-section > div:first-child {
      display: flex;
      gap: 12px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .link-box {
      display: flex;
      gap: 10px;
      margin-top: 16px;
    }

    .link-input {
      flex: 1;
      padding: 12px 16px;
      border: 1px solid #e1e5e9;
      border-radius: 8px;
      font-size: 14px;
      background: #f8f9fa;
      transition: all 0.2s;
    }

    .link-input:focus {
      outline: none;
      border-color: #00bf80;
      background: white;
    }

    /* 主操作按钮 - 生成链接 */
    .copy-btn {
      padding: 12px 20px;
      background: #00bf80;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
      font-weight: 500;
      font-size: 14px;
      min-height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .copy-btn:hover {
      background: #00a86b;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0,191,128,0.3);
    }

    .copy-btn:disabled {
      background: #cccccc !important;
      color: #666666 !important;
      cursor: not-allowed !important;
      transform: none !important;
      box-shadow: none !important;
      opacity: 0.6 !important;
    }

    .copy-btn:disabled:hover {
      background: #cccccc !important;
      transform: none !important;
      box-shadow: none !important;
    }

    /* 次要操作按钮 - 下载模板 */
    .secondary-btn {
      background: #6c757d !important;
      border: 1px solid #6c757d !important;
    }

    .secondary-btn:hover {
      background: #5a6268 !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(108,117,125,0.3);
    }

    /* 关闭按钮 */
    .close-btn {
      position: absolute;
      top: 16px;
      right: 16px;
      width: 28px;
      height: 28px;
      cursor: pointer;
      opacity: 0.6;
      transition: all 0.2s;
      background: rgba(0,0,0,0.05);
      border-radius: 50%;
      padding: 4px;
    }

    .close-btn:hover {
      opacity: 1;
      background: rgba(0,0,0,0.1);
      transform: scale(1.1);
    }

    /* 提示信息 */
    .hint-text {
      color: #6c757d;
      font-size: 13px;
      margin-top: 8px;
      text-align: center;
      line-height: 1.4;
    }

    .success-msg {
      color: #00bf80;
      display: none;
      margin-top: 12px;
      font-weight: 500;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .modal-content {
        width: 95%;
        padding: 20px;
        margin: 20px;
      }

      .link-section > div:first-child {
        flex-direction: column;
        gap: 10px;
      }

      .copy-btn {
        width: 100%;
        justify-content: center;
      }

      .method-title {
        font-size: 16px;
      }
    }
    /* 搜索框样式 */
    .search-bar {
      background: #fff;
      padding: 8px 15px;
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .search-input {
      width: 100%;
      height: 36px;
      border: 1px solid #e5e5e5;
      border-radius: 18px;
      padding: 0 15px;
      box-sizing: border-box;
      background: #f0f0f0;
      font-size: 14px;
    }

    /* 简历列表项 */
    .resume-item {
      background: #fff;
      border-radius: 8px;
      margin: 18px 0;
      padding: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .basic-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .left-info > div,
    .right-info > div {
      margin: 2px 0;
    }

    .position-info {
      color: #666;
      margin: 8px 0;
      padding: 8px 0;
      border-top: 1px solid #eee;
    }

    /* 按钮区域 */
    .action-area {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
    }

    .btn {
      padding: 6px 15px;
      border-radius: 15px;
      font-size: 13px;
      border: none;
    }

    .btn-primary {
      background: #07c160;
      color: white;
    }

    .btn-disabled {
      background: #ddd;
      color: #999;
    }

    .status-tag {
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 12px;
    }
    .chat-btn {
      background: #1d4051;
      color: white;
    }
    .chat-btn i {
      display: inline-block;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: #da5352;
      position: absolute;
      z-index: 2;
      margin-top: -16px;
      margin-left: -66px;
    }

    .study-btn {
      background: #f08b19;
      color: white;
    }
    /* 状态颜色 */
    .status-communicating {
      background: #e6f3ff;
      color: #007aff;
    }
    .status-training {
      background: #fff7e6;
      color: #ff9500;
    }
    .status-employed {
      background: #e6ffe6;
      color: #07c160;
    }
    .status-terminated {
      background: #ffe6e6;
      color: #ff3b30;
    }
    .status-jobdoc {
      background: #fffbfb;
      color: #115c0d;
      border: 1px dashed #606760;
    }
    .status-jobdocself {
      background: #fffbfb;
      color: #115c0d;
      border: 1px dashed #ccc;
    }
    .status-jobdoce {
      background: #fffbfb;
      color: #115c0d;
    }
    .status-jobdocerr {
      background: #f3a05c;
      color: #115c0d;
    }

    /* 简历类型筛选按钮样式 */
    .resume-type-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .resume-type-btn {
      padding: 6px 12px;
      background: #fff;
      color: #666;
      border: 1px solid #ddd;
      border-radius: 4px;
      text-decoration: none;
      font-size: 13px;
      transition: all 0.2s;
    }

    .resume-type-btn:hover {
      background: #f0f0f0;
      color: #333;
      text-decoration: none;
    }

    .resume-type-btn.active {
      background: #007aff;
      color: #fff;
      border-color: #007aff;
    }

    /* 简历类型标识样式 */
    .resume-type-badge {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 11px;
      margin-left: 8px;
    }

    .resume-type-badge.own-resume {
      background: #e6ffe6;
      color: #07c160;
      border: 1px solid #07c160;
    }

    .resume-type-badge.zjb-resume {
      background: #e6f3ff;
      color: #007aff;
      border: 1px solid #007aff;
    }

    /* 培训弹窗表单样式 */
    .training-form {
      padding: 16px;
      background-color: #fff;
      max-height: 80vh;
      overflow-y: auto;
    }

    .form-item {
      margin-bottom: 16px;
    }

    .form-item label {
      display: block;
      margin-bottom: 6px;
      font-weight: bold;
      color: #333;
      font-size: 14px;
    }

    /* 收益计算区域样式 */
    .profit-calculation {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;
    }

    .profit-calculation h4 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #495057;
      font-weight: bold;
    }

    .formula-item {
      margin-bottom: 8px;
      font-size: 12px;
      color: #6c757d;
      line-height: 1.4;
    }

    .profit-details {
      background: #fff;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 10px;
      margin-top: 10px;
    }

    .profit-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
      font-size: 13px;
    }

    .profit-row:last-child {
      margin-bottom: 0;
      font-weight: bold;
      color: #28a745;
      border-top: 1px solid #dee2e6;
      padding-top: 6px;
    }

    .profit-label {
      color: #495057;
    }

    .profit-value {
      font-weight: 500;
    }

    /* 价格区间样式 */
    .price-range {
      background: #e3f2fd;
      border: 1px solid #bbdefb;
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 13px;
      color: #1976d2;
      font-weight: 500;
    }

    /* 基准成本价显示 */
    .base-cost-info {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 13px;
      color: #856404;
      margin-bottom: 10px;
    }

    /* 价格输入框组样式 */
    .price-input-group {
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .price-format-hint {
      font-size: 11px;
      color: #6c757d;
      margin-top: 4px;
      line-height: 1.3;
    }

    .price-format-hint.active {
      color: #007bff;
      font-weight: 500;
    }

    .formatted-result {
      font-size: 12px;
      color: #28a745;
      margin-top: 4px;
      font-weight: 500;
      display: none;
    }

    .formatted-result.warning {
      color: #dc3545;
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
      .training-form {
        padding: 12px;
      }

      .profit-calculation {
        padding: 10px;
      }

      .form-item label {
        font-size: 13px;
      }

      .formula-item {
        font-size: 11px;
      }

      .profit-row {
        font-size: 12px;
      }
    }
  </style>
  <body>
    <include file="headers" />

    <div class="tabs-hd02" style="margin: 10px">
      <div class="weap">
        <ul>
          <li class="{:empty($type) ? 'on' : ''}" data-type="0">
            <span class="a">全部({$stateCounts.allcount})</span>
          </li>
          <li class="{:$type == 1 ? 'on' : ''}" data-type="1">
            <span class="a">沟通中({$stateCounts.communicating})</span>
          </li>
          <li class="{:$type == 2 ? 'on' : ''}" data-type="2">
            <span class="a">培训中({$stateCounts.training})</span>
          </li>
          <li class="{:$type == 3 ? 'on' : ''}" data-type="3">
            <span class="a">已入职({$stateCounts.onboarding})</span>
          </li>
          <li class="{:$type == 4 ? 'on' : ''}" data-type="4">
            <span class="a">服务终止({$stateCounts.terminated})</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- 简历类型筛选标签（仅服务站用户显示） -->
    <if condition="$serviceStationRow['zsb_type'] eq 1">
    <div class="resume-type-tabs" style="margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 8px;">
      <div style="margin-bottom: 8px; font-size: 14px; color: #666;">简历类型筛选：</div>
      <div class="resume-type-buttons">
        <a href="javascript:void(0);" class="resume-type-btn {:$resume_type == 0 ? 'active' : ''}" data-type="0">全部简历</a>
        <a href="javascript:void(0);" class="resume-type-btn {:$resume_type == 1 ? 'active' : ''}" data-type="1">自有简历</a>
        <a href="javascript:void(0);" class="resume-type-btn {:$resume_type == 2 ? 'active' : ''}" data-type="2">招就办简历</a>
      </div>
    </div>
    </if>

    <div class="container">
      <div class="search-bar">
        <input
          type="text"
          name="kwd"
          value="{:$kwd}"
          class="search-input js_kwd"
          placeholder="搜索姓名"
        />
      </div>
      <php>if (empty($list)) {</php>
      <div class="bd">
        <ul>
          <li
            style="
              text-align: center;
              font-size: 13px;
              color: darkgray;
              padding: 20px 0;
            "
          >
            无相关简历数据
          </li>
        </ul>
      </div>
      <php>} else {</php>
      <div id="resumeList">
        <include file="list-joblist" />
      </div>
      <php>}</php>
    </div>
    <div class="modal-overlay" id="modal">
      <div class="modal-content">
        <!-- 关闭按钮 -->
        <svg
          class="close-btn"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#666"
          onclick="closeModal()"
        >
          <path
            d="M18 6L6 18M6 6l12 12"
            stroke-width="2"
            stroke-linecap="round"
          />
        </svg>
        <!-- 操作方式选择 -->
        <div class="method-section" style="margin-top: 22px">
          <h2 class="method-title" id="t1">选择简历添加方式</h2>
          <h2
            class="method-title"
            style="display: none; color: red; font-weight: bold"
            id="t2"
          >
            简历诉求
          </h2>
          <div class="method-options">
            <!-- 上传区域 -->
            <div class="upload-box">
              <div id="stext">
                <div class="upload-icon">📤</div>
                <p>【您自己操作】点这里上传简历文件</p>
                <p class="hint-text">仅支持使用官方标准模板填写的Word简历文档</p>
                <input type="file" id="file-input" accept=".docx" />
              </div>

              <div style="width: 100%; display: none" id="j_content">
                <!-- 分割线 -->
                <textarea
                  id="job_content"
                  class="link-input"
                  style="width: 100%; height: 88px"
                  placeholder="在这里输入该简历诉求，比如想去哪里，想找什么工作"
                ></textarea>
                <!-- 提交按钮绑定事件 -->
                <button
                  class="copy-btn"
                  onclick="submitResume()"
                  style="text-align: center; margin-top: 18px"
                >
                  提 交
                </button>
                <br />
              </div>

              <!-- 上传进度 -->
              <p class="success-msg" id="upload-success">✓ 正在保存简历……</p>
              <div class="progress-bar">
                <div class="progress" id="progress"></div>
              </div>

              <!-- 多阶段进度指示器 -->
              <div class="multi-stage-progress" id="multi-stage-progress">
                <div class="stage-header">
                  <div class="stage-title">简历处理中</div>
                  <div class="stage-subtitle">请耐心等待，我们正在验证您的简历</div>
                </div>

                <div class="stages-container">
                  <div class="stage-progress-line" id="stage-progress-line"></div>

                  <div class="stage-item" id="stage-upload">
                    <div class="stage-icon pending">
                      <span class="icon-content">📤</span>
                    </div>
                    <div class="stage-label">文件上传</div>
                  </div>

                  <div class="stage-item" id="stage-template">
                    <div class="stage-icon pending">
                      <span class="icon-content">📋</span>
                    </div>
                    <div class="stage-label">模板检测</div>
                  </div>

                  <div class="stage-item" id="stage-verify">
                    <div class="stage-icon pending">
                      <span class="icon-content">🔍</span>
                    </div>
                    <div class="stage-label">内容验证</div>
                  </div>

                  <div class="stage-item" id="stage-save">
                    <div class="stage-icon pending">
                      <span class="icon-content">💾</span>
                    </div>
                    <div class="stage-label">保存简历</div>
                  </div>
                </div>

                <div class="current-stage-info" id="current-stage-info">
                  <div class="current-stage-text" id="current-stage-text">准备开始处理...</div>
                  <div class="current-stage-detail" id="current-stage-detail">请稍候</div>
                </div>
              </div>
            </div>

            <div id="stext2">
              <!-- 分割线 -->
              <div style="text-align: center; color: #999; margin: 10px 0">
                或 生成简历创建链接复制发给求职者
              </div>

              <!-- 生成链接区域 -->
              <div class="link-section">
                <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                  <button class="copy-btn" onclick="generateLink()">
                    点这生成简历创建链接
                  </button>
                  <a href="http://c.zhongcaiguoke.com/data/%E5%BA%94%E8%81%98%E4%BA%BA%E5%91%98%E6%8A%A5%E5%90%8D%E8%A1%A8%EF%BC%88%E9%80%9A%E7%94%A8%EF%BC%89.docx"
                     download="应聘人员报名表（通用）.docx"
                     class="copy-btn secondary-btn"
                     style="text-decoration: none; display: inline-block; text-align: center;">
                    📥 下载标准模板
                  </a>
                </div>
                <div class="link-box">
                  <input
                    type="text"
                    class="link-input"
                    id="link-text"
                    readonly
                    placeholder="点上方生成链接发给求职者"
                  />
                  <button class="copy-btn" onclick="copyLink()">复制</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // 显示模态框
      function showModal() {
        document.getElementById("modal").style.display = "flex";
        document.body.style.overflow = "hidden"; // 禁止背景滚动
      }

      // 关闭弹窗时重置状态
      function closeModal() {
        document.getElementById("modal").style.display = "none";
        document.body.style.overflow = "auto";
        resetUploadStatus();
        // 新增重置操作
        selectedFile = null;
        document.getElementById("job_content").value = "";
      }

      // 点击遮罩关闭
      document.getElementById("modal").addEventListener("click", function (e) {
        if (e.target === this) closeModal();
      });

      // 生成随机链接
      function generateLink() {
        $.ajax({
          type: "post",
          data: {},
          url: "/index/getsorturl",
          beforeSend: function () {
            //loading层
            layer.open({
              type: 2,
              shadeClose: false,
            });
          },
          complete: function () {
            // Handle the complete event
          },
          success: function (data) {
            layer.closeAll();
            ajaxsend = true;
            if (data.status == 1) {
              $("#link-text").val(data.info);
            } else {
              layer.open({
                style: "width:80%",
                shadeClose: false,
                content:
                  "<div style='text-align: left'>" + data.info + "</div>",
                btn: ["我知道了"],
                yes: function (index) {
                  layer.closeAll();
                },
              });
            }
          },
        });
      }

      // 复制链接功能
      function copyLink() {
        const link = document.getElementById("link-text").value;
        if (!link) return;
        copyToClipboardLegacyTwo(link);

        // navigator.clipboard.writeText(link).then(() => {
        //     showTempMessage('链接已复制到剪贴板');
        // });
      }

      // 新增全局变量用于存储文件对象
      let selectedFile = null;

      // 修改文件选择事件（仅保存文件不立即上传）
      document
        .getElementById("file-input")
        .addEventListener("change", function (e) {
          const file = e.target.files[0];
          if (!file) return;

          // 验证文件格式
          const fileName = file.name.toLowerCase();
          const validExtensions = [".doc", ".docx", ".pdf"];
          const isValidFile = validExtensions.some((ext) =>
            fileName.endsWith(ext)
          );

          if (!isValidFile) {
            alert("仅支持上传word文档或PDF格式简历");
            this.value = ""; // 清空选择
            return;
          }

          // 保存文件对象
          selectedFile = file;

          // 立即显示输入区域
          const jcontentstr = document.getElementById("j_content");
          const stextstr = document.getElementById("stext");
          const stextstr2 = document.getElementById("stext2");
          jcontentstr.style.display = "block";
          stextstr.style.display = "none";
          stextstr2.style.display = "none";
          document.getElementById("t1").style.display = "none";
          document.getElementById("t2").style.display = "block";
        });

      // 多阶段进度控制
      const stages = [
        { id: 'stage-upload', name: '文件上传', icon: '📤', spinner: '⟳' },
        { id: 'stage-template', name: '模板检测', icon: '📋', spinner: '⟳' },
        { id: 'stage-verify', name: '内容验证', icon: '🔍', spinner: '⟳' },
        { id: 'stage-save', name: '保存简历', icon: '💾', spinner: '⟳' }
      ];

      // 时间记录系统
      let resumeUploadTimestamps = {
        processStart: null,
        stageStart: {},
        stageEnd: {},
        processEnd: null
      };

      // 阶段显示控制
      const STAGE_MIN_DURATION = 1000; // 每个阶段最小显示时间（毫秒）
      let isStageTransitioning = false; // 是否正在阶段切换中
      let pendingServerResponse = null; // 待处理的服务器响应
      let errorModalClosed = false; // 错误弹窗是否已被用户关闭

      // 格式化时间输出函数
      function formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString() + '.' + date.getMilliseconds().toString().padStart(3, '0');
      }

      // 记录阶段时间的函数
      function logStageTime(action, stageIndex = null, additionalInfo = '') {
        const timestamp = Date.now();
        const timeStr = formatTimestamp(timestamp);

        if (action === 'process_start') {
          resumeUploadTimestamps.processStart = timestamp;
        } else if (action === 'stage_start') {
          resumeUploadTimestamps.stageStart[stageIndex] = timestamp;
        } else if (action === 'stage_end') {
          resumeUploadTimestamps.stageEnd[stageIndex] = timestamp;
        } else if (action === 'stage_error') {
          // 记录错误时间
        } else if (action === 'process_end') {
          resumeUploadTimestamps.processEnd = timestamp;
        }
      }

      let currentStageIndex = 0;
      let stageTimers = []; // 存储所有阶段相关的定时器
      let isErrorState = false; // 标记是否处于错误状态

      function showMultiStageProgress() {
        // 重置状态变量
        isStageTransitioning = false;
        pendingServerResponse = null;
        isErrorState = false;
        errorModalClosed = false;

        // 禁用提交按钮，防止重复提交
        const submitBtn = document.querySelector('.copy-btn');
        if (submitBtn) {
          submitBtn.disabled = true;
          submitBtn.style.opacity = '0.6';
          submitBtn.style.cursor = 'not-allowed';
          submitBtn.textContent = '处理中...';
        }

        // 记录进度开始时间
        logStageTime('process_start', null, '- 显示多阶段进度界面');

        document.querySelector(".progress-bar").style.display = "none";
        document.getElementById("upload-success").style.display = "none";
        document.getElementById("multi-stage-progress").style.display = "block";
        currentStageIndex = 0;
        updateStageProgress();
      }

      function hideMultiStageProgress() {
        document.getElementById("multi-stage-progress").style.display = "none";

        // 重新启用提交按钮
        const submitBtn = document.querySelector('.copy-btn');
        if (submitBtn) {
          submitBtn.disabled = false;
          submitBtn.style.opacity = '1';
          submitBtn.style.cursor = 'pointer';
          submitBtn.textContent = '提 交';
        }
      }

      function updateStageProgress() {
        // 记录当前阶段开始时间
        logStageTime('stage_start', currentStageIndex, `- 更新UI界面`);

        const progressLine = document.getElementById("stage-progress-line");
        const currentStageText = document.getElementById("current-stage-text");
        const currentStageDetail = document.getElementById("current-stage-detail");

        // 更新进度线
        const progressPercent = (currentStageIndex / (stages.length - 1)) * 80; // 80% 是连接线的实际宽度
        progressLine.style.width = progressPercent + '%';

        // 更新所有阶段状态
        stages.forEach((stage, index) => {
          const stageElement = document.getElementById(stage.id);
          const iconElement = stageElement.querySelector('.stage-icon');
          const iconContent = stageElement.querySelector('.icon-content');

          // 移除所有状态类
          iconElement.classList.remove('pending', 'active', 'completed', 'error');
          stageElement.classList.remove('pending', 'active', 'completed', 'error');

          if (index < currentStageIndex) {
            // 已完成的阶段
            iconElement.classList.add('completed');
            stageElement.classList.add('completed');
            iconContent.textContent = '✓';
          } else if (index === currentStageIndex) {
            // 当前进行的阶段
            iconElement.classList.add('active');
            stageElement.classList.add('active');
            iconContent.innerHTML = '<span class="icon-spinner">' + stage.spinner + '</span>';
          } else {
            // 待进行的阶段
            iconElement.classList.add('pending');
            stageElement.classList.add('pending');
            iconContent.textContent = stage.icon;
          }
        });

        // 更新当前状态文字
        if (currentStageIndex < stages.length) {
          const currentStage = stages[currentStageIndex];
          currentStageText.textContent = currentStage.name + '中...';

          switch (currentStageIndex) {
            case 0:
              currentStageDetail.textContent = '正在上传文件到服务器';
              break;
            case 1:
              currentStageDetail.textContent = '检查简历是否使用官方模板';
              break;
            case 2:
              currentStageDetail.textContent = '验证联系方式和身份信息';
              break;
            case 3:
              currentStageDetail.textContent = '保存简历信息到数据库';
              break;
          }
        }
      }

      function nextStage() {
        if (!isErrorState && currentStageIndex < stages.length - 1) {
          // 记录当前阶段完成时间
          logStageTime('stage_end', currentStageIndex, '- 阶段切换');

          currentStageIndex++;
          updateStageProgress();
        }
      }

      // 新增：带最小时间控制的阶段切换函数
      function nextStageWithMinDuration() {
        return new Promise((resolve) => {
          if (isStageTransitioning) {
            resolve();
            return;
          }

          isStageTransitioning = true;

          const stageStartTime = resumeUploadTimestamps.stageStart[currentStageIndex];
          const elapsed = stageStartTime ? Date.now() - stageStartTime : 0;
          const remainingTime = Math.max(0, STAGE_MIN_DURATION - elapsed);

          logStageTime('stage_end', currentStageIndex, `- 阶段切换 (已显示${elapsed}ms, 等待${remainingTime}ms)`);

          setTimeout(() => {
            // 切换到下一个阶段
            currentStageIndex++;
            updateStageProgress();
            isStageTransitioning = false;
            resolve();
          }, remainingTime);
        });
      }



      function clearAllStageTimers() {
        stageTimers.forEach(timer => clearTimeout(timer));
        stageTimers = [];
      }

      function resetToStage(targetStageIndex) {
        // 清除所有定时器，防止自动推进
        clearAllStageTimers();
        isErrorState = true;

        // 重置所有阶段状态
        stages.forEach((stage, index) => {
          const stageElement = document.getElementById(stage.id);
          const iconElement = stageElement.querySelector('.stage-icon');
          const iconContent = stageElement.querySelector('.icon-content');

          // 清除所有状态类
          iconElement.classList.remove('active', 'completed', 'error');
          stageElement.classList.remove('active', 'completed', 'error');

          if (index < targetStageIndex) {
            // 前面的阶段设为完成
            iconElement.classList.add('completed');
            stageElement.classList.add('completed');
            iconContent.textContent = '✓';
          } else if (index === targetStageIndex) {
            // 目标阶段设为当前活动阶段
            iconElement.classList.add('active');
            stageElement.classList.add('active');
            iconContent.textContent = stage.spinner;
          } else {
            // 后面的阶段保持默认状态
            iconContent.textContent = stage.icon;
          }
        });

        // 更新当前阶段索引
        currentStageIndex = targetStageIndex;
      }

      function setStageError(errorMessage) {
        // 记录错误发生时间
        logStageTime('stage_error', currentStageIndex, `- 错误信息: ${errorMessage}`);
        const stageElement = document.getElementById(stages[currentStageIndex].id);
        const iconElement = stageElement.querySelector('.stage-icon');
        const iconContent = stageElement.querySelector('.icon-content');

        // 清除所有状态类，确保错误状态优先
        iconElement.classList.remove('active', 'completed', 'pending');
        stageElement.classList.remove('active', 'completed', 'pending');

        // 设置错误状态
        iconElement.classList.add('error');
        stageElement.classList.add('error');
        iconContent.textContent = '✗';

        document.getElementById("current-stage-text").textContent = '处理失败';
        document.getElementById("current-stage-detail").textContent = errorMessage;
      }

      function completeAllStages() {
        currentStageIndex = stages.length - 1;
        updateStageProgress();

        // 标记最后一个阶段为完成
        setTimeout(() => {
          const lastStageElement = document.getElementById(stages[stages.length - 1].id);
          const iconElement = lastStageElement.querySelector('.stage-icon');
          const iconContent = lastStageElement.querySelector('.icon-content');

          iconElement.classList.remove('active');
          iconElement.classList.add('completed');
          lastStageElement.classList.remove('active');
          lastStageElement.classList.add('completed');
          iconContent.textContent = '✓';

          document.getElementById("current-stage-text").textContent = '处理完成';
          document.getElementById("current-stage-detail").textContent = '简历已成功保存';
        }, 500);
      }

      // 新增提交函数
      function submitResume() {
        if (!selectedFile) {
          alert("请先选择简历文件");
          return;
        }

        const jobContent = document.getElementById("job_content").value.trim();
        if (!jobContent) {
          alert("请输入简历求职诉求");
          return;
        }

        const formData = new FormData();

        // 合并文件和其他参数
        formData.append("file", selectedFile);
        formData.append("jobcontent", jobContent);

        // 显示多阶段进度
        showMultiStageProgress();

        const xhr = new XMLHttpRequest();

        // 添加进度模拟定时器变量
        let progressTimer = null;
        let serverResponseReceived = false;

        xhr.upload.onprogress = function (e) {
          if (e.lengthComputable) {
            const percent = (e.loaded / e.total) * 100;
            // 文件上传阶段的进度
            if (currentStageIndex === 0 && percent >= 100 && !isErrorState && !isStageTransitioning) {
              // 上传完成，记录但不立即切换，等待服务器响应
              logStageTime('stage_end', 0, `- 文件上传完成 (${percent.toFixed(1)}%)`);
            }
          }
        };

        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4) {
            // 标记服务器响应已收到
            serverResponseReceived = true;

            // 清除进度模拟定时器
            if (progressTimer) {
              clearTimeout(progressTimer);
              progressTimer = null;
            }

            if (xhr.status === 200) {
              const res = JSON.parse(xhr.responseText);
              pendingServerResponse = res; // 保存服务器响应

              if (res.status === 1) {
                // 成功响应，从当前阶段开始完成剩余阶段
                logStageTime('stage_end', currentStageIndex, '- 服务器响应成功');

                async function completeRemainingStages() {
                  // 从当前阶段开始完成剩余阶段
                  while (currentStageIndex < stages.length - 1) {
                    await nextStageWithMinDuration();
                  }

                  // 所有阶段完成
                  logStageTime('stage_end', currentStageIndex, '- 最终阶段完成');
                  completeAllStages();
                  showTempMessage(res.msg);
                  logStageTime('process_end', null, '- 简历上传成功');

                  // 重新启用提交按钮
                  const submitBtn = document.querySelector('.copy-btn');
                  if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.style.opacity = '1';
                    submitBtn.style.cursor = 'pointer';
                    submitBtn.textContent = '提 交';
                  }

                  setTimeout(() => {
                    closeModal();
                    window.location.href = res.url || '{:U("index/joblist")}';
                  }, 2000);
                }

                completeRemainingStages();
              } else if (res.status === 2) {
                // 重复文件错误，特殊处理
                // 立即重置到上传阶段显示错误
                resetToStage(0);
                setStageError(res.msg);
                logStageTime('process_end', null, `- 简历上传失败: ${res.msg}`);

                // 显示友好的重复文件错误弹窗
                setTimeout(() => {
                  showDuplicateFileError(res);
                }, 800);
              } else {
                // 处理错误 - 根据错误类型确定正确的错误阶段并立即显示
                let errorMessage = res.msg || '处理失败';
                let targetStageIndex = 1; // 默认为模板检测阶段

                // 根据错误类型确定正确的错误阶段
                if (res.error_type === 'verification_failed' ||
                    res.error_type === 'contact_info_missing' ||
                    res.error_type === 'email_missing' ||
                    res.error_type === 'phone_missing' ||
                    res.error_type === 'invalid_id_card' ||
                    res.error_type === 'duplicate_submission' ||
                    res.error_type === 'student_locked') {
                  // 内容验证错误，应该显示在第三阶段
                  targetStageIndex = 2; // 内容验证阶段
                } else if (res.error_type === 'template_detection_failed') {
                  // 模板检测错误，显示在第二阶段
                  targetStageIndex = 1; // 模板检测阶段
                } else if (res.error_type === 'upload_failed') {
                  // 上传错误，显示在第一阶段
                  targetStageIndex = 0; // 文件上传阶段
                }

                // 设置错误状态标志，防止其他逻辑干扰
                isErrorState = true;
                clearAllStageTimers();

                // 先推进到错误阶段，让用户看到进展
                async function progressToErrorStage() {
                  // 逐步推进到目标错误阶段
                  while (currentStageIndex < targetStageIndex) {
                    await nextStageWithMinDuration();
                  }

                  // 到达错误阶段后显示错误
                  setStageError(errorMessage);
                  logStageTime('process_end', null, `- 简历上传失败: ${errorMessage}`);

                  setTimeout(() => {
                    showDetailedError(res);
                  }, 800);

                  setTimeout(() => {
                    // 只有在用户没有手动关闭错误弹窗时才自动隐藏
                    if (!errorModalClosed) {
                      hideMultiStageProgress();
                      resetUploadStatus();
                    }
                  }, 5000);
                }

                progressToErrorStage();
              }
            } else {
              let errorMessage = '网络错误';
              if (xhr.status == 413) {
                errorMessage = "文件过大，请检查文件大小";
              } else {
                errorMessage = "网络连接异常，请重试";
              }
              // 网络错误重置到上传阶段
              resetToStage(0);
              setTimeout(() => {
                setStageError(errorMessage);
              }, 100);

              // 记录网络错误结束时间
              logStageTime('process_end', null, `- 网络错误: ${errorMessage}`);

              setTimeout(() => {
                hideMultiStageProgress();
                alert("上传失败：" + errorMessage);
                resetUploadStatus();
              }, 2000);
            }
          }
        };

        xhr.open("POST", '{:U("index/uploadJobFile")}', true);
        xhr.send(formData);
      }
      //
      // 显示临时提示信息
      function showTempMessage(msg) {
        const tempDiv = document.createElement("div");
        tempDiv.className = "hint-text";
        tempDiv.textContent = msg;
        tempDiv.style.color = "#00bf80";
        tempDiv.style.marginTop = "12px";

        document.querySelector(".method-options").appendChild(tempDiv);
        setTimeout(() => tempDiv.remove(), 2000);
      }

      // 显示详细错误信息
      function showDetailedError(response) {

        // 移除之前的错误提示
        const existingError = document.querySelector('.error-detail-modal');
        if (existingError) {
          existingError.remove();
        }

        // 根据错误类型定制错误信息
        let errorIcon = '⚠️';
        let errorTitle = '简历上传失败';
        let suggestions = response.suggestions || [];

        // 优化错误类型判断，支持更精确的错误提示
        if (response.error_type) {
          switch (response.error_type) {
            case 'verification_failed':
              errorIcon = '🔍';
              errorTitle = '简历验证失败';
              suggestions = [
                '请确保上传的是完整的Word文档',
                '检查文件是否损坏或格式错误',
                '如果问题持续，请联系客服'
              ];
              break;
            case 'contact_info_missing':
              errorIcon = '📞';
              errorTitle = '联系方式不完整';
              suggestions = [
                '请在简历中填写有效的手机号码',
                '请在简历中填写有效的邮箱地址',
                '确保联系方式格式正确且清晰可见'
              ];
              break;
            case 'email_missing':
              errorIcon = '📧';
              errorTitle = '邮箱地址缺失';
              suggestions = [
                '请在简历中填写有效的邮箱地址',
                '确保邮箱格式正确（如：<EMAIL>）',
                '邮箱地址应清晰可见，不要使用图片形式'
              ];
              break;
            case 'phone_missing':
              errorIcon = '📱';
              errorTitle = '联系电话缺失';
              suggestions = [
                '请在简历中填写有效的手机号码',
                '确保手机号码为11位数字',
                '联系电话应清晰可见，不要使用图片形式'
              ];
              break;
            case 'invalid_id_card':
              errorIcon = '🆔';
              errorTitle = '身份证信息无效';
              suggestions = [
                '请检查简历中的身份证号码是否正确',
                '确保身份证号码为18位有效数字',
                '检查身份证号码是否清晰可见'
              ];
              break;
            case 'duplicate_submission':
              errorIcon = '🔄';
              errorTitle = '简历重复提交';
              suggestions = [
                '该身份证号码的简历已存在',
                '请检查是否之前已经上传过相同学员的简历',
                '如需更新简历，请先删除之前的记录'
              ];
              break;
            case 'student_locked':
              errorIcon = '🔒';
              errorTitle = '学员已被锁定';
              suggestions = [
                '该学员已在其他服务站注册',
                '如有疑问，请联系相关服务站协调',
                '或联系平台客服处理'
              ];
              break;
            default:
              // 根据错误消息内容智能判断错误类型
              if (response.msg && response.msg.includes('身份证')) {
                errorIcon = '🆔';
                errorTitle = '身份证信息无效';
                suggestions = [
                  '请检查简历中的身份证号码是否正确',
                  '确保身份证号码为18位有效数字',
                  '检查身份证号码是否清晰可见'
                ];
              } else if (response.msg && response.msg.includes('锁定')) {
                errorIcon = '🔒';
                errorTitle = '学员已被锁定';
                suggestions = [
                  '该学员已在其他服务站注册',
                  '如有疑问，请联系相关服务站协调',
                  '或联系平台客服处理'
                ];
              } else if (response.msg && response.msg.includes('邮箱') && response.msg.includes('电话')) {
                errorIcon = '📞';
                errorTitle = '联系方式不完整';
                suggestions = [
                  '请在简历中填写有效的手机号码',
                  '请在简历中填写有效的邮箱地址',
                  '确保联系方式格式正确且清晰可见'
                ];
              } else if (response.msg && response.msg.includes('邮箱')) {
                errorIcon = '📧';
                errorTitle = '邮箱地址缺失';
                suggestions = [
                  '请在简历中填写有效的邮箱地址',
                  '确保邮箱格式正确（如：<EMAIL>）',
                  '邮箱地址应清晰可见，不要使用图片形式'
                ];
              } else if (response.msg && response.msg.includes('电话')) {
                errorIcon = '📱';
                errorTitle = '联系电话缺失';
                suggestions = [
                  '请在简历中填写有效的手机号码',
                  '确保手机号码为11位数字',
                  '联系电话应清晰可见，不要使用图片形式'
                ];
              } else if (response.msg && response.msg.includes('联系')) {
                errorIcon = '📞';
                errorTitle = '联系方式不完整';
                suggestions = [
                  '请在简历中填写有效的手机号码',
                  '请在简历中填写有效的邮箱地址',
                  '确保联系方式格式正确且清晰可见'
                ];
              }
              break;
          }
        } else {
          // 如果没有错误类型，根据消息内容智能判断
          if (response.msg && response.msg.includes('联系')) {
            errorIcon = '📞';
            errorTitle = '联系方式不完整';
            suggestions = [
              '请在简历中填写有效的手机号码',
              '请在简历中填写有效的邮箱地址',
              '确保联系方式格式正确且清晰可见'
            ];
          } else if (response.msg && response.msg.includes('身份证')) {
            errorIcon = '🆔';
            errorTitle = '身份证信息无效';
            suggestions = [
              '请检查简历中的身份证号码是否正确',
              '确保身份证号码为18位有效数字',
              '检查身份证号码是否清晰可见'
            ];
          } else if (response.msg && response.msg.includes('锁定')) {
            errorIcon = '🔒';
            errorTitle = '学员已被锁定';
            suggestions = [
              '该学员已在其他服务站注册',
              '如有疑问，请联系相关服务站协调',
              '或联系平台客服处理'
            ];
          }
        }

        // 创建错误详情弹窗
        const errorModal = document.createElement('div');
        errorModal.className = 'error-detail-modal';
        errorModal.innerHTML = `
          <div class="error-detail-content">
            <div class="error-header">
              <span class="error-icon">${errorIcon}</span>
              <h3>${errorTitle}</h3>
              <button class="error-close" onclick="closeErrorModal()">&times;</button>
            </div>
            <div class="error-body">
              <p class="error-message">${response.msg}</p>
              ${suggestions.length > 0 ? `
                <div class="error-suggestions">
                  <h4>💡 解决方案：</h4>
                  <ul>
                    ${suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                  </ul>
                </div>
              ` : ''}
              <div class="error-actions">
                ${response.template_url ? `
                  <a href="${response.template_url}"
                     download="应聘人员报名表（通用）.docx"
                     class="btn-download-template">
                    📥 下载官方模板
                  </a>
                ` : ''}
                <button class="btn-retry" onclick="closeErrorModal()">我知道了</button>
              </div>
            </div>
          </div>
        `;

        document.body.appendChild(errorModal);
        addErrorModalStyles();
      }

      // 添加错误弹窗样式
      function addErrorModalStyles() {
        if (!document.querySelector('#error-modal-styles')) {
          const styles = document.createElement('style');
          styles.id = 'error-modal-styles';
          styles.textContent = `
            .error-detail-modal {
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.6);
              display: flex;
              justify-content: center;
              align-items: center;
              z-index: 10000;
              animation: fadeIn 0.3s ease;
            }
            .error-detail-content {
              background: white;
              border-radius: 12px;
              max-width: 500px;
              width: 90%;
              max-height: 80vh;
              overflow-y: auto;
              box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }
            .error-header {
              display: flex;
              align-items: center;
              padding: 20px;
              border-bottom: 1px solid #eee;
              background: #fff5f5;
              border-radius: 12px 12px 0 0;
            }
            .error-icon {
              font-size: 24px;
              margin-right: 10px;
            }
            .error-header h3 {
              flex: 1;
              margin: 0;
              color: #d73027;
              font-size: 18px;
            }
            .error-close {
              background: none;
              border: none;
              font-size: 24px;
              cursor: pointer;
              color: #999;
              padding: 0;
              width: 30px;
              height: 30px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .error-body {
              padding: 20px;
            }
            .error-message {
              font-size: 16px;
              color: #333;
              margin-bottom: 15px;
              line-height: 1.5;
            }
            .error-suggestions {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              margin-bottom: 20px;
            }
            .error-suggestions h4 {
              margin: 0 0 10px 0;
              color: #495057;
              font-size: 14px;
            }
            .error-suggestions ul {
              margin: 0;
              padding-left: 20px;
            }
            .error-suggestions li {
              margin-bottom: 8px;
              color: #6c757d;
              line-height: 1.4;
            }
            .error-actions {
              display: flex;
              gap: 10px;
              justify-content: flex-end;
            }
            .btn-download-template {
              background: #28a745;
              color: white;
              padding: 10px 20px;
              border-radius: 6px;
              text-decoration: none;
              font-size: 14px;
              transition: background 0.3s;
            }
            .btn-download-template:hover {
              background: #218838;
            }
            .btn-retry {
              background: #007bff;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 6px;
              cursor: pointer;
              font-size: 14px;
              transition: background 0.3s;
            }
            .btn-retry:hover {
              background: #0056b3;
            }
            @keyframes fadeIn {
              from { opacity: 0; }
              to { opacity: 1; }
            }
          `;
          document.head.appendChild(styles);
        }
      }

      // 显示重复文件错误弹窗
      function showDuplicateFileError(response) {
        // 移除之前的错误提示
        const existingError = document.querySelector('.error-detail-modal');
        if (existingError) {
          existingError.remove();
        }

        // 创建重复文件错误弹窗
        const errorModal = document.createElement('div');
        errorModal.className = 'error-detail-modal';
        errorModal.innerHTML = `
          <div class="error-detail-content">
            <div class="error-header">
              <span class="error-icon">⚠️</span>
              <h3>简历上传失败</h3>
              <button class="error-close" onclick="closeErrorModal()">&times;</button>
            </div>
            <div class="error-body">
              <p class="error-message">${response.msg}</p>
              <div class="error-suggestions">
                <h4>💡 解决方案：</h4>
                <ul>
                  <li>请检查是否之前已经上传过相同的简历文件</li>
                  <li>如需重新上传，请先删除之前的简历记录</li>
                  <li>或者选择其他不同的简历文件进行上传</li>
                </ul>
              </div>
              <div class="error-actions">
                <button class="btn-retry" onclick="closeErrorModal()">我知道了</button>
              </div>
            </div>
          </div>
        `;

        document.body.appendChild(errorModal);
        addErrorModalStyles();
      }

      // 关闭错误弹窗
      function closeErrorModal() {
        const errorModal = document.querySelector('.error-detail-modal');
        if (errorModal) {
          // 标记错误弹窗已被用户关闭
          errorModalClosed = true;

          errorModal.style.animation = 'fadeOut 0.3s ease';
          setTimeout(() => {
            errorModal.remove();

            // 立即隐藏进度条并重置状态，不等待5秒延迟
            hideMultiStageProgress();
            resetUploadStatus();
          }, 300);
        }
      }

      // 重置上传状态
      function resetUploadStatus(delay = 0) {
        setTimeout(() => {
          document.getElementById("file-input").value = "";
          document.getElementById("progress").style.width = "0%";
          document.getElementById("upload-success").style.display = "none";
          document.getElementById("j_content").style.display = "none";
          document.getElementById("stext").style.display = "block";
          document.getElementById("stext2").style.display = "block";
          document.getElementById("t2").style.display = "none";
          document.getElementById("t1").style.display = "block";

          // 重置多阶段进度
          hideMultiStageProgress();
          currentStageIndex = 0;

          // 重置所有阶段状态
          stages.forEach((stage) => {
            const stageElement = document.getElementById(stage.id);
            const iconElement = stageElement.querySelector('.stage-icon');
            const iconContent = stageElement.querySelector('.icon-content');

            iconElement.classList.remove('pending', 'active', 'completed', 'error');
            stageElement.classList.remove('pending', 'active', 'completed', 'error');
            iconElement.classList.add('pending');
            stageElement.classList.add('pending');
            iconContent.textContent = stage.icon;
          });

          // 重置进度线
          document.getElementById("stage-progress-line").style.width = '0%';

          // 重置全局变量
          selectedFile = null;
          isErrorState = false; // 重置错误状态
          clearAllStageTimers(); // 清除所有定时器
        }, delay);
      }
    </script>
    <div class="footernav">
      <div class="box">
        <ul>
          <li>
            <a href="javascript:void();" onclick="showModal()">
              <button class="add-btn" onclick="showModal()">+ 添加简历</button>
            </a>
          </li>
        </ul>
      </div>
    </div>
    <script type="text/javascript">
      var kwd = '{$kwd}';
      var url = "{:U('index/joblist')}"
      var sortShowTime = '{:$show_time}';
      var sortShowNum = '{:$show_num}';
      var type = '{$type}';
      var resumeType = '{$resume_type}';
      $(document).ready(function(){
      $(".tabs-hd02 ul li").click(function(){
        $(this).siblings().removeClass("on");
        $(this).addClass("on");
          var newType = $(this).attr('data-type');
          $(this).siblings().removeClass("on");
          $(this).addClass("on");
          if (newType != type) {
              window.location.href = url + '?kwd='+kwd+"&type="+newType+"&resume_type="+resumeType;
              return
          }
      });

      // 简历类型筛选功能
      $(".resume-type-btn").click(function(){
          var newResumeType = $(this).attr('data-type');
          $(this).siblings().removeClass("active");
          $(this).addClass("active");
          if (newResumeType != resumeType) {
              window.location.href = url + '?kwd='+kwd+"&type="+type+"&resume_type="+newResumeType;
              return
          }
      });


      $(".screen01 li").click(function(){
              var typeOnOrUp = 0;
              var dataType = $(this).attr('data-type');
              if($(this).is('.ondown')){
                  $(this).removeClass('ondown');
                  $(this).siblings().removeClass("ondown");
                  $(this).siblings().removeClass('onup');
                  $(this).addClass('onup');
                  typeOnOrUp = 1;
              } else {
                  $(this).removeClass('onup');
                  $(this).siblings().removeClass("ondown");
                  $(this).siblings().removeClass('onup');
                  $(this).addClass('ondown');
                  typeOnOrUp = 2;
              }
              if (dataType == 'sortShowTime') {
                  sortShowNum = 0;
                  sortShowTime = typeOnOrUp;
              } else {
                  sortShowTime = 0;
                  sortShowNum = typeOnOrUp;
              }
              reloadData();
              return false;
          });


          $('.js_kwd').blur(function () {
              var  new_kwd = $(this).val();
              if (new_kwd != kwd) {
                  window.location.href = url + '?kwd='+new_kwd+"&type="+type;
                  return
              }
          })

          var noticeSwiper = new Swiper('.noticeSwiper', {
              direction: 'vertical',
              loop: true,
              autoplay: {
                  delay: 3000,
                  disableOnInteraction: false,
              }
          })
          var noticeSwiper = new Swiper('.page0106Swiper', {
              pagination: {
                  el: ".swiper-pagination",
              },

              loop: true,
              autoplay: {
              delay: 3000,
              disableOnInteraction: false,
              }
          });

      });


          var page=1,pages=<?= (int)$page->Total_Pages ?>;

          function loadmore(){
              if(page<pages){
                  page+=1;
                  $.get('/index/joblist?p=' + page+'&kwd='+kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum, function(str){
                      $('#job').append(str);
                  }, 'html');
              } else if (page==pages) {
                  page+=1;
                  setTimeout("$('.container').append('<p class=\"nomore\">------ 没有了 ------</p>')", 500);
              }
          }
          $(window).scroll(function(){
              var scrollTop = $(this).scrollTop();     //滚动条距离顶部的高度
              var scrollHeight = $(document).height(); //当前页面的总高度
              var clientHeight = $(this).height();     //当前可视的页面高度

              if(scrollTop + clientHeight >= scrollHeight){
                  loadmore();
              }else if(scrollTop<=0){
              }
          });


       function baoming(jobId) {
          if (!jobId) {
            layer.open({
              content: '简历ID不存在',
              skin: 'msg',
              time: 2
            });
            return;
          }

          // 检查学员是否已报名
          var trainingOrderList = {:json_encode($trainingOrderList)};

          // 使用 some 方法进行比较，确保类型一致
          var isAlreadyRegistered = false;
          if (trainingOrderList && trainingOrderList.length > 0) {
            isAlreadyRegistered = trainingOrderList.some(function(id) {
              return String(id) === String(jobId);
            });
          }

          if (isAlreadyRegistered) {
            layer.open({
              content: '该学员已报名培训',
              skin: 'msg',
              time: 2
            });
            return;
          }

          // 获取学员简历信息
          $.ajax({
            url: "{:U('training/getUserJobInfo')}",
            type: 'GET',
            data: {user_job_id: jobId},
            dataType: 'json',
            success: function(res) {
              if (res.status == 1) {
                // 继续创建培训订单表单
                createTrainingOrderForm(jobId);
              } else {
                layer.open({
                  content: res.msg || '获取学员信息失败',
                  skin: 'msg',
                  time: 2
                });
              }
            },
            error: function() {
              layer.open({
                content: '网络错误，请重试',
                skin: 'msg',
                time: 2
              });
            }
          });
        }

        // 全局价格格式化函数
        function formatMoney(amount) {
          return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }

        function formatPriceToThousands(value) {
          if (!value || value === '') {
            return 0;
          }
          var num = parseInt(value);
          if (isNaN(num) || num <= 0) {
            return 0;
          }
          // 向下取整到千位（保留千位以上的数字，去除百位、十位、个位）
          return Math.floor(num / 1000) * 1000;
        }

        function showFormattedPriceHint(inputElement, originalValue, formattedValue) {
          var $inputGroup = $(inputElement).closest('.price-input-group');
          var $hint = $inputGroup.find('.price-format-hint');
          var $result = $inputGroup.find('.formatted-result');

          if (originalValue != formattedValue) {
            $hint.addClass('active');
            $result.text('格式化后：' + formatMoney(formattedValue) + ' 元').show();
            if (formattedValue === 0) {
              $result.addClass('warning').text('格式化后：0 元（原价格小于1000）');
            } else {
              $result.removeClass('warning');
            }
          } else {
            $hint.removeClass('active');
            $result.hide();
          }
        }

        function validateTrainingFeeWithFormattingJoblist(input) {
          var value = input.value;

          // 移除千分符和非数字字符，只保留数字
          var numericValue = value.replace(/[^\d]/g, '');

          if (numericValue === '' || numericValue === '0') {
            input.value = '';
            // 隐藏格式化提示
            var $inputGroup = $(input).closest('.price-input-group');
            $inputGroup.find('.price-format-hint').removeClass('active');
            $inputGroup.find('.formatted-result').hide();
            // 清空收益显示
            $('#profit_fee_amount').text('0.00 元');
            $('#profit_base_cost').text('0.00 元');
            $('#profit_platform_fee').text('0.00 元');
            $('#profit_station_profit').text('0.00 元');
            return;
          }

          // 移除前导零
          numericValue = numericValue.replace(/^0+/, '');
          if (numericValue === '') {
            input.value = '';
            return;
          }

          // 转换为整数
          var intValue = parseInt(numericValue);
          if (isNaN(intValue) || intValue <= 0) {
            input.setCustomValidity('请输入正整数');
            input.style.borderColor = '#dc3545';
            return;
          }

          // 实时添加千分符显示
          var formattedDisplay = formatMoney(intValue);

          // 保存光标位置
          var cursorPosition = input.selectionStart;
          var oldLength = input.value.length;

          // 更新输入框值为千分符格式
          input.value = formattedDisplay;

          // 恢复光标位置（考虑千分符的影响）
          var newLength = input.value.length;
          var lengthDiff = newLength - oldLength;
          var newCursorPosition = cursorPosition + lengthDiff;

          // 确保光标位置在有效范围内
          if (newCursorPosition < 0) newCursorPosition = 0;
          if (newCursorPosition > newLength) newCursorPosition = newLength;

          // 设置光标位置
          setTimeout(function() {
            input.setSelectionRange(newCursorPosition, newCursorPosition);
          }, 0);

          // 显示格式化预览
          var formattedValue = formatPriceToThousands(intValue);
          showFormattedPriceHint(input, intValue, formattedValue);

          // 验证费用范围
          var minFee = parseFloat($(input).attr('min')) || 0;
          var maxFee = parseFloat($(input).attr('max')) || 0;

          if (minFee > 0 && formattedValue < minFee) {
            input.setCustomValidity('格式化后的报名费不得低于最低报价（' + formatMoney(minFee) + '元）');
            input.style.borderColor = '#dc3545';
            return;
          }

          if (maxFee > 0 && formattedValue > maxFee) {
            input.setCustomValidity('格式化后的报名费不得超过最高报价（' + formatMoney(maxFee) + '元）');
            input.style.borderColor = '#dc3545';
            return;
          }

          input.setCustomValidity('');
          input.style.borderColor = '#28a745';

          // 实时计算收益（使用格式化后的值）
          var postId = $('#modal_post_id').val();
          if (postId && formattedValue > 0) {
            calculateProfitJoblist(postId, formattedValue);
          }
        }

        function applyTrainingFeeFormattingJoblist(input) {
          if (input.value && input.value !== '') {
            var originalValue = parseInt(input.value.replace(/[^\d]/g, '')) || 0;
            var formattedValue = formatPriceToThousands(originalValue);

            if (formattedValue > 0) {
              input.value = formatMoney(formattedValue);
            } else {
              input.value = '';
            }

            // 重新触发验证
            validateTrainingFeeWithFormattingJoblist(input);
          }
        }

        // 收益计算函数
        function calculateProfitJoblist(postId, feeAmount) {
          $.ajax({
            url: "{:U('training/getPostProfitDetails')}",
            type: 'GET',
            data: {
              post_id: postId,
              fee_amount: feeAmount
            },
            dataType: 'json',
            success: function(res) {
              if (res.status == 1 && res.data.profit_details) {
                var profit = res.data.profit_details;
                $('#profit_fee_amount').text(formatMoney(profit.fee_amount) + ' 元');
                $('#profit_base_cost').text(formatMoney(profit.base_cost) + ' 元');
                $('#profit_platform_fee').text(formatMoney(profit.platform_fee) + ' 元');
                $('#profit_station_profit').text(formatMoney(profit.station_profit) + ' 元');
              }
            },
            error: function() {
              console.log('计算收益失败');
            }
          });
        }

        // 创建培训订单表单
        function createTrainingOrderForm(jobId) {

          // 先通过AJAX获取表单数据
          $.ajax({
            url: "{:U('training/getFormData')}",
            type: 'GET',
            dataType: 'json',
            success: function(res) {
              if (res.status == 1) {

                // 创建弹窗内容
                var modalContent = '<div class="training-form" style="padding: 20px; background-color: #fff;">';
                modalContent += '<form id="trainingForm">';

                // 隐藏字段 - 简历ID
                modalContent += '<input type="hidden" id="modal_job_id" value="' + jobId + '">';

                // 学员信息 - 只读，因为是从简历中获取的
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-user" style="margin-right: 5px;"></i>学员：</label>';
                modalContent += '<select name="user_id" id="modal_user_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required disabled>';

                // 根据jobId过滤出对应的学员
                var selectedUser = null;
                for (var i = 0; i < res.data.users.length; i++) {
                  if (res.data.users[i].id == jobId) {
                    selectedUser = res.data.users[i];
                    modalContent += '<option value="' + res.data.users[i].id + '" selected>' +
                                    res.data.users[i].realname + ' (' + res.data.users[i].mobile + ')' +
                                    '</option>';
                    break;
                  }
                }

                // 如果没有找到对应的学员，显示提示信息并继续创建表单（后端会自动创建用户）
                if (!selectedUser) {
                  modalContent += '<option value="' + jobId + '" selected>系统将自动创建用户记录</option>';
                }

                modalContent += '</select>';
                modalContent += '</div>';

                // 培训项目
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-project" style="margin-right: 5px;"></i>培训项目：</label>';
                modalContent += '<select name="project_id" id="modal_project_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required>';
                modalContent += '<option value="">请选择培训项目</option>';

                // 添加项目选项
                for (var i = 0; i < res.data.projects.length; i++) {
                  modalContent += '<option value="' + res.data.projects[i].id + '">' +
                                  res.data.projects[i].name +
                                  '</option>';
                }

                modalContent += '</select>';
                modalContent += '</div>';

                // 培训岗位
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-tag" style="margin-right: 5px;"></i>培训岗位：</label>';
                modalContent += '<select name="post_id" id="modal_post_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required>';
                modalContent += '<option value="">请先选择培训项目</option>';
                modalContent += '</select>';
                modalContent += '</div>';

                // 学员学历和学员专业字段已隐藏

                // 基准成本价显示（仅服务站用户显示）
                <if condition="$serviceStationRow['zsb_type'] neq 2">
                // 岗位价格区间显示（仅服务站用户）
                modalContent += '<div class="form-item">';
                modalContent += '<label><i class="iconfont icon-money" style="margin-right: 5px;"></i>岗位价格区间：</label>';
                modalContent += '<div id="modal_price_range" class="price-range">请先选择培训岗位</div>';
                modalContent += '</div>';
                </if>

                // 基准成本价显示
                modalContent += '<div class="form-item">';
                modalContent += '<div id="modal_base_cost_info" class="base-cost-info" style="display: none;">';
                modalContent += '<strong>基准成本价：</strong><span id="modal_base_cost">0</span> 元';
                modalContent += '</div>';
                modalContent += '</div>';

                // 报名费输入（根据用户类型显示不同界面）
                <if condition="$serviceStationRow['zsb_type'] eq 2">
                // 招就办用户：显示固定报名费
                modalContent += '<div class="form-item" id="zsb_fee_display_section" style="display: none;">';
                modalContent += '<label><i class="iconfont icon-check" style="margin-right: 5px;"></i>报名费：</label>';
                modalContent += '<div class="zsb-fee-display" style="padding: 12px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; font-weight: bold; color: #07c160;">';
                modalContent += '<span id="zsb_fee_amount">请先选择培训岗位</span>';
                modalContent += '</div>';
                modalContent += '<div style="font-size: 12px; color: #666; margin-top: 5px;">报名费由招就办价格配置自动确定</div>';
                modalContent += '</div>';
                // 隐藏的输入框，用于提交数据
                modalContent += '<input type="hidden" id="modal_fee_input" value="">';
                <else />
                // 服务站用户：显示输入框
                modalContent += '<div class="form-item" id="fee_input_section">';
                modalContent += '<label><i class="iconfont icon-check" style="margin-right: 5px;"></i>输入报名费：</label>';
                modalContent += '<div class="price-input-group">';
                modalContent += '<input type="text" id="modal_fee_input" class="form-control" placeholder="请输入报名费（元）" style="width: 100%; height: 40px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 14px; color: #333;">';
                modalContent += '<div class="price-format-hint">价格将自动调整为千的整数倍（如：12345 → 12000）</div>';
                modalContent += '<div class="formatted-result" style="display: none;"></div>';
                modalContent += '<div id="fee_range_hint" style="font-size: 12px; color: #666; margin-top: 5px;">请先选择培训岗位</div>';
                modalContent += '</div>';
                modalContent += '</div>';
                </if>

                // 收益显示区域（根据用户类型显示不同内容）
                <if condition="$serviceStationRow['zsb_type'] eq 2">
                // 招就办用户：招就办收益显示（样式与报名费一致）
                modalContent += '<div class="form-item" id="modal_zsb_profit" style="display: none;">';
                modalContent += '<label><i class="iconfont icon-check" style="margin-right: 5px;"></i>招就办收益：</label>';
                modalContent += '<div class="zsb-commission-display" style="padding: 12px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; font-weight: bold; color: #07c160;">';
                modalContent += '<span id="zsb_commission_amount">0.00 元</span>';
                modalContent += '</div>';
                modalContent += '</div>';
                <else />
                // 服务站用户：完整的收益计算
                modalContent += '<div class="profit-calculation" id="modal_profit_calculation" style="display: none;">';
                modalContent += '<h4>收益计算</h4>';
                modalContent += '<div class="formula-item">平台服务费 = max(0, (报名费 - 最低报价) × <span id="modal_platform_rate">30</span>%)</div>';
                modalContent += '<div class="formula-item">服务站收益 = 报名费 - 基准成本价 - 平台服务费</div>';
                modalContent += '<div class="profit-details" id="modal_profit_details">';
                modalContent += '<div class="profit-row"><span class="profit-label">报名费：</span><span class="profit-value" id="profit_fee_amount">0.00 元</span></div>';
                modalContent += '<div class="profit-row"><span class="profit-label">基准成本价：</span><span class="profit-value" id="profit_base_cost">0.00 元</span></div>';
                modalContent += '<div class="profit-row"><span class="profit-label">平台服务费：</span><span class="profit-value" id="profit_platform_fee">0.00 元</span></div>';
                modalContent += '<div class="profit-row"><span class="profit-label">服务站收益：</span><span class="profit-value" id="profit_station_profit">0.00 元</span></div>';
                modalContent += '</div>';
                modalContent += '</div>';
                </if>

                modalContent += '</form>';
                modalContent += '</div>';

                // 打开弹窗
                layer.open({
                  type: 1,
                  title: '<span style="font-weight: bold; font-size: 16px;">创建培训订单</span>',
                  area: ['480px', 'auto'], // 固定宽度，避免超宽问题
                  maxHeight: '80vh', // 限制最大高度
                  skin: 'layui-layer-molv', // 使用墨绿色皮肤
                  content: modalContent, // 直接使用我们构建的HTML内容
                  btn: ['提交', '取消'],
                  btnAlign: 'c', // 按钮居中
                  success: function(layero) {
                    // 添加样式到弹窗
                    $(layero).find('.layui-layer-title').css({
                      'background-color': '#07c160',
                      'color': '#fff',
                      'border': 'none',
                      'height': '50px',
                      'line-height': '50px'
                    });

                    $(layero).find('.layui-layer-btn a').css({
                      'border-radius': '4px',
                      'height': '38px',
                      'line-height': '38px',
                      'padding': '0 18px',
                      'font-size': '15px'
                    });

                    $(layero).find('.layui-layer-btn .layui-layer-btn0').css({
                      'border-color': '#07c160',
                      'background-color': '#07c160',
                      'color': '#fff'
                    });

                    $(layero).find('.layui-layer-btn .layui-layer-btn1').css({
                      'border-color': '#ddd',
                      'background-color': '#f7f7f7',
                      'color': '#333'
                    });



                    // 学员学历和专业信息设置已移除

                    // 项目选择变化时，获取对应的岗位列表
                    $('#modal_project_id').change(function() {
                      var projectId = $(this).val();
                      if (projectId) {
                        $.ajax({
                          url: "{:U('training/getProjectPosts')}",
                          type: 'GET',
                          data: {project_id: projectId},
                          dataType: 'json',
                          success: function(res) {
                            if (res.status == 1 && res.data && res.data.length > 0) {
                              var posts = res.data;
                              var options = '<option value="">请选择培训岗位</option>';
                              for (var i = 0; i < posts.length; i++) {
                                options += '<option value="' + posts[i].id + '" ' +
                                  'data-service-price="' + posts[i].service_price_formatted + '" ' +
                                  'data-service-price-in-cents="' + posts[i].service_price_in_cents + '" ' +
                                  'data-service-price-text="' + posts[i].service_price_text + '" ' +
                                  'data-max-price="' + posts[i].max_price_formatted + '" ' +
                                  'data-max-price-in-cents="' + posts[i].max_price_in_cents + '" ' +
                                  'data-max-price-text="' + posts[i].max_price_text + '" ' +
                                  'data-price="' + posts[i].service_price_formatted + '" ' +
                                  'data-price-in-cents="' + posts[i].service_price_in_cents + '">' +
                                  posts[i].name + '</option>';
                              }
                              $('#modal_post_id').html(options);
                            } else {
                              $('#modal_post_id').html('<option value="">无</option>');
                              layer.open({
                                content: res.msg || '没有找到相关岗位',
                                skin: 'msg',
                                time: 2
                              });
                            }
                          },
                          error: function() {
                            layer.open({
                              content: '网络错误，请重试',
                              skin: 'msg',
                              time: 2
                            });
                          }
                        });
                      } else {
                        $('#modal_post_id').html('<option value="">请先选择培训项目</option>');
                        <if condition="$serviceStationRow['zsb_type'] neq 2">
                        $('#modal_price_range').text('请先选择培训项目');
                        $('#modal_base_cost_info').hide();
                        $('#modal_profit_calculation').hide();
                        $('#modal_fee_input').val('');
                        $('#fee_range_hint').text('请先选择培训项目');
                        </if>
                      }
                    });

                    // 学员学历和专业信息获取逻辑已移除

                    // 岗位选择变化事件
                    $('#modal_post_id').change(function() {
                      var postId = $(this).val(); // 获取选中的岗位ID

                      // 只有选择了岗位才获取详细信息
                      if (postId) {
                        <if condition="$serviceStationRow['zsb_type'] eq 2">
                        // 招就办用户：获取招就办价格配置
                        console.log('招就办用户调用价格API，岗位ID:', postId);
                        $.ajax({
                          url: "{:U('training/getZsbPostPrice')}",
                          type: 'GET',
                          data: {post_id: postId},
                          dataType: 'json',
                          success: function(res) {
                            console.log('API调用成功:', res);
                            if (res.status == 1) {
                              var data = res.data;

                              // 显示招就办费用
                              $('#zsb_fee_amount').text(data.sale_price_formatted + ' 元');
                              $('#zsb_fee_display_section').show();

                              // 设置隐藏输入框的值（用于提交）
                              $('#modal_fee_input').val(data.sale_price_formatted);

                              // 显示招就办收益
                              $('#zsb_commission_amount').text(data.commission_formatted + ' 元');
                              $('#modal_zsb_profit').show();

                            } else {
                              $('#zsb_fee_amount').text('未配置价格');
                              $('#zsb_fee_display_section').show();
                              $('#modal_zsb_profit').hide();
                            }
                          },
                          error: function(xhr, status, error) {
                            console.error('获取招就办价格失败:', error);
                            console.error('响应状态:', xhr.status);
                            console.error('响应内容:', xhr.responseText);
                            $('#zsb_fee_amount').text('获取价格失败');
                            $('#zsb_fee_display_section').show();
                            $('#modal_zsb_profit').hide();
                          }
                        });
                        <else />
                        // 服务站用户：获取岗位收益计算详情
                        $.ajax({
                          url: "{:U('training/getPostProfitDetails')}",
                          type: 'GET',
                          data: {post_id: postId},
                          dataType: 'json',
                          success: function(res) {
                            if (res.status == 1) {
                              var data = res.data;

                              // 更新价格区间显示
                              $('#modal_price_range').text(data.price_range);

                              // 显示基准成本价
                              $('#modal_base_cost').text(data.base_cost);
                              $('#modal_base_cost_info').show();

                              // 更新输入框的范围提示和属性（使用基准成本价作为最低值）
                              $('#modal_fee_input').attr('min', data.min_fee);
                              $('#modal_fee_input').attr('max', data.max_fee);
                              $('#fee_range_hint').text('报名费范围：' + data.fee_range);

                              // 更新平台费率显示
                              $('#modal_platform_rate').text(data.platform_rate_percent);

                              // 设置默认值为最低限额（格式化后的值）
                              var formattedMinFee = formatPriceToThousands(data.min_fee);
                              $('#modal_fee_input').val(formatMoney(formattedMinFee));

                              // 显示收益计算区域
                              $('#modal_profit_calculation').show();

                              // 立即计算一次收益（使用格式化后的最低限额）
                              calculateProfitJoblist(postId, formattedMinFee);

                            } else {
                              $('#fee_range_hint').text('无法获取岗位信息');
                              $('#modal_profit_calculation').hide();
                              $('#modal_base_cost_info').hide();
                            }
                          },
                          error: function() {
                            $('#fee_range_hint').text('获取岗位信息失败');
                            $('#modal_profit_calculation').hide();
                            $('#modal_base_cost_info').hide();
                          }
                        });
                        </if>
                      } else {
                        // 没有选择岗位时，清空所有显示
                        <if condition="$serviceStationRow['zsb_type'] eq 2">
                        $('#zsb_fee_amount').text('请先选择培训岗位');
                        $('#zsb_fee_display_section').hide();
                        $('#modal_zsb_profit').hide();
                        <else />
                        $('#modal_price_range').text('请先选择培训岗位');
                        $('#modal_base_cost_info').hide();
                        $('#modal_profit_calculation').hide();
                        $('#modal_fee_input').val('');
                        $('#fee_range_hint').text('请先选择培训岗位');
                        </if>
                      }
                    });

                    // 报名费输入变化事件 - 实时格式化和计算收益（仅服务站用户）
                    <if condition="$serviceStationRow['zsb_type'] neq 2">
                    $('#modal_fee_input').on('input', function() {
                      validateTrainingFeeWithFormattingJoblist(this);
                    });

                    // 报名费输入框失去焦点时应用格式化
                    $('#modal_fee_input').on('blur', function() {
                      applyTrainingFeeFormattingJoblist(this);
                    });
                    </if>


                  },
                  yes: function(index) {
                    try {
                      // 获取表单数据
                      var userJobId = $('#modal_job_id').val();
                      var projectId = $('#modal_project_id').val();
                      var postId = $('#modal_post_id').val();



                      // 表单验证
                      if (!userJobId) {
                        layer.open({
                          content: '简历信息错误',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }
                      if (!projectId) {
                        layer.open({
                          content: '请选择培训项目',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }
                      if (!postId) {
                        layer.open({
                          content: '请选择培训岗位',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }

                      // 获取费用金额
                      <if condition="$serviceStationRow['zsb_type'] eq 2">
                      // 招就办用户：直接从隐藏输入框获取价格
                      var feeAmount = parseFloat($('#modal_fee_input').val()) || 0;

                      if (!feeAmount || feeAmount <= 0) {
                        layer.open({
                          content: '请先选择培训岗位以获取价格配置',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }
                      <else />
                      // 服务站用户：从输入框中获取格式化后的价格
                      var inputValue = $('#modal_fee_input').val().replace(/[^\d]/g, ''); // 移除千分符
                      var originalAmount = parseInt(inputValue) || 0;
                      var feeAmount = formatPriceToThousands(originalAmount); // 使用格式化后的值

                      if (!feeAmount || feeAmount <= 0) {
                        layer.open({
                          content: '请输入有效的报名费',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }

                      // 验证费用范围（使用格式化后的值）
                      var minFee = parseFloat($('#modal_fee_input').attr('min'));
                      var maxFee = parseFloat($('#modal_fee_input').attr('max'));
                      if (feeAmount < minFee || feeAmount > maxFee) {
                        layer.open({
                          content: '报名费必须在 ' + formatMoney(minFee) + ' - ' + formatMoney(maxFee) + ' 元之间（最低报价~最高报价）',
                          skin: 'msg',
                          time: 3
                        });
                        return false;
                      }
                      </if>


                      // 提交表单
                      $.ajax({
                        url: "{:U('training/create')}", // 使用ThinkPHP U函数生成正确的URL
                        type: 'POST',
                        data: {
                          user_id: userJobId,
                          post_id: postId,
                          fee_amount: feeAmount
                        },
                        dataType: 'json',
                        success: function(res) {
                          if (res.status == 1) {
                            layer.open({
                              content: '报名成功',
                              skin: 'msg',
                              time: 2
                            });
                            setTimeout(function() {
                              window.location.href = res.url || "{:U('training/index')}";
                            }, 1000);
                          } else {
                            layer.open({
                              content: res.info || '报名失败',
                              skin: 'msg',
                              time: 2
                            });
                          }
                        },
                        error: function(xhr, status, error) {
                          layer.open({
                            content: '网络错误，请重试',
                            skin: 'msg',
                            time: 2
                          });
                        }
                      });
                    } catch (e) {
                      console.error('表单提交过程中出错:', e);
                      layer.open({
                        content: '表单提交出错',
                        skin: 'msg',
                        time: 2
                      });
                      return false;
                    }
                  }
                });
              } else {
                layer.open({
                  content: '获取表单数据失败',
                  skin: 'msg',
                  time: 2
                });
              }
            },
            error: function() {
              layer.open({
                content: '网络错误，请重试',
                skin: 'msg',
                time: 2
              });
            }
          });
      }
    </script>
  </body>
</html>
