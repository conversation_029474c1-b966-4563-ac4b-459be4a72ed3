<?php

namespace Prime\Controller;

use Common\Controller\PrimeController;

class MomentController extends PrimeController {
    // 列表页
    public function index(){
        // 定义搜索关键字
        $c_kw = [
            'content' => '内容',
        ];
        
        // 定义状态列表
        $statusList = [
            '0' => ['text' => '未启用'],
            '1' => ['text' => '已启用'],
        ];
        
        // 定义审核状态列表
        $reviewStatusList = [
            '0' => ['text' => '待审核'],
            '1' => ['text' => '已通过'],
            '2' => ['text' => '修改通过'],
        ];
        
        // 初始化GET参数，避免未定义索引错误
        $_get = [
            'kw' => I("get.kw", ''),
            'val' => I("get.val", ''),
            'status' => I("get.status", ''),
            'project_id' => I("get.project_id", 0),
            'post_id' => I("get.post_id", 0),
            'review_status' => I("get.review_status", ''),
        ];
        
        // 搜索条件
        $where = [];
        $s_kw = $_get['kw'];
        $s_val = $_get['val'];
        $s_status = $_get['status'];
        $s_project_id = $_get['project_id'];
        $s_post_id = $_get['post_id'];
        $s_review_status = $_get['review_status'];
        
        if ($s_status !== '') $where['status'] = $s_status;
        if ($s_project_id) $where['project_id'] = $s_project_id;
        if ($s_post_id) $where['post_id'] = $s_post_id;
        if ($s_review_status !== '') $where['review_status'] = $s_review_status;
        
        if ($s_kw && $s_val !== '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['content'])) {
                $where[$s_kw] = ['like', "%$s_val%"];
            } else {
                $where[$s_kw] = $s_val;
            }
        }
        
        // 获取每页显示条数，默认10条，支持10/20/50选项
        $pageSize = I('get.psz', 10, 'intval');
        // 确保页面大小只能是10, 20, 50
        if (!in_array($pageSize, [10, 20, 50])) {
            $pageSize = 10;
        }
        
        // 获取数据总数
        $count = D('Moment')->where($where)->count();
        
        // 创建分页对象，使用用户选择的页面大小
        $page = $this->page($count, $pageSize);
        
        // 查询数据，带分页限制
        $list = D('Moment')->where($where)
            ->order('create_time DESC')
            ->limit($page->firstRow . ',' . $page->listRows)
            ->select();
        
        // 获取项目和岗位列表
        $projectList = D("Project")->getField('id,name', true);
        $postList = D("Project_post")->getField('id,job_name', true);
        
        // 获取所有用户列表（管理员）
        $userList = M('Users')->getField('id,username', true);
        
        // 处理列表数据，添加创建者和审核者信息
        foreach ($list as &$item) {
            $item['creator_name'] = isset($userList[$item['creator_id']]) ? $userList[$item['creator_id']] : '未知';
            $item['reviewer_name'] = isset($userList[$item['reviewer_id']]) ? $userList[$item['reviewer_id']] : '未审核';
            $item['review_status_text'] = isset($reviewStatusList[$item['review_status']]['text']) ? $reviewStatusList[$item['review_status']]['text'] : '未知';
            // 添加更新者信息
            $item['updater_name'] = ($item['update_by'] > 0 && isset($userList[$item['update_by']])) ? $userList[$item['update_by']] : '未更新';
        }
        
        // 分配变量到模板
        $this->assign('c_kw', $c_kw);
        $this->assign('statusList', $statusList);
        $this->assign('reviewStatusList', $reviewStatusList);
        $this->assign('_get', $_get);  // 使用我们初始化过的$_get数组
        $this->assign('projectList', $projectList);
        $this->assign('postList', $postList);
        $this->assign('list', $list);
        $this->assign('page', $page->show());
        $this->assign('pageSize', $pageSize); // 将当前页面大小传递给视图
        $this->display();
    }

    // 添加/编辑页面
    public function edit(){
        $obj = D("Moment"); // Initialize $obj at the beginning of the method

        if(IS_POST){
            $data = I('post.');
            
            // 处理图片上传
            $upload = new \Think\Upload();
            $upload->maxSize   = 10*1024*1024;
            $upload->exts     = array('jpg', 'gif', 'png', 'jpeg');
            $upload->rootPath  = '/';
            $upload->savePath = 'Moments/';
            
            $info = $upload->upload();
            if($info){
                for($i=1;$i<=9;$i++){
                    if(isset($info['img'.$i])){
                        $data['img'.$i] = $info['img'.$i]['savepath'].$info['img'.$i]['savename'];
                    }
                }
            }
            
            // 根据是否有ID决定是添加还是更新
            if(isset($data['id']) && $data['id'] > 0){
                // 更新操作
                // 获取当前管理员ID
                $admin_id = intval(session('admin_id'));
                if($admin_id <= 0) {
                    $this->error('登录信息失效，请重新登录');
                }
                
                // 设置更新相关信息
                $data['update_by'] = $admin_id;          // 更新者ID
                $data['update_time'] = time();           // 更新时间
                $data['review_status'] = 0;              // 重置为待审核状态
                $data['status'] = 0;                     // 自动下架
                
                $result = $obj->save($data);
                if($result !== false){
                    $this->success('更新成功', U('index'));
                }else{
                    $this->error('更新失败');
                }
            }else{
                // 添加操作
                $data['create_time'] = time();
                // 设置默认状态为下架(0)
                $data['status'] = 0;
                // 记录创建者ID - 确保有值并且是整数
                $admin_id = intval(session('admin_id'));
                if($admin_id <= 0) {
                    $this->error('登录信息失效，请重新登录');
                }
                $data['creator_id'] = $admin_id;
                // 设置审核状态为未审核
                $data['review_status'] = 0;
                
                if($obj->add($data)){
                    $this->success('添加成功', U('index'));
                }else{
                    $this->error('添加失败：'.$obj->getError());
                }
            }
        }else{
            $id = intval(I('get.id')); // Ensure id is fetched for GET requests too, if needed for $row assignment
            if ($id) {
                $row = $obj->where("id=".$id)->find();
                if (!$row) $this->error('参数错误');
                $row['content'] = htmlspecialchars_decode($row['content']);
                $this->assign('row',$row);
            }
            
            // Fetch projects: (status=1 OR name='通用')
            $projectWhere = array();
            // Assuming 'name' is the column for the project name and '通用' is the exact value.
            // Assuming 'status' is the column for status and 1 means active.
            $projectWhere['_complex'] = array(
                'status' => 1,
                'name' => '通用', // If project name column is different, change here
                '_logic' => 'OR'
            );
            $selectableProjects = D("Project")->where($projectWhere)->getField('id,name', true);
            $this->assign('projects', $selectableProjects);
            
            $roles = M('Role')->where("status=1")->order("id desc")->select();
            $this->assign("roles", $roles);
            $this->display();
        }
    }

    // 获取岗位接口
    public function getPosts(){
        $project_id = I('get.project_id');
        
        $where = array();
        $where['project_id'] = $project_id;
        
        // Assuming 'job_name' is the column for the post name and '通用' is the exact value.
        // Assuming 'status' is the column for status and 1 means active.
        $where['_complex'] = array(
            'status' => 1,
            'job_name' => '通用',
            '_logic' => 'OR'
        );

        $posts = D('Project_post')->where($where)->select();
        
        $this->ajaxReturn($posts);
    }

    /**
 * 删除素材（安全增强版）
 */
public function del() {
    $id = I('get.id/d', 0); // 强制转为整数 + 默认值0
    if ($id <= 0) {
        $this->error('参数错误'); // 直接拦截非法ID
    }

    $obj = D("Moment");
    // 前置验证（带共享锁，防止并发修改）
    $row = $obj->where(['id' => $id])->lock(true)->find();
    if (!$row) {
        $this->error('记录不存在或已被删除');
    }

    // 启用事务（确保在数据有效后开启）
    $obj->startTrans();
    try {
        $deleteResult = $obj->where(['id' => $id])->delete();
        
        // 严格判断删除结果（受影响行数应=1）
        if ($deleteResult !== 1) {
            throw new \Exception('删除操作未生效'); 
        }

        $obj->commit();
        $this->success('删除成功');
    } catch (\Exception $e) {
        $obj->rollback();
        // Log::record('删除失败：'.$e->getMessage(), 'error'); // 实际开发需记录日志
        $this->error('删除失败：'.$e->getMessage()); 
    }
}


    /**
     * 状态变更
     */
    public function cgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("Moment");
        if (!array_key_exists($status, $obj->status)) $this->error('参数错误 ');
        
        // 获取当前记录
        $moment = $obj->where(['id' => $id])->find();
        if (!$moment) {
            $this->error('记录不存在');
        }
        
        // 检查审核状态，只有审核通过或修改通过的才能上架
        if ($moment['review_status'] != 1 && $moment['review_status'] != 2) {
            $this->error('只有审核通过或修改通过的素材才能修改状态');
        }
        
        $obj->save(['id' => $id, 'status' => $status]);
        $this->success('修改成功');
    }

    /**
     * 审核素材
     */
    public function review() {
        if (!IS_POST) {
            // 手动设置当前菜单，使其与朋友圈素材管理菜单关联
            $menu = D("Menu")->where(['model' => 'Moment', 'action' => 'index'])->find();
            if ($menu) {
                $cur_menu = D("Menu")->curMenu(1);
                $cur_menu['id'] = $menu['id'];
                $cur_menu['parentid'] = $menu['parentid'];
                $cur_menu['boot_id'] = D("Menu")->getBoot($menu['parentid']);
                $this->assign("cur_menu", $cur_menu);
            }
            
            $id = I('get.id/d', 0);
            if ($id <= 0) {
                $this->error('参数错误');
            }
            
            $obj = D("Moment");
            $row = $obj->where(['id' => $id])->find();
            if (!$row) {
                $this->error('记录不存在');
            }
            
            // 获取当前管理员ID
            $currentUserId = session('admin_id');
            
            // 修改权限检查逻辑
            if ($row['update_by'] > 0) {
                // 如果素材已被更新，则更新者不能审核
                if ($row['update_by'] == $currentUserId) {
                    $this->error('您不能审核自己最后更新的素材，请联系其他管理员进行审核');
                }
            } else {
                // 如果素材未被更新，则创建者不能审核
                if ($row['creator_id'] == $currentUserId) {
                    $this->error('您不能审核自己创建的素材，请联系其他管理员进行审核');
                }
            }
            
            // 获取创建者信息
            $creator = M('Users')->where(['id' => $row['creator_id']])->getField('username');
            $row['creator_name'] = $creator ? $creator : '未知';
            
            // 获取当前审核者信息 - 修正为使用admin_id
            $currentUser = M('Users')->where(['id' => $currentUserId])->getField('username');
            $this->assign('current_reviewer', $currentUser ? $currentUser : '未知');
            $this->assign('current_reviewer_id', $currentUserId);
            
            // 获取项目和岗位信息
            $projectList = D("Project")->getField('id,name', true);
            $postList = D("Project_post")->getField('id,job_name', true);
            $this->assign('projectList', $projectList);
            $this->assign('postList', $postList);
            
            $this->assign('row', $row);
            $this->display();
        } else {
            $data = I('post.');
            $id = intval($data['id']);
            $review_status = intval($data['review_status']);
            $currentUserId = session('admin_id'); // 修正为使用admin_id
            
            if ($id <= 0 || !in_array($review_status, [1, 2])) {
                $this->error('参数错误');
            }
            
            $obj = D("Moment");
            $moment = $obj->where(['id' => $id])->find();
            if (!$moment) {
                $this->error('记录不存在');
            }
            
            // 修改权限检查逻辑
            if ($moment['update_by'] > 0) {
                // 如果素材已被更新，则更新者不能审核
                if ($moment['update_by'] == $currentUserId) {
                    $this->error('您不能审核自己最后更新的素材，请联系其他管理员进行审核');
                }
            } else {
                // 如果素材未被更新，则创建者不能审核
                if ($moment['creator_id'] == $currentUserId) {
                    $this->error('您不能审核自己创建的素材，请联系其他管理员进行审核');
                }
            }
            
            // 更新审核信息
            $updateData = [
                'id' => $id,
                'reviewer_id' => $currentUserId,
                'review_time' => time(),
                'review_status' => $review_status
            ];
            
            // 如果选择通过或修改通过，则更新状态为上架
            if ($review_status == 1 || $review_status == 2) {
                $updateData['status'] = 1;
            }
            
            // 如果是"修改通过"，还需要处理内容和图片的更新
            if ($review_status == 2 && isset($data['content'])) {
                // 更新内容
                $updateData['content'] = $data['content'];
                
                // 处理图片上传
                $info = false; // Initialize $info
                if (!empty($_FILES)) {
                    $momentFilesExist = false;
                    for ($k=1; $k<=9; $k++) {
                        if (isset($_FILES['img'.$k]) && $_FILES['img'.$k]['error'] == UPLOAD_ERR_OK && !empty($_FILES['img'.$k]['tmp_name'])) {
                            $momentFilesExist = true;
                            break;
                        }
                    }

                    if ($momentFilesExist) {
                        $upload = new \Think\Upload();
                        $upload->maxSize   = 10*1024*1024;
                        $upload->exts      = array('jpg', 'gif', 'png', 'jpeg');
                        $upload->rootPath  = '/';
                        $upload->savePath  = 'Moments/';
                        $uploadResult = $upload->upload();
                        if ($uploadResult) {
                            $info = $uploadResult;
                        } else {
                            // 可以选择性地记录 $upload->getError()
                            // $this->error($upload->getError()); // 或者以其他方式处理错误
                        }
                    }
                }
                
                for ($i=1; $i<=9; $i++) {
                    $imgKey = 'img'.$i;
                    if (isset($info[$imgKey]) && !empty($info[$imgKey]['savename'])) {
                        // 条件1: 新图片在审核阶段被成功上传，使用这个新图片
                        $updateData[$imgKey] = $info[$imgKey]['savepath'].$info[$imgKey]['savename'];
                    } elseif (array_key_exists($imgKey, $data)) {
                        // 条件2: 没有新图片在审核阶段上传，使用表单提交的 $data[$imgKey] 的值。
                        // 这个值是 edit() 之后（或更早之前）的图片路径。
                        // 如果 $data[$imgKey] 是空字符串 (例如，如果字段允许清空且被清空了)，图片会被清空。
                        $updateData[$imgKey] = $data[$imgKey];
                    }
                    // 如果 $imgKey 既不来自新上传($info)，也不在表单提交数据($data)中，
                    // 则 $updateData 不会包含此 $imgKey，该数据库字段在此次保存中不会被更新。
                }
            }
            
            $result = $obj->save($updateData);
            if ($result !== false) {
                $this->success('审核完成', U('index'));
            } else {
                $this->error('审核失败');
            }
        }
    }

    /**
     * 一键下架过期素材
     * 下架条件：关联的项目已下架 或 关联的岗位已下架
     * 例外条件：项目名为"通用" 或 岗位名为"通用"的不下架
     */
    public function batchOfflineExpired() {
        // 权限检查
        $admin_id = intval(session('admin_id'));
        if ($admin_id <= 0) {
            $this->ajaxReturn(['status' => 0, 'msg' => '登录信息失效，请重新登录']);
        }

        $obj = D("Moment");

        // 开启事务
        $obj->startTrans();
        try {
            // 构建查询条件：找出需要下架的素材
            // 1. 素材当前是上架状态(status=1)
            // 2. 关联的项目已下架(project.status=0) 或 关联的岗位已下架(project_post.status=0)
            // 3. 排除项目名为"通用" 或 岗位名为"通用"的素材

            $sql = "UPDATE " . C('DB_PREFIX') . "moment m
                    LEFT JOIN " . C('DB_PREFIX') . "project p ON m.project_id = p.id
                    LEFT JOIN " . C('DB_PREFIX') . "project_post pp ON m.post_id = pp.id
                    SET m.status = 0, m.update_time = " . time() . ", m.update_by = " . $admin_id . "
                    WHERE m.status = 1
                    AND (p.status = 0 OR pp.status = 0)
                    AND NOT (p.name = '通用' OR pp.job_name = '通用')";

            // 执行批量更新
            $result = M()->execute($sql);

            if ($result !== false) {
                $obj->commit();
                $this->ajaxReturn([
                    'status' => 1,
                    'msg' => '操作成功',
                    'data' => ['count' => $result]
                ]);
            } else {
                throw new \Exception('批量下架操作失败');
            }

        } catch (\Exception $e) {
            $obj->rollback();
            $this->ajaxReturn([
                'status' => 0,
                'msg' => '操作失败：' . $e->getMessage()
            ]);
        }
    }

}
