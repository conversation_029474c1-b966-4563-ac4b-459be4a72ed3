<include file="block/hat" />
<script type="text/javascript" src="/static/js/lib/jquery-ui-1.10.3.min.js"></script>
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<ul class="nav nav-tabs">
				<li class="active"><a>积分异动</a></li>
			</ul>
			<style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
<div class="main">
	<div class="alert alert-warning">请谨慎使用此功能。</div>
	<form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
		<div class="panel panel-default">
			<div class="panel-body">
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>用户</label>
					<div class="col-sm-9 col-xs-12">
						<p class='form-control-static'>#{$row.id}， 昵称：{$row.nickname}</p>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>异动积分</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="point" class="form-control" maxlength="7"  />
						<span class="help-block">需变动的积分数，填写整数</span>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style='color:red'>*</span>说明</label>
					<div class="col-sm-9 col-xs-12">
                        <input type="text" name="desc" class="form-control"  />
						<span class="help-block">说明异动原因</span>
					</div>
				</div>
			</div>
		</div>
		<div class="form-group col-sm-12">
		    <input type="hidden" name="user_id" value="{$row.id}" />
			<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
		</div>
	</form>
</div>


		</div>
	</div>
</div>

<script type="text/javascript">
$(function() {
	$('#preview').click(function() {
		var img = $("#form1 input[name=img]");
		var url = $("#form1 input[name=url]");
		if (!img.val() || !url.val()) {
			alert('请先设置好图片和网址');
			return false;
		}
	});
})
</script>
<include file="block/footer" />