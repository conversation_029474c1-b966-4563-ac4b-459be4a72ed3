<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化简历上传页面样式 */
                .upjob-page-container {
                    /* 限制样式作用域 */
                }

                .upjob-page-container .upjob-wrapper {
                    width: 100%;
                    padding: 1.5rem;
                    background: #f7fafc;
                    min-height: 100vh;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.5;
                }

                /* 现代化页面标题 */
                .upjob-page-container .upjob-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                    width: 100%;
                }

                .upjob-page-container .upjob-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .upjob-page-container .upjob-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .upjob-page-container .upjob-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .upjob-page-container .upjob-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .upjob-page-container .upjob-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .upjob-page-container .upjob-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .upjob-page-container .upjob-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                /* 表单卡片样式 */
                .upjob-page-container .upjob-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .upjob-page-container .upjob-card-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .upjob-page-container .upjob-card-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .upjob-page-container .upjob-card-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .upjob-page-container .upjob-card-body {
                    padding: 2rem;
                }

                /* 表单样式 */
                .upjob-page-container .upjob-form-group {
                    margin-bottom: 2rem;
                }

                .upjob-page-container .upjob-form-label {
                    display: block;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 0.75rem;
                }

                .upjob-page-container .upjob-form-label .required {
                    color: #ef4444;
                    margin-right: 0.25rem;
                }

                .upjob-page-container .upjob-form-select,
                .upjob-page-container .upjob-form-input {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                    font-family: inherit;
                }

                .upjob-page-container .upjob-form-select:focus,
                .upjob-page-container .upjob-form-input:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .upjob-page-container .upjob-form-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                .upjob-page-container .upjob-form-help {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-top: 0.5rem;
                    line-height: 1.5;
                }

                /* 文件上传区域样式 */
                .upjob-page-container .upjob-file-upload {
                    border: 2px dashed #d1d5db;
                    border-radius: 0.75rem;
                    padding: 2rem;
                    text-align: center;
                    background: #f9fafb;
                    transition: all 0.3s ease;
                    position: relative;
                    min-height: 200px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                }

                .upjob-page-container .upjob-file-upload:hover {
                    border-color: #667eea;
                    background: #f0f4ff;
                }

                .upjob-page-container .upjob-file-upload.dragover {
                    border-color: #667eea;
                    background: #e0e7ff;
                    transform: scale(1.02);
                }

                .upjob-page-container .upjob-file-icon {
                    width: 4rem;
                    height: 4rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 1rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 2rem;
                    margin-bottom: 1rem;
                }

                .upjob-page-container .upjob-file-text {
                    font-size: 1.5rem;
                    color: #374151;
                    margin-bottom: 0.5rem;
                    font-weight: 600;
                }

                .upjob-page-container .upjob-file-hint {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-bottom: 1.5rem;
                }

                .upjob-page-container .upjob-file-types {
                    font-size: 1.125rem;
                    color: #9ca3af;
                    background: #f3f4f6;
                    padding: 0.5rem 1rem;
                    border-radius: 0.375rem;
                    margin-top: 1rem;
                }

                /* 提交按钮样式 */
                .upjob-page-container .upjob-submit-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    padding: 2rem;
                    text-align: center;
                    margin-top: 2rem;
                }

                .upjob-page-container .upjob-submit-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 1rem 2rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    font-family: inherit;
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .upjob-page-container .upjob-submit-btn:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                }

                /* 动画效果 */
                @keyframes upjobFadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .upjob-page-container .upjob-fade-in {
                    animation: upjobFadeInUp 0.6s ease-out;
                }

                .upjob-page-container .upjob-fade-in-delay-1 {
                    animation: upjobFadeInUp 0.6s ease-out 0.1s both;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .upjob-page-container .upjob-header-content {
                        flex-direction: column;
                        text-align: center;
                    }
                }

                @media (max-width: 768px) {
                    .upjob-page-container .upjob-wrapper {
                        padding: 1rem;
                    }

                    .upjob-page-container .upjob-title-main {
                        font-size: 1.75rem;
                    }

                    .upjob-page-container .upjob-title-sub {
                        font-size: 1.25rem;
                    }

                    .upjob-page-container .upjob-card-body {
                        padding: 1rem;
                    }

                    .upjob-page-container .upjob-file-upload {
                        padding: 1.5rem;
                        min-height: 150px;
                    }

                    .upjob-page-container .upjob-file-icon {
                        width: 3rem;
                        height: 3rem;
                        font-size: 1.5rem;
                    }

                    .upjob-page-container .upjob-file-text {
                        font-size: 1.25rem;
                    }

                    .upjob-page-container .upjob-file-hint {
                        font-size: 1.125rem;
                    }
                }
            </style>

            <div class="upjob-page-container">
            <div class="upjob-wrapper">
            <!-- 现代化页面标题 -->
            <div class="upjob-header upjob-fade-in">
                <div class="upjob-header-content">
                    <div class="upjob-title">
                        <div class="upjob-title-icon">
                            <i class="fa fa-upload"></i>
                        </div>
                        <div class="upjob-title-text">
                            <h1 class="upjob-title-main">简历文件上传</h1>
                            <p class="upjob-title-sub">Resume File Upload</p>
                        </div>
                    </div>
                </div>
            </div>

            <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
                <!-- 简历上传表单卡片 -->
                <div class="upjob-card upjob-fade-in-delay-1">
                    <div class="upjob-card-header">
                        <div class="upjob-card-icon">
                            <i class="fa fa-file-text"></i>
                        </div>
                        <h3 class="upjob-card-title">简历信息</h3>
                    </div>
                    <div class="upjob-card-body">
                        <!-- 服务站选择 -->
                        <div class="upjob-form-group">
                            <label class="upjob-form-label">所属服务站</label>
                            <select name="service_station_id" class="upjob-form-select">
                                <php>foreach($serviceStationList as $serviceStationId => $serviceName) {</php>
                                <option value="{:$serviceStationId}" {:  $row['service_station_id'] == $serviceStationId ? 'selected' : ''} >{:$serviceStationId}-{:$serviceName}</option>
                                <php>}</php>
                            </select>
                            <div class="upjob-form-help">选择简历所属的服务站，用于分类管理</div>
                        </div>

                        <!-- 文件上传区域 -->
                        <div class="upjob-form-group">
                            <label class="upjob-form-label">
                                <span class="required">*</span>
                                简历文件
                            </label>
                            <div class="upjob-file-upload" id="fileUploadArea">
                                <div class="upjob-file-icon">
                                    <i class="fa fa-cloud-upload"></i>
                                </div>
                                <div class="upjob-file-text">点击上传或拖拽文件到此区域</div>
                                <div class="upjob-file-hint">支持 PDF、DOC、DOCX 格式的简历文件</div>
                                <div class="upjob-file-types">文件大小不超过 10MB</div>

                                <!-- 原有的文件上传组件 -->
                                <div style="margin-top: 1rem;">
                                    <php>
                                        echo tpl_form_field_file('files', $row['files'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                    </php>
                                </div>
                            </div>
                            <div class="upjob-form-help">
                                <strong>上传说明：</strong><br>
                                • 请确保简历内容完整、格式规范<br>
                                • 建议使用PDF格式以保证最佳显示效果<br>
                                • 文件名建议包含姓名信息，便于识别
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="upjob-submit-container">
                    <input type="hidden" name="id" value="{$row.id}"/>
                    <button type="submit" name="submit" class="upjob-submit-btn">
                        <i class="fa fa-save"></i>
                        <span>保存简历文件</span>
                    </button>
                </div>
            </form>
            </div>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />
<script>
    $(function() {
        // 页面动画效果
        $('.upjob-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });

        // 文件上传区域拖拽效果
        var $fileUploadArea = $('#fileUploadArea');

        // 防止默认拖拽行为
        $(document).on('dragover drop', function(e) {
            e.preventDefault();
        });

        // 拖拽进入
        $fileUploadArea.on('dragenter dragover', function(e) {
            e.preventDefault();
            $(this).addClass('dragover');
        });

        // 拖拽离开
        $fileUploadArea.on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
        });

        // 文件放置
        $fileUploadArea.on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');

            var files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });

        // 处理文件上传
        function handleFileUpload(file) {
            var allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
            var maxSize = 10 * 1024 * 1024; // 10MB

            if (!allowedTypes.includes(file.type)) {
                if (typeof layer !== 'undefined') {
                    layer.msg('请上传PDF、DOC或DOCX格式的文件', {icon: 2});
                } else {
                    alert('请上传PDF、DOC或DOCX格式的文件');
                }
                return;
            }

            if (file.size > maxSize) {
                if (typeof layer !== 'undefined') {
                    layer.msg('文件大小不能超过10MB', {icon: 2});
                } else {
                    alert('文件大小不能超过10MB');
                }
                return;
            }

            console.log('文件准备上传:', file.name);
        }

        // 表单验证增强
        $('.upjob-form-select, .upjob-form-input').on('focus', function() {
            $(this).css('border-color', '#667eea');
            $(this).css('box-shadow', '0 0 0 3px rgba(102, 126, 234, 0.1)');
        });

        $('.upjob-form-select, .upjob-form-input').on('blur', function() {
            if (!$(this).val().trim()) {
                $(this).css('border-color', '#e5e7eb');
                $(this).css('box-shadow', 'none');
            }
        });

        // 表单提交增强
        $('#form1').on('submit', function(e) {
            var $submitBtn = $('.upjob-submit-btn');
            var originalText = $submitBtn.html();

            // 验证服务站选择
            var serviceStationId = $('select[name="service_station_id"]').val();
            if (!serviceStationId) {
                e.preventDefault();
                $('select[name="service_station_id"]').focus();
                $('select[name="service_station_id"]').css('border-color', '#ef4444');
                $('select[name="service_station_id"]').css('box-shadow', '0 0 0 3px rgba(239, 68, 68, 0.1)');

                if (typeof layer !== 'undefined') {
                    layer.msg('请选择所属服务站', {icon: 2});
                } else {
                    alert('请选择所属服务站');
                }
                return false;
            }

            // 提交状态
            $submitBtn.html('<i class="fa fa-spinner fa-spin"></i> <span>上传中...</span>');
            $submitBtn.prop('disabled', true);
        });

        // 卡片悬停效果
        $('.upjob-card').hover(
            function() {
                $(this).css('transform', 'translateY(-2px)');
                $(this).css('box-shadow', '0 8px 15px -3px rgba(0, 0, 0, 0.1)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
                $(this).css('box-shadow', '0 4px 6px -1px rgba(0, 0, 0, 0.1)');
            }
        );
    });

    // 保留原有功能
    require(["datetimepicker", 'layer', 'util'], function($, layer, util){
        $(function () {
            $('.form input[name="type"]').change(function () {
                var type = $(this).val();
                $('.js_type').hide();
                $('.js_type'+type).show();
            });
        })

        $(".datetimepicker").each(function(){
            var opt = {
                language: "zh-CN",
                format: "yyyy-mm-dd",
                minView: 2,
                autoclose: true,
                format : "yyyy-mm-dd",
                minView : 2,
                minuteStep:1,
            };
            $(this).datetimepicker(opt);
        });
    });

    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
            u.editor($('.richtext1')[0]);
        })
    });
</script>