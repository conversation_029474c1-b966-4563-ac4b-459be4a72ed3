<include file="block/hat" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 重置和基础样式 */
                * {
                    box-sizing: border-box;
                }

                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    color: #2d3748;
                }

                .projectpost-edit-container {
                    padding: 2rem;
                    background: #f7fafc;
                    min-height: 100vh;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                }

                /* 现代化标签页 */
                .projectpost-tabs {
                    background: white;
                    border-radius: 1rem 1rem 0 0;
                    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    border-bottom: none;
                    overflow: hidden;
                    margin-bottom: 0;
                }

                .projectpost-tab-list {
                    display: flex;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    flex-wrap: wrap;
                }

                .projectpost-tab-item {
                    flex: 1;
                    min-width: 120px;
                }

                .projectpost-tab-link {
                    display: block;
                    padding: 1.5rem 1rem;
                    color: #4a5568;
                    text-decoration: none;
                    font-weight: 600;
                    font-size: 1.5rem;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    border-right: 1px solid #e2e8f0;
                    position: relative;
                    text-align: center;
                }

                .projectpost-tab-item:last-child .projectpost-tab-link {
                    border-right: none;
                }

                .projectpost-tab-link:hover {
                    background: #f7fafc;
                    color: #667eea;
                    text-decoration: none;
                }

                .projectpost-tab-link.active {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .projectpost-tab-link.active::after {
                    content: '';
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                    right: 0;
                    height: 2px;
                    background: white;
                }

                /* 页面头部 */
                .projectpost-page-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 2rem 0;
                    flex-wrap: wrap;
                    gap: 1.5rem;
                }

                .projectpost-page-title-section {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .projectpost-page-title {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .projectpost-page-subtitle {
                    color: #4a5568;
                    font-size: 1.5rem;
                    margin: 0;
                }

                .projectpost-page-actions {
                    display: flex;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                /* 现代化表单卡片 */
                .projectpost-form-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    margin-bottom: 1.5rem;
                }

                .projectpost-form-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .projectpost-form-header {
                    padding: 2rem 2rem 1rem 2rem;
                    border-bottom: 1px solid #f1f5f9;
                }

                .projectpost-form-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .projectpost-form-description {
                    color: #4a5568;
                    font-size: 1.5rem;
                    margin: 0.5rem 0 0 0;
                }

                .projectpost-form-body {
                    padding: 2rem;
                }

                /* 现代化表单组 */
                .projectpost-form-group {
                    margin-bottom: 2rem;
                }

                .projectpost-form-label {
                    display: block;
                    font-weight: 600;
                    color: #2d3748;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .projectpost-form-label .required {
                    color: #f56565;
                    font-weight: 700;
                }

                .projectpost-form-control {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: white;
                    font-family: inherit;
                }

                .projectpost-form-control:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    background: #fafafa;
                }

                .projectpost-form-control:hover {
                    border-color: #cbd5e0;
                }

                .projectpost-textarea {
                    resize: vertical;
                    min-height: 120px;
                }

                .projectpost-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                /* 表单帮助文本 */
                .projectpost-help-text {
                    font-size: 1.5rem;
                    color: #718096;
                    margin-top: 0.5rem;
                }

                .projectpost-error-text {
                    font-size: 1.5rem;
                    color: #f56565;
                    margin-top: 0.5rem;
                }

                /* 现代化按钮 */
                .projectpost-btn {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    font-size: 1.5rem;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                    font-family: inherit;
                }

                .projectpost-btn:before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .projectpost-btn:hover:before {
                    left: 100%;
                }

                .projectpost-btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .projectpost-btn-primary:hover {
                    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    color: white;
                    text-decoration: none;
                }

                .projectpost-btn-secondary {
                    background: #f1f5f9;
                    color: #4a5568;
                    border: 1px solid #e2e8f0;
                }

                .projectpost-btn-secondary:hover {
                    background: #e2e8f0;
                    transform: translateY(-1px);
                    color: #2d3748;
                    text-decoration: none;
                }

                .projectpost-btn-lg {
                    padding: 1rem 2rem;
                    font-size: 1.5rem;
                }

                /* 表单操作区域 */
                .projectpost-form-actions {
                    padding: 1.5rem 2rem 2rem 2rem;
                    background: #f7fafc;
                    border-top: 1px solid #e2e8f0;
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    flex-wrap: wrap;
                }

                /* 双列表单布局 */
                .projectpost-form-row {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 2rem;
                }

                /* 报价信息展示 */
                .projectpost-pricing-info {
                    background: #f0fff4;
                    border: 1px solid #9ae6b4;
                    border-radius: 0.5rem;
                    padding: 1rem;
                    font-size: 1.5rem;
                    color: #22543d;
                    line-height: 1.8;
                }

                /* 富文本编辑器样式 */
                .projectpost-richtext-container {
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .projectpost-richtext-container:focus-within {
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .projectpost-richtext-container .richtext {
                    border: none;
                    font-size: 1.5rem;
                    font-family: inherit;
                    padding: 0.75rem 1rem;
                    width: 100%;
                    resize: vertical;
                    min-height: 120px;
                }

                .projectpost-richtext-container .richtext:focus {
                    outline: none;
                }

                /* 图片上传区域样式 */
                .projectpost-image-upload {
                    border: 2px dashed #e2e8f0;
                    border-radius: 0.5rem;
                    padding: 1rem;
                    transition: all 0.3s ease;
                }

                .projectpost-image-upload:hover {
                    border-color: #667eea;
                    background: #f7fafc;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .projectpost-edit-container {
                        padding: 1.5rem;
                    }

                    .projectpost-page-header {
                        flex-direction: column;
                        align-items: flex-start;
                    }

                    .projectpost-page-title {
                        font-size: 1.5rem;
                    }

                    .projectpost-form-header,
                    .projectpost-form-body,
                    .projectpost-form-actions {
                        padding: 1.5rem;
                    }

                    .projectpost-form-actions {
                        flex-direction: column;
                    }

                    .projectpost-btn {
                        width: 100%;
                        justify-content: center;
                    }

                    .projectpost-tab-list {
                        flex-direction: column;
                    }

                    .projectpost-tab-item {
                        flex: none;
                    }

                    .projectpost-tab-link {
                        border-right: none;
                        border-bottom: 1px solid #e2e8f0;
                    }

                    .projectpost-tab-item:last-child .projectpost-tab-link {
                        border-bottom: none;
                    }

                    .projectpost-form-row {
                        grid-template-columns: 1fr;
                        gap: 1rem;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .projectpost-fade-in {
                    animation: fadeInUp 0.5s ease-out;
                }

                .projectpost-fade-in-delay-1 {
                    animation: fadeInUp 0.5s ease-out 0.1s both;
                }

                .projectpost-fade-in-delay-2 {
                    animation: fadeInUp 0.5s ease-out 0.2s both;
                }

                /* 表单验证状态 */
                .projectpost-form-control.error {
                    border-color: #f56565;
                    box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
                }

                .projectpost-form-control.success {
                    border-color: #48bb78;
                    box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
                }

                /* 加载状态 */
                .projectpost-btn.loading {
                    pointer-events: none;
                    opacity: 0.7;
                }

                .projectpost-btn.loading::after {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    margin: auto;
                    border: 2px solid transparent;
                    border-top-color: currentColor;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>

            <div class="projectpost-edit-container">
                <!-- 现代化标签页 -->
                <div class="projectpost-tabs">
                    <ul class="projectpost-tab-list">
                        <li class="projectpost-tab-item">
                            <a href="{:U('Project/index')}" class="projectpost-tab-link <php>if(CONTROLLER_NAME=='Project' && ACTION_NAME=='index') echo 'active'</php>">
                                <i class="fa fa-briefcase"></i>
                                项目管理
                            </a>
                        </li>
                        <li class="projectpost-tab-item">
                            <a href="{:U('Project/edit')}" class="projectpost-tab-link <php>if(CONTROLLER_NAME=='Project' && ACTION_NAME=='edit') echo 'active'</php>">
                                <i class="fa fa-<php>echo !I('get.id') ? 'plus' : 'edit';</php>"></i>
                                <php>echo !I('get.id') ? '添加' : '编辑';</php>项目
                            </a>
                        </li>
                        <li class="projectpost-tab-item">
                            <a href="{:U('Projectpost/index')}" class="projectpost-tab-link <php>if(CONTROLLER_NAME=='Projectpost' && ACTION_NAME=='index') echo 'active'</php>">
                                <i class="fa fa-users"></i>
                                岗位管理
                            </a>
                        </li>
                        <li class="projectpost-tab-item">
                            <a href="{:U('Projectpost/edit')}" class="projectpost-tab-link <php>if(CONTROLLER_NAME=='Projectpost' && ACTION_NAME=='edit') echo 'active'</php>">
                                <i class="fa fa-<php>echo !I('get.id') ? 'plus' : 'edit';</php>"></i>
                                <php>echo !I('get.id') ? '添加' : '编辑';</php>岗位
                            </a>
                        </li>
                        <li class="projectpost-tab-item">
                            <a href="{:U('Project/quotation')}" class="projectpost-tab-link <php>if(ACTION_NAME=='quotation') echo 'active'</php>">
                                <i class="fa fa-calculator"></i>
                                报价管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 页面头部 -->
                <div class="projectpost-page-header" style="margin-top: 2rem;">
                    <div class="projectpost-page-title-section">
                        <h1 class="projectpost-page-title">
                            <i class="fa fa-<php>echo I('get.id') ? 'edit' : 'plus';</php>"></i>
                            <php>echo I('get.id') ? '编辑岗位' : '添加岗位';</php>
                        </h1>
                        <p class="projectpost-page-subtitle">
                            <php>echo I('get.id') ? '修改岗位信息和配置' : '创建新的岗位并设置详细信息';</php>
                        </p>
                    </div>
                    <div class="projectpost-page-actions">
                        <a href="{:U('Projectpost/index')}" class="projectpost-btn projectpost-btn-secondary">
                            <i class="fa fa-arrow-left"></i>
                            返回列表
                        </a>
                    </div>
                </div>

                <!-- 现代化表单 -->
                <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="projectpost-form">
                    <!-- 基本信息卡片 -->
                    <div class="projectpost-form-card">
                        <div class="projectpost-form-header">
                            <h2 class="projectpost-form-title">
                                <i class="fa fa-info-circle"></i>
                                岗位基本信息
                            </h2>
                            <p class="projectpost-form-description">
                                请填写岗位的基本信息，包括名称、所属项目等
                            </p>
                        </div>

                        <div class="projectpost-form-body">
                            <!-- 岗位名称 -->
                            <div class="projectpost-form-group">
                                <label class="projectpost-form-label">
                                    <i class="fa fa-user-circle"></i>
                                    岗位名称
                                    <span class="required">*</span>
                                </label>
                                <input type="text"
                                       name="job_name"
                                       class="projectpost-form-control"
                                       value="{$row.job_name}"
                                       placeholder="请输入岗位名称"
                                       required />
                                <div class="projectpost-help-text">岗位的完整名称，将显示在岗位列表中</div>
                            </div>

                            <!-- 所属项目 -->
                            <div class="projectpost-form-group">
                                <label class="projectpost-form-label">
                                    <i class="fa fa-briefcase"></i>
                                    所属项目
                                    <span class="required">*</span>
                                </label>
                                <select name="project_id" class="projectpost-form-control projectpost-select" required>
                                    <option value="0">请选择项目</option>
                                    <php>foreach($projectList as $key => $val) {</php>
                                    <option value="{$key}" {: $key== $row['project_id'] ? 'selected' : ''}>{$val}</option>
                                    <php>}</php>
                                </select>
                                <div class="projectpost-help-text">选择该岗位所属的项目</div>
                            </div>

                            <!-- 是否公益 -->
                            <div class="projectpost-form-group">
                                <label class="projectpost-form-label">
                                    <i class="fa fa-heart"></i>
                                    是否公益
                                    <span class="required">*</span>
                                </label>
                                <select name="is_free" class="projectpost-form-control projectpost-select" required>
                                    <option value="0">请选择</option>
                                    <php>foreach($freeList as $key => $val) {</php>
                                    <option value="{$key}" {: $key== $row['is_free'] ? 'selected' : ''}>{$val.text}</option>
                                    <php>}</php>
                                </select>
                                <div class="projectpost-help-text">选择该岗位是否为公益性质</div>
                            </div>
                        </div>
                    </div>

                    <!-- 报价管理卡片 -->
                    <div class="projectpost-form-card projectpost-fade-in-delay-1">
                        <div class="projectpost-form-header">
                            <h2 class="projectpost-form-title">
                                <i class="fa fa-calculator"></i>
                                报价管理
                            </h2>
                            <p class="projectpost-form-description">
                                查看和管理岗位的报价信息
                            </p>
                        </div>

                        <div class="projectpost-form-body">
                            <!-- 报价管理信息展示 -->
                            <div class="projectpost-form-group">
                                <label class="projectpost-form-label">
                                    <i class="fa fa-list"></i>
                                    当前报价信息
                                </label>
                                <div class="projectpost-pricing-info">
                                    <php>
                                        foreach($projectIdentityList as $projectIdentitRow) {
                                            $projectIdentitJoinCost = $projectJoinIdentity[$row['id']][$projectIdentitRow['id']] ?:0;
                                            echo $projectIdentitRow['name']."：".$projectIdentitJoinCost." ； ";
                                        }
                                    </php>
                                </div>
                                <div class="projectpost-help-text">显示该岗位在不同身份下的报价信息</div>
                            </div>
                        </div>
                    </div>

                    <!-- 成本和奖励信息卡片 -->
                    <div class="projectpost-form-card projectpost-fade-in-delay-1">
                        <div class="projectpost-form-header">
                            <h2 class="projectpost-form-title">
                                <i class="fa fa-money"></i>
                                成本和奖励信息
                            </h2>
                            <p class="projectpost-form-description">
                                设置岗位的成本、报价和奖励信息
                            </p>
                        </div>

                        <div class="projectpost-form-body">
                            <!-- 内部成本和推荐人奖励 -->
                            <div class="projectpost-form-row">
                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-calculator"></i>
                                        内部成本
                                    </label>
                                    <input type="text"
                                           name="internal_costs"
                                           class="projectpost-form-control"
                                           value="{$row.internal_costs}"
                                           placeholder="请输入内部成本" />
                                    <div class="projectpost-help-text">岗位的内部成本费用</div>
                                </div>

                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-gift"></i>
                                        推荐人服务奖励
                                    </label>
                                    <input type="text"
                                           name="reward"
                                           class="projectpost-form-control"
                                           value="{$row.reward}"
                                           placeholder="请输入推荐人奖励" />
                                    <div class="projectpost-help-text">推荐人获得的服务奖励</div>
                                </div>
                            </div>

                            <!-- 服务报价 -->
                            <div class="projectpost-form-row">
                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-tag"></i>
                                        服务报价
                                    </label>
                                    <input type="text"
                                           name="service_price"
                                           class="projectpost-form-control"
                                           value="{$row.service_price}"
                                           placeholder="请输入服务报价" />
                                    <div class="projectpost-help-text">基础服务报价</div>
                                </div>

                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-file-text"></i>
                                        服务报价说明
                                    </label>
                                    <input type="text"
                                           name="service_price_text"
                                           class="projectpost-form-control"
                                           value="{$row.service_price_text}"
                                           placeholder="请输入服务报价说明" />
                                    <div class="projectpost-help-text">服务报价的详细说明</div>
                                </div>
                            </div>

                            <!-- 最高报价 -->
                            <div class="projectpost-form-row">
                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-line-chart"></i>
                                        最高报价
                                    </label>
                                    <input type="text"
                                           name="max_price"
                                           class="projectpost-form-control"
                                           value="{$row.max_price}"
                                           placeholder="请输入最高报价" />
                                    <div class="projectpost-help-text">该岗位的最高报价</div>
                                </div>

                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-file-text"></i>
                                        最高报价说明
                                    </label>
                                    <input type="text"
                                           name="max_price_text"
                                           class="projectpost-form-control"
                                           value="{$row.max_price_text}"
                                           placeholder="请输入最高报价说明" />
                                    <div class="projectpost-help-text">最高报价的详细说明</div>
                                </div>
                            </div>

                            <!-- 结算周期 -->
                            <div class="projectpost-form-group">
                                <label class="projectpost-form-label">
                                    <i class="fa fa-clock-o"></i>
                                    结算周期
                                </label>
                                <input type="text"
                                       name="settle_day"
                                       class="projectpost-form-control"
                                       value="{$row.settle_day}"
                                       placeholder="请输入结算周期（天数）" />
                                <div class="projectpost-help-text">项目结算的周期，单位为天</div>
                            </div>
                        </div>
                    </div>

                    <!-- 人员要求卡片 -->
                    <div class="projectpost-form-card projectpost-fade-in-delay-2">
                        <div class="projectpost-form-header">
                            <h2 class="projectpost-form-title">
                                <i class="fa fa-user"></i>
                                人员要求
                            </h2>
                            <p class="projectpost-form-description">
                                设置岗位对人员的具体要求
                            </p>
                        </div>

                        <div class="projectpost-form-body">
                            <!-- 学历和性别 -->
                            <div class="projectpost-form-row">
                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-graduation-cap"></i>
                                        学历要求
                                    </label>
                                    <select name="qualification" class="projectpost-form-control projectpost-select">
                                        <option value="0">请选择学历</option>
                                        <php>foreach($qualificationList as $key => $val) {</php>
                                        <option value="{$key}" {: $key== $row['qualification'] ? 'selected' : ''}>{$val.text}</option>
                                        <php>}</php>
                                    </select>
                                    <div class="projectpost-help-text">选择岗位要求的最低学历</div>
                                </div>

                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-venus-mars"></i>
                                        性别要求
                                    </label>
                                    <select name="sex" class="projectpost-form-control projectpost-select">
                                        <option value="0">请选择性别</option>
                                        <php>foreach($sexList as $key => $val) {</php>
                                        <option value="{$key}" {: $key== $row['sex'] ? 'selected' : ''}>{$val.text}</option>
                                        <php>}</php>
                                    </select>
                                    <div class="projectpost-help-text">选择岗位的性别要求</div>
                                </div>
                            </div>

                            <!-- 专业要求 -->
                            <div class="projectpost-form-group">
                                <label class="projectpost-form-label">
                                    <i class="fa fa-book"></i>
                                    专业要求
                                </label>
                                <input type="text"
                                       name="major"
                                       class="projectpost-form-control"
                                       value="{$row.major}"
                                       placeholder="请输入专业要求" />
                                <div class="projectpost-help-text">岗位要求的专业背景，可留空表示不限专业</div>
                            </div>

                            <!-- 年龄要求 -->
                            <div class="projectpost-form-row">
                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-birthday-cake"></i>
                                        最小年龄
                                    </label>
                                    <input type="text"
                                           name="min_age"
                                           class="projectpost-form-control"
                                           value="{$row.min_age}"
                                           placeholder="请输入最小年龄" />
                                    <div class="projectpost-help-text">岗位要求的最小年龄</div>
                                </div>

                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-birthday-cake"></i>
                                        最大年龄
                                    </label>
                                    <input type="text"
                                           name="max_age"
                                           class="projectpost-form-control"
                                           value="{$row.max_age}"
                                           placeholder="请输入最大年龄" />
                                    <div class="projectpost-help-text">岗位要求的最大年龄</div>
                                </div>
                            </div>

                            <!-- 身高要求和标签 -->
                            <div class="projectpost-form-row">
                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-arrows-v"></i>
                                        身高要求
                                    </label>
                                    <input type="text"
                                           name="height"
                                           class="projectpost-form-control"
                                           value="{$row.height}"
                                           placeholder="请输入身高要求" />
                                    <div class="projectpost-help-text">岗位的身高要求，如：160cm以上</div>
                                </div>

                                <div class="projectpost-form-group">
                                    <label class="projectpost-form-label">
                                        <i class="fa fa-tags"></i>
                                        标签
                                    </label>
                                    <input type="text"
                                           name="tag"
                                           class="projectpost-form-control"
                                           value="{$row.tag}"
                                           placeholder="请输入岗位标签" />
                                    <div class="projectpost-help-text">岗位的特殊标签或要求</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 展示图片卡片 -->
                    <div class="projectpost-form-card projectpost-fade-in-delay-2">
                        <div class="projectpost-form-header">
                            <h2 class="projectpost-form-title">
                                <i class="fa fa-image"></i>
                                展示图片
                            </h2>
                            <p class="projectpost-form-description">
                                上传岗位的展示图片，用于岗位展示
                            </p>
                        </div>

                        <div class="projectpost-form-body">
                            <!-- 展示图片1 -->
                            <div class="projectpost-form-group">
                                <label class="projectpost-form-label">
                                    <i class="fa fa-picture-o"></i>
                                    展示图片1
                                    <span class="required">*</span>
                                </label>
                                <div class="projectpost-image-upload">
                                    <php>
                                        echo tpl_form_field_image('img_url_one', $row['img_url_one'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                    </php>
                                </div>
                                <div class="projectpost-help-text">上传第一张展示图片，建议尺寸：800x600像素</div>
                            </div>

                            <!-- 展示图片2 -->
                            <div class="projectpost-form-group">
                                <label class="projectpost-form-label">
                                    <i class="fa fa-picture-o"></i>
                                    展示图片2
                                    <span class="required">*</span>
                                </label>
                                <div class="projectpost-image-upload">
                                    <php>
                                        echo tpl_form_field_image('img_url_two', $row['img_url_two'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                    </php>
                                </div>
                                <div class="projectpost-help-text">上传第二张展示图片，建议尺寸：800x600像素</div>
                            </div>

                            <!-- 展示图片3 -->
                            <div class="projectpost-form-group">
                                <label class="projectpost-form-label">
                                    <i class="fa fa-picture-o"></i>
                                    展示图片3
                                    <span class="required">*</span>
                                </label>
                                <div class="projectpost-image-upload">
                                    <php>
                                        echo tpl_form_field_image('img_url_three', $row['img_url_three'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                    </php>
                                </div>
                                <div class="projectpost-help-text">上传第三张展示图片，建议尺寸：800x600像素</div>
                            </div>
                        </div>
                    </div>

                    <!-- 其他描述卡片 -->
                    <div class="projectpost-form-card projectpost-fade-in-delay-2">
                        <div class="projectpost-form-header">
                            <h2 class="projectpost-form-title">
                                <i class="fa fa-file-text"></i>
                                其他描述
                            </h2>
                            <p class="projectpost-form-description">
                                添加岗位的详细描述和其他相关信息
                            </p>
                        </div>

                        <div class="projectpost-form-body">
                            <!-- 其他描述 -->
                            <div class="projectpost-form-group">
                                <label class="projectpost-form-label">
                                    <i class="fa fa-edit"></i>
                                    详细描述
                                </label>
                                <div class="projectpost-richtext-container">
                                    <textarea name="other_content"
                                              rows="6"
                                              class="richtext projectpost-textarea"
                                              placeholder="请输入岗位的详细描述...">{$row.other_content}</textarea>
                                </div>
                                <div class="projectpost-help-text">详细的岗位描述，支持富文本格式，可以包含岗位职责、工作内容、福利待遇等信息</div>
                            </div>
                        </div>
                    </div>

                    <!-- 表单操作区域 -->
                    <div class="projectpost-form-actions">
                        <input type="hidden" name="id" value="{$row.id}" />
                        <button type="button" onclick="history.back()" class="projectpost-btn projectpost-btn-secondary">
                            <i class="fa fa-times"></i>
                            取消
                        </button>
                        <button type="submit" name="submit" class="projectpost-btn projectpost-btn-primary projectpost-btn-lg">
                            <i class="fa fa-save"></i>
                            <php>echo I('get.id') ? '保存修改' : '创建岗位';</php>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="__ROOT__/static/js/prime/common.js"></script>
<link rel="stylesheet" href="__ROOT__/static/js/app/layer/skin/layer.css" />

<script>
	require(['layer', 'util'], function(layer, u) {
		$(function(){
			// 初始化富文本编辑器
			u.editor($('.richtext')[0]);
			u.editor($('.richtext1')[0]);

			// 表单验证和交互
			var $form = $('#projectpost-form');
			var $submitBtn = $('button[type="submit"]');

			// 表单字段实时验证
			$('.projectpost-form-control').on('input blur', function() {
				var $field = $(this);
				var value = $field.val().trim();
				var isRequired = $field.prop('required');
				var fieldName = $field.attr('name');

				// 移除之前的验证状态
				$field.removeClass('error success');
				$field.siblings('.projectpost-error-text').remove();

				// 必填字段验证
				if (isRequired && !value) {
					$field.addClass('error');
					$field.after('<div class="projectpost-error-text"><i class="fa fa-exclamation-circle"></i> 此字段为必填项</div>');
					return;
				}

				// 特定字段验证
				if (value) {
					var isValid = true;
					var errorMsg = '';

					switch(fieldName) {
						case 'job_name':
							if (value.length < 2) {
								isValid = false;
								errorMsg = '岗位名称至少2个字符';
							}
							break;
						case 'project_id':
							if (value == '0') {
								isValid = false;
								errorMsg = '请选择所属项目';
							}
							break;
						case 'is_free':
							if (value == '0') {
								isValid = false;
								errorMsg = '请选择是否公益';
							}
							break;
						case 'internal_costs':
						case 'reward':
						case 'service_price':
						case 'max_price':
							if (value && !/^\d+(\.\d{1,2})?$/.test(value)) {
								isValid = false;
								errorMsg = '请输入有效的金额格式';
							}
							break;
						case 'settle_day':
						case 'min_age':
						case 'max_age':
							if (value && !/^\d+$/.test(value)) {
								isValid = false;
								errorMsg = '请输入有效的数字';
							}
							break;
					}

					if (!isValid) {
						$field.addClass('error');
						$field.after('<div class="projectpost-error-text"><i class="fa fa-exclamation-circle"></i> ' + errorMsg + '</div>');
					} else {
						$field.addClass('success');
					}
				}
			});

			// 表单提交处理
			$form.on('submit', function(e) {
				e.preventDefault();

				// 验证必填字段
				var hasError = false;
				$('.projectpost-form-control[required]').each(function() {
					var $field = $(this);
					var value = $field.val().trim();

					if (!value || (value == '0' && ($field.attr('name') == 'project_id' || $field.attr('name') == 'is_free'))) {
						$field.addClass('error');
						$field.siblings('.projectpost-error-text').remove();
						$field.after('<div class="projectpost-error-text"><i class="fa fa-exclamation-circle"></i> 此字段为必填项</div>');
						hasError = true;
					}
				});

				// 检查是否有验证错误
				if ($('.projectpost-form-control.error').length > 0) {
					hasError = true;
				}

				if (hasError) {
					layer.msg('请修正表单中的错误信息', {icon: 2});
					// 滚动到第一个错误字段
					var $firstError = $('.projectpost-form-control.error').first();
					if ($firstError.length) {
						$('html, body').animate({
							scrollTop: $firstError.offset().top - 100
						}, 500);
						$firstError.focus();
					}
					return false;
				}

				// 设置提交按钮为加载状态
				$submitBtn.addClass('loading');
				$submitBtn.prop('disabled', true);
				var originalText = $submitBtn.html();
				$submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

				// 提交表单
				$.ajax({
					url: $form.attr('action') || window.location.href,
					type: 'POST',
					data: $form.serialize(),
					dataType: 'json',
					success: function(response) {
						if (response.status === 1 || response.code === 1) {
							layer.msg(response.info || '操作成功', {icon: 1}, function() {
								window.location.href = "{:U('Projectpost/index')}";
							});
						} else {
							layer.msg(response.info || '操作失败', {icon: 2});
						}
					},
					error: function() {
						layer.msg('网络错误，请重试', {icon: 2});
					},
					complete: function() {
						// 恢复提交按钮状态
						$submitBtn.removeClass('loading');
						$submitBtn.prop('disabled', false);
						$submitBtn.html(originalText);
					}
				});
			});

			// 页面加载动画
			setTimeout(function() {
				$('.projectpost-form-card').addClass('projectpost-fade-in');
			}, 100);

			// 表单字段聚焦效果
			$('.projectpost-form-control').on('focus', function() {
				$(this).closest('.projectpost-form-group').addClass('focused');
			}).on('blur', function() {
				$(this).closest('.projectpost-form-group').removeClass('focused');
			});

			// 富文本编辑器聚焦效果
			$('.richtext').on('focus', function() {
				$(this).closest('.projectpost-richtext-container').addClass('focused');
			}).on('blur', function() {
				$(this).closest('.projectpost-richtext-container').removeClass('focused');
			});

			// 键盘快捷键
			$(document).on('keydown', function(e) {
				// Ctrl+S 保存
				if (e.ctrlKey && e.keyCode === 83) {
					e.preventDefault();
					$form.submit();
				}
				// Esc 取消
				if (e.keyCode === 27) {
					if (confirm('确定要取消编辑吗？未保存的更改将丢失。')) {
						history.back();
					}
				}
			});

			// 表单数据变化检测
			var originalFormData = $form.serialize();
			var hasUnsavedChanges = false;

			$('.projectpost-form-control, .richtext').on('input change', function() {
				hasUnsavedChanges = ($form.serialize() !== originalFormData);
			});

			// 页面离开提醒
			$(window).on('beforeunload', function() {
				if (hasUnsavedChanges) {
					return '您有未保存的更改，确定要离开吗？';
				}
			});

			// 成功提交后清除未保存标记
			$form.on('submit', function() {
				hasUnsavedChanges = false;
			});

			// 项目选择提示
			$('select[name="project_id"]').on('change', function() {
				var $select = $(this);
				var value = $select.val();
				var $helpText = $select.siblings('.projectpost-help-text');

				if (value && value !== '0') {
					$helpText.html('<i class="fa fa-check-circle" style="color: #48bb78;"></i> 已选择项目：' + $select.find('option:selected').text());
				} else {
					$helpText.text('选择该岗位所属的项目');
				}
			});

			// 公益性质选择提示
			$('select[name="is_free"]').on('change', function() {
				var $select = $(this);
				var value = $select.val();
				var $helpText = $select.siblings('.projectpost-help-text');

				if (value && value !== '0') {
					var selectedText = $select.find('option:selected').text();
					if (value == '1') {
						$helpText.html('<i class="fa fa-heart" style="color: #48bb78;"></i> 已选择：' + selectedText);
					} else {
						$helpText.html('<i class="fa fa-check-circle" style="color: #667eea;"></i> 已选择：' + selectedText);
					}
				} else {
					$helpText.text('选择该岗位是否为公益性质');
				}
			});

			// 字符计数功能
			$('input[name="job_name"]').on('input', function() {
				var $input = $(this);
				var value = $input.val();
				var $helpText = $input.siblings('.projectpost-help-text');
				var length = value.length;

				if (length > 0) {
					$helpText.html('岗位名称长度：' + length + ' 个字符 <i class="fa fa-check-circle" style="color: #48bb78;"></i>');
				} else {
					$helpText.text('岗位的完整名称，将显示在岗位列表中');
				}
			});

			// 富文本编辑器字符计数
			$('.richtext').on('input', function() {
				var $textarea = $(this);
				var value = $textarea.val();
				var $helpText = $textarea.closest('.projectpost-form-group').find('.projectpost-help-text');
				var length = value.length;

				if (length > 0) {
					$helpText.html('描述长度：' + length + ' 个字符。支持富文本格式，可以包含岗位职责、工作内容、福利待遇等信息');
				} else {
					$helpText.text('详细的岗位描述，支持富文本格式，可以包含岗位职责、工作内容、福利待遇等信息');
				}
			});
		});
	});
</script>

<include file="block/footer" />