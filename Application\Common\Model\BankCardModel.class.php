<?php
namespace Common\Model;

use Think\Model;

class BankCardModel extends Model
{
    protected $_auto = [
        ['created_at', 'time', self::MODEL_INSERT, 'function'],
        ['updated_at', 'time', self::MODEL_BOTH, 'function'],
        // 如果后台添加时需要记录管理员ID
        // ['added_by_admin_id', 'getAdminId', self::MODEL_INSERT, 'callback'], 
    ];

    // 可以根据需要添加验证规则
    // protected $_validate = [
    //     ['card_number', 'require', '银行卡号不能为空！'],
    //     ['account_holder', 'require', '开户人姓名不能为空！'],
    //     ['bank_name', 'require', '银行名称不能为空！'],
    // ];

    public $status = [
        '1' => ['text' => '可用', 'style' => 'success'],
        '0' => ['text' => '禁用', 'style' => 'danger'],
    ];

    // // 回调方法示例：获取当前登录的后台管理员ID
    // protected function getAdminId(){
    //     return session('admin_id'); // 假设后台管理员ID存储在session中
    // }
} 