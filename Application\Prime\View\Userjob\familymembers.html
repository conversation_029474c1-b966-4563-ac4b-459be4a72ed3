<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Userjob/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="panel panel-info">
                    <div class="panel-body">
                       简历人：{:$userJobRow['name']}
                    </div>
                </div>
                <form action="" method="post" >
                    <div class="panel panel-default">
                        <div class="panel-body table-responsive">
                            <table class="table table-hover">
                                <thead class="navbar-inner">
                                    <tr>
                                        <th width="75px">#ID</th>
                                        <th>亲属关系</th>
                                        <th>姓名</th>
                                        <th>工作单位</th>
                                        <th>职务</th>
                                        <th>联系电话</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($list as $v) { </php>
                                    <tr>
                                        <td>{$v.id}</td>
                                        <td>{:$v['relationship']}</td>
                                        <td>{:$v['full_name']}</td>
                                        <td>{:$v['work_unit']}</td>
                                        <td>{:$v['position']}</td>
                                        <td>{:$v['contact']}</td>
                                        <td>
                                            <a href="{:U('userjob/familymembersedit', ['id' => $v['id']])}" class="btn btnc btn-primary">编辑</a><br>
                                        </td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />

<script>
	require(["daterangepicker"], function($){

		$('.js_status').click(function() {
			var that = $(this),
				url = that.attr('url');
			$.get(url, function(data) {
				window.location.reload();
			});

		})
	});
</script>
