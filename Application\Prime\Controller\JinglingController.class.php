<?php
namespace Prime\Controller;
use Think\Controller;
use Common\Controller\PrimeController;

/**
 * 灵工绑定管理控制器
 */
class JinglingController extends PrimeController {
    
    /**
     * 初始化方法
     */
    public function _initialize() {
        parent::_initialize();
        // 不再调用checkRule方法
    }
    
    /**
     * 绑定记录列表
     */
    public function index() {
        $model = D('LinggongBinding');
        $status = I('get.status', '');
        $keyword = I('get.keyword', '');
        $platformType = I('get.platform_type', '');
        $where = [];
        
        // 根据状态筛选
        if ($status !== '') {
            $where['status'] = (int)$status;
        }
        
        // 根据平台类型筛选
        if ($platformType !== '') {
            $where['platform_type'] = (int)$platformType;
        }
        
        // 关键词搜索
        if (!empty($keyword)) {
            $where['_complex'] = [
                'worker_name' => ['like', "%{$keyword}%"],
                'id_card' => ['like', "%{$keyword}%"],
                'phone' => ['like', "%{$keyword}%"],
                '_logic' => 'OR'
            ];
        }
        
        // 分页查询
        $count = $model->where($where)->count();
        $page = $this->page($count, 15);
        $list = $model->where($where)
                      ->order('id DESC')
                      ->limit($page->firstRow, $page->listRows)
                      ->select();
        
        // 关联用户和服务站信息
        if ($list) {
            $userModel = D('User');
            $stationModel = D('ServiceStation');
            
            foreach ($list as &$item) {
                // 获取用户信息
                $userInfo = $userModel->where(['id' => $item['user_id']])->field('id, nickname, mobile')->find();
                $item['user_info'] = $userInfo ?: [];
                
                // 获取服务站信息
                $stationInfo = $stationModel->where(['id' => $item['service_station_id']])->field('id, service_name')->find();
                $item['station_info'] = $stationInfo ?: [];
                
                // 格式化时间
                $item['create_time_format'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['update_time_format'] = date('Y-m-d H:i:s', $item['update_time']);
            }
        }
        
        $this->assign('status_list', $model->status);
        $this->assign('platform_types', $model->platformTypes);
        $this->assign('list', $list);
        $this->assign('page', $page->show());
        $this->assign('status', $status);
        $this->assign('platform_type', $platformType);
        $this->assign('keyword', $keyword);
        
        $this->display();
    }
    
    /**
     * 审核绑定记录
     */
    public function review() {
        $id = I('get.id', 0, 'intval');
        $model = D('LinggongBinding');
        
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        // 获取绑定记录
        $binding = $model->where(['id' => $id])->find();
        if (!$binding) {
            $this->error('绑定记录不存在');
        }
        
        // 添加设置当前菜单的代码
        $menu = D("Menu")->where(['model' => 'Jingling', 'action' => 'index'])->find();
        if ($menu) {
            $cur_menu = D("Menu")->curMenu(1);
            $cur_menu['id'] = $menu['id'];
            $cur_menu['parentid'] = $menu['parentid'];
            $cur_menu['boot_id'] = D("Menu")->getBoot($menu['parentid']);
            $this->assign("cur_menu", $cur_menu);
        }
        
        if (IS_POST) {
            $status = I('post.status', 0, 'intval');
            $remark = I('post.remark', '', 'trim');
            
            if (!in_array($status, [1, 2])) {
                $this->error('状态参数错误');
            }
            
            // 更新状态
            $result = $model->reviewBinding($id, $status, $remark);
            if ($result) {
                $this->success('审核完成', U('Jingling/index'));
            } else {
                $this->error('审核失败，请重试');
            }
        }
        
        // 关联用户和服务站信息
        $userModel = D('User');
        $stationModel = D('ServiceStation');
        
        // 获取用户信息
        $userInfo = $userModel->where(['id' => $binding['user_id']])->find();
        $binding['user_info'] = $userInfo ?: [];
        
        // 获取服务站信息
        $stationInfo = $stationModel->where(['id' => $binding['service_station_id']])->find();
        $binding['station_info'] = $stationInfo ?: [];
        
        $this->assign('binding', $binding);
        $this->assign('status_list', $model->status);
        $this->assign('platform_types', $model->platformTypes);
        
        $this->display();
    }
    
    /**
     * 删除绑定记录
     */
    public function delete() {
        $id = I('get.id', 0, 'intval');
        $model = D('LinggongBinding');
        
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        $result = $model->where(['id' => $id])->delete();
        if ($result) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败，请重试');
        }
    }
} 