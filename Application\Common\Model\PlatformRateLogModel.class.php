<?php
namespace Common\Model;

use Think\Model;

/**
 * 平台费率修改日志模型
 */
class PlatformRateLogModel extends Model
{
    protected $tableName = 'platform_rate_log';
    
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
    ];

    /**
     * 执行类型定义（仅支持立即执行）
     */
    public $executeTypes = [
        1 => '立即执行'
    ];

    /**
     * 执行状态定义
     */
    public $executeStatus = [
        1 => '执行中',
        2 => '成功',
        3 => '失败'
    ];

    /**
     * 获取执行类型文本
     * @param int $type 执行类型
     * @return string
     */
    public function getExecuteTypeText($type)
    {
        return isset($this->executeTypes[$type]) ? $this->executeTypes[$type] : '未知';
    }

    /**
     * 获取执行状态文本
     * @param int $status 执行状态
     * @return string
     */
    public function getExecuteStatusText($status)
    {
        return isset($this->executeStatus[$status]) ? $this->executeStatus[$status] : '未知';
    }

    /**
     * 获取费率修改历史
     * @param int $limit 限制数量
     * @return array
     */
    public function getRateChangeHistory($limit = 10)
    {
        $list = $this->order('create_time DESC')
            ->limit($limit)
            ->select();
        
        if (!empty($list)) {
            foreach ($list as &$item) {
                $item['execute_type_text'] = $this->getExecuteTypeText($item['execute_type']);
                $item['execute_status_text'] = $this->getExecuteStatusText($item['execute_status']);
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['execute_time_text'] = $item['execute_time'] ? date('Y-m-d H:i:s', $item['execute_time']) : '';
                $item['scheduled_time_text'] = $item['scheduled_time'] ? date('Y-m-d H:i:s', $item['scheduled_time']) : '';
                $item['old_rate_percent'] = ($item['old_rate'] * 100) . '%';
                $item['new_rate_percent'] = ($item['new_rate'] * 100) . '%';
            }
        }
        
        return $list ?: [];
    }

    /**
     * 获取当前正在执行的任务
     * @return array|false
     */
    public function getRunningTask()
    {
        return $this->where([
            'execute_status' => 1
        ])->order('create_time DESC')->find();
    }

    /**
     * 获取最近的成功任务
     * @return array|false
     */
    public function getLastSuccessTask()
    {
        return $this->where([
            'execute_status' => 2
        ])->order('execute_time DESC')->find();
    }

    /**
     * 统计任务执行情况
     * @param int $days 统计天数，默认30天
     * @return array
     */
    public function getTaskStats($days = 30)
    {
        $startTime = time() - ($days * 24 * 3600);
        
        $stats = [
            'total' => 0,
            'success' => 0,
            'failed' => 0,
            'running' => 0
        ];
        
        $list = $this->where([
            'create_time' => ['egt', $startTime]
        ])->field('execute_status, COUNT(*) as count')
        ->group('execute_status')
        ->select();
        
        foreach ($list as $item) {
            $stats['total'] += $item['count'];
            switch ($item['execute_status']) {
                case 1:
                    $stats['running'] = $item['count'];
                    break;
                case 2:
                    $stats['success'] = $item['count'];
                    break;
                case 3:
                    $stats['failed'] = $item['count'];
                    break;
            }
        }
        
        return $stats;
    }
}
