<include file="block/hat" />

<style>
    /* 保持原有CSS样式不变 */
    .form-container { max-width: 1200px; margin: 20px auto; font-family: 'Microsoft YaHei', sans-serif; font-size: 14px; }
    .form-section { border: 1px solid #666; margin-bottom: 15px; padding: 10px; background: #f9f9f9; }
    .form-row { display: flex; margin: 8px 0; min-height: 35px; align-items: stretch; }
    .form-field { flex: 1; display: flex; align-items: center; margin: 0 2px; border-bottom: 1px solid #ccc; position: relative; }
    .form-label { min-width: 80px; padding: 0 10px; font-weight: bold; white-space: nowrap; background: #f0f0f0; }
    .form-content { flex: 1; padding: 5px 10px; min-height: 30px; }
    .vertical-text { writing-mode: vertical-rl; text-orientation: upright; padding: 5px; }
    .photo-field { width: 120px; height: 160px; border: 1px dashed #999; text-align: center; line-height: 160px; flex-shrink: 0; }
    .col-4 { flex: 4; } .col-3 { flex: 3; } .col-2 { flex: 2; } .col-1 { flex: 1; }
    .signature-area { padding: 20px; line-height: 1.8; }
</style>
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Userjob/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading" style="text-align: center;">{:$userJob['name']}应聘人员报名表</div>
                    <div class="panel-body">
    <div class="form-container">
    
        <!-- 基本信息 -->
        <div class="form-section">
            <div style="width: 85%;float: left;"> 
            <div class="form-row">
                <div class="form-field col-1">
                    <span class="form-label">姓名</span>
                    <div class="form-content">{:$userJob['name']}</div>
                </div>
                <div class="form-field col-1">
                    <span class="form-label">性别</span>
                    <div class="form-content">{:$userJob['gender'] == '男' ? '男' : '女'}</div>
                </div>
                <div class="form-field col-1">
                    <span class="form-label">出生年月</span>
                    <div class="form-content">{:date('Y-m', strtotime($userJob['birthday']))}</div>
                </div>
  
            </div>

            <!-- 其他基本信息字段 -->
            <div class="form-row">
                <div class="form-field col-1">
                    <span class="form-label">民族</span>
                    <div class="form-content">{:$userJob['nation']}</div>
                </div>
                <div class="form-field col-1">
                    <span class="form-label">籍贯</span>
                    <div class="form-content">{:$userJob['hometown']}</div>
                </div>
                <div class="form-field col-1">
                    <span class="form-label">政治面貌</span>
                    <div class="form-content">{:$userJob['political_status']}</div>
                </div>
              
            </div>

                        <!-- 更多字段... -->
             
                        <div class="form-row">
                            <div class="form-field col-1">
                                <span class="form-label">婚否</span>
                                <div class="form-content">{:$userJob['marital_status']}</div>
                            </div>
                            <div class="form-field col-2">
                                <span class="form-label">健康状况</span>
                                <div class="form-content">{:$userJob['health_status']}</div>
                            </div>
                            <div class="form-field col-1">
                                <span class="form-label">身高</span>
                                <div class="form-content">{:$userJob['height']}</div>
                            </div>
                        </div>
            
                        <div class="form-row">
                            <div class="form-field col-1">
                                <span class="form-label">体重</span>
                                <div class="form-content">{:$userJob['weight']}</div>
                            </div>
                            <div class="form-field col-1">
                                <span class="form-label">视力</span>
                                <div class="form-content">{:$userJob['vision']}</div>
                            </div>
                            <div class="form-field col-1">
                                <span class="form-label">听力</span>
                                <div class="form-content">{:$userJob['hearing']}</div>
                            </div>
                        </div>

            </div>
            <div style="width:15%;"  class="form-field col-4">

                
                    <div class="photo-field" style="margin-left: 28px;">
                        <php> if(!empty($userJob['photo_path'])){</php>
                            <img src="{:$userJob['photo_path']}" style="max-width:100%; max-height:100%;">
                        <php>}else{</php>
                            照片（电子版）
                        <php>}</php>
                    </div>
                </div>

            </div>
     

       

        <!-- 学历信息区块 -->
        <div class="form-section">
            <div class="form-row">
                <div class="form-field col-2">
                    <span class="form-label">学历</span>
                    <div class="form-content">{:$userJob['education_level']}</div>
                </div>
                <div class="form-field col-2">
                    <span class="form-label">专业技术职称</span>
                    <div class="form-content">{:$userJob['Professional']}</div>
                </div>
                <div class="form-field col-1">
                    <span class="form-label">是否恐高</span>
                    <div class="form-content">{:$userJob['is_afraid_heights']}</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-field col-3">
                    <span class="form-label">毕业院校</span>
                    <div class="form-content">{:$userJob['graduate_school']}</div>
                </div>
                <div class="form-field col-2">
                    <span class="form-label">所学专业</span>
                    <div class="form-content">{:$userJob['major']}</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-field col-2">
                    <span class="form-label">毕业时间</span>
                    <div class="form-content">{:$userJob['graduation_time']}</div>
                </div>
                <div class="form-field col-3">
                    <span class="form-label">参加工作时间</span>
                    <div class="form-content">{:$userJob['Time_to_work']}</div>
                </div>
            </div>
        </div>

        <!-- 身份信息区块 -->
        <div class="form-section">
            <div class="form-row">
                <div class="form-field col-3">
                    <span class="form-label">身份证号码</span>
                    <div class="form-content">{:$userJob['id_number']}</div>
                </div>
                <div class="form-field col-2">
                    <span class="form-label">户籍地</span>
                    <div class="form-content">{:$userJob['Registered']}</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-field col-4">
                    <span class="form-label">求职意向</span>
                    <div class="form-content">{:$userJob['Job_Intention']}</div>
                </div>
                <div class="form-field col-3">
                    <span class="form-label">现工作单位及职务</span>
                    <div class="form-content">{:$userJob['company']} {:$userJob['Intention']}</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-field col-3">
                    <span class="form-label">电子邮箱</span>
                    <div class="form-content">{:$userJob['email']}</div>
                </div>
                <div class="form-field col-2">
                    <span class="form-label">联系电话</span>
                    <div class="form-content">{:$userJob['phone']}</div>
                </div>
            </div>
        </div>

 

        <!-- 教育经历 -->
        <div class="form-section">
            <h3>教育经历（高中填起）  <a href="{:U('userjob/education', ['job_id' => $userJob['id']])}" class="btn btnc btn-primary">编辑/添加</a></h3>
            <div class="form-row header">
                <div class="form-field col-1"><span class="form-label">起止年月</span></div>
                <div class="form-field col-3"><span class="form-label">学校名称</span></div>
                <div class="form-field col-2"><span class="form-label">专业</span></div>
                <div class="form-field col-1"><span class="form-label">证明人</span></div>
            </div>
            <php> foreach($educationList as $edu){</php>
            <div class="form-row">
                <div class="form-field col-1">
                    <div class="form-content">
                        {:date('Y.m', strtotime($edu['start_date']))} - {:date('Y.m', strtotime($edu['end_date']))}
                    </div>
                </div>
                <div class="form-field col-3">
                    <div class="form-content">{:$edu['school_name']}</div>
                </div>
                <div class="form-field col-2">
                    <div class="form-content">{:$edu['major']}</div>
                </div>
                <div class="form-field col-1">
                    <div class="form-content">{:$edu['witness']}</div>
                </div>
            </div>
            <php>}</php>
        </div>

        <!-- 工作经历 -->
        <div class="form-section">
            <h3>工作经历  <a href="{:U('userjob/workexperience', ['job_id' => $userJob['id']])}" class="btn btnc btn-primary">编辑/添加</a></h3>
            <div class="form-row header">
                <div class="form-field col-1"><span class="form-label">起止年月</span></div>
                <div class="form-field col-3"><span class="form-label">单位名称</span></div>
                <div class="form-field col-2"><span class="form-label">职位</span></div>
                <div class="form-field col-1"><span class="form-label">离职原因</span></div>
            </div>
            <php> foreach($workExperience as $work){</php>
            <div class="form-row">
                <div class="form-field col-1">
                    <div class="form-content">
                        {:date('Y.m', strtotime($work['start_date']))} - {:date('Y.m', strtotime($work['end_date']))}
                    </div>
                </div>
                <div class="form-field col-3">
                    <div class="form-content">{:$work['company_name']}</div>
                </div>
                <div class="form-field col-2">
                    <div class="form-content">{:$work['position']}</div>
                </div>
                <div class="form-field col-1">
                    <div class="form-content">{:$work['leave_reason']}</div>
                </div>
            </div>
            <php>}</php>
        </div>

        <!-- 家庭成员 -->
        <div class="form-section">
            <h3>家庭成员  <a href="{:U('userjob/familymembers', ['job_id' => $userJob['id']])}" class="btn btnc btn-primary">编辑/添加</a></h3>
            <div class="form-row header">
                <div class="form-field col-1"><span class="form-label">关系</span></div>
                <div class="form-field col-2"><span class="form-label">姓名</span></div>
                <div class="form-field col-3"><span class="form-label">工作单位</span></div>
                <div class="form-field col-2"><span class="form-label">职务</span></div>
                <div class="form-field col-1"><span class="form-label">联系电话</span></div>
            </div>
            <php> foreach($familyMembers as $member){</php>
            <div class="form-row">
                <div class="form-field col-1">
                    <div class="form-content">{:$member['relationship']}</div>
                </div>
                <div class="form-field col-2">
                    <div class="form-content">{:$member['full_name']}</div>
                </div>
                <div class="form-field col-3">
                    <div class="form-content">{:$member['work_unit']}</div>
                </div>
                <div class="form-field col-2">
                    <div class="form-content">{:$member['position']}</div>
                </div>
                <div class="form-field col-1">
                    <div class="form-content">{:$member['contact']}</div>
                </div>
            </div>
            <php>}</php>
        </div>


        
        <!-- 其他信息 -->
        <div class="form-section">
            <div class="form-row">
                <div class="form-field col-2">
                    <span class="form-label">是否服从调剂</span>
                    <div class="form-content"></div>
                </div>
                <div class="form-field col-2">
                    <span class="form-label">外语水平</span>
                    <div class="form-content"></div>
                </div>
                <div class="form-field col-2">
                    <span class="form-label">计算机水平</span>
                    <div class="form-content"></div>
                </div>
                <div class="form-field col-2">
                    <span class="form-label">普通话水平</span>
                    <div class="form-content"></div>
                </div>
            </div>

        
        <!-- 技能证书 -->
       
            <div class="form-row">
                <div class="form-field col-6">
                    <span class="form-label">专业特长/获奖证书  <a href="{:U('userjob/skillscertificates', ['job_id' => $userJob['id']])}" class="btn btnc btn-primary">编辑/添加</a></span>
                    <div class="form-content">
                        <php> foreach($skillsCertificates as $cert){</php>
                        <p>▪ {:$cert['certificate_name']} {:$cert['get_date']}</p>
                        <php>}</php>
                    </div>
                </div>
            </div>
        

            <div class="form-row">
                <div class="form-field col-3">
                    <span class="form-label">受过奖励</span>
                    <div class="form-content"></div>
                </div>
                <div class="form-field col-3">
                    <span class="form-label">受过处分</span>
                    <div class="form-content"></div>
                </div>
            </div>
        </div>


 
    </div>

    </div>
            </div>
        </div>
    </div></div>
</div>
<include file="block/footer" />
 
