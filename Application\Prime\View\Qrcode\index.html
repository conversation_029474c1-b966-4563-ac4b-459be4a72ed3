<include file="block/hat"/>
<div class="container-fluid">
    <div class="row">
        <include file="block/menu"/>
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a href="{:U('qrcode/index')}">服务站二维码列表</a></li>
            </ul>
            <style type='text/css'>.tab-pane {
                padding: 20px 0 20px 0;
            }</style>
            <div class="main">
                <div class="panel panel-info">
                    <!--<div class="panel-heading">筛选</div>-->
                    <div class="panel-body">
                        <form method="get" class="form-inline" role="form" id="form1">
                            <div class="form-group">
                                <select class="form-control" name="kw1">
                                    <php>foreach($c_kw1 as $key=>$value){</php>
                                    <option value="{$key}"
                                    <php> if($key == $_get['kw1']){</php>
                                    selected
                                    <php>}</php>
                                    >{$value}</option>
                                    <php>}</php>
                                </select>=<input class="form-control" type="text" name="val1" value="{$_get.val1}"/>
                            </div>

                            <button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> 搜索</button>
                            <a href="{:U('qrcode/index')}" class="btn btn-warning">重置</a>
                        </form>
                    </div>
                </div>
                <form action="" method="post">
                    <div class="panel panel-default">
                        <div class="panel-body table-responsive">
                            <table class="table table-hover">
                                <thead class="navbar-inner">
                                <tr>
                                    <th>所属服务号</th>
                                    <th>所属服务站</th>
                                    <th>查看二维码</th>
                                </tr>
                                </thead>
                                <tbody>
                                <php>foreach($list as $v) {</php>
                                <tr>
                                    <td>
                                       {:$serviceStationList[$v['service_station_id']]}
                                    </td>
                                    <td>  {:$serviceList[$v['service_id']]}</td>
                                    <td>
                                        <a class="btn btnc btn-primary qrcode"
                                           data-desc="服务号：{:$serviceList[$v['service_id']]} | 场景值：{$v.scene_id}"
                                           data-ticket="{$v.ticket|urlencode}">二维码</a><br>
                                    </td>
                                </tr>
                                <php>}</php>
                                </tbody>
                            </table>
                            {$page}
                        </div>
                    </div>
            </div>
            </form>

        </div>
    </div>
</div>
<script>
    require(["daterangepicker", "layer"], function ($) {
        $(function () {
            $(".daterange.daterange-time").each(function () {
                var elm = this;
                $(this).daterangepicker({
                    startDate: $(elm).prev().prev().val(),
                    endDate: $(elm).prev().val(),
                    format: "YYYY-MM-DD HH:mm",
                    timePicker: false,
                    timePicker12Hour: false,
                    timePickerIncrement: 1,
                    minuteStep: 1
                }, function (start, end) {
                    $(elm).find(".date-title").html(start.toDateTimeStr() + " 至 " + end.toDateTimeStr());
                    $(elm).prev().prev().val(start.toDateTimeStr());
                    $(elm).prev().val(end.toDateTimeStr());
                });
            });
            $('[data-toggle="tooltip"]').tooltip();
            $('.qrcode').click(function () {
                var ticket = $(this).data('ticket');
                layer.open({
                    type: 1,
                    title: $(this).data('desc'),
                    closeBtn: 0,
                    area: ['400px', '442px'],
                    shadeClose: true,
                    content: '<img width="400" src="https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=' + ticket + '" />'
                });
            });
        });
    });
</script>
<include file="block/footer"/>