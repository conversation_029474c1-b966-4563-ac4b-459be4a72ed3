<!DOCTYPE html>
<html>
<head>
<title>批量岗位价格配置</title>
<meta name="Keywords" content="关键字">
<meta name="Description" content="内容">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
<meta content="no-cache,must-revalidate" http-equiv="Cache-Control">
<meta content="telephone=no, address=no" name="format-detection">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta content="no-cache" http-equiv="pragma">
<meta content="0" http-equiv="expires">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"> 
<link rel="stylesheet" type="text/css" href="/static/stations/css/css.css?v={:time()}" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
<script src="/static/stations/js/jquery-1.9.1.min.js"></script>
<script src="/static/stations/js/swiper-bundle.min.js"></script>
<style>
/* 重置和基础样式 */
body {
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    max-width: none !important;
    width: 100% !important;
}

.bg01, .topbar, .nav {
    max-width: none !important;
}

[style*="display: none"], [style*="display:none"] {
    display: none !important;
}

.hidden, .hide {
    display: none !important;
}

/* 批量配置页面专用样式 */
body.batch-config-page {
    overflow-x: hidden;
}

body.batch-config-page::after {
    content: "";
    display: block;
    clear: both;
    height: 0;
}

.topbar .weap, .nav .weap {
    max-width: 750px;
    margin: 0 auto;
    padding-left: 10px;
    padding-right: 10px;
}

.notice {
    max-width: none !important;
    height: auto;
    overflow: visible;
}

.notice .weap {
    max-width: 750px;
    margin: 0 auto;
    padding-left: 10px;
    padding-right: 10px;
    height: auto;
}

.batch-config-container .weap {
    max-width: 1400px;
    margin: 0 auto;
    padding-left: 20px;
    padding-right: 20px;
}

/* 确保公告栏的Swiper正常显示 */
.noticeSwiper {
    width: 100%;
    max-width: none;
    height: 40px !important;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 5px rgba(0,0,0,.1);
    overflow: hidden !important;
    position: relative;
}

.noticeSwiper .swiper-wrapper {
    width: 100%;
    height: 40px;
}

.noticeSwiper .swiper-slide {
    width: 100%;
    height: 40px !important;
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.noticeSwiper .swiper-slide .a {
    font-size: 13px;
    color: #f36722;
    margin-left: 35px;
    display: block;
    line-height: 40px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding-right: 50px;
    position: relative;
    width: 100%;
}

.noticeSwiper .icon-gonggao {
    position: absolute;
    z-index: 2;
    left: 5px;
    top: 0;
    font-size: 24px;
    display: block;
    line-height: 40px;
    color: #f36722;
}

.noticeSwiper .swiper-slide .icon-jinrujiantouxiao {
    font-size: 12px;
    color: #999;
    position: absolute;
    display: block;
    z-index: 2;
    right: 10px;
    top: 0;
    line-height: 40px;
}

.batch-config-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 200px);
    background-color: #f5f5f5;
    width: 95%;
    position: relative;
    z-index: 1;
}

.step-card {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.step-card:hover {
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.step-card h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
}

.step-card h3 i {
    color: #007bff;
    font-size: 20px;
}

.step-card select {
    width: 100%;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    background-color: #fff;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 12px;
}

.step-card select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* 招就办选择区域样式 */
.zsb-selection-area {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.zsb-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.zsb-item {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.zsb-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

.zsb-item.selected {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.zsb-item input[type="checkbox"] {
    position: absolute;
    top: 10px;
    right: 10px;
    transform: scale(1.2);
}

.zsb-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.zsb-contact {
    font-size: 14px;
    color: #666;
}

/* 价格配置区域样式 */
.price-config-area {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-col {
    flex: 1;
}

.form-col label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: bold;
    font-size: 14px;
}

.form-col input, .form-col select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-col input:focus, .form-col select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* 价格输入组样式 */
.price-input-group {
    position: relative;
    display: flex;
    flex-direction: column;
}

.price-format-hint {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    line-height: 1.3;
}

.price-format-hint.active {
    color: #007bff;
    font-weight: 500;
}

.formatted-result {
    font-size: 11px;
    color: #28a745;
    margin-top: 2px;
    font-weight: 500;
}

.formatted-result.warning {
    color: #ffc107;
}

/* 价格信息面板样式 */
.price-info-panel {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.commission-info, .platform-fee-info, .station-profit-info {
    margin-bottom: 10px;
}

.commission-amount, .platform-fee-amount, .station-profit-amount {
    font-size: 18px;
    font-weight: bold;
}

.commission-label, .platform-fee-label, .station-profit-label {
    font-size: 14px;
    color: #6c757d;
}

.calculation-note {
    font-size: 12px;
    color: #6c757d;
    border-top: 1px solid #dee2e6;
    padding-top: 10px;
    line-height: 1.5;
}

.btn-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.btn-primary {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-primary:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

/* 选中统计样式 */
.selection-summary {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 6px;
    padding: 10px 15px;
    margin-top: 15px;
    font-weight: bold;
    color: #1976d2;
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        max-width: 750px !important;
    }

    .batch-config-container {
        padding: 10px;
        width: 100%;
    }

    .batch-config-container .weap {
        max-width: none;
        padding: 0 10px;
    }

    .topbar .weap, .nav .weap, .notice .weap {
        padding-left: 10px;
        padding-right: 10px;
    }

    .step-card {
        padding: 15px;
        margin-bottom: 15px;
    }

    .step-card h3 {
        font-size: 16px;
    }

    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .btn-group {
        flex-direction: column;
        gap: 10px;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        padding: 15px;
        font-size: 16px;
    }

    .zsb-grid {
        grid-template-columns: 1fr;
    }
}

/* 返回按钮样式 */
.return-btn {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white !important;
    padding: 10px 18px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 3px 12px rgba(116, 185, 255, 0.4);
    transition: all 0.3s ease;
    border: none;
}

.return-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 16px rgba(116, 185, 255, 0.6);
    color: white !important;
}

.return-btn:active {
    transform: translateY(0);
}

.return-btn .btn-icon {
    font-size: 16px;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.return-btn:hover .btn-icon {
    transform: translateX(-2px);
}

.return-btn .btn-text {
    font-weight: 500;
    letter-spacing: 0.3px;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .return-btn {
        padding: 8px 14px;
        font-size: 13px;
        border-radius: 18px;
    }

    .return-btn .btn-icon {
        font-size: 14px;
    }
}

@media (min-width: 1200px) {
    .batch-config-container {
        max-width: 1600px;
    }
}
</style>
</head>
<body class="batch-config-page">
<include file="headers"/>

<div class="batch-config-container">
    <!-- 功能导航 -->
    <div class="step-card" style="margin-bottom: 15px;">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
            <h3 style="margin: 0;">🚀 批量岗位价格配置</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap; align-items: center;">
                <a href="{:U('index/price_config')}" class="return-btn" style="text-decoration: none; display: inline-flex; align-items: center; gap: 8px;">
                    <span class="btn-icon">←</span>
                    <span class="btn-text">返回单招就办配置</span>
                </a>
            </div>
        </div>
        <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">
            💡 <strong>功能说明：</strong>此页面用于先选择多个招就办，然后选择单个岗位，最后批量给这些招就办设置相同的价格配置。
        </p>
    </div>

    <!-- 第一步：选择招就办 -->
    <div class="step-card">
        <h3>🏢 第一步：选择招就办</h3>
        <p style="color: #666; margin-bottom: 15px;">请选择要配置价格的招就办（可多选）：</p>

        <div class="zsb-selection-area">
            <div style="margin-bottom: 10px;">
                <label style="cursor:pointer;user-select:none;">
                    <input type="checkbox" id="selectAllZsbCheckbox" style="vertical-align:middle;margin-right:6px;">全选
                </label>
            </div>
            <div class="zsb-grid" id="zsbGrid">
                <!-- 招就办列表将通过AJAX动态加载 -->
            </div>
            <div class="selection-summary" id="selectionSummary" style="display: none;">
                已选择 <span id="selectedCount">0</span> 个招就办
            </div>
        </div>
    </div>

    <!-- 第二步：选择岗位 -->
    <div class="step-card" id="postSelectionCard" style="display: none;">
        <h3>📋 第二步：选择岗位</h3>
        <select id="postSelect" onchange="onPostChange()">
            <option value="">请选择岗位</option>
        </select>
        <div id="postInfo" style="display: none; margin-top: 15px; padding: 15px; background: #e3f2fd; border-radius: 6px;">
            <!-- 岗位信息将在这里显示 -->
        </div>
    </div>

    <!-- 第三步：价格配置 -->
    <div class="step-card" id="priceConfigCard" style="display: none;">
        <h3>💰 第三步：价格配置</h3>
        <p style="color: #666; margin-bottom: 15px;">为选中的招就办和岗位设置统一的价格配置：</p>

        <div class="price-config-area">
            <form id="batchPriceForm">
                <!-- 第一行：成本价和对外价 -->
                <div class="form-row">
                    <div class="form-col">
                        <label>您给招就办的成本价（元）：</label>
                        <div class="price-input-group">
                            <input type="text" id="costPrice" name="cost_price" placeholder="请输入成本价，不得低于服务站成本价" oninput="validatePositiveIntegerWithFormatting(this)" onchange="calculateBatchCommission()" onblur="applyPriceFormatting(this)" onfocus="restoreInputOnFocus(this)">
                            <div class="price-format-hint">
                                <span style="font-size: 12px; color: #7f8c8d;">· 成本价不得低于服务站（您）的成本价</span><br>
                                · 价格将自动调整为千的整数倍（如：12345 → 12000）
                            </div>
                            <div class="formatted-result" style="display: none;"></div>
                        </div>
                    </div>
                    <div class="form-col">
                        <label>招就办对外展示服务费（元）：</label>
                        <div class="price-input-group">
                            <input type="text" id="salePrice" name="sale_price" placeholder="请输入对外价，不得超过报价区间的最大报价" oninput="validatePositiveIntegerWithFormatting(this)" onchange="calculateBatchCommission()" onblur="applyPriceFormatting(this)" onfocus="restoreInputOnFocus(this)">
                            <div class="price-format-hint">· 价格将自动调整为千的整数倍（如：12345 → 12000）</div>
                            <div class="formatted-result" style="display: none;"></div>
                        </div>
                    </div>
                </div>

                <!-- 价格信息显示区域 -->
                <div class="price-info-panel">
                    <!-- 佣金显示 -->
                    <div class="commission-info">
                        <div class="commission-amount" id="batchCommissionAmount" style="color: #28a745;">0</div>
                        <div class="commission-label">招就办主任提成（元）</div>
                    </div>

                    <!-- 平台服务费显示 -->
                    <div class="platform-fee-info">
                        <div class="platform-fee-amount" id="batchPlatformFeeAmount" style="color: #9b59b6;">0</div>
                        <div class="platform-fee-label">平台服务费（元）</div>
                    </div>

                    <!-- 您的收益显示 -->
                    <div class="station-profit-info">
                        <div class="station-profit-amount" id="batchStationProfitAmount" style="color: #28a745;">--</div>
                        <div class="station-profit-label">您的收益（元）</div>
                    </div>

                    <!-- 计算说明 -->
                    <div class="calculation-note">
                        <strong>计算公式：</strong><br>
                        招就办主任提成 = 对外价 - 成本价 - 服务费<br>
                        您的收益 = 成本价 - 服务站成本价<br>
                        服务费 = max(0, (对外价 - 报价区间最低报价)) × <span class="platform-rate-display">30.0%</span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <label>状态设置（是否在招就办后台显示该岗位）：</label>
                        <select id="priceStatus" name="status">
                            <option value="1">显示</option>
                            <option value="0">不显示</option>
                        </select>
                    </div>
                    <div class="form-col"></div>
                </div>

                <div class="btn-group">
                    <button type="button" class="btn-secondary" onclick="resetForm()">重置</button>
                    <button type="button" class="btn-primary" id="saveBtn" onclick="saveBatchConfig()" disabled>批量保存配置</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
var currentPostId = '';
var selectedZsbIds = []; // 存储选中的招就办ID（字符串类型）
var allZsbList = [];
var currentPostInfo = null;
var dynamicPlatformRate = 0.30; // 动态平台费率，默认值，页面加载时会更新

// 获取动态平台费率
function getDynamicPlatformRate() {
    $.ajax({
        url: '/index/get_platform_rate',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                dynamicPlatformRate = response.rate;
                console.log('动态平台费率已更新:', dynamicPlatformRate);
                updatePlatformRateDisplay();
            } else {
                console.warn('获取平台费率失败，使用默认值:', dynamicPlatformRate);
            }
        },
        error: function() {
            console.warn('获取平台费率网络错误，使用默认值:', dynamicPlatformRate);
        }
    });
}

// 更新页面上的平台费率显示
function updatePlatformRateDisplay() {
    var ratePercent = (dynamicPlatformRate * 100).toFixed(1) + '%';
    $('.platform-rate-display').text(ratePercent);
}

// 页面初始化
$(document).ready(function(){
    // 获取动态平台费率
    getDynamicPlatformRate();

    // 初始化公告栏轮播
    var noticeSwiper = new Swiper('.noticeSwiper', {
        direction: 'vertical',
        loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    });

    // 加载招就办列表
    loadZsbList();

    // 加载岗位列表
    loadPostList();
    
    // 清理页面底部可能的异常元素
    setTimeout(function() {
        var keepElements = [
            '.bg01', '.topbar', '.nav', '.notice',
            '.batch-config-container',
            'script', 'style', 'link', 'meta', 'title'
        ];

        $('body').children().each(function() {
            var $this = $(this);
            var shouldKeep = false;

            for (var i = 0; i < keepElements.length; i++) {
                if ($this.is(keepElements[i]) || $this.hasClass(keepElements[i].replace('.', ''))) {
                    shouldKeep = true;
                    break;
                }
            }

            if (!shouldKeep) {
                $this.css({
                    'display': 'none !important',
                    'visibility': 'hidden !important',
                    'opacity': '0 !important',
                    'position': 'absolute !important',
                    'left': '-9999px !important',
                    'top': '-9999px !important'
                });
            }
        });

        $('body').append('<div style="clear: both; height: 0; overflow: hidden;"></div>');
        console.log('页面清理完成');
    }, 200);
});

// 加载岗位列表
function loadPostList() {
    $.ajax({
        url: '{:U("index/getPostListAjax")}',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                var html = '<option value="">请选择岗位</option>';
                for (var i = 0; i < response.data.length; i++) {
                    var post = response.data[i];
                    html += '<option value="' + post.id + '">' + post.job_name + ' - ' + (post.project_name || '未知项目') + '</option>';
                }
                $('#postSelect').html(html);
            } else {
                alert('加载岗位列表失败：' + (response.msg || '未知错误'));
            }
        },
        error: function() {
            alert('网络错误，请重试');
        }
    });
}

// 岗位选择变化
function onPostChange() {
    var postId = $('#postSelect').val();
    currentPostId = postId;

    // 隐藏后续步骤
    $('#priceConfigCard').hide();
    $('#postInfo').hide();

    // 重置价格计算
    resetForm();

    if (!postId) {
        return;
    }

    // 获取岗位详细信息
    getPostInfo(postId);

    // 检查是否已选择招就办，如果有则显示价格配置
    checkShowPriceConfig();
}

// 获取岗位详细信息
function getPostInfo(postId) {
    $.ajax({
        url: '{:U("index/getPostInfoAjax")}',
        type: 'GET',
        data: { post_id: postId },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                currentPostInfo = response.data;
                displayPostInfo(response.data);
            } else {
                alert('获取岗位信息失败：' + (response.msg || '未知错误'));
            }
        },
        error: function() {
            alert('网络错误，请重试');
        }
    });
}

// 显示岗位信息
function displayPostInfo(postInfo) {
    var html = '';
    html += '<h4 style="margin: 0 0 10px 0; color: #1976d2;">' + postInfo.job_name + '</h4>';
    html += '<p style="margin: 5px 0; color: #666;"><strong>所属项目：</strong>' + (postInfo.project_name || '-') + '</p>';
    html += '<p style="margin: 5px 0; color: #666;"><strong>服务费报价区间：</strong>' + formatMoney(postInfo.service_price) + '-' + formatMoney(postInfo.max_price) + ' 元</p>';

    if (postInfo.base_cost && postInfo.base_cost > 0) {
        html += '<p style="margin: 5px 0; color: #e67e22;"><strong>服务站成本价：</strong>' + formatMoney(postInfo.base_cost) + ' 元</p>';
    }

    // 显示平台服务费说明
    html += '<p style="margin: 5px 0; font-size: 12px; color: #9b59b6;"><strong>平台服务费：</strong>超出最低报价部分的<span class="platform-rate-display">' + (dynamicPlatformRate * 100).toFixed(1) + '%</span></p>';
    html += '<p style="margin: 5px 0; font-size: 12px; color: #f39c12; line-height: 1.4;">服务费由招就办承担，超出报价区间最低部分的费用，由招就办主任承担</p>';

    $('#postInfo').html(html).show();

    // 更新价格配置区域的平台费率显示
    updatePlatformRateDisplay();
}

// 加载招就办列表
function loadZsbList() {
    $.ajax({
        url: '{:U("index/getZsbListAjax")}',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                allZsbList = response.data;
                renderZsbList(response.data);
                // 招就办列表已经在第一步显示，不需要额外显示
            } else {
                alert('加载招就办列表失败：' + (response.msg || '未知错误'));
            }
        },
        error: function() {
            alert('网络错误，请重试');
        }
    });
}

// 渲染招就办列表
function renderZsbList(zsbList) {
    var html = '';

    if (zsbList.length === 0) {
        html = '<div style="text-align: center; padding: 40px; color: #666;">暂无可用的招就办</div>';
    } else {
        for (var i = 0; i < zsbList.length; i++) {
            var zsb = zsbList[i];
            // 确保ID是字符串类型进行比较
            var zsbIdStr = String(zsb.id);
            var isSelected = selectedZsbIds.indexOf(zsbIdStr) !== -1;
            var itemClass = isSelected ? 'zsb-item selected' : 'zsb-item';

            html += '<div class="' + itemClass + '" data-zsb-id="' + zsb.id + '">';
            html += '<input type="checkbox" id="zsb_' + zsb.id + '" ' + (isSelected ? 'checked' : '') + ' data-zsb-id="' + zsb.id + '">';
            html += '<div class="zsb-name">' + zsb.service_name + '</div>';
            html += '<div class="zsb-contact">联系人：' + zsb.contract_name + '</div>';
            html += '<div class="zsb-contact">手机：' + zsb.mobile + '</div>';
            html += '</div>';
        }
    }

    $('#zsbGrid').html(html);

    // 渲染后，更新全选框状态
    updateSelectAllZsbCheckbox();
}

// 切换招就办选择状态
function toggleZsbSelection(zsbId) {
    // 确保ID是字符串类型
    var zsbIdStr = String(zsbId);
    var index = selectedZsbIds.indexOf(zsbIdStr);

    if (index === -1) {
        selectedZsbIds.push(zsbIdStr);
    } else {
        selectedZsbIds.splice(index, 1);
    }

    console.log('当前选中的招就办IDs:', selectedZsbIds); // 调试信息

    // 重新渲染招就办列表
    renderZsbList(allZsbList);

    // 更新选择统计
    updateSelectionSummary();

    // 更新保存按钮状态
    updateSaveButton();

    // 检查是否显示岗位选择和价格配置
    checkShowPostSelection();
    checkShowPriceConfig();
}

// 全选/取消全选功能
$(document).on('change', '#selectAllZsbCheckbox', function() {
    var isChecked = $(this).prop('checked');
    if (isChecked) {
        // 全选
        selectedZsbIds = allZsbList.map(function(zsb) { return String(zsb.id); });
    } else {
        // 取消全选
        selectedZsbIds = [];
    }
    renderZsbList(allZsbList);
    updateSelectionSummary();
    updateSaveButton();
    checkShowPostSelection();
    checkShowPriceConfig();
});
// 招就办单选/多选后，自动更新全选框状态
function updateSelectAllZsbCheckbox() {
    var total = allZsbList.length;
    var selected = selectedZsbIds.length;
    var $checkbox = $('#selectAllZsbCheckbox');
    if (selected === 0) {
        $checkbox.prop('checked', false).prop('indeterminate', false);
    } else if (selected === total && total > 0) {
        $checkbox.prop('checked', true).prop('indeterminate', false);
    } else {
        $checkbox.prop('checked', false).prop('indeterminate', true);
    }
}

// 检查是否显示岗位选择
function checkShowPostSelection() {
    if (selectedZsbIds.length > 0) {
        $('#postSelectionCard').show();
    } else {
        $('#postSelectionCard').hide();
        $('#priceConfigCard').hide();
    }
}

// 检查是否显示价格配置
function checkShowPriceConfig() {
    if (selectedZsbIds.length > 0 && currentPostId) {
        $('#priceConfigCard').show();
    } else {
        $('#priceConfigCard').hide();
    }
}

// 使用事件委托处理复选框和卡片点击
$(document).on('click', '.zsb-item', function(e) {
    // 如果点击的是复选框，不处理
    if (e.target.type === 'checkbox') {
        return;
    }

    var zsbId = $(this).data('zsb-id');
    console.log('点击卡片，招就办ID:', zsbId); // 调试信息

    // 触发选择状态变化
    toggleZsbSelection(zsbId);
});

// 处理复选框直接点击
$(document).on('change', '.zsb-item input[type="checkbox"]', function() {
    var zsbId = $(this).data('zsb-id');
    console.log('点击复选框，招就办ID:', zsbId); // 调试信息
    toggleZsbSelection(zsbId);
});

// 更新选择统计
function updateSelectionSummary() {
    if (selectedZsbIds.length > 0) {
        $('#selectedCount').text(selectedZsbIds.length);
        $('#selectionSummary').show();
    } else {
        $('#selectionSummary').hide();
    }
}

// 更新保存按钮状态
function updateSaveButton() {
    var costPrice = $('#costPrice').val();
    var salePrice = $('#salePrice').val();
    var hasSelection = selectedZsbIds.length > 0;
    var hasPrices = costPrice && salePrice && parseInt(costPrice) > 0 && parseInt(salePrice) > 0;

    $('#saveBtn').prop('disabled', !(hasSelection && hasPrices));
}

// 格式化金额显示
function formatMoney(amount) {
    if (amount === null || amount === undefined || amount === '') {
        return '0';
    }

    var num = parseFloat(amount);
    if (isNaN(num)) {
        return '0';
    }

    num = Math.round(num);
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 价格格式化核心函数：将价格向下取整到千位
function formatPriceToThousands(value) {
    if (!value || value === '') {
        return 0;
    }

    var num = parseInt(value);
    if (isNaN(num) || num <= 0) {
        return 0;
    }

    // 向下取整到千位（保留千位以上的数字，去除百位、十位、个位）
    return Math.floor(num / 1000) * 1000;
}

// 显示格式化提示信息
function showFormattedPriceHint(inputElement, originalValue, formattedValue) {
    var $inputGroup = $(inputElement).closest('.price-input-group');
    var $hint = $inputGroup.find('.price-format-hint');
    var $result = $inputGroup.find('.formatted-result');

    if (originalValue != formattedValue) {
        $hint.addClass('active');
        $result.text('格式化后：' + formatMoney(formattedValue) + ' 元').show();
        if (formattedValue === 0) {
            $result.addClass('warning').text('格式化后：0 元（原价格小于1000）');
        } else {
            $result.removeClass('warning');
        }
    } else {
        $hint.removeClass('active');
        $result.hide();
    }
}

// 更新价格输入框并应用格式化
function updatePriceInputWithFormatting(inputElement) {
    var originalValue = parseInt(inputElement.value) || 0;
    var formattedValue = formatPriceToThousands(originalValue);

    // 更新输入框值
    if (formattedValue > 0) {
        inputElement.value = formattedValue.toString();
        $(inputElement).data('formatted-value', formattedValue);
    } else {
        inputElement.value = '';
    }

    // 显示格式化提示
    showFormattedPriceHint(inputElement, originalValue, formattedValue);

    return formattedValue;
}

// 在输入框失去焦点时格式化显示
function formatInputOnBlur(inputElement) {
    if (inputElement.value && inputElement.value !== '') {
        var value = parseInt(inputElement.value) || 0;
        if (value > 0) {
            // 显示千分符格式
            $(inputElement).attr('type', 'text').val(formatMoney(value));
        }
    }
}

// 在输入框获得焦点时恢复数字格式
function restoreInputOnFocus(inputElement) {
    var $input = $(inputElement);
    var displayValue = $input.val();

    // 如果当前显示的是千分符格式，转换回数字
    if (displayValue && displayValue.indexOf(',') !== -1) {
        var numericValue = displayValue.replace(/,/g, '');
        $input.attr('type', 'text').val(numericValue);
    }
}

// 获取输入框的纯数字值（移除千分符）
function getNumericValue(inputElement) {
    var value = inputElement.value || '';
    return value.replace(/[^\d]/g, '');
}

// 应用价格格式化（失去焦点时触发）
function applyPriceFormatting(input) {
    if (input.value && input.value !== '') {
        var formattedValue = updatePriceInputWithFormatting(input);

        // 显示千分符格式
        formatInputOnBlur(input);

        // 重新触发验证
        if (input.id === 'costPrice' || input.id === 'salePrice') {
            validatePositiveIntegerWithFormatting(input);
            // 重新计算佣金
            calculateBatchCommission();
        }
    }
}

// 验证正整数输入（带格式化功能）
function validatePositiveIntegerWithFormatting(input) {
    var value = input.value;

    // 移除千分符和非数字字符，只保留数字
    var numericValue = value.replace(/[^\d]/g, '');

    // 如果为空或者为0，设置为空字符串
    if (numericValue === '' || numericValue === '0') {
        input.value = '';
        input.setCustomValidity('');
        input.style.borderColor = '#ddd';
        return;
    }

    // 移除前导零
    numericValue = numericValue.replace(/^0+/, '');

    // 如果移除前导零后为空，设置为空
    if (numericValue === '') {
        input.value = '';
        input.setCustomValidity('');
        input.style.borderColor = '#ddd';
        return;
    }

    // 设置处理后的值
    input.value = numericValue;

    // 验证是否为正整数
    var intValue = parseInt(numericValue);
    if (isNaN(intValue) || intValue <= 0) {
        input.setCustomValidity('请输入正整数');
        input.style.borderColor = '#dc3545';
        return;
    }

    // 如果是成本价输入框，验证是否不低于基准成本价
    if (input.id === 'costPrice') {
        if (currentPostInfo && currentPostInfo.base_cost && intValue < currentPostInfo.base_cost) {
            input.setCustomValidity('成本价不得低于您的成本价（' + currentPostInfo.base_cost + '元）');
            input.style.borderColor = '#dc3545';
            return;
        }
    }

    // 如果是对外价输入框，验证是否不超过最高报价
    if (input.id === 'salePrice') {
        if (currentPostInfo && currentPostInfo.max_price && intValue > currentPostInfo.max_price) {
            input.setCustomValidity('对外价不能超过最高报价（' + formatMoney(currentPostInfo.max_price) + '元）');
            input.style.borderColor = '#dc3545';
            return;
        }
    }

    input.setCustomValidity('');
    input.style.borderColor = '#28a745';
}

// 计算批量配置中的佣金和平台服务费
function calculateBatchCommission() {
    var costPrice = parseInt(getNumericValue(document.getElementById('costPrice'))) || 0;
    var salePrice = parseInt(getNumericValue(document.getElementById('salePrice'))) || 0;

    // 只有当两个价格都大于0时才计算佣金
    if (costPrice <= 0 || salePrice <= 0) {
        $('#batchCommissionAmount').text('请输入完整价格信息');
        $('#batchPlatformFeeAmount').text('--');
        $('#batchStationProfitAmount').text('--');
        return;
    }

    if (!currentPostInfo) {
        $('#batchCommissionAmount').text('请先选择岗位');
        $('#batchPlatformFeeAmount').text('--');
        $('#batchStationProfitAmount').text('--');
        return;
    }

    // 计算平台服务费：max(0, (对外价 - 最低报价)) × 动态平台费率
    var platformFee = Math.max(0, (salePrice - currentPostInfo.service_price)) * dynamicPlatformRate;

    // 计算招就办主任提成：对外价 - 成本价 - 平台服务费
    var commission = salePrice - costPrice - platformFee;

    // 计算服务站收益：成本价 - 基准成本价
    var stationProfit = 0;
    if (currentPostInfo.base_cost && currentPostInfo.base_cost > 0 && costPrice > 0) {
        stationProfit = costPrice - currentPostInfo.base_cost;
    }

    // 更新平台服务费显示
    $('#batchPlatformFeeAmount').text(formatMoney(platformFee));

    // 更新佣金显示
    if (commission < 0) {
        $('#batchCommissionAmount').html('<span style="color: #dc3545;">' + formatMoney(commission) + '</span>');
        $('#batchCommissionAmount').parent().find('.commission-label').html('<span style="color: #dc3545;">招就办主任提成（元）- 提成为负数，请调整价格</span>');
    } else {
        $('#batchCommissionAmount').html('<span style="color: #28a745;">' + formatMoney(commission) + '</span>');
        $('#batchCommissionAmount').parent().find('.commission-label').html('招就办主任提成（元）');
    }

    // 更新服务站收益显示
    if (currentPostInfo.base_cost && currentPostInfo.base_cost > 0 && costPrice > 0) {
        var profitColor = stationProfit >= 0 ? '#28a745' : '#dc3545';
        $('#batchStationProfitAmount').html('<span style="color: ' + profitColor + ';">' + formatMoney(stationProfit) + '</span>');
    } else {
        $('#batchStationProfitAmount').text('--');
    }
}

// 重置表单
function resetForm() {
    $('#batchPriceForm')[0].reset();
    $('#batchCommissionAmount').text('0');
    $('#batchPlatformFeeAmount').text('0');
    $('#batchStationProfitAmount').text('--');
    updateSaveButton();
}

// 监听价格输入变化
$(document).on('input', '#costPrice, #salePrice', function() {
    updateSaveButton();
});

// 批量保存配置
function saveBatchConfig() {
    // 获取纯数字值（移除千分符）
    var costPriceValue = getNumericValue(document.getElementById('costPrice'));
    var salePriceValue = getNumericValue(document.getElementById('salePrice'));

    // 应用格式化到纯数字值
    if (costPriceValue) {
        costPriceValue = formatPriceToThousands(parseInt(costPriceValue));
    }
    if (salePriceValue) {
        salePriceValue = formatPriceToThousands(parseInt(salePriceValue));
    }

    var status = parseInt($('#priceStatus').val());

    // 验证数据
    if (!costPriceValue || costPriceValue <= 0) {
        alert('请输入有效的成本价');
        return;
    }

    if (!salePriceValue || salePriceValue <= 0) {
        alert('请输入有效的对外价');
        return;
    }

    if (salePriceValue < costPriceValue) {
        alert('对外价不能低于成本价');
        return;
    }

    if (selectedZsbIds.length === 0) {
        alert('请至少选择一个招就办');
        return;
    }

    // 验证成本价不能低于服务站成本价
    if (currentPostInfo && currentPostInfo.base_cost && costPriceValue < currentPostInfo.base_cost) {
        alert('成本价不能低于服务站成本价（' + formatMoney(currentPostInfo.base_cost) + ' 元）');
        return;
    }

    // 验证对外价不能超过最高报价
    if (currentPostInfo && currentPostInfo.max_price && salePriceValue > currentPostInfo.max_price) {
        alert('对外价不能超过报价区间最高价（' + formatMoney(currentPostInfo.max_price) + ' 元）');
        return;
    }

    // 验证招就办主任提成不能为负数
    if (currentPostInfo) {
        var platformFee = Math.max(0, (salePriceValue - currentPostInfo.service_price)) * dynamicPlatformRate;
        var commission = salePriceValue - costPriceValue - platformFee;
        if (commission < 0) {
            alert('招就办主任提成不能为负数。当前平台服务费为' + formatMoney(platformFee) + '元，请调整价格配置');
            return;
        }
    }

    // 确认操作
    var confirmMsg = '确定要为 ' + selectedZsbIds.length + ' 个招就办批量设置价格配置吗？\n';
    confirmMsg += '岗位：' + (currentPostInfo ? currentPostInfo.job_name : '') + '\n';
    confirmMsg += '成本价：' + formatMoney(costPriceValue) + ' 元\n';
    confirmMsg += '对外价：' + formatMoney(salePriceValue) + ' 元';

    if (!confirm(confirmMsg)) {
        return;
    }

    // 禁用保存按钮
    $('#saveBtn').prop('disabled', true).text('保存中...');

    // 发送请求
    $.ajax({
        url: '{:U("index/batchSetPostPriceAjax")}',
        type: 'POST',
        data: {
            post_id: currentPostId,
            zsb_ids: selectedZsbIds,
            cost_price: costPriceValue,
            sale_price: salePriceValue,
            status: status
        },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                alert('批量配置成功！');
                // 重置表单和选择
                resetForm();
                selectedZsbIds = [];
                updateSelectionSummary();
                renderZsbList(allZsbList);
                $('#priceConfigCard').hide();
            } else {
                alert('批量配置失败：' + (response.msg || '未知错误'));
            }
        },
        error: function() {
            alert('网络错误，请重试');
        },
        complete: function() {
            $('#saveBtn').prop('disabled', false).text('批量保存配置');
            updateSaveButton();
        }
    });
}
</script>

</body>
</html>
