<?php

/**
 * CLI模式执行，robot脚本控制器
 * 一般用来放置守护进程或执行时间很长的脚本  todo 增加脚本，统计一段时间内，点击多而关注少的情况，在后台监控页面中显示出来，用来查出作弊者，可以下架公众号，或直接禁用商户
 */

namespace Cli\Controller;

use Think\Controller;

class ScriptController extends Controller
{

    public function _initialize()
    {
        set_time_limit(0);
//        if (! IS_CLI) {
//            exit('Deny!');
//        }
        import('Vendor.Simplelog');
        $this->log = new \Simplelog();
    }

   public function repiredfilehash() {
       $existFileList = D("UserJobDoc")->select();
       foreach ($existFileList as $row) {
           $fileHash = md5_file(SITE_PATH . $row['content']); // 计算文件哈希值
           D("UserJobDoc")->save(['file_hash' => $fileHash, 'id' => $row['id']]);
            echo "success".$row['id'].PHP_EOL;
       }
        echo "done!";
   }
}
