<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">

            <style type='text/css'>
                /* 现代化服务号管理页面样式 */
                .service-index-wrapper {
                    width: 100%;
                    padding: 1.5rem;
                    background: #f7fafc;
                    min-height: 100vh;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.5;
                }

                /* 现代化页面标题 */
                .service-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                    width: 100%;
                }

                .service-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .service-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .service-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .service-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .service-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .service-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .service-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .service-index-actions {
                    display: flex;
                    gap: 1rem;
                    align-items: center;
                }

                .service-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                }

                .service-index-search-toggle:hover {
                    background: #4b5563;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    text-decoration: none;
                }

                .service-index-nav-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .service-index-nav-btn:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    text-decoration: none;
                }

                /* 隐藏式搜索面板 */
                .service-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    max-height: 0;
                    opacity: 0;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .service-index-search-panel.show {
                    max-height: 500px;
                    opacity: 1;
                }

                .service-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .service-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .service-index-search-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .service-index-search-body {
                    padding: 2rem;
                    background: white;
                }

                .service-index-search-form {
                    display: flex;
                    flex-direction: column;
                    gap: 1.5rem;
                }

                .service-index-form-row {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 1.5rem;
                }

                .service-index-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .service-index-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0;
                }

                .service-index-form-input,
                .service-index-form-select {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                }

                .service-index-form-input:focus,
                .service-index-form-select:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .service-index-form-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                .service-index-search-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    align-items: center;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e5e7eb;
                    flex-wrap: wrap;
                }

                .service-index-search-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                }

                .service-index-search-btn.btn-primary {
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .service-index-search-btn.btn-primary:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    text-decoration: none;
                }

                .service-index-search-btn.btn-secondary {
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                }

                .service-index-search-btn.btn-secondary:hover {
                    background: #4b5563;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    text-decoration: none;
                }

                /* 现代化列表容器 */
                .service-index-list-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }

                .service-list-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 0.75rem;
                }

                .service-list-title-section {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .service-list-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .service-list-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .service-list-count {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    background: #667eea;
                    color: white;
                    border-radius: 2rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .service-list-body {
                    background: white;
                }

                /* 服务号卡片样式 */
                .service-list-item {
                    display: flex;
                    align-items: flex-start;
                    padding: 2rem;
                    border-bottom: 1px solid #f1f5f9;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    gap: 2rem;
                }

                .service-list-item:last-child {
                    border-bottom: none;
                }

                .service-list-item:hover {
                    background: #f8fafc;
                    transform: translateX(4px);
                }

                .service-list-item::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 4px;
                    background: transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .service-list-item:hover::before {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                /* 服务号ID区域 */
                .service-item-id-section {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 0.5rem;
                    min-width: 6rem;
                }

                .service-item-id {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 0.5rem 1rem;
                    border-radius: 2rem;
                    font-size: 1.5rem;
                    font-weight: 700;
                    text-align: center;
                    min-width: 5rem;
                }

                /* 服务号内容区域 */
                .service-item-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 1.5rem;
                }

                .service-item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                .service-item-title {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .service-item-title i {
                    color: #667eea;
                    font-size: 1.75rem;
                }

                .service-status-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    border-radius: 2rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .service-status-badge.success {
                    background: #d1fae5;
                    color: #059669;
                }

                .service-status-badge.warning {
                    background: #fef3c7;
                    color: #d97706;
                }

                .service-status-badge.danger {
                    background: #fee2e2;
                    color: #dc2626;
                }

                .service-status-badge.info {
                    background: #dbeafe;
                    color: #2563eb;
                }

                /* 服务号信息网格 */
                .service-info-section {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                }

                .service-section-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0 0 1rem 0;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding-bottom: 0.75rem;
                    border-bottom: 2px solid #667eea;
                }

                .service-section-title i {
                    color: #667eea;
                    font-size: 1.5rem;
                }

                .service-info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 1rem;
                }

                .service-info-item {
                    background: white;
                    border: 1px solid #e5e7eb;
                    border-radius: 0.5rem;
                    padding: 1rem;
                }

                .service-info-label {
                    display: block;
                    font-size: 1.25rem;
                    color: #6b7280;
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                }

                .service-info-value {
                    display: block;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #1a202c;
                    line-height: 1.4;
                    word-break: break-all;
                }

                .service-info-value.token {
                    font-family: 'Courier New', monospace;
                    background: #f3f4f6;
                    padding: 0.5rem;
                    border-radius: 0.25rem;
                    border: 1px solid #d1d5db;
                }

                .service-info-value.url {
                    color: #3b82f6;
                    text-decoration: underline;
                    cursor: pointer;
                }

                .service-info-value.url:hover {
                    color: #1d4ed8;
                }

                /* 操作按钮区域 */
                .service-item-actions {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    align-items: flex-end;
                    min-width: 120px;
                }

                .service-item-actions .btn {
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    padding: 0.5rem 1rem;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    text-decoration: none;
                    text-align: center;
                    min-width: 80px;
                }

                .service-item-actions .btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .service-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .service-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .service-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .service-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .service-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .service-info-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .service-list-item {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 1rem;
                        padding: 1.5rem 1rem;
                    }

                    .service-item-content {
                        margin-left: 0;
                        width: 100%;
                    }

                    .service-item-actions {
                        margin-left: 0;
                        width: 100%;
                        flex-direction: row;
                        justify-content: flex-start;
                        align-items: center;
                    }

                    .service-index-title-main {
                        font-size: 1.75rem;
                    }

                    .service-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .service-index-actions {
                        flex-direction: column;
                        width: 100%;
                    }

                    .service-index-search-toggle,
                    .service-index-nav-btn {
                        width: 100%;
                        justify-content: center;
                    }
                }

                /* 弹出层样式 */
                .service-modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 1000;
                    display: none;
                    align-items: center;
                    justify-content: center;
                    backdrop-filter: blur(4px);
                    padding: 1rem;
                    box-sizing: border-box;
                }

                .service-modal-overlay.show {
                    display: flex;
                }

                .service-modal {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                    max-width: 600px;
                    width: 100%;
                    max-height: calc(100vh - 2rem);
                    overflow-y: auto;
                    position: relative;
                    animation: modalSlideIn 0.3s ease-out;
                    margin: auto;
                }

                @keyframes modalSlideIn {
                    from {
                        opacity: 0;
                        transform: translateY(-20px) scale(0.95);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                    }
                }

                .service-modal-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    border-radius: 1rem 1rem 0 0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .service-modal-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .service-modal-close {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                }

                .service-modal-close:hover {
                    background: rgba(255, 255, 255, 0.2);
                }

                .service-modal-body {
                    padding: 2rem;
                }

                .service-modal-form {
                    display: flex;
                    flex-direction: column;
                    gap: 1.5rem;
                }

                .service-modal-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .service-modal-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;
                }

                .service-modal-form-label .required {
                    color: #ef4444;
                    font-size: 1.25rem;
                }

                .service-modal-form-input {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                    font-family: inherit;
                }

                .service-modal-form-input:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .service-modal-form-help {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-top: 0.25rem;
                }

                .service-modal-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    align-items: center;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e5e7eb;
                    flex-wrap: wrap;
                }

                .service-modal-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    font-family: inherit;
                }

                .service-modal-btn.btn-primary {
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .service-modal-btn.btn-primary:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                }

                .service-modal-btn.btn-secondary {
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                }

                .service-modal-btn.btn-secondary:hover {
                    background: #4b5563;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                }

                /* 响应式弹出层 */
                @media (max-width: 768px) {
                    .service-modal-overlay {
                        padding: 0.5rem;
                    }

                    .service-modal {
                        width: 100%;
                        max-width: none;
                        max-height: calc(100vh - 1rem);
                        margin: 0;
                    }

                    .service-modal-header {
                        padding: 1rem 1.5rem;
                    }

                    .service-modal-title {
                        font-size: 1.5rem;
                    }

                    .service-modal-body {
                        padding: 1.5rem;
                    }

                    .service-modal-actions {
                        flex-direction: column;
                        align-items: stretch;
                    }

                    .service-modal-btn {
                        justify-content: center;
                    }
                }

                @media (max-width: 480px) {
                    .service-modal-overlay {
                        padding: 0.25rem;
                    }

                    .service-modal {
                        max-height: calc(100vh - 0.5rem);
                    }

                    .service-modal-header {
                        padding: 0.75rem 1rem;
                    }

                    .service-modal-title {
                        font-size: 1.25rem;
                    }

                    .service-modal-body {
                        padding: 1rem;
                    }

                    .service-modal-form-input {
                        font-size: 1.25rem;
                        padding: 0.625rem 0.875rem;
                    }

                    .service-modal-form-label {
                        font-size: 1.25rem;
                    }

                    .service-modal-form-help {
                        font-size: 1rem;
                    }
                }
            </style>

            <div class="service-index-wrapper">
            <!-- 现代化页面标题 -->
            <div class="service-index-header service-index-fade-in">
                <div class="service-index-header-content">
                    <div class="service-index-title">
                        <div class="service-index-title-icon">
                            <i class="fa fa-cogs"></i>
                        </div>
                        <div class="service-index-title-text">
                            <h1 class="service-index-title-main">服务号管理</h1>
                            <p class="service-index-title-sub">Service Management</p>
                        </div>
                    </div>
                    <div class="service-index-actions">
                        <button type="button" class="service-index-search-toggle" onclick="toggleSearchPanel()">
                            <i class="fa fa-search"></i>
                            <span>搜索筛选</span>
                        </button>
                        <button type="button" class="service-index-nav-btn" onclick="openServiceModal()">
                            <i class="fa fa-plus"></i>
                            <span>添加服务号</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 现代化搜索面板 -->
            <div class="service-index-search-panel service-index-fade-in-delay-1" id="searchPanel">
                <div class="service-index-search-header">
                    <div class="service-index-search-icon">
                        <i class="fa fa-search"></i>
                    </div>
                    <h3 class="service-index-search-title">搜索筛选</h3>
                </div>
                <div class="service-index-search-body">
                    <form method="get" class="service-index-search-form" role="form">
                        <div class="service-index-form-row">
                            <div class="service-index-form-group">
                                <label class="service-index-form-label">搜索字段</label>
                                <select class="service-index-form-select" name="kw">
                                    <php>foreach($c_kw as $key=>$value){</php>
                                    <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                    <php>}</php>
                                </select>
                            </div>
                            <div class="service-index-form-group">
                                <label class="service-index-form-label">搜索内容</label>
                                <input class="service-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
                            </div>
                            <div class="service-index-form-group">
                                <label class="service-index-form-label">状态筛选</label>
                                <select class="service-index-form-select" name="status">
                                    <option value="">全部状态</option>
                                    <php> foreach($statusList as  $k => $v) { </php>
                                    <option value="{$k}" <php>if(is_numeric($_get['status']) && $_get['status']==$k) echo 'selected';</php>>{$v.text}</option>
                                    <php> } </php>
                                </select>
                            </div>
                        </div>
                        <div class="service-index-search-actions">
                            <button type="submit" class="service-index-search-btn btn-primary">
                                <i class="fa fa-search"></i>
                                <span>搜索</span>
                            </button>
                            <a href="{:U('service/index')}" class="service-index-search-btn btn-secondary">
                                <i class="fa fa-refresh"></i>
                                <span>重置</span>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 现代化列表容器 -->
            <form action="" method="post">
                <div class="service-index-list-container service-index-fade-in-delay-2">
                    <div class="service-list-header">
                        <div class="service-list-title-section">
                            <div class="service-list-icon">
                                <i class="fa fa-cogs"></i>
                            </div>
                            <h3 class="service-list-title">服务号列表</h3>
                        </div>
                        <div class="service-list-count">
                            <i class="fa fa-list"></i>
                            共 <php>echo count($list);</php> 个服务号
                        </div>
                    </div>
                    <div class="service-list-body">
                        <php>foreach($list as $v) { </php>
                        <div class="service-list-item">
                            <!-- ID显示 -->
                            <div class="service-item-id-section">
                                <div class="service-item-id">
                                    #{$v.id}
                                </div>
                            </div>

                            <!-- 服务号内容区域 -->
                            <div class="service-item-content">
                                <!-- 服务号名称和状态 -->
                                <div class="service-item-header">
                                    <h4 class="service-item-title">
                                        <i class="fa fa-cog"></i>
                                        {$v.name}
                                    </h4>
                                    <span class="service-status-badge {:$statusList[$v['status']]['style']}">
                                        <i class="fa fa-circle"></i>
                                        {:$statusList[$v['status']]['text']}
                                    </span>
                                </div>

                                <!-- 服务号详细信息 -->
                                <div class="service-info-section">
                                    <h5 class="service-section-title">
                                        <i class="fa fa-info-circle"></i>
                                        服务号信息
                                    </h5>
                                    <div class="service-info-grid">
                                        <div class="service-info-item">
                                            <span class="service-info-label">AppID：</span>
                                            <div class="service-info-value">{$v.appid}</div>
                                        </div>
                                        <div class="service-info-item">
                                            <span class="service-info-label">Secret：</span>
                                            <div class="service-info-value">{$v.secret}</div>
                                        </div>
                                        <div class="service-info-item">
                                            <span class="service-info-label">GH_ID：</span>
                                            <div class="service-info-value">{$v.gh_id}</div>
                                        </div>
                                        <div class="service-info-item">
                                            <span class="service-info-label">回调URL：</span>
                                            <div class="service-info-value url" onclick="copyToClipboard(this)">
                                                <php>echo __HOST__."/wxcall/index/id/".$v['id'];</php>
                                            </div>
                                        </div>
                                        <div class="service-info-item">
                                            <span class="service-info-label">Token：</span>
                                            <div class="service-info-value token" onclick="copyToClipboard(this)">
                                                {$v.wx_token}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="service-item-actions">
                                <button type="button" class="btn btn-primary" onclick="editService('{$v.id}', '{$v.name}', '{$v.appid}', '{$v.secret}', '{$v.gh_id}', '{$v.wx_token}')">
                                    <i class="fa fa-edit"></i>
                                    编辑
                                </button>
                                <php>echo serviceStatBtn($v['id'], $v['status']);</php>
                            </div>
                        </div>
                        <php>}</php>
                    </div>
                </div>

                <!-- 分页信息 -->
                <div class="service-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem; font-size: 1.5rem;">
                    {$page}
                </div>
            </form>
            </div>

            <!-- 服务号弹出层 -->
            <div class="service-modal-overlay" id="serviceModal">
                <div class="service-modal">
                    <div class="service-modal-header">
                        <h3 class="service-modal-title" id="modalTitle">
                            <i class="fa fa-plus"></i>
                            添加服务号
                        </h3>
                        <button type="button" class="service-modal-close" onclick="closeServiceModal()">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                    <div class="service-modal-body">
                        <form class="service-modal-form" id="serviceForm">
                            <input type="hidden" id="serviceId" name="id" value="">

                            <div class="service-modal-form-group">
                                <label class="service-modal-form-label">
                                    <span class="required">*</span>
                                    服务号名称
                                </label>
                                <input type="text" id="serviceName" name="name" class="service-modal-form-input" placeholder="请输入服务号名称" required>
                                <div class="service-modal-form-help">用于识别和管理的服务号名称</div>
                            </div>

                            <div class="service-modal-form-group">
                                <label class="service-modal-form-label">
                                    <span class="required">*</span>
                                    AppID
                                </label>
                                <input type="text" id="serviceAppid" name="appid" class="service-modal-form-input" placeholder="请输入微信服务号AppID" required>
                                <div class="service-modal-form-help">微信公众平台提供的AppID</div>
                            </div>

                            <div class="service-modal-form-group">
                                <label class="service-modal-form-label">
                                    <span class="required">*</span>
                                    AppSecret
                                </label>
                                <input type="text" id="serviceSecret" name="secret" class="service-modal-form-input" placeholder="请输入微信服务号AppSecret" required>
                                <div class="service-modal-form-help">微信公众平台提供的AppSecret</div>
                            </div>

                            <div class="service-modal-form-group">
                                <label class="service-modal-form-label">
                                    GH_ID
                                </label>
                                <input type="text" id="serviceGhId" name="gh_id" class="service-modal-form-input" placeholder="请输入微信服务号原始ID">
                                <div class="service-modal-form-help">微信服务号的原始ID（可选）</div>
                            </div>

                            <div class="service-modal-form-group">
                                <label class="service-modal-form-label">
                                    Token
                                </label>
                                <input type="text" id="serviceToken" name="wx_token" class="service-modal-form-input" placeholder="请输入Token">
                                <div class="service-modal-form-help">用于验证的Token（可选）</div>
                            </div>

                            <div class="service-modal-actions">
                                <button type="button" class="service-modal-btn btn-secondary" onclick="closeServiceModal()">
                                    <i class="fa fa-times"></i>
                                    <span>取消</span>
                                </button>
                                <button type="submit" class="service-modal-btn btn-primary">
                                    <i class="fa fa-save"></i>
                                    <span>保存</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // 服务号卡片悬停效果增强
        $('.service-list-item').hover(
            function() {
                $(this).addClass('hover-effect');
            },
            function() {
                $(this).removeClass('hover-effect');
            }
        );

        // 空状态处理
        var totalItems = $('.service-list-item').length;
        if (totalItems === 0) {
            var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-cogs"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无服务号数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的服务号。</p></div>');
            $('.service-index-list-container .service-list-body').append($emptyState);
        }

        // 服务号ID点击复制功能
        $('.service-item-id').click(function() {
            var $id = $(this);
            var idText = $id.text();

            // 创建临时输入框进行复制
            var $temp = $('<input>');
            $('body').append($temp);
            $temp.val(idText).select();
            document.execCommand('copy');
            $temp.remove();

            // 显示复制成功提示
            var originalText = $id.text();
            $id.text('已复制!');
            $id.css('background', 'linear-gradient(135deg, #10b981 0%, #059669 100%)');
            $id.css('color', 'white');

            setTimeout(function() {
                $id.text(originalText);
                $id.css('background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
                $id.css('color', 'white');
            }, 1000);
        });

        // 保留原有的状态按钮功能
        $('.js_status').click(function() {
            var that = $(this),
                url = that.attr('url');
            $.get(url, function(data) {
                window.location.reload();
            });
        });

        // 搜索框实时搜索提示
        $('input[name="val"]').on('input', function() {
            var $input = $(this);
            var value = $input.val();

            if (value.length > 0) {
                $input.css('border-color', '#667eea');
            } else {
                $input.css('border-color', '#e5e7eb');
            }
        });
    });

    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
        } else {
            panel.classList.add('show');
        }
    }

    // 复制到剪贴板功能
    function copyToClipboard(element) {
        var text = element.textContent || element.innerText;
        var $temp = $('<input>');
        $('body').append($temp);
        $temp.val(text).select();
        document.execCommand('copy');
        $temp.remove();

        // 显示复制成功提示
        var $element = $(element);
        var originalBg = $element.css('background-color');
        $element.css('background-color', '#10b981');
        $element.css('color', 'white');

        setTimeout(function() {
            $element.css('background-color', originalBg);
            $element.css('color', '');
        }, 1000);

        // 可选：显示提示消息
        if (typeof layer !== 'undefined') {
            layer.msg('已复制到剪贴板', {icon: 1, time: 1000});
        }
    }

    // 弹出层功能
    function openServiceModal(id, name, appid, secret, gh_id, wx_token) {
        var modal = document.getElementById('serviceModal');
        var modalTitle = document.getElementById('modalTitle');
        var form = document.getElementById('serviceForm');

        // 重置表单
        form.reset();

        if (id) {
            // 编辑模式
            modalTitle.innerHTML = '<i class="fa fa-edit"></i> 编辑服务号';
            document.getElementById('serviceId').value = id;
            document.getElementById('serviceName').value = name || '';
            document.getElementById('serviceAppid').value = appid || '';
            document.getElementById('serviceSecret').value = secret || '';
            document.getElementById('serviceGhId').value = gh_id || '';
            document.getElementById('serviceToken').value = wx_token || '';
        } else {
            // 添加模式
            modalTitle.innerHTML = '<i class="fa fa-plus"></i> 添加服务号';
            document.getElementById('serviceId').value = '';
        }

        modal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // 聚焦到第一个输入框
        setTimeout(function() {
            document.getElementById('serviceName').focus();
        }, 300);
    }

    function closeServiceModal() {
        var modal = document.getElementById('serviceModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }

    function editService(id, name, appid, secret, gh_id, wx_token) {
        openServiceModal(id, name, appid, secret, gh_id, wx_token);
    }

    // 表单提交处理
    $(document).ready(function() {
        $('#serviceForm').on('submit', function(e) {
            e.preventDefault();

            var formData = {
                id: $('#serviceId').val(),
                name: $('#serviceName').val(),
                appid: $('#serviceAppid').val(),
                secret: $('#serviceSecret').val(),
                gh_id: $('#serviceGhId').val(),
                wx_token: $('#serviceToken').val()
            };

            // 验证必填字段
            if (!formData.name || !formData.appid || !formData.secret) {
                if (typeof layer !== 'undefined') {
                    layer.msg('请填写所有必填字段', {icon: 2});
                } else {
                    alert('请填写所有必填字段');
                }
                return;
            }

            // 显示提交状态
            var $submitBtn = $(this).find('button[type="submit"]');
            var originalText = $submitBtn.html();
            $submitBtn.html('<i class="fa fa-spinner fa-spin"></i> <span>保存中...</span>');
            $submitBtn.prop('disabled', true);

            // 提交数据
            $.ajax({
                url: '{:U("service/edit")}',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 1 || response.status === 'success') {
                        if (typeof layer !== 'undefined') {
                            layer.msg('保存成功', {icon: 1}, function() {
                                window.location.reload();
                            });
                        } else {
                            alert('保存成功');
                            window.location.reload();
                        }
                    } else {
                        if (typeof layer !== 'undefined') {
                            layer.msg(response.info || '保存失败', {icon: 2});
                        } else {
                            alert(response.info || '保存失败');
                        }
                    }
                },
                error: function() {
                    if (typeof layer !== 'undefined') {
                        layer.msg('网络错误，请重试', {icon: 2});
                    } else {
                        alert('网络错误，请重试');
                    }
                },
                complete: function() {
                    $submitBtn.html(originalText);
                    $submitBtn.prop('disabled', false);
                }
            });
        });

        // 点击遮罩层关闭弹出层
        $('#serviceModal').on('click', function(e) {
            if (e.target === this) {
                closeServiceModal();
            }
        });

        // ESC键关闭弹出层
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && $('#serviceModal').hasClass('show')) {
                closeServiceModal();
            }
        });

        // 表单验证增强
        $('.service-modal-form-input[required]').on('blur', function() {
            var $field = $(this);
            var value = $field.val().trim();

            if (!value) {
                $field.css('border-color', '#ef4444');
                $field.css('box-shadow', '0 0 0 3px rgba(239, 68, 68, 0.1)');
            } else {
                $field.css('border-color', '#10b981');
                $field.css('box-shadow', '0 0 0 3px rgba(16, 185, 129, 0.1)');
            }
        });

        // 输入框焦点效果
        $('.service-modal-form-input').on('focus', function() {
            $(this).css('border-color', '#667eea');
            $(this).css('box-shadow', '0 0 0 3px rgba(102, 126, 234, 0.1)');
        });

        $('.service-modal-form-input').on('blur', function() {
            if (!$(this).is('[required]') || $(this).val().trim()) {
                $(this).css('border-color', '#e5e7eb');
                $(this).css('box-shadow', 'none');
            }
        });
    });
</script>

<include file="block/footer" />