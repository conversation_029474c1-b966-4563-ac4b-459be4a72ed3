<?php
/**
 * Created by PhpStorm.
 * User: yangbin
 * Date: 2019/5/20
 * Time: 19:04
 */

namespace Prime\Controller;
use Common\Controller\PrimeController;
use \LaneWeChat\Core as WE;

class QrcodeController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    public function index()
    {
        $c_kw = [
            'name' => '名称',
            'id' => 'ID',
        ];


        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");

        $s_type = I("get.use_type");
        if ($s_type !== '') $where['use_type'] = $s_type;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['name'])) {
                $where[$s_kw] = ['like', "%$s_val%"];
            } else {
                $where[$s_kw] = $s_val;
            }
        }

        $obj = D("Qrcode");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 20);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        if ($list) {
            $service_station_arr_id = array_column($list, 'service_station_id');
            $serviceStationList = D("ServiceStation")->where(['id' => ['in', $service_station_arr_id]])->getField('id,service_name', true);
            $this->assign('serviceStationList', $serviceStationList);
        }
        $this->assign('list', $list);
        $this->assign('serviceList', D("Service")->getField('id,name'));
        $this->assign('_get', I('get.'));
        $this->assign('typeList', $obj->use_type);
        $this->assign("page", $page->show());
        $this->display();
    }

    // 停用二维码
    public function stop()
    {
        $id = I('get.id');
        D('Qrcode')->stop($id);
        $this->success('修改成功');
    }

    /**
     * 生成新二维码
     */
    public function edit()
    {
        if (IS_POST) {
            $datas = I('post.');
            $service_station_id = 1; //服务站ID
            $code = D("Qrcode")->createNew(1, 1, $service_station_id);
            if ($code) {
                D("Qrcode")->save(['id' => $code['id'], 'name' => $datas['name']]);
                $this->success('添加带参数二维码成功', U("qrcode/index"));
                die;
            }
            $this->error('添加二维码失败');
        }
        $this->display();
    }
}


