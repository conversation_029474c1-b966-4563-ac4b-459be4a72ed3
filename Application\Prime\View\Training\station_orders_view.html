<!DOCTYPE html>
<html>
<head>
    <title>服务站订单统一查看 - {$station.service_name}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/static/prime/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/prime/css/style.css">
    <script src="/static/prime/js/jquery.min.js"></script>
    <script src="/static/prime/js/bootstrap.min.js"></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4>服务站订单统一查看 - {$station.service_name}</h4>
                        <small class="text-muted">服务站可以查看招就办订单和自有订单，但都只有查看权限</small>
                    </div>
                    
                    <div class="panel-body">
                        <!-- 订单类型筛选 -->
                        <div class="row" style="margin-bottom: 15px;">
                            <div class="col-md-12">
                                <div class="btn-group" role="group">
                                    <a href="{:U('Training/stationOrdersView', array_merge($_GET, ['order_type' => 'all']))}" 
                                       class="btn btn-{:$orderType == 'all' ? 'primary' : 'default'}">
                                        全部订单
                                    </a>
                                    <a href="{:U('Training/stationOrdersView', array_merge($_GET, ['order_type' => 'zsb']))}" 
                                       class="btn btn-{:$orderType == 'zsb' ? 'success' : 'default'}">
                                        招就办订单
                                    </a>
                                    <a href="{:U('Training/stationOrdersView', array_merge($_GET, ['order_type' => 'station']))}" 
                                       class="btn btn-{:$orderType == 'station' ? 'info' : 'default'}">
                                        自有订单
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- 搜索筛选 -->
                        <form method="get" class="form-inline" style="margin-bottom: 15px;">
                            <input type="hidden" name="station_id" value="{$station.id}">
                            <input type="hidden" name="order_type" value="{$orderType}">
                            
                            <div class="form-group">
                                <select name="order_status" class="form-control">
                                    <option value="">订单状态</option>
                                    <php>foreach($order_status_list as $k => $v) {</php>
                                    <option value="{$k}" <php>if($_GET['order_status'] == $k) echo 'selected';</php>>{$v.text}</option>
                                    <php>}</php>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <select name="payment_status" class="form-control">
                                    <option value="">支付状态</option>
                                    <php>foreach($payment_status_list as $k => $v) {</php>
                                    <option value="{$k}" <php>if($_GET['payment_status'] == $k) echo 'selected';</php>>{$v.text}</option>
                                    <php>}</php>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <input type="text" name="kwd" value="{$_GET.kwd}" class="form-control" placeholder="搜索订单ID">
                            </div>
                            
                            <button type="submit" class="btn btn-primary">搜索</button>
                            <a href="{:U('Training/stationOrdersView', ['station_id' => $station.id, 'order_type' => $orderType])}" class="btn btn-default">重置</a>
                        </form>

                        <!-- 订单列表 -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>订单ID</th>
                                        <th>订单类型/服务站</th>
                                        <th>学员信息</th>
                                        <th>项目/岗位</th>
                                        <th>价格信息</th>
                                        <th>订单状态</th>
                                        <th>支付状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>if(empty($list)) {</php>
                                    <tr>
                                        <td colspan="9" class="text-center text-muted">暂无订单数据</td>
                                    </tr>
                                    <php>} else {</php>
                                    <php>foreach($list as $v) {</php>
                                    <tr>
                                        <td><strong>{$v.id}</strong></td>
                                        <td>
                                            <php>if($v['is_zsb_order']) {</php>
                                            <span class="label label-success">招就办订单</span>
                                            <br><small class="text-muted">服务站：{$v.station_name}</small>
                                            <php>if(!empty($v['zsb_name'])) {</php>
                                            <br><small class="text-muted">招就办：{$v.zsb_name}</small>
                                            <php>}</php>
                                            <php>} else {</php>
                                            <span class="label label-primary">自有订单</span>
                                            <br><small class="text-muted">服务站：{$v.station_name}</small>
                                            <php>}</php>
                                        </td>
                                        <td>
                                            <strong>{$v.user_name}</strong><br>
                                            <small class="text-muted">{$v.user_mobile}</small>
                                        </td>
                                        <td>
                                            <strong>{$v.project_name}</strong><br>
                                            <small class="text-muted">{$v.post_name}</small>
                                        </td>
                                        <td>
                                            <php>if($v['is_zsb_order']) {</php>
                                            <!-- 招就办订单价格 -->
                                            <php>
                                            $zsbFeeYuan = $v['zsb_price'] / 100;
                                            if($v['zsb_price'] <= 0) {
                                            </php>
                                                <strong style="color: #f59e0b;">待确定</strong><br>
                                            <php>
                                            } elseif($zsbFeeYuan >= 99999) {
                                            </php>
                                                <strong style="color: #999;">异常金额已隐藏</strong><br>
                                            <php>
                                            } else {
                                            </php>
                                                <strong style="color: #d9534f;">¥{$v.zsb_price_yuan}</strong><br>
                                            <php>
                                            }
                                            </php>
                                            <small class="text-success">服务站：¥{:number_format($v['station_profit_detail'] / 100, 2)}</small><br>
                                            <small class="text-success">招就办：¥{:number_format($v['zsb_commission_detail'] / 100, 2)}</small>
                                            <php>} else {</php>
                                            <!-- 自有订单价格 -->
                                            <php>
                                            $feeYuan = $v['fee_amount'] / 100;
                                            if($v['fee_amount'] <= 0) {
                                            </php>
                                                <strong style="color: #f59e0b;">待确定</strong><br>
                                            <php>
                                            } elseif($feeYuan >= 99999) {
                                            </php>
                                                <strong style="color: #999;">异常金额已隐藏</strong><br>
                                            <php>
                                            } else {
                                            </php>
                                                <strong style="color: #d9534f;">¥{:number_format($v['fee_amount'] / 100, 2)}</strong><br>
                                            <php>
                                            }
                                            </php>
                                            <small class="text-success">奖励: ¥{:number_format($v['reward_station_amt'] / 100, 2)}</small>
                                            <php>}</php>
                                        </td>
                                        <td>
                                            <span class="label label-{$v.order_status_style}">{$v.order_status_text}</span>
                                        </td>
                                        <td>
                                            <span class="label label-{$v.payment_status_style}">{$v.payment_status_text}</span>
                                        </td>
                                        <td>
                                            <small>{:date('Y-m-d H:i', $v['create_time'])}</small>
                                        </td>
                                        <td>
                                            <a href="{:U('Training/stationOrderDetail', ['id' => $v['id'], 'station_id' => $station.id])}" 
                                               class="btn btn-sm btn-info" title="查看详情">
                                                <i class="glyphicon glyphicon-eye-open"></i> 查看
                                            </a>
                                        </td>
                                    </tr>
                                    <php>}</php>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="row">
                            <div class="col-md-12">
                                {$page}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .label {
            font-size: 11px;
            padding: 3px 6px;
        }
        .table td {
            vertical-align: middle;
        }
        .btn-group .btn {
            margin-right: 5px;
        }
        .form-inline .form-group {
            margin-right: 10px;
        }
    </style>
</body>
</html>
