<include file="block/hat" />
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<style type='text/css'>
				/* 现代化用户管理页面样式 */
				.user-index-wrapper {
					width: 100%;
					padding: 1.5rem;
					background: #f7fafc;
					min-height: 100vh;
					font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
					font-size: 14px;
					line-height: 1.5;
				}

				/* 现代化页面标题 */
				.user-index-header {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 2rem;
					overflow: hidden;
					position: relative;
					width: 100%;
				}

				.user-index-header::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4px;
					background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
				}

				.user-index-header-content {
					padding: 2rem;
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex-wrap: wrap;
					gap: 1rem;
				}

				.user-index-title {
					display: flex;
					align-items: center;
					gap: 1rem;
					margin: 0;
				}

				.user-index-title-icon {
					width: 3rem;
					height: 3rem;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					border-radius: 0.75rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.5rem;
				}

				.user-index-title-text {
					display: flex;
					flex-direction: column;
				}

				.user-index-title-main {
					font-size: 2rem;
					font-weight: 700;
					color: #1a202c;
					margin: 0;
					line-height: 1.2;
				}

				.user-index-title-sub {
					font-size: 1.5rem;
					color: #718096;
					margin: 0;
					font-weight: 400;
				}

				.user-index-actions {
					display: flex;
					gap: 1rem;
					align-items: center;
				}

				.user-index-search-toggle {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					border: none;
					border-radius: 0.5rem;
					font-size: 1.5rem;
					font-weight: 600;
					text-decoration: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					white-space: nowrap;
					background: #6b7280;
					color: white;
					box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
				}

				.user-index-search-toggle:hover {
					background: #4b5563;
					color: white;
					transform: translateY(-1px);
					box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
					text-decoration: none;
				}

				.user-index-nav-btn {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					border: none;
					border-radius: 0.5rem;
					font-size: 1.5rem;
					font-weight: 600;
					text-decoration: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					white-space: nowrap;
					background: #667eea;
					color: white;
					box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
				}

				.user-index-nav-btn:hover {
					background: #5a67d8;
					color: white;
					transform: translateY(-1px);
					box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
					text-decoration: none;
				}

				/* 隐藏式搜索面板 */
				.user-index-search-panel {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 2rem;
					overflow: hidden;
					max-height: 0;
					opacity: 0;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.user-index-search-panel.show {
					max-height: 500px;
					opacity: 1;
				}

				.user-index-search-header {
					background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
					padding: 1.5rem 2rem;
					border-bottom: 1px solid #e2e8f0;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.user-index-search-icon {
					width: 2.5rem;
					height: 2.5rem;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					border-radius: 0.5rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.25rem;
				}

				.user-index-search-title {
					font-size: 1.75rem;
					font-weight: 600;
					color: #1a202c;
					margin: 0;
				}

				.user-index-search-body {
					padding: 2rem;
					background: white;
				}

				.user-index-search-form {
					display: flex;
					flex-direction: column;
					gap: 1.5rem;
				}

				.user-index-form-group {
					display: flex;
					flex-direction: column;
					gap: 0.5rem;
				}

				.user-index-form-label {
					font-size: 1.5rem;
					font-weight: 600;
					color: #374151;
					margin: 0;
				}

				.user-index-form-input,
				.user-index-form-select {
					padding: 0.75rem 1rem;
					border: 2px solid #e5e7eb;
					border-radius: 0.5rem;
					font-size: 1.5rem;
					transition: all 0.3s ease;
					background: white;
					color: #374151;
				}

				.user-index-form-input:focus,
				.user-index-form-select:focus {
					outline: none;
					border-color: #667eea;
					box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
				}

				.user-index-form-select {
					background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
					background-position: right 0.5rem center;
					background-repeat: no-repeat;
					background-size: 1.5em 1.5em;
					padding-right: 2.5rem;
					appearance: none;
				}

				.user-index-search-actions {
					display: flex;
					gap: 1rem;
					justify-content: flex-end;
					align-items: center;
					padding-top: 1.5rem;
					border-top: 1px solid #e5e7eb;
					flex-wrap: wrap;
				}

				.user-index-search-btn {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					border: none;
					border-radius: 0.5rem;
					font-size: 1.5rem;
					font-weight: 600;
					text-decoration: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					white-space: nowrap;
				}

				.user-index-search-btn.btn-primary {
					background: #667eea;
					color: white;
					box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
				}

				.user-index-search-btn.btn-primary:hover {
					background: #5a67d8;
					color: white;
					transform: translateY(-1px);
					box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
					text-decoration: none;
				}

				.user-index-search-btn.btn-secondary {
					background: #6b7280;
					color: white;
					box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
				}

				.user-index-search-btn.btn-secondary:hover {
					background: #4b5563;
					color: white;
					transform: translateY(-1px);
					box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
					text-decoration: none;
				}

				/* 现代化列表容器 */
				.user-index-list-container {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					overflow: hidden;
				}

				.user-list-header {
					background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
					padding: 1.5rem 2rem;
					border-bottom: 1px solid #e2e8f0;
					display: flex;
					align-items: center;
					justify-content: space-between;
					gap: 0.75rem;
				}

				.user-list-title-section {
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.user-list-icon {
					width: 2.5rem;
					height: 2.5rem;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					border-radius: 0.5rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.25rem;
				}

				.user-list-title {
					font-size: 1.75rem;
					font-weight: 600;
					color: #1a202c;
					margin: 0;
				}

				.user-list-count {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.5rem 1rem;
					background: #667eea;
					color: white;
					border-radius: 2rem;
					font-size: 1.25rem;
					font-weight: 600;
				}

				.user-list-body {
					background: white;
				}

				/* 用户卡片样式 */
				.user-list-item {
					display: flex;
					align-items: flex-start;
					padding: 2rem;
					border-bottom: 1px solid #f1f5f9;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					gap: 2rem;
				}

				.user-list-item:last-child {
					border-bottom: none;
				}

				.user-list-item:hover {
					background: #f8fafc;
					transform: translateX(4px);
				}

				.user-list-item::before {
					content: '';
					position: absolute;
					left: 0;
					top: 0;
					bottom: 0;
					width: 4px;
					background: transparent;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.user-list-item:hover::before {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				}

				/* 用户头像区域 */
				.user-item-avatar-section {
					display: flex;
					flex-direction: column;
					align-items: center;
					gap: 0.5rem;
					min-width: 6rem;
				}

				.user-item-avatar {
					width: 5rem;
					height: 5rem;
					border-radius: 50%;
					object-fit: cover;
					border: 3px solid #e2e8f0;
					transition: all 0.3s ease;
				}

				.user-item-avatar:hover {
					border-color: #667eea;
					transform: scale(1.05);
				}

				.user-item-id {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
					padding: 0.25rem 0.75rem;
					border-radius: 1rem;
					font-size: 1.25rem;
					font-weight: 700;
					text-align: center;
				}

				/* 用户信息区域 */
				.user-item-content {
					flex: 1;
					display: flex;
					flex-direction: column;
					gap: 1.5rem;
				}

				.user-item-header {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					gap: 1rem;
					flex-wrap: wrap;
				}

				.user-item-title {
					font-size: 2rem;
					font-weight: 700;
					color: #1a202c;
					margin: 0;
					line-height: 1.2;
					display: flex;
					align-items: center;
					gap: 0.5rem;
				}

				.user-item-title i {
					color: #667eea;
					font-size: 1.75rem;
				}

				/* 用户信息网格 */
				.user-info-section {
					background: #f8fafc;
					border: 1px solid #e2e8f0;
					border-radius: 0.75rem;
					padding: 1.5rem;
				}

				.user-section-title {
					font-size: 1.75rem;
					font-weight: 600;
					color: #1a202c;
					margin: 0 0 1rem 0;
					display: flex;
					align-items: center;
					gap: 0.5rem;
					padding-bottom: 0.75rem;
					border-bottom: 2px solid #667eea;
				}

				.user-section-title i {
					color: #667eea;
					font-size: 1.5rem;
				}

				.user-info-grid {
					display: grid;
					grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
					gap: 1rem;
				}

				.user-info-item {
					background: white;
					border: 1px solid #e5e7eb;
					border-radius: 0.5rem;
					padding: 1rem;
				}

				.user-info-label {
					display: block;
					font-size: 1.25rem;
					color: #6b7280;
					font-weight: 500;
					margin-bottom: 0.5rem;
				}

				.user-info-value {
					display: block;
					font-size: 1.5rem;
					font-weight: 600;
					color: #1a202c;
					line-height: 1.4;
				}

				.user-status-badge {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.5rem 1rem;
					border-radius: 2rem;
					font-size: 1.25rem;
					font-weight: 600;
				}

				.user-status-badge.bound {
					background: #d1fae5;
					color: #059669;
				}

				.user-status-badge.unbound {
					background: #fee2e2;
					color: #dc2626;
				}

				.user-status-badge.warning {
					background: #fef3c7;
					color: #d97706;
				}

				.user-time-value {
					font-size: 1.5rem;
					color: #6b7280;
					line-height: 1.4;
				}

				/* 操作按钮区域 */
				.user-item-actions {
					display: flex;
					flex-direction: column;
					gap: 0.5rem;
					align-items: flex-end;
					min-width: 120px;
				}

				.user-item-actions .btn {
					border-radius: 0.5rem;
					font-size: 1.25rem;
					padding: 0.5rem 1rem;
					font-weight: 600;
					transition: all 0.3s ease;
					text-decoration: none;
					text-align: center;
					min-width: 80px;
				}

				.user-item-actions .btn:hover {
					transform: translateY(-1px);
					box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
					text-decoration: none;
				}

				/* 动画效果 */
				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(20px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}

				.user-index-fade-in {
					animation: fadeInUp 0.6s ease-out;
				}

				.user-index-fade-in-delay-1 {
					animation: fadeInUp 0.6s ease-out 0.1s both;
				}

				.user-index-fade-in-delay-2 {
					animation: fadeInUp 0.6s ease-out 0.2s both;
				}

				.user-index-fade-in-delay-3 {
					animation: fadeInUp 0.6s ease-out 0.3s both;
				}

				/* 响应式设计 */
				@media (max-width: 1024px) {
					.user-index-header-content {
						flex-direction: column;
						text-align: center;
					}

					.user-info-grid {
						grid-template-columns: 1fr;
					}
				}

				@media (max-width: 768px) {
					.user-list-item {
						flex-direction: column;
						align-items: flex-start;
						gap: 1rem;
						padding: 1.5rem 1rem;
					}

					.user-item-content {
						margin-left: 0;
						width: 100%;
					}

					.user-item-actions {
						margin-left: 0;
						width: 100%;
						flex-direction: row;
						justify-content: flex-start;
						align-items: center;
					}

					.user-index-title-main {
						font-size: 1.75rem;
					}

					.user-index-title-sub {
						font-size: 1.25rem;
					}

					.user-index-actions {
						flex-direction: column;
						width: 100%;
					}

					.user-index-search-toggle,
					.user-index-nav-btn {
						width: 100%;
						justify-content: center;
					}
				}
			</style>

			<div class="user-index-wrapper">
			<!-- 现代化页面标题 -->
			<div class="user-index-header user-index-fade-in">
				<div class="user-index-header-content">
					<div class="user-index-title">
						<div class="user-index-title-icon">
							<i class="fa fa-users"></i>
						</div>
						<div class="user-index-title-text">
							<h1 class="user-index-title-main">用户管理</h1>
							<p class="user-index-title-sub">User Management</p>
						</div>
					</div>
					<div class="user-index-actions">
						<button type="button" class="user-index-search-toggle" onclick="toggleSearchPanel()">
							<i class="fa fa-search"></i>
							<span>搜索筛选</span>
						</button>
						<a href="{:U('user/edit')}" class="user-index-nav-btn">
							<i class="fa fa-plus"></i>
							<span>添加用户</span>
						</a>
					</div>
				</div>
			</div>

			<!-- 现代化搜索面板 -->
			<div class="user-index-search-panel user-index-fade-in-delay-1" id="searchPanel">
				<div class="user-index-search-header">
					<div class="user-index-search-icon">
						<i class="fa fa-search"></i>
					</div>
					<h3 class="user-index-search-title">搜索筛选</h3>
				</div>
				<div class="user-index-search-body">
					<form method="get" class="user-index-search-form" role="form">
						<div class="user-index-form-group">
							<label class="user-index-form-label">搜索字段</label>
							<select class="user-index-form-select" name="kw">
								<php>foreach($c_kw as $key=>$value){</php>
								<option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
								<php>}</php>
							</select>
						</div>
						<div class="user-index-form-group">
							<label class="user-index-form-label">搜索内容</label>
							<input class="user-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
						</div>
						<div class="user-index-form-group">
							<label class="user-index-form-label">用户类型</label>
							<select class="user-index-form-select" name="service_id">
								<option value="">全部类型</option>
								<option value="0" <php>if($s_service_id === '0'){</php>selected<php>}</php>>未分类用户</option>
								<option value="1" <php>if($s_service_id === '1'){</php>selected<php>}</php>>服务站和招就办用户</option>
								<option value="2" <php>if($s_service_id === '2'){</php>selected<php>}</php>>简历用户</option>
							</select>
						</div>
						<div class="user-index-search-actions">
							<button type="submit" class="user-index-search-btn btn-primary">
								<i class="fa fa-search"></i>
								<span>搜索</span>
							</button>
							<a href="{:U('prime/user/index')}" class="user-index-search-btn btn-secondary">
								<i class="fa fa-refresh"></i>
								<span>重置</span>
							</a>
						</div>
					</form>
				</div>
			</div>

			<!-- 现代化列表容器 -->
			<form action="" method="post">
				<div class="user-index-list-container user-index-fade-in-delay-2">
					<div class="user-list-header">
						<div class="user-list-title-section">
							<div class="user-list-icon">
								<i class="fa fa-users"></i>
							</div>
							<h3 class="user-list-title">全部用户列表</h3>
						</div>
						<div class="user-list-count">
							<i class="fa fa-users"></i>
							共 <php>echo count($list);</php> 个用户
							<span style="margin-left: 15px; font-size: 12px; color: #666;">
								<span class="label label-default">未分类: <php>echo count(array_filter($list, function($item) { return $item['service_id'] == 0; }));</php></span>
								<span class="label label-success">服务站和招就办: <php>echo count(array_filter($list, function($item) { return $item['service_id'] == 1; }));</php></span>
								<span class="label label-warning">简历: <php>echo count(array_filter($list, function($item) { return $item['service_id'] == 2; }));</php></span>
							</span>
						</div>
					</div>
					<div class="user-list-body">
						<php>foreach($list as $v) { </php>
						<div class="user-list-item">
							<!-- 用户头像和ID -->
							<div class="user-item-avatar-section">
								<img src="<php>$head = $v['headimgurl']; echo $head ? $head.'2' :'/static/images/nopic.jpg';</php>"
									 alt="{$v.nickname}"
									 class="user-item-avatar">
								<div class="user-item-id">
									#{$v.id}
								</div>
							</div>

							<!-- 用户信息区域 -->
							<div class="user-item-content">
								<!-- 用户昵称 -->
								<div class="user-item-header">
									<h4 class="user-item-title">
										<i class="fa fa-user"></i>
										{$v.nickname}
									</h4>
								</div>

								<!-- 用户详细信息 -->
								<div class="user-info-section">
									<h5 class="user-section-title">
										<i class="fa fa-info-circle"></i>
										用户信息
									</h5>
									<div class="user-info-grid">
										<div class="user-info-item">
											<span class="user-info-label">所属服务站：</span>
											<div class="user-info-value">
												<php>if($v['self_service_station_id']) {</php>
												<span class="user-status-badge bound">
													<i class="fa fa-check-circle"></i>
													{:$serviceStationList[$v['self_service_station_id']]}
												</span>
												<php>} else {</php>
												<span class="user-status-badge unbound">
													<i class="fa fa-times-circle"></i>
													未绑定
												</span>
												<php>}</php>
											</div>
										</div>
										<div class="user-info-item">
											<span class="user-info-label">上级服务站：</span>
											<div class="user-info-value">
												<php>if($v['service_station_id']) {</php>
												<span class="user-status-badge bound">
													<i class="fa fa-check-circle"></i>
													{:$serviceStationList[$v['service_station_id']]}
												</span>
												<php>} else {</php>
												<span class="user-status-badge unbound">
													<i class="fa fa-times-circle"></i>
													未绑定
												</span>
												<php>}</php>
											</div>
										</div>
										<div class="user-info-item">
											<span class="user-info-label">Service ID：</span>
											<div class="user-info-value">
												<span class="user-status-badge
													<php>if($v['service_id'] == 1) echo 'bound'; elseif($v['service_id'] == 2) echo 'warning'; else echo 'unbound';</php>">
													<i class="fa fa-tag"></i>
													{$v.service_id}
													<php>if($v['service_id'] == 1) echo ' (服务站和招就办)'; elseif($v['service_id'] == 2) echo ' (简历用户)'; else echo ' (未分类)';</php>
												</span>
												<button type="button" class="btn btn-xs btn-info" onclick="changeServiceId({$v.id}, {$v.service_id})" style="margin-left: 10px;">
													<i class="fa fa-edit"></i> 修改
												</button>
											</div>
										</div>
										<div class="user-info-item">
											<span class="user-info-label">创建时间：</span>
											<div class="user-time-value">
												<i class="fa fa-calendar"></i>
												<php>echo date('Y-m-d H:i:s', $v['created']);</php>
											</div>
										</div>
									</div>
								</div>
							</div>

							<!-- 操作按钮 -->
							<div class="user-item-actions">
								<php>if ($v['self_service_station_id'] > 0) {</php>
								<a href="{:U('user/loginsout', ['id' => $v['id']])}" class="btn btn-warning">退出登录</a>
								<php>}</php>
								<a href="{:U('user/edit', ['id' => $v['id']])}" class="btn btn-primary">编辑</a>
							</div>
						</div>
						<php>}</php>
					</div>
				</div>

				<!-- 分页信息 -->
				<div class="user-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem; font-size: 1.5rem;">
					{$page}
				</div>
			</form>
			</div>
		</div>
	</div>
</div>

<script>
	$(document).ready(function() {
		// 用户卡片悬停效果增强
		$('.user-list-item').hover(
			function() {
				$(this).addClass('hover-effect');
			},
			function() {
				$(this).removeClass('hover-effect');
			}
		);

		// 空状态处理
		var totalItems = $('.user-list-item').length;
		if (totalItems === 0) {
			var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-users"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无用户数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的用户。</p></div>');
			$('.user-index-list-container .user-list-body').append($emptyState);
		}

		// 用户ID点击复制功能
		$('.user-item-id').click(function() {
			var $id = $(this);
			var idText = $id.text();

			// 创建临时输入框进行复制
			var $temp = $('<input>');
			$('body').append($temp);
			$temp.val(idText).select();
			document.execCommand('copy');
			$temp.remove();

			// 显示复制成功提示
			var originalText = $id.text();
			$id.text('已复制!');
			$id.css('background', 'linear-gradient(135deg, #10b981 0%, #059669 100%)');
			$id.css('color', 'white');

			setTimeout(function() {
				$id.text(originalText);
				$id.css('background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
				$id.css('color', 'white');
			}, 1000);
		});

		// 用户头像点击效果
		$('.user-item-avatar').click(function() {
			var $avatar = $(this);
			$avatar.css('transform', 'scale(0.95)');
			setTimeout(function() {
				$avatar.css('transform', 'scale(1)');
			}, 150);
		});

		// 搜索框实时搜索提示
		$('input[name="val"]').on('input', function() {
			var $input = $(this);
			var value = $input.val();

			if (value.length > 0) {
				$input.css('border-color', '#667eea');
			} else {
				$input.css('border-color', '#e5e7eb');
			}
		});
	});

	// 搜索面板切换功能
	function toggleSearchPanel() {
		var panel = document.getElementById('searchPanel');
		if (panel.classList.contains('show')) {
			panel.classList.remove('show');
		} else {
			panel.classList.add('show');
		}
	}

	// 修改用户service_id
	function changeServiceId(userId, currentServiceId) {
		var serviceOptions = {
			0: '未分类',
			1: '服务站和招就办',
			2: '简历用户'
		};

		var optionsHtml = '';
		for (var id in serviceOptions) {
			var selected = (id == currentServiceId) ? 'selected' : '';
			optionsHtml += '<option value="' + id + '" ' + selected + '>' + id + ' - ' + serviceOptions[id] + '</option>';
		}

		var html = '<div style="padding: 20px;">' +
			'<h4 style="margin-bottom: 15px; color: #333;">修改用户 Service ID</h4>' +
			'<div style="margin-bottom: 15px;">' +
			'<label style="display: block; margin-bottom: 5px; font-weight: bold;">选择新的 Service ID：</label>' +
			'<select id="newServiceId" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">' +
			optionsHtml +
			'</select>' +
			'</div>' +
			'<div style="text-align: right;">' +
			'<button type="button" onclick="layer.closeAll()" style="margin-right: 10px; padding: 8px 16px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 4px;">取消</button>' +
			'<button type="button" onclick="submitServiceIdChange(' + userId + ')" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px;">确认修改</button>' +
			'</div>' +
			'</div>';

		layer.open({
			type: 1,
			title: false,
			closeBtn: 1,
			area: ['400px', 'auto'],
			content: html
		});
	}

	// 提交service_id修改
	function submitServiceIdChange(userId) {
		var newServiceId = document.getElementById('newServiceId').value;

		$.ajax({
			url: '{:U("user/changeServiceId")}',
			type: 'POST',
			data: {
				id: userId,
				service_id: newServiceId
			},
			dataType: 'json',
			success: function(response) {
				if (response.status == 1) {
					layer.msg('修改成功', {icon: 1});
					// 动态更新页面显示，避免刷新导致session问题
					updateUserServiceIdDisplay(userId, newServiceId);
				} else {
					layer.msg(response.msg, {icon: 2});
					// 如果是登录失效，提示用户刷新页面
					if (response.msg && response.msg.indexOf('登录') !== -1) {
						setTimeout(function() {
							layer.confirm('登录已失效，是否刷新页面重新登录？', {
								btn: ['刷新页面', '取消']
							}, function() {
								location.reload();
							});
						}, 1500);
					}
				}
			},
			error: function(xhr, status, error) {
				// 检查是否是登录失效的错误
				if (xhr.status === 200 && xhr.responseJSON && xhr.responseJSON.info && xhr.responseJSON.info.indexOf('登录') !== -1) {
					layer.msg('登录已失效', {icon: 2});
					setTimeout(function() {
						layer.confirm('登录已失效，是否刷新页面重新登录？', {
							btn: ['刷新页面', '取消']
						}, function() {
							location.reload();
						});
					}, 1500);
				} else {
					layer.msg('请求失败，请重试', {icon: 2});
				}
			}
		});

		layer.closeAll();
	}

	// 动态更新用户service_id显示
	function updateUserServiceIdDisplay(userId, newServiceId) {
		var serviceOptions = {
			0: '未分类',
			1: '服务站和招就办',
			2: '简历用户'
		};

		var badgeClass = '';
		if (newServiceId == 1) badgeClass = 'bound';
		else if (newServiceId == 2) badgeClass = 'warning';
		else badgeClass = 'unbound';

		// 找到对应用户的service_id显示元素并更新
		$('.user-list-item').each(function() {
			var $item = $(this);
			var itemUserId = $item.find('.user-item-id').text().replace('ID: ', '');
			if (itemUserId == userId) {
				var $serviceIdBadge = $item.find('.user-info-item:contains("Service ID") .user-status-badge');
				$serviceIdBadge.removeClass('bound warning unbound').addClass(badgeClass);
				$serviceIdBadge.html('<i class="fa fa-tag"></i> ' + newServiceId + ' (' + serviceOptions[newServiceId] + ')');

				// 更新修改按钮的参数
				var $modifyBtn = $item.find('button[onclick*="changeServiceId"]');
				$modifyBtn.attr('onclick', 'changeServiceId(' + userId + ', ' + newServiceId + ')');
				return false; // 找到后退出循环
			}
		});
	}
</script>

<include file="block/footer" />