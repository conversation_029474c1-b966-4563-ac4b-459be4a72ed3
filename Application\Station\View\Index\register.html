<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,viewport-fit=cover">
    <title><if condition="$registerType == 2">添加招就办主任<else/>服务站注册</if></title>
    <script src="/static/js/lrz.all.bundle.js?v={:time()}"></script>
    <link rel="stylesheet" href="https://res.wx.qq.com/open/libs/weui/2.0.1/weui.min.css"/>
    <link rel="stylesheet" href="/static/css/station.css?v={:time()}"/>
    <style>
        /* 基础样式 */
        .copyright {
            margin:25px 0 0 0;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
        .ul .weui-label {
            width: 100px;
        }
        .copyright span {
            font-size: 14px;
            color: #999;
        }

        /* 弹窗样式 */
        .protocol-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(3px);
        }

        .protocol-modal-content {
            background: #fff;
            border-radius: 16px;
            padding: 24px;
            margin: 20px;
            max-width: 400px;
            width: calc(100% - 40px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            transform: scale(0.9);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .protocol-modal.show .protocol-modal-content {
            transform: scale(1);
            opacity: 1;
        }

        .protocol-modal-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .protocol-modal-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #669cfe 0%, #5a8bfc 100%);
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .protocol-modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0 0 8px 0;
        }

        .protocol-modal-subtitle {
            font-size: 14px;
            color: #666;
            margin: 0;
        }

        .protocol-modal-body {
            margin-bottom: 24px;
        }

        .protocol-modal-text {
            font-size: 15px;
            line-height: 1.6;
            color: #555;
            margin-bottom: 16px;
        }

        .protocol-links {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .protocol-link {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            text-decoration: none;
            color: #669cfe;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .protocol-link:hover {
            background: #669cfe;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 156, 254, 0.3);
        }



        .protocol-modal-footer {
            display: flex;
            gap: 12px;
        }

        .protocol-btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .protocol-btn-cancel {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e9ecef;
        }

        .protocol-btn-cancel:hover {
            background: #e9ecef;
        }

        .protocol-btn-confirm {
            background: linear-gradient(135deg, #669cfe 0%, #5a8bfc 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 156, 254, 0.3);
        }

        .protocol-btn-confirm:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(102, 156, 254, 0.4);
        }

        .protocol-btn-confirm:active {
            transform: translateY(0);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .protocol-modal-content {
                margin: 10px;
                padding: 20px;
                width: calc(100% - 20px);
            }

            .protocol-modal-footer {
                flex-direction: column;
            }

            .protocol-btn {
                margin-bottom: 8px;
            }

            .protocol-links {
                flex-direction: column;
                align-items: center;
            }

            .protocol-link {
                width: 100%;
                justify-content: center;
                margin-bottom: 8px;
            }
        }

        /* 页面整体优化 */
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            background: transparent;
        }

        /* 返回按钮优化 */
        .back-arrow {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
        }

        .back-arrow:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateX(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .back-arrow::before {
            content: "←";
            font-size: 18px;
            color: #333;
            font-weight: bold;
        }

        /* 文件上传区域优化 */
        .upload_btn {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .upload_btn:hover {
            border-color: #669cfe;
            background: #f8f9ff;
        }


        .tips {
            position: relative;
            width: 90%;
            height: auto;
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 4px;
            background: #fff;
            border: 1px solid #ccc;
            box-shadow: 0px 0px 3px rgba(0,0,0,0.35);
        }


        .upload_btn {
            background: url(/static/images/img.png) center no-repeat;
            background-size: 6.575rem auto;
            overflow: hidden;
            position: relative;
            width: 6.575rem;
            height: 6.575rem;
            margin:.6rem auto .8rem;

        }

        .upload_btn2{
            width: 6.575rem;
            height:auto;
            margin:1.2rem auto .2rem;
            background:#39F;
            border-radius:.2rem;
            position:relative;

        }
        .upload_btn2 .del {
            position: absolute;
            right: -0.25rem;
            top: -0.25rem;
            background: url(/static/images/icon_del.png) no-repeat;
            background-size: 100%;
            width: 1rem;
            height: 1rem;
            display: inline-block;
        }
        .upload_btn2 img{ display:inline-block; width:6.575rem;}
        .upload_btn input {
            opacity: 0;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 10;
            overflow: hidden;
            height: 6.575rem;
            width: 6.575rem;

        }

        .img_list .del {
            position: absolute;
            right: -0.5rem;
            top: -0.5rem;
            background: url(/static/images/icon_del.png) no-repeat;
            background-size: 100%;
            width: 1.5rem;
            height: 1.5rem;
            display: inline-block;
        }

        
 /* 返回箭头样式 */
.back-arrow {
    position: fixed;
    top: 115px;
    left: 15px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.2s;
}

.back-arrow::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    width: 12px;
    height: 12px;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: translateY(-50%) rotate(45deg);
}

/* 点击反馈效果 */
.back-arrow:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.7);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .back-arrow {
        top: 5px;
        left: 4px;
        width: 36px;
        height: 36px;
    }

    .back-arrow::before {
        left: 13px;
        width: 10px;
        height: 10px;
    }
}

/* 招就办名称自动显示样式 - 参考京东京灵设计风格 */
.auto-zsb-name-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    transition: all 0.3s ease;
}

.auto-zsb-name-container:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

#auto-zsb-name-display {
    background: #fff;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    margin: 8px 0;
    padding: 15px 12px !important;
    position: relative;
    transition: all 0.2s ease;
}



.auto-zsb-name-container .weui-label {
    color: #333 !important;
    font-weight: 600 !important;
    font-size: 15px;
}

/* 表单整体优化 */
.weui-cells_form {
    margin-top: 0;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    border: 1px solid #f0f0f0;
}

.weui-cell {
    padding: 18px 20px;
    border-bottom: 1px solid #f5f5f5;
    transition: all 0.2s ease;
    position: relative;
}

.weui-cell:hover {
    background: #fafbfc;
}

.weui-cell:last-child {
    border-bottom: none;
}

.weui-cell:focus-within {
    background: #f8f9ff;
    border-color: #669cfe;
}

.weui-label {
    font-weight: 600;
    color: #333;
    font-size: 15px;
    min-width: 100px;
    display: flex;
    align-items: center;
}

.weui-label::before {
    content: "";
    width: 3px;
    height: 16px;
    background: linear-gradient(135deg, #669cfe 0%, #5a8bfc 100%);
    border-radius: 2px;
    margin-right: 8px;
}

.weui-input {
    font-size: 16px;
    color: #333;
    border: none;
    outline: none;
    background: transparent;
    transition: all 0.2s ease;
}

.weui-input:focus {
    color: #333;
}

.weui-input::placeholder {
    color: #999;
    font-size: 15px;
}

/* 验证码按钮优化 */
.weui-vcode-btn {
    background: linear-gradient(135deg, #669cfe 0%, #5a8bfc 100%);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(102, 156, 254, 0.3);
}

.weui-vcode-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 156, 254, 0.4);
}

.weui-vcode-btn:active {
    transform: translateY(0);
}

.weui-vcode-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 按钮优化 */
.js_reg_check {
    background: linear-gradient(135deg, #669cfe 0%, #5a8bfc 100%) !important;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 4px 12px rgba(102, 156, 254, 0.3);
    transition: all 0.3s ease;
}

.js_reg_check:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(102, 156, 254, 0.4);
}

/* 页面头部优化 */
.page-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px 15px;
    margin-bottom: 20px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    position: relative;
}

.page-header .weui-form__title {
    position: relative;
    z-index: 1;
}

.page-header .subtitle {
    position: relative;
    z-index: 1;
    background: rgba(255, 255, 255, 0.8);
    padding: 8px 15px;
    border-radius: 20px;
    display: inline-block;
    margin: 0 auto;
}
    </style>
</head>
<body ontouchstart style="background: #f1f1f1; min-height: 100%">
<!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
<div class="container ul"  style="margin: 10px;">
    <div class="page-header" style="text-align: center; margin-bottom: 20px;">
        <h2 class="weui-form__title" style="margin: 0 0 15px 0; font-size: 22px; font-weight: 600; color: #333;">
            <if condition="$registerType == 2">添加招就办主任<else/>服务站注册</if>
        </h2>
        <if condition="$registerType == 2">
            <div class="subtitle" style="font-size: 14px; color: #666; margin-bottom: 10px;">
                请填写您的招就办信息
            </div>
        </if>
    </div>
    <form method="post" id="loginForm">
        <!-- 隐藏字段传递注册类型 -->
        <input type="hidden" name="register_type" value="{$registerType|default=1}">

        <div class="weui-cells weui-cells_form" >
            <!-- 服务站字段 -->
            <if condition="$registerType == 1">
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">公司名称</label></div>
                    <div class="weui-cell__bd">
                        <input class="weui-input" name="enterprise_name" type="text" placeholder="请输入公司名称">
                    </div>
                </div>
            </if>

            <!-- 招就办字段 -->
            <if condition="$registerType == 2">
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">联系人姓名</label></div>
                    <div class="weui-cell__bd">
                        <input class="weui-input" name="contract_name" id="contract_name" type="text" placeholder="请输入联系人姓名">
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">身份证号</label></div>
                    <div class="weui-cell__bd">
                        <input class="weui-input" name="id_card" type="text" placeholder="请输入身份证号" maxlength="18">
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">电子邮件</label></div>
                    <div class="weui-cell__bd">
                        <input class="weui-input" name="email" type="email" placeholder="请输入电子邮件">
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">通讯地址</label></div>
                    <div class="weui-cell__bd">
                        <input class="weui-input" name="mail_address" type="text" placeholder="请输入通讯地址">
                    </div>
                </div>
            </if>

            <!-- 共同字段 -->
            <if condition="$registerType == 1">
                <!-- 服务站名称输入 -->
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">服务站名称</label></div>
                    <div class="weui-cell__bd">
                        <input class="weui-input" name="service_name" type="text" placeholder="请输入服务站名称">
                    </div>
                </div>
            <else/>
                <!-- 招就办名称自动显示 -->
                <div style="margin: 0 15px 10px 15px;">
                    <div class="weui-cell auto-zsb-name-container" style="background: #f8f9fa; border-radius: 8px; margin: 0;">
                        <div class="weui-cell__hd"><label class="weui-label" style="color: #333; font-weight: 500;">招就办名称</label></div>
                        <div class="weui-cell__bd">
                            <div id="auto-zsb-name-display" style="padding: 12px 0; color: #999; font-weight: 500; font-size: 16px;">
                                请输入联系人姓名，系统将自动生成招就办名称
                            </div>
                            <input type="hidden" name="service_name" id="service_name_hidden" value="">
                        </div>
                    </div>
                </div>
            </if>
            <div class="weui-cell weui-cell_vcode" >
                <div class="weui-cell__hd">
                    <label class="weui-label">手机号</label>
                </div>
                <div class="weui-cell__bd">
                    <input class="weui-input" type="number" name="mobile" id="mobile" placeholder="请输入手机号">
                </div>
                <div class="weui-cell__ft">
                    <span class="weui-vcode-btn vcode-btn  getcode" >获取验证码</span>
                </div>
            </div>
            <div class="weui-cell">
                <div class="weui-cell__hd"><label class="weui-label">验证码</label></div>
                <div class="weui-cell__bd">
                    <input class="weui-input" name="code" id="code" type="number" placeholder="请输入验证码">
                </div>
            </div>

            <!-- 服务站专用字段 -->
            <if condition="$registerType == 1">
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">营业执照</label></div>
                    <div class="weui-cell__bd">
                        <div class="img_list js_img_limit" style="position: relative; width: 100px;">
                            <div class="upload_btn">
                                <input type="file" accept="image/*" id="upload_image2" name="business_license_img" >
                                <input type="hidden" name="business_license"  value="" />
                            </div>
                        </div>
                        <div id="img_list_dom"  style="display: none;">
                            <div class="img_list" style="position: relative; width: 100px;">
                                <div style="display: flex; justify-content: space-between; align-content: center; align-items: flex-start;">
                                    <span class="del del_one" style="z-index: 11;"></span>
                                    <img class="js_imgs"  src="" style="max-width:100px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">加盟表</label></div>
                    <div class="weui-cell__bd">
                        <div class="img_list js_img_limit_two" style="position: relative; width: 100px;">
                            <div class="upload_btn">
                                <input type="file" accept=".doc,.docx,.pdf,image/*" id="upload_image3" name="franchise_list_img" >
                                <input type="hidden" name="franchise_list"  value="" />
                            </div>
                        </div>
                        <div id="img_list_dom_two"  style="display: none;">
                            <div class="img_list" style="position: relative; width: 100px;">
                                <div style="display: flex; justify-content: space-between; align-content: center; align-items: flex-start;">
                                    <span class="del del_two" style="z-index: 11;"></span>
                                    <img class="js_imgs_two"  src="" style="max-width:100px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </if>

            <!-- 招就办专用字段 -->
            <if condition="$registerType == 2">
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">证件照</label></div>
                    <div class="weui-cell__bd">
                        <div class="img_list js_img_limit_zj" style="position: relative; width: 100px;">
                            <div class="upload_btn">
                                <input type="file" accept="image/*" id="upload_zj_pic" name="zsb_zj_pic_img" >
                                <input type="hidden" name="zsb_zj_pic"  value="" />
                            </div>
                        </div>
                        <div id="img_list_dom_zj"  style="display: none;">
                            <div class="img_list" style="position: relative; width: 100px;">
                                <div style="display: flex; justify-content: space-between; align-content: center; align-items: flex-start;">
                                    <span class="del del_zj" style="z-index: 11;"></span>
                                    <img class="js_imgs_zj"  src="" style="max-width:100px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">身份证人像面</label></div>
                    <div class="weui-cell__bd">
                        <div class="img_list js_img_limit_rx" style="position: relative; width: 100px;">
                            <div class="upload_btn">
                                <input type="file" accept="image/*" id="upload_sfz_rx" name="zsb_sfz_rx_img" >
                                <input type="hidden" name="zsb_sfz_rx"  value="" />
                            </div>
                        </div>
                        <div id="img_list_dom_rx"  style="display: none;">
                            <div class="img_list" style="position: relative; width: 100px;">
                                <div style="display: flex; justify-content: space-between; align-content: center; align-items: flex-start;">
                                    <span class="del del_rx" style="z-index: 11;"></span>
                                    <img class="js_imgs_rx"  src="" style="max-width:100px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">身份证国徽面</label></div>
                    <div class="weui-cell__bd">
                        <div class="img_list js_img_limit_gh" style="position: relative; width: 100px;">
                            <div class="upload_btn">
                                <input type="file" accept="image/*" id="upload_sfz_gh" name="zsb_sfz_gh_img" >
                                <input type="hidden" name="zsb_sfz_gh"  value="" />
                            </div>
                        </div>
                        <div id="img_list_dom_gh"  style="display: none;">
                            <div class="img_list" style="position: relative; width: 100px;">
                                <div style="display: flex; justify-content: space-between; align-content: center; align-items: flex-start;">
                                    <span class="del del_gh" style="z-index: 11;"></span>
                                    <img class="js_imgs_gh"  src="" style="max-width:100px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">人力资源资质证书</label></div>
                    <div class="weui-cell__bd">
                        <div class="img_list js_img_limit_rlzyzs" style="position: relative; width: 100px;">
                            <div class="upload_btn">
                                <input type="file" accept="image/*" id="upload_rlzyzs" name="zsb_rlzyzs_img" >
                                <input type="hidden" name="zsb_rlzyzs"  value="" />
                            </div>
                        </div>
                        <div id="img_list_dom_rlzyzs"  style="display: none;">
                            <div class="img_list" style="position: relative; width: 100px;">
                                <div style="display: flex; justify-content: space-between; align-content: center; align-items: flex-start;">
                                    <span class="del del_rlzyzs" style="z-index: 11;"></span>
                                    <img class="js_imgs_rlzyzs"  src="" style="max-width:100px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">服务合同</label></div>
                    <div class="weui-cell__bd">
                        <div class="img_list js_img_limit_fwzht" style="position: relative; width: 100px;">
                            <div class="upload_btn">
                                <input type="file" accept="image/*,.pdf" id="upload_fwzht" name="zsb_fwzht_img" >
                                <input type="hidden" name="zsb_fwzht"  value="" />
                            </div>
                        </div>
                        <div id="img_list_dom_fwzht"  style="display: none;">
                            <div class="img_list" style="position: relative; width: 100px;">
                                <div style="display: flex; justify-content: space-between; align-content: center; align-items: flex-start;">
                                    <span class="del del_fwzht" style="z-index: 11;"></span>
                                    <img class="js_imgs_fwzht"  src="" style="max-width:100px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__hd"><label class="weui-label">无犯罪证明</label></div>
                    <div class="weui-cell__bd">
                        <div class="img_list js_img_limit_wfzzm" style="position: relative; width: 100px;">
                            <div class="upload_btn">
                                <input type="file" accept="image/*,.pdf" id="upload_wfzzm" name="zsb_wfzzm_img" >
                                <input type="hidden" name="zsb_wfzzm"  value="" />
                            </div>
                        </div>
                        <div id="img_list_dom_wfzzm"  style="display: none;">
                            <div class="img_list" style="position: relative; width: 100px;">
                                <div style="display: flex; justify-content: space-between; align-content: center; align-items: flex-start;">
                                    <span class="del del_wfzzm" style="z-index: 11;"></span>
                                    <img class="js_imgs_wfzzm"  src="" style="max-width:100px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </if>
        </div>
        <!-- 底部操作区域 -->
        <div class="bottom-action-area" style="margin-top: 24px; background: #fff; border-radius: 16px; padding: 24px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid #f0f0f0;">
            <div id="tips_box" style="margin: 10px 0px;display: flex;align-items: center;justify-content: center;display:none;">
                <div class="tips" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 12px 16px; color: #856404; font-size: 14px; line-height: 1.5; position: relative;">
                    <div style="display: flex; align-items: flex-start; gap: 8px;">
                        <span style="font-size: 16px;">⚠️</span>
                        <div>
                            请务必阅读下方蓝色字体链接，并勾选同意后继续完成注册。<br>如拒绝，请直接关闭本页面即可。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 优化后的条款同意区域 -->
            <div class="protocol-agreement" style="margin-bottom: 24px; background: #f8f9ff; border: 2px solid #e6f0ff; border-radius: 12px; padding: 20px; position: relative;">
                <div style="display: flex; align-items: flex-start; gap: 12px;">
                    <div style="margin-top: 2px;">
                        <input type="radio" class="tips_radio" name="protocol_status" value="1" style="width: 18px; height: 18px; accent-color: #669cfe; cursor: pointer;">
                    </div>
                    <div style="flex: 1;">
                        <div style="color: #333; font-size: 15px; font-weight: 500; margin-bottom: 8px;">
                            服务条款与隐私政策
                        </div>
                        <div style="color: #666; font-size: 14px; line-height: 1.6;">
                            我已仔细阅读并同意
                            <a href="https://station.zhongcaiguoke.com/index/law/id/6" target="_blank" style="color: #669cfe; text-decoration: underline; font-weight: 600;">用户协议</a>
                            和
                            <a href="https://station.zhongcaiguoke.com/index/law/id/4" target="_blank" style="color: #669cfe; text-decoration: underline; font-weight: 600;">隐私政策</a>
                        </div>
                    </div>
                </div>


            </div>

            <div class="button-sp-area" style="padding:0;">
                <button type="button" class="weui-btn weui-btn_block js_reg_check" style="padding: 16px 48px; background: linear-gradient(135deg, #669cfe 0%, #5a8bfc 100%); border: none; border-radius: 12px; font-size: 16px; font-weight: 600; color: white; box-shadow: 0 4px 16px rgba(102, 156, 254, 0.3); transition: all 0.3s ease; cursor: pointer;">
                    <if condition="$registerType == 2">添加招就办主任<else/>注册服务站</if>
                </button>
            </div>

        </div>
    </form>
    <div class="copyright">
        <span>copyright@中才国科</span>
    </div>
</div>

<!-- 条款提醒弹窗 -->
<div id="protocolModal" class="protocol-modal">
    <div class="protocol-modal-content">
        <div class="protocol-modal-header">
            <div class="protocol-modal-icon">!</div>
            <h3 class="protocol-modal-title">请先阅读服务条款</h3>
            <p class="protocol-modal-subtitle">为了保障您的权益，请仔细阅读以下条款</p>
        </div>

        <div class="protocol-modal-body">
            <p class="protocol-modal-text">
                在获取验证码之前，您需要先阅读并同意我们的服务条款和隐私政策。这些条款说明了我们如何收集、使用和保护您的个人信息。
            </p>

            <div class="protocol-links">
                <a href="https://station.zhongcaiguoke.com/index/law/id/6" target="_blank" class="protocol-link">用户协议</a>
                <a href="https://station.zhongcaiguoke.com/index/law/id/4" target="_blank" class="protocol-link">隐私政策</a>
            </div>
        </div>

        <div class="protocol-modal-footer">
            <button type="button" class="protocol-btn protocol-btn-cancel" onclick="closeProtocolModal()">稍后再说</button>
            <button type="button" class="protocol-btn protocol-btn-confirm" onclick="agreeProtocol()">我已阅读并同意</button>
        </div>
    </div>
</div>
<script src="/static/js/jquery2_2_4.min.js"></script>
<script src="/static/js/layer/layer.m.js"></script>
<script>
    msgs = function(msg) {
        layer.open({ content: msg ,skin: 'msg' ,time: 2  });
    };
    $('input[name="business_license_img"]').on('change', function(){
        layer.open({type: 2});
        lrz(this.files[0], {width:20000})
            .then(function (rst) {
                $.ajax({
                    url: '{:U("index/uploads")}',
                    type: 'post',
                    data: {img: rst.base64},
                    dataType: 'json',
                    timeout: 60000,
                    error: function () {
                        msgs('营业执照上传错误')
                    },
                    success: function (data) {
                        layer.closeAll()
                        if (data.status == 1) {
                            $('#img_list_dom').show();
                            $('.js_imgs').attr('src', data.info);
                            $('.js_img_limit').hide();
                            $('input[name="business_license"]').val(data.info);
                        } else {
                            msgs('营业执照上传错误')
                        }
                    },
                });

            })
            .catch(function (err) {
                layer.closeAll();
                msgs("头像获取错误")
            })
            .always(function () {
            });
    });

    //删除图片
    $('#img_list_dom').on('click', '.del', function () {
        $('#img_list_dom').hide();
        $('.js_imgs').attr('src', '');
        $('.js_img_limit').show();
        $('input[name="business_license"]').val('');
    })



    $('input[name="franchise_list_img"]').on('change', function(){
        layer.open({type: 2});
        lrz(this.files[0], {width:20000})
            .then(function (rst) {
                $.ajax({
                    url: '{:U("index/uploads")}',
                    type: 'post',
                    data: {img: rst.base64},
                    dataType: 'json',
                    timeout: 60000,
                    error: function () {
                        msgs('图片上传错误')
                    },
                    success: function (data) {
                        layer.closeAll()
                        if (data.status == 1) {
                            $('#img_list_dom_two').show();
                            $('.js_imgs_two').attr('src', data.info);
                            $('.js_img_limit_two').hide();
                            $('input[name="franchise_list"]').val(data.info);
                        } else {
                            msgs('加盟表上传错误')
                        }
                    },
                });

            }).catch(function (err) {
                layer.closeAll();
                msgs("加盟表获取错误")
            })
            .always(function () {
            });
    });

    //删除图片
    $('#img_list_dom_two').on('click', '.del', function () {
        $('#img_list_dom_two').hide();
        $('.js_imgs_two').attr('src', '');
        $('.js_img_limit_two').show();
        $('input[name="franchise_list"]').val('');
    })

    // 招就办字段上传处理
    // 证件照上传
    $('input[name="zsb_zj_pic_img"]').on('change', function(){
        uploadZjbImage(this, 'zsb_zj_pic', 'zj', '证件照');
    });
    $('#img_list_dom_zj').on('click', '.del', function () {
        deleteZjbImage('zj', 'zsb_zj_pic');
    });

    // 身份证人像面上传
    $('input[name="zsb_sfz_rx_img"]').on('change', function(){
        uploadZjbImage(this, 'zsb_sfz_rx', 'rx', '身份证人像面');
    });
    $('#img_list_dom_rx').on('click', '.del', function () {
        deleteZjbImage('rx', 'zsb_sfz_rx');
    });

    // 身份证国徽面上传
    $('input[name="zsb_sfz_gh_img"]').on('change', function(){
        uploadZjbImage(this, 'zsb_sfz_gh', 'gh', '身份证国徽面');
    });
    $('#img_list_dom_gh').on('click', '.del', function () {
        deleteZjbImage('gh', 'zsb_sfz_gh');
    });

    // 人力资源资质证书上传
    $('input[name="zsb_rlzyzs_img"]').on('change', function(){
        uploadZjbImage(this, 'zsb_rlzyzs', 'rlzyzs', '人力资源资质证书');
    });
    $('#img_list_dom_rlzyzs').on('click', '.del', function () {
        deleteZjbImage('rlzyzs', 'zsb_rlzyzs');
    });

    // 服务合同上传
    $('input[name="zsb_fwzht_img"]').on('change', function(){
        uploadZjbImage(this, 'zsb_fwzht', 'fwzht', '服务合同');
    });
    $('#img_list_dom_fwzht').on('click', '.del', function () {
        deleteZjbImage('fwzht', 'zsb_fwzht');
    });

    // 无犯罪证明上传
    $('input[name="zsb_wfzzm_img"]').on('change', function(){
        uploadZjbImage(this, 'zsb_wfzzm', 'wfzzm', '无犯罪证明');
    });
    $('#img_list_dom_wfzzm').on('click', '.del', function () {
        deleteZjbImage('wfzzm', 'zsb_wfzzm');
    });

    // 恢复上传文件显示的函数
    function restoreUploadedFiles(savedData) {
        // 服务站文件恢复
        if (savedData.business_license) {
            $('#img_list_dom').show();
            $('.js_imgs').attr('src', savedData.business_license);
            $('.js_img_limit').hide();
            $('input[name="business_license"]').val(savedData.business_license);
        }
        if (savedData.franchise_list) {
            $('#img_list_dom_two').show();
            $('.js_imgs_two').attr('src', savedData.franchise_list);
            $('.js_img_limit_two').hide();
            $('input[name="franchise_list"]').val(savedData.franchise_list);
        }

        // 招就办文件恢复
        var zsbFields = [
            {field: 'zsb_zj_pic', suffix: 'zj'},
            {field: 'zsb_sfz_rx', suffix: 'rx'},
            {field: 'zsb_sfz_gh', suffix: 'gh'},
            {field: 'zsb_rlzyzs', suffix: 'rlzyzs'},
            {field: 'zsb_fwzht', suffix: 'fwzht'},
            {field: 'zsb_wfzzm', suffix: 'wfzzm'}
        ];

        zsbFields.forEach(function(item) {
            if (savedData[item.field]) {
                $('#img_list_dom_' + item.suffix).show();
                $('.js_imgs_' + item.suffix).attr('src', savedData[item.field]);
                $('.js_img_limit_' + item.suffix).hide();
                $('input[name="' + item.field + '"]').val(savedData[item.field]);
            }
        });
    }

    // 招就办文件上传通用函数（支持图片和PDF）
    function uploadZjbImage(fileInput, hiddenFieldName, suffix, fieldLabel) {
        var file = fileInput.files[0];
        if (!file) return;

        layer.open({type: 2});

        // 检查文件类型
        var fileType = file.type;
        var isImage = fileType.startsWith('image/');
        var isPDF = fileType === 'application/pdf';

        if (!isImage && !isPDF) {
            layer.closeAll();
            msgs(fieldLabel + '只支持图片和PDF文件');
            return;
        }

        // 将文件转换为base64
        var reader = new FileReader();
        reader.onload = function(e) {
            var base64Data = e.target.result;

            // 如果是图片，使用lrz压缩
            if (isImage) {
                lrz(file, {width:20000})
                    .then(function (rst) {
                        uploadFileToServer(rst.base64, hiddenFieldName, suffix, fieldLabel);
                    })
                    .catch(function (err) {
                        layer.closeAll();
                        msgs(fieldLabel + "图片处理错误");
                    });
            } else {
                // 如果是PDF，直接上传
                uploadFileToServer(base64Data, hiddenFieldName, suffix, fieldLabel);
            }
        };
        reader.readAsDataURL(file);
    }

    // 文件上传到服务器的通用函数
    function uploadFileToServer(base64Data, hiddenFieldName, suffix, fieldLabel) {
        $.ajax({
            url: '{:U("index/uploads")}',
            type: 'post',
            data: {img: base64Data},
            dataType: 'json',
            timeout: 60000,
            error: function () {
                layer.closeAll();
                msgs(fieldLabel + '上传错误');
            },
            success: function (data) {
                layer.closeAll();
                if (data.status == 1) {
                    $('#img_list_dom_' + suffix).show();

                    // 如果是PDF文件，显示PDF标识而不是预览图
                    if (data.info.endsWith('.pdf')) {
                        $('.js_imgs_' + suffix).attr('src', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y0MDAwMCIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+UERGPC90ZXh0Pjwvc3ZnPg==').attr('alt', 'PDF文件');
                    } else {
                        $('.js_imgs_' + suffix).attr('src', data.info);
                    }

                    $('.js_img_limit_' + suffix).hide();
                    $('input[name="' + hiddenFieldName + '"]').val(data.info);
                } else {
                    msgs(fieldLabel + '上传错误');
                }
            }
        });
    }

    // 招就办图片删除通用函数
    function deleteZjbImage(suffix, hiddenFieldName) {
        $('#img_list_dom_' + suffix).hide();
        $('.js_imgs_' + suffix).attr('src', '');
        $('.js_img_limit_' + suffix).show();
        $('input[name="' + hiddenFieldName + '"]').val('');
    }

    var is_invite_code = false;
    var reg_check_send = false;
    //检查用户协议勾选状态
    function checkProtocolStatus(){
        var val=$('input:radio[name="protocol_status"]:checked').val();
        if(val==null){
            //获取radio 距离左边距离
            $('#tips_box').css('display','flex');
            let radioLeft = $('.tips_radio').offset().left;
            let radioHeight = $('.tips').height() - 1;
            let leftNum = Math.ceil(radioLeft) - ($(window).width()*0.031)
            $('.tips').append(`<style>.tips:after {
                    position: absolute;
                    display: inline-block;
                    top: ${radioHeight}px;
                    left: ${leftNum}px;
                    width: 1px;
                    height: 0px;
                    content: '';
                    border-style: solid;
                    border-width: 8px;
                    border-color: #fff #fff transparent transparent;
                    transform: rotate(135deg);
                    box-shadow: 2px -2px 2px #ccc;
                }</style>`);
            return false;
        }else{
            return  true;
        }
    }
    $(function () {
        var send = false;

        // 恢复保存的表单数据
        <if condition="$savedFormData">
        var savedData = {$savedFormData|json_encode};
        if (savedData) {
            // 恢复文本输入框的值
            for (var field in savedData) {
                if (savedData.hasOwnProperty(field)) {
                    var $input = $('input[name="' + field + '"]');
                    if ($input.length && $input.attr('type') !== 'file') {
                        $input.val(savedData[field]);
                    }
                }
            }

            // 恢复上传的文件显示
            restoreUploadedFiles(savedData);

            // 恢复条款同意状态
            if (savedData.protocol_status) {
                $('input[name="protocol_status"]').prop('checked', true);
                $('#tips_box').hide();
            }
        }
        </if>

        // 如果是招就办注册，监听联系人姓名输入，动态生成招就办名称
        <if condition="$registerType == 2">
        // 获取服务站名称
        loadStationName();

        // 监听联系人姓名输入
        $('#contract_name').on('input', function() {
            generateZsbName();
        });
        </if>

        $('.getcode').click(function(){
            let res = checkProtocolStatus();
            if(!res){
                // 显示友好的弹窗提醒
                showProtocolModal();
                return false;
            }

            var tel = $('#mobile').val();
            if (!(/^1[3456789]\d{9}$/.test(tel))) {
                layer.open({
                    content: '请输入正确的手机号码',
                    skin: 'msg',
                    time: 2
                });
                return false;
            }
            if (send == false) {
                // 添加加载状态
                $('.getcode').text('发送中...').attr('disabled', true);

                $.getJSON('{:U("index/getcode")}?mobile=' + tel, function (ret) {
                    var msg = ret.msg;
                    if (ret.code == 0 || ret.code == 2) {
                        $('.getcode').attr('disabled', true).text('已发送').css({
                            'background': '#28a745',
                            'color': 'white'
                        });
                        msg = '✅ 验证码已发送到您的手机！';
                        send = true;
                        if (ret.is_invite_code != undefined && ret.is_invite_code == 1) {
                            is_invite_code = true;
                            $('.js_invite_code').show();
                        } else {
                            is_invite_code = false;
                            $('.js_invite_code').hide();
                        }
                    } else {
                        $('.getcode').text('获取验证码').attr('disabled', false);
                    }
                    layer.open({
                        content: msg,
                        btn: ['确认'],
                        yes: function (index) {
                            layer.close(index);
                            $('#code').focus();
                        },
                    });
                }).fail(function() {
                    $('.getcode').text('获取验证码').attr('disabled', false);
                    layer.open({
                        content: '网络错误，请重试',
                        skin: 'msg',
                        time: 2
                    });
                });
            } else {
                layer.open({
                    content: '验证码已发送，请查收',
                    btn: ['确认'],
                    yes: function (index) {
                        layer.close(index);
                        $('#code').focus();
                    },
                });
            }
            return false;
        });

        //注册前检测
        $('.js_reg_check').click(function(){
            let res = checkProtocolStatus();
            if(!res){
                return false;
            }
            var tel = $('#mobile').val();
            var code = $('#code').val();
            if (!(/^1[3456789]\d{9}$/.test(tel))) {
                layer.open({content: '手机号填写不正确！'});
                return false;
            }
            if (code == '') {
                layer.open({content: '验证码未填写！'});
                return false;
            }
            $("#loginForm").submit();
        });


        //用户协议提示
        $('.tips_radio').click(function(){
            $('#tips_box').css('display','none');
            // 添加选中动画效果
            $('.protocol-agreement').addClass('protocol-checked');
            setTimeout(function() {
                $('.protocol-agreement').removeClass('protocol-checked');
            }, 300);
        });

        // 添加协议选中的CSS动画
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .protocol-checked {
                    transform: scale(1.02);
                    box-shadow: 0 4px 20px rgba(102, 156, 254, 0.2);
                    border-color: #669cfe;
                }
                .protocol-agreement {
                    transition: all 0.3s ease;
                }
                .protocol-link:hover {
                    background: #669cfe !important;
                    color: white !important;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(102, 156, 254, 0.3);
                }
                .js_reg_check:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px rgba(102, 156, 254, 0.4);
                }
                .js_reg_check:active {
                    transform: translateY(0);
                }
            `)
            .appendTo('head');
    });

        // 返回逻辑（优先返回历史记录，失败则跳转首页）
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/'; // 修改为你的默认回退地址
            }
        }

        // 全局变量存储服务站名称
        var stationName = '';

        // 加载服务站名称
        function loadStationName() {
            $.ajax({
                url: '{:U("index/getStationInfo")}',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.status == 1) {
                        stationName = response.data.station_name;
                        generateZsbName(); // 尝试生成招就办名称
                    } else {
                        $('#auto-zsb-name-display').html('获取服务站信息失败：' + response.msg).css('color', '#f56c6c');
                    }
                },
                error: function() {
                    $('#auto-zsb-name-display').html('网络错误，无法获取服务站信息').css('color', '#f56c6c');
                }
            });
        }

        // 生成招就办名称
        function generateZsbName() {
            var contactName = $('#contract_name').val().trim();

            if (stationName && contactName) {
                var zsbName = stationName + '-' + contactName;
                $('#auto-zsb-name-display').html(zsbName).css('color', '#00bf80');
                $('#service_name_hidden').val(zsbName);
            } else if (stationName && !contactName) {
                $('#auto-zsb-name-display').html('请输入联系人姓名，系统将自动生成招就办名称').css('color', '#999');
                $('#service_name_hidden').val('');
            } else {
                $('#auto-zsb-name-display').html('正在获取服务站信息...').css('color', '#999');
                $('#service_name_hidden').val('');
            }
        }

        // 弹窗相关函数
        function showProtocolModal() {
            const modal = document.getElementById('protocolModal');
            modal.style.display = 'flex';
            // 添加显示动画
            setTimeout(function() {
                modal.classList.add('show');
            }, 10);

            // 阻止背景滚动
            document.body.style.overflow = 'hidden';
        }

        function closeProtocolModal() {
            const modal = document.getElementById('protocolModal');
            modal.classList.remove('show');

            setTimeout(function() {
                modal.style.display = 'none';
                document.body.style.overflow = '';
            }, 300);
        }

        function agreeProtocol() {
            // 自动勾选协议
            $('.tips_radio').prop('checked', true).trigger('click');

            // 关闭弹窗
            closeProtocolModal();

            // 直接触发获取验证码
            $('.getcode').trigger('click');
        }

        // 点击弹窗背景关闭
        $(document).on('click', '.protocol-modal', function(e) {
            if (e.target === this) {
                closeProtocolModal();
            }
        });

        // ESC键关闭弹窗
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27) { // ESC键
                closeProtocolModal();
            }
        });

        // 优化表单输入体验
        $('.weui-input').on('focus', function() {
            $(this).closest('.weui-cell').addClass('input-focused');
        }).on('blur', function() {
            $(this).closest('.weui-cell').removeClass('input-focused');
        });

        // 添加输入焦点样式
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .input-focused {
                    background: #f8f9ff !important;
                    border-color: #669cfe !important;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(102, 156, 254, 0.1);
                }
            `)
            .appendTo('head');
</script>
</body>
</html>
