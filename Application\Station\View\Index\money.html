﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <title>余额管理</title>
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <style>
        :root {
            --primary-color: #00bf80;
            --secondary-color: #666;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, system-ui, sans-serif;
        }

   

        .container {
          
            margin: 10px;
        }

        /* 新版头部 */

        .delay-notice {
            color: #e67e22;
            background: #fdf6ec;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        .zje{
            font-size: 16px;
            color: #000;
            margin-bottom: 8px;
            font-weight: bold;
        }
        /* 金额区域升级版 */
        .amount-section {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            text-align: center;
        }
        .amount-group {
            margin-bottom: 16px;
        }
        .total-amount {
            font-size: 36px;
            color: var(--primary-color);
            font-weight: 600;
            line-height: 1.3;
        }
        .available-amount {
            font-size: 16px;
            color: #999696;
            margin-top: 4px;
        }
        .time-tip {
            color: var(--secondary-color);
            font-size: 14px;
            margin: 12px 0 24px;
        }

        /* 按钮组优化 */
        .action-btns {
            display: grid;
            gap: 12px;
        }
        .btn {
            padding: 14px;
            border-radius: 6px;
            border: 1px solid var(--primary-color);
            background: #59c567;
            color: #fff;
            font-size: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            position: relative;
        }
        .btn::after {
            position: absolute;
            right: 14px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,191,128,0.1);
            color: var(--primary-color);
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 2px;
        }

        /* 双栏布局优化 */
        .columns {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 24px;
            margin-top: 20px;
        }

        /* 左侧菜单新增图标 */
        .menu-section {
            background: #fff;
            border-radius: 8px;
            padding: 16px;
        }
        .menu-item {
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .menu-item::before {
            content: "•";
            color: var(--primary-color);
        }

        /* 温馨提示样式升级 */
        .tips-section {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
        }
        .tips-list li {
            color: #a8a6a6;
            margin-bottom: 16px;
            padding-left: 24px;
            text-indent: -24px;
            
        }
        .tips-list li::before {
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .columns {
                grid-template-columns: 1fr;
            }
            .total-amount {
                font-size: 28px;
            }
            .btn {
                font-size: 18px;
                padding: 12px;
            }
        }
              
        /* 返回箭头样式 */
        .back-arrow {
            position: fixed;
            top: 115px;
            left: 15px;
            width: 40px;
            height: 40px;
            z-index: 9999;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            transition: all 0.2s;
        }

        .back-arrow::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 50%;
            width: 12px;
            height: 12px;
            border-left: 2px solid #333;
            border-bottom: 2px solid #333;
            transform: translateY(-50%) rotate(45deg);
        }

        /* 点击反馈效果 */
        .back-arrow:active {
            transform: scale(0.95);
            background: rgba(255, 255, 255, 0.7);
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .back-arrow {
                top: 220px;
                left: 10px;
                width: 36px;
                height: 36px;
            }
            
            .back-arrow::before {
                left: 13px;
                width: 10px;
                height: 10px;
            }
        }
        
        /* 额度卡片样式 */
        .quota-card {
            background: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .quota-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        .quota-details-link {
            font-size: 14px;
            color: #59c567;
            font-weight: normal;
        }
        .progress-bar {
            height: 10px;
            background: #f1f1f1;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 5px;
        }
        .progress-fill {
            height: 100%;
            width: <php>echo $quota['percent'];</php>%;
            background: <php>echo $quota['is_exceed'] ? '#f56c6c' : '#67c23a';</php>;
            border-radius: 5px;
        }
        .quota-labels {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .quota-status {
            font-size: 15px;
            text-align: center;
            padding: 8px 0;
        }
        .quota-normal {
            color: #27ae60;
        }
        .quota-exceeded {
            color: #e74c3c;
        }

        /* 京东京灵平台绑定弹窗样式 */
        .jd-binding-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(3px);
        }
        
        .jd-binding-content {
            width: 90%;
            max-width: 350px;
            background: #fff;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            text-align: center;
        }
        
        .jd-binding-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .jd-binding-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .qr-code-container {
            margin: 0 auto 20px;
            width: 200px;
            height: 200px;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .qr-code-container img {
            max-width: 100%;
            max-height: 100%;
        }
        
        .jd-binding-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .jd-binding-btn {
            padding: 12px;
            border-radius: 6px;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .jd-binding-btn.primary {
            background: #59c567;
            color: #fff;
        }
        
        .jd-binding-btn.secondary {
            background: #f5f5f5;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
    <include file="headers"/>
    <div class="container">
        <!--div class="header">
            <div class="delay-notice">流程：申请提现 -> 财务审核 -> 云账户完税付款</div>
        </div-->

        <div class="amount-section">
            <div class="amount-group">
                <H1>总金额</H1>
                <div class="total-amount">¥{:$serviceStationRow['total_price'] ?:0}</div>
                <if condition="$serviceStationRow['zsb_type'] neq 2">
                    <div class="available-amount">可提现：<a href="{:U('index/money_details')}" style="color: #7e7d7c;">¥{:$serviceStationRow['price'] ?:0} ></a></div>
                <else />
                    <div class="available-amount">可用余额：<a href="{:U('index/money_details')}" style="color: #7e7d7c;">¥{:$serviceStationRow['price'] ?:0} ></a></div>
                </if>
            </div>
            <if condition="$serviceStationRow['zsb_type'] neq 2">
                <div class="time-tip">请先仔细阅读下方"温馨提示"</div>

                <div class="action-btns">
                    <a href="{:U('Index/money_card')}" class="btn">余额提现</a>
                </div>
            </if>
        </div>

        <!-- 额度信息卡片 - 仅对非招就办用户显示 -->
        <if condition="$serviceStationRow['zsb_type'] neq 2">
            <div class="quota-card">
                <div class="quota-title">
                    <span>每月完税额度 - {$quota.year_month}</span>
                    <a href="{:U('index/check_quota')}" class="quota-details-link">查看详情</a>
                </div>
                <div class="quota-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {$quota.percent}%;
                             background-color: <if condition="$quota.is_exceed">#e74c3c<else/>#27ae60</if>;">
                        </div>
                    </div>
                    <div class="quota-stats">
                        <span>已用: ¥{$quota.used_amount}</span>
                        <span>剩余: ¥{$quota.remaining_quota}</span>
                    </div>
                </div>
                <div class="quota-status">
                    <if condition="$quota.is_exceed">
                        <span class="quota-exceeded">超出额度，后续提现需开具发票</span>
                    <else/>
                        <span class="quota-normal">额度内，平台负责完税</span>
                    </if>
                </div>
            </div>
        </if>

        <div class="columns">
            <div class="menu-section" style="font-size:15px;">
                <div class="menu-item"><a href="{:U('index/money_alldetails')}" style="font-size: 18px;">资产明细 <span style="color: #ccc;">></span></a></div>
                <if condition="$serviceStationRow['zsb_type'] neq 2">
                    <div class="menu-item"><a href="{:U('index/money_withdrawal')}" style="font-size: 18px;">提现进度 <span style="color: #ccc;">></span></a></div>
                    <div class="menu-item"><a href="{:U('index/check_quota')}" style="font-size: 18px;">查看平台完税额度 <span style="color: #ccc;">></span></a></div>
                    <div class="menu-item"><a href="{:U('Index/free_limit_info')}" style="font-size: 18px;">查看免手续费额度 <span style="color: #ccc;">></span></a></div>
                </if>
            </div>

            <div class="tips-section">
                <div class="zje">温馨提示</div>
                <if condition="$serviceStationRow['zsb_type'] neq 2">
                    <ol class="tips-list" style="margin-top: 18px;">
                        <li>1. 技术服务费8%，处理提现时从提现金额扣除。<br>
                            - 每月9.8万内平台负责完税（通过云账户灵活用工平台以个人经营形式完税）；<br>
                            - 每月9.8万超出部分的提现流程为：申请提现 --> 财务审核 --> 您开发票 --> 财务转账。</li>
                        <li>2. 仅支持提现到登记企业/个体户对应法人或授权联络人账户，不支持其他第三方收款。</li>
                        <li>3. 银行卡提现全面升级资金风控系统<br>
                            - 账号安全情况下，提现可以免手机验证<br>
                            - 如果检测到账户有风险，提现需验证手机号，账户资金安全更有保证。</li>
                        <li>4. 周一至周五工作日内9:00-17:00，5万及以下金额财务转账后秒到账（银行系统升级、提现需安全验证等特殊情况除外），其他时间段或超出5万的资金提现第二个工作日到账。</li>
                    </ol>
                <else />
                    <ol class="tips-list" style="margin-top: 18px;">
                        <li>1. 招就办用户余额主要用于统计和管理。</li>
                        <li>2. 余额数据实时更新，您可以通过资产明细查看详细的收支记录。</li>
                        <li>3. 如有余额相关疑问，请联系客服获取帮助。</li>
                    </ol>
                </if>
            </div>
        </div>
    </div>

    <!-- 京东京灵平台绑定弹窗 -->
    <div class="jd-binding-modal" id="jdBindingModal">
        <div class="jd-binding-content">
            <div class="jd-binding-title">京东京灵平台灵工绑定</div>
            <div class="jd-binding-desc">请扫描下方二维码进行京东京灵平台灵工绑定，或点击"已绑定"按钮填写绑定信息。</div>
            <div class="qr-code-container">
                <img src="{$bindingQrCode}" alt="绑定二维码">
            </div>
            <div class="jd-binding-buttons">
                <a href="{:U('Index/jdBindingForm')}" class="jd-binding-btn primary">已绑定请填写绑定信息</a>
                <button class="jd-binding-btn secondary" onclick="closeJdBindingModal()">暂不绑定</button>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        var kwd = '{$kwd}';
        var url = "{:U('index/moeny')}"
        var sortShowTime = '{:$show_time}';
        var sortShowNum = '{:$show_num}';
        $(document).ready(function(){
        $(".tabs-hd02 ul li").click(function(){
          $(this).siblings().removeClass("on");
          $(this).addClass("on");
        });
        $(".tabs-hd02 ul li").click(function(){
          $(this).siblings().removeClass("on");
          $(this).addClass("on");
        });
        
            $(".screen01 li").click(function(){
                var typeOnOrUp = 0;
                var dataType = $(this).attr('data-type');
                if($(this).is('.ondown')){
                    $(this).removeClass('ondown');
                    $(this).siblings().removeClass("ondown");
                    $(this).siblings().removeClass('onup');
                    $(this).addClass('onup');
                    typeOnOrUp = 1;
                } else {
                    $(this).removeClass('onup');
                    $(this).siblings().removeClass("ondown");
                    $(this).siblings().removeClass('onup');
                    $(this).addClass('ondown');
                    typeOnOrUp = 2;
                }
                if (dataType == 'sortShowTime') {
                    sortShowNum = 0;
                    sortShowTime = typeOnOrUp;
                } else {
                    sortShowTime = 0;
                    sortShowNum = typeOnOrUp;
                }
                window.location.href = url + '?kwd='+kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum;
                return
            });
            $('.js_kwd').blur(function () {
                var  new_kwd = $(this).val();
                if (new_kwd != kwd) {
                    window.location.href = url + '?kwd='+new_kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum;
                    return
                }
            })
        
            var noticeSwiper = new Swiper('.noticeSwiper', {
                direction: 'vertical',
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                }
            })
            var noticeSwiper = new Swiper('.page0106Swiper', {
                pagination: {
                    el: ".swiper-pagination",
                },
        
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                }
            });
         
        });

                // 返回逻辑（优先返回历史记录，失败则跳转首页）
                function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/'; // 修改为你的默认回退地址
            }
        }

        // 京东京灵平台绑定弹窗相关
        function showJdBindingModal() {
            document.getElementById('jdBindingModal').style.display = 'flex';
        }

        function closeJdBindingModal() {
            document.getElementById('jdBindingModal').style.display = 'none';
        }

        // 页面加载完成检查是否需要显示绑定弹窗
        $(document).ready(function() {
            <if condition="!$isJdBound && $serviceStationRow['zsb_type'] == 1">
                // 只对服务站用户显示京东京灵绑定弹窗，招就办用户不显示
                // 等待1秒后显示弹窗，避免页面加载时就弹出
                setTimeout(function() {
                    showJdBindingModal();
                }, 500);
            </if>
        });
        </script>
</body>
</html>