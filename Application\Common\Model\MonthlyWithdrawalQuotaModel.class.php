<?php
namespace Common\Model;

use Think\Model;

class MonthlyWithdrawalQuotaModel extends Model
{
    protected $_auto = [
        ['created_at', 'time', self::MODEL_INSERT, 'function'],
        ['updated_at', 'time', self::MODEL_BOTH, 'function'],
    ];

    /**
     * 获取服务站当月额度使用情况
     * @param int $stationId 服务站ID
     * @param string|null $yearMonth 指定年月，默认当月，格式：YYYYMM
     * @return array|null 额度记录
     */
    public function getStationMonthlyQuota($stationId, $yearMonth = null)
    {
        if (!$yearMonth) {
            $yearMonth = date('Ym');
        }
        
        $quota = $this->where([
            'service_station_id' => $stationId,
            'year_month' => $yearMonth
        ])->find();
        
        if (!$quota) {
            // 不存在则创建新记录
            $quota = [
                'service_station_id' => $stationId,
                'year_month' => $yearMonth,
                'used_amount' => 0,
                'total_quota' => 98000.00,
                'remaining_quota' => 98000.00,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $id = $this->add($quota);
            if ($id) {
                $quota['id'] = $id;
            }
        }
        
        return $quota;
    }
    
    /**
     * 更新服务站当月额度使用情况
     * @param int $stationId 服务站ID
     * @param float $amount 本次提现金额
     * @param string|null $yearMonth 指定年月，默认当月
     * @return array [是否超额, 更新后的额度记录, 超额部分]
     */
    public function updateQuotaUsage($stationId, $amount, $yearMonth = null)
    {
        if (!$yearMonth) {
            $yearMonth = date('Ym');
        }
        
        // 获取当前额度情况
        $quota = $this->getStationMonthlyQuota($stationId, $yearMonth);
        
        // 计算实际需要扣除的额度（只扣除平台完税部分）
        $actualDeduction = min($amount, $quota['remaining_quota']);
        
        // 计算新的已使用额度和剩余额度
        $newUsedAmount = $quota['used_amount'] + $actualDeduction;
        $newRemainingQuota = $quota['total_quota'] - $newUsedAmount;
        
        // 更新额度记录
        $this->where(['id' => $quota['id']])->save([
            'used_amount' => $newUsedAmount,
            'remaining_quota' => $newRemainingQuota,
            'updated_at' => time()
        ]);
        
        // 重新获取更新后的记录
        $updatedQuota = $this->find($quota['id']);
        
        // 确定是否超出每月9.8万额度
        $isExceedQuota = $amount > $actualDeduction;
        
        // 计算超额部分（用于混合处理情况）
        $exceededAmount = $isExceedQuota ? ($amount - $actualDeduction) : 0;
        
        return [$isExceedQuota, $updatedQuota, $exceededAmount];
    }
    
    /**
     * 计算提现对额度的影响（不更新数据库）
     * @param int $stationId 服务站ID
     * @param float $amount 本次提现金额
     * @param string|null $yearMonth 指定年月，默认当月
     * @return array [平台完税金额, 用户需开票金额, 当前额度信息]
     */
    public function calculateQuotaImpact($stationId, $amount, $yearMonth = null)
    {
        if (!$yearMonth) {
            $yearMonth = date('Ym');
        }
        
        // 获取当前额度情况
        $quota = $this->getStationMonthlyQuota($stationId, $yearMonth);
        
        // 计算剩余额度
        $remainingQuota = $quota['remaining_quota'];
        
        // 如果额度充足
        if ($remainingQuota >= $amount) {
            return [$amount, 0, $quota];
        }
        
        // 如果额度不足，但还有部分额度
        if ($remainingQuota > 0) {
            return [$remainingQuota, $amount - $remainingQuota, $quota];
        }
        
        // 如果完全没有额度
        return [0, $amount, $quota];
    }
    
    /**
     * 返还提现额度（用于驳回提现申请时）
     * @param int $stationId 服务站ID
     * @param float $amount 需要返还的金额
     * @param string|null $yearMonth 指定年月，默认当月
     * @return array 更新后的额度记录
     */
    public function refundQuotaUsage($stationId, $amount, $yearMonth = null)
    {
        if (!$yearMonth) {
            $yearMonth = date('Ym');
        }
        
        // 获取当前额度情况
        $quota = $this->getStationMonthlyQuota($stationId, $yearMonth);
        
        // 计算新的已使用额度和剩余额度
        $newUsedAmount = max(0, $quota['used_amount'] - $amount); // 确保不会出现负数
        $newRemainingQuota = $quota['total_quota'] - $newUsedAmount;
        
        // 更新额度记录
        $this->where(['id' => $quota['id']])->save([
            'used_amount' => $newUsedAmount,
            'remaining_quota' => $newRemainingQuota,
            'updated_at' => time()
        ]);
        
        // 重新获取更新后的记录
        $updatedQuota = $this->find($quota['id']);
        
        return $updatedQuota;
    }
} 