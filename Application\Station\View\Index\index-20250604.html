﻿<!DOCTYPE html>
<html >
<head>
<title>管理中心</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta content="no-cache" http-equiv="pragma">
<meta content="0" http-equiv="expires">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<link rel="stylesheet" type="text/css" href="/static/stations/css/css.css?v={:time()}" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
<script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
<script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
<script src="/static/js/layer/layer.m.js"></script>

<style>


    /* 添加按钮样式 */
    .add-btn {
        margin-top: 2px;
        padding: 10px 18px;
        background: #FF6B35;
        color: white;
        border: none;
        border-radius: 10px;
        font-size: 17px;
        cursor: pointer;
        transition: all 0.2s;
        box-shadow: 0 4px 12px rgba(0,191,128,0.3);
    }

    .add-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0,191,128,0.4);
    }

    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        justify-content: center;
        align-items: center;
        z-index: 999;
    }

    /* 大图样式 */
    .modal-img {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
    }

    /* 缩略图样式 */
    .thumbnail {
        width: 200px;
        height: 200px;
        cursor: pointer;
        object-fit: cover;
    }

    /* 模态框遮罩层 */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    /* 模态框内容 */
    .modal-content {
        background: white;
        padding: 32px;
        border-radius: 16px;
        width: 90%;
        max-width: 500px;
        position: relative;
        animation: modalSlide 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    }

    @keyframes modalSlide {
        from {
            transform: scale(0.8);
            opacity: 0;
        }
        to {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* 操作类型选择 */
    .method-section {
        margin-bottom: 28px;
    }

    .method-title {
        color: #333;
        font-size: 20px;
        margin-bottom: 16px;
        text-align: center;
    }

    .method-options {
        display: grid;
        gap: 16px;
    }

    /* 上传区域 */
    .upload-box {
        border: 2px dashed #00bf80;
        border-radius: 12px;
        padding: 24px;
        text-align: center;
        position: relative;
        transition: all 0.3s;
    }

    .upload-box:hover {
        background: rgba(0,191,128,0.05);
    }

    #file-input {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        cursor: pointer;
    }

    .upload-icon {
        font-size: 40px;
        margin-bottom: 12px;
        color: #00bf80;
    }

    /* 进度条样式 */
    .progress-bar {
        width: 100%;
        height: 8px;
        background: #eee;
        border-radius: 4px;
        margin-top: 16px;
        overflow: hidden;
        display: none;
    }

    .progress {
        width: 0%;
        height: 100%;
        background: #00bf80;
        transition: width 0.3s ease;
    }

    /* 链接生成区域 */
    .link-section {
        margin-top: 4px;
    }

    .link-box {
        display: flex;
        gap: 8px;
        margin-top: 16px;
    }

    .link-input {
        flex: 1;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
    }

    .copy-btn {
        padding: 12px 20px;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .copy-btn:hover {
        background: #1472cc;
    }

    /* 关闭按钮 */
    .close-btn {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 32px;
        height: 32px;
        cursor: pointer;
        opacity: 0.7;
        transition: all 0.2s;
    }

    .close-btn:hover {
        opacity: 1;
        transform: rotate(90deg);
    }

    /* 提示信息 */
    .hint-text {
        color: #666;
        font-size: 13px;
        margin-top: 8px;
        text-align: center;
    }

    .success-msg {
        color: #00bf80;
        display: none;
        margin-top: 12px;
    }
    #specialNotice {
    display: none; /* 默认隐藏声明 */
}
</style>
</head>
<body>
<include file="header"/>
    <div class="page0101 mb10">
        <div class="weap">
            <div class="boxs">
                <div class="bar01">
                    <div class="title">
                        <h3>数据中心</h3>
                    </div>
                </div>
                <div class="bd">
                    <ul class="ul01 clearfix">
                        <li style="padding-left: 8px;width: 100%;">
                            <span>· 任务一：推荐1个服务站：{:$refStationCount ? '已完成 <span style="padding-left:12px;color:#FF6B35;font-size: 12px;text-decoration: underline;"  onclick="workwenhao()">推荐更多的好处?</span>' : '未完成' }</span>
                        </li>
                        <li style="margin-top: 12px;padding-left: 8px;width: 100%;">
                            <span>· 任务二：每年要求 36 份简历，已完成 {:$alljobCount ? :0} 份</span>
                        </li>
                    </ul>
                    <ul class="ul02 clearfix">
                        <li>
                            <div class="p1">¥<span>0</span></div>
                            <div class="p2">今日收入</div>
                        </li>
                        <li>
                            <div class="p1"><span>{:$dayServiceNum ? : 0}</span></div>
                            <div class="p2">今日扫码用户</div>
                        </li>
                        <li>
                            <div class="p1"><span>{:$jobCount ? : 0}</span></div>
                            <div class="p2">今日简历</div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>


    <div class="page0104 mb10" id="specialNotice">
        <div class="weap">
            <div class="boxs">

                <div class="bar01">
                    <div class="title" style="text-align: center;color: red;"">
                        <h3>特别声明</h3>
                    </div>
                </div>
                <div class="bd">
                    <ul class="clearfix" style="font-size: 14px;">
                        <li style="padding-left: 8px;width: 100%;line-height: 25px;color: #000;">
                            <A href="{:U('index/law', ['id' => '13'])}">
                            平台及服务站均需严格遵守国家法律法规开展经营活动。任何服务站不得利用平行合作关系开展诸如非法集资、传销、诈骗等违法违规行为，一经发现，平台将立即终止合作，并依法追究其法律责任 >></A>
                        </li>
                            <li style="width: 100%;text-align: center;margin-top: 6px;">
                            <A href="{:U('index/law', ['id' => '5'])}" style="padding-left: 5px;text-decoration: underline;font-size: 12px;">服务协议</A>
                            <A href="{:U('index/law', ['id' => '6'])}" style="text-decoration: underline;font-size: 12px;">用户协议</A>
                            <A href="{:U('index/law', ['id' => '4'])}" style="text-decoration: underline;font-size: 12px;">隐私政策</A>
                            <A href="{:U('index/law', ['id' => '7'])}" style="text-decoration: underline;font-size: 12px;">隐私保护指引</A>
                        </li>
                    </ul>
                </div>

            </div>


        </div>

    </div>

    <div class="page0103 mb10">
        <div class="weap">
            <div class="boxs">
                <div class="bar01">
                    <div class="title">
                        <h3>常用功能</h3>
                    </div>
                </div>
                <div class="bd">
                    <ul class="clearfix">
                        <li>
                            <a href="{:U('index/moments')}">
                                <div class="ico"><img src="/static/stations/images/pyq.png" alt=""></div>
                                <h3>图文素材</h3>
                            </a>
                        </li-->
                        <li>
                            <a href="javascript:void(0);" onclick="workwenhao()">
                                <div class="ico"><img src="/static/stations/images/ico-b02.png" alt=""></div>
                                <h3>级别说明</h3>
                            </a>
                        </li>
                    <php>if ($userRow['is_first_wechat'] == 1){</php>
                        <li>
                            <a href="{:U('index/loginaccount')}">
                                <div class="ico"><img src="/static/stations/images/ico-b01.png" alt=""></div>
                                <h3>登录管理</h3>
                            </a>
                        </li>
                    <php>}</php>
                    <li>
                        <a href="{:U('training/index')}">
                            <div class="ico"><img src="/static/stations/images/ico-b03.png" alt=""></div>
                            <h3>培训管理</h3>
                        </a>
                    </li>
                    <li>
                        <a href="https://work.weixin.qq.com/kfid/kfc0f3ecae7d5279996">
                            <div class="ico"><img src="/static/stations/images/ico-b06.png" alt=""></div>
                            <h3>官方客服</h3>
                        </a>
                    </li>
                    <li>
                        <a href="{:U('index/allindex')}">
                            <div class="ico"><img src="/static/stations/images/ic_all.png" alt=""></div>
                            <h3>显示详情</h3>
                        </a>
                    </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="page0107box mb10">
        <div class="page0107">
            <div class="weap">
                <div class="boxs mb10" style="width: 100%;">
                    <div class="hd01">
                        <div class="search" style="background-color: #f8d7da; border-radius: 4px; text-align: center;">
                            <span style="color: #721c24; font-size: 14px; line-height: 40px; display: block;">注意：岗位信息及培训费根据用工单位需求会随时调整。</span>
                        </div>
                    </div>
                    <div class="hd01">
                        <div class="search">
                          <input class="input js_kwd" name="" type="kwd" placeholder="搜索岗位关键词" style="width: 100%;">
                          <button class="inbtn"><i class="iconfont icon-31sousuo"></i></button>
                        </div>
                    </div>
                    <div class="hd02">
                        <ul >
                            <li class="on" data-type="0">
                                <a>全部学历</a>
                            </li>
                            <li data-type="1">
 				                <a>中专/高中</a>
                            </li>
                            <li data-type="2">
                                <a>大专</a>
                            </li>
                            <li data-type="3">
                                <a>本科</a>
                            </li>
                            <li data-type="4">
                                <a>更高学历</a>
                            </li>

                        </ul>
                    </div>
                    <div class="hd03">
                        <ul >
                            <li class="on" data-type="0">
                                <a>全部</a>
                            </li>
                            <li  data-type="1">
                                <a>央企</a>
                            </li>

                            <li  data-type="2">
                                <a>国企</a>
                            </li>
                            <li  data-type="4">
                                <a>上市</a>
                            </li>
                            <li  data-type="6">
                                <a>出国</a>
                            </li>
                            <li data-type="7">
                                <a>其他</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="bd">
                    <ul id="boxcontent">
                        <include file="list-index"/>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-overlay" id="modal">
        <div class="modal-content">
            <!-- 关闭按钮 -->
            <svg class="close-btn" viewBox="0 0 24 24" fill="none" stroke="#666" onclick="closeModal()">
                <path d="M18 6L6 18M6 6l12 12" stroke-width="2" stroke-linecap="round"/>
            </svg>
           <!-- 操作方式选择 -->
           <div class="method-section" style="margin-top: 22px;">
            <h2 class="method-title" id="t1">选择简历添加方式</h2>
            <h2 class="method-title" style="display: none;color: red;font-weight: bold;" id="t2">简历诉求</h2>
            <div class="method-options">
                    <!-- 上传区域 -->
                    <div class="upload-box">

                        <div id="stext">
                        <div class="upload-icon">📤</div>
                        <p>【您自己操作】点这里上传简历文件</p>
                        <p class="hint-text">支持格式：Word文档/PDF</p>
                        <input type="file" id="file-input"
                               accept=".doc,.docx,.pdf">
                        </div>

                        <div style="width: 100%;display: none;" ID="j_content">
                            <!-- 分割线 -->
                               <textarea
                               id = "job_content"
                               class="link-input"
                               style="width: 100%;height: 88px;"
                               placeholder="在这里输入该简历诉求，比如想去哪里，想找什么工作"></textarea>
                               <!-- 提交按钮绑定事件 -->
                               <button class="copy-btn" onclick="submitResume()" style="text-align: center;margin-top: 18px;">
                                   提 交
                               </button>
                               <BR>
                        </div>

                        <!-- 上传进度 -->
                        <p class="success-msg" id="upload-success">✓ 正在保存简历……</p>
                        <div class="progress-bar">
                            <div class="progress" id="progress"></div>
                        </div>


                    </div>

                    <div id="stext2">
                    <!-- 分割线 -->
                    <div style="text-align: center; color: #999; margin: 10px 0">或 生成简历创建链接复制发给求职者</div>

                    <!-- 生成链接区域 -->
                    <div class="link-section">
                        <button class="copy-btn" onclick="generateLink()">
                            点这生成简历创建链接
                        </button>
                        <div class="link-box">
                            <input type="text"
                                   class="link-input"
                                   id="link-text"
                                   readonly
                                   placeholder="点上方生成链接发给求职者">
                            <button class="copy-btn" onclick="copyLink()">
                                复制
                            </button>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </div>

<!-- 弹窗层 -->
<div class="modal">
    <img class="modal-img">
</div>

<div class="footernav">
<div class="box">
    <ul>
        <li>
            <a href="http://www.zhongcaiguoke.com">
                <div class="ico"><span class="iconfont icon-shouye"></span></div>
                <h3>官网</h3>
            </a>
        </li>
        <!--li>
            <a href="#">
                <div class="ico"><span class="iconfont icon-dingdan"><i></i></span></div>
                <h3>订单</h3>
            </a>
        </li-->
        <li>
            <a href="javascript:void(0);"  onclick="showModal()">
                <button class="add-btn" onclick="showModal()">+ 添加简历</button>
            </a>
        </li>
        <!--li>
            <a href="#">
                <div class="ico"><span class="iconfont icon-xiaoxi"><i class="iconfont icon-caidan"></i></span></div>
                <h3>消息</h3>
            </a>
        </li-->
        <li class="on">
            <a href="#">
                <div class="ico"><span class="iconfont icon-gerenzhongxin"><i></i></span></div>
                <h3>管理中心</h3>
            </a>
        </li>
    </ul>
</div>
</div>
<div id="copy" style="display: none;"></div>

<script>
    // 显示模态框
    function showModal() {
        document.getElementById('modal').style.display = 'flex';
        document.body.style.overflow = 'hidden'; // 禁止背景滚动
    }

    // 关闭弹窗时重置状态
    function closeModal() {
        document.getElementById('modal').style.display = 'none';
        document.body.style.overflow = 'auto';
        resetUploadStatus();
        // 新增重置操作
        selectedFile = null;
        document.getElementById('job_content').value = '';
    }

    // 点击遮罩关闭
    document.getElementById('modal').addEventListener('click', function(e) {
        if(e.target === this) closeModal();
    });

    // 生成随机链接
    function generateLink() {
        $.ajax({
            type: 'post',
            data: {},
            url: '/index/getsorturl',
            beforeSend: function(){
                //loading层
                layer.open({
                    type: 2,
                    shadeClose : false,
                });
            },
            complete: function(){

                // Handle the complete event
            },
            success: function(data) {
                layer.closeAll();
                ajaxsend = true;
                if (data.status == 1) {
                    $('#link-text').val(data.info);
                }  else {
                    layer.open({
                        style : "width:80%",
                        shadeClose : false,
                        content: "<div style='text-align: left'>"+data.info+"</div>",
                        btn: ['我知道了'],
                        yes: function (index) {
                            layer.closeAll();
                        },
                    });
                }
            }
        });
    }

    // 复制链接功能
    function copyLink() {
        const link = document.getElementById('link-text').value;
        if(!link) return;
        copyToClipboardLegacyTwo(link)

        // navigator.clipboard.writeText(link).then(() => {
        //     showTempMessage('链接已复制到剪贴板');
        // });
    }

 // 新增全局变量用于存储文件对象
let selectedFile = null;

// 修改文件选择事件（仅保存文件不立即上传）
document.getElementById('file-input').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if(!file) return;

    // 验证文件格式
    const fileName = file.name.toLowerCase();
    const validExtensions = ['.doc', '.docx', '.pdf'];
    const isValidFile = validExtensions.some(ext => fileName.endsWith(ext));

    if (!isValidFile) {
        alert('仅支持上传word文档或PDF格式简历');
        this.value = ''; // 清空选择
        return;
    }

    // 保存文件对象
    selectedFile = file;

    // 立即显示输入区域
    const jcontentstr = document.getElementById('j_content');
    const stextstr = document.getElementById('stext');
    const stextstr2 = document.getElementById('stext2');
    jcontentstr.style.display = 'block';
    stextstr.style.display = 'none';
    stextstr2.style.display = 'none';
    document.getElementById('t1').style.display = 'none';
    document.getElementById('t2').style.display = 'block';
});

// 新增提交函数
function submitResume() {
    if (!selectedFile) {
        alert('请先选择简历文件');
        return;
    }

    const jobContent = document.getElementById('job_content').value.trim();
    if (!jobContent) {
        alert('请输入简历求职诉求');
        return;
    }

    const progressBar = document.querySelector('.progress-bar');
    const progress = document.getElementById('progress');
    const successMsg = document.getElementById('upload-success');
    const formData = new FormData();

    // 合并文件和其他参数
    formData.append('file', selectedFile);
    formData.append('jobcontent', jobContent);

    progressBar.style.display = 'block';
    successMsg.style.display = 'none';

    const xhr = new XMLHttpRequest();

    xhr.upload.onprogress = function(e) {
        if (e.lengthComputable) {
            const percent = (e.loaded / e.total) * 100;
            progress.style.width = percent + '%';
        }
    };

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            progressBar.style.display = 'none';
            if (xhr.status === 200) {
                const res = JSON.parse(xhr.responseText);
                if(res.status === 1) {
                    successMsg.style.display = 'block';
                    showTempMessage(res.msg);
                    // 3秒后自动关闭弹窗
                    setTimeout(() => {
                        closeModal();
                        window.location.href = res.url || '{:U("index/joblist")}'; // TP3.2路由
                    }, 1000);
                } else {
                    alert(res.msg);
                    resetUploadStatus();
                }
            } else {
                if (xhr.status == 413) {
                    alert('上传失败，简历文件过大异常！');
                }else{
                    alert('上传失败，HTTP状态码：' + xhr.status);
                }
                resetUploadStatus();
            }
        }
    };

    xhr.open('POST', '{:U("index/uploadJobFile")}', true);
    xhr.send(formData);
}
    //
    // 显示临时提示信息
    function showTempMessage(msg) {
        const tempDiv = document.createElement('div');
        tempDiv.className = 'hint-text';
        tempDiv.textContent = msg;
        tempDiv.style.color = '#00bf80';
        tempDiv.style.marginTop = '12px';

        document.querySelector('.method-options').appendChild(tempDiv);
        setTimeout(() => tempDiv.remove(), 2000);
    }

    // 重置上传状态
    function resetUploadStatus(delay = 0) {
        setTimeout(() => {
            document.getElementById('file-input').value = '';
            document.getElementById('progress').style.width = '0%';
            document.getElementById('upload-success').style.display = 'none';
            document.getElementById('j_content').style.display = 'none';
            document.getElementById('stext').style.display = 'block';
            document.getElementById('stext2').style.display = 'block';
            document.getElementById('t2').style.display = 'none';
            document.getElementById('t1').style.display = 'block';
        }, delay);
    }
</script>
<script>
function removeFirstLineSpaces(text) {
    if (typeof text !== 'string') return text;

    // 核心处理逻辑（新增第五步处理中文括号）
    // 1. 清除首行前导空格
    let processed = text.replace(/^ +/gm, '');

    // 2. 处理开头换行
    const startsWithNewline = /^[\r\n]/.test(text);
    if (startsWithNewline) {
        processed = processed.replace(/^[\r\n]*/, match =>
            match.includes('\r\n') ? '\r\n' : '\n'
        );
    }

    // 3. 处理末尾换行
    processed = processed.replace(/([\r\n])+$/, '$1');

    // 4. 合并连续空行
    processed = processed.replace(/(\r\n|\n){2,}/g, '\n');

    // 5. 新增中文括号换行处理（核心新增逻辑）
    processed = processed.replace(/【/g, '\n\n【')  // 先插入两个换行
        .replace(/(\r\n|\n){3,}/g, '\n\n')        // 处理可能产生的三个以上换行
        .replace(/([\r\n])+$/, '$1');              // 再次确保末尾换行

    return processed;
}

    function copyToClipboardLegacy(text) {
        var texts = removeFirstLineSpaces(text)
        const tempTextArea = document.createElement("textarea");
        tempTextArea.value = texts; // textarea 会保留换行符
        tempTextArea.style.position = "fixed";   // 隐藏元素避免页面闪烁
        tempTextArea.style.left = "-9999px";     // 移出可视区域
        document.body.appendChild(tempTextArea);

        // 2. 选中内容
        tempTextArea.select();
        tempTextArea.setSelectionRange(0, 99999); // 兼容移动端


        // 3. 执行复制命令
        try {
            const success = document.execCommand("copy");
            if (success) {
                layer.open({
                    content: '复制成功！'
                });
            } else {
                layer.open({
                    content: '复制失败！'
                });
            }
        } catch (err) {
            layer.open({
                content: '复制失败！'
            });
        } finally {
            // 4. 清理临时元素
            document.body.removeChild(tempTextArea);
        }
    }

    function copyToClipboardLegacyTwo(text) {
        const tempTextArea = document.createElement("textarea");
        tempTextArea.value = text; // textarea 会保留换行符
        tempTextArea.style.position = "fixed";   // 隐藏元素避免页面闪烁
        tempTextArea.style.left = "-9999px";     // 移出可视区域
        document.body.appendChild(tempTextArea);

        // 2. 选中内容
        tempTextArea.select();
        tempTextArea.setSelectionRange(0, 99999); // 兼容移动端


        // 3. 执行复制命令
        try {
            const success = document.execCommand("copy");
            if (success) {
                layer.open({
                    content: '复制成功！'
                });
            } else {
                layer.open({
                    content: '复制失败！'
                });
            }
        } catch (err) {
            layer.open({
                content: '复制失败！'
            });
        } finally {
            // 4. 清理临时元素
            document.body.removeChild(tempTextArea);
        }
    }

    $("#boxcontent").on('click', '.js_copy', function () {
        let copyid = $(this).attr('data-id');
        let copyhtml =$("#comtepy_"+copyid).html()+"‼️"+$("#texttitle_"+copyid).html()+$("#textContent_"+copyid).html();
        copyToClipboardLegacy(htmlToTextWithLineBreaks(copyhtml.replace(/<\/p>/gi, '<br>').replace(/<p>/gi, '')))
    })

    /**
     * 将 HTML 内容转换为纯文本，处理 <br> 为换行符
     * @param {string} html - 原始 HTML 字符串
     * @returns {string} 转换后的纯文本
     */
    function htmlToTextWithLineBreaks(html) {
        // 创建临时容器
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = html;
        // 处理 <br> 标签为换行符
        const brElements = tempDiv.getElementsByTagName("br");
        while (brElements.length) {
            const br = brElements[0];
            br.parentNode.replaceChild(document.createTextNode("\n"), br);
        }

        // 提取纯文本（自动去除其他标签）
        return tempDiv.textContent || tempDiv.innerText;
    }

    function toggleText(t) {
        const textElement =  document.getElementById('textContent_'+t);
        const arrow = document.querySelector('.arrow');

        textElement.classList.toggle('collapsed');
        textElement.classList.toggle('expanded');

        // 自动滚动到展开位置
        if (textElement.classList.contains('expanded')) {
            textElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
    // 自动隐藏不需要展开的箭头
    window.addEventListener('DOMContentLoaded', (t) => {
        let textElement = document.getElementById('textContent_'+t);
        let toggleBtn = document.querySelector('.toggle-btn');
        if (textElement != null && textElement.scrollHeight <= textElement.clientHeight) {
            toggleBtn.style.display = 'none';
        }
    });

    var page=1,pages='{:(int)$page->Total_Pages}';
    var type = 0;
    var qualification = 0;
    var kwd = '';
    function ajaxcommon() {
        $.get('/index/index?n=1&p=' + page +'&type='+type+"&qualification="+qualification+"&kwd="+kwd, function(str){
            p = 1;
            $('#boxcontent').html(str);
        }, 'html');
    }
    $(document).ready(function(){


    var noticeSwiper = new Swiper('.noticeSwiper', {
        direction: 'vertical',
		loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    })
    var noticeSwiper = new Swiper('.page0106Swiper', {
        pagination: {
          el: ".swiper-pagination",
        },
		loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    });

    $('.js_kwd').blur(function () {
        var  new_kwd = $(this).val();
        if (new_kwd != kwd) {
            $(this).siblings().removeClass("on");
            $(this).addClass("on");
            kwd = new_kwd
            ajaxcommon();
        }
    })


	$(".page0107 .hd02 li").click(function(){
        $(this).siblings().removeClass("on");
       $(this).addClass("on");
   });

   $(".page0107 .hd03 li").click(function(){
       var newType = $(this).attr('data-type');
       if (newType != type) {
           $(this).siblings().removeClass("on");
           $(this).addClass("on");
           type = newType
           ajaxcommon();
       }
   });

    $(".page0107 .hd02 li").click(function(){
        var newQualification = $(this).attr('data-type');
        console.log(newQualification)
        if (newQualification != qualification) {
            $(this).siblings().removeClass("on");
            $(this).addClass("on");
            qualification = newQualification
            ajaxcommon();
        }
    });

        // 获取元素
        const thumbnail = document.querySelector('.thumbnail');
        const modal = document.querySelector('.modal');
        const modalImg = document.querySelector('.modal-img');
        $("body").on('click', '.thumbnail', function () {
            modal.style.display = 'flex';
            modalImg.src = $(this).attr('src');
        })

        // 点击任意位置关闭弹窗
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });


});
function loadmore(){
    console.log(pages)
    if(page<pages){
        page+=1;
        $.get('/index/index?p=' + page +'&type='+type+"&qualification="+qualification+"&kwd="+kwd, function(str){
            console.log(str)
            $('#boxcontent').append(str);
        }, 'html');
    } else if (page==pages) {
        page+=1;
        setTimeout("$('.container').append('<p class=\"nomore\">------ 没有了 ------</p>')", 500);
    }
}



$(window).scroll(function(){
    var scrollTop = $(this).scrollTop();     //滚动条距离顶部的高度
    var scrollHeight = $(document).height(); //当前页面的总高度
    var clientHeight = $(this).height();     //当前可视的页面高度
    // console.log("top:"+scrollTop+",doc:"+scrollHeight+",client:"+clientHeight);
    if(scrollTop + clientHeight >= scrollHeight){
        loadmore();
    }else if(scrollTop<=0){
    }
});


document.addEventListener('DOMContentLoaded', function() {
    // 获取声明元素
    const noticeElement = document.getElementById('specialNotice');

    // 获取当前日期字符串（格式示例："Mon Jul 24 2023"）
    const today = new Date().toDateString();

    // 从本地存储获取最后显示日期
    const lastShownDate = localStorage.getItem('specialNoticeDate');

    // 判断是否是今天第一次访问
    if (!lastShownDate || lastShownDate !== today) {
        // 显示声明
        noticeElement.style.display = 'block';
        // 更新存储日期
        localStorage.setItem('specialNoticeDate', today);
    } else {
        // 隐藏声明
        noticeElement.style.display = 'none';
    }
});

 // 在原有代码中添加
document.querySelector('.close-btn').addEventListener('click', function() {
    document.getElementById('specialNotice').style.display = 'none';
});
</script>

</body>
</html>
