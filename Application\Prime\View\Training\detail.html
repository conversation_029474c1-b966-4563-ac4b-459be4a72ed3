<include file="block/hat" />
<div class="container-fluid">
  <div class="row">
    <include file="block/menu" />
    <div class="col-xs-12 col-sm-9 col-lg-10">
      <style type='text/css'>
        /* 现代化培训订单详情页面样式 */
        .training-detail-wrapper {
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
          min-height: 100vh;
          padding: 0;
          margin: 0;
        }

        .training-detail-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 2rem;
          background: transparent;
        }

        /* 现代化页面标题 */
        .training-detail-header {
          background: white;
          border-radius: 1rem;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          border: 1px solid #e2e8f0;
          margin-bottom: 2rem;
          overflow: hidden;
          position: relative;
        }

        .training-detail-header::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }

        .training-detail-header-content {
          padding: 2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 1rem;
        }

        .training-detail-title {
          display: flex;
          align-items: center;
          gap: 1rem;
          margin: 0;
        }

        .training-detail-title-icon {
          width: 3rem;
          height: 3rem;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 0.75rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 1.5rem;
        }

        .training-detail-title-text {
          display: flex;
          flex-direction: column;
        }

        .training-detail-title-main {
          font-size: 2rem;
          font-weight: 700;
          color: #1a202c;
          margin: 0;
          line-height: 1.2;
        }

        .training-detail-title-sub {
          font-size: 1.5rem;
          color: #718096;
          margin: 0;
          font-weight: 400;
        }

        /* 订单类型标签样式 */
        .training-detail-order-type-tag {
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          border-radius: 1.5rem;
          font-size: 1rem;
          font-weight: 600;
          margin-top: 0.75rem;
          border: 2px solid;
          transition: all 0.3s ease;
        }

        .training-detail-order-type-tag.type-station {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          color: white;
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .training-detail-order-type-tag.type-zsb {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          color: white;
          border-color: #10b981;
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .training-detail-order-type-tag:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        .training-detail-actions {
          display: flex;
          align-items: center;
          gap: 1rem;
          flex-wrap: wrap;
        }

        .training-detail-action-btn {
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          border-radius: 0.75rem;
          font-size: 1.5rem;
          font-weight: 600;
          text-decoration: none;
          border: none;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .training-detail-action-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.2);
          text-decoration: none;
        }

        .training-detail-action-btn.btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }

        .training-detail-action-btn.btn-primary:hover {
          color: white;
        }

        .training-detail-action-btn.btn-success {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          color: white;
        }

        .training-detail-action-btn.btn-success:hover {
          color: white;
        }

        .training-detail-action-btn.btn-info {
          background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
          color: white;
        }

        .training-detail-action-btn.btn-info:hover {
          color: white;
        }

        .training-detail-action-btn.btn-danger {
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
          color: white;
        }

        .training-detail-action-btn.btn-danger:hover {
          color: white;
        }

        /* 现代化信息卡片 */
        .training-detail-card {
          background: white;
          border-radius: 1rem;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          border: 1px solid #e2e8f0;
          margin-bottom: 2rem;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .training-detail-card:hover {
          box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .training-detail-card-header {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          padding: 1.5rem 2rem;
          border-bottom: 1px solid #e2e8f0;
          display: flex;
          align-items: center;
          gap: 0.75rem;
        }

        .training-detail-card-icon {
          width: 2.5rem;
          height: 2.5rem;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 1.25rem;
        }

        .training-detail-card-title {
          font-size: 1.75rem;
          font-weight: 600;
          color: #1a202c;
          margin: 0;
        }

        .training-detail-card-body {
          padding: 2rem;
        }

        .training-detail-info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
        }

        .training-detail-info-item {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
          margin-bottom: 1.5rem;
          padding: 1rem;
          background: #f8fafc;
          border-radius: 0.5rem;
        }

        .training-detail-info-item:last-child {
          margin-bottom: 0;
        }

        .training-detail-info-label {
          color: #6b7280;
          font-weight: 600;
          font-size: 1.5rem;
          min-width: 120px;
          flex-shrink: 0;
        }

        .training-detail-info-value {
          color: #374151;
          font-weight: 500;
          font-size: 1.5rem;
          flex: 1;
          word-break: break-all;
        }

        /* 状态操作按钮样式 */
        .status-action-btn.btn-default:disabled {
          background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
          border: none !important;
          color: #fff !important;
          cursor: not-allowed !important;
          opacity: 0.7;
          transform: none !important;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }

        .status-action-btn.btn-default:disabled:hover {
          background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
          transform: none !important;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }

        /* 状态操作按钮激活状态增强 */
        .training-detail-action-btn.btn-success {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          color: white;
          position: relative;
          overflow: hidden;
        }

        .training-detail-action-btn.btn-success::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
          transition: left 0.5s;
        }

        .training-detail-action-btn.btn-success:hover::before {
          left: 100%;
        }

        .training-detail-action-btn.btn-info {
          background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
          color: white;
          position: relative;
          overflow: hidden;
        }

        .training-detail-action-btn.btn-info::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
          transition: left 0.5s;
        }

        .training-detail-action-btn.btn-info:hover::before {
          left: 100%;
        }

        /* 现代化状态操作弹窗样式 */
        .status-modal-container {
          padding: 25px;
          background: white;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .status-modal-fee-info {
          background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
          border-radius: 12px;
          padding: 20px;
          margin-bottom: 25px;
          border: 1px solid #90caf9;
          box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
        }

        .fee-details-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 15px;
          margin-top: 15px;
        }

        .fee-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 12px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 8px;
          border: 1px solid rgba(33, 150, 243, 0.2);
        }

        .fee-label {
          font-size: 12px;
          color: #666;
          margin-bottom: 5px;
          font-weight: 500;
        }

        .fee-value {
          font-size: 16px;
          font-weight: 600;
          color: #1976D2;
        }

        .fee-paid {
          color: #4CAF50;
        }

        .fee-remaining {
          color: #FF9800;
        }

        .status-form {
          background: white;
        }

        .form-group-modern {
          margin-bottom: 20px;
        }

        .form-label-modern {
          display: flex;
          align-items: center;
          font-weight: 600;
          color: #374151;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .form-control-modern {
          width: 100%;
          padding: 12px 16px;
          border: 2px solid #e5e7eb;
          border-radius: 8px;
          font-size: 14px;
          transition: all 0.3s ease;
          background: #f9fafb;
        }

        .form-control-modern:focus {
          outline: none;
          border-color: #667eea;
          background: white;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control-modern:hover {
          border-color: #d1d5db;
          background: white;
        }

        .form-help-text {
          font-size: 12px;
          color: #6b7280;
          margin-top: 5px;
          display: block;
        }

        .form-error-text {
          font-size: 12px;
          color: #ef4444;
          margin-top: 5px;
          display: block;
        }

        /* Layer弹窗自定义皮肤 */
        .modern-layer-skin .layui-layer-title {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          color: white !important;
          border-radius: 12px 12px 0 0 !important;
          font-weight: 500 !important;
          padding: 15px 20px !important;
          border: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
        }

        .modern-layer-skin .layui-layer-content {
          padding: 0 !important;
          background: #f8f9fa !important;
          border-radius: 0 0 12px 12px !important;
        }

        .modern-layer-skin .layui-layer-btn {
          text-align: center !important;
          padding: 20px !important;
          background: #f8f9fa !important;
          border-radius: 0 0 12px 12px !important;
          min-width: 400px !important;
          width: auto !important;
          overflow: visible !important;
        }

        .modern-layer-skin .layui-layer-btn a {
          border-radius: 8px !important;
          font-weight: 500 !important;
          padding: 15px 50px !important;
          margin: 0 20px !important;
          transition: all 0.3s ease !important;
          min-width: 140px !important;
          max-width: none !important;
          width: auto !important;
          height: auto !important;
          text-align: center !important;
          white-space: nowrap !important;
          overflow: visible !important;
          display: inline-block !important;
          box-sizing: border-box !important;
          font-size: 14px !important;
          line-height: 1.4 !important;
          text-overflow: clip !important;
          word-wrap: normal !important;
        }

        .modern-layer-skin .layui-layer-btn0 {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          border: none !important;
          color: white !important;
        }

        .modern-layer-skin .layui-layer-btn0:hover {
          background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%) !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
        }

        .modern-layer-skin .layui-layer-btn1 {
          background: #f8f9fa !important;
          border: 1px solid #dee2e6 !important;
          color: #6c757d !important;
        }

        .modern-layer-skin .layui-layer-btn1:hover {
          background: #e9ecef !important;
          border-color: #adb5bd !important;
          transform: translateY(-1px) !important;
        }



        /* 响应式设计 */
        @media (max-width: 1024px) {
          .training-detail-container {
            padding: 1.5rem;
          }

          .training-detail-header-content {
            flex-direction: column;
            text-align: center;
          }

          .training-detail-order-type-tag {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
            margin-top: 0.5rem;
          }

          .training-detail-info-grid {
            grid-template-columns: 1fr;
          }
        }

        @media (max-width: 768px) {
          .training-detail-container {
            padding: 1rem;
          }

          .training-detail-title-main {
            font-size: 1.75rem;
          }

          .training-detail-title-sub {
            font-size: 1.25rem;
          }

          .training-detail-actions {
            flex-direction: column;
            width: 100%;
          }

          .training-detail-action-btn {
            width: 100%;
            justify-content: center;
          }

          .training-detail-info-item {
            flex-direction: column;
            gap: 0.5rem;
          }

          .training-detail-info-label {
            min-width: auto;
          }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .training-detail-fade-in {
          animation: fadeInUp 0.6s ease-out;
        }

        .training-detail-fade-in-delay-1 {
          animation: fadeInUp 0.6s ease-out 0.1s both;
        }

        .training-detail-fade-in-delay-2 {
          animation: fadeInUp 0.6s ease-out 0.2s both;
        }

        .training-detail-fade-in-delay-3 {
          animation: fadeInUp 0.6s ease-out 0.3s both;
        }

        /* 状态历史弹窗自定义样式 */
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f7fafc;
          border-radius: 4px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(135deg, #cbd5e0 0%, #a0aec0 100%);
          border-radius: 4px;
          transition: all 0.3s ease;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        }

        /* 现代化徽章样式 */
        .modern-badge {
          display: inline-block;
          transition: all 0.3s ease;
        }

        .modern-badge:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 历史记录项悬停效果 */
        .history-item:hover {
          transform: translateX(5px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        }

        /* 状态历史弹窗内容动画 */
        @keyframes slideInRight {
          from {
            opacity: 0;
            transform: translateX(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        .status-section {
          animation: slideInRight 0.4s ease-out;
        }

        .status-section:nth-child(2) {
          animation-delay: 0.1s;
        }

        .status-section:nth-child(3) {
          animation-delay: 0.2s;
        }

        .status-section:nth-child(4) {
          animation-delay: 0.3s;
        }

        /* Layer弹窗自定义皮肤 */
        .modern-layer-skin .layui-layer-title {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          color: white !important;
          border-radius: 12px 12px 0 0 !important;
          font-weight: 500 !important;
          padding: 15px 20px !important;
          border: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
        }

        .modern-layer-skin .layui-layer-content {
          padding: 0 !important;
          background: #f8f9fa !important;
          border-radius: 0 0 12px 12px !important;
        }

        .modern-layer-skin {
          border-radius: 12px !important;
          box-shadow: 0 10px 40px rgba(0,0,0,0.15) !important;
          border: none !important;
        }

        /* 隐藏number输入框的spinner控件 */
        input[type="number"]::-webkit-outer-spin-button,
        input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        /* Firefox */
        input[type="number"] {
            -moz-appearance: textfield;
        }

        /* 沟通记录样式 */
        .training-communication-header {
            transition: all 0.2s ease;
        }

        .training-communication-header:hover {
            background: rgba(59, 130, 246, 0.05);
        }

        .training-communication-header:hover .training-communication-toggle {
            background: rgba(59, 130, 246, 0.1);
        }

        .training-communication-toggle {
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            height: 40px;
        }

        .training-communication-toggle:hover {
            background: rgba(59, 130, 246, 0.1);
        }

        /* 查看简历按钮特殊样式 */
        .training-resume-btn {
            position: relative;
            overflow: hidden;
        }

        .training-resume-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .training-resume-btn:hover::after {
            left: 100%;
        }

        .training-resume-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .training-communication-body {
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .training-communication-body.show {
            display: block !important;
        }

        .training-communication-list {
            max-height: 400px;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 5px;
            text-align: left;
            direction: ltr;
        }

        .training-message-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            padding-right: 18px;
            margin-bottom: 12px;
            margin-right: 5px;
            position: relative;
            text-align: left;
            direction: ltr;
            transition: all 0.2s ease;
        }

        .training-message-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        /* 消息删除按钮 */
        .training-message-delete-btn {
            position: absolute;
            top: 4px;
            right: -3px;
            width: 18px;
            height: 18px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 11px;
            cursor: pointer;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .training-message-delete-btn:hover {
            background: #dc2626;
            transform: scale(1.1);
        }

        .training-message-item:hover .training-message-delete-btn {
            display: flex;
        }

        .training-message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            text-align: left;
        }

        .training-message-type {
            padding: 0.375rem 0.75rem;
            border-radius: 1rem;
            font-size: 1rem;
            font-weight: bold;
        }

        .training-message-type-platform {
            background: #e74c3c;
            color: white;
        }

        .training-message-type-station {
            background: #27ae60;
            color: white;
        }

        .training-message-time {
            font-size: 1.5rem;
            color: #6c757d;
            font-weight: 500;
        }

        .training-message-content {
            font-size: 1.5rem;
            line-height: 1.5;
            color: #495057;
            max-height: 80px;
            overflow: hidden;
            position: relative;
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;
            max-width: 100%;
            text-align: left;
        }

        .training-message-content::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 30px;
            height: 20px;
            background: linear-gradient(to right, transparent, #f8f9fa);
        }

        .training-message-expand-btn {
            background: none;
            border: none;
            color: #007bff;
            font-size: 14px;
            cursor: pointer;
            padding: 2px 5px;
            text-decoration: underline;
        }

        .training-message-expand-btn:hover {
            color: #0056b3;
        }

        .training-message-content.collapsed {
            max-height: 40px;
            overflow: hidden;
            position: relative;
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;
            max-width: 100%;
            text-align: left;
        }

        .training-message-content.collapsed::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 50px;
            height: 20px;
            background: linear-gradient(to right, transparent, #f8f9fa);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .training-communication-list {
                padding-right: 8px;
            }

            .training-message-item {
                padding-right: 20px;
                margin-right: 8px;
            }

            .training-message-delete-btn {
                top: 5px;
                right: -2px;
                width: 20px;
                height: 20px;
                font-size: 14px;
            }

            .training-quick-reply-options {
                flex-direction: column;
                align-items: stretch !important;
                gap: 1rem !important;
            }

            .training-reply-checkboxes {
                justify-content: center;
            }
        }
      </style>

      <div class="training-detail-wrapper">
        <div class="training-detail-container">
          <!-- 现代化页面标题 -->
          <div class="training-detail-header training-detail-fade-in">
            <div class="training-detail-header-content">
              <div class="training-detail-title">
                <div class="training-detail-title-icon">
                  <i class="fa fa-file-text"></i>
                </div>
                <div class="training-detail-title-text">
                  <h1 class="training-detail-title-main">{$user.display_realname|default="未填写姓名"} 培训订单详情</h1>
                  <p class="training-detail-title-sub">Training Order Details</p>
                  <!-- 订单类型标签 -->
                  <php>if(!empty($order['zsb_id']) && $order['zsb_id'] > 0) {</php>
                  <span class="training-detail-order-type-tag type-zsb">
                    <i class="fa fa-building"></i>
                    招就办订单
                  </span>
                  <php>} else {</php>
                  <span class="training-detail-order-type-tag type-station">
                    <i class="fa fa-home"></i>
                    服务站订单
                  </span>
                  <php>}</php>
                </div>
              </div>
              <div class="training-detail-actions">
                <a href="{:U('training/index')}" class="training-detail-action-btn btn-primary">
                  <i class="fa fa-arrow-left"></i>
                  <span>返回列表</span>
                </a>

                <!-- 查看简历按钮 -->
                <php>if(!empty($user['display_realname'])) {</php>
                <a href="{:U('userjob/index')}?kw=name&val={$user.display_realname|urlencode}" class="training-detail-action-btn btn-info training-resume-btn" target="_blank" title="查看学员 {$user.display_realname} 的简历信息">
                  <i class="fa fa-file-text-o"></i>
                  <span>查看简历</span>
                </a>
                <php>}</php>

                <!-- 新的状态操作按钮 -->
                <php>if(session('admin_id')) {</php>
                <button type="button" class="training-detail-action-btn btn-success status-action-btn" id="statusActionBtn" data-order-id="{$order.id}" data-sub-status="{$order.sub_status}" onclick="showStatusModal({$order.id}, '{$order.sub_status}')">
                  <i class="fa fa-cog"></i>
                  <span>状态操作</span>
                </button>
                <button type="button" class="training-detail-action-btn btn-info" onclick="showStatusHistory({$order.id})">
                  <i class="fa fa-history"></i>
                  <span>状态历史</span>
                </button>
                <!-- 取消订单按钮 -->
                <php>if(!in_array($order['sub_status'], ['completed', 'terminated'])) {</php>
                <button type="button" class="training-detail-action-btn btn-danger" onclick="cancelOrder({$order.id}, '{$order.sub_status}')">
                  <i class="fa fa-ban"></i>
                  <span>取消订单</span>
                </button>
                <php>}</php>
                <php>}</php>
              </div>
            </div>
          </div>

          <!-- 沟通记录 -->
          <php>if(!empty($order['user_job_id'])) {</php>
          <div class="training-detail-card training-detail-fade-in-delay-1">
            <div class="training-detail-card-header training-communication-header" onclick="toggleTrainingCommunication()" style="cursor: pointer; user-select: none;">
              <div style="display: flex; align-items: center; flex: 1;">
                <div class="training-detail-card-icon">
                  <i class="fa fa-comments"></i>
                </div>
                <h3 class="training-detail-card-title" style="padding-left: 0.7rem;">
                  沟通记录
                  <php>if($message_count > 0) {</php>
                  <small style="margin-left: 0.5rem; padding: 0.2rem 0.5rem; background: rgba(59, 130, 246, 0.1); color: #1d4ed8; border-radius: 0.5rem; font-size: 1.2rem; font-weight: 500;">共{$message_count}条</small>
                  <php>} else {</php>
                  <small style="margin-left: 0.5rem; padding: 0.2rem 0.5rem; background: rgba(156, 163, 175, 0.1); color: #6b7280; border-radius: 0.5rem; font-size: 1.2rem; font-weight: 500;">暂无记录</small>
                  <php>}</php>
                </h3>
              </div>
              <div style="display: flex; align-items: center; margin-left: auto;">
                <span style="font-size: 1.4rem; color: #9ca3af; margin-right: 0.5rem;">点击展开/隐藏</span>
                <div class="training-communication-toggle" style="padding: 0.5rem; border-radius: 0.25rem; transition: all 0.2s ease;">
                  <i class="fa fa-chevron-down" id="trainingCommunicationIcon" style="font-size: 1.2rem; color: #6b7280; transition: transform 0.3s ease;"></i>
                </div>
              </div>
            </div>
            <div class="training-detail-card-body training-communication-body" id="trainingCommunicationBody" style="display: none; transition: all 0.3s ease;">
              <php>if(!empty($recent_messages_list)) {</php>
              <!-- 显示最近5条沟通记录 -->
              <div class="training-communication-list">
                <php>foreach($recent_messages_list as $msgIndex => $recentMsg) {</php>
                <div class="training-message-item" data-message-id="{$recentMsg.id}">
                  <button class="training-message-delete-btn" onclick="deleteTrainingMessage('{$recentMsg.id}', '{$order.user_job_id}')" title="删除消息">
                    <i class="fa fa-times"></i>
                  </button>
                  <div class="training-message-header">
                    <span class="training-message-type <php>echo $recentMsg['type'] == 2 ? 'training-message-type-platform' : 'training-message-type-station';</php>">
                      <i class="fa fa-<php>echo $recentMsg['type'] == 2 ? 'desktop' : 'building';</php>"></i>
                      <php>echo $recentMsg['type'] == 2 ? '平台' : '服务站';</php>
                    </span>
                    <span class="training-message-time">
                      <i class="fa fa-clock-o"></i>
                      {:date('m-d H:i', $recentMsg['create_time'])}
                    </span>
                  </div>
                  <div class="training-message-content <php>echo strlen($recentMsg['content']) > 100 ? 'collapsed' : '';</php>" id="training-msg-content-{$order.user_job_id}-{$msgIndex}">
                    {:strip_tags(htmlspecialchars_decode($recentMsg['content']))}
                  </div>
                  <php>if(strlen($recentMsg['content']) > 100) {</php>
                  <div style="text-align: right; margin-top: 5px;">
                    <button class="training-message-expand-btn" onclick="toggleTrainingMessageContent('{$order.user_job_id}-{$msgIndex}')">
                      展开全文
                    </button>
                  </div>
                  <php>}</php>
                </div>
                <php>}</php>
              </div>

              <!-- 快捷回复区域 -->
              <div class="training-quick-reply-section" style="margin-top: 1.5rem; padding-top: 1.5rem; border-top: 1px solid #e2e8f0;">
                <h4 style="font-size: 1.5rem; font-weight: 600; color: #374151; margin-bottom: 1rem;">
                  <i class="fa fa-reply" style="margin-right: 0.5rem; color: #3b82f6;"></i>
                  快捷回复
                </h4>
                <form id="trainingQuickReplyForm" onsubmit="return false;">
                  <div class="training-quick-reply-form">
                    <textarea
                      id="trainingQuickReplyContent"
                      placeholder="请输入回复内容..."
                      rows="3"
                      style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1.5rem; resize: vertical; min-height: 80px;"
                    ></textarea>
                    <div class="training-quick-reply-options" style="display: flex; justify-content: space-between; align-items: center; margin-top: 1rem; flex-wrap: wrap; gap: 1rem;">
                      <div class="training-reply-checkboxes" style="display: flex; gap: 1.5rem; flex-wrap: wrap;">
                        <label style="display: flex; align-items: center; gap: 0.5rem; font-size: 1.5rem; color: #374151; cursor: pointer;">
                          <input type="checkbox" id="trainingNeedReply" style="width: 1rem; height: 1rem;">
                          <span>提醒服务站回复</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 0.5rem; font-size: 1.5rem; color: #374151; cursor: pointer;">
                          <input type="checkbox" checked id="trainingSendWechat" style="width: 1rem; height: 1rem;">
                          <span>发送微信通知</span>
                        </label>
                      </div>
                      <button
                        type="button"
                        onclick="submitTrainingQuickReply('{$order.user_job_id}')"
                        style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-size: 1.5rem; font-weight: 600; cursor: pointer; transition: all 0.2s ease; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);"
                        onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(59, 130, 246, 0.4)';"
                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(59, 130, 246, 0.3)';"
                      >
                        <i class="fa fa-send" style="margin-right: 0.5rem;"></i>
                        发送回复
                      </button>
                    </div>
                  </div>
                </form>
              </div>
              <php>} else {</php>
              <!-- 无沟通记录时显示 -->
              <div class="training-no-communication" style="text-align: center; color: #6b7280; font-style: italic; padding: 2rem; background: #f9fafb; border-radius: 0.75rem; border: 2px dashed #d1d5db;">
                <i class="fa fa-comment-o" style="font-size: 3rem; margin-bottom: 1rem; display: block; color: #9ca3af;"></i>
                <p style="font-size: 1.25rem; margin-bottom: 1.5rem;">暂无沟通记录</p>
                <button
                  onclick="showTrainingQuickReplyForm('{$order.user_job_id}')"
                  style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-size: 1rem; font-weight: 600; cursor: pointer; transition: all 0.2s ease; box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);"
                  onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(16, 185, 129, 0.4)';"
                  onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(16, 185, 129, 0.3)';"
                >
                  <i class="fa fa-plus" style="margin-right: 0.5rem;"></i>
                  开始沟通
                </button>
              </div>
              <php>}</php>
            </div>
          </div>
          <php>}</php>

          <!-- 订单基本信息 -->
          <div class="training-detail-card training-detail-fade-in-delay-2">
            <div class="training-detail-card-header">
              <div class="training-detail-card-icon">
                <i class="fa fa-info-circle"></i>
              </div>
              <h3 class="training-detail-card-title">
                订单基本信息
                <php>if(!empty($order['zsb_id']) && $order['zsb_id'] > 0) {</php>
                <small style="margin-left: 0.5rem; padding: 0.2rem 0.5rem; background: rgba(16, 185, 129, 0.1); color: #059669; border-radius: 0.5rem; font-size: 0.75rem; font-weight: 500;">招就办</small>
                <php>} else {</php>
                <small style="margin-left: 0.5rem; padding: 0.2rem 0.5rem; background: rgba(59, 130, 246, 0.1); color: #1d4ed8; border-radius: 0.5rem; font-size: 0.75rem; font-weight: 500;">服务站</small>
                <php>}</php>
              </h3>
            </div>
            <div class="training-detail-card-body">
              <div class="training-detail-info-grid">
                <div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">订单ID：</span>
                    <span class="training-detail-info-value">#{$order.id}</span>
                  </div>
                  <!-- 招就办信息（Prime模块管理后台始终显示） -->
                  <notempty name="zsb_info">
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">订单类型：</span>
                    <span class="training-detail-info-value">
                      <span class="label label-success" style="font-size: 1.25rem; padding: 0.5rem 1rem;">招就办订单</span>
                    </span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">所属招就办：</span>
                    <span class="training-detail-info-value">{$zsb_info.service_name|default="未知招就办"}</span>
                  </div>
                  </notempty>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">订单状态：</span>
                    <span class="training-detail-info-value">
                      <!-- 优化的二级状态显示 -->
                      <div class="order-status-container">
                        <div class="status-hierarchy">
                          <span class="status-main label label-{$main_status[$order['main_status']]['style']}" style="font-size: 1.25rem; padding: 0.5rem 1rem; margin-right: 0.5rem;">
                            <i class="{$main_status[$order['main_status']]['icon']}"></i> {$main_status[$order['main_status']]['text']}
                          </span>
                          <span class="status-separator" style="color: #999; font-weight: bold; margin: 0 0.5rem;">></span>
                          <span class="status-sub label label-info" style="font-size: 1.25rem; padding: 0.5rem 1rem;">{$sub_status[$order['sub_status']]['text']}</span>
                        </div>
                        <php>if(!empty($order['status_update_time'])) {</php>
                        <div class="status-time" style="margin-top: 0.5rem;">
                          <small class="text-muted">更新时间：{:date('Y-m-d H:i:s', $order['status_update_time'])}</small>
                        </div>
                        <php>}</php>
                      </div>
                    </span>
                  </div>
                </div>
                <div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">创建时间：</span>
                    <span class="training-detail-info-value">{:date('Y-m-d H:i:s', $order['create_time'])}</span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">更新时间：</span>
                    <span class="training-detail-info-value">{:date('Y-m-d H:i:s', $order['update_time'])}</span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">报名费：</span>
                    <span class="training-detail-info-value" style="color: #10b981; font-weight: bold; font-size: 1.75rem;">
                      ¥{:number_format($order['fee_amount'] / 100, 2)}
                    </span>
                  </div>
                  <!-- 招就办订单显示招就办收益 -->
                  <notempty name="zsb_price_config">
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">招就办收益：</span>
                    <span class="training-detail-info-value" style="color: #07c160; font-weight: bold; font-size: 1.75rem;">
                      ¥{:number_format($zsb_price_config['commission'] / 100, 2)}
                    </span>
                  </div>
                  </notempty>
                </div>
              </div>
            </div>
          </div>

          <!-- 学员信息 -->
          <div class="training-detail-card training-detail-fade-in-delay-2">
            <div class="training-detail-card-header">
              <div class="training-detail-card-icon">
                <i class="fa fa-user"></i>
              </div>
              <h3 class="training-detail-card-title">学员信息</h3>
            </div>
            <div class="training-detail-card-body">
              <div class="training-detail-info-grid">
                <div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">学员ID：</span>
                    <span class="training-detail-info-value">#{$user.id}</span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">姓名：</span>
                    <span class="training-detail-info-value" style="font-weight: bold; color: #1a202c;">
                      {$user.display_realname|default="未填写姓名"}
                      <php>if(isset($user['from_resume']) && $user['from_resume']) {</php>
                      <span class="label label-info" style="font-size: 1rem; margin-left: 0.5rem; padding: 0.25rem 0.5rem;">简历来源</span>
                      <php>}</php>
                    </span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">手机号：</span>
                    <span class="training-detail-info-value">{$user.display_mobile|default=$user.mobile}</span>
                  </div>
                </div>
                <div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">身份证号：</span>
                    <span class="training-detail-info-value">{$user.idcard|default="未填写"}</span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">服务状态：</span>
                    <span class="training-detail-info-value">
                      <php>
                        $serviceStatusList = [
                          '0' => ['text' => '沟通中', 'style' => 'success'],
                          '1' => ['text' => '培训中', 'style' => 'warning'],
                          '2' => ['text' => '已入职', 'style' => 'success'],
                          '3' => ['text' => '服务终止', 'style' => 'danger']
                        ];
                        $statusCode = $user['service_status'];
                        $statusInfo = isset($serviceStatusList[$statusCode]) ? $serviceStatusList[$statusCode] : ['text' => '未知状态', 'style' => 'default'];
                      </php>
                      <span class="label label-{$statusInfo.style}" style="font-size: 1.25rem; padding: 0.5rem 1rem;">
                        {$statusInfo.text}
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 培训项目信息 -->
          <div class="training-detail-card training-detail-fade-in-delay-3">
            <div class="training-detail-card-header">
              <div class="training-detail-card-icon">
                <i class="fa fa-graduation-cap"></i>
              </div>
              <h3 class="training-detail-card-title">培训项目信息</h3>
            </div>
            <div class="training-detail-card-body">
              <div class="training-detail-info-grid">
                <div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">项目ID：</span>
                    <span class="training-detail-info-value">#{$project.id}</span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">项目名称：</span>
                    <span class="training-detail-info-value" style="font-weight: bold; color: #1a202c;">
                      {$project.name|default="未指定项目"}
                    </span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">项目分类：</span>
                    <span class="training-detail-info-value">
                      <php>
                        $categoryList = [
                          '1' => '央企',
                          '2' => '国企',
                          '3' => '事业单位',
                          '4' => '上市公司',
                          '5' => '民营企业',
                          '6' => '出国',
                          '7' => '其他'
                        ];
                        $categoryText = isset($categoryList[$project['category']]) ? $categoryList[$project['category']] : '未知';
                      </php>
                      <span class="label label-primary" style="font-size: 1.25rem; padding: 0.5rem 1rem;">
                        {$categoryText}
                      </span>
                    </span>
                  </div>
                </div>
                <div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">岗位ID：</span>
                    <span class="training-detail-info-value">#{$post.id}</span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">岗位名称：</span>
                    <span class="training-detail-info-value" style="font-weight: bold; color: #1a202c;">
                      {$post.job_name|default="未指定岗位"}
                    </span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">
                      <notempty name="zsb_info">
                        岗位报价：
                        <else />
                        岗位报价区间：
                      </notempty>
                    </span>
                    <span class="training-detail-info-value" style="color: #10b981; font-weight: bold; font-size: 1.75rem;">
                      <?php
                        // 显示报价区间格式
                        if(isset($post['service_price']) && isset($post['max_price']) && $post['max_price'] > 0) {
                          // 如果有两个价格，显示价格区间
                          if($post['service_price'] != $post['max_price']) {
                            echo '¥' . number_format($post['service_price'], 0) . ' - ¥' . number_format($post['max_price'], 0);
                          } else {
                            // 如果两个价格相同，只显示一个价格
                            echo '¥' . number_format($post['service_price'], 0);
                          }
                        } elseif(isset($post['service_price']) && $post['service_price'] > 0) {
                          // 只有一个价格
                          echo '¥' . number_format($post['service_price'], 0);
                        } else {
                          // 兼容旧数据或价格为0的情况
                          if(isset($post['price']) && $post['price'] > 0) {
                            echo '¥' . number_format($post['price'], 0);
                          } else {
                            echo '待定';
                          }
                        }
                      ?>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 服务站信息 -->
          <div class="training-detail-card">
            <div class="training-detail-card-header">
              <div class="training-detail-card-icon">
                <i class="fa fa-building"></i>
              </div>
              <h3 class="training-detail-card-title">服务站信息</h3>
            </div>
            <div class="training-detail-card-body">
              <div class="training-detail-info-grid">
                <div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">服务站ID：</span>
                    <span class="training-detail-info-value">#{$station.id}</span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">服务站名称：</span>
                    <span class="training-detail-info-value" style="font-weight: bold; color: #1a202c;">
                      {$station.service_name|default="未指定服务站"}
                    </span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">服务站收益：</span>
                    <span class="training-detail-info-value" style="color: #10b981; font-weight: bold; font-size: 1.75rem;">
                      <php>if(!empty($order['zsb_id']) && $order['zsb_id'] > 0) {</php>
                      <!-- 招就办订单：显示服务站实际收益 -->
                      ¥{:number_format($order['station_profit_detail'] / 100, 2)}
                      <php>} else {</php>
                      <!-- 普通订单：显示服务站奖励 -->
                      ¥{:number_format($order['reward_station_amt'] / 100, 2)}
                      <php>}</php>
                    </span>
                  </div>
                </div>
                <div>
                  <notempty name="parent_station">
                    <div class="training-detail-info-item">
                      <span class="training-detail-info-label">上级服务站ID：</span>
                      <span class="training-detail-info-value">#{$parent_station.id}</span>
                    </div>
                    <div class="training-detail-info-item">
                      <span class="training-detail-info-label">上级服务站名称：</span>
                      <span class="training-detail-info-value" style="font-weight: bold; color: #1a202c;">
                        {$parent_station.service_name|default="未指定上级服务站"}
                      </span>
                    </div>
                    <div class="training-detail-info-item">
                      <span class="training-detail-info-label">
                        <php>if(!empty($order['zsb_id']) && $order['zsb_id'] > 0) {</php>
                        服务站入账（包含招就办收益）：
                        <php>} else {</php>
                        服务站奖励：
                        <php>}</php>
                      </span>
                      <span class="training-detail-info-value" style="color: #10b981; font-weight: bold; font-size: 1.75rem;">
                        ¥{:number_format($order['reward_station_amt'] / 100, 2)}
                      </span>
                    </div>
                    <else />
                    <div class="training-detail-info-item">
                      <span class="training-detail-info-label">上级服务站：</span>
                      <span class="training-detail-info-value">无</span>
                    </div>
                    <div class="training-detail-info-item">
                      <span class="training-detail-info-label">
                        <php>if(!empty($order['zsb_id']) && $order['zsb_id'] > 0) {</php>
                        服务站入账（包含招就办收益）：
                        <php>} else {</php>
                        服务站奖励：
                        <php>}</php>
                      </span>
                      <span class="training-detail-info-value" style="color: #10b981; font-weight: bold; font-size: 1.75rem;">
                        ¥{:number_format($order['reward_station_amt'] / 100, 2)}
                      </span>
                    </div>
                  </notempty>
                </div>
              </div>
            </div>
          </div>

          <!-- 付款信息 -->
          <div class="training-detail-card">
            <div class="training-detail-card-header">
              <div class="training-detail-card-icon">
                <i class="fa fa-money"></i>
              </div>
              <h3 class="training-detail-card-title">付款信息</h3>
            </div>
            <div class="training-detail-card-body">
              <div class="training-detail-info-grid">
                <div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">意向金：</span>
                    <span class="training-detail-info-value" style="color: #f59e0b; font-weight: bold; font-size: 1.75rem;">
                      ¥{:number_format($order['intent_amount_yuan'], 2)}
                    </span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">部分付款：</span>
                    <span class="training-detail-info-value" style="color: #3b82f6; font-weight: bold; font-size: 1.75rem;">
                      ¥{:number_format($order['partial_amount_yuan'], 2)}
                    </span>
                  </div>
                </div>
                <div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">全额付款：</span>
                    <span class="training-detail-info-value" style="color: #10b981; font-weight: bold; font-size: 1.75rem;">
                      ¥{:number_format($order['full_amount_yuan'], 2)}
                    </span>
                  </div>
                  <div class="training-detail-info-item">
                    <span class="training-detail-info-label">总付款金额：</span>
                    <span class="training-detail-info-value" style="color: #07c160; font-weight: bold; font-size: 2rem;">
                      ¥{:number_format($order['total_paid_yuan'], 2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 支付记录 -->
          <div class="training-detail-card">
            <div class="training-detail-card-header">
              <div class="training-detail-card-icon">
                <i class="fa fa-credit-card"></i>
              </div>
              <h3 class="training-detail-card-title">支付记录</h3>
            </div>
            <div class="training-detail-card-body">
              <div class="table-responsive">
                <table class="table table-hover" style="margin-bottom: 0;">
                  <thead style="background: #f8fafc;">
                    <tr>
                      <th style="font-size: 1.5rem; padding: 1rem;">ID</th>
                      <th style="font-size: 1.5rem; padding: 1rem;">支付类型</th>
                      <th style="font-size: 1.5rem; padding: 1rem;">支付渠道</th>
                      <th style="font-size: 1.5rem; padding: 1rem;">支付金额</th>
                      <th style="font-size: 1.5rem; padding: 1rem;">支付时间</th>
                      <th style="font-size: 1.5rem; padding: 1rem;">备注</th>
                    </tr>
                  </thead>
                  <tbody>
                    <notempty name="payment_records">
                      <foreach name="payment_records" item="record">
                        <tr>
                          <td style="font-size: 1.5rem; padding: 1rem;">#{$record.id}</td>
                          <td style="font-size: 1.5rem; padding: 1rem;">
                            <php>
                            $payTypeMap = [
                              'intent' => ['text' => '意向金', 'style' => 'info'],
                              'partial' => ['text' => '部分付款', 'style' => 'warning'],
                              'full' => ['text' => '全额付款', 'style' => 'success'],
                              'refund' => ['text' => '退款', 'style' => 'danger']
                            ];
                            $payType = $payTypeMap[$record['pay_type']] ?? ['text' => '未知', 'style' => 'default'];
                            </php>
                            <span class="label label-{$payType.style}" style="font-size: 1.25rem; padding: 0.5rem 1rem;">{$payType.text}</span>
                          </td>
                          <td style="font-size: 1.5rem; padding: 1rem;">{$record.pay_channel_text}</td>
                          <td style="font-size: 1.5rem; padding: 1rem; color: #10b981; font-weight: bold;">
                            ¥{:number_format($record['pay_amount']/100, 2)}
                          </td>
                          <td style="font-size: 1.5rem; padding: 1rem;">{:date('Y-m-d H:i:s', $record['pay_time'])}</td>
                          <td style="font-size: 1.5rem; padding: 1rem;">{$record.remark|default="-"}</td>
                        </tr>
                      </foreach>
                      <else />
                      <tr>
                        <td colspan="6" class="text-center" style="font-size: 1.5rem; padding: 2rem; color: #6b7280;">
                          <i class="fa fa-info-circle" style="margin-right: 0.5rem;"></i>
                          暂无支付记录
                        </td>
                      </tr>
                    </notempty>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<include file="block/footer" />

<script>
// 页面加载时检查状态操作按钮
$(document).ready(function() {
    checkStatusButton();
});

// 检查状态操作按钮的可用性
function checkStatusButton() {
    var $btn = $('#statusActionBtn');
    if ($btn.length === 0) return;

    var orderId = $btn.data('order-id');

    // 先设置加载状态
    $btn.prop('disabled', true).text('检查中...');

    // 检查该订单是否有可操作选项
    $.get('{:U("training/getStatusOptions")}', {order_id: orderId})
        .done(function(res) {
            if (res.status == 1 && res.data.length > 0) {
                // 有可操作选项，保持正常样式
                $btn.removeClass('btn-default').addClass('btn-success');
                $btn.prop('disabled', false);
                $btn.text('状态操作');
            } else {
                // 无可操作选项，变成灰色
                $btn.removeClass('btn-success').addClass('btn-default');
                $btn.prop('disabled', true);
                $btn.text('状态操作');
                $btn.css({
                    'background-color': '#999',
                    'border-color': '#999',
                    'cursor': 'not-allowed',
                    'color': '#fff'
                });
            }
        })
        .fail(function() {
            // 请求失败时恢复默认状态
            $btn.removeClass('btn-default').addClass('btn-success');
            $btn.prop('disabled', false);
            $btn.text('状态操作');
        });
}

// 全局变量存储订单信息
var currentOrderInfo = null;

// 状态操作功能
function showStatusModal(orderId, currentStatus) {
    // 获取可用的状态转换选项
    $.get('{:U("training/getStatusOptions")}', {order_id: orderId}, function(res) {
        if (res.status == 1) {
            var options = res.data;
            var orderInfo = res.order_info;
            currentOrderInfo = orderInfo; // 保存到全局变量

            if (options.length == 0) {
                layer.msg('当前状态无可操作选项');
                return;
            }

            var optionHtml = '';
            for (var i = 0; i < options.length; i++) {
                var option = options[i];
                optionHtml += '<option value="' + option.value + '" data-has-amount="' + (option.has_amount ? '1' : '0') + '" data-amount-field="' + (option.amount_field || '') + '">' + option.text + '</option>';
            }

            // 构建费用信息提示
            var feeInfoHtml = '';
            if (orderInfo.fee_amount_yuan > 0) {
                feeInfoHtml = '<div class="status-modal-fee-info">' +
                    '<div style="display: flex; align-items: center; margin-bottom: 15px;">' +
                    '<i class="fa fa-info-circle" style="color: #2196F3; margin-right: 8px; font-size: 18px;"></i>' +
                    '<h4 style="margin: 0; color: #1976D2; font-weight: 600;">费用信息</h4>' +
                    '</div>' +
                    '<div class="fee-details-grid">' +
                    '<div class="fee-item">' +
                    '<span class="fee-label">报名费</span>' +
                    '<span class="fee-value">¥' + orderInfo.fee_amount_yuan.toFixed(2) + '</span>' +
                    '</div>' +
                    '<div class="fee-item">' +
                    '<span class="fee-label">已支付</span>' +
                    '<span class="fee-value fee-paid">¥' + orderInfo.paid_amount_yuan.toFixed(2) + '</span>' +
                    '</div>' +
                    '<div class="fee-item">' +
                    '<span class="fee-label">剩余金额</span>' +
                    '<span class="fee-value fee-remaining">¥' + orderInfo.remaining_amount_yuan.toFixed(2) + '</span>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
            }

            // 构建支付方式选项
            var payChannelOptions = [
                {value: 'offline', text: '线下支付'},
                {value: 'alipay', text: '支付宝'},
                {value: 'wechat', text: '微信支付'},
                {value: 'bank', text: '银行转账'},
                {value: 'manual', text: '手动录入'}
            ];

            var payChannelHtml = '';
            for (var i = 0; i < payChannelOptions.length; i++) {
                var channel = payChannelOptions[i];
                payChannelHtml += '<option value="' + channel.value + '">' + channel.text + '</option>';
            }

            var formHtml = '<div class="status-modal-container">' +
                // 费用信息区域
                (orderInfo.fee_amount_yuan > 0 ? feeInfoHtml : '') +

                // 表单区域
                '<form id="statusForm" class="status-form">' +
                '<div class="form-group-modern">' +
                '<label class="form-label-modern">' +
                '<i class="fa fa-exchange" style="margin-right: 8px; color: #4CAF50;"></i>' +
                '选择新状态' +
                '</label>' +
                '<select name="status" class="form-control-modern" onchange="toggleAmountInput(this)">' +
                '<option value="">请选择状态</option>' +
                optionHtml +
                '</select>' +
                '</div>' +

                '<div class="form-group-modern" id="amountGroup" style="display:none;">' +
                '<label class="form-label-modern" id="amountLabel">' +
                '<i class="fa fa-money" style="margin-right: 8px; color: #FF9800;"></i>' +
                '金额' +
                '</label>' +
                '<input type="number" name="amount" class="form-control-modern" step="0.01" placeholder="请输入金额（元）" oninput="validateAmount(this)" onkeydown="preventArrowKeys(event)" onwheel="preventWheel(event)">' +
                '<small id="amountHelp" class="form-help-text"></small>' +
                '<div id="amountError" class="form-error-text" style="display:none;"></div>' +
                '</div>' +

                '<div class="form-group-modern" id="payChannelGroup" style="display:none;">' +
                '<label class="form-label-modern">' +
                '<i class="fa fa-credit-card" style="margin-right: 8px; color: #9C27B0;"></i>' +
                '支付方式' +
                '</label>' +
                '<select name="pay_channel" class="form-control-modern">' +
                '<option value="">请选择支付方式</option>' +
                payChannelHtml +
                '</select>' +
                '</div>' +

                '<div class="form-group-modern">' +
                '<label class="form-label-modern">' +
                '<i class="fa fa-comment" style="margin-right: 8px; color: #607D8B;"></i>' +
                '备注' +
                '</label>' +
                '<textarea name="remark" class="form-control-modern" rows="3" placeholder="请输入操作备注"></textarea>' +
                '</div>' +
                '</form>' +
                '</div>';

            layer.open({
                type: 1,
                title: '<div style="text-align: center;"><i class="fa fa-cog"></i> 状态操作</div>',
                area: ['700px', 'auto'],
                maxHeight: '80%',
                offset: 'auto',
                shade: 0.3,
                shadeClose: false,
                content: formHtml,
                btn: ['确定', '取消'],
                btnAlign: 'c',
                skin: 'modern-layer-skin',
                success: function(layero, index) {
                    // 添加现代化样式
                    layero.css({
                        'border-radius': '12px',
                        'box-shadow': '0 10px 40px rgba(0,0,0,0.15)',
                        'border': 'none'
                    });

                    // 标题栏样式
                    layero.find('.layui-layer-title').css({
                        'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        'color': 'white',
                        'border-radius': '12px 12px 0 0',
                        'font-weight': '500',
                        'padding': '15px 20px',
                        'border': 'none',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center'
                    });

                    // 确保标题居中
                    layero.find('.layui-layer-title').find('div').css({
                        'width': '100%',
                        'text-align': 'center',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center',
                        'gap': '8px'
                    });

                    // 内容区域样式
                    layero.find('.layui-layer-content').css({
                        'padding': '0',
                        'background': '#f8f9fa',
                        'border-radius': '0 0 12px 12px'
                    });

                    // 按钮样式
                    layero.find('.layui-layer-btn a').css({
                        'border-radius': '8px',
                        'font-weight': '500',
                        'padding': '15px 50px',
                        'margin': '0 20px',
                        'transition': 'all 0.3s ease',
                        'min-width': '140px',
                        'max-width': 'none',
                        'width': 'auto',
                        'height': 'auto',
                        'text-align': 'center',
                        'white-space': 'nowrap',
                        'overflow': 'visible',
                        'display': 'inline-block',
                        'box-sizing': 'border-box',
                        'font-size': '14px',
                        'line-height': '1.4',
                        'text-overflow': 'clip',
                        'word-wrap': 'normal'
                    });

                    layero.find('.layui-layer-btn0').css({
                        'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        'border': 'none',
                        'color': 'white'
                    });

                    layero.find('.layui-layer-btn1').css({
                        'background': '#f8f9fa',
                        'border': '1px solid #dee2e6',
                        'color': '#6c757d'
                    });

                    // 关闭按钮样式
                    layero.find('.layui-layer-close').css({
                        'color': 'white',
                        'font-size': '18px',
                        'font-weight': 'bold'
                    });
                },
                yes: function(index) {
                    submitStatusChange(orderId, index);
                }
            });
        } else {
            layer.msg(res.info || '获取状态选项失败');
        }
    });
}

function toggleAmountInput(select) {
    var selectedOption = $(select).find('option:selected');
    var hasAmount = selectedOption.data('has-amount') == '1';
    var amountField = selectedOption.data('amount-field');

    if (hasAmount) {
        $('#amountGroup').show();
        $('#payChannelGroup').show(); // 显示支付方式选择
        $('#amountLabel').text(getAmountLabel(amountField) + '：');

        // 设置金额提示和限制
        var $amountInput = $('input[name="amount"]');
        var $amountHelp = $('#amountHelp');
        var $amountError = $('#amountError');
        var $payChannelSelect = $('select[name="pay_channel"]');
        var maxAmount = 0;

        // 清除之前的错误提示
        $amountError.hide();

        // 重置支付方式选择
        $payChannelSelect.val('');

        if (currentOrderInfo) {
            switch(amountField) {
                case 'intent_amount':
                    maxAmount = currentOrderInfo.remaining_amount_yuan;
                    $amountHelp.text('最大可输入金额：¥' + maxAmount.toFixed(2));
                    $amountInput.val(''); // 清空输入框
                    $amountInput.prop('readonly', false); // 移除只读状态
                    $amountInput.css('background-color', ''); // 恢复背景色
                    break;
                case 'partial_amount':
                    maxAmount = currentOrderInfo.remaining_amount_yuan;
                    $amountHelp.text('最大可输入金额：¥' + maxAmount.toFixed(2));
                    $amountInput.val(''); // 清空输入框
                    $amountInput.prop('readonly', false); // 移除只读状态
                    $amountInput.css('background-color', ''); // 恢复背景色
                    break;
                case 'fee_amount':
                    maxAmount = currentOrderInfo.remaining_amount_yuan;
                    $amountHelp.text('剩余应付金额：¥' + maxAmount.toFixed(2) + '（全额付款时金额固定）');
                    // 全额付款时自动填入剩余金额并设为只读
                    $amountInput.val(maxAmount.toFixed(2));
                    $amountInput.prop('readonly', true);
                    $amountInput.css('background-color', '#f5f5f5');
                    break;
                default:
                    maxAmount = currentOrderInfo.remaining_amount_yuan;
                    $amountHelp.text('最大可输入金额：¥' + maxAmount.toFixed(2));
                    $amountInput.val(''); // 清空输入框
                    $amountInput.prop('readonly', false); // 移除只读状态
                    $amountInput.css('background-color', ''); // 恢复背景色
            }

            $amountInput.attr('max', maxAmount);
            $amountInput.data('max-amount', maxAmount);
        }
    } else {
        $('#amountGroup').hide();
        $('#payChannelGroup').hide(); // 隐藏支付方式选择
    }
}

function getAmountLabel(field) {
    switch(field) {
        case 'intent_amount': return '意向金金额';
        case 'partial_amount': return '部分付款金额';
        case 'fee_amount': return '全额付款金额';
        default: return '金额';
    }
}

// 防止方向键上下调整金额
function preventArrowKeys(event) {
    // 阻止上箭头键（keyCode 38）和下箭头键（keyCode 40）
    if (event.keyCode === 38 || event.keyCode === 40) {
        event.preventDefault();
        return false;
    }
}

// 防止鼠标滚轮调整金额
function preventWheel(event) {
    event.preventDefault();
    return false;
}

// 金额验证函数
function validateAmount(input) {
    var $input = $(input);
    var $error = $('#amountError');
    var amount = parseFloat($input.val());
    var maxAmount = parseFloat($input.data('max-amount'));

    $error.hide();

    if (isNaN(amount) || amount <= 0) {
        return true; // 空值或0不显示错误，由必填验证处理
    }

    if (amount > maxAmount) {
        $error.text('付款金额不能超过剩余应付金额 ¥' + maxAmount.toFixed(2)).show();
        return false;
    }

    return true;
}

function submitStatusChange(orderId, layerIndex) {
    var form = $('#statusForm');
    var status = form.find('[name="status"]').val();
    var amount = form.find('[name="amount"]').val();
    var payChannel = form.find('[name="pay_channel"]').val();
    var remark = form.find('[name="remark"]').val();

    if (!status) {
        layer.msg('请选择状态');
        return;
    }

    var selectedOption = form.find('[name="status"] option:selected');
    var hasAmount = selectedOption.data('has-amount') == '1';
    var amountField = selectedOption.data('amount-field');

    if (hasAmount && !amount) {
        layer.msg('请输入金额');
        return;
    }

    if (hasAmount && !payChannel) {
        layer.msg('请选择支付方式');
        return;
    }

    // 验证金额
    if (hasAmount && amount) {
        var $amountInput = form.find('[name="amount"]');
        if (!validateAmount($amountInput[0])) {
            layer.msg('付款金额超出限制，请检查输入');
            return;
        }

        var amountValue = parseFloat(amount);
        var maxAmount = parseFloat($amountInput.data('max-amount'));

        if (amountValue > maxAmount) {
            layer.msg('付款金额不能超过剩余应付金额 ¥' + maxAmount.toFixed(2));
            return;
        }
    }

    var postData = {
        order_id: orderId,
        status: status,
        remark: remark,
        amount_info: {},
        pay_channel: payChannel
    };

    if (hasAmount && amount) {
        postData.amount_info[amountField] = parseFloat(amount);
    }

    $.post('{:U("training/updateStatus")}', postData, function(res) {
        if (res.status == 1) {
            layer.close(layerIndex);
            layer.msg('状态更新成功', {icon: 1, time: 650}, function() {
                location.reload();
            });
        } else if (res.status === 'payment_required') {
            // 需要付款确认
            layer.close(layerIndex);
            showPaymentConfirmModal(orderId, res.payment_info, res.target_status, res.remark);
        } else if (res.status === 'fund_operation_confirm') {
            // 需要资金操作确认
            layer.close(layerIndex);
            showFundOperationConfirmModal(orderId, res.fund_info, res.target_status, res.remark);
        } else {
            layer.msg(res.info || '状态更新失败');
        }
    });
}

// 显示付款确认弹窗
function showPaymentConfirmModal(orderId, paymentInfo, targetStatus, remark) {
    // 先获取支付历史记录
    $.get('{:U("training/getPaymentHistory")}', {order_id: orderId}, function(res) {
        var orderInfo = paymentInfo.order_info;
        var remainingAmount = paymentInfo.remaining;
        var remainingAmountYuan = (remainingAmount / 100).toFixed(2);
        var feeAmountYuan = (paymentInfo.fee_amount / 100).toFixed(2);
        var paidAmountYuan = (paymentInfo.paid_amount / 100).toFixed(2);
        var payments = res.status == 1 ? (res.data || []) : [];

        // 构建支付记录HTML
        var paymentHistoryHtml = '';
        if (payments.length > 0) {
            paymentHistoryHtml = `
                <div style="margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <i class="fa fa-history" style="color: #667eea; margin-right: 8px; font-size: 18px;"></i>
                        <h4 style="margin: 0; color: #374151; font-weight: 600;">支付历史记录</h4>
                    </div>
                    <div style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); max-height: 200px; overflow-y: auto;">
                        <table style="width: 100%; border-collapse: collapse; margin: 0; font-size: 13px;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                    <th style="padding: 10px 8px; text-align: left; font-weight: 600; border: none;">时间</th>
                                    <th style="padding: 10px 8px; text-align: left; font-weight: 600; border: none;">金额</th>
                                    <th style="padding: 10px 8px; text-align: left; font-weight: 600; border: none;">方式</th>
                                    <th style="padding: 10px 8px; text-align: left; font-weight: 600; border: none;">类型</th>
                                </tr>
                            </thead>
                            <tbody>`;

            payments.forEach(function(payment, index) {
                var payTypeText = payment.pay_type_text || payment.pay_type;
                var payChannelText = payment.pay_channel_text || payment.pay_channel;
                var amountYuan = (payment.pay_amount / 100).toFixed(2);
                var rowBg = index % 2 === 0 ? '#f8f9fa' : 'white';

                var amountColor = '#4CAF50';
                if (payment.pay_type === 'refund') {
                    amountColor = '#f44336';
                }

                var typeColor = '#2196F3';
                if (payment.pay_type === 'intent') typeColor = '#FF9800';
                else if (payment.pay_type === 'partial') typeColor = '#9C27B0';
                else if (payment.pay_type === 'full') typeColor = '#4CAF50';
                else if (payment.pay_type === 'refund') typeColor = '#f44336';

                paymentHistoryHtml += `
                    <tr style="background: ${rowBg};">
                        <td style="padding: 8px; border: none; color: #374151;">${payment.pay_time_text}</td>
                        <td style="padding: 8px; border: none; color: ${amountColor}; font-weight: 600;">¥${amountYuan}</td>
                        <td style="padding: 8px; border: none; color: #374151;">${payChannelText}</td>
                        <td style="padding: 8px; border: none;">
                            <span style="background: ${typeColor}; color: white; padding: 2px 6px; border-radius: 8px; font-size: 11px; font-weight: 500;">${payTypeText}</span>
                        </td>
                    </tr>`;
            });

            paymentHistoryHtml += `
                            </tbody>
                        </table>
                    </div>
                </div>`;
        } else {
            paymentHistoryHtml = `
                <div style="margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <i class="fa fa-history" style="color: #667eea; margin-right: 8px; font-size: 18px;"></i>
                        <h4 style="margin: 0; color: #374151; font-weight: 600;">支付历史记录</h4>
                    </div>
                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; border: 2px dashed #e5e7eb;">
                        <i class="fa fa-credit-card" style="font-size: 32px; color: #d1d5db; margin-bottom: 10px;"></i>
                        <p style="color: #6b7280; margin: 0; font-size: 13px;">暂无支付记录</p>
                    </div>
                </div>`;
        }

        var content = `
            <div class="status-modal-container">
                <div style="text-align: center; margin-bottom: 25px;">
                    <i class="fa fa-exclamation-triangle" style="font-size: 48px; color: #f39c12; margin-bottom: 15px;"></i>
                    <h3 style="color: #e74c3c; margin: 0; font-weight: 600;">付款确认提醒</h3>
                </div>

                <div class="status-modal-fee-info">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <i class="fa fa-info-circle" style="color: #2196F3; margin-right: 8px; font-size: 18px;"></i>
                        <h4 style="margin: 0; color: #1976D2; font-weight: 600;">该培训订单还需支付款项</h4>
                    </div>
                    <div class="fee-details-grid">
                        <div class="fee-item">
                            <span class="fee-label">培训项目</span>
                            <span class="fee-value" style="color: #374151;">${orderInfo.project_name || '未知项目'}</span>
                        </div>
                        <div class="fee-item">
                            <span class="fee-label">学员姓名</span>
                            <span class="fee-value" style="color: #374151;">${orderInfo.user_name || '未知学员'}</span>
                        </div>
                        <div class="fee-item">
                            <span class="fee-label">报名费总额</span>
                            <span class="fee-value">¥${feeAmountYuan}</span>
                        </div>
                        <div class="fee-item">
                            <span class="fee-label">已支付金额</span>
                            <span class="fee-value fee-paid">¥${paidAmountYuan}</span>
                        </div>
                        <div class="fee-item" style="grid-column: 1 / -1; background: rgba(255, 152, 0, 0.1); border: 2px solid #FF9800;">
                            <span class="fee-label">剩余应付</span>
                            <span class="fee-value fee-remaining" style="font-size: 20px; font-weight: 700;">¥${remainingAmountYuan}</span>
                        </div>
                    </div>
                </div>

                ${paymentHistoryHtml}

                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin-bottom: 20px;">
                    <p style="margin: 0; color: #856404; font-size: 14px;">
                        <i class="fa fa-info-circle" style="margin-right: 8px;"></i>
                        请确认是否已收到学员付款？确认后将自动创建支付记录并执行状态跳转。
                    </p>
                </div>

                <div class="form-group-modern">
                    <label class="form-label-modern">
                        <i class="fa fa-credit-card" style="margin-right: 8px; color: #667eea;"></i>
                        收款方式
                    </label>
                    <select id="confirmPayChannel" class="form-control-modern">
                        <option value="">请选择收款方式</option>
                        <option value="offline">线下支付</option>
                        <option value="alipay">支付宝</option>
                        <option value="wechat">微信支付</option>
                        <option value="bank">银行转账</option>
                    </select>
                    <span class="form-help-text">选择学员实际使用的付款方式</span>
                </div>
            </div>
        `;

        layer.open({
            type: 1,
            title: '<i class="fa fa-credit-card" style="margin-right: 8px;"></i>付款确认',
            content: content,
            area: ['600px', 'auto'],
            skin: 'modern-layer-skin',
            btn: ['确认已收款', '取消操作'],
            btn1: function(index) {
                var payChannel = $('#confirmPayChannel').val();
                if (!payChannel) {
                    layer.msg('请选择收款方式', {icon: 2});
                    return false;
                }
                confirmPayment(orderId, remainingAmount, targetStatus, remark, payChannel);
                layer.close(index);
            },
            btn2: function(index) {
                layer.close(index);
            },
            success: function(layero, index) {
                // 应用现代化样式
                layero.css({
                    'border-radius': '12px',
                    'overflow': 'hidden',
                    'box-shadow': '0 10px 25px rgba(0,0,0,0.15)'
                });
            }
        });
    }).fail(function() {
        layer.msg('获取支付记录失败，但仍可进行付款确认', {icon: 2});
        // 如果获取支付记录失败，显示简化版本
        showSimplePaymentConfirmModal(orderId, paymentInfo, targetStatus, remark);
    });
}

// 显示资金操作确认弹窗
function showFundOperationConfirmModal(orderId, fundInfo, targetStatus, remark) {
    var orderInfo = fundInfo.order_info;
    var fundAmountYuan = (fundInfo.amount / 100).toFixed(2);
    var operationType = fundInfo.operation_type;
    var description = fundInfo.description;

    var operationText = operationType === 'freeze' ? '冻结' : (operationType === 'refund' ? '退款' : '解冻');
    var operationColor = operationType === 'freeze' ? '#e74c3c' : (operationType === 'refund' ? '#f39c12' : '#27ae60');
    var operationIcon = operationType === 'freeze' ? 'fa-lock' : (operationType === 'refund' ? 'fa-undo' : 'fa-unlock');

    var content = `
        <div class="fund-operation-modal" style="padding: 20px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <i class="fa ${operationIcon}" style="font-size: 48px; color: ${operationColor}; margin-bottom: 10px;"></i>
                <h3 style="color: ${operationColor}; margin: 0;">资金操作确认</h3>
            </div>

            <div class="fund-info" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #2c3e50;">本次操作将${operationType === 'refund' ? '扣除冻结资金' : operationText + '服务站资金'}：</h4>
                <div style="margin: 10px 0;">
                    <div style="display: flex; justify-content: space-between; margin: 8px 0;">
                        <span>培训项目：</span>
                        <strong>${orderInfo.project_name || '未知项目'}</strong>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin: 8px 0;">
                        <span>学员姓名：</span>
                        <strong>${orderInfo.user_name || '未知学员'}</strong>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin: 8px 0; border-top: 1px solid #dee2e6; padding-top: 8px;">
                        <span>${operationType === 'refund' ? '扣除' : operationText}金额：</span>
                        <strong style="color: ${operationColor}; font-size: 18px;">${fundAmountYuan}元</strong>
                    </div>
                </div>
            </div>

            <div style="background: ${operationType === 'freeze' ? '#f8d7da' : (operationType === 'refund' ? '#fff3cd' : '#d4edda')}; padding: 15px; border-radius: 8px; border-left: 4px solid ${operationColor}; margin-bottom: 20px;">
                <p style="margin: 0; color: ${operationType === 'freeze' ? '#721c24' : (operationType === 'refund' ? '#856404' : '#155724')};">
                    <i class="fa fa-info-circle" style="margin-right: 8px;"></i>
                    ${description}${operationType === 'unfreeze' ? '，资金将转为可提现状态' : (operationType === 'refund' ? '，冻结资金将被扣除' : '')}
                </p>
            </div>
        </div>
    `;

    layer.open({
        type: 1,
        title: '资金操作确认',
        content: content,
        area: ['450px', 'auto'],
        btn: ['确认执行', '取消操作'],
        btn1: function(index) {
            confirmFundOperation(orderId, targetStatus, remark);
            layer.close(index);
        },
        btn2: function(index) {
            layer.close(index);
        }
    });
}

// 确认收款
function confirmPayment(orderId, remainingAmount, targetStatus, remark, payChannel) {
    var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

    $.post('{:U("training/confirmRemainingPayment")}', {
        order_id: orderId,
        remaining_amount: remainingAmount,
        target_status: targetStatus,
        remark: remark,
        pay_channel: payChannel
    }, function(res) {
        layer.close(loadingIndex);
        if (res.status == 1) {
            layer.msg('确认收款成功', {icon: 1, time: 1000}, function() {
                location.reload();
            });
        } else {
            layer.msg(res.info || '确认收款失败');
        }
    }).fail(function() {
        layer.close(loadingIndex);
        layer.msg('网络请求失败');
    });
}

// 确认资金操作
function confirmFundOperation(orderId, targetStatus, remark) {
    var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

    $.post('{:U("training/confirmFundOperation")}', {
        order_id: orderId,
        target_status: targetStatus,
        remark: remark
    }, function(res) {
        layer.close(loadingIndex);
        if (res.status == 1) {
            layer.msg('操作成功', {icon: 1, time: 1000}, function() {
                location.reload();
            });
        } else {
            layer.msg(res.info || '操作失败');
        }
    }).fail(function() {
        layer.close(loadingIndex);
        layer.msg('网络请求失败');
    });
}



function showStatusHistory(orderId) {
    $.get('{:U("training/getStatusHistory")}', {order_id: orderId}, function(res) {
        if (res.status == 1) {
            var data = res.data;
            var current = data.current_status;
            var statusHistory = data.status_history || [];
            var payments = data.payment_records || [];
            var milestones = data.key_milestones || {};
            var stats = data.statistics || {};

            var contentHtml = '<div style="padding: 25px; background: white; margin: 0;">';

            // 当前状态信息
            contentHtml += '<div class="status-section" style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">' +
                '<h4 style="color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 8px; margin-bottom: 20px; font-weight: 600; display: flex; align-items: center;">' +
                '<i class="fa fa-info-circle" style="margin-right: 8px; color: #3498db;"></i> 当前状态</h4>' +
                '<div class="current-status-display" style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">' +
                '<span class="modern-badge primary" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 8px 16px; border-radius: 20px; font-size: 13px; font-weight: 500; box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);">' + current.main_status_text + '</span>' +
                '<span class="modern-badge info" style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 6px 14px; border-radius: 16px; font-size: 12px; font-weight: 500; box-shadow: 0 2px 4px rgba(116, 185, 255, 0.3);">' + current.sub_status_text + '</span>' +
                '</div>';

            if (current.update_time_text) {
                contentHtml += '<div style="margin: 12px 0; padding: 10px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #3498db;"><small style="color: #5a6c7d; font-weight: 500;"><i class="fa fa-clock-o" style="margin-right: 6px; color: #3498db;"></i>最后更新：' + current.update_time_text + '</small></div>';
            }
            if (current.remark) {
                contentHtml += '<div style="margin: 12px 0; padding: 10px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;"><small style="color: #856404; font-weight: 500;"><i class="fa fa-comment" style="margin-right: 6px; color: #ffc107;"></i>备注：' + current.remark + '</small></div>';
            }
            contentHtml += '</div>';

            // 状态变更历史
            if (statusHistory && statusHistory.length > 0) {
                contentHtml += '<div class="status-section" style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">' +
                    '<h4 style="color: #2c3e50; border-bottom: 3px solid #27ae60; padding-bottom: 8px; margin-bottom: 20px; font-weight: 600; display: flex; align-items: center;">' +
                    '<i class="fa fa-history" style="margin-right: 8px; color: #27ae60;"></i> 状态变更历史 <span style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px;">' + statusHistory.length + '条</span></h4>';

                for (var i = 0; i < statusHistory.length; i++) {
                    var record = statusHistory[i];
                    contentHtml += '<div class="history-item" style="border-left: 4px solid #27ae60; padding: 16px 20px; margin-bottom: 12px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); transition: all 0.3s ease;">';

                    // 状态变更信息
                    if (record.from_status.main_text) {
                        contentHtml += '<div style="margin-bottom: 12px; display: flex; align-items: center; gap: 10px;">' +
                            '<span class="modern-badge secondary" style="background: #6c757d; color: white; padding: 4px 12px; border-radius: 14px; font-size: 11px; font-weight: 500;">' + record.from_status.main_text + ' - ' + record.from_status.sub_text + '</span>' +
                            '<i class="fa fa-arrow-right" style="color: #27ae60; font-size: 14px;"></i>' +
                            '<span class="modern-badge success" style="background: linear-gradient(135deg, #00b894 0%, #00a085 100%); color: white; padding: 4px 12px; border-radius: 14px; font-size: 11px; font-weight: 500; box-shadow: 0 2px 4px rgba(0, 184, 148, 0.3);">' + record.to_status.main_text + ' - ' + record.to_status.sub_text + '</span>' +
                            '</div>';
                    } else {
                        contentHtml += '<div style="margin-bottom: 12px;">' +
                            '<span class="modern-badge success" style="background: linear-gradient(135deg, #00b894 0%, #00a085 100%); color: white; padding: 6px 14px; border-radius: 16px; font-size: 12px; font-weight: 500; box-shadow: 0 2px 4px rgba(0, 184, 148, 0.3);"><i class="fa fa-plus-circle" style="margin-right: 4px;"></i>创建订单：' + record.to_status.main_text + ' - ' + record.to_status.sub_text + '</span>' +
                            '</div>';
                    }

                    // 时间和操作人（更现代化的布局）
                    contentHtml += '<div class="history-time" style="font-size: 12px; color: #5a6c7d; margin-bottom: 8px; display: flex; align-items: center; gap: 15px;">' +
                        '<span style="display: flex; align-items: center; gap: 4px;"><i class="fa fa-clock-o" style="color: #74b9ff;"></i> ' + record.create_time_text + '</span>';

                    if (record.operator && record.operator.name) {
                        contentHtml += '<span style="display: flex; align-items: center; gap: 4px;"><i class="fa fa-user" style="color: #a29bfe;"></i> ' + record.operator.name + '</span>';
                    }
                    contentHtml += '</div>';

                    // 备注（如果有的话，显示在卡片中）
                    if (record.remark) {
                        contentHtml += '<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 8px 12px; margin-top: 8px;">' +
                            '<span style="font-size: 12px; color: #856404; font-weight: 500;"><i class="fa fa-comment" style="margin-right: 6px; color: #fdcb6e;"></i>' + record.remark + '</span></div>';
                    }

                    // 金额信息（更醒目的显示）
                    if (record.amount_info) {
                        contentHtml += '<div style="background: #ffe8d6; border: 1px solid #fab1a0; border-radius: 6px; padding: 8px 12px; margin-top: 8px;">' +
                            '<span style="font-size: 12px; color: #d63031; font-weight: 500;"><i class="fa fa-money" style="margin-right: 6px; color: #e17055;"></i>金额变更</span></div>';
                    }

                    contentHtml += '</div>';
                }
                contentHtml += '</div>';
            }

            // 付款记录
            if (payments && payments.length > 0) {
                contentHtml += '<div class="status-section" style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">' +
                    '<h4 style="color: #2c3e50; border-bottom: 3px solid #e74c3c; padding-bottom: 8px; margin-bottom: 20px; font-weight: 600; display: flex; align-items: center;">' +
                    '<i class="fa fa-credit-card" style="margin-right: 8px; color: #e74c3c;"></i> 付款记录 <span style="background: #e74c3c; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px;">' + payments.length + '条</span></h4>' +
                    '<div class="table-responsive" style="border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">' +
                    '<table class="table table-sm" style="margin-bottom: 0; font-size: 13px; background: white;">' +
                    '<thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;"><tr><th style="border: none; padding: 12px; font-weight: 600;">类型</th><th style="border: none; padding: 12px; font-weight: 600;">金额</th><th style="border: none; padding: 12px; font-weight: 600;">渠道</th><th style="border: none; padding: 12px; font-weight: 600;">时间</th><th style="border: none; padding: 12px; font-weight: 600;">备注</th></tr></thead>' +
                    '<tbody>';

                for (var i = 0; i < payments.length; i++) {
                    var payment = payments[i];
                    var typeClass = payment.type === 'refund' ? 'text-danger' : 'text-success';
                    var rowBg = i % 2 === 0 ? '#f8f9fa' : 'white';
                    contentHtml += '<tr style="background: ' + rowBg + '; transition: all 0.3s ease;">' +
                        '<td style="border: none; padding: 12px; vertical-align: middle;"><span class="modern-badge ' + (payment.type === 'refund' ? 'danger' : 'success') + '" style="background: ' + (payment.type === 'refund' ? 'linear-gradient(135deg, #fd79a8 0%, #e84393 100%)' : 'linear-gradient(135deg, #00b894 0%, #00a085 100%)') + '; color: white; padding: 4px 10px; border-radius: 12px; font-size: 11px; font-weight: 500;">' + payment.type_text + '</span></td>' +
                        '<td style="border: none; padding: 12px; vertical-align: middle;"><strong style="color: #2d3436; font-size: 14px;">' + payment.amount_text + '</strong></td>' +
                        '<td style="border: none; padding: 12px; vertical-align: middle; color: #636e72;">' + payment.channel + '</td>' +
                        '<td style="border: none; padding: 12px; vertical-align: middle; color: #636e72;">' + payment.pay_time_text + '</td>' +
                        '<td style="border: none; padding: 12px; vertical-align: middle; color: #636e72;">' + (payment.remark || '-') + '</td>' +
                        '</tr>';
                }
                contentHtml += '</tbody></table></div></div>';
            }

            // 关键节点
            contentHtml += '<div class="status-section" style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">' +
                '<h4 style="color: #2c3e50; border-bottom: 3px solid #6c5ce7; padding-bottom: 8px; margin-bottom: 20px; font-weight: 600; display: flex; align-items: center;">' +
                '<i class="fa fa-calendar" style="margin-right: 8px; color: #6c5ce7;"></i> 关键节点</h4>' +
                '<div class="row" style="margin: 0;">' +
                '<div class="col-md-6" style="padding: 0 10px 0 0;"><div style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 12px; border-radius: 8px; margin-bottom: 10px;"><i class="fa fa-plus-circle" style="margin-right: 6px;"></i><strong>订单创建</strong><br><small style="opacity: 0.9;">' + milestones.created_text + '</small></div></div>' +
                '<div class="col-md-6" style="padding: 0 0 0 10px;"><div style="background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%); color: white; padding: 12px; border-radius: 8px; margin-bottom: 10px;"><i class="fa fa-refresh" style="margin-right: 6px;"></i><strong>最后更新</strong><br><small style="opacity: 0.9;">' + milestones.last_updated_text + '</small></div></div>' +
                '</div>';

            if (milestones.status_updated_text) {
                contentHtml += '<div style="background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%); color: white; padding: 12px; border-radius: 8px; margin-top: 10px;"><i class="fa fa-clock-o" style="margin-right: 6px;"></i><strong>状态更新：</strong> ' + milestones.status_updated_text + '</div>';
            }

            // 统计信息
            if (stats.total_status_changes > 0 || stats.total_payments > 0) {
                contentHtml += '<div style="margin-top: 20px; padding: 16px; background: linear-gradient(135deg, #dfe6e9 0%, #b2bec3 100%); border-radius: 10px; border: 1px solid #b2bec3;">' +
                    '<div style="display: flex; align-items: center; margin-bottom: 8px;"><i class="fa fa-bar-chart" style="margin-right: 8px; color: #2d3436; font-size: 16px;"></i><strong style="color: #2d3436; font-size: 14px;">统计信息</strong></div>' +
                    '<div style="display: flex; gap: 20px; flex-wrap: wrap;">';
                if (stats.total_status_changes > 0) {
                    contentHtml += '<div style="background: white; padding: 8px 12px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);"><span style="color: #00b894; font-weight: 600;">' + stats.total_status_changes + '</span> <small style="color: #636e72;">次状态变更</small></div>';
                }
                if (stats.total_payments > 0) {
                    contentHtml += '<div style="background: white; padding: 8px 12px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);"><span style="color: #e17055; font-weight: 600;">' + stats.total_payments + '</span> <small style="color: #636e72;">次付款</small>';
                    if (stats.total_amount_paid > 0) {
                        contentHtml += '<br><small style="color: #2d3436; font-weight: 500;">共 ' + (stats.total_amount_paid / 100).toFixed(2) + ' 元</small>';
                    }
                    contentHtml += '</div>';
                }
                contentHtml += '</div></div>';
            }

            contentHtml += '</div>';

            contentHtml += '</div>';

            layer.open({
                type: 1,
                title: '<div style="text-align: center;"><i class="fa fa-history"></i> 状态历史</div>',
                area: ['900px', '80%'], // 调整为响应式高度
                offset: 'auto', // 页面居中
                maxmin: true, // 允许最大化最小化
                shade: 0.3,
                shadeClose: true,
                content: contentHtml,
                skin: 'modern-layer-skin',
                success: function(layero, index) {
                    // 添加现代化样式
                    layero.css({
                        'border-radius': '12px',
                        'box-shadow': '0 10px 40px rgba(0,0,0,0.15)',
                        'border': 'none'
                    });

                    // 标题栏样式
                    layero.find('.layui-layer-title').css({
                        'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        'color': 'white',
                        'border-radius': '12px 12px 0 0',
                        'font-weight': '500',
                        'padding': '15px 20px',
                        'border': 'none',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center'
                    });

                    // 确保标题居中
                    layero.find('.layui-layer-title').find('div').css({
                        'width': '100%',
                        'text-align': 'center',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center',
                        'gap': '8px'
                    });

                    // 内容区域样式
                    layero.find('.layui-layer-content').css({
                        'padding': '0',
                        'background': '#f8f9fa',
                        'border-radius': '0 0 12px 12px',
                        'max-height': 'calc(80vh - 60px)',
                        'overflow-y': 'auto'
                    });

                    // 关闭按钮样式
                    layero.find('.layui-layer-close').css({
                        'color': 'white',
                        'font-size': '18px',
                        'font-weight': 'bold'
                    });

                    // 滚动条样式
                    layero.find('.layui-layer-content').addClass('custom-scrollbar');

                    // 添加一些样式
                    layero.find('.current-status-display').css({
                        'margin': '5px 0',
                        'padding': '5px 0'
                    });
                }
            });
        } else {
            layer.msg(res.info || '获取状态历史失败');
        }
    });
}

// 取消订单功能
function cancelOrder(orderId, currentStatus) {
    var confirmText = '确定要取消该订单吗？';

    // 根据当前状态给出不同的提示
    if (currentStatus == 'full_paid' || currentStatus.indexOf('training') !== -1 || currentStatus.indexOf('employment') !== -1) {
        confirmText = '该订单已付款，取消后将进行退款处理。确定要取消吗？';
    }

    layer.confirm(confirmText, {
        btn: ['确定取消', '我再想想'],
        title: '<i class="fa fa-exclamation-triangle"></i> 取消订单确认',
        skin: 'modern-layer-skin',
        area: ['500px', '280px'],
        shade: 0.5,
        success: function(layero, index) {
            // 确保弹窗居中显示
            var windowWidth = $(window).width();
            var windowHeight = $(window).height();
            var layerWidth = layero.outerWidth();
            var layerHeight = layero.outerHeight();

            // 计算居中位置
            var left = (windowWidth - layerWidth) / 2;
            var top = (windowHeight - layerHeight) / 2;

            // 添加现代化样式
            layero.css({
                'border-radius': '16px',
                'box-shadow': '0 20px 60px rgba(0,0,0,0.2)',
                'border': 'none',
                'position': 'fixed',
                'left': left + 'px',
                'top': top + 'px',
                'transform': 'none'
            });

            // 标题栏样式 - 使用警告色
            layero.find('.layui-layer-title').css({
                'background': 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
                'color': 'white',
                'border-radius': '16px 16px 0 0',
                'font-weight': '600',
                'padding': '20px 25px',
                'border': 'none',
                'text-align': 'center',
                'font-size': '18px',
                'box-shadow': '0 2px 10px rgba(251,191,36,0.3)'
            });

            // 内容区域样式
            layero.find('.layui-layer-content').css({
                'padding': '40px 30px',
                'background': 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                'font-size': '16px',
                'line-height': '1.6',
                'color': '#374151',
                'text-align': 'center',
                'min-height': '80px'
            });

            // 按钮区域样式
            layero.find('.layui-layer-btn').css({
                'padding': '30px',
                'text-align': 'center',
                'background': 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
                'border-radius': '0 0 16px 16px',
                'border-top': '1px solid #e9ecef'
            });

            // 按钮样式
            layero.find('.layui-layer-btn a').css({
                'border-radius': '8px',
                'font-weight': '500',
                'padding': '12px 30px',
                'margin': '0 15px',
                'transition': 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                'min-width': '120px',
                'font-size': '15px',
                'line-height': '1.4',
                'text-align': 'center',
                'display': 'inline-block',
                'box-sizing': 'border-box',
                'cursor': 'pointer'
            });

            // 确定取消按钮样式
            layero.find('.layui-layer-btn0').css({
                'background': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
                'border': 'none',
                'color': 'white',
                'box-shadow': '0 4px 15px rgba(255,107,107,0.4)'
            });

            // 取消按钮样式
            layero.find('.layui-layer-btn1').css({
                'background': 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                'border': '1px solid #dee2e6',
                'color': '#6c757d',
                'box-shadow': '0 2px 8px rgba(0,0,0,0.1)'
            });
        }
    }, function(index) {
        // 使用现代化弹窗输入取消原因
        var formHtml = '<div class="status-modal-container" style="padding: 30px;">' +
            '<div class="form-group-modern" style="margin-bottom: 0;">' +
            '<label class="form-label-modern" style="margin-bottom: 15px; font-size: 16px;">' +
            '<i class="fa fa-exclamation-triangle" style="margin-right: 10px; color: #f56565;"></i>' +
            '请输入取消原因' +
            '</label>' +
            '<textarea id="cancelReason" class="form-control-modern" rows="6" placeholder="请详细说明取消原因，以便我们改进服务质量..." style="resize: vertical; min-height: 120px; font-size: 14px; line-height: 1.5;"></textarea>' +
            '</div>' +
            '</div>';

        layer.open({
            type: 1,
            title: '<div style="text-align: center;"><i class="fa fa-ban"></i> 取消报名</div>',
            area: ['500px', '400px'],
            offset: 'auto',
            shade: 0.5,
            shadeClose: false,
            content: formHtml,
            btn: ['确定取消', '我再想想'],
            btnAlign: 'c',
            skin: 'modern-layer-skin',
            resize: false,
            success: function(layero, index) {
                // 确保弹窗居中显示
                var windowWidth = $(window).width();
                var windowHeight = $(window).height();
                var layerWidth = layero.outerWidth();
                var layerHeight = layero.outerHeight();

                // 计算居中位置
                var left = (windowWidth - layerWidth) / 2;
                var top = (windowHeight - layerHeight) / 2;

                // 添加现代化样式
                layero.css({
                    'border-radius': '16px',
                    'box-shadow': '0 20px 60px rgba(0,0,0,0.2)',
                    'border': 'none',
                    'position': 'fixed',
                    'left': left + 'px',
                    'top': top + 'px',
                    'transform': 'none'
                });

                // 标题栏样式 - 使用红色渐变表示危险操作
                layero.find('.layui-layer-title').css({
                    'background': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
                    'color': 'white',
                    'border-radius': '16px 16px 0 0',
                    'font-weight': '600',
                    'padding': '20px 25px',
                    'border': 'none',
                    'text-align': 'center',
                    'font-size': '18px',
                    'box-shadow': '0 2px 10px rgba(255,107,107,0.3)'
                });

                // 确保标题居中
                layero.find('.layui-layer-title').find('div').css({
                    'width': '100%',
                    'text-align': 'center',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'center',
                    'gap': '10px'
                });

                // 内容区域样式
                layero.find('.layui-layer-content').css({
                    'padding': '0',
                    'background': 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
                    'border-radius': '0',
                    'height': 'auto',
                    'overflow': 'visible'
                });

                // 按钮区域样式
                layero.find('.layui-layer-btn').css({
                    'padding': '20px 30px',
                    'text-align': 'center',
                    'background': 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
                    'border-radius': '0 0 16px 16px',
                    'border-top': '1px solid #e9ecef',
                    'position': 'relative'
                });

                // 按钮样式
                layero.find('.layui-layer-btn a').css({
                    'border-radius': '12px',
                    'font-weight': '600',
                    'padding': '14px 28px',
                    'margin': '0 10px',
                    'transition': 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    'min-width': '120px',
                    'font-size': '15px',
                    'line-height': '1.4',
                    'text-align': 'center',
                    'display': 'inline-block',
                    'box-sizing': 'border-box',
                    'cursor': 'pointer',
                    'position': 'relative',
                    'overflow': 'hidden'
                });

                // 确定取消按钮 - 红色危险样式
                layero.find('.layui-layer-btn0').css({
                    'background': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
                    'border': 'none',
                    'color': 'white',
                    'box-shadow': '0 4px 15px rgba(255,107,107,0.4)'
                });

                // 取消按钮样式
                layero.find('.layui-layer-btn1').css({
                    'background': 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                    'border': '1px solid #dee2e6',
                    'color': '#6c757d',
                    'box-shadow': '0 2px 8px rgba(0,0,0,0.1)'
                });

                // 关闭按钮样式
                layero.find('.layui-layer-close').css({
                    'color': 'white',
                    'font-size': '18px',
                    'font-weight': 'bold'
                });

                // 聚焦到文本框
                setTimeout(function() {
                    $('#cancelReason').focus();
                }, 100);
            },
            yes: function(promptIndex) {
                var reason = $('#cancelReason').val();
                if (!reason || reason.trim() === '') {
                    layer.msg('请输入取消原因');
                    return;
                }

                // 调用取消接口
                $.post('{:U("training/cancelOrder")}', {
                    order_id: orderId,
                    reason: reason.trim()
                }, function(res) {
                    if (res.status == 1) {
                        layer.close(promptIndex);
                        layer.close(index);
                        layer.msg('订单取消成功', {icon: 1, time: 650}, function() {
                            location.reload();
                        });
                    } else {
                        layer.msg(res.info || '取消失败');
                    }
                });
            }
        });
    });
}

// 培训订单沟通记录相关功能

// 切换沟通记录显示/隐藏
function toggleTrainingCommunication() {
    var $body = $('#trainingCommunicationBody');
    var $icon = $('#trainingCommunicationIcon');

    if ($body.is(':visible')) {
        // 隐藏沟通记录
        $body.slideUp(300);
        $icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
        $icon.css('transform', 'rotate(0deg)');
    } else {
        // 显示沟通记录
        $body.slideDown(300);
        $icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
        $icon.css('transform', 'rotate(180deg)');
    }
}

function toggleTrainingMessageContent(msgId) {
    var $content = $('#training-msg-content-' + msgId);
    var $btn = $content.next().find('.training-message-expand-btn');

    if ($content.hasClass('collapsed')) {
        $content.removeClass('collapsed');
        $btn.text('收起');
    } else {
        $content.addClass('collapsed');
        $btn.text('展开全文');
    }
}

// 显示快捷回复表单（无沟通记录时）
function showTrainingQuickReplyForm(userJobId) {
    // 首先展开沟通记录区域
    var $body = $('#trainingCommunicationBody');
    var $icon = $('#trainingCommunicationIcon');

    if (!$body.is(':visible')) {
        $body.slideDown(300);
        $icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
        $icon.css('transform', 'rotate(180deg)');
    }

    // 滚动到快捷回复区域
    setTimeout(function() {
        $('html, body').animate({
            scrollTop: $('.training-quick-reply-section').offset().top - 100
        }, 500);

        // 聚焦到输入框
        setTimeout(function() {
            $('#trainingQuickReplyContent').focus();
        }, 600);
    }, 350);
}

// 提交培训订单快捷回复
function submitTrainingQuickReply(userJobId) {
    var content = $('#trainingQuickReplyContent').val().trim();
    var needReply = $('#trainingNeedReply').is(':checked') ? '1' : '0';
    var sendWechat = $('#trainingSendWechat').is(':checked') ? '1' : '0';

    if (!content) {
        layer.msg('请输入回复内容', {icon: 2});
        $('#trainingQuickReplyContent').focus();
        return;
    }

    if (content.length > 1000) {
        layer.msg('回复内容不能超过1000个字符', {icon: 2});
        $('#trainingQuickReplyContent').focus();
        return;
    }

    // 显示加载状态
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });

    // 发送请求
    $.ajax({
        url: '{:U("Training/quickReply")}',
        type: 'POST',
        data: {
            user_job_id: userJobId,
            content: content,
            need_reply: needReply,
            send_wechat: sendWechat
        },
        timeout: 15000,
        success: function(response) {
            layer.close(loadingIndex);

            try {
                var result = typeof response === 'string' ? JSON.parse(response) : response;

                if (result.status === 1) {
                    layer.msg('回复发送成功', {icon: 1, time: 1500});

                    // 清空表单
                    $('#trainingQuickReplyContent').val('');
                    $('#trainingNeedReply').prop('checked', false);
                    $('#trainingSendWechat').prop('checked', false);

                    // 延迟刷新页面
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    var errorMsg = result.info || result.message || result.msg || '发送失败，请重试';
                    layer.msg(errorMsg, {icon: 2});
                }
            } catch (e) {
                layer.msg('响应数据格式错误', {icon: 2});
            }
        },
        error: function(xhr, status, error) {
            layer.close(loadingIndex);

            var errorMsg = '网络错误，请重试';
            if (status === 'timeout') {
                errorMsg = '请求超时，请重试';
            } else if (xhr.status === 404) {
                errorMsg = '接口不存在，请联系管理员';
            } else if (xhr.status === 500) {
                errorMsg = '服务器错误，请重试';
            }

            layer.msg(errorMsg, {icon: 2});
        }
    });
}

// 删除培训订单消息功能
function deleteTrainingMessage(messageId, userJobId) {
    console.log('准备删除培训订单消息:', messageId, userJobId);

    if (!messageId) {
        layer.msg('消息ID无效', {icon: 2});
        return;
    }

    // 确认删除
    layer.confirm('确定要删除这条消息吗？删除后无法恢复。', {
        icon: 3,
        title: '确认删除',
        btn: ['确定删除', '取消']
    }, function(index) {
        // 确定删除
        layer.close(index);

        // 显示加载状态
        var loadingIndex = layer.load(1, {
            shade: [0.3, '#000']
        });

        // 发送删除请求
        $.ajax({
            url: '{:U("Message/deleteMessage")}',
            type: 'POST',
            data: {
                message_id: messageId
            },
            timeout: 15000,
            success: function(response) {
                console.log('删除响应:', response);

                try {
                    // 尝试解析JSON响应
                    var result = typeof response === 'string' ? JSON.parse(response) : response;

                    if (result.status === 1 || result.success === true) {
                        layer.msg('消息删除成功', {icon: 1, time: 1500});

                        // 从页面中移除消息元素
                        var $messageItem = $('[data-message-id="' + messageId + '"]');
                        if ($messageItem.length > 0) {
                            $messageItem.fadeOut(300, function() {
                                $(this).remove();

                                // 检查是否还有其他消息
                                var $communicationList = $('.training-communication-list');
                                if ($communicationList.find('.training-message-item').length === 0) {
                                    // 如果没有消息了，刷新页面显示无消息状态
                                    setTimeout(function() {
                                        location.reload();
                                    }, 500);
                                }
                            });
                        }
                    } else {
                        var errorMsg = result.message || result.msg || result.info || '删除失败，请重试';
                        layer.msg(errorMsg, {icon: 2});
                    }
                } catch (e) {
                    // 如果不是JSON格式，检查响应文本
                    var responseText = response.toString();
                    if (responseText.includes('成功') || responseText.includes('success')) {
                        layer.msg('消息删除成功', {icon: 1, time: 1500});

                        // 从页面中移除消息元素
                        var $messageItem = $('[data-message-id="' + messageId + '"]');
                        if ($messageItem.length > 0) {
                            $messageItem.fadeOut(300, function() {
                                $(this).remove();
                            });
                        }
                    } else {
                        layer.msg('删除失败，请重试', {icon: 2});
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('删除请求失败:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });

                var errorMsg = '网络错误，请重试';
                if (status === 'timeout') {
                    errorMsg = '请求超时，请重试';
                } else if (xhr.status === 404) {
                    errorMsg = '删除接口不存在，请联系管理员';
                } else if (xhr.status === 500) {
                    errorMsg = '服务器错误，请重试';
                } else if (xhr.status === 403) {
                    errorMsg = '没有权限删除此消息';
                }

                layer.msg(errorMsg, {icon: 2});
            },
            complete: function() {
                layer.close(loadingIndex);
            }
        });
    }, function(index) {
        // 取消删除
        layer.close(index);
    });
}
</script>
