<?php
namespace Common\Model;
use Think\Model;

/**
 * 京东京灵平台灵工绑定模型
 */
class JdJinglingBindingModel extends Model {
    protected $tableName = 'jd_jingling_binding';
    
    // 状态定义
    public $status = [
        0 => '待审核',
        1 => '已通过',
        2 => '已拒绝'
    ];
    
    /**
     * 检查用户是否已绑定京东京灵平台灵工
     * @param int $userId 用户ID
     * @return array|bool 绑定记录或false
     */
    public function checkUserBinding($userId) {
        if (empty($userId)) {
            return false;
        }
        
        $binding = $this->where(['user_id' => $userId])->find();
        return $binding ? $binding : false;
    }
    
    /**
     * 检查服务站是否已绑定京东京灵平台灵工
     * @param int $stationId 服务站ID
     * @return array|bool 绑定记录或false
     */
    public function checkStationBinding($stationId) {
        if (empty($stationId)) {
            return false;
        }
        
        $binding = $this->where(['service_station_id' => $stationId])->find();
        return $binding ? $binding : false;
    }
    
    /**
     * 添加绑定信息
     * @param array $data 绑定数据
     * @return int|bool 新增ID或false
     */
    public function addBinding($data) {
        if (empty($data) || !is_array($data)) {
            return false;
        }
        
        // 设置默认状态和时间
        $data['status'] = isset($data['status']) ? $data['status'] : 0;
        $data['create_time'] = time();
        $data['update_time'] = time();
        
        return $this->add($data);
    }
    
    /**
     * 更新绑定信息
     * @param int $id 绑定记录ID
     * @param array $data 更新数据
     * @return bool 是否成功
     */
    public function updateBinding($id, $data) {
        if (empty($id) || empty($data) || !is_array($data)) {
            return false;
        }
        
        // 设置更新时间
        $data['update_time'] = time();
        
        return $this->where(['id' => $id])->save($data) !== false;
    }
    
    /**
     * 审核绑定信息
     * @param int $id 绑定记录ID
     * @param int $status 审核状态：1=通过，2=拒绝
     * @param string $remark 备注
     * @return bool 是否成功
     */
    public function reviewBinding($id, $status, $remark = '') {
        if (empty($id) || !in_array($status, [1, 2])) {
            return false;
        }
        
        $data = [
            'status' => $status,
            'update_time' => time()
        ];
        
        if (!empty($remark)) {
            $data['remark'] = $remark;
        }
        
        return $this->where(['id' => $id])->save($data) !== false;
    }
    
    /**
     * 生成绑定二维码
     * @param int $userId 用户ID
     * @return string 二维码URL
     */
    public function generateQrCode($userId) {
        // 实际项目中可能需要调用京东API获取二维码
        // 这里仅做示例，返回一个虚拟URL
        return '/qrcode/jd_binding/' . $userId . '?' . time();
    }
} 