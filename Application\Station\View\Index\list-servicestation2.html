<php>if ($list) { foreach($list as $v) {</php>
    <div class="list-row">
        <div class="list-item li1" style="display: flex;text-align: left;align-items: center;justify-content: flex-start;">
            {:$v['service_name']}<br>
        </div>

        <div class="list-item li2">
            {:$v['mobile'] ? str_replace(substr($v['mobile'],3,4),'****',$v['mobile']) : '--'}
        </div>
        <div class="list-item li2">
            {:$statusList[$v['status']]['text']}
        </div>
        <div class="list-item li3">
            <span style="display: flex;flex-direction: column;">
                    <a  href="{:U('index/succservicestation', ['id' => $v['id']])}"  style=" display: flex; align-items:center; justify-content:center;flex-direction:row;padding: 4px 8px; background: #669cfe; color: #fff; border-radius: 5px; font-size: 16px; ">审核成功</a><br>
                    <a  href="{:U('index/errservicestation', ['id' => $v['id']])}"  style=" display: flex; align-items:center; justify-content:center;flex-direction:row;padding: 4px 8px; background: #669cfe; color: #fff; border-radius: 5px; font-size: 16px; ">审核失败</a><br>

            </span>
        </div>
    </div>
<php>}}</php>