<?php
namespace Common\Model;

use Think\Model;

/**
 * 线程锁模型
 * 用于管理审核操作的线程锁
 */
class ThreadLockModel extends Model
{
    protected $_auto = [
        ['created_at', 'time', self::MODEL_INSERT, 'function'],
    ];

    /**
     * 获取线程锁
     * 
     * @param string $lockName 锁名称
     * @param int $expireSeconds 锁过期时间(秒)
     * @return boolean 是否成功获取锁
     */
    public function acquire($lockName, $expireSeconds = 60)
    {
        // 记录尝试获取锁的信息
        \Think\Log::write('[锁操作] 尝试获取锁: ' . $lockName . ', 当前时间: ' . date('Y-m-d H:i:s'), 'INFO');
        
        // 使用MySQL GET_LOCK函数获取命名锁，超时等待1秒
        $db = M();
        $result = $db->query("SELECT GET_LOCK('{$lockName}', 1) as lock_result");
        
        if (!$result || !isset($result[0]['lock_result']) || $result[0]['lock_result'] != 1) {
            // 获取锁失败
            \Think\Log::write('[锁操作] 获取MySQL锁失败: ' . $lockName, 'INFO');
            return false;
        }
        
        // 获取锁成功，记录锁信息到表中用于展示
        $now = time();
        $lock = $this->where(['lock_name' => $lockName])->find();
        
        // 添加测试延迟 - 5秒
        sleep(5);
        
        if ($lock) {
            // 锁信息已存在，更新
            $updateResult = $this->where(['id' => $lock['id']])->save([
                'lock_time' => $now,
                'expire_time' => $now + $expireSeconds
            ]);
            \Think\Log::write('[锁操作] 更新锁记录: ' . $lockName . ', 结果: ' . ($updateResult !== false ? '成功' : '失败'), 'INFO');
        } else {
            // 锁信息不存在，创建
            $data = [
                'lock_name' => $lockName,
                'lock_time' => $now,
                'expire_time' => $now + $expireSeconds
            ];
            $addResult = $this->add($data);
            \Think\Log::write('[锁操作] 创建锁记录: ' . $lockName . ', 结果: ' . ($addResult !== false ? '成功' : '失败'), 'INFO');
        }
        
        \Think\Log::write('[锁操作] MySQL锁获取成功: ' . $lockName, 'INFO');
        return true;
    }

    /**
     * 释放线程锁
     * 
     * @param string $lockName 锁名称
     * @return boolean 是否成功释放锁
     */
    public function release($lockName)
    {
        \Think\Log::write('[锁操作] 准备释放锁: ' . $lockName, 'INFO');
        
        // 从表中删除锁记录
        $this->where(['lock_name' => $lockName])->delete();
        
        // 释放MySQL锁
        $db = M();
        $result = $db->query("SELECT RELEASE_LOCK('{$lockName}') as release_result");
        $success = $result && isset($result[0]['release_result']) && $result[0]['release_result'] == 1;
        
        \Think\Log::write('[锁操作] MySQL锁释放' . ($success ? '成功' : '失败') . ': ' . $lockName, 'INFO');
        return $success;
    }

    /**
     * 检查锁是否存在且有效
     * 
     * @param string $lockName 锁名称
     * @return boolean 锁是否有效
     */
    public function check($lockName)
    {
        $lock = $this->where(['lock_name' => $lockName])->find();
        if (!$lock) {
            return false;
        }
        return $lock['expire_time'] > time();
    }

    /**
     * 清理过期的锁
     * 
     * @return int 清理的锁数量
     */
    public function clean()
    {
        $now = time();
        $result = $this->where(['expire_time' => ['lt', $now]])->delete();
        return $result;
    }

    /**
     * 为服务站审核操作创建锁名
     * 
     * @param int $stationId 服务站ID
     * @param string $action 操作类型 (review, payment, invoice, etc)
     * @return string 锁名称
     */
    public static function generateLockName($stationId, $action = 'review')
    {
        // 对于所有确认操作，使用相同的锁名前缀以确保互斥
        $lockName = "withdrawal_station_{$stationId}";
        \Think\Log::write("[锁名生成] 服务站ID: {$stationId}, 操作: {$action}, 生成锁名: {$lockName}", 'INFO');
        return $lockName;
    }
} 