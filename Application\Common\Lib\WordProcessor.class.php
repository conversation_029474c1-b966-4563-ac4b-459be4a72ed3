<?php
/**
 * Word文档处理类
 * 专门用于简历模板水印检测
 * 基于ZipArchive实现，无需额外依赖
 */

class WordProcessor {
    
    private $filePath;
    private $zipArchive;
    
    /**
     * 构造函数
     * @param string $filePath Word文档路径
     */
    public function __construct($filePath) {
        $this->filePath = $filePath;
        $this->zipArchive = new ZipArchive();
    }
    
    /**
     * 打开Word文档
     * @return bool
     */
    public function open() {
        if (!file_exists($this->filePath)) {
            return false;
        }
        
        $result = $this->zipArchive->open($this->filePath);
        return $result === TRUE;
    }
    
    /**
     * 关闭文档
     */
    public function close() {
        if ($this->zipArchive) {
            $this->zipArchive->close();
        }
    }
    
    /**
     * 读取文档内容
     * @return string|false
     */
    public function getDocumentContent() {
        $content = $this->zipArchive->getFromName('word/document.xml');
        if ($content === false) {
            return false;
        }
        
        // 解析XML并提取文本内容
        $dom = new DOMDocument();
        if (@$dom->loadXML($content)) {
            return $dom->textContent;
        }
        
        return $content;
    }
    
    /**
     * 读取文档自定义属性（元数据）
     * @return array
     */
    public function getCustomProperties() {
        $properties = [];

        // 读取自定义属性文件
        $customPropsContent = $this->zipArchive->getFromName('docProps/custom.xml');
        if ($customPropsContent !== false) {
            $dom = new DOMDocument();
            if (@$dom->loadXML($customPropsContent)) {
                $xpath = new DOMXPath($dom);
                // 注册命名空间
                $xpath->registerNamespace('cp', 'http://schemas.openxmlformats.org/officeDocument/2006/custom-properties');
                $xpath->registerNamespace('vt', 'http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes');

                // 使用正确的命名空间查询
                $propertyNodes = $xpath->query('//cp:property');

                foreach ($propertyNodes as $node) {
                    $name = $node->getAttribute('name');
                    // 获取vt:lpwstr元素的值
                    $vtNodes = $xpath->query('.//vt:lpwstr', $node);
                    if ($vtNodes->length > 0) {
                        $value = $vtNodes->item(0)->textContent;
                        $properties[$name] = $value;
                    }
                }
            }
        }

        return $properties;
    }
    
    /**
     * 添加自定义属性到文档
     * @param string $name 属性名
     * @param string $value 属性值
     * @return bool
     */
    public function addCustomProperty($name, $value) {
        // 读取现有的自定义属性
        $customPropsContent = $this->zipArchive->getFromName('docProps/custom.xml');
        
        if ($customPropsContent === false) {
            // 创建新的自定义属性文件
            $customPropsContent = $this->createCustomPropertiesXml();
        }
        
        $dom = new DOMDocument('1.0', 'UTF-8');
        $dom->formatOutput = true;
        
        if (@$dom->loadXML($customPropsContent)) {
            $root = $dom->documentElement;
            
            // 创建新的属性节点
            $property = $dom->createElement('property');
            $property->setAttribute('fmtid', '{D5CDD505-2E9C-101B-9397-08002B2CF9AE}');
            $property->setAttribute('pid', '2');
            $property->setAttribute('name', $name);
            
            $lpwstr = $dom->createElement('vt:lpwstr', htmlspecialchars($value));
            $property->appendChild($lpwstr);
            
            $root->appendChild($property);
            
            // 保存修改后的内容
            $newContent = $dom->saveXML();
            return $this->zipArchive->addFromString('docProps/custom.xml', $newContent);
        }
        
        return false;
    }
    
    /**
     * 创建自定义属性XML模板
     * @return string
     */
    private function createCustomPropertiesXml() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
</Properties>';
    }
    
    /**
     * 在文档内容中添加零宽度字符水印
     * @param string $watermark 水印文本
     * @return bool
     */
    public function addZeroWidthWatermark($watermark) {
        $documentContent = $this->zipArchive->getFromName('word/document.xml');
        if ($documentContent === false) {
            return false;
        }

        // 将水印文本编码为纯零宽度字符序列，不包含明文
        $encodedWatermark = $this->encodeToZeroWidth($watermark);

        $dom = new DOMDocument('1.0', 'UTF-8');
        $dom->preserveWhiteSpace = true; // 保持空白字符
        $dom->formatOutput = false; // 不格式化输出

        if (@$dom->loadXML($documentContent)) {
            // 注册命名空间
            $xpath = new DOMXPath($dom);
            $xpath->registerNamespace('w', 'http://schemas.openxmlformats.org/wordprocessingml/2006/main');

            // 查找文档中已有的文本节点
            $textNodes = $xpath->query('//w:t');

            if ($textNodes->length > 0) {
                // 分散式布局：在多个不同位置添加零宽字符
                $positions = $this->calculateWatermarkPositions($textNodes->length);
                $addedCount = 0;

                foreach ($positions as $position) {
                    if ($position < $textNodes->length) {
                        $targetNode = $textNodes->item($position);
                        $currentText = $targetNode->nodeValue;

                        // 在该位置添加零宽字符
                        $targetNode->nodeValue = $currentText . $encodedWatermark;
                        $addedCount++;
                    }
                }

                if ($addedCount > 0) {
                    // 保存修改后的内容
                    $newContent = $dom->saveXML();
                    return $this->zipArchive->addFromString('word/document.xml', $newContent);
                }
            }
        }

        return false;
    }
    
    /**
     * 添加元数据水印
     * @param string $watermark 水印文本
     * @return bool
     */
    public function addMetadataWatermark($watermark) {
        // 获取自定义属性文件
        $customPropsContent = $this->zipArchive->getFromName('docProps/custom.xml');

        $dom = new DOMDocument('1.0', 'UTF-8');
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput = true;

        if ($customPropsContent === false) {
            // 如果不存在自定义属性文件，创建一个新的
            $root = $dom->createElement('Properties');
            $root->setAttribute('xmlns', 'http://schemas.openxmlformats.org/officeDocument/2006/custom-properties');
            $root->setAttribute('xmlns:vt', 'http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes');
            $dom->appendChild($root);
        } else {
            // 加载现有文件
            if (!@$dom->loadXML($customPropsContent)) {
                return false;
            }
            $root = $dom->documentElement;
        }

        // 注册命名空间
        $xpath = new DOMXPath($dom);
        $xpath->registerNamespace('cp', 'http://schemas.openxmlformats.org/officeDocument/2006/custom-properties');
        $xpath->registerNamespace('vt', 'http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes');

        // 检查是否已存在该属性
        $existingProperty = $xpath->query('//cp:property[@name="ZCGK_TEMPLATE_WATERMARK"]');
        if ($existingProperty->length > 0) {
            // 更新现有属性
            $property = $existingProperty->item(0);
            $vtLpwstr = $xpath->query('.//vt:lpwstr', $property)->item(0);
            if ($vtLpwstr) {
                $vtLpwstr->nodeValue = $watermark;
            }
        } else {
            // 添加新属性
            $property = $dom->createElement('property');
            $property->setAttribute('fmtid', '{D5CDD505-2E9C-101B-9397-08002B2CF9AE}');
            $property->setAttribute('pid', '2');
            $property->setAttribute('name', 'ZCGK_TEMPLATE_WATERMARK');

            $vtLpwstr = $dom->createElement('vt:lpwstr', $watermark);
            $property->appendChild($vtLpwstr);

            $root->appendChild($property);
        }

        // 保存修改后的内容
        $newContent = $dom->saveXML();
        return $this->zipArchive->addFromString('docProps/custom.xml', $newContent);
    }

    /**
     * 计算零宽字符水印的分散位置
     * @param int $totalNodes 总文本节点数
     * @return array 位置索引数组
     */
    private function calculateWatermarkPositions($totalNodes) {
        $positions = [];

        if ($totalNodes <= 5) {
            // 节点数较少时，在每个节点都添加
            for ($i = 0; $i < $totalNodes; $i++) {
                $positions[] = $i;
            }
        } else {
            // 节点数较多时，选择5个分散的位置
            $positions[] = 0; // 第一个节点（文档开头）
            $positions[] = intval($totalNodes * 0.25); // 25%位置
            $positions[] = intval($totalNodes * 0.5);  // 50%位置（中间）
            $positions[] = intval($totalNodes * 0.75); // 75%位置
            $positions[] = $totalNodes - 1; // 最后一个节点（文档末尾）
        }

        // 去重并排序
        $positions = array_unique($positions);
        sort($positions);

        return $positions;
    }

    /**
     * 将文本编码为零宽度字符序列（简化版，避免布局问题）
     * @param string $text
     * @return string
     */
    private function encodeToZeroWidth($text) {
        // 使用简化的编码方式，避免生成过多字符影响布局
        // 只使用一个零宽度字符作为标识符
        return "\u{200B}"; // 单个零宽度空格作为水印标识
    }

    /**
     * 从零宽度字符序列解码文本（简化版）
     * @param string $encoded
     * @return string
     */
    private function decodeFromZeroWidth($encoded) {
        // 简化的解码方式，只检查是否包含零宽度字符
        if (strpos($encoded, "\u{200B}") !== false) {
            return 'ZCGK_TEMPLATE_WATERMARK'; // 返回标识字符串
        }
        return '';
    }
    
    /**
     * 检测零宽度字符水印
     * @param string $expectedWatermark 期望的水印文本
     * @return bool
     */
    public function detectZeroWidthWatermark($expectedWatermark) {
        $content = $this->getDocumentContent();
        if ($content === false) {
            return false;
        }

        // 检测特定的零宽度字符水印标识
        $zeroWidthPattern = '/\x{200B}/u'; // 只检测零宽度空格

        // 计算零宽度字符的数量，确保有足够的水印标识
        $matches = [];
        $count = preg_match_all($zeroWidthPattern, $content, $matches);

        // 至少需要检测到3个零宽度字符才认为是官方模板（分散式布局的最小要求）
        if ($count >= 3) {
            return true;
        }

        return false;
    }
    

    
    /**
     * 保存文档
     * @param string $newPath 新文件路径（可选）
     * @return bool
     */
    public function save($newPath = null) {
        if ($newPath === null) {
            $newPath = $this->filePath;
        }
        
        $this->close();
        
        // 如果是保存到新路径，复制文件
        if ($newPath !== $this->filePath) {
            return copy($this->filePath, $newPath);
        }
        
        return true;
    }
}
?>
