<?php
/**
 * 工具类控制器
 */

namespace Pub\Controller;

use Think\Controller;
class UtilController extends Controller
{

    public function _initialize() {

    }

    /**
     * 图片上传
     */
    public function upload()
    {
        if (IS_POST) {
            $options = unserialize(base64_decode(I('post.options')));
            $type = 1;
            if (isset($options['type'])) {
                $type = $options['type'];
            }

            $_file = $_FILES["file"] ? $_FILES["file"] : ($_FILES["imgFile"] ? $_FILES["imgFile"] : '');

            if($_file['tmp_name']) {
                do {
                    $result = uploadImg($_file, null, $type);
                    if(is_array($result)) {
                        $errmsg = $result['err'];
                        break;
                    }
                    $file = __ROOT__.C("UPLOADPATH").'/'.$result;
                    $res = [ 'error' => 0,
                        'message' => '',
                        'path' => $file,
                        'filename' => $file,
                        'url' => $file, 
                        ];
                } while (false);
                if (isset($errmsg)) {
                    $res = [ 'error' => 1, 'message' => $errmsg, ];
                }
                frameCallback(I("request.callback"), json_encode($res));
            }
        }
    }

    /**
     * 图片浏览
     */
    public function browser()
    {
        $type = I("request.type");
        if ($type == 'image') {
            $path = I("get.path");
            $currentpath = SITE_PATH.C("UPLOADPATH").'/';
            $browser = $year = $month = '';

            if (! empty($path)) {
                $path = trim($path, '/');
                $currentpath .= $path;
                $browser .= $path;
                $strs = explode('/', $path);
                if (isset($strs[0])) {
                    $year = $strs[0]; 
                }
                if (isset($strs[1])) {
                    $month = $strs[1]; 
                }
            }
            $exts = ['gif', 'jpg', 'jpeg', 'png', 'bmp'];
            $files = [];
            $files[] =[ 
                'filename' => '..',
                'is_dir' => true,
                'datetime' => date('Y-m-d H:i:s', filemtime($file)),
            ];
            
            if ($handle = opendir($currentpath)) {
                while (false !== ($filename = readdir($handle))) {
                    if($filename == '.' || $filename == '..') continue;
                    $file = $currentpath .'/'. $filename;
                    $file = str_replace('//', '/', $file);
                    if (is_dir($file)) {
                        $files[] = [
                            'filename' => $filename,
                            'is_dir' => true,
                            'datetime' => date('Y-m-d H:i:s', filemtime($file)),
                        ];
                    } else {
                        $fileext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                        $entry = array();
                        $entry['url'] = C("UPLOADPATH").'/'.$year.'/'.$month.'/'.$filename;
                        $entry['filename'] = C("UPLOADPATH").'/'.$year.'/'.$month.'/'.$filename;
                        if($option['global']){
                            $entry['url'] = 'images/global/'. $filename;
                            $entry['filename'] = 'images/global/'. $filename;
                        }
                        $entry['url'] = str_replace('//', '/', $entry['url']);
                        $entry['url'] = str_replace(':/', '://', $entry['url']);
                        $entry['filename'] = str_replace('//', '/', $entry['filename']);
                        $files[] = array(
                            'filename' => $filename,
                            'is_dir' => false,
                            'is_photo' => in_array($fileext, $exts),
                            'filesize' => filesize($file),
                            'filetype' => $fileext,
                            'url' => $entry['url'],
                            'attachment' => $entry['filename'],
                            'entry' => str_replace('"', '\'', json_encode($entry)),
                            'datetime' => date('Y-m-d H:i:s', filemtime($file)),
                        );
                    }
                }
            }
            $callback = I("request.callback");
            usort($files, 'file_compare');
            $this->assign("year", $year);
            $this->assign("month", $month);
            $this->assign("browser", $browser);
            $this->assign("callback", $callback);
            $this->assign("files", $files);
            $this->display();
        }
    
    }

    /**
     * 微信授权代理
     */
    public function proxyAuth()
    {
        $state = I('get.state','');
        if($state != '') {
            $uri = base64_decode($state);
            unset($_GET['state']);
            $query = http_build_query($_GET);
            $u = parse_url($uri);
            if($query != '') {
                if($u['query']) {
                    $u['query'] = $u['query'].'&'.$query;
                } else {
                    $u['query'] = $query;
                }
            }
            $url = $u['scheme'].'://'.$u['host'].$u['path'];
            if($u['query']) {
                $url .= '?'.$u['query'];
            }
            if($u['fragment']) {
                $url .= '#'.$u['fragment'];
            }
            redirect($url);
        }
    }

}
