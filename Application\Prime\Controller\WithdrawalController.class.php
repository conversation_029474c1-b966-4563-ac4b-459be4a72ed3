<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;
use Common\Model\ThreadLockModel;

/**
 * 后台提现管理
 */
class WithdrawalController extends PrimeController
{

    /**
     * 提现审核列表
     */
    public function index()
    {
        $withdrawalRequestModel = D("WithdrawalRequest");
        $where = [];

        // 搜索条件
        $serviceStationName = I('get.service_station_name');
        $status = I('get.status', 0, 'intval');
        $startTime = I('get.start_time');
        $endTime = I('get.end_time');

        if (!empty($serviceStationName)) {
            $stationIds = D('ServiceStation')->where(['service_name' => ['like', '%' . $serviceStationName . '%']])->getField('id', true);
            if ($stationIds) {
                $where['wr.service_station_id'] = ['in', $stationIds];
            } else {
                // 如果搜不到服务站，直接返回空结果
                 $where['wr.service_station_id'] = -1;
            }
        }
        if ($status > 0) {
            $where['wr.status'] = $status;
        }
        if (!empty($startTime)) {
            $where['wr.request_time'][] = ['egt', strtotime($startTime)];
        }
        if (!empty($endTime)) {
            $where['wr.request_time'][] = ['elt', strtotime($endTime . ' 23:59:59')];
        }

        $count = $withdrawalRequestModel->alias('wr')->where($where)->count();
        $page = $this->page($count, 20);

        $list = $withdrawalRequestModel->alias('wr')
            ->join('LEFT JOIN __SERVICE_STATION__ ss ON wr.service_station_id = ss.id')
            ->join('LEFT JOIN __BANK_CARD__ bc ON wr.bank_card_id = bc.id')
            ->join('LEFT JOIN __LINGGONG_BINDING__ lb ON wr.linggong_binding_id = lb.id')
            ->join('LEFT JOIN __USER__ u ON wr.user_id = u.id') // 假设用户表是 z_user
            ->join('LEFT JOIN __USERS__ admin ON wr.reviewer_id = admin.id') // 假设后台用户表是 z_users
            ->field('wr.*, ss.service_name, bc.bank_name, bc.card_number, bc.account_holder, ' .
                   'lb.bank_name as linggong_bank_name, lb.bank_account_last4 as linggong_last4, ' .
                   'lb.platform_type as linggong_platform_type, lb.worker_name as linggong_worker_name, ' .
                   'u.nickname as requester_name, admin.username as reviewer_name')
            ->where($where)
            ->limit($page->firstRow . ',' . $page->listRows)
            ->order('wr.id DESC') // 按ID降序排序
            ->select();

        // 数据处理，例如银行卡号脱敏
        if ($list) {
            foreach ($list as &$item) {
                // 获取LinggongBinding模型
                $linggongBindingModel = D("LinggongBinding");

                // 设置平台类型名称
                if ($item['platform_type'] == 'linggong' && !empty($item['linggong_platform_type'])) {
                    $item['platform_type_name'] = $linggongBindingModel->platformTypes[$item['linggong_platform_type']] ?? '未知平台';
                }

                // 银行卡或灵工账户信息处理
                if ($item['platform_type'] == 'linggong') {
                    // 灵工平台账户处理
                    $item['display_bank_name'] = $item['linggong_bank_name'] ?? 'N/A';
                    $item['display_card_number'] = $item['linggong_last4'] ? '**** ' . $item['linggong_last4'] : 'N/A';
                    $item['display_account_holder'] = $item['linggong_worker_name'] ?? 'N/A';
                } else {
                    // 银行卡处理
                    $item['display_bank_name'] = $item['bank_name'] ?? 'N/A';
                    if (!empty($item['card_number'])) {
                        $item['display_card_number'] = '**** ' . substr($item['card_number'], -4);
                    } else {
                        $item['display_card_number'] = 'N/A';
                    }
                    $item['display_account_holder'] = $item['account_holder'] ?? 'N/A';
                }
            }
            unset($item);
        }

        $this->assign('list', $list);
        $this->assign('statusList', $withdrawalRequestModel->status);
        $this->assign('page', $page->show());

        // 添加灵工平台类型数据
        $linggongBindingModel = D("LinggongBinding");
        $this->assign('linggongPlatformTypes', $linggongBindingModel->platformTypes);

        // 解决左侧菜单问题：手动设置当前菜单
        $menuModel = D("Menu");

        // 查找Withdrawal/index菜单项
        $menuItem = $menuModel->where(['app' => 'Prime', 'model' => 'Withdrawal', 'action' => 'index'])->find();
        \Think\Log::write('Searching for menu: app=Prime, model=Withdrawal, action=index. Result: '.($menuItem ? 'Found ID='.$menuItem['id'] : 'Not Found'), 'INFO');

        if ($menuItem) {
            // 手动构建完整的菜单信息
            $parentId = $menuItem['parentid'];
            $bootId = 0;

            \Think\Log::write('Menu item parent ID: '.$parentId, 'INFO');

            // 向上查找父级菜单，直到找到根菜单（parentid=0的菜单）
            if ($parentId > 0) {
                $bootId = $menuModel->getBoot($parentId);
                \Think\Log::write('Menu bootId for Withdrawal/index: '.$bootId, 'INFO');
            } else {
                $bootId = $menuItem['id']; // 如果当前就是顶级菜单
                \Think\Log::write('Menu item is top level, using its ID as bootId: '.$bootId, 'INFO');
            }

            // 构建完整的菜单信息
            $menuInfo = [
                'id' => $menuItem['id'],
                'parentid' => $parentId,
                'boot_id' => $bootId ? $bootId : $menuItem['id'] // 如果bootId为0，则使用当前菜单ID
            ];

            \Think\Log::write('Setting menu info: '.json_encode($menuInfo), 'INFO');

            // 重新分配菜单变量到模板
            $this->assign('cur_menu', $menuInfo);

            // 确保main_menus变量中包含这个boot_id
            $mainMenus = session('main_menus');
            if ($mainMenus && !isset($mainMenus[$menuInfo['boot_id']])) {
                \Think\Log::write('Warning: boot_id '.$menuInfo['boot_id'].' not found in main_menus session variable', 'WARN');
                // 获取菜单树并重新设置session
                $mainMenus = $menuModel->menu_json();
                session('main_menus', $mainMenus);
                \Think\Log::write('Refreshed main_menus in session', 'INFO');
            }
        } else {
            \Think\Log::write('Failed to find menu item for Withdrawal/index', 'WARN');
        }

        $this->display();
    }

    /**
     * 查看详情与审核
     */
    public function review()
    {
        $id = I('request.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误:缺少提现申请ID');
        }

        $withdrawalRequestModel = D("WithdrawalRequest");
        $requestInfo = $withdrawalRequestModel->alias('wr')
            ->join('LEFT JOIN __SERVICE_STATION__ ss ON wr.service_station_id = ss.id')
            ->join('LEFT JOIN __BANK_CARD__ bc ON wr.bank_card_id = bc.id')
            ->join('LEFT JOIN __LINGGONG_BINDING__ lb ON wr.linggong_binding_id = lb.id')
            ->join('LEFT JOIN __USER__ u ON wr.user_id = u.id') // 假设用户表是 z_user
            ->join('LEFT JOIN __USERS__ admin1 ON wr.reviewer_id = admin1.id') // 审核人信息
            ->join('LEFT JOIN __USERS__ admin2 ON wr.payer_id = admin2.id') // 打款操作人信息
            ->field('wr.*, ss.service_name, ss.price as station_available_price, ss.freeze_price as station_freeze_price, ss.total_price as station_total_price, '
                   . 'bc.bank_name, bc.card_number, bc.account_holder, bc.bank_branch, '
                   . 'u.nickname as requester_name, u.mobile as requester_mobile, '
                   . 'admin1.username as reviewer_name, admin2.username as payer_name, '
                   . 'lb.bank_name as linggong_bank_name, lb.bank_account_last4 as linggong_last4, lb.platform_type as linggong_platform_type, lb.worker_name as linggong_worker_name, lb.phone as linggong_phone, lb.id_card as linggong_id_card')
            ->where(['wr.id' => $id])
            ->find();

        if (!$requestInfo) {
            $this->error('未找到指定的提现申请记录');
        }

         // 银行卡脱敏
        if (!empty($requestInfo['card_number'])) {
            $requestInfo['card_number_display'] = $requestInfo['card_number']; // 审核页显示完整卡号
        }

        // 如果是需要开票的情况，获取额度信息
        if(isset($requestInfo['tax_handling']) && $requestInfo['tax_handling'] == 2) {
            $monthlyQuotaModel = D("MonthlyWithdrawalQuota");
            $stationId = $requestInfo['service_station_id'];

            // 获取申请时所在月份
            $requestMonth = date('Ym', $requestInfo['request_time']);

            // 获取额度使用情况
            $quota = $monthlyQuotaModel->getStationMonthlyQuota($stationId, $requestMonth);

            // 格式化为友好显示
            $quotaData = [
                'total_quota' => number_format($quota['total_quota'], 2),
                'used_amount' => number_format($quota['used_amount'], 2),
                'remaining_quota' => number_format($quota['remaining_quota'], 2),
                'year_month' => substr($requestMonth, 0, 4) . '年' . substr($requestMonth, 4, 2) . '月'
            ];

            $this->assign('quota_info', $quotaData);
        }

        // 实例化 LinggongBinding 模型并获取 platformTypes
        $linggongBindingModel = D("LinggongBinding");
        $this->assign('linggongPlatformTypes', $linggongBindingModel->platformTypes);

        // 获取服务站资金明细
        $stationId = $requestInfo['service_station_id'];
        if ($stationId) {
            $stationMoneyModel = D("StationMoney");
            $moneyDetails = $stationMoneyModel->where(['service_station_id' => $stationId])
                                               ->order('id desc')
                                               ->select();
            $stationMoneyTypes = $stationMoneyModel->type; // 获取类型映射
            $this->assign('moneyDetails', $moneyDetails);
            $this->assign('stationMoneyTypes', $stationMoneyTypes);
        } else {
            $this->assign('moneyDetails', []); // 确保变量存在
            $this->assign('stationMoneyTypes', []);
        }

        $this->assign('info', $requestInfo);
        $this->assign('statusList', $withdrawalRequestModel->status);
        $this->assign('taxHandlingList', $withdrawalRequestModel->tax_handling);

        // 解决左侧菜单问题：手动设置当前菜单
        $menuModel = D("Menu");

        // 查找Withdrawal/index菜单项
        $menuItem = $menuModel->where(['app' => 'Prime', 'model' => 'Withdrawal', 'action' => 'index'])->find();
        \Think\Log::write('Searching for menu: app=Prime, model=Withdrawal, action=index. Result: '.($menuItem ? 'Found ID='.$menuItem['id'] : 'Not Found'), 'INFO');

        if ($menuItem) {
            // 手动构建完整的菜单信息
            $parentId = $menuItem['parentid'];
            $bootId = 0;

            \Think\Log::write('Menu item parent ID: '.$parentId, 'INFO');

            // 向上查找父级菜单，直到找到根菜单（parentid=0的菜单）
            if ($parentId > 0) {
                $bootId = $menuModel->getBoot($parentId);
                \Think\Log::write('Menu bootId for Withdrawal/index: '.$bootId, 'INFO');
            } else {
                $bootId = $menuItem['id']; // 如果当前就是顶级菜单
                \Think\Log::write('Menu item is top level, using its ID as bootId: '.$bootId, 'INFO');
            }

            // 构建完整的菜单信息
            $menuInfo = [
                'id' => $menuItem['id'],
                'parentid' => $parentId,
                'boot_id' => $bootId ? $bootId : $menuItem['id'] // 如果bootId为0，则使用当前菜单ID
            ];

            \Think\Log::write('Setting menu info: '.json_encode($menuInfo), 'INFO');

            // 重新分配菜单变量到模板
            $this->assign('cur_menu', $menuInfo);

            // 确保main_menus变量中包含这个boot_id
            $mainMenus = session('main_menus');
            if ($mainMenus && !isset($mainMenus[$menuInfo['boot_id']])) {
                \Think\Log::write('Warning: boot_id '.$menuInfo['boot_id'].' not found in main_menus session variable', 'WARN');
                // 获取菜单树并重新设置session
                $mainMenus = $menuModel->menu_json();
                session('main_menus', $mainMenus);
                \Think\Log::write('Refreshed main_menus in session', 'INFO');
            }
        } else {
            \Think\Log::write('Failed to find menu item for Withdrawal/index', 'WARN');
        }

        $this->display(); // 显示审核页面视图
    }

    /**
     * 处理审核操作 (通过/驳回)
     */
     public function do_review() {
         if (IS_POST) {
             $id = I('post.id', 0, 'intval');
             $action = I('post.action'); // 'approve' or 'reject' or 'invoice'
             $review_remark = I('post.review_remark', '');

             if (!$id || !in_array($action, ['approve', 'reject', 'invoice'])) {
                 $this->error('参数错误');
             }
             if ($action == 'reject' && empty($review_remark)) {
                 $this->error('驳回申请必须填写备注说明');
             }

             $withdrawalRequestModel = D("WithdrawalRequest");
             $serviceStationModel = D("ServiceStation");
             $stationMoneyModel = D("StationMoney");

             // 先查询提现申请信息，获取服务站ID
             $requestInfo = $withdrawalRequestModel->find($id);
             if (!$requestInfo) {
                 $this->error('记录不存在');
             }

             // 创建并尝试获取线程锁
             $threadLockModel = D("Common/ThreadLock");
             $stationId = $requestInfo['service_station_id'];
             $lockName = ThreadLockModel::generateLockName($stationId, 'review');

             if (!$threadLockModel->acquire($lockName)) {
                 $this->error('当前有其他操作正在处理该服务站，请稍后再试');
             }

             try {
                 // 使用事务锁查询最新状态
                 $requestInfo = $withdrawalRequestModel->lock(true)->find($id);
                 if (!$requestInfo) {
                     throw new \Exception('记录不存在');
                 }
                 if ($requestInfo['status'] != 1) { // 1: 待审核
                     throw new \Exception('该申请已被处理，请勿重复操作');
                 }

                 $adminId = session('admin_id');
                 $updateData = [
                     'reviewer_id' => $adminId,
                     'review_time' => time(),
                     'review_remark' => $review_remark
                 ];

                 // 启动事务
                 $withdrawalRequestModel->startTrans();
                 try {
                     if ($action == 'approve') {
                         // 审核通过 -> 状态变为 待打款(2)
                         $updateData['status'] = 2;
                         // 记录申请月份
                         $updateData['request_month'] = date('Ym');
                         $result = $withdrawalRequestModel->where(['id' => $id])->save($updateData);
                         if ($result === false) {
                             throw new \Exception('更新提现申请状态失败');
                         }
                         // 注意：审核通过时，冻结金额暂时不释放
                     } else if ($action == 'invoice') {
                         // 需要开票 -> 状态变为 待开票(6)
                         $updateData['status'] = 6;
                         // 记录申请月份
                         $updateData['request_month'] = date('Ym');
                         $result = $withdrawalRequestModel->where(['id' => $id])->save($updateData);
                         if ($result === false) {
                             throw new \Exception('更新提现申请状态失败');
                         }
                         // 需要开票时，冻结金额暂时不释放，等待用户开票
                     } else { // reject
                         // 审核驳回 -> 状态变为 已驳回(3)
                         $updateData['status'] = 3;
                         $result = $withdrawalRequestModel->where(['id' => $id])->save($updateData);
                         if ($result === false) {
                             throw new \Exception('更新提现申请状态失败');
                         }

                         // 解冻服务站金额: 减少 freeze_price, 增加 price (可用余额)
                         $unfreezeResult = $serviceStationModel->where(['id' => $requestInfo['service_station_id']])
                                                             ->save([
                                                                'freeze_price' => ['exp', 'freeze_price - ' . $requestInfo['amount']],
                                                                'price' => ['exp', 'price + ' . $requestInfo['amount']]
                                                              ]);
                         if ($unfreezeResult === false) {
                             throw new \Exception('解冻服务站金额失败');
                         }

                         // 检查原始服务费是否为0，如果是，则返还免服务费额度
                         if (isset($requestInfo['service_fee']) && floatval($requestInfo['service_fee']) === 0.0) {
                             $refundLimitResult = $serviceStationModel->where(['id' => $requestInfo['service_station_id']])
                                                                      ->setInc('free_withdrawal_limit', $requestInfo['amount']);
                             if ($refundLimitResult === false) {
                                 // 记录错误，但通常不因此回滚，避免影响核心资金返还
                                 \Think\Log::write('返还服务站免服务费额度失败 (驳回): 服务站ID=' . $requestInfo['service_station_id'] . ', 金额=' . $requestInfo['amount'], 'ERR');
                                 // throw new \Exception('返还免服务费额度失败'); // 或者选择抛出异常
                             } else {
                                  \Think\Log::write('免服务费额度已返还 (驳回): 服务站ID=' . $requestInfo['service_station_id'] . ', 金额=' . $requestInfo['amount'], 'INFO');
                             }
                         }

                        // 添加一条资金返还记录到StationMoney表
                        $returnMoneyData = [
                            'service_station_id' => $requestInfo['service_station_id'],
                            'type' => 4, // 新类型：资金返还
                            'money' => $requestInfo['amount'], // 返还金额，与提现金额相同
                            'withdrawal_request_id' => $id, // 关联提现申请ID
                            'create_time' => time(),
                            'remark' => '提现申请驳回返还：' . $review_remark, // 添加备注说明返还来源
                        ];
                        $returnMoneyLogId = $stationMoneyModel->add($returnMoneyData);
                        if (!$returnMoneyLogId) {
                            // 记录详细错误信息以便调试
                            \Think\Log::write('添加资金返还记录失败: ' . $stationMoneyModel->getError() . ', SQL: ' . $stationMoneyModel->getLastSql(), 'ERR');
                            throw new \Exception('添加资金返还记录失败: ' . $stationMoneyModel->getError());
                        }

                        // 返还提现额度
                        $quotaModel = D("MonthlyWithdrawalQuota");
                        $requestMonth = $requestInfo['request_month'] ?: date('Ym');
                        // 全部平台完税模式
                        if (isset($requestInfo['tax_handling']) && $requestInfo['tax_handling'] == 1) {
                            $updatedQuota = $quotaModel->refundQuotaUsage($requestInfo['service_station_id'], $requestInfo['amount'], $requestMonth);
                            if (!$updatedQuota) {
                                \Think\Log::write('返还提现额度失败: 服务站ID=' . $requestInfo['service_station_id'] . ', 金额=' . $requestInfo['amount'], 'ERR');
                                throw new \Exception('返还提现额度失败');
                            }

                            // 记录额度返还日志
                            \Think\Log::write('提现额度已返还: 服务站ID=' . $requestInfo['service_station_id'] .
                                            ', 金额=' . $requestInfo['amount'] .
                                            ', 当前已用额度=' . $updatedQuota['used_amount'] .
                                            ', 剩余额度=' . $updatedQuota['remaining_quota'], 'INFO');
                        }
                        // 混合处理模式
                        else if (isset($requestInfo['tax_handling']) && $requestInfo['tax_handling'] == 3 && isset($requestInfo['platform_tax_amount']) && $requestInfo['platform_tax_amount'] > 0) {
                            $updatedQuota = $quotaModel->refundQuotaUsage($requestInfo['service_station_id'], $requestInfo['platform_tax_amount'], $requestMonth);
                            if (!$updatedQuota) {
                                \Think\Log::write('返还混合处理模式平台完税额度失败: 服务站ID=' . $requestInfo['service_station_id'] . ', 金额=' . $requestInfo['platform_tax_amount'], 'ERR');
                                throw new \Exception('返还混合处理模式平台完税额度失败');
                            }

                            // 记录额度返还日志
                            \Think\Log::write('混合处理模式平台完税额度已返还: 服务站ID=' . $requestInfo['service_station_id'] .
                                            ', 平台完税金额=' . $requestInfo['platform_tax_amount'] .
                                            ', 当前已用额度=' . $updatedQuota['used_amount'] .
                                            ', 剩余额度=' . $updatedQuota['remaining_quota'], 'INFO');
                        }

                        // (可选) 更新关联的 StationMoney 记录状态为失败
                        if ($requestInfo['withdrawal_request_id']) {
                              // 旧代码存在问题，表中不存在status和remark字段
                              // $stationMoneyModel->where(['withdrawal_request_id' => $id, 'type'=>1])
                              //                 ->save(['status' => 5, 'remark' => '提现申请驳回: ' . $review_remark]); // 5: 失败状态

                              // 记录日志，以便后期可以手动查看
                              \Think\Log::write('提现申请驳回: ID=' . $id . ', 原因=' . $review_remark, 'INFO');
                        }
                     }

                     // 提交事务
                     $withdrawalRequestModel->commit();
                     // 释放线程锁
                     $threadLockModel->release($lockName);
                     $this->success('审核操作成功', U('index', ['id' => $id]));

                 } catch (\Exception $e) {
                     $withdrawalRequestModel->rollback();
                     // 释放线程锁
                     $threadLockModel->release($lockName);
                     $this->error('操作失败: ' . $e->getMessage());
                 }
             } catch (\Exception $e) {
                 // 释放线程锁
                 $threadLockModel->release($lockName);
                 $this->error($e->getMessage());
             }
         } else {
             $this->error('非法请求');
         }
     }

    /**
     * 处理打款操作 (成功/失败)
     */
    public function do_payment() {
         if (IS_POST) {
             $id = I('post.id', 0, 'intval');
             $action = I('post.action'); // 'paid' or 'failed'
             $payment_remark = I('post.payment_remark', ''); // 打款流水号或失败原因

             if (!$id || !in_array($action, ['paid', 'failed'])) {
                 $this->error('参数错误');
             }
              if ($action == 'failed' && empty($payment_remark)) {
                  $this->error('标记打款失败必须填写备注说明');
              }

             $withdrawalRequestModel = D("WithdrawalRequest");
             $serviceStationModel = D("ServiceStation");
             $stationMoneyModel = D("StationMoney");

             // 先查询提现申请信息，获取服务站ID
             $requestInfo = $withdrawalRequestModel->find($id);
             if (!$requestInfo) {
                 $this->error('记录不存在');
             }

             // 创建并尝试获取线程锁
             $threadLockModel = D("Common/ThreadLock");
             $stationId = $requestInfo['service_station_id'];
             $lockName = ThreadLockModel::generateLockName($stationId, 'payment');

             if (!$threadLockModel->acquire($lockName)) {
                 $this->error('当前有其他操作正在处理该服务站，请稍后再试');
             }

             try {
                 // 使用事务锁查询最新状态
                 $requestInfo = $withdrawalRequestModel->lock(true)->find($id);
                 if (!$requestInfo) {
                     throw new \Exception('记录不存在');
                 }
                 if ($requestInfo['status'] != 2) { // 2: 待打款
                     throw new \Exception('该申请状态不是待打款，无法操作');
                 }

                 $adminId = session('admin_id');
                 $updateData = [
                     'payment_time' => time(),
                     'payment_remark' => $payment_remark,
                     'payer_id' => $adminId // 添加打款操作人ID
                 ];

                 // 启动事务
                 $withdrawalRequestModel->startTrans();
                 try {
                     if ($action == 'paid') {
                         // 打款成功 -> 状态变为 打款完成(4)
                         $updateData['status'] = 4;
                         $result = $withdrawalRequestModel->where(['id' => $id])->save($updateData);
                         if ($result === false) {
                             throw new \Exception('更新提现申请状态失败');
                         }

                         // 更新服务站金额：扣除总额，解冻金额 (可用余额 price 在申请时已扣除)
                         $updateStationResult = $serviceStationModel->where(['id' => $requestInfo['service_station_id']])
                                                                   ->save([
                                                                       'total_price' => ['exp', 'total_price - ' . $requestInfo['amount']],
                                                                       'freeze_price' => ['exp', 'freeze_price - ' . $requestInfo['amount']]
                                                                   ]);
                        if ($updateStationResult === false) {
                            throw new \Exception('更新服务站余额失败');
                        }
                         // (可选) 更新关联的 StationMoney 记录状态为成功
                         if ($requestInfo['withdrawal_request_id']) {
                              // 旧代码存在问题，表中不存在status字段
                              // $stationMoneyModel->where(['withdrawal_request_id' => $id, 'type'=>1])
                              //                 ->save(['status' => 4]); // 4: 成功状态

                              // 记录日志，以便后期可以手动查看
                              \Think\Log::write('提现申请成功完成: ID=' . $id, 'INFO');
                         }

                     } else { // failed
                          // 打款失败 -> 状态变为 打款失败(5)
                         $updateData['status'] = 5;
                         $result = $withdrawalRequestModel->where(['id' => $id])->save($updateData);
                         if ($result === false) {
                             throw new \Exception('更新提现申请状态失败');
                         }
                         // 解冻服务站金额: 减少 freeze_price, 增加 price (可用余额)
                         $unfreezeResult = $serviceStationModel->where(['id' => $requestInfo['service_station_id']])
                                                             ->save([
                                                                'freeze_price' => ['exp', 'freeze_price - ' . $requestInfo['amount']],
                                                                'price' => ['exp', 'price + ' . $requestInfo['amount']]
                                                              ]);
                        if ($unfreezeResult === false) {
                            throw new \Exception('解冻服务站金额失败');
                        }

                        // 检查原始服务费是否为0，如果是，则返还免服务费额度
                        if (isset($requestInfo['service_fee']) && floatval($requestInfo['service_fee']) === 0.0) {
                            $refundLimitResult = $serviceStationModel->where(['id' => $requestInfo['service_station_id']])
                                                                     ->setInc('free_withdrawal_limit', $requestInfo['amount']);
                            if ($refundLimitResult === false) {
                                // 记录错误，但通常不因此回滚
                                \Think\Log::write('返还服务站免服务费额度失败 (打款失败): 服务站ID=' . $requestInfo['service_station_id'] . ', 金额=' . $requestInfo['amount'], 'ERR');
                                // throw new \Exception('返还免服务费额度失败');
                            } else {
                                \Think\Log::write('免服务费额度已返还 (打款失败): 服务站ID=' . $requestInfo['service_station_id'] . ', 金额=' . $requestInfo['amount'], 'INFO');
                            }
                        }

                        // 添加一条资金返还记录到StationMoney表
                        $returnMoneyData = [
                            'service_station_id' => $requestInfo['service_station_id'],
                            'type' => 4, // 新类型：资金返还
                            'money' => $requestInfo['amount'], // 返还金额，与提现金额相同
                            'withdrawal_request_id' => $id, // 关联提现申请ID
                            'create_time' => time(),
                            'remark' => '提现打款失败返还：' . $payment_remark, // 添加备注说明返还来源
                        ];
                        $returnMoneyLogId = $stationMoneyModel->add($returnMoneyData);
                        if (!$returnMoneyLogId) {
                            // 记录详细错误信息以便调试
                            \Think\Log::write('添加资金返还记录失败: ' . $stationMoneyModel->getError() . ', SQL: ' . $stationMoneyModel->getLastSql(), 'ERR');
                            throw new \Exception('添加资金返还记录失败: ' . $stationMoneyModel->getError());
                        }

                        // 返还提现额度（如果有使用平台完税额度）
                        if (isset($requestInfo['tax_handling']) && $requestInfo['tax_handling'] == 1) {
                            // 返还提现额度
                            $quotaModel = D("MonthlyWithdrawalQuota");
                            $requestMonth = $requestInfo['request_month'] ?: date('Ym');
                            $updatedQuota = $quotaModel->refundQuotaUsage($requestInfo['service_station_id'], $requestInfo['amount'], $requestMonth);
                            if (!$updatedQuota) {
                                \Think\Log::write('返还提现额度失败: 服务站ID=' . $requestInfo['service_station_id'] . ', 金额=' . $requestInfo['amount'], 'ERR');
                                throw new \Exception('返还提现额度失败');
                            }

                            // 记录额度返还日志
                            \Think\Log::write('提现额度已返还: 服务站ID=' . $requestInfo['service_station_id'] .
                                            ', 金额=' . $requestInfo['amount'] .
                                            ', 当前已用额度=' . $updatedQuota['used_amount'] .
                                            ', 剩余额度=' . $updatedQuota['remaining_quota'], 'INFO');
                        }
                        // 混合处理模式下返还平台完税部分额度
                        else if (isset($requestInfo['tax_handling']) && $requestInfo['tax_handling'] == 3 && isset($requestInfo['platform_tax_amount']) && $requestInfo['platform_tax_amount'] > 0) {
                            // 返还平台完税部分额度
                            $quotaModel = D("MonthlyWithdrawalQuota");
                            $requestMonth = $requestInfo['request_month'] ?: date('Ym');
                            $updatedQuota = $quotaModel->refundQuotaUsage($requestInfo['service_station_id'], $requestInfo['platform_tax_amount'], $requestMonth);
                            if (!$updatedQuota) {
                                \Think\Log::write('返还混合处理模式平台完税额度失败: 服务站ID=' . $requestInfo['service_station_id'] . ', 金额=' . $requestInfo['platform_tax_amount'], 'ERR');
                                throw new \Exception('返还混合处理模式平台完税额度失败');
                            }

                            // 记录额度返还日志
                            \Think\Log::write('混合处理模式平台完税额度已返还: 服务站ID=' . $requestInfo['service_station_id'] .
                                            ', 平台完税金额=' . $requestInfo['platform_tax_amount'] .
                                            ', 当前已用额度=' . $updatedQuota['used_amount'] .
                                            ', 剩余额度=' . $updatedQuota['remaining_quota'], 'INFO');
                        }

                          // (可选) 更新关联的 StationMoney 记录状态为失败
                         if ($requestInfo['withdrawal_request_id']) {
                              // 旧代码存在问题，表中不存在status和remark字段
                              // $stationMoneyModel->where(['withdrawal_request_id' => $id, 'type'=>1])
                              //                 ->save(['status' => 5, 'remark' => '打款失败: ' . $payment_remark]); // 5: 失败状态

                              // 记录日志，以便后期可以手动查看
                              \Think\Log::write('打款失败: ID=' . $id . ', 原因=' . $payment_remark, 'INFO');
                         }
                     }

                     // 提交事务
                     $withdrawalRequestModel->commit();
                     // 释放线程锁
                     $threadLockModel->release($lockName);
                     $this->success('打款操作标记成功', U('review', ['id' => $id]));

                 } catch (\Exception $e) {
                     $withdrawalRequestModel->rollback();
                     // 释放线程锁
                     $threadLockModel->release($lockName);
                     $this->error('操作失败: ' . $e->getMessage());
                 }
             } catch (\Exception $e) {
                 // 释放线程锁
                 $threadLockModel->release($lockName);
                 $this->error($e->getMessage());
             }
         } else {
             $this->error('非法请求');
         }
    }

    /**
     * 处理开票操作 (已收到发票/取消开票)
     */
    public function do_invoice() {
         if (IS_POST) {
             $id = I('post.id', 0, 'intval');
             $action = I('post.action'); // 'received' or 'cancel'
             $invoice_remark = I('post.invoice_remark', '');

             if (!$id || !in_array($action, ['received', 'cancel'])) {
                 $this->error('参数错误');
             }

             $withdrawalRequestModel = D("WithdrawalRequest");
             $serviceStationModel = D("ServiceStation");

             // 先查询提现申请信息，获取服务站ID
             $requestInfo = $withdrawalRequestModel->find($id);
             if (!$requestInfo) {
                 $this->error('记录不存在');
             }

             // 创建并尝试获取线程锁
             $threadLockModel = D("Common/ThreadLock");
             $stationId = $requestInfo['service_station_id'];
             $lockName = ThreadLockModel::generateLockName($stationId, 'invoice');

             if (!$threadLockModel->acquire($lockName)) {
                 $this->error('当前有其他操作正在处理该服务站，请稍后再试');
             }

             try {
                 // 使用事务锁查询最新状态
                 $requestInfo = $withdrawalRequestModel->lock(true)->find($id);
                 if (!$requestInfo) {
                     throw new \Exception('记录不存在');
                 }
                 if ($requestInfo['status'] != 6) { // 6: 待开票
                     throw new \Exception('该申请状态不是待开票，无法操作');
                 }

                 $adminId = session('admin_id');
                 $updateData = [
                     'invoice_operator_id' => $adminId,
                     'invoice_time' => time(),
                     'invoice_remark' => $invoice_remark
                 ];

                 // 启动事务
                 $withdrawalRequestModel->startTrans();
                 try {
                     if ($action == 'received') {
                         // 已收到发票 -> 状态变为 待打款(2)
                         $updateData['status'] = 2;
                         $result = $withdrawalRequestModel->where(['id' => $id])->save($updateData);
                         if ($result === false) {
                             throw new \Exception('更新提现申请状态失败');
                         }
                         // 收到发票后，状态转为待打款，资金状态不变
                     } else { // cancel
                          // 取消开票 -> 状态变为 待审核(1)，返回原始审核流程
                         $updateData['status'] = 1;
                         $result = $withdrawalRequestModel->where(['id' => $id])->save($updateData);
                         if ($result === false) {
                             throw new \Exception('更新提现申请状态失败');
                         }

                         // 记录取消开票操作，状态回到待审核，资金保持冻结状态
                         // 注释掉原来的解冻资金代码，因为返回待审核状态资金仍需冻结
                         /*
                         $unfreezeResult = $serviceStationModel->where(['id' => $requestInfo['service_station_id']])
                                                             ->save([
                                                                'freeze_price' => ['exp', 'freeze_price - ' . $requestInfo['amount']],
                                                                'price' => ['exp', 'price + ' . $requestInfo['amount']]
                                                              ]);
                        if ($unfreezeResult === false) {
                            throw new \Exception('解冻服务站金额失败');
                        }

                         // 添加一条资金返还记录到StationMoney表
                         $stationMoneyModel = D("StationMoney");
                         $returnMoneyData = [
                            'service_station_id' => $requestInfo['service_station_id'],
                            'type' => 4, // 新类型：资金返还
                            'money' => $requestInfo['amount'], // 返还金额，与提现金额相同
                            'withdrawal_request_id' => $id, // 关联提现申请ID
                            'create_time' => time(),
                         ];
                         $returnMoneyLogId = $stationMoneyModel->add($returnMoneyData);
                         if (!$returnMoneyLogId) {
                             // 记录详细错误信息以便调试
                             \Think\Log::write('添加资金返还记录失败: ' . $stationMoneyModel->getError() . ', SQL: ' . $stationMoneyModel->getLastSql(), 'ERR');
                             throw new \Exception('添加资金返还记录失败: ' . $stationMoneyModel->getError());
                         }
                         */

                          // 记录日志
                          \Think\Log::write('取消开票要求: ID=' . $id . ', 原因=' . $invoice_remark . '，状态返回待审核', 'INFO');

                          // 注意：此处不需要返还额度，因为状态仅返回待审核，而非驳回或打款失败，
                          // 在待审核状态下仍需保留占用的额度，以便后续的审核处理
                     }

                     // 提交事务
                     $withdrawalRequestModel->commit();
                     // 释放线程锁
                     $threadLockModel->release($lockName);
                     $this->success('开票操作标记成功', U('review', ['id' => $id]));

                 } catch (\Exception $e) {
                     $withdrawalRequestModel->rollback();
                     // 释放线程锁
                     $threadLockModel->release($lockName);
                     $this->error('操作失败: ' . $e->getMessage());
                 }
             } catch (\Exception $e) {
                 // 释放线程锁
                 $threadLockModel->release($lockName);
                 $this->error($e->getMessage());
             }
         } else {
             $this->error('非法请求');
         }
    }
}