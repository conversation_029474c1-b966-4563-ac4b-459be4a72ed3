﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>关注公众号</title>
    <style>
        * {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
        }

        .container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            box-sizing: border-box;
        }

        .qrcode-card {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(20px, 5vw, 40px);
            box-shadow: 0 6px 30px rgba(0, 0, 0, 0.08);
            text-align: center;
            width: min(90%, 400px);
            margin: 20px;
            position: relative;
        }
        .qrcode-card:active {
            transform: scale(0.98);
            transition: transform 0.1s;
        }
        .qrcode-img {
            width: 100%;
            aspect-ratio: 1/1;
            margin: 0 auto;
            position: relative;
        }

        .qrcode-img img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .instruction {
            color: #666;
            font-size: clamp(14px, 3.5vw, 16px);
            margin: 25px 0 15px;
            line-height: 1.6;
            padding: 0 10px;
        }

        .tips {
            color: #999;
            font-size: clamp(12px, 3vw, 14px);
            margin-top: 30px;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.9; }
            50% { opacity: 0.6; }
            100% { opacity: 0.9; }
        }

        .wechat-footer {
            color: #999;
            font-size: 12px;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="qrcode-card">
            <div class="qrcode-img">
                <!-- 替换为你的二维码URL -->
                <img width="400" src="https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket={:$tempQrcodeRow['ticket']}" />
            </div>
            
            <div class="instruction">
                ▲ 长按识别图中二维码 ▲<br>
                立即关注“中才国科就业服务站”公众号
            </div>

            <div class="tips">
                （关注后按提示填写简历）
            </div>
        </div>
    </div>

    <div class="wechat-footer">
        
    </div>
</body>
<!-- 在微信中隐藏顶部导航栏 -->
<script>
    document.addEventListener('WeixinJSBridgeReady', function() {
        WeixinJSBridge.call('hideToolbar');
    });
</script>
</html>