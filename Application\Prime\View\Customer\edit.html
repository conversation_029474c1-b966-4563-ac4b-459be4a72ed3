<include file="block/hat"/>
<script type="text/javascript" src="__ROOT__/static/js/lib/jquery-ui-1.10.3.min.js"></script>
<div class="container-fluid">
    <div class="row">
        <include file="block/menu"/>
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li><a href="{:U('customer/index')}">客服列表</a></li>
                <li class="active"><a href="{:U('customer/edit')}">添加客服</a></li>
            </ul>
            <style type='text/css'>.tab-pane {
                padding: 20px 0 20px 0;
            }</style>
            <div class="main">
                <form action="" method="post" class="form-horizontal form js-ajax-form" enctype="multipart/form-data"
                      id="form1">
                    <div class="panel panel-default">
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                        style="color:red">*</span>昵称</label>
                                <div class="col-sm-9 col-xs-12">
                                    <input type="text" name="name" class="form-control sp_input" value="{$row.name}"/>
                                    <span class="help-block sp_span">*</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-xs-12 col-sm-3 col-md-2 control-label">微信二维码</label>
                                <div class="col-sm-9 col-xs-12">
                                    <php>echo tpl_form_field_image('qrcode', $row['qrcode'], '', ['type'=>4,'extras' => ['text' => 'readonly']])</php>
                                    <span class="help-block">微信二维码</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group col-sm-12">
                        <input type="hidden" name="id" value="{$row.id}"/>
                        <input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1 js-ajax-submit"/>
                    </div>
                </form>
            </div>

        </div>
    </div>
</div>
<script src="__ROOT__/static/js/prime/common.js"></script>
<link rel="stylesheet" href="__ROOT__/static/js/app/layer/skin/layer.css"/>
<script>

    //function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}
    require(['layer']);
    //require(['layer'], function(layer) {
    //_alert(123);
    //layer.alert(123);
    //layer.alert(123)
    //});


    $(function () {
        //function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}

        //layer.config({
        //extend: 'extend/layer.ext.js'
        //});
        //_alert(123)
    })

</script>
<include file="block/footer"/>