<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <!-- 页面数据变量 -->
            <script type="text/javascript">
                // 从后端获取的数据变量
                window.momentPageData = {
                    totalCount: 0, // 将通过分页信息动态获取
                    currentPage: 1,
                    pageSize: parseInt('{$pageSize}') || 10,
                    totalPages: 1
                };

                // 动态获取分页信息的函数
                window.extractPaginationInfo = function() {
                    try {
                        var currentPageCards = $('.moment-card').length;

                        // 方法1: 尝试从分页组件的HTML中提取信息
                        var paginationContainer = $('.pagination').closest('div');
                        var paginationText = paginationContainer.text() || '';

                        console.log('分页容器文本:', paginationText);

                        // 查找类似 "共 123 条记录" 或 "总共 123 条" 的文本
                        var totalMatch = paginationText.match(/(?:共|总共)\s*(\d+)\s*(?:条|个|项)/);
                        if (totalMatch) {
                            window.momentPageData.totalCount = parseInt(totalMatch[1]);
                            console.log('从分页文本提取总数:', totalMatch[1]);
                        }

                        // 方法2: 尝试从URL参数获取页码信息
                        var urlParams = new URLSearchParams(window.location.search);
                        var pageParam = urlParams.get('p') || urlParams.get('page') || '1';
                        window.momentPageData.currentPage = parseInt(pageParam) || 1;

                        // 方法3: 从分页链接中获取信息
                        var paginationLinks = $('.pagination a');
                        if (paginationLinks.length > 0) {
                            var maxPage = 1;
                            paginationLinks.each(function() {
                                var href = $(this).attr('href') || '';
                                var pageMatch = href.match(/[?&]p=(\d+)/);
                                if (pageMatch) {
                                    maxPage = Math.max(maxPage, parseInt(pageMatch[1]));
                                }
                            });
                            window.momentPageData.totalPages = maxPage;
                            console.log('从分页链接提取最大页数:', maxPage);
                        }

                        // 方法4: 如果还是没有总数，根据分页情况估算
                        if (window.momentPageData.totalCount === 0 && currentPageCards > 0) {
                            // 检查是否有下一页
                            var hasNextPage = $('.pagination a').filter(function() {
                                return $(this).text().indexOf('下一页') >= 0 || $(this).text().indexOf('»') >= 0;
                            }).length > 0;

                            // 检查是否有上一页
                            var hasPrevPage = $('.pagination a').filter(function() {
                                return $(this).text().indexOf('上一页') >= 0 || $(this).text().indexOf('«') >= 0;
                            }).length > 0;

                            if (hasNextPage || hasPrevPage || window.momentPageData.totalPages > 1) {
                                // 有多页数据，估算总数
                                var estimatedTotal = window.momentPageData.totalPages * window.momentPageData.pageSize;
                                window.momentPageData.totalCount = estimatedTotal;
                                console.log('估算总数:', estimatedTotal);
                            } else {
                                // 只有一页数据
                                window.momentPageData.totalCount = currentPageCards;
                                console.log('单页数据总数:', currentPageCards);
                            }
                        }

                        // 方法5: 最后的fallback - 使用当前页卡片数
                        if (window.momentPageData.totalCount === 0) {
                            window.momentPageData.totalCount = currentPageCards;
                            console.log('使用当前页卡片数作为总数:', currentPageCards);
                        }

                        console.log('最终提取的分页信息:', window.momentPageData);
                    } catch (e) {
                        console.log('提取分页信息失败:', e);
                        // 使用当前页面卡片数量作为fallback
                        window.momentPageData.totalCount = $('.moment-card').length;
                    }
                };
            </script>

            <style type='text/css'>
                /* 现代化素材管理页面样式 */
                .moment-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .moment-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .moment-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .moment-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #06b6d4 0%, #0891b2 50%, #0e7490 100%);
                }

                .moment-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .moment-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .moment-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .moment-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .moment-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .moment-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .moment-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .moment-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(6, 182, 212, 0.3);
                    text-decoration: none;
                }

                .moment-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(6, 182, 212, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .moment-index-batch-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.3);
                }

                .moment-index-batch-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(239, 68, 68, 0.4);
                }

                /* 现代化导航标签 */
                .moment-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .moment-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .moment-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .moment-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .moment-index-nav-link:hover {
                    color: #06b6d4;
                    background: rgba(6, 182, 212, 0.05);
                    text-decoration: none;
                }

                .moment-index-nav-link.active {
                    color: #06b6d4 !important;
                    background: white !important;
                    border-bottom-color: #06b6d4 !important;
                    box-shadow: 0 -2px 4px rgba(6, 182, 212, 0.1) !important;
                    font-weight: 700 !important;
                }

                .moment-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #06b6d4 0%, #0891b2 100%);
                }

                .moment-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 搜索面板 - 默认隐藏 */
                .moment-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    display: none;
                    animation: slideDown 0.3s ease-out;
                }

                .moment-index-search-panel.show {
                    display: block;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .moment-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .moment-index-search-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .moment-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .moment-index-search-close {
                    background: none;
                    border: none;
                    color: #718096;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                }

                .moment-index-search-close:hover {
                    background: #f1f5f9;
                    color: #374151;
                }

                .moment-index-search-body {
                    padding: 2rem;
                }

                /* 现代化素材卡片 */
                .moment-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .moment-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .moment-card-header {
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .moment-card-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .moment-card-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin: 0;
                }

                .moment-card-project {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .moment-card-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    flex-direction: column;
                }

                .moment-status-badge {
                    padding: 0.5rem 1rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    margin-bottom: 0.5rem;
                }

                .moment-status-badge.status-online {
                    background: rgba(16, 185, 129, 0.9);
                    border-color: rgba(16, 185, 129, 0.5);
                }

                .moment-status-badge.status-offline {
                    background: rgba(107, 114, 128, 0.9);
                    border-color: rgba(107, 114, 128, 0.5);
                }

                .moment-review-badge {
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1rem;
                    font-weight: 600;
                    border: 1px solid rgba(255, 255, 255, 0.5);
                }

                .moment-review-badge.review-pending {
                    background: rgba(251, 191, 36, 0.9);
                    color: white;
                }

                .moment-review-badge.review-approved {
                    background: rgba(16, 185, 129, 0.9);
                    color: white;
                }

                .moment-review-badge.review-rejected {
                    background: rgba(239, 68, 68, 0.9);
                    color: white;
                }

                .moment-card-body {
                    padding: 2rem;
                }

                .moment-content-section {
                    margin-bottom: 2rem;
                }

                .moment-content-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1rem 0;
                }

                .moment-content-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .moment-content-text {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    border: 1px solid #e2e8f0;
                    font-size: 1.5rem;
                    line-height: 1.6;
                    color: #374151;
                    max-height: 200px;
                    overflow-y: auto;
                }

                .moment-images-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                    gap: 1rem;
                    margin-top: 1rem;
                }

                .moment-image-item {
                    position: relative;
                    border-radius: 0.75rem;
                    overflow: hidden;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    transition: all 0.3s ease;
                    cursor: pointer;
                }

                .moment-image-item:hover {
                    transform: scale(1.05);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                }

                .moment-image-item img {
                    width: 100%;
                    height: 150px;
                    object-fit: cover;
                    display: block;
                }

                .moment-image-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                }

                .moment-image-item:hover .moment-image-overlay {
                    opacity: 1;
                }

                .moment-image-zoom {
                    color: white;
                    font-size: 1.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                    width: 3rem;
                    height: 3rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .moment-info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                .moment-info-section {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    border: 1px solid #e2e8f0;
                }

                .moment-info-section-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1rem 0;
                }

                .moment-info-section-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .moment-info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                }

                .moment-info-item:last-child {
                    margin-bottom: 0;
                }

                .moment-info-label {
                    color: #6b7280;
                    font-weight: 500;
                    min-width: 80px;
                    flex-shrink: 0;
                }

                .moment-info-value {
                    color: #374151;
                    font-weight: 600;
                    flex: 1;
                }

                /* 操作按钮区域 */
                .moment-actions {
                    display: flex;
                    gap: 0.75rem;
                    margin-top: 2rem;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                    flex-wrap: wrap;
                }

                .moment-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .moment-action-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .moment-action-btn.btn-primary {
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    color: white;
                }

                .moment-action-btn.btn-primary:hover {
                    color: white;
                }

                .moment-action-btn.btn-success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                }

                .moment-action-btn.btn-success:hover {
                    color: white;
                }

                .moment-action-btn.btn-warning {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    color: white;
                }

                .moment-action-btn.btn-warning:hover {
                    color: white;
                }

                .moment-action-btn.btn-danger {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    color: white;
                }

                .moment-action-btn.btn-danger:hover {
                    color: white;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .moment-index-container {
                        padding: 1.5rem;
                    }

                    .moment-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .moment-info-grid {
                        grid-template-columns: 1fr;
                    }

                    .moment-images-grid {
                        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                    }
                }

                @media (max-width: 768px) {
                    .moment-index-container {
                        padding: 1rem;
                    }

                    .moment-index-nav-tabs {
                        flex-direction: column;
                    }

                    .moment-index-nav-item {
                        flex: none;
                    }

                    .moment-index-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .moment-index-nav-link.active {
                        border-bottom: none !important;
                        border-right-color: #06b6d4 !important;
                    }

                    .moment-index-title-main {
                        font-size: 1.75rem;
                    }

                    .moment-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .moment-actions {
                        flex-direction: column;
                    }

                    .moment-card-header {
                        flex-direction: column;
                        gap: 1rem;
                        text-align: center;
                    }

                    .moment-images-grid {
                        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                    }

                    .moment-image-item img {
                        height: 100px;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .moment-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .moment-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .moment-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .moment-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }

                /* 图片预览模态框 */
                .moment-image-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.9);
                    display: none;
                    align-items: center;
                    justify-content: center;
                    z-index: 9999;
                    cursor: pointer;
                }

                .moment-image-modal.show {
                    display: flex;
                }

                .moment-image-modal img {
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                    border-radius: 0.5rem;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
                }

                .moment-image-modal-close {
                    position: absolute;
                    top: 2rem;
                    right: 2rem;
                    color: white;
                    font-size: 2rem;
                    cursor: pointer;
                    background: rgba(0, 0, 0, 0.5);
                    border-radius: 50%;
                    width: 3rem;
                    height: 3rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s ease;
                }

                .moment-image-modal-close:hover {
                    background: rgba(0, 0, 0, 0.8);
                    transform: scale(1.1);
                }
                
            </style>

            <div class="moment-index-wrapper">
                <div class="moment-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="moment-index-header moment-index-fade-in">
                        <div class="moment-index-header-content">
                            <div class="moment-index-title">
                                <div class="moment-index-title-icon">
                                    <i class="fa fa-file-image-o"></i>
                                </div>
                                <div class="moment-index-title-text">
                                    <h1 class="moment-index-title-main">素材管理</h1>
                                    <p class="moment-index-title-sub">Content Material Management</p>
                                </div>
                          <div style="text-align: center;margin-left: 168px;">
                            <a href="{:U('moment/edit')}" class="moment-index-search-toggle">
                                <i class="fa fa-plus"></i>
                                <span>添加素材</span>
                            </a>
                    
                        </div>
                            </div>
                            <div class="moment-index-actions">
                                <button type="button" class="moment-index-batch-btn" id="batchOfflineBtn">
                                    <i class="fa fa-trash"></i>
                                    <span>批量下架</span>
                                </button>
                                <button type="button" class="moment-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>
                                
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="moment-index-nav-container moment-index-fade-in-delay-1">
                        <ul class="moment-index-nav-tabs">
                            <li class="moment-index-nav-item">
                                <a href="{:U('Moment/index')}" class="moment-index-nav-link active">
                                    <i class="fa fa-list moment-index-nav-icon"></i>
                                    <span>全部素材</span>
                                </a>
                            </li>
                            <li class="moment-index-nav-item">
                                <a href="javascript:void(0)" class="moment-index-nav-link quick-filter" data-filter="pending">
                                    <i class="fa fa-clock-o moment-index-nav-icon"></i>
                                    <span>待审核</span>
                                </a>
                            </li>
                            <li class="moment-index-nav-item">
                                <a href="javascript:void(0)" class="moment-index-nav-link quick-filter" data-filter="approved">
                                    <i class="fa fa-check-circle moment-index-nav-icon"></i>
                                    <span>已通过</span>
                                </a>
                            </li>
                            <li class="moment-index-nav-item">
                                <a href="javascript:void(0)" class="moment-index-nav-link quick-filter" data-filter="rejected">
                                    <i class="fa fa-times-circle moment-index-nav-icon"></i>
                                    <span>已拒绝</span>
                                </a>
                            </li>
                            <li class="moment-index-nav-item">
                                <a href="javascript:void(0)" class="moment-index-nav-link quick-filter" data-filter="online">
                                    <i class="fa fa-eye moment-index-nav-icon"></i>
                                    <span>已上架</span>
                                </a>
                            </li>
                            <li class="moment-index-nav-item">
                                <a href="javascript:void(0)" class="moment-index-nav-link quick-filter" data-filter="offline">
                                    <i class="fa fa-eye-slash moment-index-nav-icon"></i>
                                    <span>已下架</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 搜索面板 - 默认隐藏 -->
                    <div class="moment-index-search-panel" id="searchPanel">
                        <div class="moment-index-search-header">
                            <div class="moment-index-search-title">
                                <div class="moment-index-search-icon">
                                    <i class="fa fa-search"></i>
                                </div>
                                <span>搜索筛选</span>
                            </div>
                            <button type="button" class="moment-index-search-close" onclick="toggleSearchPanel()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                        <div class="moment-index-search-body">
                            <form method="get" action="{:U('Moment/index')}" class="search-form">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">搜索条件：</label>
                                            <select class="form-control" name="kw">
                                                <php>foreach($c_kw as $key=>$value){</php>
                                                <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">搜索值：</label>
                                            <input class="form-control" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容" />
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">项目：</label>
                                            <select name="project_id" class='form-control' id="project">
                                                <option value="">选择项目</option>
                                                <php> foreach($projectList as $k => $v) { </php>
                                                <option value="{$k}" <php>if($_get['project_id']==$k) echo 'selected';</php>>{$v}</option>
                                                <php> } </php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">岗位：</label>
                                            <select name="post_id" class='form-control' id="post">
                                                <option value="">选择岗位</option>
                                                <php>if($_get['post_id']) {</php>
                                                <option value="{$_get['post_id']}" selected>{$postList[$_get['post_id']]}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">状态：</label>
                                            <select name="status" class='form-control'>
                                                <option value="">全部状态</option>
                                                <php> foreach($statusList as  $k => $v) { </php>
                                                <option value="{$k}" <php>if(is_numeric($_get['status']) && $_get['status']==$k) echo 'selected';</php>>{$v.text}</option>
                                                <php> } </php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">审核状态：</label>
                                            <select name="review_status" class='form-control'>
                                                <option value="">全部审核状态</option>
                                                <php> foreach($reviewStatusList as  $k => $v) { </php>
                                                <option value="{$k}" <php>if(is_numeric($_get['review_status']) && $_get['review_status']==$k) echo 'selected';</php>>{$v.text}</option>
                                                <php> } </php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">&nbsp;</label>
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fa fa-search"></i> 搜索
                                                </button>
                                                <a href="{:U('moment/index')}" class="btn btn-default">
                                                    <i class="fa fa-refresh"></i> 重置
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 素材列表 -->
                    <div class="moment-index-fade-in-delay-2">
                        <php>foreach($list as $v) { </php>
                        <div class="moment-card">
                            <!-- 卡片头部 -->
                            <div class="moment-card-header">
                                <div class="moment-card-title">
                                    <span class="moment-card-project">
                                        <php>if($v['project_id'] && isset($projectList[$v['project_id']])){ </php>
                                            {:$projectList[$v['project_id']]}
                                        <php>} else { </php>
                                            通用项目
                                        <php>}</php>
                                        /
                                        <php>if($v['post_id'] && isset($postList[$v['post_id']])){ </php>
                                            {:$postList[$v['post_id']]}
                                        <php>} else { </php>
                                            通用岗位
                                        <php>}</php>
                                    </span>
                                </div>
                                <div class="moment-card-status">
                                    <!-- 状态徽章 -->
                                    <php>if($v['status'] == 1) { </php>
                                        <span class="moment-status-badge status-online">
                                            <i class="fa fa-eye"></i>
                                            已上架
                                        </span>
                                    <php>} else { </php>
                                        <span class="moment-status-badge status-offline">
                                            <i class="fa fa-eye-slash"></i>
                                            已下架
                                        </span>
                                    <php>}</php>

                                    <!-- 审核状态徽章 -->
                                    <php>if($v['review_status'] == 0) { </php>
                                        <span class="moment-review-badge review-pending">
                                            <i class="fa fa-clock-o"></i>
                                            待审核
                                        </span>
                                    <php>} elseif($v['review_status'] == 1) { </php>
                                        <span class="moment-review-badge review-approved">
                                            <i class="fa fa-check-circle"></i>
                                            已通过
                                        </span>
                                    <php>} elseif($v['review_status'] == 2) { </php>
                                        <span class="moment-review-badge review-rejected">
                                            <i class="fa fa-times-circle"></i>
                                            已拒绝
                                        </span>
                                    <php>}</php>
                                </div>
                            </div>

                            <!-- 卡片主体 -->
                            <div class="moment-card-body">
                                <!-- 文本内容 -->
                                <div class="moment-content-section">
                                    <h4 class="moment-content-title">
                                        <div class="moment-content-icon">
                                            <i class="fa fa-file-text"></i>
                                        </div>
                                        文本内容
                                    </h4>
                                    <div class="moment-content-text">
                                        {:htmlspecialchars_decode($v['content'])}
                                    </div>
                                </div>

                                <!-- 图片素材 -->
                                <php>
                                $hasImages = false;
                                for($i = 1; $i <= 9; $i++) {
                                    if($v['img'.$i]) {
                                        $hasImages = true;
                                        break;
                                    }
                                }
                                if($hasImages) {
                                </php>
                                <div class="moment-content-section">
                                    <h4 class="moment-content-title">
                                        <div class="moment-content-icon">
                                            <i class="fa fa-image"></i>
                                        </div>
                                        图片素材
                                    </h4>
                                    <div class="moment-images-grid">
                                        <php>for($i = 1; $i <= 9; $i++) { </php>
                                        <php>if($v['img'.$i]) { </php>
                                        <div class="moment-image-item" onclick="showImageModal('http://we.zhongcaiguoke.com/{:$v['img'.$i]}')">
                                            <img src="http://we.zhongcaiguoke.com/{:$v['img'.$i]}" alt="素材图片">
                                            <div class="moment-image-overlay">
                                                <div class="moment-image-zoom">
                                                    <i class="fa fa-search-plus"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <php>}</php>
                                        <php>}</php>
                                    </div>
                                </div>
                                <php>}</php>

                                <!-- 信息网格 -->
                                <div class="moment-info-grid">
                                    <!-- 创建信息 -->
                                    <div class="moment-info-section">
                                        <h4 class="moment-info-section-title">
                                            <div class="moment-info-section-icon">
                                                <i class="fa fa-user"></i>
                                            </div>
                                            创建信息
                                        </h4>
                                        <div class="moment-info-item">
                                            <span class="moment-info-label">创建者：</span>
                                            <span class="moment-info-value">{$v.creator_name}</span>
                                        </div>
                                        <div class="moment-info-item">
                                            <span class="moment-info-label">创建时间：</span>
                                            <span class="moment-info-value">{$v.create_time|date='Y-m-d H:i',###}</span>
                                        </div>
                                        <php>if($v['update_by'] > 0) { </php>
                                        <div class="moment-info-item">
                                            <span class="moment-info-label">更新者：</span>
                                            <span class="moment-info-value">{$v.updater_name}</span>
                                        </div>
                                        <div class="moment-info-item">
                                            <span class="moment-info-label">更新时间：</span>
                                            <span class="moment-info-value"><php>if($v['update_time']) echo date('Y-m-d H:i', $v['update_time']);</php></span>
                                        </div>
                                        <php>}</php>
                                    </div>

                                    <!-- 审核信息 -->
                                    <div class="moment-info-section">
                                        <h4 class="moment-info-section-title">
                                            <div class="moment-info-section-icon">
                                                <i class="fa fa-gavel"></i>
                                            </div>
                                            审核信息
                                        </h4>
                                        <div class="moment-info-item">
                                            <span class="moment-info-label">审核者：</span>
                                            <span class="moment-info-value">
                                                <php>if($v['reviewer_name']) { </php>
                                                    {$v.reviewer_name}
                                                <php>} else { </php>
                                                    <span style="color: #6b7280;">待审核</span>
                                                <php>}</php>
                                            </span>
                                        </div>
                                        <div class="moment-info-item">
                                            <span class="moment-info-label">审核状态：</span>
                                            <span class="moment-info-value">
                                                <php>if($v['review_status'] == 0) { </php>
                                                    <span style="color: #f59e0b; font-weight: 600;">
                                                        <i class="fa fa-clock-o"></i> 待审核
                                                    </span>
                                                <php>} elseif($v['review_status'] == 1) { </php>
                                                    <span style="color: #10b981; font-weight: 600;">
                                                        <i class="fa fa-check-circle"></i> 已通过
                                                    </span>
                                                <php>} elseif($v['review_status'] == 2) { </php>
                                                    <span style="color: #ef4444; font-weight: 600;">
                                                        <i class="fa fa-times-circle"></i> 已拒绝
                                                    </span>
                                                <php>}</php>
                                            </span>
                                        </div>
                                        <div class="moment-info-item">
                                            <span class="moment-info-label">上架状态：</span>
                                            <span class="moment-info-value">
                                                <php>if($v['status'] == 1) { </php>
                                                    <span style="color: #10b981; font-weight: 600;">
                                                        <i class="fa fa-eye"></i> 已上架
                                                    </span>
                                                <php>} else { </php>
                                                    <span style="color: #6b7280; font-weight: 600;">
                                                        <i class="fa fa-eye-slash"></i> 已下架
                                                    </span>
                                                <php>}</php>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="moment-actions">
                                    <php>
                                    // 获取当前管理员ID
                                    $currentUserId = session('admin_id');

                                    // 修改审核按钮显示逻辑
                                    if($v['review_status'] == 0) {
                                        $canReview = false;

                                        // 如果素材已被更新
                                        if($v['update_by'] > 0) {
                                            // 创建者可以审核，但最后更新者不能审核
                                            if($v['update_by'] != $currentUserId) {
                                                $canReview = true;
                                            }
                                        } else {
                                            // 如果素材未被更新，则创建者不能审核
                                            if($v['creator_id'] != $currentUserId) {
                                                $canReview = true;
                                            }
                                        }

                                        if($canReview) {
                                    </php>
                                    <a href="{:U('Moment/review', array('id'=>$v['id']))}" class="moment-action-btn btn-primary">
                                        <i class="fa fa-gavel"></i>
                                        <span>审核</span>
                                    </a>
                                    <php>
                                        }
                                    }
                                    </php>

                                    <php>if($v['review_status'] == 1 || $v['review_status'] == 2) { </php>
                                    <php>echo momentStatBtn($v['id'], $v['status']);</php>
                                    <php>}</php>
                                </div>
                            </div>
                        </div>
                        <php>}</php>

                        <php>if(empty($list)) { </php>
                        <div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;">
                            <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;">
                                <i class="fa fa-file-image-o"></i>
                            </div>
                            <h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无素材数据</h3>
                            <p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或创建新的素材。</p>
                        </div>
                        <php>}</php>
                        <!-- 分页和每页显示数量 -->
                        <div class="moment-index-fade-in-delay-3" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 1.5rem; margin-top: 2rem;">
                            <div class="row">
                                <div class="col-sm-8">
                                    <div style="display: flex; align-items: center; justify-content: center;">
                                        {$page}
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="pull-right form-inline" style="display: flex; align-items: center; gap: 0.5rem;">
                                        <span style="font-size: 1.5rem; color: #374151;">每页显示:</span>
                                        <select class="form-control" id="pageSizeSelector" style="width: auto; font-size: 1.5rem;">
                                            <option value="10" <php>if($pageSize == 10) echo 'selected';</php>>10条</option>
                                            <option value="20" <php>if($pageSize == 20) echo 'selected';</php>>20条</option>
                                            <option value="50" <php>if($pageSize == 50) echo 'selected';</php>>50条</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图片预览模态框 -->
                    <div class="moment-image-modal" id="imageModal" onclick="hideImageModal()">
                        <div class="moment-image-modal-close">
                            <i class="fa fa-times"></i>
                        </div>
                        <img id="modalImage" src="" alt="预览图片">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />
<script>
    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
            setTimeout(function() {
                panel.style.display = 'none';
            }, 300);
        } else {
            panel.style.display = 'block';
            setTimeout(function() {
                panel.classList.add('show');
            }, 10);
        }
    }

    // 图片预览功能
    function showImageModal(imageSrc) {
        var modal = document.getElementById('imageModal');
        var modalImage = document.getElementById('modalImage');
        modalImage.src = imageSrc;
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    function hideImageModal() {
        var modal = document.getElementById('imageModal');
        modal.classList.remove('show');
        document.body.style.overflow = 'auto';
    }

    $(document).ready(function() {
        // 根据当前URL参数设置active状态
        function setActiveNavTab() {
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');
            var reviewStatus = urlParams.get('review_status');

            // 移除所有active类
            $('.moment-index-nav-link').removeClass('active');

            // 根据URL参数设置对应的active类
            if (reviewStatus === '0') {
                // 待审核状态筛选
                $('.quick-filter[data-filter="pending"]').addClass('active');
            } else if (reviewStatus === '1') {
                // 已通过状态筛选
                $('.quick-filter[data-filter="approved"]').addClass('active');
            } else if (reviewStatus === '2') {
                // 已拒绝状态筛选
                $('.quick-filter[data-filter="rejected"]').addClass('active');
            } else if (status === '1') {
                // 已上架状态筛选
                $('.quick-filter[data-filter="online"]').addClass('active');
            } else if (status === '0') {
                // 已下架状态筛选
                $('.quick-filter[data-filter="offline"]').addClass('active');
            } else {
                // 默认全部素材
                $('.moment-index-nav-link').first().addClass('active');
            }
        }

        // 页面加载时设置active状态
        setActiveNavTab();

        // 快速筛选功能
        $('.quick-filter').click(function(e) {
            e.preventDefault();
            var filter = $(this).data('filter');
            var currentUrl = window.location.href.split('?')[0];
            var newUrl = currentUrl + '?';

            // 移除当前active类并添加到点击的元素
            $('.moment-index-nav-link').removeClass('active');
            $(this).addClass('active');

            switch(filter) {
                case 'pending':
                    newUrl += 'review_status=0'; // 待审核状态
                    break;
                case 'approved':
                    newUrl += 'review_status=1'; // 已通过状态
                    break;
                case 'rejected':
                    newUrl += 'review_status=2'; // 已拒绝状态
                    break;
                case 'online':
                    newUrl += 'status=1'; // 已上架状态
                    break;
                case 'offline':
                    newUrl += 'status=0'; // 已下架状态
                    break;
                default:
                    newUrl = currentUrl;
            }

            window.location.href = newUrl;
        });

        // "全部素材"链接点击处理
        $('.moment-index-nav-link').first().click(function(e) {
            if ($(this).attr('href') !== 'javascript:void(0)') {
                $('.moment-index-nav-link').removeClass('active');
                $(this).addClass('active');
            }
        });

        // 项目变化时加载对应岗位
        $('#project').change(function(){
            var pid = $(this).val();
            if(pid) {
                $.get('{:U("getPosts")}', {project_id:pid}, function(res){
                    var html = '<option value="">选择岗位</option>';
                    $.each(res, function(k,v){
                        html += '<option value="'+v.id+'">'+v.job_name+'</option>';
                    });
                    $('#post').html(html);
                });
            } else {
                $('#post').html('<option value="">选择岗位</option>');
            }
        });

        // 页面加载时，如果已选择项目，加载对应岗位
        var project_id = '{$_get.project_id}';
        if(project_id) {
            $.get('{:U("getPosts")}', {project_id:project_id}, function(res){
                var html = '<option value="">选择岗位</option>';
                var post_id = '{$_get.post_id}';
                $.each(res, function(k,v){
                    if(post_id == v.id) {
                        html += '<option value="'+v.id+'" selected>'+v.job_name+'</option>';
                    } else {
                        html += '<option value="'+v.id+'">'+v.job_name+'</option>';
                    }
                });
                $('#post').html(html);
            });
        }

        // 分页选择器改变事件处理
        $('#pageSizeSelector').change(function() {
            var pageSize = $(this).val();

            // 获取当前搜索参数
            var kw = '{$_get.kw}';
            var val = '{$_get.val}';
            var project_id = '{$_get.project_id}';
            var post_id = '{$_get.post_id}';
            var status = '{$_get.status}';
            var review_status = '{$_get.review_status}';

            // 构建URL
            var url = '{:U("Moment/index")}';
            url += '?psz=' + pageSize;

            // 添加其他搜索条件
            if (kw) url += '&kw=' + kw;
            if (val) url += '&val=' + encodeURIComponent(val);
            if (project_id) url += '&project_id=' + project_id;
            if (post_id) url += '&post_id=' + post_id;
            if (status !== '') url += '&status=' + status;
            if (review_status !== '') url += '&review_status=' + review_status;

            // 跳转到新URL
            window.location.href = url;
        });

        // 一键下架过期素材按钮点击事件
        $('#batchOfflineBtn').click(function() {
            if (confirm('确定要一键下架所有过期素材吗？\n\n过期素材是指：关联的项目已下架或关联的岗位已下架的素材\n注意：项目名为"通用"或岗位名为"通用"的素材不会被下架')) {
                var $btn = $(this);
                var originalText = $btn.html();

                // 禁用按钮并显示加载状态
                $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> <span>处理中...</span>');

                $.ajax({
                    url: '{:U("Moment/batchOfflineExpired")}',
                    type: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        if (response.status == 1) {
                            if (typeof layer !== 'undefined') {
                                layer.msg('操作成功！共下架了 ' + response.data.count + ' 条过期素材', {icon: 1, time: 3000});
                            } else {
                                alert('操作成功！共下架了 ' + response.data.count + ' 条过期素材');
                            }
                            // 刷新页面显示最新数据
                            setTimeout(function() {
                                window.location.reload();
                            }, 2000);
                        } else {
                            if (typeof layer !== 'undefined') {
                                layer.msg('操作失败：' + response.msg, {icon: 2});
                            } else {
                                alert('操作失败：' + response.msg);
                            }
                        }
                    },
                    error: function() {
                        if (typeof layer !== 'undefined') {
                            layer.msg('网络错误，请稍后重试', {icon: 2});
                        } else {
                            alert('网络错误，请稍后重试');
                        }
                    },
                    complete: function() {
                        // 恢复按钮状态
                        $btn.prop('disabled', false).html(originalText);
                    }
                });
            }
        });

        // 卡片悬停效果增强
        $('.moment-card').hover(
            function() {
                $(this).find('.moment-card-header').css('transform', 'scale(1.02)');
            },
            function() {
                $(this).find('.moment-card-header').css('transform', 'scale(1)');
            }
        );

        // 搜索表单增强
        $('.search-form').on('submit', function() {
            var $submitBtn = $(this).find('button[type="submit"]');
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 搜索中...');

            setTimeout(function() {
                $submitBtn.prop('disabled', false).html(originalText);
            }, 3000);
        });

        // 添加加载动画
        $('.moment-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });

        // 滚动到顶部功能
        var $scrollToTop = $('<button class="scroll-to-top" style="position: fixed; bottom: 2rem; right: 2rem; width: 3rem; height: 3rem; background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); color: white; border: none; border-radius: 50%; font-size: 1.25rem; cursor: pointer; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; display: none; transition: all 0.3s ease;"><i class="fa fa-arrow-up"></i></button>');
        $('body').append($scrollToTop);

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $scrollToTop.fadeIn();
            } else {
                $scrollToTop.fadeOut();
            }
        });

        $scrollToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 600);
        });

        // 搜索结果统计函数
        function showSearchStats() {
            // 先提取分页信息
            if (typeof window.extractPaginationInfo === 'function') {
                window.extractPaginationInfo();
            }

            var currentPageCards = $('.moment-card').length;
            var totalCount = window.momentPageData ? window.momentPageData.totalCount : currentPageCards;
            var currentPage = window.momentPageData ? window.momentPageData.currentPage : 1;
            var pageSize = window.momentPageData ? window.momentPageData.pageSize : 10;

            console.log('统计信息:', {
                currentPageCards: currentPageCards,
                totalCount: totalCount,
                currentPage: currentPage,
                pageSize: pageSize
            });

            // 如果总数为0但有当前页数据，使用当前页数据
            if (totalCount === 0 && currentPageCards > 0) {
                totalCount = currentPageCards;
                window.momentPageData.totalCount = totalCount;
                console.log('使用当前页数据作为总数:', totalCount);
            }

            displayStats(currentPageCards, totalCount, currentPage, pageSize);
        }

        // 显示统计信息的函数
        function displayStats(currentPageCards, totalCount, currentPage, pageSize) {

            if (currentPageCards > 0 && totalCount > 0) {
                var startIndex = (currentPage - 1) * pageSize + 1;
                var endIndex = Math.min(startIndex + currentPageCards - 1, totalCount);

            // 根据当前筛选状态显示不同的统计信息
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');
            var reviewStatus = urlParams.get('review_status');
            var keyword = urlParams.get('kw');
            var projectId = urlParams.get('project_id');
            var postId = urlParams.get('post_id');

            var statusText = '';
            var statusIcon = 'fa-info-circle';
            var statusColor = '#06b6d4';

            if (reviewStatus === '0') {
                statusText = '待审核';
                statusIcon = 'fa-clock-o';
                statusColor = '#f59e0b';
            } else if (reviewStatus === '1') {
                statusText = '已通过';
                statusIcon = 'fa-check-circle';
                statusColor = '#10b981';
            } else if (reviewStatus === '2') {
                statusText = '已拒绝';
                statusIcon = 'fa-times-circle';
                statusColor = '#ef4444';
            } else if (status === '1') {
                statusText = '已上架';
                statusIcon = 'fa-eye';
                statusColor = '#10b981';
            } else if (status === '0') {
                statusText = '已下架';
                statusIcon = 'fa-eye-slash';
                statusColor = '#6b7280';
            } else {
                statusText = '全部';
                statusIcon = 'fa-list';
                statusColor = '#06b6d4';
            }

            // 检查是否有搜索条件
            var hasSearchConditions = keyword || projectId || postId || status || reviewStatus;
            var searchText = '';

            if (hasSearchConditions) {
                var conditions = [];
                if (keyword) conditions.push('关键词"' + keyword + '"');
                if (projectId) conditions.push('指定项目');
                if (postId) conditions.push('指定岗位');
                if (status || reviewStatus) conditions.push(statusText + '状态');

                searchText = '筛选条件：' + conditions.join('、') + '，';
            }

            var statsHtml = '<div class="search-stats" style="background: white; border-radius: 0.75rem; padding: 1.5rem; margin-bottom: 1.5rem; box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; text-align: center; color: #374151; font-size: 1.25rem; line-height: 1.6;">';
            statsHtml += '<div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin-bottom: 0.5rem;">';
            statsHtml += '<i class="fa ' + statusIcon + '" style="color: ' + statusColor + '; font-size: 1.5rem;"></i>';
            statsHtml += '<span style="font-weight: 600; color: #1f2937;">' + statusText + '素材统计</span>';
            statsHtml += '</div>';

            if (hasSearchConditions) {
                statsHtml += '<div style="font-size: 1.125rem; color: #6b7280; margin-bottom: 0.5rem;">' + searchText + '</div>';
            }

                statsHtml += '<div style="font-size: 1.375rem;">';

                // 检查是否有分页信息来判断数据的准确性
                var hasPagination = $('.pagination a').length > 0;
                var isAccurateCount = totalCount > currentPageCards || !hasPagination;

                if (isAccurateCount) {
                    statsHtml += '共找到 <span style="color: ' + statusColor + '; font-weight: 700; font-size: 1.5rem;">' + totalCount + '</span> 个素材';
                } else {
                    statsHtml += '当前页显示 <span style="color: ' + statusColor + '; font-weight: 700; font-size: 1.5rem;">' + currentPageCards + '</span> 个素材';
                    if (hasPagination) {
                        statsHtml += '<div style="font-size: 1.125rem; color: #6b7280; margin-top: 0.5rem;">';
                        statsHtml += '<i class="fa fa-info-circle"></i> 总数统计中，请查看分页信息';
                        statsHtml += '</div>';
                    }
                }

                if (totalCount > pageSize && isAccurateCount) {
                    statsHtml += '<div style="font-size: 1.125rem; color: #6b7280; margin-top: 0.5rem;">';
                    statsHtml += '当前显示第 <span style="color: ' + statusColor + '; font-weight: 600;">' + startIndex + '-' + endIndex + '</span> 个';
                    if (window.momentPageData.totalPages > 1) {
                        statsHtml += '（共 ' + window.momentPageData.totalPages + ' 页）';
                    }
                    statsHtml += '</div>';
                } else if (currentPageCards > 0 && hasPagination) {
                    statsHtml += '<div style="font-size: 1.125rem; color: #6b7280; margin-top: 0.5rem;">';
                    statsHtml += '第 ' + currentPage + ' 页数据';
                    statsHtml += '</div>';
                }

                statsHtml += '</div></div>';

            var $statsInfo = $(statsHtml);
            $('.moment-card').first().before($statsInfo);
        } else if (totalCount === 0) {
            // 如果没有数据，显示相应的提示
            var urlParams = new URLSearchParams(window.location.search);
            var hasFilters = urlParams.get('status') || urlParams.get('review_status') || urlParams.get('kw') || urlParams.get('project_id') || urlParams.get('post_id');

            var noResultsHtml = '<div class="search-stats" style="background: white; border-radius: 0.75rem; padding: 2rem; margin-bottom: 1.5rem; box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; text-align: center; color: #374151; font-size: 1.25rem;">';

            if (hasFilters) {
                noResultsHtml += '<div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin-bottom: 1rem;">';
                noResultsHtml += '<i class="fa fa-search" style="color: #f59e0b; font-size: 2rem;"></i>';
                noResultsHtml += '<span style="font-weight: 600; color: #1f2937; font-size: 1.375rem;">搜索结果</span>';
                noResultsHtml += '</div>';
                noResultsHtml += '<div style="color: #ef4444; font-size: 1.25rem; margin-bottom: 1rem;">';
                noResultsHtml += '<i class="fa fa-exclamation-triangle"></i> 未找到符合条件的素材';
                noResultsHtml += '</div>';
                noResultsHtml += '<div style="color: #6b7280; font-size: 1.125rem; line-height: 1.6;">';
                noResultsHtml += '建议您：<br>';
                noResultsHtml += '• 检查筛选条件是否过于严格<br>';
                noResultsHtml += '• 尝试使用不同的关键词<br>';
                noResultsHtml += '• 清除部分筛选条件重新搜索';
                noResultsHtml += '</div>';
                noResultsHtml += '<div style="margin-top: 1.5rem;">';
                noResultsHtml += '<a href="{:U(\'Moment/index\')}" class="btn" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem;">';
                noResultsHtml += '<i class="fa fa-refresh"></i> 清除筛选条件';
                noResultsHtml += '</a>';
                noResultsHtml += '</div>';
            } else {
                noResultsHtml += '<div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin-bottom: 1rem;">';
                noResultsHtml += '<i class="fa fa-inbox" style="color: #6b7280; font-size: 2rem;"></i>';
                noResultsHtml += '<span style="font-weight: 600; color: #1f2937; font-size: 1.375rem;">暂无素材</span>';
                noResultsHtml += '</div>';
                noResultsHtml += '<div style="color: #6b7280; font-size: 1.125rem; margin-bottom: 1.5rem;">还没有任何素材，快来添加第一个吧！</div>';
                noResultsHtml += '<div>';
                noResultsHtml += '<a href="{:U(\'Moment/edit\')}" class="btn" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem;">';
                noResultsHtml += '<i class="fa fa-plus"></i> 添加素材';
                noResultsHtml += '</a>';
                noResultsHtml += '</div>';
            }

            noResultsHtml += '</div>';

                var $noResultsInfo = $(noResultsHtml);
                if ($('.empty-state').length > 0) {
                    $('.empty-state').before($noResultsInfo);
                } else {
                    $('.moment-index-cards-container').prepend($noResultsInfo);
                }
            }
        }

        // 调用统计显示函数
        showSearchStats();

        // 键盘快捷键
        $(document).on('keydown', function(e) {
            // ESC 关闭图片预览
            if (e.keyCode === 27) {
                hideImageModal();
            }
        });

        // 延迟执行active状态设置，确保DOM完全加载
        setTimeout(function() {
            setActiveNavTab();
        }, 100);
    });

    // 添加自定义样式增强
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .search-stats {
                animation: fadeInUp 0.6s ease-out;
            }

            .empty-state {
                animation: fadeInUp 0.6s ease-out;
            }

            .moment-card {
                animation: fadeInUp 0.6s ease-out both;
            }

            .scroll-to-top:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 15px rgba(0,0,0,0.2);
            }

            .moment-action-btn:not(.qrcode) {
                margin-right: 0.5rem;
                margin-bottom: 0.5rem;
            }

            .moment-card-header {
                transition: all 0.3s ease;
            }

            /* 增强active状态的视觉效果 */
            .moment-index-nav-link.active {
                background: white !important;
                color: #06b6d4 !important;
                border-bottom-color: #06b6d4 !important;
                box-shadow: 0 -2px 4px rgba(6, 182, 212, 0.1) !important;
                font-weight: 700 !important;
            }

            .moment-index-nav-link.active .moment-index-nav-icon {
                color: #06b6d4 !important;
            }

            /* 确保active状态在hover时保持 */
            .moment-index-nav-link.active:hover {
                background: white !important;
                color: #06b6d4 !important;
            }

            /* 图片预览增强 */
            .moment-image-modal {
                backdrop-filter: blur(5px);
            }

            .moment-content-text {
                scrollbar-width: thin;
                scrollbar-color: #cbd5e1 #f1f5f9;
            }

            .moment-content-text::-webkit-scrollbar {
                width: 8px;
            }

            .moment-content-text::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 4px;
            }

            .moment-content-text::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 4px;
            }

            .moment-content-text::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }
        `)
        .appendTo('head');
</script>

 
