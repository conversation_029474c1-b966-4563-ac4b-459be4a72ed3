<include file="block/hat" />
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化提现审核管理页面样式 */
                .withdrawal-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .withdrawal-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .withdrawal-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .withdrawal-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .withdrawal-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .withdrawal-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .withdrawal-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .withdrawal-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .withdrawal-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .withdrawal-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .withdrawal-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .withdrawal-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                    text-decoration: none;
                }

                .withdrawal-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .withdrawal-index-stats-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
                    text-decoration: none;
                }

                .withdrawal-index-stats-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .withdrawal-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .withdrawal-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .withdrawal-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .withdrawal-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .withdrawal-index-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .withdrawal-index-nav-link.active {
                    color: #667eea;
                    background: white;
                    border-bottom-color: #667eea;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1);
                }

                .withdrawal-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .withdrawal-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 搜索面板 - 默认隐藏 */
                .withdrawal-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    display: none;
                    animation: slideDown 0.3s ease-out;
                }

                .withdrawal-index-search-panel.show {
                    display: block;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .withdrawal-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .withdrawal-index-search-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .withdrawal-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .withdrawal-index-search-close {
                    background: none;
                    border: none;
                    color: #718096;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                }

                .withdrawal-index-search-close:hover {
                    background: #f1f5f9;
                    color: #374151;
                }

                .withdrawal-index-search-body {
                    padding: 2rem;
                }

                /* 现代化提现申请卡片 */
                .withdrawal-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .withdrawal-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .withdrawal-card-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .withdrawal-card-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .withdrawal-card-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin: 0;
                }

                .withdrawal-card-id {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .withdrawal-card-service-name {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .withdrawal-card-user-name {
                    font-size: 1.25rem;
                    color: rgba(255, 255, 255, 0.9);
                    margin: 0.25rem 0 0 0;
                    font-weight: 400;
                }

                .withdrawal-card-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    flex-direction: column;
                }

                .withdrawal-status-badge {
                    padding: 0.5rem 1rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    margin-bottom: 0.5rem;
                }

                .withdrawal-status-badge.status-pending {
                    background: rgba(251, 191, 36, 0.9);
                    border-color: rgba(251, 191, 36, 0.5);
                }

                .withdrawal-status-badge.status-approved {
                    background: rgba(16, 185, 129, 0.9);
                    border-color: rgba(16, 185, 129, 0.5);
                }

                .withdrawal-status-badge.status-rejected {
                    background: rgba(239, 68, 68, 0.9);
                    border-color: rgba(239, 68, 68, 0.5);
                }

                .withdrawal-status-badge.status-processing {
                    background: rgba(59, 130, 246, 0.9);
                    border-color: rgba(59, 130, 246, 0.5);
                }

                .withdrawal-card-body {
                    padding: 2rem;
                }

                .withdrawal-info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                .withdrawal-info-section {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    border: 1px solid #e2e8f0;
                }

                .withdrawal-info-section-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1rem 0;
                }

                .withdrawal-info-section-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .withdrawal-info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                }

                .withdrawal-info-item:last-child {
                    margin-bottom: 0;
                }

                .withdrawal-info-label {
                    color: #6b7280;
                    font-weight: 500;
                    min-width: 80px;
                    flex-shrink: 0;
                }

                .withdrawal-info-value {
                    color: #374151;
                    font-weight: 600;
                    flex: 1;
                    word-break: break-all;
                }

                /* 金额统计区域 */
                .withdrawal-amount-section {
                    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
                    border: 1px solid #10b981;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    margin-bottom: 2rem;
                }

                .withdrawal-amount-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #065f46;
                    margin: 0 0 1rem 0;
                }

                .withdrawal-amount-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 1rem;
                }

                .withdrawal-amount-item {
                    text-align: center;
                    padding: 1rem;
                    background: rgba(16, 185, 129, 0.1);
                    border-radius: 0.5rem;
                }

                .withdrawal-amount-label {
                    font-size: 1.25rem;
                    color: #065f46;
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                }

                .withdrawal-amount-value {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #047857;
                    margin: 0;
                }

                .withdrawal-amount-value.highlight {
                    color: #ff6b00;
                    font-size: 2.25rem;
                }

                /* 银行卡信息区域 */
                .withdrawal-bank-section {
                    background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
                    border: 1px solid #a855f7;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    margin-bottom: 2rem;
                }

                .withdrawal-bank-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #7c2d12;
                    margin: 0 0 1rem 0;
                }

                .withdrawal-bank-info {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                }

                .withdrawal-bank-item {
                    background: rgba(168, 85, 247, 0.1);
                    padding: 1rem;
                    border-radius: 0.5rem;
                    border: 1px solid rgba(168, 85, 247, 0.2);
                }

                .withdrawal-bank-label {
                    font-size: 1.25rem;
                    color: #7c2d12;
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                }

                .withdrawal-bank-value {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #581c87;
                    margin: 0;
                    word-break: break-all;
                }

                .withdrawal-platform-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    margin-top: 0.5rem;
                }

                /* 审核信息区域 */
                .withdrawal-review-section {
                    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
                    border: 1px solid #f59e0b;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    margin-bottom: 2rem;
                }

                .withdrawal-review-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #92400e;
                    margin: 0 0 1rem 0;
                }

                .withdrawal-review-info {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                }

                .withdrawal-review-item {
                    background: rgba(251, 191, 36, 0.1);
                    padding: 1rem;
                    border-radius: 0.5rem;
                    border: 1px solid rgba(251, 191, 36, 0.2);
                }

                .withdrawal-review-label {
                    font-size: 1.25rem;
                    color: #92400e;
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                }

                .withdrawal-review-value {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #d97706;
                    margin: 0;
                }

                /* 操作按钮区域 */
                .withdrawal-actions {
                    display: flex;
                    gap: 0.75rem;
                    margin-top: 2rem;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                    flex-wrap: wrap;
                }

                .withdrawal-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .withdrawal-action-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .withdrawal-action-btn.btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .withdrawal-action-btn.btn-primary:hover {
                    color: white;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .withdrawal-index-container {
                        padding: 1.5rem;
                    }

                    .withdrawal-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .withdrawal-info-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .withdrawal-index-container {
                        padding: 1rem;
                    }

                    .withdrawal-index-nav-tabs {
                        flex-direction: column;
                    }

                    .withdrawal-index-nav-item {
                        flex: none;
                    }

                    .withdrawal-index-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .withdrawal-index-nav-link.active {
                        border-bottom: none;
                        border-right-color: #667eea;
                    }

                    .withdrawal-index-title-main {
                        font-size: 1.75rem;
                    }

                    .withdrawal-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .withdrawal-actions {
                        flex-direction: column;
                    }

                    .withdrawal-card-header {
                        flex-direction: column;
                        gap: 1rem;
                        text-align: center;
                    }

                    .withdrawal-amount-grid {
                        grid-template-columns: repeat(2, 1fr);
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .withdrawal-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .withdrawal-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .withdrawal-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .withdrawal-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="withdrawal-index-wrapper">
                <div class="withdrawal-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="withdrawal-index-header withdrawal-index-fade-in">
                        <div class="withdrawal-index-header-content">
                            <div class="withdrawal-index-title">
                                <div class="withdrawal-index-title-icon">
                                    <i class="fa fa-money"></i>
                                </div>
                                <div class="withdrawal-index-title-text">
                                    <h1 class="withdrawal-index-title-main">提现审核管理</h1>
                                    <p class="withdrawal-index-title-sub">Withdrawal Review Management</p>
                                </div>
                            </div>
                            <div class="withdrawal-index-actions">
                                <button type="button" class="withdrawal-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>
                                <a href="javascript:void(0)" class="withdrawal-index-stats-btn" onclick="showStats()">
                                    <i class="fa fa-bar-chart"></i>
                                    <span>统计报表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="withdrawal-index-nav-container withdrawal-index-fade-in-delay-1">
                        <ul class="withdrawal-index-nav-tabs">
                            <li class="withdrawal-index-nav-item">
                                <a href="{:U('Withdrawal/index')}" class="withdrawal-index-nav-link active">
                                    <i class="fa fa-list withdrawal-index-nav-icon"></i>
                                    <span>全部申请</span>
                                </a>
                            </li>
                            <li class="withdrawal-index-nav-item">
                                <a href="javascript:void(0)" class="withdrawal-index-nav-link quick-filter" data-filter="pending">
                                    <i class="fa fa-clock-o withdrawal-index-nav-icon"></i>
                                    <span>待审核</span>
                                </a>
                            </li>
                            <li class="withdrawal-index-nav-item">
                                <a href="javascript:void(0)" class="withdrawal-index-nav-link quick-filter" data-filter="processing">
                                    <i class="fa fa-credit-card withdrawal-index-nav-icon"></i>
                                    <span>待打款</span>
                                </a>
                            </li>
                            <li class="withdrawal-index-nav-item">
                                <a href="javascript:void(0)" class="withdrawal-index-nav-link quick-filter" data-filter="approved">
                                    <i class="fa fa-check withdrawal-index-nav-icon"></i>
                                    <span>已完成</span>
                                </a>
                            </li>
                            <li class="withdrawal-index-nav-item">
                                <a href="javascript:void(0)" class="withdrawal-index-nav-link quick-filter" data-filter="rejected">
                                    <i class="fa fa-times withdrawal-index-nav-icon"></i>
                                    <span>已驳回</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 搜索面板 - 默认隐藏 -->
                    <div class="withdrawal-index-search-panel" id="searchPanel">
                        <div class="withdrawal-index-search-header">
                            <div class="withdrawal-index-search-title">
                                <div class="withdrawal-index-search-icon">
                                    <i class="fa fa-search"></i>
                                </div>
                                <span>搜索筛选</span>
                            </div>
                            <button type="button" class="withdrawal-index-search-close" onclick="toggleSearchPanel()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                        <div class="withdrawal-index-search-body">
                            <form method="get" action="{:U('Withdrawal/index')}" class="search-form">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">服务站名称：</label>
                                            <input type="text" name="service_station_name" class="form-control" value="{:I('get.service_station_name')}" placeholder="请输入服务站名称" />
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">提现状态：</label>
                                            <select name="status" class="form-control">
                                                <option value="0">全部状态</option>
                                                <volist name="statusList" id="vo">
                                                    <option value="{$key}" <eq name="key" value="{:I('get.status')}">selected</eq>>{$vo.text}</option>
                                                </volist>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">开始时间：</label>
                                            <input type="text" name="start_time" class="form-control js-datetime" value="{:I('get.start_time')}" autocomplete="off" placeholder="选择开始时间" />
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">结束时间：</label>
                                            <input type="text" name="end_time" class="form-control js-datetime" value="{:I('get.end_time')}" autocomplete="off" placeholder="选择结束时间" />
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-top: 15px;">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-search"></i> 搜索
                                            </button>
                                            <a href="{:U('Withdrawal/index')}" class="btn btn-default">
                                                <i class="fa fa-refresh"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- 提现申请列表 -->
                    <div class="withdrawal-index-fade-in-delay-2">
                        <foreach name="list" item="vo">
                        <div class="withdrawal-card">
                            <!-- 卡片头部 -->
                            <div class="withdrawal-card-header">
                                <div class="withdrawal-card-title">
                                    <span class="withdrawal-card-id">#{$vo.id}</span>
                                    <div>
                                        <h3 class="withdrawal-card-service-name">{$vo.service_name}</h3>
                                        <p class="withdrawal-card-user-name">{$vo.requester_name} (ID: {$vo.user_id})</p>
                                    </div>
                                </div>
                                <div class="withdrawal-card-status">
                                    <php>
                                        $statusClass = '';
                                        $statusStyle = isset($statusList[$vo['status']]['style']) ? $statusList[$vo['status']]['style'] : 'default';
                                        switch($statusStyle) {
                                            case 'warning':
                                                $statusClass = 'status-pending';
                                                break;
                                            case 'success':
                                                $statusClass = 'status-approved';
                                                break;
                                            case 'danger':
                                                $statusClass = 'status-rejected';
                                                break;
                                            case 'info':
                                                $statusClass = 'status-processing';
                                                break;
                                            default:
                                                $statusClass = 'status-pending';
                                        }
                                    </php>
                                    <span class="withdrawal-status-badge {$statusClass}">
                                        <i class="fa fa-circle"></i>
                                        {$statusList[$vo['status']]['text']}
                                    </span>
                                </div>
                            </div>

                            <!-- 卡片主体 -->
                            <div class="withdrawal-card-body">
                                <!-- 金额统计 -->
                                <div class="withdrawal-amount-section">
                                    <h4 class="withdrawal-amount-title">
                                        <i class="fa fa-money"></i>
                                        金额统计
                                    </h4>
                                    <div class="withdrawal-amount-grid">
                                        <div class="withdrawal-amount-item">
                                            <div class="withdrawal-amount-label">申请金额</div>
                                            <div class="withdrawal-amount-value">¥{$vo.amount}</div>
                                        </div>
                                        <div class="withdrawal-amount-item">
                                            <div class="withdrawal-amount-label">服务费(8%)</div>
                                            <div class="withdrawal-amount-value">¥{$vo.service_fee|default='0.00'}</div>
                                        </div>
                                        <div class="withdrawal-amount-item">
                                            <div class="withdrawal-amount-label">实际到账</div>
                                            <div class="withdrawal-amount-value highlight">¥{$vo.actual_amount|default=$vo['amount']}</div>
                                        </div>
                                        <div class="withdrawal-amount-item">
                                            <div class="withdrawal-amount-label">申请时间</div>
                                            <div class="withdrawal-amount-value" style="font-size: 1.5rem;">{:date('Y-m-d H:i', $vo['request_time'])}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 银行卡信息 -->
                                <div class="withdrawal-bank-section">
                                    <h4 class="withdrawal-bank-title">
                                        <i class="fa fa-credit-card"></i>
                                        收款信息
                                    </h4>
                                    <div class="withdrawal-bank-info">
                                        <div class="withdrawal-bank-item">
                                            <div class="withdrawal-bank-label">
                                                <if condition="$vo.platform_type eq 'linggong'">
                                                    平台名称
                                                <else />
                                                    银行名称
                                                </if>
                                            </div>
                                            <div class="withdrawal-bank-value">{$vo.display_bank_name}</div>
                                        </div>
                                        <div class="withdrawal-bank-item">
                                            <div class="withdrawal-bank-label">
                                                <if condition="$vo.platform_type eq 'linggong'">
                                                    账号
                                                <else />
                                                    卡号
                                                </if>
                                            </div>
                                            <div class="withdrawal-bank-value">{$vo.display_card_number}</div>
                                        </div>
                                        <div class="withdrawal-bank-item">
                                            <div class="withdrawal-bank-label">户名</div>
                                            <div class="withdrawal-bank-value">{$vo.display_account_holder}</div>
                                        </div>
                                        <if condition="$vo.platform_type eq 'linggong'">
                                        <div class="withdrawal-bank-item">
                                            <div class="withdrawal-bank-label">平台类型</div>
                                            <div class="withdrawal-bank-value">
                                                <span class="withdrawal-platform-badge">
                                                    <i class="fa fa-mobile"></i>
                                                    灵工平台 ({$vo.platform_type_name|default='未知'})
                                                </span>
                                            </div>
                                        </div>
                                        </if>
                                    </div>
                                </div>

                                <!-- 审核信息 -->
                                <div class="withdrawal-review-section">
                                    <h4 class="withdrawal-review-title">
                                        <i class="fa fa-user-circle"></i>
                                        审核信息
                                    </h4>
                                    <div class="withdrawal-review-info">
                                        <div class="withdrawal-review-item">
                                            <div class="withdrawal-review-label">审核人</div>
                                            <div class="withdrawal-review-value">{$vo.reviewer_name|default='-'}</div>
                                        </div>
                                        <div class="withdrawal-review-item">
                                            <div class="withdrawal-review-label">审核时间</div>
                                            <div class="withdrawal-review-value">
                                                <notempty name="vo.review_time">
                                                    {:date('Y-m-d H:i', $vo['review_time'])}
                                                <else/>
                                                    -
                                                </notempty>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="withdrawal-actions">
                                    <a href="{:U('Withdrawal/review',array('id'=>$vo['id']))}" class="withdrawal-action-btn btn-primary">
                                        <i class="fa fa-eye"></i>
                                        <span>查看/处理</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                        </foreach>

                        <if condition="empty($list)">
                        <div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;">
                            <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;">
                                <i class="fa fa-money"></i>
                            </div>
                            <h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无提现申请</h3>
                            <p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或等待新的提现申请。</p>
                        </div>
                        </if>

                        <!-- 分页 -->
                        <div class="withdrawal-index-fade-in-delay-3" style="text-align: center; margin-top: 30px;">
                            {$page}
                        </div>
                    </div>
                </div>
            </div>
            </div>
            <!-- Main content end -->
        </div>
	</div>
</div>

<include file="block/footer" />

<script>
    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
            setTimeout(function() {
                panel.style.display = 'none';
            }, 300);
        } else {
            panel.style.display = 'block';
            setTimeout(function() {
                panel.classList.add('show');
            }, 10);
        }
    }

    // 统计报表功能
    function showStats() {
        layer.open({
            type: 1,
            title: '提现统计报表',
            area: ['800px', '600px'],
            content: '<div style="padding: 20px; text-align: center;"><i class="fa fa-spinner fa-spin" style="font-size: 2rem;"></i><br><br>统计数据加载中...</div>',
            success: function(layero, index) {
                // 这里可以加载统计数据
                setTimeout(function() {
                    var statsHtml = `
                        <div style="padding: 20px;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1.5rem; border-radius: 0.5rem; text-align: center;">
                                    <div style="font-size: 2rem; font-weight: bold;">待审核</div>
                                    <div style="font-size: 1.25rem; opacity: 0.9;">申请数量</div>
                                </div>
                                <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 1.5rem; border-radius: 0.5rem; text-align: center;">
                                    <div style="font-size: 2rem; font-weight: bold;">已通过</div>
                                    <div style="font-size: 1.25rem; opacity: 0.9;">申请数量</div>
                                </div>
                                <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 1.5rem; border-radius: 0.5rem; text-align: center;">
                                    <div style="font-size: 2rem; font-weight: bold;">总金额</div>
                                    <div style="font-size: 1.25rem; opacity: 0.9;">提现金额</div>
                                </div>
                            </div>
                            <div style="text-align: center; color: #6b7280;">
                                <p>详细统计功能开发中...</p>
                            </div>
                        </div>
                    `;
                    layero.find('.layui-layer-content').html(statsHtml);
                }, 1000);
            }
        });
    }

    $(document).ready(function() {
        // 根据当前URL参数设置active状态
        function setActiveNavTab() {
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');

            // 移除所有active类
            $('.withdrawal-index-nav-link').removeClass('active');

            // 根据status参数设置对应的active类
            if (!status || status === '0') {
                // 全部申请
                $('.withdrawal-index-nav-link').first().addClass('active');
            } else {
                var filterMap = {
                    '1': 'pending',    // 待审核
                    '2': 'processing', // 待打款
                    '3': 'rejected',   // 已驳回
                    '4': 'approved',   // 打款完成
                    '5': 'rejected',   // 打款失败 (归类为拒绝状态)
                    '6': 'processing'  // 待开票 (归类为处理中)
                };

                var filterType = filterMap[status];
                if (filterType) {
                    $('.quick-filter[data-filter="' + filterType + '"]').addClass('active');
                }
            }
        }

        // 页面加载时设置active状态
        setActiveNavTab();

        // 调试信息 - 显示当前URL参数
        var urlParams = new URLSearchParams(window.location.search);
        var currentStatus = urlParams.get('status');
        console.log('当前状态参数:', currentStatus);
        console.log('当前URL:', window.location.href);

        // 快速筛选功能
        $('.quick-filter').click(function(e) {
            e.preventDefault();
            var filter = $(this).data('filter');
            var currentUrl = window.location.href.split('?')[0];
            var newUrl = currentUrl + '?';

            // 移除当前active类并添加到点击的元素
            $('.withdrawal-index-nav-link').removeClass('active');
            $(this).addClass('active');

            switch(filter) {
                case 'pending':
                    newUrl += 'status=1'; // 待审核状态
                    break;
                case 'processing':
                    newUrl += 'status=2'; // 待打款状态 (处理中)
                    break;
                case 'approved':
                    newUrl += 'status=4'; // 打款完成状态 (已通过)
                    break;
                case 'rejected':
                    newUrl += 'status=3'; // 已驳回状态 (已拒绝)
                    break;
                default:
                    newUrl = currentUrl;
            }

            window.location.href = newUrl;
        });

        // "全部申请"链接点击处理
        $('.withdrawal-index-nav-link').first().click(function(e) {
            if ($(this).attr('href') !== 'javascript:void(0)') {
                $('.withdrawal-index-nav-link').removeClass('active');
                $(this).addClass('active');
            }
        });

        // 卡片悬停效果增强
        $('.withdrawal-card').hover(
            function() {
                $(this).find('.withdrawal-card-header').css('transform', 'scale(1.02)');
            },
            function() {
                $(this).find('.withdrawal-card-header').css('transform', 'scale(1)');
            }
        );

        // 搜索表单增强
        $('.search-form').on('submit', function() {
            var $submitBtn = $(this).find('button[type="submit"]');
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 搜索中...');

            setTimeout(function() {
                $submitBtn.prop('disabled', false).html(originalText);
            }, 3000);
        });

        // 操作按钮确认增强
        $('.withdrawal-action-btn').click(function(e) {
            var $btn = $(this);
            var text = $btn.text().trim();

            if (text.includes('处理') && !$btn.data('confirmed')) {
                e.preventDefault();
                layer.confirm('确定要处理该提现申请吗？', {
                    icon: 3,
                    title: '操作确认',
                    btn: ['确定', '取消']
                }, function(index) {
                    $btn.data('confirmed', true);
                    window.location.href = $btn.attr('href');
                    layer.close(index);
                });
            }
        });

        // 金额数据可视化增强
        $('.withdrawal-amount-value').each(function() {
            var text = $(this).text();
            var value = parseFloat(text.replace(/[^\d.]/g, ''));
            var $item = $(this).closest('.withdrawal-amount-item');

            // 根据数值添加不同的视觉效果
            if (value > 10000) {
                $item.css('background', 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)');
            } else if (value > 5000) {
                $item.css('background', 'linear-gradient(135deg, #fefce8 0%, #fef3c7 100%)');
            } else if (value > 0) {
                $item.css('background', 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)');
            }
        });

        // 银行卡号脱敏显示增强
        $('.withdrawal-bank-value').each(function() {
            var text = $(this).text();
            if (/^\d{16,19}$/.test(text.replace(/\s/g, ''))) {
                // 银行卡号脱敏
                var maskedCard = text.substring(0, 4) + ' **** **** ' + text.substring(text.length - 4);
                $(this).attr('title', '点击查看完整卡号').css('cursor', 'pointer');
                $(this).data('original', text).text(maskedCard);

                $(this).click(function() {
                    if ($(this).text() === maskedCard) {
                        $(this).text($(this).data('original'));
                        $(this).attr('title', '点击隐藏卡号');
                    } else {
                        $(this).text(maskedCard);
                        $(this).attr('title', '点击查看完整卡号');
                    }
                });
            }
        });

        // 滚动到顶部功能
        var $scrollToTop = $('<button class="scroll-to-top" style="position: fixed; bottom: 2rem; right: 2rem; width: 3rem; height: 3rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 50%; font-size: 1.25rem; cursor: pointer; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; display: none; transition: all 0.3s ease;"><i class="fa fa-arrow-up"></i></button>');
        $('body').append($scrollToTop);

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $scrollToTop.fadeIn();
            } else {
                $scrollToTop.fadeOut();
            }
        });

        $scrollToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 600);
        });

        // 添加加载动画
        $('.withdrawal-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });

        // 搜索结果统计
        var totalCards = $('.withdrawal-card').length;
        if (totalCards > 0) {
            var $statsInfo = $('<div class="search-stats" style="background: white; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; color: #6b7280; font-size: 1.25rem;"><i class="fa fa-info-circle"></i> 共找到 ' + totalCards + ' 个提现申请</div>');
            $('.withdrawal-card').first().before($statsInfo);
        }

        // 状态统计
        var statusCounts = {};
        $('.withdrawal-status-badge').each(function() {
            var status = $(this).text().trim();
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });

        console.log('状态统计:', statusCounts);

        // 日期时间选择器初始化
        $('.js-datetime').each(function() {
            $(this).datetimepicker({
                format: 'Y-m-d H:i',
                lang: 'zh',
                timepicker: true,
                datepicker: true,
                step: 30
            });
        });

        // 延迟执行active状态设置，确保DOM完全加载
        setTimeout(function() {
            setActiveNavTab();
        }, 100);
    });

    // 添加自定义样式
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .search-stats {
                animation: fadeInUp 0.6s ease-out;
            }

            .empty-state {
                animation: fadeInUp 0.6s ease-out;
            }

            .withdrawal-card {
                animation: fadeInUp 0.6s ease-out both;
            }

            .scroll-to-top:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 15px rgba(0,0,0,0.2);
            }

            .withdrawal-action-btn:not(.qrcode) {
                margin-right: 0.5rem;
                margin-bottom: 0.5rem;
            }

            .withdrawal-card-header {
                transition: all 0.3s ease;
            }

            .withdrawal-bank-value[title] {
                border-bottom: 1px dashed #a855f7;
            }

            .withdrawal-bank-value[title]:hover {
                background: rgba(168, 85, 247, 0.1);
                border-radius: 0.25rem;
                padding: 0.25rem;
            }

            /* 增强active状态的视觉效果 */
            .withdrawal-index-nav-link.active {
                background: white !important;
                color: #667eea !important;
                border-bottom-color: #667eea !important;
                box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1) !important;
                font-weight: 700 !important;
            }

            .withdrawal-index-nav-link.active .withdrawal-index-nav-icon {
                color: #667eea !important;
            }

            /* 确保active状态在hover时保持 */
            .withdrawal-index-nav-link.active:hover {
                background: white !important;
                color: #667eea !important;
            }
        `)
        .appendTo('head');
</script>