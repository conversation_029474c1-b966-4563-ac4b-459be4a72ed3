<?php
namespace Common\Model;

use Think\Model;

class UserJobModel extends Model
{
    protected $_validate = array(
        array('photo_path', 'require', '证件照不能为空！',0, 'unique', self::MODEL_INSERT),
        array('name', 'require', '姓名不能为空！',0, 'unique', self::MODEL_INSERT),
        array('gender', 'require', '性别不能为空！',0, 'unique', self::MODEL_INSERT),
        array('birthdate', 'require', '出生日期不能为空！',0, 'unique', self::MODEL_INSERT),
        array('id_number', 'require', '身份证号不能为空！',0, 'unique', self::MODEL_INSERT),
        array('nation', 'require', '民族不能为空！',0, 'unique', self::MODEL_INSERT),
        array('political_status', 'require', '政治面貌不能为空！',0, 'unique', self::MODEL_INSERT),
        array('marital_status', 'require', '婚姻状况不能为空！',0, 'unique', self::MODEL_INSERT),
        array('health_status', 'require', '健康状况不能为空！',0, 'unique', self::MODEL_INSERT),
        array('height', 'require', '身高不能为空！',0, 'unique', self::MODEL_INSERT),
        array('weight', 'require', '体重不能为空！',0, 'unique', self::MODEL_INSERT),
        array('vision', 'require', '视力不能为空！',0, 'unique', self::MODEL_INSERT),
        array('is_afraid_heights', 'require', '是否恐高不能为空！',0, 'unique', self::MODEL_INSERT),
        array('education_level', 'require', '学历不能为空！',0, 'unique', self::MODEL_INSERT),
        array('graduate_school', 'require', '毕业院校不能为空！',0, 'unique', self::MODEL_INSERT),
        array('major', 'require', '专业不能为空！',0, 'unique', self::MODEL_INSERT),
        array('work_experience_years', 'require', '工作经验年限不能为空！',0, 'unique', self::MODEL_INSERT),
        array('phone', 'require', '手机号码不能为空！',0, 'unique', self::MODEL_INSERT),
        array('email', 'require', '邮箱不能为空！',0, 'unique', self::MODEL_INSERT),
        array('emergency_contact', 'require', '紧急联系人电话不能为空！',0, 'unique', self::MODEL_INSERT),
        array('applied_position', 'require', '应聘职位不能为空！',0, 'unique', self::MODEL_INSERT),
    );
}