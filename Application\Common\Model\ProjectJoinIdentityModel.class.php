<?php
namespace Common\Model;

use Think\Model;

/**
 * 项目身份成本模型
 */
class ProjectJoinIdentityModel extends Model
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_UPDATE, 'function'],
    ];

    /**
     * 获取指定岗位的服务站身份成本价（基准成本价）
     * @param int $postId 岗位ID
     * @return int 成本价（元单位），如果不存在返回0
     */
    public function getBaseCostByPostId($postId)
    {
        // 先获取岗位信息，确保使用正确的项目ID
        $post = D('ProjectPost')->where(['id' => $postId])->find();
        if (!$post) {
            return 0;
        }

        // 使用项目ID和岗位ID双重条件查询，确保数据一致性
        $result = $this->where([
            'project_id' => $post['project_id'],
            'project_post_id' => $postId,
            'project_identity_id' => 3, // 服务站身份
        ])->find();

        return $result ? intval($result['cost']) : 0;
    }

    /**
     * 获取指定项目的所有身份成本配置
     * @param int $projectId 项目ID
     * @return array
     */
    public function getProjectIdentityCosts($projectId)
    {
        return $this->alias('pji')
            ->join('LEFT JOIN __PROJECT_IDENTITY__ pi ON pji.project_identity_id = pi.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON pji.project_post_id = pp.id')
            ->where(['pji.project_id' => $projectId])
            ->field('pji.*, pi.name as identity_name, pp.job_name')
            ->select();
    }

    /**
     * 批量设置项目身份成本
     * @param int $projectId 项目ID
     * @param int $postId 岗位ID
     * @param array $identityCosts 身份成本数组 [identity_id => cost]
     * @return bool
     */
    public function batchSetIdentityCosts($projectId, $postId, $identityCosts)
    {
        $this->startTrans();
        try {
            foreach ($identityCosts as $identityId => $cost) {
                $existing = $this->where([
                    'project_id' => $projectId,
                    'project_post_id' => $postId,
                    'project_identity_id' => $identityId
                ])->find();

                if ($existing) {
                    // 更新现有记录
                    $this->save([
                        'id' => $existing['id'],
                        'cost' => $cost,
                        'update_time' => time()
                    ]);
                } else {
                    // 新增记录
                    $this->add([
                        'project_id' => $projectId,
                        'project_post_id' => $postId,
                        'project_identity_id' => $identityId,
                        'cost' => $cost,
                        'create_time' => time()
                    ]);
                }
            }
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }
}
