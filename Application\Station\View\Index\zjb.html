﻿<!DOCTYPE html>
<html >
<head>
<title>招就办管理系统</title>
<meta name="Keywords" content="关键字">
<meta name="Description" content="内容">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
<meta content="no-cache,must-revalidate" http-equiv="Cache-Control">
<meta content="telephone=no, address=no" name="format-detection">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta content="no-cache" http-equiv="pragma">
<meta content="0" http-equiv="expires">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"> 
<link rel="stylesheet" type="text/css" href="/static/stations/css/css.css?v={:time()}" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
<script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
<script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <style>
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
            z-index: 999;
        }

        /* 大图样式 */
        .modal-img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }

        /* 缩略图样式 */
        .thumbnail {
            width: 200px;
            height: 200px;
            cursor: pointer;
            object-fit: cover;
        }

        /* 模态框遮罩层 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        /* 确保弹窗居中显示 */
        .modal-overlay.show {
            display: flex !important;
        }

        /* 模态框内容 */
        .modal-content {
            background: white;
            padding: 32px;
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            position: relative;
            animation: modalSlide 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        }

        /* 价格配置按钮样式 */
        .price-config-btn {
            display: inline-block;
            background: rgba(255,255,255,0.9);
            color: #333;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .price-config-btn:hover {
            background: #fff;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            color: #333;
            text-decoration: none;
        }
        .price-config-container {
            padding: 12px 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 招就办照片显示样式 - 参考现有imgs样式 */
        .zjb-photos-section {
            margin-bottom: 10px;
        }

        .zjb-photo-placeholder {
            width: 100%;
            background: #f0f0f0;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }

        /* 价格变化通知弹窗样式优化 */
        .price-notify-modal {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            width: 90%;
            max-width: 400px;
            position: relative;
            animation: modalSlideIn 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .price-notify-modal .close-btn {
            position: absolute;
            top: 15px;
            right: 20px;
            width: 30px;
            height: 30px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 20px;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .price-notify-modal .close-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .modal-header {
            text-align: center;
            padding: 30px 20px 20px;
            color: white;
        }

        .notification-icon {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 28px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .modal-body {
            background: white;
            padding: 25px;
            margin: 0 15px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .greeting-text {
            font-size: 15px;
            color: #666;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .content-text {
            font-size: 14px;
            color: #333;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .time-info {
            font-size: 12px;
            color: #999;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 3px solid #667eea;
        }

        .modal-footer {
            padding: 20px 25px 25px;
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .modal-footer button {
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .btn-secondary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: white;
            color: #667eea;
            font-weight: bold;
        }

        .btn-primary:hover {
            background: #f8f9ff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
<include file="headers"/>
<div class="page0107box mb10">
        <div class="page0107">
            <div class="weap">
                <div class="boxs mb10" style="width: 100%;">
                    <div class="hd01">
                        <div class="search">
                            <form action="">
                                <input class="input" name="kwd" value="{:$kwd}" type="search" placeholder="搜索" style="width: 100%;">
                                <button class="inbtn"><i class="iconfont icon-31sousuo"></i></button>
                            </form>
                        </div>
                    </div>

                    <!-- 价格配置按钮 - 只对服务站用户显示 -->
                    <php>if ($serviceStationRow && $serviceStationRow['zsb_type'] == 1) {</php>
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 12px 15px; border-radius: 8px; margin-bottom: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="position: relative;">
                                <a href="{:U('index/price_config')}" id="priceConfigBtn" style="display: inline-block; background: rgba(255,255,255,0.9); color: #333; padding: 10px 20px; border-radius: 6px; text-decoration: none; font-size: 14px; font-weight: bold; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0,0,0,0.1); position: relative;" onmouseover="this.style.background='#fff'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.15)';" onmouseout="this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)';">
                                    <i class="iconfont icon-bianji" style="margin-right: 5px;"></i> 价格配置管理
                                    <i id="priceChangeNotifyDot" style="display: none; position: absolute; top: -5px; right: -5px; width: 12px; height: 12px; background-color: #da5352; border-radius: 50%; border: 2px solid #fff; z-index: 10;"></i>
                                </a>
                            </div>
                            <div style="color: rgba(255,255,255,0.9); font-size: 13px;">
                                统一管理招就办岗位价格
                            </div>
                        </div>
                    </div>
                    <php>}</php>

                    <div class="hd03">
                        <ul>
                            <li data-type="" class="{:empty($type) ? 'on' : ''} ">
                                <a>全部</a>
                            </li>
                            <li data-type="3" class="{:$type == 3 ? 'on' : ''} ">
                                <a>审核中</a>
                            </li>
                            <li data-type="2" class="{:$type == 2 ? 'on' : ''} ">
                                <a>未过审</a>
                            </li> 
                            <li data-type="1" class="{:$type== 1 ? 'on' : ''} ">
                                <a>已过审</a>
                            </li>
                             
                        </ul>
                    </div>   
                </div>
                <div class="bd">
                    <ul>
                        <php>if ($refCount == 0) {</php>
                        <li style="text-align: center;font-size: 13px;color: darkgray;padding: 20px 0;">
                            您当前没有完成服务站推荐加盟工作任务   
                        </li>
                        <php>} else {</php>
                            <php>if (empty($list)) {</php>
                                <li style="text-align: center;font-size: 13px;color: darkgray;padding: 20px 0;">无相关服务站数据</li>
                                <php>}else{</php>
                                <include file="list-zjb"/>
                        <php>}}</php>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="footernav">
        <div class="box">
            <ul>
 
                <li>
                    <a href="{:U('index/register', ['type' => 2])}">
                        <div class="ico"><span class="iconfont icon-jiajianzujianjiahao"></span></div>
                        <h3>添加招就办主任</h3>
                    </a>
                </li>
 
            </ul>
        </div>
        </div>
<!-- 弹窗层 -->
<div class="modal">
    <img class="modal-img">
</div>

<!-- 价格变化通知弹窗 -->
<div class="modal-overlay" id="priceChangeNotifyModal" style="display: none;">
    <div class="price-notify-modal">
        <!-- 关闭按钮 -->
        <div class="close-btn" onclick="closePriceChangeNotifyModal()">×</div>

        <!-- 弹窗头部 -->
        <div class="modal-header">
            <div class="notification-icon">
                <span style="font-size: 28px;">🔔</span>
            </div>
            <h3 class="modal-title">平台服务费比率变更通知</h3>
        </div>

        <!-- 弹窗内容 -->
        <div class="modal-body">
            <div class="greeting-text">尊敬的服务站用户，您好！</div>
            <div class="content-text">
                平台服务费比率已调整为 <span id="currentRateText" style="color: #e67e22; font-weight: bold;">--</span>，可能会影响您的招就办岗位价格配置。请及时查看并调整相关价格设置。
            </div>
            <div class="time-info" id="notifyTimeText">
                变更时间：--
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="modal-footer">
            <button class="btn-secondary" onclick="permanentClearNotify()">
                我已知晓并不再提醒
            </button>
            <button class="btn-primary" onclick="tempClearNotify()">
                我知道了
            </button>
        </div>
    </div>
</div>
</body>


<script type="text/javascript">
var kwd = '{$kwd}';
var type = '{$type}';
var url = '{:U("index/zjb")}';
$(document).ready(function(){
    var noticeSwiper = new Swiper('.noticeSwiper', {
        direction: 'vertical',
		loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    })
    var noticeSwiper = new Swiper('.page0106Swiper', {
        pagination: {
          el: ".swiper-pagination",
        },

		loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    });

    // 检查价格变化通知
    checkPriceChangeNotify();

    // 检查并显示价格变化通知弹窗
    checkAndShowZjbPriceChangeNotify();
	$(".page0107 .hd02 li").click(function(){
        $(this).siblings().removeClass("on");
       $(this).addClass("on");
   });
   $(".page0107 .hd03 li").click(function(){
       var newType = $(this).attr('data-type');
        $(this).siblings().removeClass("on");
       $(this).addClass("on");
       if (newType != type) {
           window.location.href = url + '?kwd='+kwd+"&type="+newType;
           return
       }
   });


    $('.js_kwd').blur(function () {
        var  new_kwd = $(this).val();
        if (new_kwd != kwd) {
            window.location.href = url + '?kwd='+new_kwd+"&type="+type;
            return
        }
    })

    // 获取元素
    const thumbnail = document.querySelector('.thumbnail');
    const modal = document.querySelector('.modal');
    const modalImg = document.querySelector('.modal-img');
    $("body").on('click', '.thumbnail', function () {
        modal.style.display = 'flex';
        modalImg.src = $(this).attr('src');
    })

    // 点击任意位置关闭弹窗
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // 点击价格通知弹窗遮罩层关闭弹窗
    $('#priceChangeNotifyModal').on('click', function(e) {
        if (e.target === this) {
            closePriceChangeNotifyModal();
        }
    });

});

// 检查价格变化通知
function checkPriceChangeNotify() {
    <php>if ($serviceStationRow && $serviceStationRow['zsb_type'] == 1) {</php>
    var stationId = {$serviceStationRow.id};

    $.ajax({
        url: '/index/get_price_change_notify_status',
        type: 'GET',
        data: {},
        dataType: 'json',
        success: function(response) {
            if (response.status == 1 && response.has_notify) {
                // 显示红点提醒
                $('#priceChangeNotifyDot').show();

                // 同时更新导航栏红点（如果存在）
                if ($('#zjbNavNotifyDot').length > 0) {
                    $('#zjbNavNotifyDot').show();
                }

                // 为价格配置按钮添加点击事件
                $('#priceConfigBtn').off('click.notify').on('click.notify', function(e) {
                    // 不在这里清除通知标记，让价格配置页面处理
                });
            } else {
                // 隐藏红点提醒
                $('#priceChangeNotifyDot').hide();

                // 隐藏导航栏中招就办按钮的红点（精确定位，不影响其他按钮）
                $('.nav a[href*="zjb"] .ico i').hide();

                // 同时隐藏可能存在的其他红点标识
                if ($('#zjbNavNotifyDot').length > 0) {
                    $('#zjbNavNotifyDot').hide();
                }
            }
        },
        error: function() {
            console.log('检查价格变化通知失败');
        }
    });
    <php>}</php>
}

// 清除价格变化通知（临时清除）
function clearPriceChangeNotify(stationId) {
    $.ajax({
        url: '/prime/sys/clear_price_change_notify',
        type: 'POST',
        data: {
            station_id: stationId,
            permanent: 0  // 临时清除
        },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                // 隐藏红点提醒
                $('#priceChangeNotifyDot').hide();

                // 隐藏导航栏中招就办按钮的红点（精确定位，不影响其他按钮）
                $('.nav a[href*="zjb"] .ico i').hide();

                // 同时隐藏可能存在的其他红点标识
                if ($('#zjbNavNotifyDot').length > 0) {
                    $('#zjbNavNotifyDot').hide();
                }
            }
        },
        error: function() {
            console.log('清除价格变化通知失败');
        }
    });
}

// 检查并显示招就办页面的价格变化通知弹窗
function checkAndShowZjbPriceChangeNotify() {
    <php>if ($serviceStationRow && $serviceStationRow['zsb_type'] == 1) {</php>
    var stationId = {$serviceStationRow.id};
    console.log('招就办页面 - 检查价格变化通知，服务站ID:', stationId);

    $.ajax({
        url: '/index/get_price_change_notify_status',
        type: 'GET',
        data: {},
        dataType: 'json',
        success: function(response) {
            console.log('招就办页面 - 价格变化通知查询结果:', response);
            if (response.status == 1 && response.has_notify) {
                console.log('招就办页面 - 显示价格变化通知弹窗');
                showZjbPriceChangeNotifyModal(stationId, response.rate_percent, response.change_time);
            } else {
                console.log('招就办页面 - 无需显示通知弹窗，has_notify:', response.has_notify);
            }
        },
        error: function(xhr, status, error) {
            console.log('招就办页面 - 检查价格变化通知失败:', {
                status: status,
                error: error,
                response: xhr.responseText
            });
        }
    });
    <php>} else {</php>
    console.log('招就办页面 - 非服务站用户，无需检查价格变化通知');
    <php>}</php>
}

// 显示价格变化通知弹窗
function showZjbPriceChangeNotifyModal(stationId, ratePercent, changeTime) {
    // 设置变更时间（从数据库获取实际变更时间）
    var timeText = changeTime || '未知';
    $('#notifyTimeText').text('变更时间：' + timeText);

    // 设置当前费率信息
    if (ratePercent) {
        $('#currentRateText').text(ratePercent);
    }

    // 显示弹窗 - 使用CSS类确保居中
    $('#priceChangeNotifyModal').addClass('show').fadeIn(300);

    // 存储服务站ID供后续使用
    $('#priceChangeNotifyModal').data('stationId', stationId);
}

// 关闭价格变化通知弹窗
function closePriceChangeNotifyModal() {
    $('#priceChangeNotifyModal').fadeOut(300, function() {
        $(this).removeClass('show');
    });
}

// 永久清除通知（我已知晓并不再提醒）
function permanentClearNotify() {
    var stationId = $('#priceChangeNotifyModal').data('stationId');

    $.ajax({
        url: '/index/clear_price_change_notify',
        type: 'POST',
        data: {
            station_id: stationId,
            permanent: 1  // 永久清除
        },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                $('#priceChangeNotifyModal').fadeOut(300, function() {
                    $(this).removeClass('show');
                });
                // 隐藏红点提醒
                $('#priceChangeNotifyDot').hide();

                // 隐藏导航栏中招就办按钮的红点（精确定位，不影响其他按钮）
                $('.nav a[href*="zjb"] .ico i').hide();

                // 同时隐藏可能存在的其他红点标识
                if ($('#zjbNavNotifyDot').length > 0) {
                    $('#zjbNavNotifyDot').hide();
                }

                // 设置localStorage标记，通知index页面隐藏红点（永久清除）
                localStorage.setItem('zjb_price_notify_permanent_cleared', '1');
                localStorage.setItem('zjb_price_notify_temp_cleared', '1');
                localStorage.setItem('zjb_price_notify_cleared_time', Date.now().toString());

                console.log('已永久清除价格变化通知');
            } else {
                alert('操作失败：' + response.msg);
            }
        },
        error: function() {
            alert('操作失败，请重试');
        }
    });
}

// 临时清除通知（我知道了）
function tempClearNotify() {
    var stationId = $('#priceChangeNotifyModal').data('stationId');

    $.ajax({
        url: '/index/clear_price_change_notify',
        type: 'POST',
        data: {
            station_id: stationId,
            permanent: 0  // 临时清除
        },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                $('#priceChangeNotifyModal').fadeOut(300, function() {
                    $(this).removeClass('show');
                });
                // 隐藏红点提醒
                $('#priceChangeNotifyDot').hide();

                // 隐藏导航栏中招就办按钮的红点（精确定位，不影响其他按钮）
                $('.nav a[href*="zjb"] .ico i').hide();

                // 同时隐藏可能存在的其他红点标识
                if ($('#zjbNavNotifyDot').length > 0) {
                    $('#zjbNavNotifyDot').hide();
                }

                // 设置localStorage标记，通知index页面隐藏红点
                localStorage.setItem('zjb_price_notify_temp_cleared', '1');
                localStorage.setItem('zjb_price_notify_temp_cleared_time', Date.now().toString());

                console.log('已临时清除价格变化通知');
            } else {
                alert('操作失败：' + response.msg);
            }
        },
        error: function() {
            alert('操作失败，请重试');
        }
    });
}
</script>
</body>
</html>