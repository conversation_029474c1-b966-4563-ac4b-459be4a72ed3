<include file="block/hat" />
<style>
            /* 调整原有容器间距 */
            .container {
            margin-top: 10px;  /* 增大顶部间距 */
            max-height: 500px;
            overflow-y: auto;
            padding-right: 5px; /* 防止内容被滚动条遮挡 */
            scrollbar-gutter: stable; /* 防止内容跳动 */
            scrollbar-color: #c1c1c1 transparent;  /* Firefox */
            scrollbar-width: thin;  /* Firefox */
        }

                /* 新增聊天记录样式 */
                .record-list {
            margin-bottom: 30px;
        }

        /* 消息项基础样式 */
        .message-item {
            display: flex;
            margin: 5px 0;
            max-width: 45%;
        }

        /* 对方消息（左侧） */
        .message-left {
            align-self: flex-start;
            flex-direction: row;
        }

        /* 己方消息（右侧） */
        .message-right {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

     
        /* 消息内容区域 */
        .message-content {
            max-width: calc(100% - 60px);
        }

        /* 对方消息气泡 */
        .bubble-left {
            background: #fff;
            border: 1px solid #e5e5e5;
            color: #333;
            border-radius: 12px;
            padding: 12px;
            position: relative;
            margin: 5px 0;
        }

        /* 己方消息气泡 */
        .bubble-right {
            background: #07c160;
            color: white;
            border-radius: 12px;
            padding: 12px;
            margin: 5px 0;
        }

        /* 发送者信息 */
        .sender-info {
            font-size: 14px;
            color: #888;
            margin: 4px 3px;
        }

        /* 时间样式 */
        .message-time {
            font-size: 12px;
            color: #999;
            margin: 10px 0;
        }
        img{width: 80px;}
</style>
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Moment/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
                <div class="main">

                    <div style="width: 46%;float: left;">
                        
                        <div class="panel panel-default">
                            <div class="panel-heading">{:$serviceStationRow['service_name']} {:$jobRow['name']} 简历 沟通记录</div>
                            <div class="panel-body">
                                <div class="container">

                                    {:$jobRow['need_reply'] ? '<div style="width: 46%;color: red;font-size: 14px;margin: 33px;text-align: center;">已提醒服务站回复信息</div>' : ''}

                                    <div class="record-list" id="recordList">
                                        <php>foreach ($jobList as $jobRows) {</php>
                                            <php>if ($jobRows['type'] == 2) {</php>
                                            
                                                <!-- 己方消息 -->
                                    
                                                    <!-- <img src="https://via.placeholder.com/40/07c160/fff?text=张" class="avatar" alt="用户头像">-->
                                                    
                                                        <div class="message-time" style="text-align: right;">{:date("Y年m月d日H:i:s", $jobRows['create_time'])}</div>
                                                        <div class="message-item message-right">
                                                            <div class="message-content">
                                                        <div class="sender-info" style="text-align: right;">平台</div>
                                                        <div class="bubble-right">
                                                            {:htmlspecialchars_decode($jobRows['content'])}
                                                        </div>
                                                        
                                                    </div>
                                                    </div>
                            
                                            <php>} else {</php>
                                                                <!-- 模拟聊天记录 -->
                                            <div class="message-time" style="text-align: left;">{:date("Y年m月d日H:i:s", $jobRows['create_time'])}</div>
                                            <!-- 对方消息 -->
                                            <div class="message-item message-left">
                            <!--                    <img src="https://via.placeholder.com/40" class="avatar" alt="HR头像">-->
                                                <div class="message-content">
                                                    <div class="sender-info">服务站</div>
                                                    <div class="bubble-left">
                                                        {:htmlspecialchars_decode($jobRows['content'])}
                                                    </div>
                                                </div>
                                            </div>
                            
                                            <php>}</php>
                                        <php>} </php>
                                    </div>
                                </div>

                                
                            </div>
                        </div>
                        
                    </div>

                    <div style="width: 53%;float: right;">

                    <div class="panel panel-default">
                        <div class="panel-heading">回复留言</div>
                        <div class="panel-body">
                         
                            <div class="form-group">
                                
                                <div  style="width: 95%;margin: 18px;">
                                    <textarea name="content" rows="4" class="col-sm-9 col-xs-12 richtext">{$row.content}</textarea>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                是否提醒对方回复：
                                <select name="needreply" style="margin-right: 33px;">
                                    <option value="0">不需要</option>
                                    <option value="1">需要提醒</option>
                                </select>
                                <input type="hidden" name="id" value="{$row.id}"/>
                                <input type="submit" name="submit" value="回复留言" class="btn btn-primary" style="text-align: center;" />
                            </div>
                        </div>
                        
                    </div>
                    
                </div>
            </div>
            </form>
        </div>
    </div>
</div>
<include file="block/footer" />
<script>
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
        })
    });
</script>