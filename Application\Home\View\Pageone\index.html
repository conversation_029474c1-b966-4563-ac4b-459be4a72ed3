<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="/static/pageone/styles/swiper.min.css">
    <link rel="stylesheet" href="/static/pageone/styles/main.css">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="/static/pageone/js/jquery.min.js"></script>
</head>

<body>
    <header class="header-phone d-md-none">
        <a href="" class="btn-back"></a>
        <h3>{$row.title}</h3>
        <a href="" class="btn-menu"></a>
    </header>
    <div class="alert-menu">
        <div class="over-close"></div>
        <div class="box">
            <ul class="list">
                <php>foreach ($pageList as $id => $title){ </php>
                <li><a href="{:U('pageone/index', ['id' => $id])}">{$title}</a></li>
                <php>}</php>
            </ul>
        </div>
    </div>
    <section class="uc-wrap uc-home">
        <section class="box box1" id="id1" style="padding:100px 25px 25px  25px">
            <div class="intro">
                {:htmlspecialchars_decode($row['content'])}
            </div>
        </section>

    </section>
    <script src="/static/pageone/js/swiper.min.js"></script>
    <script src="/static/pageone/js/main.js"></script>
    <script>
        $(function() {})

        new Swiper('.swiper-container1', {
            loop: true,
            observeParents: true,
            observer: true,
            pagination: {
                el: '.swiper-container1 .swiper-pagination',
                clickable: true,
            },
            autoplay: {
                delay: 2500,
                disableOnInteraction: false,
            },
        });

        new Swiper('.swiper-container2', {
            loop: true,
            observeParents: true,
            observer: true,
            direction: 'vertical',
            pagination: {
                el: '.swiper-container2 .swiper-pagination',
                clickable: true,
            },
            autoplay: {
                delay: 2500,
                disableOnInteraction: false,
            },
        });
    </script>
</body>

</html>