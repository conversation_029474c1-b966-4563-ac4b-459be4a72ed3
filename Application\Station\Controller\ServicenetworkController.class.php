<?php

namespace Station\Controller;

use Common\Controller\WController;
use Common\Controller\WstationController;
use Util\AliSms;
use Util\HwSms;

class ServicenetworkController extends WstationController
{
    public function _initialize()
    {

    }

    /**
     * 服务站list
     * @return void
     */
    public function index() {
        $kwd = I('get.kwd', '');
        $city = I('get.city', '');
        $province = I('get.province', '');
        $obj = D("ServiceStation");
        $where = [
            'status' => 1,
            'type' => 1,
        ];
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%'.$kwd.'%'];
        }
        if (!empty($province)) {
            $where['mail_address'] = ['like', '%'.$province.'%'];
        }

        if (!empty($city)) {
            $where['mail_address'] = ['like', '%'.$city.'%'];
        }
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('qualificationList', $obj->qualification);
        $this->assign('sexList', $obj->sex);
        $this->assign('categoryList', D("Project")->category);
        $this->assign('list', $list);
        $n = I('get.n', 0);
        if ($page->Current_page > 1 || $n == 1) {
            $this->display('list-index');
            exit;
        }
        $this->assign("page", $page);
        $this->display();
    }

}