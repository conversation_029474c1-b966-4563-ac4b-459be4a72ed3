<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Moment/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="row">
                    <!-- 左侧审核表单区域 -->
                    <div class="col-md-7">
                        <div class="panel panel-default">
                            <div class="panel-heading">素材审核</div>
                            <div class="panel-body">
                                <form method="post" id="form" enctype="multipart/form-data">
                                    <input type="hidden" name="id" value="{$row.id}" />
                                    <div class="form-group">
                                        <label class="control-label">创建者：</label>
                                        <div style="margin-top: 8px;">{$row.creator_name}</div>
                                    </div>
                                    <div class="form-group">
                                        <label class="control-label">当前审核者：</label>
                                        <div style="margin-top: 8px;"><span style="color: #ff6600; font-weight: bold;">{$current_reviewer}</span></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="control-label">所属项目：</label>
                                        <div style="margin-top: 8px;">{$projectList[$row['project_id']]}</div>
                                    </div>
                                    <div class="form-group">
                                        <label class="control-label">所属岗位：</label>
                                        <div style="margin-top: 8px;">{$postList[$row['post_id']]}</div>
                                    </div>
                                    <div class="form-group">
                                        <label class="control-label">创建时间：</label>
                                        <div style="margin-top: 8px;">{$row.create_time|date='Y-m-d H:i:s',###}</div>
                                    </div>
                                    <div class="form-group">
                                        <label class="control-label">内容：</label>
                                        <div id="content-display" style="margin-top: 10px;">{$row.content}</div>
                                        <textarea id="content-edit" name="content" class="form-control" rows="15" style="width: 100%; display:none;">{$row.content}</textarea>
                                    </div>
                                    
                                    <!-- 图片组件 -->
                                    <div class="js_type">
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">图片素材1</label>
                                            <div class="col-sm-9 col-xs-12">
                                                <php>echo tpl_form_field_image('img1', $row['img1'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="js_type">
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">图片素材2</label>
                                            <div class="col-sm-9 col-xs-12">
                                                <php>echo tpl_form_field_image('img2', $row['img2'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="js_type">
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">图片素材3</label>
                                            <div class="col-sm-9 col-xs-12">
                                                <php>echo tpl_form_field_image('img3', $row['img3'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="js_type">
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">图片素材4</label>
                                            <div class="col-sm-9 col-xs-12">
                                                <php>echo tpl_form_field_image('img4', $row['img4'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="js_type">
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">图片素材5</label>
                                            <div class="col-sm-9 col-xs-12">
                                                <php>echo tpl_form_field_image('img5', $row['img5'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="js_type">
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">图片素材6</label>
                                            <div class="col-sm-9 col-xs-12">
                                                <php>echo tpl_form_field_image('img6', $row['img6'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="js_type">
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">图片素材7</label>
                                            <div class="col-sm-9 col-xs-12">
                                                <php>echo tpl_form_field_image('img7', $row['img7'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="js_type">
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">图片素材8</label>
                                            <div class="col-sm-9 col-xs-12">
                                                <php>echo tpl_form_field_image('img8', $row['img8'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="js_type">
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">图片素材9</label>
                                            <div class="col-sm-9 col-xs-12">
                                                <php>echo tpl_form_field_image('img9', $row['img9'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧预览区域 -->
                    <div class="col-md-5">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">朋友圈预览</h3>
                            </div>
                            <div class="panel-body" id="moment-preview-container" style="padding: 0; overflow: hidden; border-radius: 5px;">
                                <!-- 预览区域内容将通过JS加载 -->
                            </div>
                        </div>
                        
                        <!-- 审核栏 -->
                        <div class="panel panel-default" style="margin-top: 15px;">
                            <div class="panel-heading">
                                <h3 class="panel-title">审核操作</h3>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label><input type="radio" name="review_status" value="1" checked class="review-option" /> 通过</label>
                                    <label style="margin-left: 20px;"><input type="radio" name="review_status" value="2" class="review-option" /> 修改通过</label>
                                </div>
                                <div class="form-group" style="margin-top: 15px;">
                                    <button type="button" id="submit-review" class="btn btn-primary">提交审核</button>
                                    <button type="button" class="btn btn-warning" onclick="window.history.go(-1)" style="margin-left: 10px;">返回</button>
                                    <button type="button" id="refresh-preview" class="btn btn-info" style="margin-left: 10px;">刷新预览</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />

<!-- 引入朋友圈预览相关样式 -->
<style>
    /* 朋友圈样式 */
    .moment-preview {
        background-color: #f2f2f2;
        font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
        height: 100%;
        overflow-y: auto;
    }
    
    .moment-item {
        background-color: #fff;
        margin: 12px 0;
        padding: 16px 16px 14px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    .moment-item .header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    .moment-item .avatar {
        width: 42px;
        height: 42px;
        border-radius: 4px;
        margin-right: 10px;
        background-color: #f6f6f6;
        overflow: hidden;
    }
    .moment-item .author {
        font-weight: 550;
        font-size: 15px;
        color: #576b95;
    }
    .moment-item .content {
        font-size: 15px;
        line-height: 1.5;
        margin: 10px 0;
        color: #333;
        word-wrap: break-word;
        padding: 2px 0;
        letter-spacing: 0;
        padding-left: 52px; /* 与头像左侧对齐：头像宽度42px + 右边距10px */
    }
    /* 段落样式 */
    .moment-item .content p {
        margin: 0 0 0.8em 0;
        padding: 0;
    }
    /* 最后一个段落不需要底部边距 */
    .moment-item .content p:last-child {
        margin-bottom: 0;
    }
    /* 特殊标记样式 */
    .moment-item .content .emoji {
        display: inline-block;
        vertical-align: -0.1em;
        width: 1.2em;
        height: 1.2em;
        margin: 0 0.1em;
    }
    /* 表情符号样式 */
    .moment-item .content .checkmark {
        color: #07c160;
        font-weight: bold;
    }
    .moment-item .footer {
        display: flex;
        justify-content: space-between;
        color: #999;
        font-size: 13px;
        margin-top: 12px;
        padding-top: 10px;
        border-top: 1px solid #f1f1f1;
    }
    .moment-item .time {
        color: #b2b2b2;
        font-size: 14px;
    }
    .moment-item .actions {
        display: flex;
    }
    .moment-item .action-btn {
        color: #576b95;
        margin-left: 15px;
        text-decoration: none;
        font-size: 14px;
    }
    /* 图片样式 */
    .moment-item .images {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 52px; /* 与头像左侧对齐 */
    }
    /* 单张图片样式 */
    .moment-item .images.single-image .img-box {
        width: 66%;
        max-width: 200px;
        position: relative;
        margin-right: 0;
        margin-bottom: 0;
        padding-top: 66%;
    }
    /* 两张图片样式 */
    .moment-item .images.two-images .img-box {
        width: 48%;
        margin-right: 4%;
        position: relative;
        margin-bottom: 0;
        padding-top: 48%;
    }
    .moment-item .images.two-images .img-box:nth-child(2n) {
        margin-right: 0;
    }
    /* 三张及以上图片样式 */
    .moment-item .img-box {
        width: 32%;
        margin-right: 2%;
        margin-bottom: 6px;
        position: relative;
        padding-top: 32%; /* 保持宽高比 */
    }
    .moment-item .img-box:nth-child(3n) {
        margin-right: 0;
    }
    .moment-item .img-box img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 3px;
    }
    /* 全文链接样式优化 */
    .read-more {
        color: #576b95;
        margin: 0;
        display: block;
        font-size: 15px;
        line-height: 1.4;
        padding: 8px 0;
        font-weight: 400;
        margin-left: 52px; /* 与头像左侧对齐 */
    }
    /* 无换行长文本缩略样式 */
    .content-truncated {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }
    /* 多行文本缩略样式 */
    .content-truncated-multi {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 5;
        -webkit-box-orient: vertical;
        max-height: calc(1.5em * 5);
    }
    
    /* 图片查看大图功能样式 */
    .img-preview-wrapper {
        position: relative;
        display: inline-block;
        overflow: hidden;
        cursor: pointer;
    }
    
    .img-zoom-icon {
        position: absolute;
        top: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0 0 0 4px;
        opacity: 0.7;
        transition: opacity 0.3s;
    }
    
    .img-preview-wrapper:hover .img-zoom-icon {
        opacity: 1;
    }
    
    /* 大图预览模态框样式 */
    .img-preview-modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.85);
        padding: 50px;
        box-sizing: border-box;
        align-items: center;
        justify-content: center;
    }
    
    .img-preview-modal-content {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    }
    
    .img-preview-close {
        position: absolute;
        top: 20px;
        right: 25px;
        color: #f1f1f1;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }
    
    .img-preview-close:hover {
        color: #bbb;
    }
    
    .input-group{
        width: 100%;
    }

</style>

<script>
$(function(){
    // 预览区域初始化
    initPreview();
    
    // 统一处理编辑状态的函数
    function setReviewEditState(isEditable) {
        if (isEditable) { // 修改通过状态
            $('#content-display').hide();
            $('#content-edit').show().focus(); // 显示并聚焦内容编辑框
            $('#form .js_type .input-group-btn').show(); // 显示所有图片选择按钮的容器
            $('#form').attr('enctype', 'multipart/form-data'); // 启用表单文件上传
        } else { // 通过状态 (只读)
            $('#content-display').show();
            $('#content-edit').hide();
            $('#form .js_type .input-group-btn').hide(); // 隐藏所有图片选择按钮的容器
            $('#form').attr('enctype', ''); // 恢复表单默认enctype
        }
    }

    // 监听审核选项变化
    $('.review-option').change(function(){
        var selectedValue = $('input[name="review_status"]:checked').val();
        setReviewEditState(selectedValue == '2');
    });
    
    // 监听内容输入
    $('#content-edit').on('input', function() {
        updateContentPreview();
    });
    
    // 点击提交审核按钮
    $('#submit-review').click(function() {
        var reviewStatus = $('input[name="review_status"]:checked').val();
        var form = $('#form');

        // 清理旧的隐藏字段，以防重复添加
        form.find('input[type="hidden"][name="review_status"]').remove();
        
        // 将当前选中的审核状态作为隐藏字段添加到表单中
        form.append('<input type="hidden" name="review_status" value="' + reviewStatus + '">');
        
        // 提交表单
        form.submit();
    });
    
    // 点击刷新预览按钮
    $('#refresh-preview').click(function() {
        initPreview();
        alert('预览已刷新');
    });
    
    // 初始化预览区域
    function initPreview() {
        var previewContainer = $('#moment-preview-container');
        if(!previewContainer.length) return;
        
        // 添加模拟手机界面
        var previewHtml = `
            <!-- 模拟手机界面 -->
            <div style="background-color: #f7f7f7; width: 393px; margin: 0 auto; box-shadow: 0 0 10px rgba(0,0,0,0.1); height: 600px; position: relative; overflow-y: auto; overflow-x: hidden; transform-origin: top center; border: 1px solid #ddd; border-radius: 18px;">
                <!-- 手机状态栏 -->
                <div class="status-bar notch-design" style="height: 32px; background-color: #000; padding: 0 16px; display: flex; justify-content: space-between; align-items: center; border-top-left-radius: 18px; border-top-right-radius: 18px; position: relative;">
                    <!-- 刘海屏设计 -->
                    <div style="position: absolute; top: 0; left: 50%; transform: translateX(-50%); width: 120px; height: 28px; background-color: #000; border-bottom-left-radius: 14px; border-bottom-right-radius: 14px; display: flex; justify-content: center; align-items: center;">
                        <div style="width: 8px; height: 8px; background-color: #333; border-radius: 50%; margin-right: 30px;"></div> <!-- 前置摄像头 -->
                        <div style="width: 50px; height: 4px; background-color: #333; border-radius: 2px;"></div> <!-- 听筒 -->
                    </div>
                    <span style="color: #fff; font-size: 14px; margin-left: 8px;">10:30</span>
                    <div style="display: flex; align-items: center; margin-right: 8px;">
                        <span style="color: #fff; font-size: 12px; margin-right: 8px;">4G</span>
                        <div style="width: 25px; height: 12px; border: 1px solid #fff; border-radius: 3px; position: relative; display: flex; align-items: center;">
                            <div style="height: 8px; width: 20px; background-color: #fff; margin-left: 2px; border-radius: 1px;"></div>
                            <div style="height: 8px; width: 2px; background-color: #fff; position: absolute; right: -3px; border-radius: 1px;"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 朋友圈导航栏 -->
                <div class="nav-bar" style="height: 48px; background-color: #fff; display: flex; justify-content: center; align-items: center; position: relative; border-bottom: 1px solid #eee;">
                    <div style="position: absolute; left: 16px; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                    </div>
                    <div style="font-size: 18px; font-weight: 500;">朋友圈</div>
                    <div style="position: absolute; right: 16px; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <circle cx="12" cy="10" r="3"></circle>
                            <path d="M7 20.662V19c0-1 .1-1.9 1-2h8c.9.1 1 1 1 2v1.662"></path>
                        </svg>
                    </div>
                </div>
                
                <!-- 朋友圈内容预览 -->
                <div id="moment-preview" class="moment-preview">
                    <div class="moment-item">
                        <div class="header">
                            <img src="/static/stations/images/logozcgk.png" class="avatar">
                            <div class="author" id="preview-project">${getProjectName()}</div>
                        </div>
                        <div class="content" id="preview-content"></div>
                        <div class="images" id="preview-images"></div>
                        <div class="footer">
                            <div class="time">刚刚</div>
                            <div class="actions">
                                <a href="javascript:void(0);" class="action-btn">赞</a>
                                <a href="javascript:void(0);" class="action-btn">评论</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        previewContainer.html(previewHtml);
        
        // 初始化内容和图片
        updateContentPreview();
        
        // 特别确保updateImagesPreview在DOM完全加载后调用
        setTimeout(function() {
            updateImagesPreview();
            console.log('延迟执行图片预览更新');
        }, 500);
        
        // 添加定期刷新
        setInterval(function() {
            updateImagesPreview();
        }, 3000);
    }
    
    // 获取项目名称
    function getProjectName() {
        try {
            var projectName = '{$projectList[$row["project_id"]]}';
            return projectName || '中才国科';
        } catch(e) {
            return '中才国科';
        }
    }
    
    // 更新内容预览
    function updateContentPreview() {
        var content;
        var selectedValue = $('input[name="review_status"]:checked').val();
        
        if(selectedValue == '2') { // 修改通过 - 使用编辑框内容
            content = $('#content-edit').val();
        } else { // 通过 - 使用原始内容
            content = $('#content-display').html();
        }
        
        if (!content) {
            content = '暂无内容';
        }
        
        // 处理内容格式
        if(typeof content === 'string' && !content.startsWith('<p>')) {
            // 处理换行和段落
            content = content.replace(/\n\n+/g, '</p><p>');
            content = content.replace(/\n/g, '<br>');
            content = '<p>' + content + '</p>';
        }
        
        // 处理emoji
        var emojiMap = {
            '✅': '<span class="checkmark">✓</span>',
            '👉': '<span class="emoji">👉</span>',
            '🔥': '<span class="emoji">🔥</span>',
            '⭐': '<span class="emoji">⭐</span>',
            '📢': '<span class="emoji">📢</span>',
            '💰': '<span class="emoji">💰</span>',
            '🌟': '<span class="emoji">🌟</span>'
        };
        
        for (var emoji in emojiMap) {
            if (emojiMap.hasOwnProperty(emoji)) {
                content = content.replace(new RegExp(emoji, 'g'), emojiMap[emoji]);
            }
        }
        
        // 更新预览内容
        var previewContent = document.getElementById('preview-content');
        if(previewContent) {
            previewContent.innerHTML = content;
        }
    }
    
    // 更新图片预览
    function updateImagesPreview() {
        var previewImagesContainer = document.getElementById('preview-images');
        if (!previewImagesContainer) return;
        
        // 清空预览容器
        previewImagesContainer.innerHTML = '';
        
        // 收集所有图片
        var images = [];
        
        // 方法1：查找所有img-responsive图片
        document.querySelectorAll('.img-responsive.img-thumbnail').forEach(function(img) {
            if (img.src && img.src.indexOf('nopic.jpg') === -1 && img.src.indexOf('icon-plus') === -1) {
                images.push(img.src);
            }
        });
        
        // 方法2：从隐藏输入字段获取值
        if (images.length === 0) {
            for (var i = 1; i <= 9; i++) {
                var inputField = document.querySelector('input[name="img' + i + '"]');
                if (inputField && inputField.value) {
                    var imgUrl = inputField.value;
                    if (imgUrl.indexOf('http') !== 0) {
                        imgUrl = 'http://we.zhongcaiguoke.com/' + imgUrl;
                    }
                    images.push(imgUrl);
                }
            }
        }
        
        // 根据图片数量添加样式
        if (images.length === 1) {
            previewImagesContainer.className = 'images single-image';
        } else if (images.length === 2) {
            previewImagesContainer.className = 'images two-images';
        } else {
            previewImagesContainer.className = 'images';
        }
        
        // 添加图片到预览
        for (var i = 0; i < images.length; i++) {
            var imgBox = document.createElement('div');
            imgBox.className = 'img-box';
            
            var img = document.createElement('img');
            img.src = images[i];
            
            // 错误处理
            img.onerror = function() {
                this.src = '/static/stations/images/logozcgk.png';
                this.onerror = null; // 防止无限循环
            };
            
            imgBox.appendChild(img);
            previewImagesContainer.appendChild(imgBox);
        }
        
        console.log('预览图片更新完成，共找到', images.length, '张图片');
    }
    
    // 页面加载时，根据默认选中的审核选项设置初始状态
    var initialSelectedValue = $('input[name="review_status"]:checked').val();
    setReviewEditState(initialSelectedValue == '2');
});
</script> 

<!-- 单独引入imageCompressor工具 -->
<script>
// 等待页面加载完成后，使用原生DOM方法添加图片压缩功能
window.addEventListener('DOMContentLoaded', function() {
    // 确保完成所有DOM加载后再执行
    setTimeout(function() {
        // 加载依赖模块
        require(['util', 'imageCompressor'], function(util, imageCompressor) {
            // 使用原生JavaScript，避免jQuery相关问题
            var MAX_IMAGE_SIZE = 100 * 1024;
            var compressedResults = {};
            window.pendingUploads = {};
            
            // 设置图片压缩处理
            function setupImageCompression() {
                var fileInputs = document.querySelectorAll('input[type="file"]');
                
                fileInputs.forEach(function(input) {
                    // 防止重复设置
                    if (input.getAttribute('data-compression-setup')) return;
                    input.setAttribute('data-compression-setup', 'true');
                    
                    // 获取原始onchange属性
                    var originalOnChange = input.getAttribute('onchange');
                    input.removeAttribute('onchange');
                    
                    // 添加新的事件处理器
                    input.addEventListener('change', function(e) {
                        var file = this.files[0];
                        if (!file) return;
                        
                        // 检查文件类型是否为图片
                        if (!file.type.match(/^image\//)) {
                            if (originalOnChange) {
                                // 使用Function构造器创建一个安全的函数
                                try {
                                    var func = new Function('event', 'this', originalOnChange);
                                    func.call(this, e, this);
                                } catch(err) {
                                    console.error('执行原始onchange失败:', err);
                                }
                            }
                            return;
                        }
                        
                        // 标记处理中状态
                        var uploadId = 'upload-' + Date.now();
                        window.pendingUploads[uploadId] = true;
                        
                        var self = this;
                        var inputName = this.getAttribute('name');
                        
                        // 压缩图片
                        imageCompressor.compressImage(file, MAX_IMAGE_SIZE, function(result) {
                            compressedResults[inputName] = result;
                            
                            // 清除处理状态
                            delete window.pendingUploads[uploadId];
                            
                            // 如果图片被压缩，显示压缩前后对比
                            if (result.compressed) {
                                setTimeout(function() {
                                    imageCompressor.showComparisonModal(result);
                                }, 100);
                                
                                try {
                                    // 替换文件输入框中的文件
                                    var dataTransfer = new DataTransfer();
                                    dataTransfer.items.add(result.file);
                                    self.files = dataTransfer.files;
                                } catch (err) {
                                    console.error('替换文件失败:', err);
                                }
                            }
                            
                            // 安全执行原始的onchange
                            setTimeout(function() {
                                if (originalOnChange) {
                                    try {
                                        var func = new Function('event', 'this', originalOnChange);
                                        func.call(self, {type: 'change'}, self);
                                    } catch (err) {
                                        console.error('执行原始onchange失败:', err);
                                        
                                        // 强制清理所有模态框，避免界面卡住
                                        $('.modal').modal('hide');
                                        $('.modal-backdrop').remove();
                                        $('body').removeClass('modal-open').css('padding-right', '');
                                    }
                                }
                            }, 200);
                        });
                    });
                });
            }
            
            // 初始设置
            setupImageCompression();
            
            // 监视DOM变化，处理新添加的文件输入框
            var observer = new MutationObserver(function(mutations) {
                var hasNewFileInputs = false;
                
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                        for (var i = 0; i < mutation.addedNodes.length; i++) {
                            var node = mutation.addedNodes[i];
                            if (node.nodeType !== 1) continue; // 只处理元素节点
                            
                            if (node.tagName === 'INPUT' && node.type === 'file') {
                                hasNewFileInputs = true;
                                break;
                            }
                            
                            if (node.querySelector && node.querySelector('input[type="file"]')) {
                                hasNewFileInputs = true;
                                break;
                            }
                        }
                    }
                });
                
                if (hasNewFileInputs) {
                    setTimeout(setupImageCompression, 50);
                }
            });
            
            // 观察整个文档的变化
            observer.observe(document.body, { 
                childList: true, 
                subtree: true 
            });
            
            // 处理表单提交
            document.querySelectorAll('form').forEach(function(form) {
                form.addEventListener('submit', function(e) {
                    if (Object.keys(window.pendingUploads).length > 0) {
                        e.preventDefault();
                        util.message('图片正在处理中，请稍候再提交', '', 'info');
                        return false;
                    }
                });
            });
        });
    }, 100);
});
</script> 

<!-- 添加图片查看大图功能 -->
<script>
$(function() {
    // 创建模态框
    if (!document.getElementById('imgPreviewModal')) {
        var modal = document.createElement('div');
        modal.id = 'imgPreviewModal';
        modal.className = 'img-preview-modal';
        modal.innerHTML = `
            <span class="img-preview-close">&times;</span>
            <img class="img-preview-modal-content" id="imgPreviewModalContent">
        `;
        document.body.appendChild(modal);
        
        // 添加关闭事件
        document.querySelector('.img-preview-close').addEventListener('click', function() {
            modal.style.display = 'none';
        });
        
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    }
    
    // 为所有图片添加放大图标和点击事件
    function setupImageZoom() {
        // 选择所有缩略图（表单中的图片预览）
        var thumbnails = document.querySelectorAll('.img-responsive.img-thumbnail');
        
        thumbnails.forEach(function(img) {
            var parent = img.parentNode;
            
            // 避免重复添加
            if (parent.classList.contains('img-preview-wrapper')) {
                return;
            }
            
            // 创建包装容器并添加放大图标
            var wrapper = document.createElement('div');
            wrapper.className = 'img-preview-wrapper';
            parent.insertBefore(wrapper, img);
            wrapper.appendChild(img);
            
            var zoomIcon = document.createElement('div');
            zoomIcon.className = 'img-zoom-icon';
            zoomIcon.innerHTML = '<i class="fa fa-search-plus"></i>';
            wrapper.appendChild(zoomIcon);
            
            // 添加点击事件显示大图
            wrapper.addEventListener('click', function() {
                var modal = document.getElementById('imgPreviewModal');
                var modalImg = document.getElementById('imgPreviewModalContent');
                modal.style.display = 'flex';
                modalImg.src = img.src;
            });
        });
    }
    
    // 初始调用
    setupImageZoom();
    
    // 监听DOM变化，处理动态添加的图片
    var observer = new MutationObserver(function(mutations) {
        var needsRefresh = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                for (var i = 0; i < mutation.addedNodes.length; i++) {
                    var node = mutation.addedNodes[i];
                    if (node.nodeType !== 1) continue; // 只处理元素节点
                    
                    if (node.classList && node.classList.contains('img-thumbnail')) {
                        needsRefresh = true;
                        break;
                    }
                    
                    if (node.querySelector && node.querySelector('.img-thumbnail')) {
                        needsRefresh = true;
                        break;
                    }
                }
            }
        });
        
        if (needsRefresh) {
            setTimeout(setupImageZoom, 50);
        }
    });
    
    // 观察整个文档的变化
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
</script> 

<script>
require(['primeImageCompressor'], function(primeImageCompressor) {
    primeImageCompressor.init();
});
</script> 