<include file="block/hat" />
<script type="text/javascript" src="__ROOT__/static/js/lib/jquery-ui-1.10.3.min.js"></script>

<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<style type='text/css'>
				/* 重置和基础样式 */
				* {
					box-sizing: border-box;
				}

				body {
					font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
					line-height: 1.6;
					color: #2d3748;
				}

				.users-edit-container {
					padding: 2rem;
					background: #f7fafc;
					min-height: 100vh;
					font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
				}

				/* 现代化标签页 */
				.users-tabs {
					background: white;
					border-radius: 1rem 1rem 0 0;
					box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					border-bottom: none;
					overflow: hidden;
					margin-bottom: 0;
				}

				.users-tab-list {
					display: flex;
					margin: 0;
					padding: 0;
					list-style: none;
				}

				.users-tab-item {
					flex: 1;
				}

				.users-tab-link {
					display: block;
					padding: 1.5rem 2rem;
					color: #4a5568;
					text-decoration: none;
					font-weight: 600;
					font-size: 1.5rem;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					border-right: 1px solid #e2e8f0;
					position: relative;
					text-align: center;
				}

				.users-tab-item:last-child .users-tab-link {
					border-right: none;
				}

				.users-tab-link:hover {
					background: #f7fafc;
					color: #667eea;
					text-decoration: none;
				}

				.users-tab-link.active {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.users-tab-link.active::after {
					content: '';
					position: absolute;
					bottom: -1px;
					left: 0;
					right: 0;
					height: 2px;
					background: white;
				}

				/* 页面头部 */
				.users-page-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin: 2rem 0;
					flex-wrap: wrap;
					gap: 1.5rem;
				}

				.users-page-title-section {
					display: flex;
					flex-direction: column;
					gap: 0.5rem;
				}

				.users-page-title {
					font-size: 2rem;
					font-weight: 700;
					color: #1a202c;
					margin: 0;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.users-page-subtitle {
					color: #4a5568;
					font-size: 1.5rem;
					margin: 0;
				}

				.users-page-actions {
					display: flex;
					gap: 1rem;
					flex-wrap: wrap;
				}

				/* 现代化表单卡片 */
				.users-form-card {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					overflow: hidden;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					margin-bottom: 1.5rem;
				}

				.users-form-card::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4px;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				}

				.users-form-header {
					padding: 2rem 2rem 1rem 2rem;
					border-bottom: 1px solid #f1f5f9;
				}

				.users-form-title {
					font-size: 1.5rem;
					font-weight: 600;
					color: #1a202c;
					margin: 0;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.users-form-description {
					color: #4a5568;
					font-size: 1.5rem;
					margin: 0.5rem 0 0 0;
				}

				.users-form-body {
					padding: 2rem;
				}

				/* 现代化表单组 */
				.users-form-group {
					margin-bottom: 2rem;
				}

				.users-form-label {
					display: block;
					font-weight: 600;
					color: #2d3748;
					margin-bottom: 0.75rem;
					font-size: 1.5rem;
					display: flex;
					align-items: center;
					gap: 0.5rem;
				}

				.users-form-label .required {
					color: #f56565;
					font-weight: 700;
				}

				.users-form-control {
					width: 100%;
					padding: 0.75rem 1rem;
					border: 2px solid #e2e8f0;
					border-radius: 0.5rem;
					font-size: 1.5rem;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					background: white;
					font-family: inherit;
				}

				.users-form-control:focus {
					outline: none;
					border-color: #667eea;
					box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
					background: #fafafa;
				}

				.users-form-control:hover {
					border-color: #cbd5e0;
				}

				.users-select {
					background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
					background-position: right 0.5rem center;
					background-repeat: no-repeat;
					background-size: 1.5em 1.5em;
					padding-right: 2.5rem;
					appearance: none;
				}

				/* 现代化单选按钮 */
				.users-radio-group {
					display: flex;
					gap: 1.5rem;
					flex-wrap: wrap;
				}

				.users-radio-item {
					display: flex;
					align-items: center;
					gap: 0.75rem;
					padding: 0.75rem 1rem;
					border: 2px solid #e2e8f0;
					border-radius: 0.5rem;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					background: white;
					min-width: 120px;
				}

				.users-radio-item:hover {
					border-color: #667eea;
					background: #f7fafc;
				}

				.users-radio-item.active {
					border-color: #667eea;
					background: rgba(102, 126, 234, 0.1);
				}

				.users-radio-input {
					width: 1.25rem;
					height: 1.25rem;
					border: 2px solid #e2e8f0;
					border-radius: 50%;
					position: relative;
					appearance: none;
					cursor: pointer;
					transition: all 0.3s ease;
				}

				.users-radio-input:checked {
					border-color: #667eea;
					background: #667eea;
				}

				.users-radio-input:checked::after {
					content: '';
					position: absolute;
					top: 50%;
					left: 50%;
					width: 6px;
					height: 6px;
					border-radius: 50%;
					background: white;
					transform: translate(-50%, -50%);
				}

				.users-radio-label {
					font-weight: 500;
					color: #2d3748;
					cursor: pointer;
					font-size: 1.5rem;
				}

				/* 表单帮助文本 */
				.users-help-text {
					font-size: 1.5rem;
					color: #718096;
					margin-top: 0.5rem;
				}

				.users-error-text {
					font-size: 1.5rem;
					color: #f56565;
					margin-top: 0.5rem;
				}

				/* 现代化按钮 */
				.users-btn {
					display: inline-flex;
					align-items: center;
					justify-content: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					border: none;
					border-radius: 0.5rem;
					font-weight: 600;
					font-size: 1.5rem;
					text-decoration: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					overflow: hidden;
					font-family: inherit;
				}

				.users-btn:before {
					content: '';
					position: absolute;
					top: 0;
					left: -100%;
					width: 100%;
					height: 100%;
					background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.users-btn:hover:before {
					left: 100%;
				}

				.users-btn-primary {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.users-btn-primary:hover {
					background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
					transform: translateY(-1px);
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					color: white;
					text-decoration: none;
				}

				.users-btn-secondary {
					background: #f1f5f9;
					color: #4a5568;
					border: 1px solid #e2e8f0;
				}

				.users-btn-secondary:hover {
					background: #e2e8f0;
					transform: translateY(-1px);
					color: #2d3748;
					text-decoration: none;
				}

				.users-btn-lg {
					padding: 1rem 2rem;
					font-size: 1.5rem;
				}

				/* 表单操作区域 */
				.users-form-actions {
					padding: 1.5rem 2rem 2rem 2rem;
					background: #f7fafc;
					border-top: 1px solid #e2e8f0;
					display: flex;
					gap: 1rem;
					justify-content: flex-end;
					flex-wrap: wrap;
				}

				/* 图片上传区域 */
				.users-image-upload {
					background: #f7fafc;
					border: 2px dashed #cbd5e0;
					border-radius: 0.5rem;
					padding: 2rem;
					text-align: center;
					transition: all 0.3s ease;
					cursor: pointer;
				}

				.users-image-upload:hover {
					border-color: #667eea;
					background: rgba(102, 126, 234, 0.05);
				}

				.users-image-upload.has-image {
					border-style: solid;
					border-color: #e2e8f0;
					background: white;
				}

				.users-image-preview {
					max-width: 200px;
					max-height: 200px;
					border-radius: 0.5rem;
					box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				}

				.users-upload-text {
					color: #4a5568;
					font-size: 1.5rem;
					margin-top: 1rem;
				}

				.users-upload-icon {
					font-size: 3rem;
					color: #a0aec0;
					margin-bottom: 1rem;
				}

				/* 表单分组 */
				.users-form-section {
					margin-bottom: 3rem;
				}

				.users-section-title {
					font-size: 1.5rem;
					font-weight: 600;
					color: #1a202c;
					margin-bottom: 1.5rem;
					padding-bottom: 0.5rem;
					border-bottom: 2px solid #e2e8f0;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				/* 响应式设计 */
				@media (max-width: 768px) {
					.users-edit-container {
						padding: 1.5rem;
					}

					.users-page-header {
						flex-direction: column;
						align-items: flex-start;
					}

					.users-page-title {
						font-size: 1.5rem;
					}

					.users-form-header,
					.users-form-body,
					.users-form-actions {
						padding: 1.5rem;
					}

					.users-radio-group {
						flex-direction: column;
					}

					.users-radio-item {
						min-width: auto;
					}

					.users-form-actions {
						flex-direction: column;
					}

					.users-btn {
						width: 100%;
						justify-content: center;
					}
				}

				/* 动画效果 */
				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(20px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}

				.users-fade-in {
					animation: fadeInUp 0.5s ease-out;
				}

				.users-fade-in-delay-1 {
					animation: fadeInUp 0.5s ease-out 0.1s both;
				}

				.users-fade-in-delay-2 {
					animation: fadeInUp 0.5s ease-out 0.2s both;
				}

				/* 表单验证状态 */
				.users-form-control.error {
					border-color: #f56565;
					box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
				}

				.users-form-control.success {
					border-color: #48bb78;
					box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
				}

				/* 加载状态 */
				.users-btn.loading {
					pointer-events: none;
					opacity: 0.7;
				}

				.users-btn.loading::after {
					content: '';
					position: absolute;
					width: 16px;
					height: 16px;
					margin: auto;
					border: 2px solid transparent;
					border-top-color: currentColor;
					border-radius: 50%;
					animation: spin 1s linear infinite;
				}

				@keyframes spin {
					0% { transform: rotate(0deg); }
					100% { transform: rotate(360deg); }
				}

				/* 表单网格布局 */
				.users-form-grid {
					display: grid;
					grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
					gap: 2rem;
				}

				.users-form-grid-full {
					grid-column: 1 / -1;
				}
			</style>

			<div class="users-edit-container users-fade-in">
				<!-- 现代化标签页 -->
				<div class="users-tabs">
					<ul class="users-tab-list">
						<li class="users-tab-item">
							<a href="{:U('users/edit')}" class="users-tab-link <php>if(ACTION_NAME=='edit' && !I('get.id')) echo 'active'</php>">
								<i class="fa fa-plus"></i>
								添加人员
							</a>
						</li>
						<li class="users-tab-item">
							<a href="{:U('users/index')}" class="users-tab-link <php>if(ACTION_NAME=='index') echo 'active'</php>">
								<i class="fa fa-users"></i>
								人员列表
							</a>
						</li>
					</ul>
				</div>

				<!-- 页面头部 -->
				<div class="users-page-header" style="margin-top: 2rem;">
					<div class="users-page-title-section">
						<h1 class="users-page-title">
							<i class="fa fa-<php>echo I('get.id') ? 'edit' : 'plus';</php>"></i>
							<php>echo I('get.id') ? '编辑人员' : '添加人员';</php>
						</h1>
						<p class="users-page-subtitle">
							<php>echo I('get.id') ? '修改人员信息和权限配置' : '创建新的人员账户并设置基本信息';</php>
						</p>
					</div>
					<div class="users-page-actions">
						<a href="{:U('users/index')}" class="users-btn users-btn-secondary">
							<i class="fa fa-arrow-left"></i>
							返回列表
						</a>
					</div>
				</div>

				<!-- 现代化表单 -->
				<form action="" method="post" class="js-ajax-form" enctype="multipart/form-data" id="users-form">
					<!-- 基本信息卡片 -->
					<div class="users-form-card users-fade-in-delay-1">
						<div class="users-form-header">
							<h2 class="users-form-title">
								<i class="fa fa-user"></i>
								基本信息
							</h2>
							<p class="users-form-description">
								请填写人员的基本账户信息，带 <span style="color: #f56565;">*</span> 的字段为必填项
							</p>
						</div>

						<div class="users-form-body">
							<div class="users-form-grid">
								<!-- 用户账号 -->
								<div class="users-form-group">
									<label class="users-form-label">
										<i class="fa fa-user"></i>
										用户账号
										<span class="required">*</span>
									</label>
									<input type="text"
										   name="username"
										   class="users-form-control"
										   value="{$row.username}"
										   placeholder="请输入用户账号"
										   required />
									<div class="users-help-text">用户登录系统时使用的账号名称</div>
								</div>

								<!-- 用户代码 -->
								<div class="users-form-group">
									<label class="users-form-label">
										<i class="fa fa-code"></i>
										用户代码
										<span class="required">*</span>
									</label>
									<input type="text"
										   name="letter"
										   class="users-form-control"
										   value="{$row.letter}"
										   placeholder="请输入用户代码"
										   required />
									<div class="users-help-text">用于系统内部识别的唯一代码</div>
								</div>

								<!-- 手机号码 -->
								<div class="users-form-group">
									<label class="users-form-label">
										<i class="fa fa-phone"></i>
										手机号码
										<span class="required">*</span>
									</label>
									<input type="tel"
										   name="mobile"
										   class="users-form-control"
										   value="{$row.mobile}"
										   placeholder="请输入手机号码"
										   required />
									<div class="users-help-text">用于接收系统通知和验证码</div>
								</div>

								<!-- 密码 -->
								<div class="users-form-group">
									<label class="users-form-label">
										<i class="fa fa-lock"></i>
										登录密码
										<span class="required">*</span>
									</label>
									<input type="password"
										   name="pwd"
										   class="users-form-control"
										   value=""
										   placeholder="<php>echo I('get.id') ? '留空则不修改密码' : '请输入登录密码';</php>" />
									<div class="users-help-text">
										<php>echo I('get.id') ? '如需修改密码请输入新密码，否则留空' : '用户登录系统时使用的密码';</php>
									</div>
								</div>

								<!-- 账户状态 -->
								<div class="users-form-group users-form-grid-full">
									<label class="users-form-label">
										<i class="fa fa-toggle-on"></i>
										账户状态
									</label>
									<div class="users-radio-group">
										<label class="users-radio-item <php>if($row['status']) echo 'active';</php>">
											<input type="radio"
												   name="status"
												   value="1"
												   class="users-radio-input"
												   <php>if($row['status']) echo 'checked';</php> />
											<span class="users-radio-label">
												<i class="fa fa-check-circle" style="color: #48bb78;"></i>
												正常
											</span>
										</label>
										<label class="users-radio-item <php>if(!$row['status']) echo 'active';</php>">
											<input type="radio"
												   name="status"
												   value="0"
												   class="users-radio-input"
												   <php>if(!$row['status']) echo 'checked';</php> />
											<span class="users-radio-label">
												<i class="fa fa-times-circle" style="color: #f56565;"></i>
												禁用
											</span>
										</label>
									</div>
									<div class="users-help-text">禁用的账户将无法登录系统</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 详细信息卡片 -->
					<div class="users-form-card users-fade-in-delay-2">
						<div class="users-form-header">
							<h2 class="users-form-title">
								<i class="fa fa-info-circle"></i>
								详细信息
							</h2>
							<p class="users-form-description">
								完善人员的详细信息，有助于更好地管理和联系
							</p>
						</div>

						<div class="users-form-body">
							<div class="users-form-grid">
								<!-- 部门职位 -->
								<div class="users-form-group">
									<label class="users-form-label">
										<i class="fa fa-briefcase"></i>
										部门职位
										<span class="required">*</span>
									</label>
									<input type="text"
										   name="position"
										   class="users-form-control"
										   value="{$row.position}"
										   placeholder="请输入职位名称"
										   required />
									<div class="users-help-text">在部门中担任的具体职位</div>
								</div>

								<!-- 所属部门 -->
								<div class="users-form-group">
									<label class="users-form-label">
										<i class="fa fa-building"></i>
										所属部门
									</label>
									<select name="role_id" class="users-form-control users-select">
										<php>foreach($roles as $v) {</php>
										<option value="{:$v['id']}" {:$v['id'] == $row['role_id'] ? 'selected' : ''}>{$v.name}</option>
										<php>}</php>
									</select>
									<div class="users-help-text">选择人员所属的部门</div>
								</div>

								<!-- 身份证号 -->
								<div class="users-form-group">
									<label class="users-form-label">
										<i class="fa fa-id-card"></i>
										身份证号
										<span class="required">*</span>
									</label>
									<input type="text"
										   name="card"
										   class="users-form-control"
										   value="{$row.card}"
										   placeholder="请输入身份证号码"
										   maxlength="18"
										   required />
									<div class="users-help-text">18位身份证号码，用于身份验证</div>
								</div>

								<!-- 邮箱地址 -->
								<div class="users-form-group">
									<label class="users-form-label">
										<i class="fa fa-envelope"></i>
										邮箱地址
										<span class="required">*</span>
									</label>
									<input type="email"
										   name="emails"
										   class="users-form-control"
										   value="{$row.emails}"
										   placeholder="请输入邮箱地址"
										   required />
									<div class="users-help-text">用于接收系统邮件通知</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 图片信息卡片 -->
					<div class="users-form-card users-fade-in-delay-2">
						<div class="users-form-header">
							<h2 class="users-form-title">
								<i class="fa fa-camera"></i>
								图片信息
							</h2>
							<p class="users-form-description">
								上传证件照和企微码，完善人员档案信息
							</p>
						</div>

						<div class="users-form-body">
							<div class="users-form-grid">
								<!-- 证件照 -->
								<div class="users-form-group">
									<label class="users-form-label">
										<i class="fa fa-camera"></i>
										证件照
									</label>
									<div class="users-image-upload <php>if($row['identification']) echo 'has-image';</php>">
										<php>if($row['identification']) {</php>
										<img src="{:$row['identification']}" alt="证件照" class="users-image-preview">
										<div class="users-upload-text">点击更换证件照</div>
										<php>} else {</php>
										<div class="users-upload-icon">
											<i class="fa fa-camera"></i>
										</div>
										<div class="users-upload-text">点击上传证件照</div>
										<php>}</php>
									</div>
									<php>echo tpl_form_field_image('identification', $row['identification'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'material' => 11]])</php>
									<div class="users-help-text">建议上传清晰的正面证件照</div>
								</div>

								<!-- 企微码 -->
								<div class="users-form-group">
									<label class="users-form-label">
										<i class="fa fa-qrcode"></i>
										企微码
									</label>
									<div class="users-image-upload <php>if($row['qrcode']) echo 'has-image';</php>">
										<php>if($row['qrcode']) {</php>
										<img src="{:$row['qrcode']}" alt="企微码" class="users-image-preview">
										<div class="users-upload-text">点击更换企微码</div>
										<php>} else {</php>
										<div class="users-upload-icon">
											<i class="fa fa-qrcode"></i>
										</div>
										<div class="users-upload-text">点击上传企微码</div>
										<php>}</php>
									</div>
									<php>echo tpl_form_field_image('qrcode', $row['qrcode'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'material' => 11]])</php>
									<div class="users-help-text">企业微信个人二维码</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 表单操作区域 -->
					<div class="users-form-actions">
						<input type="hidden" name="id" value="{$row.id}" />
						<button type="button" onclick="history.back()" class="users-btn users-btn-secondary">
							<i class="fa fa-times"></i>
							取消
						</button>
						<button type="submit" name="submit" class="users-btn users-btn-primary users-btn-lg js-ajax-submit">
							<i class="fa fa-save"></i>
							<php>echo I('get.id') ? '保存修改' : '创建人员';</php>
						</button>
					</div>
				</form>

				<!-- 操作提示卡片 -->
				<div class="users-form-card users-fade-in-delay-2" style="margin-top: 1.5rem;">
					<div class="users-form-header">
						<h2 class="users-form-title">
							<i class="fa fa-lightbulb-o"></i>
							操作提示
						</h2>
					</div>
					<div class="users-form-body">
						<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
							<div style="padding: 1rem; background: #f0fff4; border-left: 4px solid #48bb78; border-radius: 0.5rem;">
								<h4 style="margin: 0 0 0.5rem 0; color: #2f855a; font-size: 1.5rem; font-weight: 600;">
									<i class="fa fa-check-circle"></i>
									创建成功后
								</h4>
								<p style="margin: 0; color: #2f855a; font-size: 1.5rem;">
									人员创建成功后，可以为其分配具体的权限和设置服务站点
								</p>
							</div>
							<div style="padding: 1rem; background: #fffbf0; border-left: 4px solid #ed8936; border-radius: 0.5rem;">
								<h4 style="margin: 0 0 0.5rem 0; color: #c05621; font-size: 1.5rem; font-weight: 600;">
									<i class="fa fa-exclamation-triangle"></i>
									注意事项
								</h4>
								<p style="margin: 0; color: #c05621; font-size: 1.5rem;">
									请确保手机号码和邮箱地址的准确性，用于重要通知的发送
								</p>
							</div>
							<div style="padding: 1rem; background: #f0f9ff; border-left: 4px solid #3b82f6; border-radius: 0.5rem;">
								<h4 style="margin: 0 0 0.5rem 0; color: #1e40af; font-size: 1.5rem; font-weight: 600;">
									<i class="fa fa-info-circle"></i>
									密码安全
								</h4>
								<p style="margin: 0; color: #1e40af; font-size: 1.5rem;">
									建议设置复杂密码，包含字母、数字和特殊字符，确保账户安全
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>

		</div>
	</div>
</div>

<script src="__ROOT__/static/js/prime/common.js"></script>
<link rel="stylesheet" href="__ROOT__/static/js/app/layer/skin/layer.css" />

<script>
	require(['layer'], function(layer) {
		$(function(){
			// 表单验证和交互
			var $form = $('#users-form');
			var $submitBtn = $('.js-ajax-submit');

			// 单选按钮交互
			$('.users-radio-item').click(function() {
				var $item = $(this);
				var $input = $item.find('input[type="radio"]');
				var name = $input.attr('name');

				// 移除同组其他选项的active状态
				$('input[name="' + name + '"]').closest('.users-radio-item').removeClass('active');

				// 设置当前选项为active
				$item.addClass('active');
				$input.prop('checked', true);
			});

			// 表单字段实时验证
			$('.users-form-control').on('input blur', function() {
				var $field = $(this);
				var value = $field.val().trim();
				var isRequired = $field.prop('required');
				var fieldType = $field.attr('type');
				var fieldName = $field.attr('name');

				// 移除之前的验证状态
				$field.removeClass('error success');
				$field.siblings('.users-error-text').remove();

				// 必填字段验证
				if (isRequired && !value) {
					$field.addClass('error');
					$field.after('<div class="users-error-text"><i class="fa fa-exclamation-circle"></i> 此字段为必填项</div>');
					return;
				}

				// 特定字段验证
				if (value) {
					var isValid = true;
					var errorMsg = '';

					switch(fieldName) {
						case 'mobile':
							if (!/^1[3-9]\d{9}$/.test(value)) {
								isValid = false;
								errorMsg = '请输入正确的手机号码';
							}
							break;
						case 'emails':
							if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
								isValid = false;
								errorMsg = '请输入正确的邮箱地址';
							}
							break;
						case 'card':
							if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
								isValid = false;
								errorMsg = '请输入正确的身份证号码';
							}
							break;
						case 'username':
							if (value.length < 3) {
								isValid = false;
								errorMsg = '用户账号至少3个字符';
							}
							break;
						case 'letter':
							if (!/^[A-Za-z0-9]+$/.test(value)) {
								isValid = false;
								errorMsg = '用户代码只能包含字母和数字';
							}
							break;
					}

					if (!isValid) {
						$field.addClass('error');
						$field.after('<div class="users-error-text"><i class="fa fa-exclamation-circle"></i> ' + errorMsg + '</div>');
					} else {
						$field.addClass('success');
					}
				}
			});

			// 表单提交处理
			$form.on('submit', function(e) {
				e.preventDefault();

				// 验证必填字段
				var hasError = false;
				$('.users-form-control[required]').each(function() {
					var $field = $(this);
					var value = $field.val().trim();

					if (!value) {
						$field.addClass('error');
						$field.siblings('.users-error-text').remove();
						$field.after('<div class="users-error-text"><i class="fa fa-exclamation-circle"></i> 此字段为必填项</div>');
						hasError = true;
					}
				});

				// 检查是否有验证错误
				if ($('.users-form-control.error').length > 0) {
					hasError = true;
				}

				if (hasError) {
					layer.msg('请修正表单中的错误信息', {icon: 2});
					// 滚动到第一个错误字段
					var $firstError = $('.users-form-control.error').first();
					if ($firstError.length) {
						$('html, body').animate({
							scrollTop: $firstError.offset().top - 100
						}, 500);
						$firstError.focus();
					}
					return false;
				}

				// 设置提交按钮为加载状态
				$submitBtn.addClass('loading');
				$submitBtn.prop('disabled', true);
				var originalText = $submitBtn.html();
				$submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

				// 提交表单
				$.ajax({
					url: $form.attr('action') || window.location.href,
					type: 'POST',
					data: $form.serialize(),
					dataType: 'json',
					success: function(response) {
						if (response.status === 1 || response.code === 1) {
							layer.msg(response.info || '操作成功', {icon: 1}, function() {
								window.location.href = "{:U('users/index')}";
							});
						} else {
							layer.msg(response.info || '操作失败', {icon: 2});
						}
					},
					error: function() {
						layer.msg('网络错误，请重试', {icon: 2});
					},
					complete: function() {
						// 恢复提交按钮状态
						$submitBtn.removeClass('loading');
						$submitBtn.prop('disabled', false);
						$submitBtn.html(originalText);
					}
				});
			});

			// 页面加载动画
			setTimeout(function() {
				$('.users-form-card').addClass('users-fade-in');
			}, 100);

			// 表单字段聚焦效果
			$('.users-form-control').on('focus', function() {
				$(this).closest('.users-form-group').addClass('focused');
			}).on('blur', function() {
				$(this).closest('.users-form-group').removeClass('focused');
			});

			// 图片上传区域交互
			$('.users-image-upload').click(function() {
				var $upload = $(this);
				$upload.find('input[type="file"]').click();
			});

			// 键盘快捷键
			$(document).on('keydown', function(e) {
				// Ctrl+S 保存
				if (e.ctrlKey && e.keyCode === 83) {
					e.preventDefault();
					$form.submit();
				}
				// Esc 取消
				if (e.keyCode === 27) {
					if (confirm('确定要取消编辑吗？未保存的更改将丢失。')) {
						history.back();
					}
				}
			});

			// 表单数据变化检测
			var originalFormData = $form.serialize();
			var hasUnsavedChanges = false;

			$('.users-form-control').on('input change', function() {
				hasUnsavedChanges = ($form.serialize() !== originalFormData);
			});

			// 页面离开提醒
			$(window).on('beforeunload', function() {
				if (hasUnsavedChanges) {
					return '您有未保存的更改，确定要离开吗？';
				}
			});

			// 成功提交后清除未保存标记
			$form.on('submit', function() {
				hasUnsavedChanges = false;
			});

			// 密码强度检测
			$('input[name="pwd"]').on('input', function() {
				var password = $(this).val();
				var $helpText = $(this).siblings('.users-help-text');

				if (password.length > 0) {
					var strength = 0;
					if (password.length >= 8) strength++;
					if (/[a-z]/.test(password)) strength++;
					if (/[A-Z]/.test(password)) strength++;
					if (/[0-9]/.test(password)) strength++;
					if (/[^A-Za-z0-9]/.test(password)) strength++;

					var strengthText = ['很弱', '弱', '一般', '强', '很强'][strength];
					var strengthColor = ['#f56565', '#ed8936', '#ecc94b', '#48bb78', '#38a169'][strength];

					$helpText.html('密码强度: <span style="color: ' + strengthColor + '; font-weight: 600;">' + strengthText + '</span>');
				} else if (!"{$row.id}") {
					$helpText.text('用户登录系统时使用的密码');
				}
			});
		});
	});
</script>

<include file="block/footer" />

//function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}
require(['layer']);
//require(['layer'], function(layer) {
//_alert(123);
//layer.alert(123);
//layer.alert(123)
//});


$(function(){
	//function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}

	//layer.config({
		//extend: 'extend/layer.ext.js'
	//});
	//_alert(123)
})

</script>
<include file="block/footer" />