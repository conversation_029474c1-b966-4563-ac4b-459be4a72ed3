<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Project/nav" />
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <form action="" method="get" class="form-inline">
                            <div class="form-group">
                                <select name="kw" class="form-control">
                                    <php>foreach($c_kw as $k => $v) {</php>
                                    <option value="{$k}" <php>if($_get['kw']==$k) echo 'selected';</php>>{$v}</option>
                                    <php>}</php>
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="text" name="val" class="form-control" placeholder="关键词" value="{$_get.val}">
                            </div>
                            <button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> 搜索</button>
                            <a href="{:U('channel/index')}" class="btn btn-warning" >重置</a>
                        </form>
                    </div>
                </div>
                <form action="" method="post" >
                    <div class="panel panel-default">
                        <div class="panel-body table-responsive">
                            <table class="table table-hover">
                                <thead class="navbar-inner">
                                    <tr>
                                        <th width="75px">#ID</th>
                                        <th>渠道名称</th>
                                        <th>联系人信息</th>
                                        <th>联系地址</th>
                                        <th>备注说明</th>
                                        <th>添加时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($list as $v) { </php>
                                    <tr>
                                        <td>{$v.id}</td>
                                        <td>{$v.name}</td>
                                        <td>
                                            姓名：{$v.contact_name}<br>
                                            电话：{$v.contact_phone}
                                        </td>
                                        <td>{$v.contact_address}</td>
                                        <td>{$v.remark}</td>
                                        <td>{:date('Y-m-d H:i:s', $v['create_time'])}</td>
                                        <td>
                                            <a href="{:U('channel/edit', ['id' => $v['id']])}" class="btn btn-primary btn-xs">编辑</a>
                                            <a href="{:U('channel/del', ['id' => $v['id']])}" class="btn btn-danger btn-xs" onclick="return confirm('确定删除吗？')">删除</a>
                                        </td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                            {$page}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />
