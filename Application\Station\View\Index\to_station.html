﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>服务站资源包划拨</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <style>
        /* 微信环境检测提示 */
        .wechat-alert {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 12px;
            z-index: 9999;
            text-align: center;
            font-size: 18px;
        }

        /* 主容器 */
        .wx-container {
            padding: 12px;
            min-height: 100vh;
            background: #F8F8F8;
        }

        /* 操作卡片 */
        .wx-card {
            background: #FFFFFF;
            border-radius: 16px;
            padding: 12px;
            margin: 10px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        /* 关键数据展示 */
        .wx-critical {
            color: #EE2D2D;
            font-size: 52px;
            font-weight: 500;
            text-align: center;
            margin: 10px 0;
            text-shadow: 0 2px 4px rgba(238,45,45,0.12);
        }

        /* 警示文案 */
        .wx-warning {
            color: #666;
            font-size: 16px;
            line-height: 1.8;
            padding: 15px;
            background: #FFF5F5;
            border-radius: 8px;
            margin: 20px 0;
        }

        /* 输入区域 */
        .wx-input-box {
            margin: 40px 0;
            text-align: center;
        }

        .wx-input {
            width: 80%;
            height: 60px;
            border: 3px solid #EE2D2D;
            border-radius: 12px;
            font-size: 20px;
            text-align: center;
            font-weight: bold;
            caret-color: #EE2D2D;
        }

        .wx-input:focus {
            box-shadow: 0 0 12px rgba(238,45,45,0.2);
            outline: none;
        }

        /* 操作按钮 */
        .wx-btn {
            width: 80%;
            height: 50px;
            background: #EE2D2D;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            margin: 30px auto;
            display: block;
            transition: all 0.2s;
        }

        .wx-btn:disabled {
            background: #DDD;
            opacity: 0.7;
        }

        .wx-btn:active {
            transform: scale(0.96);
        }

        /* 确认弹窗 */
        .wx-dialog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .wx-dialog-box {
            background: white;
            width: 80%;
            border-radius: 16px;
            padding: 25px;
            text-align: center;
        }

        .wx-dialog-text {
            font-size: 18px;
            color: #333;
            margin: 20px 0;
            line-height: 1.6;
        }

        .wx-dialog-btns {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .wx-dialog-btn {
            flex: 1;
            height: 45px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
        }

        .wx-confirm-btn {
            background: #EE2D2D;
            color: white;
        }

        .wx-cancel-btn {
            background: #F0F0F0;
            color: #666;
        }
        /* 返回箭头样式 */
        .back-arrow {
            position: fixed;
            top: 175px;
            left: 15px;
            width: 40px;
            height: 40px;
            z-index: 9999;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            transition: all 0.2s;
        }

        .back-arrow::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 50%;
            width: 12px;
            height: 12px;
            border-left: 2px solid #333;
            border-bottom: 2px solid #333;
            transform: translateY(-50%) rotate(45deg);
        }

    </style>
</head>
<body>
    <!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
    <include file="headers"/>
 
    <div class="wx-container">
        <div class="wx-card" style="text-align: center;font-size: 18px;color:green;margin-top: 12px;">
            <BR>
            可用服务站资源包数
            <div class="wx-critical">{:$serviceStationRow['open_num']}</div>
            
            <div style="font-size: 14px;color:rgb(15, 1, 1);text-align: center;">⚠️注意：你正在划拨资源包给</div>
            <div class="wx-warning">

                {:$divideServiceStation['service_name']}
                
            </div>
            <div style="font-size: 15px;color:red;text-align: center;">操作不可逆，请确认无误后填写划拨数量</div>
            

            <div class="wx-input-box">
                <input type="number"
                       class="wx-input"
                       name="divide_open_num"
                       id="wxAmount"
                       placeholder="请输入划拨数量"
                       min="1"
                       max="{:$serviceStationRow['open_num']}"
                       inputmode="numeric"
                       pattern="\d*"
                       oninput="validateWXInput()">
            </div>

            <button class="wx-btn" 
                    id="wxConfirmBtn"
                    disabled
                    onclick="showWXDialog()">
                确定划拨
            </button>
        </div>
    </div>

    <!-- 确认弹窗 -->
    <div class="wx-dialog" id="wxDialog">
        <div class="wx-dialog-box">
            <div class="wx-dialog-text" id="dialogText">
                确认划拨 <span style="color:#EE2D2D;font-weight:bold">0</span> 个资源包给{:$divideServiceStation['service_name']}？<br>
                （此操作不可撤销）
            </div>
            <div class="wx-dialog-btns">
                <button class="wx-dialog-btn wx-cancel-btn" onclick="closeDialog()">取消</button>
                <button class="wx-dialog-btn wx-confirm-btn" onclick="handleTransfer()">确认</button>
            </div>
        </div>
    </div>
    <script src="/static/js/layer/layer.m.js"></script>
    <script>

        // 输入验证
        function validateWXInput() {
            const input = document.getElementById('wxAmount');
            const btn = document.getElementById('wxConfirmBtn');
            const value = parseInt(input.value) || 0;
            const max = parseInt(input.max);

            if (value > max) {
                input.value = max;
            }
            btn.disabled = !(value >= 1 && value <= max);
        }

        // 显示确认弹窗
        function showWXDialog() {
            const amount = document.getElementById('wxAmount').value;
            document.querySelector('#dialogText span').textContent = amount;
            document.getElementById('wxDialog').style.display = 'flex';
        }

        // 关闭弹窗
        function closeDialog() {
            document.getElementById('wxDialog').style.display = 'none';
        }

        // 执行划拨操作
        function handleTransfer() {
            closeDialog();
            const amount = document.getElementById('wxAmount').value;
            $.ajax({
                type: 'post',
                data: {"divide_open_num" : amount},
                url: '/index/divideservicestation?id={$id}',
                beforeSend: function () {
                    //loading层
                    layer.open({
                        type: 2,
                        shadeClose: false,
                    });
                },
                complete: function () {

                    // Handle the complete event
                },
                success: function (data) {
                    layer.closeAll();
                    layer.open({
                        style : "width:80%",
                        shadeClose : false,
                        content: "<div style='text-align: left'>"+data.info+"</div>",
                        btn: ['我知道了'],
                        yes: function (index) {
                            layer.closeAll();
                            window.location.reload();
                        },
                    });
                }
            });
            // 这里添加实际业务逻辑
            console.log(`划拨${amount}个资源包给张三`);

            // 重置表单
            document.getElementById('wxAmount').value = '';
            document.getElementById('wxConfirmBtn').disabled = true;
        }

        // 禁止非数字输入
        document.getElementById('wxAmount').addEventListener('keypress', (e) => {
            if (e.key === 'e' || e.key === '-' || e.key === '+' || e.key === '.') {
                e.preventDefault();
            }
        });

        // 返回逻辑（优先返回历史记录，失败则跳转首页）
       function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/'; // 修改为你的默认回退地址
            }
        }
    </script>
</body>
</html>
