<?php
class Page
{
    private $Page_size; //每页显示的条目数
    private $Total_Size; //总条目数
    private $Current_page; //当前被选中的页
    private $List_Page; //每次显示的页数  默认列表每页显示行数
    private $Total_Pages = 20; //总页数
    private $Page_tpl = array(); // 分页模板
    private $PageParam;
    private $PageLink;
    private $Static;
    // 起始行数
    public $firstRow;
    public $listRows;
    private $linkwraper = "";
    private $linkwraper_pre = "";
    private $linkwraper_after = "";
    // added by zhou
    public $ajax = false;
    public $ajax_str = '';
    //Page([总记录数=1]，   [分页大小=20]，     [当前页=1]，         [显示页数=6]，     [分页参数='page'],      [分页链接=当前页面],[是否静态=FALSE])
    function __construct($Total_Size = 1, $Page_Size = 20, $Current_Page = 1, $List_Page = 6, $PageParam = 'page', $PageLink = '', $Static = FALSE) {
        $this->Page_size = intval($Page_Size);
        $this->Total_Size = intval($Total_Size);
        if (!$Current_Page) {
            $this->Current_page = 1;
        } else {
            $this->Current_page = (int)$Current_Page < 1 ? 1 : (int)$Current_Page;
        }
        //总分页数
        $this->Total_Pages = ceil($Total_Size / $Page_Size);
        //一次显示多少个链接 ，该参数会被SetPager里传入的配置覆盖。
        $this->List_Page = (int)$List_Page;
        //接收分页参数的标识符
        $this->PageParam = $PageParam;
        //当前页面地址，当需要生成静态地址，此参数需要给，分页号用{page}
        $this->PageLink = (empty($PageLink) ? explode('?', $_SERVER["REQUEST_URI"])[0] : $PageLink);
        //是否开启静态
        $this->Static = $Static;
        $this->Page_tpl['default'] = array(
            'Tpl' => '<div class="pager">{first}{prev}{liststart}{list}{listend}{next}{last} 跳转到{jump}页</div>',
            'Config' => array()
        );
        $this->GetCurrentPage();
        $this->listRows = $Page_Size;
        $this->firstRow = ($this->Current_page - 1) * $this->listRows;
        if ($this->firstRow < 0) {
            $this->firstRow = 0;
        }
    }
    public function __set($Param, $value) {
        $this->$Param = $value;
    }
    public function __get($Param) {
        return $this->$Param;
    }

    public $linkwraper_pre_cur = '';

    public function setLinkWraper($wraper) {
        if (empty($wraper)) {
        } else {
            $this->linkwraper_after = "</$wraper>";
            $this->linkwraper_pre = "<$wraper>";
            $this->linkwraper_pre_cur = "<$wraper class=\"active\">";
        }
    }

    private function UrlParameters($url = array()) {
        foreach ($url as $key => $val) {
            if ($key != $this->PageParam && $key != "_URL_") $arg[$key] = $val;
        }
        $arg[$this->PageParam] = '*';
        if ($this->Static) {
            $plink = $this->PageLink;
            $tmp['list'] = $plink.'?'.http_build_query($arg);
            unset($arg[$this->PageParam]);
            $tmp['index'] = empty($arg) ? $plink : $plink.'?'.http_build_query($arg);
            $this->PageLink = $tmp;
            //当启用静态地址，$this->PageLink传入的是array，并且包含两个 index,list
            /*
             * array(
             *    "index"=>"http://www.a.com/192.html",//这种是表示当前是首页，无需加分页1
             *    "list"=>"http://www.a.com/192-{page}.html",//这种表示分页非首页时启用
             * )
            */
            if (is_array($this->PageLink)) {
                return str_replace('%2A', '*', $this->PageLink['list']);
            } else {
                return str_replace('%2A', '*', $this->PageLink);
            }
        } else {
            return str_ireplace("%2A", "*", U(strtolower(MODULE_NAME . "/" . CONTROLLER_NAME . "/" . ACTION_NAME)).'?'.http_build_query($arg));
            //return str_ireplace("%2A", "*", U(strtolower(MODULE_NAME . "/" . CONTROLLER_NAME . "/" . ACTION_NAME) , $arg));
        }
    }

    public function SetPager($Tpl_Name = 'default', $Tpl = '', $Config = array()) {
        if (empty($Tpl)) $Tpl = $this->Page_tpl['default']['Tpl'];
        if (empty($Config)) $Config = $this->Page_tpl['default']['Config'];
        $this->Page_tpl[$Tpl_Name] = array(
            'Tpl' => $Tpl,
            'Config' => $Config
        );
    }

    public function show($Tpl_Name = 'default') {
        //当分页数只有1的时候，不显示
        if ($this->Total_Pages <= 1) {
            // return;
        }
        return $this->Pager($this->Page_tpl[$Tpl_Name]);
    }

    public function GetCurrentPage() {
        $p = isset($_GET[$this->PageParam]) ? intval($_GET[$this->PageParam]) : 1;
        $p = $p < 1 ? 1 : $p;
        $total_pages = intval($this->Total_Pages);
        $this->Current_page = ($p <= $total_pages ? $p : $total_pages);
    }

    public function Pager($Page_tpl = '') {
        if (empty($Page_tpl)) $Page_tpl = $this->Page_tpl['default'];
        $cfg = array(
            'recordcount' => intval($this->Total_Size) ,
            'pageindex' => intval($this->Current_page) ,
            'pagecount' => intval($this->Total_Pages) ,
            'pagesize' => intval($this->Page_size) ,
            'listlong' => intval($this->List_Page) ,
            'listsidelong' => 2,
            'list' => '*',
            'currentclass' => 'current',
            'currentclassext' => 'active',
            'link' => $this->UrlParameters($_GET) ,
            'first' => '&laquo;',
            'prev' => '&#8249;',
            'next' => '&#8250;',
            'last' => '&raquo;',
            'more' => $this->linkwraper_pre . '<span>...</span>' . $this->linkwraper_after,
            'disabledclass' => 'disabled',
            'jump' => 'input',
            'jumpplus' => '',
            'jumpaction' => '',
            'jumplong' => 50
        );
        if (!empty($Page_tpl['Config'])) {
            foreach ($Page_tpl['Config'] as $key => $val) {
                if (array_key_exists($key, $cfg)) $cfg[$key] = $val;
            }
        }
        //判断listlong是否为偶数
        if ((int)$cfg['listlong'] % 2 != 0) {
            $cfg['listlong'] = $cfg['listlong'] + 1;
        }
        $tmpStr = $Page_tpl['Tpl'];
        $pStart = $cfg['pageindex'] - (($cfg['listlong'] / 2) + ($cfg['listlong'] % 2)) + 1;
        $pEnd = $cfg['pageindex'] + $cfg['listlong'] / 2;
        if ($pStart < 1) {
            $pStart = 1;
            $pEnd = $cfg['listlong'];
        }
        if ($pEnd > $cfg['pagecount']) {
            $pStart = $cfg['pagecount'] - $cfg['listlong'] + 1;
            $pEnd = $cfg['pagecount'];
        }
        if ($pStart < 1) $pStart = 1;
        for ($i = $pStart; $i <= $pEnd; $i++) {
            if ($i == $cfg['pageindex']) {
                $pList.= $this->linkwraper_pre_cur . '<span class="' . $cfg['currentclass'] . '" >' . str_replace('*', $i, $cfg['list']) . '</span> ' . $this->linkwraper_after;
            } else {
                if ($this->Static && $i == 1) {
                    $pList.= $this->linkwraper_pre . '<a '.$this->ajax_str.' href="' . $this->PageLink['index'] . '"> ' . str_replace('*', $i, $cfg['list']) . '</a> ' . $this->linkwraper_after;
                } else {
                    $pList.= $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . str_replace('*', $i, $cfg['link']) . '"> ' . str_replace('*', $i, $cfg['list']) . '</a> ' . $this->linkwraper_after;
                }
            }
        }
        if ($cfg['listsidelong'] > 0) {
            if ($cfg['listsidelong'] < $pStart) {
                for ($i = 1; $i <= $cfg['listsidelong']; $i++) {
                    if ($this->Static && $i == 1) {
                        $pListStart.= $this->linkwraper_pre . '<a'.$this->ajax_str.' href="' . $this->PageLink['index'] . '">' . str_replace('*', $i, $cfg['list']) . '</a> ' . $this->linkwraper_after;
                    } else {
                        $pListStart.= $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . str_replace('*', $i, $cfg['link']) . '">' . str_replace('*', $i, $cfg['list']) . '</a> ' . $this->linkwraper_after;
                    }
                }
                $pListStart.= ($cfg['listsidelong'] + 1) == $pStart ? '' : $cfg['more'] . ' ';
            } else {
                if ($cfg['listsidelong'] >= $pStart && $pStart > 1) {
                    for ($i = 1; $i <= ($pStart - 1); $i++) {
                        if ($this->Static && $i == 1) {
                            $pListStart.= $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . $this->PageLink['index'] . '"> ' . str_replace('*', $i, $cfg['list']) . '</a> ' . $this->linkwraper_after;
                        } else {
                            $pListStart.= $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . str_replace('*', $i, $cfg['link']) . '"> ' . str_replace('*', $i, $cfg['list']) . '</a> ' . $this->linkwraper_after;
                        }
                    }
                }
            }
            if (($cfg['pagecount'] - $cfg['listsidelong']) > $pEnd) {
                $pListEnd = ' ' . $cfg['more'] . $pListEnd;
                for ($i = (($cfg['pagecount'] - $cfg['listsidelong']) + 1); $i <= $cfg['pagecount']; $i++) {
                    if ($this->Static && $i == 1) {
                        $pListEnd.= $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . $this->PageLink['index'] . '">' . str_replace('*', $i, $cfg['list']) . '</a> ' . $this->linkwraper_after;
                    } else {
                        $pListEnd.= $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . str_replace('*', $i, $cfg['link']) . '"> ' . str_replace('*', $i, $cfg['list']) . ' </a> ' . $this->linkwraper_after;
                    }
                }
            } else {
                if (($cfg['pagecount'] - $cfg['listsidelong']) <= $pEnd && $pEnd < $cfg['pagecount']) {
                    for ($i = ($pEnd + 1); $i <= $cfg['pagecount']; $i++) {
                        if ($this->Static && $i == 1) {
                            $pListEnd.= '<a '.$this->ajax_str.'href="' . $this->PageLink['index'] . '">' . str_replace('*', $i, $cfg['list']) . '</a> ' . $this->linkwraper_after;
                        } else {
                            $pListEnd.= $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . str_replace('*', $i, $cfg['link']) . '"> ' . str_replace('*', $i, $cfg['list']) . ' </a> ' . $this->linkwraper_after;
                        }
                    }
                }
            }
        }
        //上一页 首页
        if ($cfg['pageindex'] > 1) {
            if ($this->Static) {
                $pFirst = $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . $this->PageLink['index'] . '">' . $cfg['first'] . '</a> ' . $this->linkwraper_after; //首页

            } else {
                $pFirst = $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . str_replace('*', 1, $cfg['link']) . '">' . $cfg['first'] . '</a> ' . $this->linkwraper_after; //首页

            }
            if ($this->Static && ($cfg['pageindex'] - 1) == 1) {
                $pPrev = $this->linkwraper_pre . '<a'.$this->ajax_str.' href="' . $this->PageLink['index'] . '">' . $cfg['prev'] . '</a> ' . $this->linkwraper_after; //上一页

            } else {
                $pPrev = $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . str_replace('*', $cfg['pageindex'] - 1, $cfg['link']) . '">' . $cfg['prev'] . '</a> ' . $this->linkwraper_after;
            }
        }
        //下一页，尾页
        if ($cfg['pageindex'] < $cfg['pagecount']) {
            $pLast = $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . str_replace('*', $cfg['pagecount'], $cfg['link']) . '">' . $cfg['last'] . '</a> ' . $this->linkwraper_after;
            $pNext = $this->linkwraper_pre . '<a '.$this->ajax_str.'href="' . str_replace('*', $cfg['pageindex'] + 1, $cfg['link']) . '">' . $cfg['next'] . '</a> ' . $this->linkwraper_after;
        }
        $pExt = $this->linkwraper_pre . "<a>总数 {$cfg['recordcount']}</a> " . $this->linkwraper_after;

        //快捷跳转方式
        switch (strtolower($cfg['jump'])) {
            case 'input':
                $pJumpValue = 'this.value';
                $pJump = '<input type="text" size="3" title="请输入要跳转到的页数并回车"' . (($cfg['jumpplus'] == '') ? '' : ' ' . $cfg['jumpplus']);
                $pJump.= ' onkeydown="javascript:if(event.charCode==13||event.keyCode==13){if(!isNaN(' . $pJumpValue . ')){';
                $pJump.= ($cfg['jumpaction'] == '' ? ((strtolower(substr($cfg['link'], 0, 11)) == 'javascript:') ? str_replace('*', $pJumpValue, substr($cfg['link'], 12)) : " document.location.href='" . str_replace('*', '\'+' . $pJumpValue . '+\'', $cfg['link']) . '\';') : str_replace("*", $pJumpValue, $cfg['jumpaction']));
                $pJump.= '}return false;}" />';
                break;

            case 'select':
                $pJumpValue = "this.options[this.selectedIndex].value";
                $pJump = '<select ' . ($cfg['jumpplus'] == '' ? ' ' . $cfg['jumpplus'] . ' onchange="javascript:' : $cfg['jumpplus']);
                $pJump.= ($cfg['jumpaction'] == '' ? ((strtolower(substr($cfg['link'], 0, 11)) == 'javascript:') ? str_replace('*', $pJumpValue, substr($cfg['link'], 12)) : " document.location.href='" . str_replace('*', '\'+' . $pJumpValue . '+\'', $cfg['link']) . '\';') : str_replace("*", $pJumpValue, $cfg['jumpaction']));
                $pJump.= '" title="请选择要跳转到的页数"> ';
                if ($cfg['jumplong'] == 0) {
                    for ($i = 0; $i <= $cfg['pagecount']; $i++) {
                        $pJump.= '<option value="' . $i . '"' . (($i == $cfg['pageindex']) ? ' selected="selected"' : '') . ' >' . $i . '</option> ';
                    }
                } else {
                    $pJumpLong = intval($cfg['jumplong'] / 2);
                    $pJumpStart = ((($cfg['pageindex'] - $pJumpLong) < 1) ? 1 : ($cfg['pageindex'] - $pJumpLong));
                    $pJumpStart = ((($cfg['pagecount'] - $cfg['pageindex']) < $pJumpLong) ? ($pJumpStart - ($pJumpLong - ($cfg['pagecount'] - $cfg['pageindex'])) + 1) : $pJumpStart);
                    $pJumpStart = (($pJumpStart < 1) ? 1 : $pJumpStart);
                    $j = 1;
                    for ($i = $pJumpStart; $i <= $cfg['pageindex']; $i++, $j++) {
                        $pJump.= '<option value="' . $i . '"' . (($i == $cfg['pageindex']) ? ' selected="selected"' : '') . '>' . $i . '</option> ';
                    }
                    $pJumpLong = $cfg['pagecount'] - $cfg['pageindex'] < $pJumpLong ? $pJumpLong : $pJumpLong + ($pJumpLong - $j) + 1;
                    $pJumpEnd = $cfg['pageindex'] + $pJumpLong > $cfg['pagecount'] ? $cfg['pagecount'] : $cfg['pageindex'] + $pJumpLong;
                    for ($i = $cfg['pageindex'] + 1; $i <= $pJumpEnd; $i++) {
                        $pJump.= '<option value="' . $i . '">' . $i . '</option> ';
                    }
                }
                $pJump.= '</select>';
                break;
        }
        $patterns = array(
            '/{recordcount}/',
            '/{pagecount}/',
            '/{pageindex}/',
            '/{pagesize}/',
            '/{list}/',
            '/{liststart}/',
            '/{listend}/',
            '/{first}/',
            '/{prev}/',
            '/{next}/',
            '/{last}/',
            '/{jump}/',
            '/{ext}/'
        );
        $replace = array(
            $cfg['recordcount'],
            $cfg['pagecount'],
            $cfg['pageindex'],
            $cfg['pagesize'],
            $pList,
            $pListStart,
            $pListEnd,
            $pFirst,
            $pPrev,
            $pNext,
            $pLast,
            $pJump,
            $pExt
        );
        $tmpStr = chr(13) . chr(10) . preg_replace($patterns, $replace, $tmpStr) . chr(13) . chr(10);
        unset($cfg);
        return $tmpStr;
    }

    public function setajax()
    {
        $this->ajax = true;
        $this->ajax_str = ' class="_ajax" ';
    }

}


