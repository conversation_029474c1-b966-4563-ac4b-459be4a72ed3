<?php

namespace Pwapi\Controller;

use Think\Controller;

/**
 * Wapi 模块公用控制器
 */
class JobController extends Controller
{
    public function _initialize()
    {

    }

    /**
     * 获取job文件
     */
    public function getdoc() {
        $this->start = microtime(1);
        $input = file_get_contents("php://input");
        $input or $input = urldecode($_SERVER['QUERY_STRING']);
        $input = json_decode($input, 1);
        $userJobDockRow = D("UserJobDoc")->where(['file_check_type' => 1])->find();
        if ($userJobDockRow) {
            if (strpos($userJobDockRow['content'], 'http') !== false) {
                $url = $userJobDockRow['content'];
            } else {
                $url = 'https://we.zhongcaiguoke.com'.$userJobDockRow['content'];
            }
            D("UserJobDoc")->save(['id' => $userJobDockRow['id'], 'file_check_type' => 2, 'update_time' => time()]);
            echo json_encode(['code' => 0, 'data' => $url, 'type' => "1", 'id' => $userJobDockRow['id']]);
            die;
        } else {
            echo json_encode(['code' => 1, 'next' => 100, 'data' => '']);

        }
    }

    /**
     * 分析上传文件
     */
    public function postdoc() {
        $this->start = microtime(1);
        $input = file_get_contents("php://input");
        $input or $input = urldecode($_SERVER['QUERY_STRING']);
        $input = json_decode($input, 1);
        dolog("pwapi/test", is_array($input) ? json_encode($input) : $input, 'postdoc');
        $id = $input['id'] ? :0;
        if ($id) {
            $userJobDocRow = D("UserJobDoc")->where(['id' => $id])->find();
            if ($input['IsError'] !== false) {
                D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'file_check_type' => 3, 'update_time' => time()]);
            } else {
                $string = $input['Text'];
                D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'file_check_type' => 4, 'update_time' => time()]);
                $fields = ["姓名", "性别", "出生年月", "婚否", "视力", "听力", "是否恐高", "证明人", "高中填起", "隐瞒事实"];
                $allExists = true;
                foreach ($fields as $field) {
                    if (strpos($string, $field) === false) {
                        $allExists = false;
                        break;
                    }
                }
                if ($allExists) {
                    $check_doc_succ = 1;
                } else {
                    $check_doc_succ = 2;
                }
                D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'check_doc_succ' => $check_doc_succ]);
            }
            echo json_encode(['code' => 0, 'msg' => 'ok', 'data' => []]);die;
        }
        echo json_encode(['code' => 1, 'msg' =>  'error', 'data' => []]);die;


    }

    public function getjob()
    {

        $this->start = microtime(1);
        $input = file_get_contents("php://input");
        $input or $input = urldecode($_SERVER['QUERY_STRING']);
        $input = json_decode($input, 1);
        if ($input['Message'] == 'NoLogin') {
            echo json_encode(['code' => 1, 'next' => 100, 'data' => '']);
        } else  {
            $userJobDockRow = D("UserJobDoc")->where(['status' => 1])->find();
            if ($userJobDockRow) {
                if (strpos($userJobDockRow['content'], 'http') !== false) {
                    $url = $userJobDockRow['content'];
                } else {
                    $url = 'https://we.zhongcaiguoke.com'.$userJobDockRow['content'];
                }
                if ($input['Message'] == 'Idle') {
                    $urls = "今天是".date('Y年m月d日').",你是一个专业的文档分析师 \n请根据获取的远程文件分析得到以下json内容\n并返回json对应的信息，缺失内容为空，但需要保持完全一样的格式，请勿插入其他格式内容\n\n-- 模板json--\n{\"name\":\"姓名\",\"id_number\":\"身份证\",\"gender\":\"性别 enum('男','女')\",\"birthdate\":\"生日\",\"marital_status\":\"婚姻状况 enum('已婚','未婚')\",\"nation\":\"民族\",\"political_status\":\"政治面貌\",\"phone\":\"电话\",\"email\":\"邮件\",\"health_status\":\"健康状况\",\"height\":\"身高(cm)\",\"weight\":\"体重(kg)\",\"vision\":\"视力\",\"is_afraid_heights\":\"是否恐高 enum('是','否')\",\"education_level\":\"学历\",\"graduate_school\":\"毕业院校\",\"major\":\"专业\",\"work_experience_years\":\"相关工作经验年限\",\"applied_position\":\"求职意向\",\"professional\":\"专业技术职称\",\"graduation_time\":\"毕业时间\",\"time_to_work\":\"参加工作时间\",\"registered\":\"籍贯\",\"hjadr\":\"户籍地\",\"company\":\"在职公司\",\"Intention\":\"在职职位\",\"hearing\":\"听力\",\"education\":[{\"start_date\":\"开始日期 (2025-03-23)\",\"end_date\":\"结束日期 (2025-03-23)\",\"school_name\":\"学校名称\",\"witness\":\"证明人\",\"major\":\"专业\"},{\"start_date\":\"开始日期 (2025-03-23)\",\"end_date\":\"结束日期 (2025-03-23)\",\"school_name\":\"学校名称\",\"witness\":\"证明人\",\"major\":\"专业\"}],\"work\":[{\"work_start_date\":\"开始日期 (2025-03-23)\",\"work_end_date\":\"结束日期 (2025-03-23)\",\"company_name\":\"公司名称\",\"position\":\"岗位\",\"leave_reason\":\"离职原因\"},{\"work_start_date\":\"开始日期 (2025-03-23)\",\"work_end_date\":\"结束日期 (2025-03-23)\",\"company_name\":\"公司名称\",\"position\":\"岗位\",\"leave_reason\":\"离职原因\"}],\"family_members\":[{\"relationship\":\"亲属关系\",\"full_name\":\"姓名\",\"work_unit\":\"工作单位\",\"relationship_position\":\"职务\",\"contact\":\"联系电话\"},{\"relationship\":\"亲属关系\",\"full_name\":\"姓名\",\"work_unit\":\"工作单位\",\"relationship_position\":\"职务\",\"contact\":\"联系电话\"}],\"certificates\":[{\"certificate_type\":\"类型 enum('外语','计算机','职业资格','奖励','处罚')\",\"certificate_name\":\"证书名称\",\"level\":\"证书等级\",\"get_date\":\"证书日期\"}]}\n\n以上是模板json\n分析远程文件" . $url . " \n对应模板的key，填写对应的value值，多个的自动增加";
                    $url = $urls;
                }
//            $content = "分析当前远程文件  并按前面给的json 返回对应的json内容".$url;
                D("UserJobDoc")->save(['id' => $userJobDockRow['id'], 'status' => 2, 'update_time' => time()]);
                echo json_encode(['code' => 0, 'data' => $url, 'type' => "1", 'id' => $userJobDockRow['id']]);
                die;
            } else {
                $userJobDockRows = D("UserJobDoc")->where(['is_html' => 1])->find();
                if ($userJobDockRows) {
                    if (strpos($userJobDockRows['content'], 'http') !== false) {
                        $url = $userJobDockRows['content'];
                    } else {
                        $url = 'https://we.zhongcaiguoke.com'.$userJobDockRows['content'];
                    }
                    if ($input['Message'] == 'Idle') {
                        $url .= "这是一个简历表链接地址，你是一个具备丰富经验的职业规划师和信息分析处理专业人员。今天是".date('Y年m月d日')."，请你帮我认真分析上方链接简历信息，并仔细严谨按照我以下要求帮我实现需求：1、请根据上方提供的简历信息分析从网页（https://we.zhongcaiguoke.com/public/getprojectpost?t=".time()."）提供的所有岗位进行匹配，该网页提供了岗位名称及要求；2、最后请你给出一份详细的分析报告以HTML代码形式展示，要求仅限手机响应式访问、借鉴weui清爽布局，国家单位官方色系样式，页面需要包含个人基本信息分析、核心竞争力分析、岗位匹配建议和职业发展建议，具体如下排版，页面标题名称是“简历分析报告-姓名”、顶部区域为个人基本信息即可（姓名、性别、年龄、身高、体重、标准体重建议、学历、专业，如有请显示，没有则忽略，联系方式脱敏处理），接着放核心竞争力内容信息，再接着放岗位匹配分析结果，按照岗位匹配度百分比进行排序，再接着针对简历给予简历优化建议，再接着给予职业发展规划建议。最后在页面顶部居中位置固定加一个醒目的红色背景返回按钮，点击按钮可以返回上一页。";
                    }
//            $content = "分析当前远程文件  并按前面给的json 返回对应的json内容".$url;
                    D("UserJobDoc")->save(['id' => $userJobDockRows['id'], 'is_html' => 2, 'update_time' => time()]);
                    echo json_encode(['code' => 0, 'type' => "2", 'data' => $url, 'id' => $userJobDockRows['id']]);
                    die;
                }
            }
            echo json_encode(['code' => 1, 'next' => 100, 'data' => '']);
        }


    }

    public function gethtml()
    {
        $userJobDockRow = D("UserJobDoc")->where(['is_html' => 1])->find();
        if ($userJobDockRow) {
            if (strpos($userJobDockRow['content'], 'http') !== false) {
                $url = $userJobDockRow['content'];
            } else {
                $url = 'https://we.zhongcaiguoke.com'.$userJobDockRow['content'];
            }
//            $content = "分析当前远程文件  并按前面给的json 返回对应的json内容".$url;
            D("UserJobDoc")->save(['id' => $userJobDockRow['id'], 'is_html' => 2, 'update_time' => time()]);
            echo json_encode(['code' => 0, 'type' => "2", 'data' => $url, 'id' => $userJobDockRow['id']]);
            die;
        }
        echo json_encode(['code' => 1, 'type' => "2", 'next' => 100, 'data' => '']);
    }

    public function postcontent() {
        $this->start = microtime(1);
        $input = file_get_contents("php://input");
        dolog("pwapi/test", is_array($input) ? json_encode($input) : $input, 'wx_api2');

        $input = json_decode($input, true); // 转换为关联数组
        dolog("pwapi/test", is_array($input) ? json_encode($input) : $input, 'wx_api1');
        $type = $input['type'] ? : 0;
        $id  = $input['id'] ? : 0;
        $content = $input['content'];
        if (empty($content) || !$id  || !$type) {
            echo json_encode(['code' => 1, 'msg' => 'error', 'data' => []]);die;
        }
        $userJobDocRow = D("UserJobDoc")->where(['id' => $id])->find();
        if (!$userJobDocRow) {
            echo json_encode(['code' => 1, 'msg' => 'error', 'data' => []]);die;
        }
        if ($type == 2) {
            preg_match_all('/```(.*?)```/s', $content, $matches);
            $contents = array_map('trim', $matches[1]);
            $contents = $contents[0];

            D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'html_content' => htmlspecialchars($contents), 'is_html' => 3, 'update_time' => time()]);
            echo json_encode(['code' => 0, 'msg' => 'ok', 'data' => '提交成功']);die;
        } else {
            // 最严谨的正则表达式，匹配 ```json 开头且以 { 开始的JSON对象
            preg_match_all('/```json\s*(\{.*?\})\s*```/s', $content, $matches);

            $contents = array_map('trim', $matches[1]);

            $contents = $contents[0];

            // 记录提取到的JSON内容（正则表达式已经排除了json标识符）
            \Think\Log::write('postcontent_else: 提取到的JSON内容=' . $contents, 'INFO');

            $mainData = json_decode($contents, 1);
            if (!is_array($mainData)) {
                echo json_encode(['code' => 1, 'msg' => 'ok', 'data' => '提交失败']);die;
            }
            $UserJob = M('UserJob');
            $userJobRow = $UserJob->where(['id' => $userJobDocRow['user_job_id']])->find();
            $UserJob->startTrans();
            try {
                /***************** 主表数据写入 ​*****************/
                // 主表字段映射
                $userJobData = array(
                    'id' => $userJobDocRow['user_job_id'],
                    'type' => 2,
                    'name' => $mainData['name'] ?: '',
                    'gender' => $mainData['gender'] ?: '男',
                    'birthdate' => date('Y-m-d', strtotime($mainData['birthdate'])),
                    'id_number' => $mainData['id_number'] ?:'',
                    'nation' => $mainData['nation'] ?:'',
                    'political_status' => $mainData['political_status']?:'',
                    'marital_status' => $mainData['marital_status']?:'',
                    'health_status' => $mainData['health_status']?:'',
                    'height' => $mainData['height']?:'',
                    'weight' => $mainData['weight']?:'',
                    'vision' => $mainData['vision']?:'',
                    'is_afraid_heights' => $mainData['is_afraid_heights']?:'',
                    'education_level' => $mainData['education_level']?:'',
                    'graduate_school' => $mainData['graduate_school']?:'',
                    'major' => $mainData['major']?:'',
                    'work_experience_years' => $mainData['work_experience_years']?:'',
                    'phone' => $mainData['phone']?:'',
                    'email' => $mainData['email']?:'',
                    'applied_position' => $mainData['applied_position']?:'',
                    'remark' => $mainData['remark']?:'',
                    'professional' => $mainData['professional']?:'',
                    'graduation_time' => $mainData['graduation_time']?:'',
                    'time_to_work' => $mainData['time_to_work']?:'',
                    'registered' => $mainData['registered']?:'',
                    'hjadr' => $mainData['hjadr']?:'',
                    'company' => $mainData['company']?:'',
                    'Intention' => $mainData['Intention']?:'',
                    'hearing' => $mainData['hearing']?:'',
                    'create_time' => time()
                );

                // 保护关键字段：如果UserJob记录已存在，保持原有的关键字段不被覆盖
                if ($userJobRow) {
                    // 保护resume_type字段（简历类型：1=自有简历，2=招就办简历）
                    if (isset($userJobRow['resume_type']) && $userJobRow['resume_type']) {
                        $userJobData['resume_type'] = $userJobRow['resume_type'];
                    }

                    // 保护service_station_id字段
                    if (isset($userJobRow['service_station_id']) && $userJobRow['service_station_id']) {
                        $userJobData['service_station_id'] = $userJobRow['service_station_id'];
                    }

                    // 保护remark字段（求职诉求）
                    if (isset($userJobRow['remark']) && !empty($userJobRow['remark'])) {
                        $userJobData['remark'] = $userJobRow['remark'];
                    }

                    // 保护user_id字段
                    if (isset($userJobRow['user_id']) && $userJobRow['user_id']) {
                        $userJobData['user_id'] = $userJobRow['user_id'];
                    }

                    // 如果现有记录中已有身份证号且AI解析结果为空，保持原有身份证号
                    if (isset($userJobRow['id_number']) && !empty($userJobRow['id_number']) && empty($mainData['id_number'])) {
                        $userJobData['id_number'] = $userJobRow['id_number'];
                    }
                }

                // 主表插入[2,5](@ref)
                if ($userJobRow) {
                    $userJobBoolean = $UserJob->save($userJobData);
                    $userJobId = $userJobRow['id'];
                } else {
                    $userJobId = $UserJob->add($userJobData);
                }
                if (!$userJobId) throw new Exception('主表写入失败');
                /***************** 教育经历写入 ​*****************/
                if (!empty($mainData['education'])) {
                    $EduModel = M('Education');
                    foreach ($mainData['education'] as $edu) {
                        if (strtotime($edu['start_date']) === false && strpos($edu['end_date'], '.') !== false) {
                            $eduStartDate = strtotime(preg_replace('/\./', '-',$edu['start_date'].".01"));
                        } else if (strtotime($edu['start_date']) === false && strpos($edu['start_date'], '-') !== false) {
                            $eduStartDate = strtotime($edu['start_date']."-01");
                        } else {
                            $eduStartDate = strtotime($edu['start_date']);
                        }
                        if (strtotime($edu['end_date']) === false && strpos($edu['end_date'], '.') !== false) {
                            $eduEndDate = strtotime(preg_replace('/\./', '-',$edu['end_date'].".01"));
                        }else if (strtotime($edu['end_date']) === false && strpos($edu['end_date'], '-') !== false) {
                            $eduEndDate = strtotime($edu['end_date']."-01");
                        } else {
                            $eduEndDate = strtotime($edu['end_date']);
                        }
                        $eduData = array(
                            'user_job_id' => $userJobId,
                            'start_date' => date('Y-m-d', $eduStartDate),
                            'end_date' => date('Y-m-d', $eduEndDate),
                            'school_name' => $edu['school_name'] ?:'',
                            'major' => $edu['major']?:'',
                            'witness' => $edu['witness']?:'',
                        );
                        if (!$EduModel->add($eduData)) throw new Exception('教育经历写入失败');
                    }
                }

                /***************** 工作经历写入 ​*****************/
                if (!empty($mainData['work'])) {
                    $WorkModel = M('WorkExperience');
                    foreach ($mainData['work'] as $work) {
                        if (strtotime($work['work_start_date']) === false && strpos($work['work_start_date'], '.') !== false) {
                            $wordStartDate = strtotime(preg_replace('/\./', '-',$work['work_start_date'].".01"));
                        } else if (strtotime($work['work_start_date']) === false && strpos($work['work_start_date'], '-') !== false) {
                            $wordStartDate = strtotime($work['work_start_date']."-01");
                        } else {
                            $wordStartDate = strtotime($work['work_start_date']);
                        }
                        if (strtotime($work['work_end_date']) === false && strpos($work['work_end_date'], '.') !== false) {
                            $wordEndDate = strtotime(preg_replace('/\./', '-',$work['work_end_date'].".01"));
                        }else if (strtotime($work['work_end_date']) === false && strpos($work['work_end_date'], '-') !== false) {
                            $wordEndDate = strtotime($work['work_end_date']."-01");
                        } else {
                            $wordEndDate = strtotime($work['work_end_date']);
                        }
                        $workData = array(
                            'user_job_id' => $userJobId,
                            'start_date' => date('Y-m-d', $wordStartDate),
                            'end_date' => date('Y-m-d', $wordEndDate),
                            'company_name' => $work['company_name']?:'',
                            'position' => $work['position']?:'',
                            'leave_reason' => $work['leave_reason']?:'',
                        );
                        if (!$WorkModel->add($workData)) throw new Exception('工作经历写入失败');
                    }
                }

                /***************** 证书信息写入 *****************/
                if (!empty($mainData['certificates'])) {
                    $CertModel = M('SkillsCertificates');
                    foreach ($mainData['certificates'] as $cert) {
                        if (strtotime($cert['get_date']) === false && strpos($cert['get_date'], '.') !== false) {
                            $get_date = strtotime(preg_replace('/\./', '-',$cert['get_date'].".01"));
                        }else if (strtotime($cert['get_date']) === false && strpos($cert['get_date'], '-') !== false) {
                            $get_date = strtotime($cert['get_date']."-01");
                        } else {
                            $get_date = strtotime($cert['get_date']);
                        }
                        $certData = array(
                            'user_job_id' => $userJobId ?:'',
                            'certificate_type' => $cert['certificate_type']?:'',
                            'certificate_name' => $cert['certificate_name']?:'',
                            'level' => $cert['level']?:'',
                            'get_date' => date('Y-m-d', $get_date)
                        );
                        if (!$CertModel->add($certData)) throw new Exception('证书信息写入失败');
                    }
                }


                /***************** 家庭写入 *****************/
                if (!empty($mainData['family_members'])) {
                    $familyMembersModel = M('FamilyMembers');
                    foreach ($mainData['family_members'] as $familyMembersRow) {
                        $familyMembersData = array(
                            'user_job_id' => $userJobId ?:'',
                            'relationship' => $familyMembersRow['relationship']?:'',
                            'full_name' => $familyMembersRow['full_name']?:'',
                            'work_unit' => $familyMembersRow['work_unit']?:'',
                            'position' => $familyMembersRow['relationship_position']?:'',
                            'contact' => $familyMembersRow['contact']?:'',
                        );
                        if (!$familyMembersModel->add($familyMembersData)) throw new Exception('家庭信息写入失败');
                    }
                }

                // 提交事务
                D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'status' => 3, 'update_time' => time()]);
                $UserJob->commit();
                echo json_encode(['code' => 0, 'msg' => 'ok', 'data' => '提交成功']);
                die;
            } catch (Exception $e) {
                // 回滚事务
                $UserJob->rollback();
                D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'status' => 4, 'update_time' => time()]);
                echo json_encode(['code' => 1, 'msg' => $e->getMessage(), 'data' => '提交失败']);
                die;
            }
        }
    }

    public function posthtml()
    {
        $this->start = microtime(1);
        $input = file_get_contents("php://input");
        $input = json_decode($input, true); // 转换为关联数组
        if (!is_array($input) && json_last_error() !== JSON_ERROR_NONE) {
            echo json_encode(['code' => 1, 'msg' => 'ok', 'data' => "JSON解析失败：" . json_last_error_msg()]);
            die;
        }

        $id = $input['id'] ?: 0;
        if ($id) {
            $htmls = $input['content'];
            $userJobDocRow = D("UserJobDoc")->where(['id' => $id])->find();
            if ($userJobDocRow) {
                D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'html_content' => htmlspecialchars($htmls), 'is_html' => 3, 'update_time' => time()]);
                echo json_encode(['code' => 0, 'msg' => 'ok', 'data' => '提交成功']);
            }
        } else {
            echo json_encode(['code' => 1, 'msg' => 'ok', 'data' => '提交失败']);
        }
    }

    public function postjobs()
    {
        $this->start = microtime(1);
        $input = file_get_contents("php://input");
        $input = json_decode($input, true); // 转换为关联数组
        if (!is_array($input) && json_last_error() !== JSON_ERROR_NONE) {
            echo json_encode(['code' => 1, 'msg' => 'ok', 'data' => "JSON解析失败：" . json_last_error_msg()]);
            die;
        }
        dolog("pwapi/test", is_array($input) ? json_encode($input) : $input, 'wx_api1');
        $cleanJson = preg_replace('/,\s*([}\]])/m', '$1', $input['content']); // 删除末尾逗号
        $cleanJson = str_replace(["\r", "\n", "\t"], '', $cleanJson);  // 清理特殊字符
        if (!is_array($cleanJson)) {
            $mainData = json_decode($cleanJson, 1);
        } else {
            $mainData = $cleanJson;
        }
        // 开启事务
        $UserJob = M('UserJob');
        $id = $input['id'] ?: 0;
        if (!$id) $this->error('参数错误');
        if (count($mainData) <= 1) {
            echo json_encode(['code' => 1, 'msg' => 'ok', 'data' => "JSON解析失败：" . json_last_error_msg()]);
            die;
        }
        $userJobDocRow = D("UserJobDoc")->where(['id' => $id])->find();
        $userJobRow = $UserJob->where(['id' => $userJobDocRow['user_job_id']])->find();


        $UserJob->startTrans();
        try {
            /***************** 主表数据写入 ​*****************/
            // 主表字段映射
            $userJobData = array(
                'id' => $userJobDocRow['user_job_id'],
                'type' => 2,
                'name' => $mainData['name'] ?: '',
                'gender' => $mainData['gender'] ?: '男',
                'birthdate' => date('Y-m-d', strtotime($mainData['birthdate'])),
                'id_number' => $mainData['id_number'] ?:'',
                'nation' => $mainData['nation'] ?:'',
                'political_status' => $mainData['political_status']?:'',
                'marital_status' => $mainData['marital_status']?:'',
                'health_status' => $mainData['health_status']?:'',
                'height' => $mainData['height']?:"",
                'weight' => $mainData['weight']?:"",
                'vision' => $mainData['vision']?:'',
                'is_afraid_heights' => $mainData['is_afraid_heights']?:'',
                'education_level' => $mainData['education_level']?:'',
                'graduate_school' => $mainData['graduate_school']?:'',
                'major' => $mainData['major']?:"",
                'work_experience_years' => $mainData['work_experience_years']?:'',
                'phone' => $mainData['phone']?:'',
                'email' => $mainData['email']?:'',
                'applied_position' => $mainData['applied_position']?:"",
                'remark' => $mainData['remark']?:'',
                'professional' => $mainData['professional']?:"",
                'graduation_time' => $mainData['graduation_time']?:"",
                'time_to_work' => $mainData['time_to_work']?:"",
                'registered' => $mainData['registered']?:"",
                'hjadr' => $mainData['hjadr']?:"",
                'company' => $mainData['company']?:"",
                'Intention' => $mainData['Intention']?:"",
                'hearing' => $mainData['hearing']?:'',
                'create_time' => time()
            );

            // 保护关键字段：如果UserJob记录已存在，保持原有的关键字段不被覆盖
            if ($userJobRow) {
                // 保护resume_type字段（简历类型：1=自有简历，2=招就办简历）
                if (isset($userJobRow['resume_type']) && $userJobRow['resume_type']) {
                    $userJobData['resume_type'] = $userJobRow['resume_type'];
                }

                // 保护service_station_id字段
                if (isset($userJobRow['service_station_id']) && $userJobRow['service_station_id']) {
                    $userJobData['service_station_id'] = $userJobRow['service_station_id'];
                }

                // 保护remark字段（求职诉求）
                if (isset($userJobRow['remark']) && !empty($userJobRow['remark'])) {
                    $userJobData['remark'] = $userJobRow['remark'];
                }

                // 保护user_id字段
                if (isset($userJobRow['user_id']) && $userJobRow['user_id']) {
                    $userJobData['user_id'] = $userJobRow['user_id'];
                }

                // 如果现有记录中已有身份证号且AI解析结果为空，保持原有身份证号
                if (isset($userJobRow['id_number']) && !empty($userJobRow['id_number']) && empty($mainData['id_number'])) {
                    $userJobData['id_number'] = $userJobRow['id_number'];
                }
            }

            // 主表插入[2,5](@ref)
            if ($userJobRow) {
                $userJobBoolean = $UserJob->save($userJobData);
                $userJobId = $userJobRow['id'];
            } else {
                $userJobId = $UserJob->add($userJobData);
            }
            if (!$userJobId) throw new Exception('主表写入失败');
            /***************** 教育经历写入 ​*****************/
            if (!empty($mainData['education'])) {
                $EduModel = M('Education');
                foreach ($mainData['education'] as $edu) {
                    if (strtotime($edu['start_date']) === false && strpos($edu['end_date'], '.') !== false) {
                        $eduStartDate = strtotime(preg_replace('/\./', '-',$edu['start_date'].".01"));
                    } else if (strtotime($edu['start_date']) === false && strpos($edu['start_date'], '-') !== false) {
                        $eduStartDate = strtotime($edu['start_date']."-01");
                    } else {
                        $eduStartDate = strtotime($edu['start_date']);
                    }
                    if (strtotime($edu['end_date']) === false && strpos($edu['end_date'], '.') !== false) {
                        $eduEndDate = strtotime(preg_replace('/\./', '-',$edu['end_date'].".01"));
                    }else if (strtotime($edu['end_date']) === false && strpos($edu['end_date'], '-') !== false) {
                        $eduEndDate = strtotime($edu['end_date']."-01");
                    } else {
                        $eduEndDate = strtotime($edu['end_date']);
                    }
                    $eduData = array(
                        'user_job_id' => $userJobId,
                        'start_date' => date('Y-m-d', $eduStartDate),
                        'end_date' => date('Y-m-d', $eduEndDate),
                        'school_name' => $edu['school_name'] ?:'',
                        'major' => $edu['major']?:'',
                        'witness' => $edu['witness']?:'',
                    );
                    if (!$EduModel->add($eduData)) throw new Exception('教育经历写入失败');
                }
            }

            /***************** 工作经历写入 ​*****************/
            if (!empty($mainData['work'])) {
                $WorkModel = M('WorkExperience');
                foreach ($mainData['work'] as $work) {
                    if (strtotime($work['work_start_date']) === false && strpos($work['work_start_date'], '.') !== false) {
                        $wordStartDate = strtotime(preg_replace('/\./', '-',$work['work_start_date'].".01"));
                    } else if (strtotime($work['work_start_date']) === false && strpos($work['work_start_date'], '-') !== false) {
                        $wordStartDate = strtotime($work['work_start_date']."-01");
                    } else {
                        $wordStartDate = strtotime($work['work_start_date']);
                    }
                    if (strtotime($work['work_end_date']) === false && strpos($work['work_end_date'], '.') !== false) {
                        $wordEndDate = strtotime(preg_replace('/\./', '-',$work['work_end_date'].".01"));
                    }else if (strtotime($work['work_end_date']) === false && strpos($work['work_end_date'], '-') !== false) {
                        $wordEndDate = strtotime($work['work_end_date']."-01");
                    } else {
                        $wordEndDate = strtotime($work['work_end_date']);
                    }
                    $workData = array(
                        'user_job_id' => $userJobId,
                        'start_date' => date('Y-m-d', $wordStartDate),
                        'end_date' => date('Y-m-d', $wordEndDate),
                        'company_name' => $work['company_name']?:'',
                        'position' => $work['position']?:'',
                        'leave_reason' => $work['leave_reason']?:'',
                    );
                    if (!$WorkModel->add($workData)) throw new Exception('工作经历写入失败');
                }
            }

            /***************** 证书信息写入 *****************/
            if (!empty($mainData['certificates'])) {
                $CertModel = M('SkillsCertificates');
                foreach ($mainData['certificates'] as $cert) {
                    if (strtotime($cert['get_date']) === false && strpos($cert['get_date'], '.') !== false) {
                        $get_date = strtotime(preg_replace('/\./', '-',$cert['get_date'].".01"));
                    }else if (strtotime($cert['get_date']) === false && strpos($cert['get_date'], '-') !== false) {
                        $get_date = strtotime($cert['get_date']."-01");
                    } else {
                        $get_date = strtotime($cert['get_date']);
                    }
                    $certData = array(
                        'user_job_id' => $userJobId ?:'',
                        'certificate_type' => $cert['certificate_type']?:'',
                        'certificate_name' => $cert['certificate_name']?:'',
                        'level' => $cert['level']?:'',
                        'get_date' => date('Y-m-d', $get_date)
                    );
                    if (!$CertModel->add($certData)) throw new Exception('证书信息写入失败');
                }
            }


            /***************** 家庭写入 *****************/
            if (!empty($mainData['family_members'])) {
                $familyMembersModel = M('FamilyMembers');
                foreach ($mainData['family_members'] as $familyMembersRow) {
                    $familyMembersData = array(
                        'user_job_id' => $userJobId ?:'',
                        'relationship' => $familyMembersRow['relationship']?:'',
                        'full_name' => $familyMembersRow['full_name']?:'',
                        'work_unit' => $familyMembersRow['work_unit']?:'',
                        'position' => $familyMembersRow['relationship_position']?:'',
                        'contact' => $familyMembersRow['contact']?:'',
                    );
                    if (!$familyMembersModel->add($familyMembersData)) throw new Exception('家庭信息写入失败');
                }
            }

            // 提交事务
            D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'status' => 3, 'update_time' => time()]);
            $UserJob->commit();
            echo json_encode(['code' => 0, 'msg' => 'ok', 'data' => '提交成功']);
            die;
        } catch (Exception $e) {
            // 回滚事务
            $UserJob->rollback();
            D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'status' => 4, 'update_time' => time()]);
            echo json_encode(['code' => 1, 'msg' => $e->getMessage(), 'data' => '提交失败']);
            die;
        }
    }

    public function postjobs1()
    {
        $this->start = microtime(1);
        $input = file_get_contents("php://input");
        $cleanedJson = str_replace(["\r", "\n"], '', $input); // 替换换行符[7,8](@ref)
        $input = json_decode($cleanedJson, true); // 转换为关联数组
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo json_encode(['code' => 1, 'msg' => 'ok', 'data' => "JSON解析失败：" . json_last_error_msg()]);
            die;
        }
        dolog("pwapi/test", is_array($input) ? json_encode($input) : $input, 'wx_api1');
        $this->data = $input;
        dolog("pwapi/test", json_encode($this->data), 'wx_api');
        $data = $this->data['content'];
        $json = json_decode($data, 1);
        $id = $this->data['id'];
        if (!$id) $this->error('参数错误');
        $userJobDocRow = D("UserJobDoc")->where(['id' => $id])->find();

        // 检查是否已经存在关联的UserJob记录
        $existingUserJobId = $userJobDocRow['user_job_id'];
        $userJobId = null;

        // 简历主表数据组装
        $userJobData = array(
            'user_id' => $userJobDocRow['user_id'],
            'service_station_id' => $userJobDocRow['service_station_id'],
            'name' => $json['basic']['name'],
            'type' => 2,
            'gender' => ($json['basic']['sex'] == '男') ? '男' : '女', // 字段映射转换[1](@ref)
            'birthdate' => date('Y-m-d', strtotime($json['basic']['birth'])),
            'phone' => $json['basic']['phone'],
            'email' => strtolower($json['basic']['email']), // 统一小写
            'height' => intval(str_replace('CM', '', $json['basic']['height'])),
            'weight' => intval(str_replace('kg', '', $json['basic']['weight'])),
            'applied_position' => $json['job_intention'],
            'remark' => $json['remark']?:'',
            'professional' => $json['professional']?:"",
            'graduation_time' => $json['graduation_time']?:"",
            'time_to_work' => $json['time_to_work']?:"",
            'registered' => $json['registered']?:"",
            'hjadr' => $json['hjadr']?:"",
            'company' => $json['company']?:"",
            'Intention' => $json['Intention']?:"",
            'hearing' => $json['hearing']?:'',
            'update_time' => time(),
            // 其他字段根据业务需求补充
        );

// 启动事务[6](@ref)
        M()->startTrans();
        try {
            D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'status' => 2, 'update_time' => time()]);

            if ($existingUserJobId && $existingUserJobId > 0) {
                // 获取现有UserJob记录以保护关键字段
                $existingUserJob = D('UserJob')->where(['id' => $existingUserJobId])->find();

                // 保护resume_type字段（简历类型：1=自有简历，2=招就办简历）
                if ($existingUserJob && isset($existingUserJob['resume_type']) && $existingUserJob['resume_type']) {
                    $userJobData['resume_type'] = $existingUserJob['resume_type'];
                }

                // 保护remark字段（求职诉求）
                if ($existingUserJob && isset($existingUserJob['remark']) && !empty($existingUserJob['remark'])) {
                    $userJobData['remark'] = $existingUserJob['remark'];
                }

                // 如果已经存在UserJob记录，则更新现有记录
                $userJobData['id'] = $existingUserJobId;
                $result = D('UserJob')->save($userJobData);
                if ($result !== false) {
                    $userJobId = $existingUserJobId;
                } else {
                    throw new Exception('更新UserJob记录失败');
                }
            } else {
                // 如果不存在UserJob记录，则创建新记录
                $userJobData['create_time'] = time();
                $userJobId = M('UserJob')->data($userJobData)->add();
                if (!$userJobId) throw new Exception('创建UserJob记录失败');

                // 更新UserJobDoc记录，关联新创建的UserJob记录
                D("UserJobDoc")->save(['id' => $userJobDocRow['id'], 'user_job_id' => $userJobId]);
            }

            // 处理教育经历（z_education）[3](@ref)
            if (!empty($json['education'])) {
                $eduData = array();
                foreach ($data['education'] as $item) {
                    // 处理日期格式 2022.09 -> 2022-09-01
                    list($start, $end) = explode('-', $item['time']);

                    $eduData[] = array(
                        'user_job_id' => $userJobId,
                        'start_date' => date('Y-m-d', strtotime(str_replace('.', '-', $start))),
                        'end_date' => date('Y-m-d', strtotime(str_replace('.', '-', $end))),
                        'school_name' => $item['school'],
                        'major' => $item['major'],
                        'witness' => $item['degree'] // 默认证明人
                    );
                }
                if ($eduData) {
                    D('Education')->addAll($eduData);
                }
            }
            // 提交事务
            M()->commit();
            echo json_encode(['status' => 0, 'msg' => '数据写入成功']);
            die;
        } catch (Exception $e) {
            M()->rollback();
            echo json_encode(['status' => 1, 'msg' => $e->getMessage()]);
            die;
        }

    }

}