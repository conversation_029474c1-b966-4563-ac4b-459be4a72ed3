<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title>{:$row['name']}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="/static/postcontent/styles/swiper.min.css">
    <link rel="stylesheet" href="/static/postcontent/styles/main.css?t=<%=now()%>">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="/static/postcontent/js/jquery.min.js"></script>
</head>
<body>



    <header class="header-phone d-md-none">
        <a href="{$backUrl}" class="btn-back"></a>
        <h3>{:$row['name']}介绍</h3>
        <a href="" class="btn-menu"></a>
    </header>
    <div class="alert-menu">
        <div class="over-close"></div>
        <div class="box">
            <ul class="list" style="font-size: 16px;">
                <php>foreach($projectList as $projectRow) {</php>
                <li><a href="{:U('index/postcontent', ['id' => $projectRow['id']])}">{:$projectRow['name']}</a></li>
                <php>}</php>
            </ul>
        </div>
    </div>
    <div style="margin:20px 5px;border: 1px solid #000;">
        <div style="padding-left:6px;padding-right:10px;">
            {:htmlspecialchars_decode($row['content'])}
        </div>
    </div>
    <section class="uc-wrap uc-home">
        <div class="cpt">
            * 以上信息仅供参考，具体最新文件为准 *
        </div>
    </section>
    <script src="/static/postcontent/js/swiper.min.js"></script>
    <script src="/static/postcontent/js/main.js"></script>
    <script>
        $(function() {})

        new Swiper('.swiper-container1', {
            loop: true,
            observeParents: true,
            observer: true,
            pagination: {
                el: '.swiper-container1 .swiper-pagination',
                clickable: true,
            },
            autoplay: {
                delay: 2500,
                disableOnInteraction: false,
            },
        });

        new Swiper('.swiper-container2', {
            loop: true,
            observeParents: true,
            observer: true,
            direction: 'vertical',
            pagination: {
                el: '.swiper-container2 .swiper-pagination',
                clickable: true,
            },
            autoplay: {
                delay: 2500,
                disableOnInteraction: false,
            },
        });
    </script>
    <script>
        var title = '{:$row["name"]}';
        var img = 'https://c.zhongcaiguoke.com/static/stations/images/logozcgk.png';

        var desc = '{$description}';
        var url = window.location.href;

        wx.config({
            debug: false, // 关闭调试模式
            appId: '{$appid}',
            timestamp: {:$wxconf['timestamp']?:0},
            nonceStr: '{$wxconf.noncestr}',
            signature: '{$wxconf.signature}',
            jsApiList: [
                'updateAppMessageShareData', 
                'onMenuShareTimeline',
                'onMenuShareAppMessage'
            ]
        });
        
        wx.ready(function () {
            // 分享给朋友（新版）
            wx.updateAppMessageShareData({ 
                title: title,
                desc: desc,
                link: url,
                imgUrl: img,
                success: function () {
                    console.log('分享内容设置成功');
                }
            });
            
            // 分享到朋友圈（兼容旧版）
            wx.onMenuShareTimeline({
                title: title,
                link: url,
                imgUrl: img,
                success: function () {
                    console.log('分享到朋友圈设置成功');
                }
            });
            
            // 分享给朋友（兼容旧版）
            wx.onMenuShareAppMessage({
                title: title,
                desc: desc,
                link: url,
                imgUrl: img,
                type: '', 
                dataUrl: '',
                success: function () {
                    console.log('分享给朋友设置成功');
                }
            });
        });
        
        wx.error(function (res) {
            // 静默处理错误
        });
    </script>


</body>

</html>