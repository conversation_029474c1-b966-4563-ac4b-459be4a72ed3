<?php
namespace Common\Model;

use Think\Model;

class StationMoneyModel extends Model
{
    /**
     * 注意：当前模型与数据库表结构存在不匹配
     * 数据库表 z_station_money 实际结构为：
     * - id (int)
     * - service_station_id (int)
     * - type (tinyint)
     * - money (int)
     * - withdrawal_request_id (int)
     * - withdrawal_id (int)
     * - create_time (int)
     *
     * 而模型中定义的状态和类型以下仅作为参考，实际记录时需要注意字段匹配
     */
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['updated_at', 'time', self::MODEL_BOTH, 'function'],
    ];

    public $status = [
        '1' => ['text' => '提现申请', 'style' => 'info'],
        '2' => ['text' => '处理中', 'style' => 'info'],
        '3' => ['text' => '待开票', 'style' => 'info'],
        '4' => ['text' => '成功', 'style' => 'success'],
        '5' => ['text' => '失败', 'style' => 'danger'],
    ];

    public $type = [
        '1' => ['text' => '提现申请', 'style' => 'info'],
        '2' => ['text' => '资源包回购', 'style' => 'info'],
        '3' => ['text' => '简历收入', 'style' => 'info'],
        '4' => ['text' => '资金返还', 'style' => 'success'],
        '5' => ['text' => '培训奖励(冻结)', 'style' => 'warning'],
        '6' => ['text' => '培训奖励(解冻)', 'style' => 'success'],
        '7' => ['text' => '上级培训奖励', 'style' => 'success'],
        '8' => ['text' => '余额转入冻结', 'style' => 'warning'],
        '9' => ['text' => '冻结转入余额', 'style' => 'success'],
        '10' => ['text' => '培训奖励(扣除)', 'style' => 'danger'],
        '11' => ['text' => '订单取消奖励回退', 'style' => 'danger'],
        '12' => ['text' => '招就办奖励(解冻)', 'style' => 'success'],
        '13' => ['text' => '招就办佣金(冻结)', 'style' => 'warning'],
    ];
}