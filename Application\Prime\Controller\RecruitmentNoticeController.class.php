<?php

namespace Prime\Controller;

use Common\Controller\PrimeController;

class RecruitmentNoticeController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 招聘公告列表
     */
    public function index()
    {
        $where = [];
        
        // 搜索条件
        $keyword = I('get.keyword', '');
        if (!empty($keyword)) {
            $where['title|company_name'] = ['like', "%{$keyword}%"];
        }
        
        $status = I('get.status', '');
        if ($status !== '') {
            $where['status'] = intval($status);
        }

        $page = I('get.p', 1, 'intval');
        $pageSize = 20;

        $model = D('RecruitmentNotice');
        $result = $model->getNoticeList($where, $page, $pageSize);

        // 分页
        $totalPages = ceil($result['count'] / $pageSize);
        
        $this->assign('list', $result['list']);
        $this->assign('count', $result['count']);
        $this->assign('page', $page);
        $this->assign('totalPages', $totalPages);
        $this->assign('keyword', $keyword);
        $this->assign('status', $status);
        $this->assign('statusOptions', $model->status);
        
        $this->display();
    }

    /**
     * 添加/编辑招聘公告
     */
    public function edit()
    {
        $id = I('get.id', 0, 'intval');
        $model = D('RecruitmentNotice');

        if (IS_POST) {
            $data = I('post.');

            if ($model->create($data)) {
                if ($id) {
                    $data['id'] = $id;
                    $result = $model->save($data);
                } else {
                    $result = $model->add($data);
                }

                if ($result !== false) {
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 1, 'info' => '保存成功！', 'url' => U('index')]);
                    } else {
                        $this->success('保存成功！', U('index'));
                    }
                } else {
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 0, 'info' => '保存失败！']);
                    } else {
                        $this->error('保存失败！');
                    }
                }
            } else {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => $model->getError()]);
                } else {
                    $this->error($model->getError());
                }
            }
        }

        $info = [];
        if ($id) {
            $info = $model->find($id);
            if (!$info) {
                $this->error('记录不存在！');
            }
        }

        $this->assign('info', $info);
        $this->assign('id', $id);
        $this->display();
    }

    /**
     * 删除招聘公告
     */
    public function delete()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误！');
        }

        $model = D('RecruitmentNotice');
        $result = $model->deleteNotice($id);

        if ($result) {
            $this->success('删除成功！');
        } else {
            $this->error('删除失败！');
        }
    }

    /**
     * 状态切换
     */
    public function toggle()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误！');
        }

        $model = D('RecruitmentNotice');
        $info = $model->find($id);
        if (!$info) {
            $this->error('记录不存在！');
        }

        $newStatus = $info['status'] ? 0 : 1;
        $result = $model->where(['id' => $id])->save(['status' => $newStatus]);

        if ($result !== false) {
            $this->success('状态更新成功！');
        } else {
            $this->error('状态更新失败！');
        }
    }

    /**
     * 岗位管理
     */
    public function posts()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误！');
        }

        $model = D('RecruitmentNotice');
        $notice = $model->find($id);
        if (!$notice) {
            $this->error('招聘公告不存在！');
        }

        if (IS_POST) {
            $postIds = I('post.post_ids', []);
            $result = $model->addNoticePosts($id, $postIds);

            if ($result !== false) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 1, 'info' => '岗位关联成功！', 'url' => U('posts', ['id' => $id])]);
                } else {
                    $this->success('岗位关联成功！', U('posts', ['id' => $id]));
                }
            } else {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '岗位关联失败！']);
                } else {
                    $this->error('岗位关联失败！');
                }
            }
        }

        // 获取所有可用岗位
        $allPosts = M('ProjectPost')->where(['status' => 1])->select();
        
        // 获取已关联岗位
        $linkedPosts = $model->getNoticePosts($id);
        $linkedPostIds = array_column($linkedPosts, 'id');

        $this->assign('notice', $notice);
        $this->assign('allPosts', $allPosts);
        $this->assign('linkedPosts', $linkedPosts);
        $this->assign('linkedPostIds', $linkedPostIds);
        $this->assign('id', $id);
        $this->display();
    }

    /**
     * 岗位要求配置
     */
    public function requirements()
    {
        $noticeId = I('get.notice_id', 0, 'intval');
        $postId = I('get.post_id', 0, 'intval');
        
        if (!$noticeId || !$postId) {
            $this->error('参数错误！');
        }

        $requirementsModel = D('PostRequirements');

        if (IS_POST) {
            $data = I('post.');
            $data['notice_id'] = $noticeId;
            $data['post_id'] = $postId;

            $result = $requirementsModel->saveRequirements($data);

            if ($result !== false) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 1, 'info' => '保存成功！', 'url' => U('posts', ['id' => $noticeId])]);
                } else {
                    $this->success('保存成功！', U('posts', ['id' => $noticeId]));
                }
            } else {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '保存失败！']);
                } else {
                    $this->error('保存失败！');
                }
            }
        }

        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->find($noticeId);
        
        // 获取岗位信息
        $post = M('ProjectPost')->find($postId);
        
        // 获取现有要求
        $requirements = $requirementsModel->getRequirements($postId, $noticeId);

        $this->assign('notice', $notice);
        $this->assign('post', $post);
        $this->assign('requirements', $requirements ?: []);
        $this->assign('genderOptions', $requirementsModel->genderOptions);
        $this->assign('educationOptions', $requirementsModel->educationOptions);
        $this->assign('afraidHeightsOptions', $requirementsModel->afraidHeightsOptions);
        $this->assign('maritalStatusOptions', $requirementsModel->maritalStatusOptions);
        $this->assign('freshGraduateOptions', $requirementsModel->freshGraduateOptions);
        $this->assign('noticeId', $noticeId);
        $this->assign('postId', $postId);
        $this->display();
    }

    /**
     * 批量配置岗位要求
     */
    public function batchRequirements()
    {
        $noticeId = I('get.notice_id', 0, 'intval');
        if (!$noticeId) {
            $this->error('参数错误！');
        }

        $requirementsModel = D('PostRequirements');

        if (IS_POST) {
            $postIds = I('post.post_ids', []);
            $requirements = I('post.');
            unset($requirements['post_ids']);

            if (empty($postIds)) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '请选择要配置的岗位！']);
                } else {
                    $this->error('请选择要配置的岗位！');
                }
                return;
            }

            $result = $requirementsModel->batchSaveRequirements($noticeId, $postIds, $requirements);

            if ($result) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 1, 'info' => '批量配置成功！', 'url' => U('posts', ['id' => $noticeId])]);
                } else {
                    $this->success('批量配置成功！', U('posts', ['id' => $noticeId]));
                }
            } else {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '批量配置失败！']);
                } else {
                    $this->error('批量配置失败！');
                }
            }
        }

        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->find($noticeId);
        
        // 获取公告下的岗位
        $posts = D('RecruitmentNotice')->getNoticePosts($noticeId);

        $this->assign('notice', $notice);
        $this->assign('posts', $posts);
        $this->assign('genderOptions', $requirementsModel->genderOptions);
        $this->assign('educationOptions', $requirementsModel->educationOptions);
        $this->assign('afraidHeightsOptions', $requirementsModel->afraidHeightsOptions);
        $this->assign('maritalStatusOptions', $requirementsModel->maritalStatusOptions);
        $this->assign('freshGraduateOptions', $requirementsModel->freshGraduateOptions);
        $this->assign('noticeId', $noticeId);
        $this->display();
    }

    /**
     * 执行简历匹配
     */
    public function executeMatch()
    {
        $noticeId = I('get.notice_id', 0, 'intval');
        if (!$noticeId) {
            $this->error('参数错误！');
        }

        $matchModel = D('ResumePostMatch');
        $result = $matchModel->executeMatching($noticeId);

        if ($result !== false) {
            $this->success("匹配完成！共处理 {$result} 条记录", U('matchResults', ['notice_id' => $noticeId]));
        } else {
            $this->error('匹配失败！');
        }
    }

    /**
     * 匹配结果
     */
    public function matchResults()
    {
        $noticeId = I('get.notice_id', 0, 'intval');
        $postId = I('get.post_id', 0, 'intval');
        $isQualified = I('get.is_qualified', '');
        $minScore = I('get.min_score', 0, 'intval');
        $page = I('get.p', 1, 'intval');

        $where = [];
        if ($noticeId) {
            $where['notice_id'] = $noticeId;
        }
        if ($postId) {
            $where['post_id'] = $postId;
        }
        if ($isQualified !== '') {
            $where['is_qualified'] = intval($isQualified);
        }
        if ($minScore > 0) {
            $where['min_score'] = $minScore;
        }

        $matchModel = D('ResumePostMatch');
        $result = $matchModel->getMatchResults($where, $page, 18);

        // 获取招聘公告列表
        $notices = D('RecruitmentNotice')->where(['status' => 1])->getField('id,title', true);
        
        // 获取岗位列表
        $posts = [];
        if ($noticeId) {
            $posts = D('RecruitmentNotice')->getNoticePosts($noticeId);
        }

        // 获取统计信息
        $stats = [];
        if ($noticeId) {
            $stats = $matchModel->getNoticeMatchStats($noticeId);
        }

        $pageSize = 18;
        $totalPages = ceil($result['count'] / $pageSize);

        // 计算显示记录范围
        $startRecord = ($page - 1) * $pageSize + 1;
        $endRecord = min($page * $pageSize, $result['count']);

        // 如果没有记录，调整显示
        if ($result['count'] == 0) {
            $startRecord = 0;
            $endRecord = 0;
        }

        $this->assign('list', $result['list']);
        $this->assign('count', $result['count']);
        $this->assign('page', $page);
        $this->assign('totalPages', $totalPages);
        $this->assign('pageSize', $pageSize);
        $this->assign('startRecord', $startRecord);
        $this->assign('endRecord', $endRecord);
        $this->assign('notices', $notices);
        $this->assign('posts', $posts);
        $this->assign('stats', $stats);
        $this->assign('noticeId', $noticeId);
        $this->assign('postId', $postId);
        $this->assign('isQualified', $isQualified);
        $this->assign('minScore', $minScore);
        $this->display();
    }
}
