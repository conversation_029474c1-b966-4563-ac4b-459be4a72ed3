<?php

if( $jumpUrl== 'javascript:history.back(-1);') {
	$jumpUrl = $_SERVER['HTTP_REFERER'];
}

if (isset($message)) {
    $type = 'ok';
    $msg = $message;
    $delay = 2;
} else {
    $type = 'alert';
    $msg = $error;
    $delay = 3;
}

if(stripos($msg, 'fix') === 0) {
	$msg = trim($msg, 'fix ');
	$fix = 1;
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>提示信息</title>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1, width=device-width, maximum-scale=1, user-scalable=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="format-detection" content="address=no" />
    <php>if(empty($fix)){</php><meta http-equiv="refresh" content="{$delay};url=<?php echo($jumpUrl); ?>"><php>}</php>
    <script src="__ROOT__/static/js/lib/zepto-v1.1.3.min.js"></script>
    <script src="__ROOT__/static/js/zepto.alert.min.js?v={$Think.__VERSION}"></script>
    <link rel="stylesheet" href="__ROOT__/static/css/zepto.alert.css?v={$Think.__VERSION}">
</head>
<body>
<input type="hidden" id="msg" value="{$msg}" />
<input type="hidden" id="title" value="<?php echo $type;?>"/>
</body>

<script type="text/javascript">
    $(function() {
        var msg = $('#msg'), title=$('#title');
        $.dialog({
            content : msg.val(),
            title : title.val(),
        <php>if(empty($fix)){</php>  time: {$delay}*1000<php>}</php>
    });
    });
</script>
</html>