<?php
namespace Common\Model;

use Think\Model;

class TrainingModel extends Model
{
    protected $_auto = [ 
        ['create_time', 'time', 1, 'function'],
        ['upadte_time', 'time', 2, 'function'],
    ];

    public $status = [
        '0' => ['text' => '待审核', 'style' => 'info'],
        '1' => ['text' => '审核成功', 'style' => 'success'],
        '2' => ['text' => '审核失败', 'style' => 'danger'],
    ];

    public $type = [
        '0' => ['text' => '未设置', 'style' => 'info'],
        '1' => ['text' => '外部', 'style' => 'info'],
        '2' => ['text' => '内部', 'style' => 'info'],

    ];

    public $level = [
        '0' => ['text' => '普通', 'style' => 'info'],
        '1' => ['text' => 'V1', 'style' => 'info'],
        '2' => ['text' => 'V2', 'style' => 'info'],
        '3' => ['text' => 'V3', 'style' => 'info'],
    ];


    public $resources = [
        '0' => ['text' => '不显示', 'style' => 'info'],
        '1' => ['text' => '显示', 'style' => 'success'],
    ];

    public $is_out = [
        '0' => ['text' => '不允许', 'style' => 'info'],
        '1' => ['text' => '允许', 'style' => 'success'],
    ];

    public $showList = [
        '0' => ['text' => '不显示', 'style' => 'info'],
        '1' => ['text' => '显示', 'style' => 'success'],
    ];


    public $opentype = [
        '0' => ['text' => '推荐', 'style' => 'info'],
        '1' => ['text' => '资源包', 'style' => 'info'],
        ];

    public $buyList = [
        '0' => ['text' => '已付款', 'style' => 'info'],
        '1' => ['text' => '预付款', 'style' => 'info'],
        '2' => ['text' => '部分付款', 'style' => 'info'],
        '3' => ['text' => '推荐服务站资源包抵扣', 'style' => 'info'],
        ];       
        

}