<?php
namespace Station\Controller;

use Think\Controller;

/**
 * 系统初始化控制器
 */
class InitController extends Controller
{
    /**
     * 初始化招就办管理系统配置
     */
    public function initZsbSystem()
    {
        // 检查是否为开发环境或管理员权限
        if (!IS_CLI && !$this->checkAdminPermission()) {
            $this->error('无权限执行此操作');
        }

        $result = [
            'status' => 1,
            'msg' => '初始化完成',
            'data' => []
        ];

        try {
            // 初始化系统配置
            $this->initSystemConfig();
            $result['data']['config'] = '系统配置初始化完成';

            // 检查数据表结构
            $this->checkTableStructure();
            $result['data']['tables'] = '数据表结构检查完成';

            // 初始化基准成本价数据
            $this->initBaseCostData();
            $result['data']['base_cost'] = '基准成本价数据初始化完成';

        } catch (\Exception $e) {
            $result['status'] = 0;
            $result['msg'] = '初始化失败：' . $e->getMessage();
            \Think\Log::write('招就办系统初始化失败：' . $e->getMessage(), 'ERROR');
        }

        if (IS_CLI) {
            echo json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            $this->ajaxReturn($result);
        }
    }

    /**
     * 初始化系统配置
     */
    private function initSystemConfig()
    {
        $confModel = D('Conf');
        
        // 平台费率配置
        $platformRateExists = $confModel->where(['name' => 'platform_rate'])->find();
        if (!$platformRateExists) {
            $confModel->add([
                'group' => 1,
                'name' => 'platform_rate',
                'title' => '平台费率',
                'value' => '0.30',
                'desc' => '平台抽成比例，默认30%',
                'show' => 1
            ]);
        }

        // 最大佣金配置
        $maxCommissionExists = $confModel->where(['name' => 'max_commission'])->find();
        if (!$maxCommissionExists) {
            $confModel->add([
                'group' => 1,
                'name' => 'max_commission',
                'title' => '最大佣金',
                'value' => '99999999',
                'desc' => '佣金上限(分)',
                'show' => 0
            ]);
        }

        // 价格配置版本
        $priceVersionExists = $confModel->where(['name' => 'price_config_version'])->find();
        if (!$priceVersionExists) {
            $confModel->add([
                'group' => 1,
                'name' => 'price_config_version',
                'title' => '价格配置版本',
                'value' => '1.1',
                'desc' => '当前价格配置系统版本',
                'show' => 0
            ]);
        }
    }

    /**
     * 检查数据表结构
     */
    private function checkTableStructure()
    {
        $db = M();
        
        // 检查z_zsb_post_price表是否存在必要字段
        $fields = $db->query("SHOW COLUMNS FROM z_zsb_post_price");
        $fieldNames = array_column($fields, 'Field');
        
        $requiredFields = ['id', 'zsb_id', 'post_id', 'cost_price', 'sale_price', 'commission', 'status', 'create_time', 'update_time'];
        
        foreach ($requiredFields as $field) {
            if (!in_array($field, $fieldNames)) {
                throw new \Exception("z_zsb_post_price表缺少必要字段：{$field}");
            }
        }

        // 检查z_project_join_identity表
        $identityFields = $db->query("SHOW COLUMNS FROM z_project_join_identity");
        $identityFieldNames = array_column($identityFields, 'Field');
        
        $requiredIdentityFields = ['id', 'project_id', 'project_post_id', 'project_identity_id', 'cost'];
        
        foreach ($requiredIdentityFields as $field) {
            if (!in_array($field, $identityFieldNames)) {
                throw new \Exception("z_project_join_identity表缺少必要字段：{$field}");
            }
        }
    }

    /**
     * 初始化基准成本价数据
     */
    private function initBaseCostData()
    {
        // 这里可以添加一些默认的基准成本价数据
        // 或者检查现有数据的完整性
        
        $identityModel = D('ProjectJoinIdentity');
        $postModel = D('ProjectPost');
        
        // 获取所有可用岗位
        $posts = $postModel->where(['status' => 1, 'is_zsb_available' => 1])->select();
        
        $missingBaseCosts = [];
        foreach ($posts as $post) {
            $baseCost = $identityModel->getBaseCostByPostId($post['id']);
            if ($baseCost <= 0) {
                $missingBaseCosts[] = $post['job_name'] . '(ID:' . $post['id'] . ')';
            }
        }
        
        if (!empty($missingBaseCosts)) {
            \Think\Log::write('以下岗位缺少基准成本价配置：' . implode(', ', $missingBaseCosts), 'WARN');
        }
    }

    /**
     * 检查管理员权限
     */
    private function checkAdminPermission()
    {
        // 这里可以添加管理员权限检查逻辑
        // 暂时返回true，实际使用时应该添加适当的权限验证
        return true;
    }

    /**
     * 获取系统状态
     */
    public function getSystemStatus()
    {
        $status = [
            'config' => $this->checkConfigStatus(),
            'tables' => $this->checkTableStatus(),
            'data' => $this->checkDataStatus()
        ];

        $this->ajaxReturn([
            'status' => 1,
            'data' => $status
        ]);
    }

    /**
     * 检查配置状态
     */
    private function checkConfigStatus()
    {
        $confModel = D('Conf');
        
        $requiredConfigs = ['platform_rate', 'max_commission', 'price_config_version'];
        $status = [];
        
        foreach ($requiredConfigs as $config) {
            $exists = $confModel->where(['name' => $config])->find();
            $status[$config] = $exists ? 'OK' : 'MISSING';
        }
        
        return $status;
    }

    /**
     * 检查数据表状态
     */
    private function checkTableStatus()
    {
        try {
            $this->checkTableStructure();
            return 'OK';
        } catch (\Exception $e) {
            return 'ERROR: ' . $e->getMessage();
        }
    }

    /**
     * 检查数据状态
     */
    private function checkDataStatus()
    {
        $priceModel = D('ZsbPostPrice');
        $postModel = D('ProjectPost');
        
        $totalPosts = $postModel->where(['status' => 1, 'is_zsb_available' => 1])->count();
        $configuredPosts = $priceModel->group('post_id')->count();
        
        return [
            'total_posts' => $totalPosts,
            'configured_posts' => $configuredPosts,
            'configuration_rate' => $totalPosts > 0 ? round($configuredPosts / $totalPosts * 100, 2) . '%' : '0%'
        ];
    }
}
