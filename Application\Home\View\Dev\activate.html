<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开发者模式激活</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: #667eea;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        input[type="password"] {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .status {
            margin-top: 20px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 13px;
            color: #6c757d;
            text-align: left;
        }
        
        .info h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .info ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .info li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">DEV</div>
        <h1>开发者模式</h1>
        <p class="subtitle">激活后可在浏览器中直接访问微信页面</p>
        
        <form id="activateForm">
            <div class="form-group">
                <label for="devKey">开发者密钥</label>
                <input type="password" id="devKey" name="key" placeholder="请输入开发者密钥" required>
            </div>
            
            <button type="submit" class="btn">激活开发者模式</button>
            <button type="button" class="btn btn-secondary" onclick="clearDeveloperMode()">清除开发者模式</button>
        </form>
        
        <div class="info">
            <h4>使用说明：</h4>
            <ul>
                <li>激活后有效期为30天</li>
                <li>激活期间可直接在浏览器访问微信页面</li>
                <li>如需清除，点击"清除开发者模式"按钮</li>
                <li>密钥错误会被记录到日志中</li>
            </ul>
        </div>
    </div>
    
    <script>
        document.getElementById('activateForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const key = document.getElementById('devKey').value;
            if (key) {
                window.location.href = '?key=' + encodeURIComponent(key);
            }
        });
        
        function clearDeveloperMode() {
            if (confirm('确定要清除开发者模式吗？')) {
                window.location.href = '?action=clear';
            }
        }
        
        // 检查当前状态
        fetch('./status')
            .then(response => response.json())
            .then(data => {
                const container = document.querySelector('.container');

                if (data.configured === false) {
                    const statusDiv = document.createElement('div');
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '⚠ 开发者模式未配置，无法使用';
                    container.appendChild(statusDiv);
                } else if (data.developer_mode) {
                    const statusDiv = document.createElement('div');
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '✓ 开发者模式已激活';
                    container.appendChild(statusDiv);
                } else if (data.is_production) {
                    // 生产环境显示详细限制信息
                    const statusDiv = document.createElement('div');
                    statusDiv.className = 'status error';

                    let message = '⚠ 生产环境限制：<br>';
                    if (!data.ip_allowed) {
                        message += `• IP地址 ${data.client_ip} 不在白名单中<br>`;
                    }
                    if (!data.time_allowed && data.time_limit) {
                        message += `• 当前时间 ${data.current_hour}:00 不在允许时段 ${data.time_limit.start}:00-${data.time_limit.end}:00<br>`;
                    }
                    if (!data.allowed_ips || data.allowed_ips.length === 0) {
                        message += '• 未配置IP白名单<br>';
                    }

                    statusDiv.innerHTML = message;
                    container.appendChild(statusDiv);
                }

                // 显示当前环境信息
                if (data.is_production) {
                    const infoDiv = document.createElement('div');
                    infoDiv.className = 'info';
                    infoDiv.innerHTML = `
                        <h4>生产环境信息：</h4>
                        <ul>
                            <li>当前IP：${data.client_ip}</li>
                            <li>当前时间：${data.current_hour}:00</li>
                            <li>允许的IP：${data.allowed_ips ? data.allowed_ips.join(', ') : '未配置'}</li>
                            ${data.time_limit ? `<li>允许时段：${data.time_limit.start}:00-${data.time_limit.end}:00</li>` : ''}
                        </ul>
                    `;
                    container.appendChild(infoDiv);
                }
            })
            .catch(err => console.log('状态检查失败:', err));
    </script>
</body>
</html>
