<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;
use Util\AliSms;
use Util\HwSms;
/**
 * 服务站管理
 * Class ProjectController
 * @package Prime\Controller
 */
class ServicestationController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    public function index() {
        $c_kw = [
            'service_name' => '服务站名称',
            'service_number' => '服务站编号',
            'contract_number' => '合同编号',
            'pid' => '上级服务站ID',
            'id' => 'ID',
        ];
        $where = ['zsb_type' => 1]; // 只显示服务站类型
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_sort = I("get.sort",'id');
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);
        if ($s_status != '') $where['status'] = $s_status;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['service_name']))  {
                $where[$s_kw] = ['like', "%$s_val%"];
            }  else {
                $where[$s_kw] = $s_val;
            }
        }
        //公用搜索
        $contions = I('get.');
        $contionsWhere = ['level']; // 移除type，确保只显示服务站类型
        foreach ($contions as $whereKey => $row) {
            if (in_array($whereKey, $contionsWhere) && $row != '') {
                $where[$whereKey] = $row;
            }
        }

        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['create_time'] = ['between', [$s_start, $s_end]];
        }

        $obj = D("ServiceStation");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam($s_sort, 'desc');

        // 确保排序字段安全，避免SQL注入和语法错误
        $safe_field = $sort_param['field'];
        $safe_order = $sort_param['order'];

        // 如果字段为空或不安全，使用默认排序
        if (empty($safe_field) || !preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $safe_field)) {
            $safe_field = 'id';
        }

        // 确保排序方向安全
        if (!in_array(strtolower($safe_order), ['asc', 'desc'])) {
            $safe_order = 'desc';
        }

        $list = $obj
            ->order("`{$safe_field}` {$safe_order}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        if ($list) {
            $service_station_id = array_unique(array_column($list, 'id'));
            $qrcodeList = D("Qrcode")->where(['service_station_id' => ['in', $service_station_id], 'service_id' => 2])->getField('service_station_id,scene_id,ticket', true);
            $this->assign('qrcodeList', $qrcodeList);

            $qrcodeStationList = D("Qrcode")->where(['service_station_id' => ['in', $service_station_id], 'service_id' => 1])->getField('service_station_id,scene_id,ticket', true);
            $this->assign('qrcodeStationList', $qrcodeStationList);
            $pidList = [];
            $pid_arr_id = array_unique(array_column($list, 'pid'));
            if ($pid_arr_id) {
                $pidList = $obj->where(['id' => ['in', $pid_arr_id]])->getField('id,service_name', true);
            }
            $this->assign('pidList', $pidList);

                // 新增：获取每个服务站的简历数量（键名为服务站ID，值为数量）
            $jobCounts = [];
            if (!empty($service_station_id)) {
                $jobCounts = D("UserJob")
                    ->where(['service_station_id' => ['in', $service_station_id]])
                    ->group('service_station_id')  // 按服务站分组统计
                    ->getField('service_station_id, count(*) as count'); // 返回格式：['1'=>5, '2'=>3]
            }
            $this->assign('jobCounts', $jobCounts);

        }



        $this->assign('levelList', $obj->level);
        $this->assign('resourcesList', $obj->resources);
        $this->assign('isoutList', $obj->is_out);
        $this->assign('isshowListList', $obj->showList);
        $this->assign('typeList', $obj->type);
        $this->assign('opentypeList', $obj->opentype);
        $this->assign('serviceList', D("Service")->getField('id,name'));
        $this->assign('statusList', $obj->status);
        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '0');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '0');
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    public function addqrcode() {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $code = D("Qrcode")->createNew(1, 1, $id);
        var_dump($code);die;
    }

    public function test() {
        $code = D("Qrcode")->createNew(2, 1, 1);
    }

    /**
     * 添加编辑
     */
    public function edit() {
        $id = intval(I('get.id'));
        $obj = D("ServiceStation");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        }
        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $insertId = $obj->add($data);
                    if ($insertId) {
                        $code = D("Qrcode")->createNew(2, 1, $insertId);
                    }
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("servicestation/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->assign('usersList', D("Users")->where(['status' => 1, 'id' => ['neq', 1]])->getField('id,username', true));
        $this->assign('typeList', $obj->type);
        $this->assign('resourcesList', $obj->resources);
        $this->assign('isoutList', $obj->is_out);
        $this->assign('isshowList', $obj->showList);
        $this->assign('levelList', $obj->level);
        $this->display();
    }

     /**
     * 购买资源包
     */
    public function buy() {
        $id = I('get.service_station_id');
        $obja = D("ServiceStation");
        if (!$id) $this->error('请选择正确的服务站');
        $serviceStationlRow = $obja->where(['id' => $id])->find();
        if (!$serviceStationlRow) $this->error('当前服务站不存在');
        $this->assign('serviceStationlRow', $serviceStationlRow);
        $this->assign('buyList', $obja->buyList);
        
        if ($id) {
            $row = D("ServiceStation")->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);

            $rowp = D("ServiceStation")->where("id=".$row['pid'])->find();
            if (!$rowp) $this->error('参数错误');
            $this->assign('pidnum',$rowp['open_num']);

        } else {
            $this->error('参数错误');
        }
        $obj = D("service_station_buy_log"); //日志表写入
        if (IS_POST) {
            
            $p_buy_type = I('post.buy_type', 0);
            $p_buy_open_num = I('post.buy_open_num', 0);
            if ($p_buy_type == '3' && $p_buy_open_num > $rowp['open_num']) $this->error('上级推荐服务站资源包不足使用资源包抵扣方式');

            if ($data = $obj->create()) {
                

                $data['create_time'] = time();
                $data['service_station_id'] = $data['id'];
                unset($data['id']);
                $insertId = $obj->add($data); //写入购买日志表
               if ($insertId) { //添加日志然后服务站数量更新数量
                   D("ServiceStation")->save([
                        'id' => $row['id'],
                        'all_open_num' => ['exp', 'all_open_num+'.$data['buy_open_num']], //开通数量添加
                        'open_num' => ['exp', 'open_num+'.$data['buy_open_num']], //开通数量添加
                        'resources' => '1',
                    ]);
                    // 划拨
                    if ($p_buy_type == 3) {
                        $insertId = D("ServiceStationDivideLog")->add([
                            'user_id' => 0,
                            'service_station_id' => $rowp['id'],
                            'divide_service_station_id' => $row['id'],
                            'service_station_open_num' => $rowp['open_num'],
                            'divide_open_num' => $data['buy_open_num'],
                        ]);
                        D("ServiceStation")->save([
                            'id' => $rowp['id'],
                            'open_num' => ['exp', 'open_num-'.$data['buy_open_num']],
                            'divide_open_num' => ['exp', 'divide_open_num+'.$data['buy_open_num']]
                        ]);
                    }
                }
//                if (!$id) {
//                    $data['create_time'] = time();
//                    $insertId = $obj->add($data);
//                } else {
//                    $data['id'] = $id;
//                    $obj->save($data);
//                } servicestation/buylog/service_station_id/4
//$this->success("操作成功", U("servicestation/buylog", ["service_station_id" => $rowb['service_station_id']]));
                $this->success("操作成功", U("servicestation/buylog", ["service_station_id" => $row['id']]));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->assign('usersList', D("Users")->where(['status' => 1, 'id' => ['neq', 1]])->getField('id,username', true));
        
        $this->display();
    }

    /**
     * 查看资源包购买日志/编辑
     */
    public function buylog() {
        $id = I('get.service_station_id');
        $obja = D("ServiceStation");
        if (!$id) $this->error('请选择正确的服务站');
        $serviceStationlRow = $obja->where(['id' => $id])->find();
        if (!$serviceStationlRow) $this->error('当前服务站不存在');
        $this->assign('serviceStationlRow', $serviceStationlRow);
        $this->assign('buyList', $obja->buyList);
        
        if ($id) {
            $row = D("ServiceStation")->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);

        } else {
            $this->error('参数错误');
        }
        $obj = D("service_station_buy_log");
        $where = [
            'service_station_id' => $id,
        ];
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        $userJobRow = D("UserJob")->where(['id' => $userJobId])->find();
        $this->assign('userJobRow', $userJobRow);
        $this->assign('list',$list);
        $this->display();
    }
 /**
     * 编辑日志购买资源包
     */
    public function editbuy() {

        $id =  I('get.id');
        $obj = D("service_station_buy_log"); 
        if (!$id) $this->error('无相关日志信息');
        $rowb = $obj->where(['id' => $id])->find();
        if (!$rowb) $this->error('当前日志不存在');
        $this->assign('rowb', $rowb);

        $obja = D("ServiceStation");
        $row = $obja->where("id=".$rowb['service_station_id'])->find();
        if ($row){
            $this->assign('row',$row);
           }
        $this->assign('buyList', $obja->buyList);
            
        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $data['add_user'] = session('admin_name');
                    $insertId = $obj->add($data);
                    if ($insertId) {
                        D("ServiceStation")->save([
                            'id' => $rowb['service_station_id'],
                            'all_open_num' => ['exp', 'all_open_num+'.$data['buy_open_num']], //开通数量添加
                            'open_num' => ['exp', 'open_num+'.$data['buy_open_num']], //开通数量添加
                            'resources' => '1',
                        ]);
                    }
                } else {
                    $data['id'] = $id;
                    $data['last_edittime'] = time();
                    $data['edit_user'] = session('admin_name');
                    $obj->save($data);
                }
                $this->success("操作成功", U("servicestation/buylog", ["service_station_id" => $rowb['service_station_id']]));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->assign('usersList', D("Users")->where(['status' => 1, 'id' => ['neq', 1]])->getField('id,username', true));
        $this->display();

    }

    /**
     * other 其他文件管理
     */
    public function other() {
        $id = I('get.service_station_id');
        if (!$id) $this->error('请选择正确的服务站');
        $serviceStationlRow = D("ServiceStation")->where(['id' => $id])->find();
        if (!$serviceStationlRow) $this->error('当前服务站不存在');
        $this->assign('serviceStationlRow', $serviceStationlRow);
        $c_kw = [
            'id' => 'ID',
            'name' => '名称',
        ];
        $where = ['service_station_id' => $serviceStationlRow['id']];

        $obj = D("ServiceStationFile");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('statusList', D("Project")->status);
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    public function otheredit() {
        $ids = I('get.service_station_id');
        if (!$ids) $this->error('请选择正确的服务站');
        $serviceStationlRow = D("ServiceStation")->where(['id' => $ids])->find();
        if (!$serviceStationlRow) $this->error('当前服务站不存在');
        $id = I('get.id', 0);
        $obj = D("ServiceStationFile");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        }
        $obj = D("ServiceStationFile");
        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $data['service_station_id'] = $ids;
                    $insertId = $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("servicestation/other", ['service_station_id' => $ids]));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->display();
    }

    /**
     * 状态变更
     */
    public function cgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id])->find();
        if ($row['status'] == 1) $this->error('当前服务站已经审核通过');
        if (!in_array($status, [0,1,2])) $this->error('参数错误 ');
        if ($status == 1) {
            $pidRows = [];
            $pidTop = false;
            if ($row['pid'] > 0) {
                $pidRows = $obj->where(['id' => $row['pid']])->find();
            } else {
                $pidTop = true;
            }
            if (($row['pid'] > 0 && $pidRows && ($pidRows['open_num']) > 0) || $pidTop) {
                //服务站
                if (!D("Qrcode")->where(['service_id' => 2, 'service_station_id' => $row['id']])->find()) {
                    $code1 = D("Qrcode")->createNew(2, 1, $row['id']);
                } else {
                    $code1 = true;
                }
                //服务平台
                if (!D("Qrcode")->where(['service_id' => 1, 'service_station_id' => $row['id']])->find()) {
                    $code = D("Qrcode")->createNew(1, 1, $row['id']);
                } else {
                    $code = true;
                }
                if ($code &&  $code1) {
                    $obj->save(['id' => $id,'open_type' =>'1', 'status' => $status]);
                    D("User")->where(['self_service_station_id' => $row['id']])->save(['is_service_station' => 2]);
                    if ($pidRows) {
                        $obj->where(['id' => $pidRows['id']])->save(['succ_open_num' => ['exp', 'succ_open_num+1'],'open_num' => ['exp', 'open_num-1']]);
                        
                        // 给上级推荐服务站添加16800余额，备注"资源包回购"
                        $stationMoneyData = [
                            'service_station_id' => $pidRows['id'],
                            'type' => 2, // 收入类型
                            'money' => 16800, // 添加16800余额
                            'status' => 4, // 状态正常
                            'create_time' => time(),
                            'remark' => '来自：'.$row['service_name'] // 备注说明
                        ];
                        D("station_money")->add($stationMoneyData);
                        
                        // 更新服务站余额
                        $obj->where(['id' => $pidRows['id']])->save([
                            'price' => ['exp', 'price+16800'],
                            'free_withdrawal_limit' => ['exp', 'free_withdrawal_limit+16800'],
                            'total_price' => ['exp', 'total_price+16800']
                        ]);
                    }

                    $alisms = new AliSms();
                    $result = $alisms->sendSmsstate($row['contract_name'],'通过', $row['mobile']);
 

                    $this->success('修改成功');
                } else {
                    $this->error('开通失败，请联系技术处理');
                }
            } else {
                $this->error('当前服务站的开通数量不足');
            }
        } else {
            $obj->save(['id' => $id, 'status' => $status]);
        }
        $this->success('修改成功');
    }

    /**
     * 推荐方式开通服务站
     */
    public function tjstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id])->find();
        if ($row['status'] == 1) $this->error('当前服务站已经审核通过');
        if (!in_array($status, [0,1,2])) $this->error('参数错误 ');
        if ($status == 1) {
            $pidRows = [];
            $pidTop = false;
            if ($row['pid'] > 0) {
                $pidRows = $obj->where(['id' => $row['pid']])->find();
            } else {
                $pidTop = true;
            }
            if (($row['pid'] > 0 && $pidRows) || $pidTop) {
                //服务站
                if (!D("Qrcode")->where(['service_id' => 2, 'service_station_id' => $row['id']])->find()) {
                    $code1 = D("Qrcode")->createNew(2, 1, $row['id']);
                } else {
                    $code1 = true;
                }
                //服务平台
                if (!D("Qrcode")->where(['service_id' => 1, 'service_station_id' => $row['id']])->find()) {
                    $code = D("Qrcode")->createNew(1, 1, $row['id']);
                } else {
                    $code = true;
                }
                if ($code &&  $code1) {
                    $obj->save(['id' => $id, 'status' => $status]);
                    D("User")->where(['self_service_station_id' => $row['id']])->save(['is_service_station' => 2]);
                    if ($pidRows) {
                        $obj->where(['id' => $pidRows['id']])->save(['succ_tj_open_num' => ['exp', 'succ_tj_open_num+1']]);
                    }

                    $alisms = new AliSms();
                    $result = $alisms->sendSmsstate($row['contract_name'],'通过', $row['mobile']);


                    $this->success('修改成功');
                } else {
                    $this->error('开通失败，请联系技术处理');
                }
            }
        } else {
            $obj->save(['id' => $id, 'status' => $status]);
        }
        $this->success('修改成功');
    }

    /**
     * 状态变更
     */
    public function othercgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStationFile");
        if (!in_array($status, [0,1])) $this->error('参数错误 ');
        $obj->save(['id' => $id, 'status' => $status]);
        $this->success('修改成功');
    }

    /**
     * 获取微信群成员代理接口
     * 解决前端跨域问题
     */
    public function getWechatMembers() {
        try {
            // 获取POST数据
            $postData = I('post.');

            // 验证必要参数
            if (!isset($postData['WxID']) || !isset($postData['Data']) || !isset($postData['Data']['UserName'])) {
                $this->ajaxReturn([
                    'ErrCode' => -1,
                    'ErrMsg' => '参数不完整'
                ]);
                return;
            }

            // 记录请求日志
            dolog('wechat_members/request', 'Request data: ' . json_encode($postData, JSON_UNESCAPED_UNICODE));

            // 转换为JSON格式
            $jsonData = json_encode($postData, JSON_UNESCAPED_UNICODE);

            // 初始化cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://43.139.30.237:1080/HTTPManagement.aspx?Item=AddMessage');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($jsonData),
                'Accept: application/json, text/plain, */*'
            ]);

            // 执行请求
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // 记录响应日志
            dolog('wechat_members/response', 'HTTP Code: ' . $httpCode . ', Response: ' . $response);

            if ($error) {
                dolog('wechat_members/error', 'cURL error: ' . $error);
                $this->ajaxReturn([
                    'ErrCode' => -2,
                    'ErrMsg' => 'cURL错误: ' . $error
                ]);
                return;
            }

            if ($httpCode !== 200) {
                dolog('wechat_members/error', 'HTTP error: ' . $httpCode);
                $this->ajaxReturn([
                    'ErrCode' => -3,
                    'ErrMsg' => 'HTTP错误: ' . $httpCode
                ]);
                return;
            }

            // 尝试解析响应
            $responseData = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                // 如果JSON解析失败，返回原始响应
                dolog('wechat_members/warning', 'JSON parse failed, returning raw response');
                header('Content-Type: application/json; charset=utf-8');
                echo $response;
                exit;
            }

            // 返回解析后的数据
            $this->ajaxReturn($responseData);

        } catch (Exception $e) {
            dolog('wechat_members/exception', 'Exception: ' . $e->getMessage());
            $this->ajaxReturn([
                'ErrCode' => -4,
                'ErrMsg' => '服务器异常: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取群列表代理接口
     * 解决前端跨域问题
     */
    public function getChatroomList() {
        try {
            // 获取POST数据
            $postData = I('post.');

            // 验证必要参数
            if (!isset($postData['WxID']) || !isset($postData['Data']) || !isset($postData['Data']['CallName'])) {
                $this->ajaxReturn([
                    'ErrCode' => -1,
                    'ErrMsg' => '参数不完整'
                ]);
                return;
            }

            // 记录请求日志
            dolog('chatroom_list/request', 'Request data: ' . json_encode($postData, JSON_UNESCAPED_UNICODE));

            // 转换为JSON格式
            $jsonData = json_encode($postData, JSON_UNESCAPED_UNICODE);

            // 初始化cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://43.139.30.237:1080/HTTPManagement.aspx?Item=AddMessage');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($jsonData),
                'Accept: application/json, text/plain, */*'
            ]);

            // 执行请求
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // 记录响应日志
            if ($error) {
                dolog('chatroom_list/error', 'cURL error: ' . $error);
                $this->ajaxReturn([
                    'ErrCode' => -2,
                    'ErrMsg' => 'cURL错误: ' . $error
                ]);
                return;
            }

            if ($httpCode !== 200) {
                dolog('chatroom_list/http_error', 'HTTP Code: ' . $httpCode . ', Response: ' . $response);
                $this->ajaxReturn([
                    'ErrCode' => -3,
                    'ErrMsg' => 'HTTP错误: ' . $httpCode
                ]);
                return;
            }

            // 解析响应数据
            $responseData = json_decode($response, true);
            if ($responseData === null) {
                dolog('chatroom_list/parse_error', 'Failed to parse JSON response: ' . $response);
                $this->ajaxReturn([
                    'ErrCode' => -4,
                    'ErrMsg' => 'JSON解析失败'
                ]);
                return;
            }

            // 记录成功日志
            dolog('chatroom_list/success', 'Response received successfully');

            // 返回响应数据
            $this->ajaxReturn($responseData);

        } catch (Exception $e) {
            dolog('chatroom_list/exception', 'Exception: ' . $e->getMessage());
            $this->ajaxReturn([
                'ErrCode' => -5,
                'ErrMsg' => '服务器异常: ' . $e->getMessage()
            ]);
        }
    }
}