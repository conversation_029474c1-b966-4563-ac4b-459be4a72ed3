<?php

namespace Prime\Controller;
use Common\Controller\PrimeController;

class ChannelController extends PrimeController
{

    public function _initialize() 
    {
        parent::_initialize();
    }

    public function index() {
        $c_kw = [
            'name' => '渠道名称',
            'contact_name' => '联系人姓名',
            'contact_phone' => '联系电话',
            'id' => 'ID',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);
        
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['name', 'contact_name']))  {
                $where[$s_kw] = ['like', "%$s_val%"];
            }  else {
                $where[$s_kw] = $s_val;
            }
        }
        
        if ($s_start && $s_end) {
            $where['create_time'] = [['egt', $s_start], ['elt', $s_end]];
        }

        $obj = D("Channel");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 15);
        $list = $obj->where($where)->order("id desc")->limit($page->firstRow, $page->listRows)->select();
        
        $this->assign('list', $list);
        $this->assign('page', $page->show());
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 删除
     */
    public function del() {
        $id = I('get.id');
        if (!$id) $this->error('参数错误');
        
        // 检查是否有岗位在使用此渠道
        $postCount = D("ProjectPost")->where(['channel_id' => $id])->count();
        if ($postCount > 0) {
            $this->error('该渠道下还有岗位，无法删除');
        }
        
        $obj = D("Channel");
        $obj->delete($id);
        $this->success('删除成功');
    }

    /**
     * 添加编辑
     */
    public function edit() {
        $id = intval(I('get.id'));
        $obj = D("Channel");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        }

        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("channel/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->display();
    }
}
