<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="main">
                <div class="panel panel-success">
                    <div class="panel-heading" style="background-color: #07C160; color: white;">
                        <h3 class="panel-title"><i class="fa fa-plus-circle"></i> 创建培训订单</h3>
                    </div>
                    <div class="panel-body">
                        <a href="{:U('training/index')}" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>

                <div class="panel panel-default">
                    <div class="panel-heading" style="background-color: #f9f9f9;">
                        <h3 class="panel-title"><i class="fa fa-edit"></i> 填写报名信息</h3>
                    </div>
                    <div class="panel-body">
                        <form class="form-horizontal" action="{:U('training/create')}" method="post" id="createForm">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">学员：</label>
                                <div class="col-sm-6">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                        <select name="user_id" class="form-control" required>
                                            <option value="">请选择学员</option>
                                            <foreach name="user_list" item="user">
                                                <option value="{$user.id}" <if condition="$selected_user_id eq $user['id']">selected</if>>{$user.realname} ({$user.mobile})</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">培训项目：</label>
                                <div class="col-sm-6">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-bookmark"></i></span>
                                        <select name="project_id" class="form-control" id="project_id" required>
                                            <option value="">请选择培训项目</option>
                                            <foreach name="project_list" item="project">
                                                <option value="{$project.id}">{$project.name}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">培训岗位：</label>
                                <div class="col-sm-6">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-tag"></i></span>
                                        <select name="post_id" class="form-control" id="post_id" required>
                                            <option value="">请先选择培训项目</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">培训服务费（合同金额）：</label>
                                <div class="col-sm-6">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-money"></i></span>
                                        <p class="form-control-static" id="fee_amount" style="padding-left: 10px; color: #e0672c; font-weight: bold;">0.00 元</p>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-6">
                                    <button type="submit" class="btn btn-success" style="background-color: #07C160; border-color: #07C160;">
                                        <i class="fa fa-check"></i> 提交报名
                                    </button>
                                    <a href="{:U('training/index')}" class="btn btn-default">
                                        <i class="fa fa-times"></i> 取消
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />

<script>
    require(["jquery"], function ($) {
        $(function () {
            // 项目选择变化时，获取对应的岗位列表
            $('#project_id').change(function() {
                var projectId = $(this).val();
                if (projectId) {
                    $.ajax({
                        url: "{:U('training/getProjectPosts')}",
                        type: 'GET',
                        data: {project_id: projectId},
                        dataType: 'json',
                        success: function(res) {
                            if (res.status == 1 && res.data && res.data.length > 0) {
                                var posts = res.data;
                                var options = '<option value="">请选择培训岗位</option>';
                                for (var i = 0; i < posts.length; i++) {
                                    options += '<option value="' + posts[i].id + '" data-price="' + posts[i].price + '">' + posts[i].name + '</option>';
                                }
                                $('#post_id').html(options);
                            } else {
                                $('#post_id').html('<option value="">无</option>');
                                alert(res.msg || '没有找到相关岗位');
                            }
                        },
                        error: function() {
                            alert('网络错误，请重试');
                        }
                    });
                } else {
                    $('#post_id').html('<option value="">请先选择培训项目</option>');
                    $('#fee_amount').text('0.00 元');
                }
            });

            // 岗位选择变化时，显示对应的报名费
            $('#post_id').change(function() {
                var price = $(this).find('option:selected').data('price') || 0;
                $('#fee_amount').text(parseFloat(price).toFixed(2) + ' 元');
            });

            // 表单提交前验证
            $('#createForm').submit(function() {
                if (!$('select[name="user_id"]').val()) {
                    alert('请选择学员');
                    return false;
                }
                if (!$('select[name="project_id"]').val()) {
                    alert('请选择培训项目');
                    return false;
                }
                if (!$('select[name="post_id"]').val()) {
                    alert('请选择培训岗位');
                    return false;
                }
                return true;
            });
        });
    });
</script>
