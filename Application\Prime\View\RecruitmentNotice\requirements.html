<include file="block/hat" />
<include file="RecruitmentNotice/common_styles" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="recruitment-page-wrapper">
                <div class="recruitment-page-container">
                    <!-- 现代化页面标题 -->
                    <div class="recruitment-page-header">
                        <div class="recruitment-header-content">
                            <div class="recruitment-page-title">
                                <div class="recruitment-title-icon">
                                    <i class="fa fa-cog"></i>
                                </div>
                                <div class="recruitment-title-text">
                                    <h1 class="recruitment-title-main">配置岗位要求</h1>
                                    <p class="recruitment-title-sub">设置岗位的具体招聘要求</p>
                                </div>
                            </div>
                            <div class="recruitment-header-actions">
                                <a href="{:U('posts', ['id' => $noticeId])}" class="recruitment-header-btn btn-secondary">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回岗位管理</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 公告和岗位信息 -->
                    <div class="recruitment-info-banner">
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fa fa-bullhorn"></i>
                                <span>招聘公告</span>
                            </div>
                            <div class="info-value">{$notice.title}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fa fa-briefcase"></i>
                                <span>岗位</span>
                            </div>
                            <div class="info-value">{$post.job_name}</div>
                        </div>
                    </div>

                    <!-- 要求配置表单 -->
                    <div class="recruitment-requirements-container">
                    
                        <form class="recruitment-requirements-form js-ajax-form" method="post">
                            <!-- 基本条件 -->
                            <div class="requirements-section">
                                <div class="section-header">
                                    <div class="section-title">
                                        <i class="fa fa-user"></i>
                                        <span>基本条件</span>
                                    </div>
                                </div>
                                <div class="section-content">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fa fa-calendar"></i>
                                                年龄要求
                                            </label>
                                            <div class="range-input-group">
                                                <input type="number" class="form-control" name="min_age"
                                                       value="{$requirements.min_age}" placeholder="最小年龄" min="16" max="65">
                                                <span class="range-separator">至</span>
                                                <input type="number" class="form-control" name="max_age"
                                                       value="{$requirements.max_age}" placeholder="最大年龄" min="16" max="65">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fa fa-venus-mars"></i>
                                                性别要求
                                            </label>
                                            <div class="radio-group">
                                                <volist name="genderOptions" id="option" key="k">
                                                    <label class="radio-option">
                                                        <input type="radio" name="gender" value="{$k}"
                                                            <if condition="$requirements.gender eq $k || (empty($requirements.gender) && $k eq 0)">checked</if>>
                                                        <span class="radio-custom"></span>
                                                        <span class="radio-text">{$option}</span>
                                                    </label>
                                                </volist>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fa fa-arrows-v"></i>
                                                身高要求 (cm)
                                            </label>
                                            <div class="range-input-group">
                                                <input type="number" class="form-control" name="min_height"
                                                       value="{$requirements.min_height}" placeholder="最小身高" min="140" max="220">
                                                <span class="range-separator">至</span>
                                                <input type="number" class="form-control" name="max_height"
                                                       value="{$requirements.max_height}" placeholder="最大身高" min="140" max="220">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fa fa-heart"></i>
                                                婚姻状况
                                            </label>
                                            <div class="radio-group">
                                                <volist name="maritalStatusOptions" id="option" key="k">
                                                    <label class="radio-option">
                                                        <input type="radio" name="marital_status" value="{$k}"
                                                            <if condition="$requirements.marital_status eq $k || (empty($requirements.marital_status) && $k eq 0)">checked</if>>
                                                        <span class="radio-custom"></span>
                                                        <span class="radio-text">{$option}</span>
                                                    </label>
                                                </volist>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fa fa-exclamation-triangle"></i>
                                                恐高要求
                                            </label>
                                            <div class="radio-group">
                                                <volist name="afraidHeightsOptions" id="option" key="k">
                                                    <label class="radio-option">
                                                        <input type="radio" name="is_afraid_heights" value="{$k}"
                                                            <if condition="$requirements.is_afraid_heights eq $k || (empty($requirements.is_afraid_heights) && $k eq 0)">checked</if>>
                                                        <span class="radio-custom"></span>
                                                        <span class="radio-text">{$option}</span>
                                                    </label>
                                                </volist>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 学历要求 -->
                            <div class="requirements-section">
                                <div class="section-header">
                                    <div class="section-title">
                                        <i class="fa fa-graduation-cap"></i>
                                        <span>学历要求</span>
                                    </div>
                                </div>
                                <div class="section-content">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fa fa-graduation-cap"></i>
                                                学历等级
                                            </label>
                                            <div class="radio-group">
                                                <volist name="educationOptions" id="option" key="k">
                                                    <label class="radio-option">
                                                        <input type="radio" name="education_level" value="{$k}"
                                                            <if condition="$requirements.education_level eq $k || (empty($requirements.education_level) && $k eq 0)">checked</if>>
                                                        <span class="radio-custom"></span>
                                                        <span class="radio-text">{$option}</span>
                                                    </label>
                                                </volist>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fa fa-tags"></i>
                                                专业关键词
                                            </label>
                                            <input type="text" class="form-control" name="major_keywords"
                                                   value="{$requirements.major_keywords}"
                                                   placeholder="多个关键词用逗号分隔，如：计算机,软件工程,信息技术">
                                            <div class="form-help">支持模糊匹配，多个关键词用逗号分隔</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 毕业信息 -->
                            <div class="requirements-section">
                                <div class="section-header">
                                    <div class="section-title">
                                        <i class="fa fa-graduation-cap"></i>
                                        <span>毕业信息</span>
                                    </div>
                                </div>
                                <div class="section-content">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fa fa-calendar"></i>
                                                毕业年限
                                            </label>
                                            <input type="number" class="form-control" name="graduation_years"
                                                   value="{$requirements.graduation_years}" placeholder="如：3" min="0" max="20">
                                            <div class="form-help">设置求职者毕业年限要求，如输入3表示毕业3年内</div>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fa fa-user-graduate"></i>
                                                是否应届生
                                            </label>
                                            <div class="radio-group">
                                                <label class="radio-option">
                                                    <input type="radio" name="is_fresh_graduate" value="0"
                                                        <if condition="$requirements.is_fresh_graduate eq 0 || empty($requirements.is_fresh_graduate)">checked</if>>
                                                    <span class="radio-custom"></span>
                                                    <span class="radio-text">不限</span>
                                                </label>
                                                <label class="radio-option">
                                                    <input type="radio" name="is_fresh_graduate" value="1"
                                                        <if condition="$requirements.is_fresh_graduate eq 1">checked</if>>
                                                    <span class="radio-custom"></span>
                                                    <span class="radio-text">仅限应届生</span>
                                                </label>
                                                <label class="radio-option">
                                                    <input type="radio" name="is_fresh_graduate" value="2"
                                                        <if condition="$requirements.is_fresh_graduate eq 2">checked</if>>
                                                    <span class="radio-custom"></span>
                                                    <span class="radio-text">非应届生</span>
                                                </label>
                                            </div>
                                            <div class="form-help">应届生判断标准：毕业一年内且无工作经历</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 其他要求 -->
                            <div class="requirements-section">
                                <div class="section-header">
                                    <div class="section-title">
                                        <i class="fa fa-plus-circle"></i>
                                        <span>其他要求</span>
                                    </div>
                                </div>
                                <div class="section-content">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fa fa-flag"></i>
                                                政治面貌
                                            </label>
                                            <input type="text" class="form-control" name="political_status"
                                                   value="{$requirements.political_status}" placeholder="如：党员，团员，群众等">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="requirements-actions">
                                <button type="submit" class="recruitment-btn btn-primary">
                                    <i class="fa fa-save"></i>
                                    <span>保存配置</span>
                                </button>
                                <a href="{:U('posts', ['id' => $noticeId])}" class="recruitment-btn btn-secondary">
                                    <i class="fa fa-times"></i>
                                    <span>取消</span>
                                </a>
                            </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__ROOT__/static/js/prime/common.js"></script>
<script>
$(function() {
    // Ajax表单提交
    $('.js-ajax-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        var originalText = $submitBtn.html();
        
        // 禁用提交按钮
        $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 保存中...');
        
        $.ajax({
            url: $form.attr('action') || window.location.href,
            type: 'POST',
            data: $form.serialize(),
            dataType: 'json',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.status == 1) {
                    alert(response.info);
                    if (response.url) {
                        window.location.href = response.url;
                    } else {
                        window.location.href = '{:U("posts", ["id" => $noticeId])}';
                    }
                } else {
                    alert(response.info || '保存失败');
                    $submitBtn.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr, status, error) {
                alert('网络错误，请重试');
                $submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>

<style>
/* 要求配置页面样式 */
.recruitment-info-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    opacity: 0.9;
}

.info-label i {
    font-size: 1rem;
}

.info-value {
    font-size: 1.125rem;
    font-weight: 600;
}

.recruitment-requirements-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.recruitment-requirements-form {
    padding: 2rem;
}

.requirements-section {
    margin-bottom: 2rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.25rem 1.5rem;
    border-bottom: 2px solid #e2e8f0;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
}

.section-title i {
    color: #667eea;
    font-size: 1.125rem;
}

.section-content {
    padding: 1.5rem;
}

.form-row {
    margin-bottom: 1.5rem;
}

.form-group {
    width: 100%;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
}

.form-label i {
    color: #667eea;
    width: 1rem;
}

/* 范围输入组 */
.range-input-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.range-input-group .form-control {
    flex: 1;
    max-width: 150px;
}

.range-separator {
    font-size: 1rem;
    font-weight: 500;
    color: #6b7280;
    white-space: nowrap;
}

/* 单选按钮组样式 */
.radio-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: #374151;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    background: #f8fafc;
    min-width: 120px;
}

.radio-option:hover {
    border-color: #667eea;
    background: white;
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 1rem;
    height: 1rem;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: #667eea;
    background: #667eea;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0.375rem;
    height: 0.375rem;
    background: white;
    border-radius: 50%;
}

.radio-option input[type="radio"]:checked ~ .radio-text {
    color: #667eea;
    font-weight: 600;
}

/* 表单控件样式 */
.form-control {
    font-size: 1rem;
    padding: 0.875rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
    outline: none;
}

.form-control::placeholder {
    color: #9ca3af;
}

/* 帮助文本样式 */
.form-help {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    font-style: italic;
}

/* 操作按钮区域 */
.requirements-actions {
    padding: 2rem;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
}

.recruitment-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    min-width: 120px;
    justify-content: center;
}

.recruitment-btn.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.recruitment-btn.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.recruitment-btn.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.recruitment-btn.btn-secondary:hover {
    background: #e5e7eb;
    color: #111827;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .recruitment-info-banner {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem 1.5rem;
    }

    .recruitment-requirements-form {
        padding: 1.5rem;
    }

    .section-content {
        padding: 1rem;
    }

    .radio-group {
        flex-direction: column;
        gap: 0.75rem;
    }

    .range-input-group {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .range-input-group .form-control {
        max-width: none;
    }

    .range-separator {
        text-align: center;
    }

    .requirements-actions {
        padding: 1.5rem;
        flex-direction: column;
        gap: 0.75rem;
    }

    .recruitment-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .recruitment-requirements-form {
        padding: 1rem;
    }

    .section-header {
        padding: 1rem;
    }

    .section-title {
        font-size: 1.125rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .radio-option {
        min-width: auto;
        justify-content: center;
    }
}
</style>
