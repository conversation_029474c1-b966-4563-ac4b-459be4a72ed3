<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>手机登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
        }

        body {
            background: #f8f8f8;
            min-height: 100vh;
            padding: 20px;
        }

        .login-container {
            max-width: 480px;
            margin: 0 auto;
        }

        .login-title {
            font-size: 24px;
            color: #333;
            margin: 40px 0;
            text-align: center;
        }

        .input-group {
            background: #fff;
            border-radius: 8px;
            padding: 0 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.03);
        }

        .input-item {
            display: flex;
            align-items: center;
            height: 56px;
            border-bottom: 1px solid #eee;
        }

        .input-item:last-child {
            border-bottom: none;
        }

        .input-label {
            width: 80px;
            font-size: 16px;
            color: #333;
        }

        .input-field {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: 10px;
            background: transparent;
        }

        .sms-code-wrapper {
            display: flex;
            gap: 10px;
        }

        .sms-input {
            flex: 1;
            width: 100px;
        }

        .sms-btn {
            width: 140px;
            float:right;
            background: #fff;
            border: 1px solid #07c160;
            color: #07c160;
            border-radius: 6px;
            font-size: 14px;
            padding: 0 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .sms-btn.disabled {
            background: #f8f8f8;
            border-color: #ddd;
            color: #999;
            cursor: not-allowed;
        }

        .login-btn {
            width: 100%;
            height: 48px;
            background: #07c160;
            color: #fff;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            margin-top: 30px;
            cursor: pointer;
        }

        .login-btn:active {
            opacity: 0.9;
        }

        /* 响应式调整 */
        @media (max-width: 375px) {
            .input-label {
                width: 70px;
                font-size: 15px;
            }
            .sms-btn {
                width: 100px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
<form method="post">
    <div class="login-container">
        <h1 class="login-title">手机号登录</h1>
        
        <div class="input-group">
            <div class="input-item">
                <label class="input-label">手机号</label>
                <input type="tel" class="input-field"
                       name="mobile"
                       placeholder="请输入手机号"
                       maxlength="11"
                       pattern="^1[3-9]\d{9}$"
                       required>
            </div>
            
            <div class="input-item">
                <label class="input-label">验证码</label>
                <div class="sms-code-wrapper">
                    <input type="number" class="input-field sms-input"
                           name="code"
                           placeholder="请输入验证码"
                           maxlength="6"
                           required>
                    <button type="button" class="sms-btn" onclick="sendSMSCode()">获取验证码</button>
                </div>
            </div>
        </div>

        <button type="submit" class="login-btn">立即登录</button>
    </div>
</form>
    <script src="/static/js/jquery2_2_4.min.js"></script>
    <script src="/static/js/layer/layer.m.js"></script>

    <script>
        let countdown = 60;
        let timer = null;
        var send = false;


        function sendSMSCode() {
            const btn = document.querySelector('.sms-btn');
            const phone = document.querySelector('input[type="tel"]').value;
            
            // 手机号验证正则
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                return;
            }

            // 禁用按钮
            btn.classList.add('disabled');
            btn.innerText = `${countdown}秒后重发`;
            
            timer = setInterval(() => {
                countdown--;
                btn.innerText = `${countdown}秒后重发`;
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    btn.classList.remove('disabled');
                    btn.innerText = '获取验证码';
                    countdown = 60;
                }
            }, 1000);

            if (send == false) {
                $.getJSON('/index/logincode?mobile=' + phone, function (ret) {
                    var msg = ret.msg;
                    if (ret.code == 0 || ret.code == 2) {
                        $('.getcode').attr('disabled', true).text('已发送');
                        msg = '验证码已发送！';
                        send = true;
                    } else {

                    }
                    layer.open({
                        content: msg,
                        btn: ['确认'],
                        yes: function (index) {
                            layer.close(index);
                            $('#code').focus();
                        },
                    });
                });
            } else {
                layer.open({
                    content: '验证码已发送',
                    btn: ['确认'],
                    yes: function (index) {
                        layer.close(index);
                        $('#code').focus();
                    },
                });
            }
            return false;

            // 这里可以添加实际发送验证码的逻辑
            console.log('发送验证码到:', phone);
        }

        // 倒计时逻辑优化版
function startCountdown() {
    let seconds = 60;
    const btn = document.querySelector('.sms-btn');
    
    const updateBtn = () => {
        btn.textContent = `${seconds}秒后重发`;
        if (seconds <= 0) {
            btn.disabled = false;
            btn.textContent = '获取验证码';
            clearInterval(timer);
        }
        seconds--;
    }

    btn.disabled = true;
    const timer = setInterval(updateBtn, 1000);
    updateBtn(); // 立即执行一次
}
    </script>
</body>
</html>