<ul class="nav nav-tabs">

    <li class="<php>if(CONTROLLER_NAME=='Project' && ACTION_NAME=='index' ) echo 'active'</php>"><a href="{:U('Project/index')}">项目管理</a></li>
    <li class="<php>if(CONTROLLER_NAME=='Project' && ACTION_NAME=='edit') echo 'active'</php>"><a href="{:U('Project/edit')}"><php>echo !I('get.id') ? '添加' : '编辑'</php>项目</a></li>
    <li class="<php>if(CONTROLLER_NAME=='Projectpost' && ACTION_NAME=='index' ) echo 'active'</php>"><a href="{:U('Projectpost/index')}">岗位管理</a></li>
    <li class="<php>if(CONTROLLER_NAME=='Projectpost' && ACTION_NAME=='edit') echo 'active'</php>"><a href="{:U('Projectpost/edit')}"><php>echo !I('get.id') ? '添加' : '编辑'</php>岗位</a></li>
    <li class="<php>if(CONTROLLER_NAME=='Channel' && ACTION_NAME=='index' ) echo 'active'</php>"><a href="{:U('Channel/index')}">渠道管理</a></li>
    <li class="<php>if(CONTROLLER_NAME=='Channel' && ACTION_NAME=='edit') echo 'active'</php>"><a href="{:U('Channel/edit')}"><php>echo !I('get.id') ? '添加' : '编辑'</php>渠道</a></li>

    <li class="<php>if(ACTION_NAME=='quotation' ) echo 'active'</php>"><a href="{:U('Project/quotation')}">报价管理</a></li>
    <php>if (ACTION_NAME=='setquotation' ) {
    </php>
    <li class="<php>if(ACTION_NAME=='setquotation') echo 'active'</php>"><a href="#"><php>echo !I('get.id') ? '添加' : '编辑'</php>报价</a></li>
    <php>}</php>
</ul>