<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问提醒 - 中才国科管理中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .access-denied-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px 30px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
        }



        .icon-container {
            margin-bottom: 30px;
        }

        .access-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
        }

        .access-icon::before {
            content: '!';
            font-size: 48px;
            color: white;
            font-weight: bold;
            font-family: Arial, sans-serif;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .title {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            line-height: 1.3;
        }

        .subtitle {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .message-box {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }

        .message-text {
            font-size: 16px;
            line-height: 1.6;
            color: #34495e;
            text-align: left;
        }

        .highlight {
            color: #e74c3c;
            font-weight: 600;
        }

        .contact-info {
            background: #e8f4fd;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #bee5eb;
        }

        .contact-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .contact-text {
            font-size: 14px;
            color: #5a6c7d;
            line-height: 1.5;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .footer-text {
            margin-top: 30px;
            font-size: 12px;
            color: #95a5a6;
            line-height: 1.4;
        }

        @media (max-width: 480px) {
            .access-denied-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .title {
                font-size: 24px;
            }

            .subtitle {
                font-size: 16px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="access-denied-container">
        <div class="icon-container">
            <div class="access-icon pulse"></div>
        </div>
        
        <h1 class="title">访问提醒</h1>
        <p class="subtitle">中才国科管理中心</p>
        
        <div class="message-box">
            <div class="message-text">
                <p><span class="highlight">管理中心只对服务站和招就办开放</span></p>
                <br>
                <p>如果您是服务站或招就办用户，但无法进入管理中心，可能是以下原因：</p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>账号初始化时，未通过关注服务号，从服务号菜单进入</li>
                    <li>账户类型被错误配置</li>
                </ul>
            </div>
        </div>
        
        <div class="contact-info">
            <div class="contact-title">需要帮助？</div>
            <div class="contact-text">
                请联系中才国科管理员处理<br>
                我们将协助您解决问题
            </div>
        </div>
        
        <div class="action-buttons">
            <a href="javascript:history.back()" class="btn btn-secondary">
                ← 返回上页
            </a>
            <a href="javascript:void(0)" onclick="closeCurrentPage()" class="btn btn-primary">
                ✕ 关闭此页面
            </a>
        </div>
        
        <div class="footer-text">
            确认是服务站或招就办用户？请通过联系管理员进行处理
        </div>
    </div>

    <script>
        // 关闭当前页面的函数
        function closeCurrentPage() {
            // 尝试调用微信的关闭页面接口
            if (typeof WeixinJSBridge !== 'undefined') {
                WeixinJSBridge.call('closeWindow');
            } else if (window.wx && window.wx.closeWindow) {
                // 如果有微信JS-SDK
                wx.closeWindow();
            } else {
                // 降级方案：尝试关闭窗口
                try {
                    window.close();
                } catch (e) {
                    // 如果无法关闭，则返回上一页
                    if (window.history.length > 1) {
                        window.history.back();
                    } else {
                        // 最后的降级方案：跳转到首页
                        window.location.href = '/';
                    }
                }
            }
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为按钮添加点击效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // 创建点击波纹效果
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
