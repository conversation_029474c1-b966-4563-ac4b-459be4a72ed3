<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化灵工管理页面样式 */
                .jingling-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .jingling-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .jingling-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .jingling-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%);
                }

                .jingling-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .jingling-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .jingling-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .jingling-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .jingling-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .jingling-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .jingling-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .jingling-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.3);
                    text-decoration: none;
                }

                .jingling-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(139, 92, 246, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .jingling-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .jingling-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .jingling-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .jingling-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .jingling-index-nav-link:hover {
                    color: #8b5cf6;
                    background: rgba(139, 92, 246, 0.05);
                    text-decoration: none;
                }

                .jingling-index-nav-link.active {
                    color: #8b5cf6 !important;
                    background: white !important;
                    border-bottom-color: #8b5cf6 !important;
                    box-shadow: 0 -2px 4px rgba(139, 92, 246, 0.1) !important;
                    font-weight: 700 !important;
                }

                .jingling-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #8b5cf6 0%, #a855f7 100%);
                }

                .jingling-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 搜索面板 - 默认隐藏 */
                .jingling-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    display: none;
                    animation: slideDown 0.3s ease-out;
                }

                .jingling-index-search-panel.show {
                    display: block;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .jingling-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .jingling-index-search-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .jingling-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .jingling-index-search-close {
                    background: none;
                    border: none;
                    color: #718096;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                }

                .jingling-index-search-close:hover {
                    background: #f1f5f9;
                    color: #374151;
                }

                .jingling-index-search-body {
                    padding: 2rem;
                }

                /* 现代化灵工卡片 */
                .jingling-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .jingling-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .jingling-card-header {
                    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .jingling-card-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .jingling-card-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin: 0;
                }

                .jingling-card-id {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .jingling-card-worker-name {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .jingling-card-platform {
                    font-size: 1.25rem;
                    color: rgba(255, 255, 255, 0.9);
                    margin: 0.25rem 0 0 0;
                    font-weight: 400;
                }

                .jingling-card-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    flex-direction: column;
                }

                .jingling-status-badge {
                    padding: 0.5rem 1rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    margin-bottom: 0.5rem;
                }

                .jingling-status-badge.status-pending {
                    background: rgba(251, 191, 36, 0.9);
                    border-color: rgba(251, 191, 36, 0.5);
                }

                .jingling-status-badge.status-approved {
                    background: rgba(16, 185, 129, 0.9);
                    border-color: rgba(16, 185, 129, 0.5);
                }

                .jingling-status-badge.status-rejected {
                    background: rgba(239, 68, 68, 0.9);
                    border-color: rgba(239, 68, 68, 0.5);
                }

                .jingling-platform-badge {
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1rem;
                    font-weight: 600;
                    border: 1px solid rgba(255, 255, 255, 0.5);
                }

                .jingling-platform-badge.platform-jingdong {
                    background: rgba(59, 130, 246, 0.9);
                    color: white;
                }

                .jingling-platform-badge.platform-yunzhanghu {
                    background: rgba(16, 185, 129, 0.9);
                    color: white;
                }

                .jingling-card-body {
                    padding: 2rem;
                }

                .jingling-info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                .jingling-info-section {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    border: 1px solid #e2e8f0;
                }

                .jingling-info-section-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1rem 0;
                }

                .jingling-info-section-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .jingling-info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                }

                .jingling-info-item:last-child {
                    margin-bottom: 0;
                }

                .jingling-info-label {
                    color: #6b7280;
                    font-weight: 500;
                    min-width: 80px;
                    flex-shrink: 0;
                }

                .jingling-info-value {
                    color: #374151;
                    font-weight: 600;
                    flex: 1;
                    word-break: break-all;
                }

                .jingling-id-card {
                    font-family: 'Courier New', monospace;
                    font-size: 1.5rem;
                    letter-spacing: 0.05em;
                    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
                    border: 1px solid #0ea5e9;
                    border-radius: 0.5rem;
                    padding: 0.5rem 0.75rem;
                    color: #0c4a6e;
                    font-weight: 700;
                }

                .jingling-phone {
                    font-family: 'Courier New', monospace;
                    font-size: 1.5rem;
                    letter-spacing: 0.05em;
                    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
                    border: 1px solid #22c55e;
                    border-radius: 0.5rem;
                    padding: 0.5rem 0.75rem;
                    color: #15803d;
                    font-weight: 700;
                }

                /* 操作按钮区域 */
                .jingling-actions {
                    display: flex;
                    gap: 0.75rem;
                    margin-top: 2rem;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                    flex-wrap: wrap;
                }

                .jingling-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .jingling-action-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .jingling-action-btn.btn-primary {
                    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
                    color: white;
                }

                .jingling-action-btn.btn-primary:hover {
                    color: white;
                }

                .jingling-action-btn.btn-danger {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    color: white;
                }

                .jingling-action-btn.btn-danger:hover {
                    color: white;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .jingling-index-container {
                        padding: 1.5rem;
                    }

                    .jingling-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .jingling-info-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .jingling-index-container {
                        padding: 1rem;
                    }

                    .jingling-index-nav-tabs {
                        flex-direction: column;
                    }

                    .jingling-index-nav-item {
                        flex: none;
                    }

                    .jingling-index-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .jingling-index-nav-link.active {
                        border-bottom: none !important;
                        border-right-color: #8b5cf6 !important;
                    }

                    .jingling-index-title-main {
                        font-size: 1.75rem;
                    }

                    .jingling-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .jingling-actions {
                        flex-direction: column;
                    }

                    .jingling-card-header {
                        flex-direction: column;
                        gap: 1rem;
                        text-align: center;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .jingling-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .jingling-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .jingling-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .jingling-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="jingling-index-wrapper">
                <div class="jingling-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="jingling-index-header jingling-index-fade-in">
                        <div class="jingling-index-header-content">
                            <div class="jingling-index-title">
                                <div class="jingling-index-title-icon">
                                    <i class="fa fa-users"></i>
                                </div>
                                <div class="jingling-index-title-text">
                                    <h1 class="jingling-index-title-main">灵工管理</h1>
                                    <p class="jingling-index-title-sub">Flexible Worker Management</p>
                                </div>
                            </div>
                            <div class="jingling-index-actions">
                                <button type="button" class="jingling-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="jingling-index-nav-container jingling-index-fade-in-delay-1">
                        <ul class="jingling-index-nav-tabs">
                            <li class="jingling-index-nav-item">
                                <a href="{:U('Jingling/index')}" class="jingling-index-nav-link active">
                                    <i class="fa fa-list jingling-index-nav-icon"></i>
                                    <span>全部灵工</span>
                                </a>
                            </li>
                            <li class="jingling-index-nav-item">
                                <a href="javascript:void(0)" class="jingling-index-nav-link quick-filter" data-filter="pending">
                                    <i class="fa fa-clock-o jingling-index-nav-icon"></i>
                                    <span>待审核</span>
                                </a>
                            </li>
                            <li class="jingling-index-nav-item">
                                <a href="javascript:void(0)" class="jingling-index-nav-link quick-filter" data-filter="approved">
                                    <i class="fa fa-check-circle jingling-index-nav-icon"></i>
                                    <span>已通过</span>
                                </a>
                            </li>
                            <li class="jingling-index-nav-item">
                                <a href="javascript:void(0)" class="jingling-index-nav-link quick-filter" data-filter="rejected">
                                    <i class="fa fa-times-circle jingling-index-nav-icon"></i>
                                    <span>已拒绝</span>
                                </a>
                            </li>
                            <li class="jingling-index-nav-item">
                                <a href="javascript:void(0)" class="jingling-index-nav-link quick-filter" data-filter="jingdong">
                                    <i class="fa fa-shopping-cart jingling-index-nav-icon"></i>
                                    <span>京东京灵</span>
                                </a>
                            </li>
                            <li class="jingling-index-nav-item">
                                <a href="javascript:void(0)" class="jingling-index-nav-link quick-filter" data-filter="yunzhanghu">
                                    <i class="fa fa-cloud jingling-index-nav-icon"></i>
                                    <span>云账户</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 搜索面板 - 默认隐藏 -->
                    <div class="jingling-index-search-panel" id="searchPanel">
                        <div class="jingling-index-search-header">
                            <div class="jingling-index-search-title">
                                <div class="jingling-index-search-icon">
                                    <i class="fa fa-search"></i>
                                </div>
                                <span>搜索筛选</span>
                            </div>
                            <button type="button" class="jingling-index-search-close" onclick="toggleSearchPanel()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                        <div class="jingling-index-search-body">
                            <form method="get" action="{:U('Jingling/index')}" class="search-form">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">状态：</label>
                                            <select name="status" class="form-control">
                                                <option value="">全部状态</option>
                                                <foreach name="status_list" item="status_name" key="status_key">
                                                    <option value="{$status_key}" <if condition="$status === (string)$status_key">selected</if>>{$status_name}</option>
                                                </foreach>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">平台：</label>
                                            <select name="platform_type" class="form-control">
                                                <option value="">全部平台</option>
                                                <foreach name="platform_types" item="platform_name" key="platform_key">
                                                    <option value="{$platform_key}" <if condition="$platform_type === (string)$platform_key">selected</if>>{$platform_name}</option>
                                                </foreach>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">关键词：</label>
                                            <input type="text" name="keyword" class="form-control" value="{$keyword}" placeholder="灵工姓名/身份证/手机号" />
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-top: 15px;">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-search"></i> 搜索
                                            </button>
                                            <a href="{:U('Jingling/index')}" class="btn btn-default">
                                                <i class="fa fa-refresh"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- 灵工列表 -->
                    <div class="jingling-index-fade-in-delay-2">
                        <foreach name="list" item="item">
                        <div class="jingling-card">
                            <!-- 卡片头部 -->
                            <div class="jingling-card-header">
                                <div class="jingling-card-title">
                                    <span class="jingling-card-id">#{$item.id}</span>
                                    <div>
                                        <h3 class="jingling-card-worker-name">{$item.worker_name}</h3>
                                        <p class="jingling-card-platform">
                                            <if condition="$item.platform_type eq 1">
                                                京东京灵平台
                                            <elseif condition="$item.platform_type eq 2" />
                                                云账户平台
                                            <else />
                                                未知平台
                                            </if>
                                        </p>
                                    </div>
                                </div>
                                <div class="jingling-card-status">
                                    <if condition="$item.status eq 0">
                                        <span class="jingling-status-badge status-pending">
                                            <i class="fa fa-clock-o"></i>
                                            待审核
                                        </span>
                                    <elseif condition="$item.status eq 1" />
                                        <span class="jingling-status-badge status-approved">
                                            <i class="fa fa-check-circle"></i>
                                            已通过
                                        </span>
                                    <elseif condition="$item.status eq 2" />
                                        <span class="jingling-status-badge status-rejected">
                                            <i class="fa fa-times-circle"></i>
                                            已拒绝
                                        </span>
                                    <else />
                                        <span class="jingling-status-badge">
                                            <i class="fa fa-question-circle"></i>
                                            未知状态
                                        </span>
                                    </if>

                                    <if condition="$item.platform_type eq 1">
                                        <span class="jingling-platform-badge platform-jingdong">
                                            <i class="fa fa-shopping-cart"></i>
                                            京东京灵
                                        </span>
                                    <elseif condition="$item.platform_type eq 2" />
                                        <span class="jingling-platform-badge platform-yunzhanghu">
                                            <i class="fa fa-cloud"></i>
                                            云账户
                                        </span>
                                    </if>
                                </div>
                            </div>

                            <!-- 卡片主体 -->
                            <div class="jingling-card-body">
                                <div class="jingling-info-grid">
                                    <!-- 基本信息 -->
                                    <div class="jingling-info-section">
                                        <h4 class="jingling-info-section-title">
                                            <div class="jingling-info-section-icon">
                                                <i class="fa fa-user"></i>
                                            </div>
                                            基本信息
                                        </h4>
                                        <div class="jingling-info-item">
                                            <span class="jingling-info-label">姓名：</span>
                                            <span class="jingling-info-value">{$item.worker_name}</span>
                                        </div>
                                        <div class="jingling-info-item">
                                            <span class="jingling-info-label">身份证：</span>
                                            <span class="jingling-info-value jingling-id-card">{$item.id_card}</span>
                                        </div>
                                        <div class="jingling-info-item">
                                            <span class="jingling-info-label">手机号：</span>
                                            <span class="jingling-info-value jingling-phone">{$item.phone}</span>
                                        </div>
                                    </div>

                                    <!-- 关联信息 -->
                                    <div class="jingling-info-section">
                                        <h4 class="jingling-info-section-title">
                                            <div class="jingling-info-section-icon">
                                                <i class="fa fa-link"></i>
                                            </div>
                                            关联信息
                                        </h4>
                                        <div class="jingling-info-item">
                                            <span class="jingling-info-label">关联用户：</span>
                                            <span class="jingling-info-value">{$item.user_info.nickname}（ID: {$item.user_id}）</span>
                                        </div>
                                        <div class="jingling-info-item">
                                            <span class="jingling-info-label">服务站：</span>
                                            <span class="jingling-info-value">{$item.station_info.service_name}</span>
                                        </div>
                                        <div class="jingling-info-item">
                                            <span class="jingling-info-label">结算银行：</span>
                                            <span class="jingling-info-value">{$item.bank_name}（尾号：{$item.bank_account_last4}）</span>
                                        </div>
                                    </div>

                                    <!-- 状态信息 -->
                                    <div class="jingling-info-section">
                                        <h4 class="jingling-info-section-title">
                                            <div class="jingling-info-section-icon">
                                                <i class="fa fa-info-circle"></i>
                                            </div>
                                            状态信息
                                        </h4>
                                        <div class="jingling-info-item">
                                            <span class="jingling-info-label">平台类型：</span>
                                            <span class="jingling-info-value">
                                                <if condition="$item.platform_type eq 1">
                                                    <span style="color: #3b82f6; font-weight: 600;">
                                                        <i class="fa fa-shopping-cart"></i> 京东京灵
                                                    </span>
                                                <elseif condition="$item.platform_type eq 2" />
                                                    <span style="color: #10b981; font-weight: 600;">
                                                        <i class="fa fa-cloud"></i> 云账户
                                                    </span>
                                                <else />
                                                    <span style="color: #6b7280;">
                                                        <i class="fa fa-question-circle"></i> 未知平台
                                                    </span>
                                                </if>
                                            </span>
                                        </div>
                                        <div class="jingling-info-item">
                                            <span class="jingling-info-label">审核状态：</span>
                                            <span class="jingling-info-value">
                                                <if condition="$item.status eq 0">
                                                    <span style="color: #f59e0b; font-weight: 600;">
                                                        <i class="fa fa-clock-o"></i> 待审核
                                                    </span>
                                                <elseif condition="$item.status eq 1" />
                                                    <span style="color: #10b981; font-weight: 600;">
                                                        <i class="fa fa-check-circle"></i> 已通过
                                                    </span>
                                                <elseif condition="$item.status eq 2" />
                                                    <span style="color: #ef4444; font-weight: 600;">
                                                        <i class="fa fa-times-circle"></i> 已拒绝
                                                    </span>
                                                <else />
                                                    <span style="color: #6b7280;">
                                                        <i class="fa fa-question-circle"></i> 未知状态
                                                    </span>
                                                </if>
                                            </span>
                                        </div>
                                        <div class="jingling-info-item">
                                            <span class="jingling-info-label">提交时间：</span>
                                            <span class="jingling-info-value">{$item.create_time_format}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="jingling-actions">
                                    <a href="{:U('Jingling/review', ['id' => $item['id']])}" class="jingling-action-btn btn-primary">
                                        <i class="fa fa-gavel"></i>
                                        <span>审核</span>
                                    </a>
                                    <a href="{:U('Jingling/delete', ['id' => $item['id']])}" class="jingling-action-btn btn-danger" onclick="return confirm('确定要删除此绑定记录吗？')">
                                        <i class="fa fa-trash"></i>
                                        <span>删除</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                        </foreach>

                        <empty name="list">
                        <div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;">
                            <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;">
                                <i class="fa fa-users"></i>
                            </div>
                            <h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无灵工数据</h3>
                            <p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或等待新的灵工注册。</p>
                        </div>
                        </empty>

                        <!-- 分页和每页显示数量 -->
                        <div class="jingling-index-fade-in-delay-3" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 1.5rem; margin-top: 2rem;">
                            <div class="row">
                                <div class="col-sm-8">
                                    <div style="display: flex; align-items: center; justify-content: center;">
                                        {$page}
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="pull-right form-inline" style="display: flex; align-items: center; gap: 0.5rem;">
                                        <span style="font-size: 1.5rem; color: #374151;">每页显示:</span>
                                        <select class="form-control" id="pageSizeSelector" style="width: auto; font-size: 1.5rem;">
                                            <option value="10" <php>if($pageSize == 10) echo 'selected';</php>>10条</option>
                                            <option value="20" <php>if($pageSize == 20) echo 'selected';</php>>20条</option>
                                            <option value="50" <php>if($pageSize == 50) echo 'selected';</php>>50条</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />

<script>
    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
            setTimeout(function() {
                panel.style.display = 'none';
            }, 300);
        } else {
            panel.style.display = 'block';
            setTimeout(function() {
                panel.classList.add('show');
            }, 10);
        }
    }

    $(document).ready(function() {
        // 根据当前URL参数设置active状态
        function setActiveNavTab() {
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');
            var platformType = urlParams.get('platform_type');

            // 移除所有active类
            $('.jingling-index-nav-link').removeClass('active');

            // 根据URL参数设置对应的active类
            if (status === '0') {
                // 待审核状态筛选
                $('.quick-filter[data-filter="pending"]').addClass('active');
            } else if (status === '1') {
                // 已通过状态筛选
                $('.quick-filter[data-filter="approved"]').addClass('active');
            } else if (status === '2') {
                // 已拒绝状态筛选
                $('.quick-filter[data-filter="rejected"]').addClass('active');
            } else if (platformType === '1') {
                // 京东京灵平台筛选
                $('.quick-filter[data-filter="jingdong"]').addClass('active');
            } else if (platformType === '2') {
                // 云账户平台筛选
                $('.quick-filter[data-filter="yunzhanghu"]').addClass('active');
            } else {
                // 默认全部灵工
                $('.jingling-index-nav-link').first().addClass('active');
            }
        }

        // 页面加载时设置active状态
        setActiveNavTab();

        // 快速筛选功能
        $('.quick-filter').click(function(e) {
            e.preventDefault();
            var filter = $(this).data('filter');
            var currentUrl = window.location.href.split('?')[0];
            var newUrl = currentUrl + '?';

            // 移除当前active类并添加到点击的元素
            $('.jingling-index-nav-link').removeClass('active');
            $(this).addClass('active');

            switch(filter) {
                case 'pending':
                    newUrl += 'status=0'; // 待审核状态
                    break;
                case 'approved':
                    newUrl += 'status=1'; // 已通过状态
                    break;
                case 'rejected':
                    newUrl += 'status=2'; // 已拒绝状态
                    break;
                case 'jingdong':
                    newUrl += 'platform_type=1'; // 京东京灵平台
                    break;
                case 'yunzhanghu':
                    newUrl += 'platform_type=2'; // 云账户平台
                    break;
                default:
                    newUrl = currentUrl;
            }

            window.location.href = newUrl;
        });

        // "全部灵工"链接点击处理
        $('.jingling-index-nav-link').first().click(function(e) {
            if ($(this).attr('href') !== 'javascript:void(0)') {
                $('.jingling-index-nav-link').removeClass('active');
                $(this).addClass('active');
            }
        });

        // 卡片悬停效果增强
        $('.jingling-card').hover(
            function() {
                $(this).find('.jingling-card-header').css('transform', 'scale(1.02)');
            },
            function() {
                $(this).find('.jingling-card-header').css('transform', 'scale(1)');
            }
        );

        // 搜索表单增强
        $('.search-form').on('submit', function() {
            var $submitBtn = $(this).find('button[type="submit"]');
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 搜索中...');

            setTimeout(function() {
                $submitBtn.prop('disabled', false).html(originalText);
            }, 3000);
        });

        // 每页显示数量选择器
        $('#pageSizeSelector').change(function() {
            var pageSize = $(this).val();
            var url = window.location.href;

            // 处理已有的pageSize参数
            if (url.indexOf('pageSize=') > -1) {
                url = url.replace(/pageSize=\d+/, 'pageSize=' + pageSize);
            } else {
                // 添加pageSize参数
                if (url.indexOf('?') > -1) {
                    url += '&pageSize=' + pageSize;
                } else {
                    url += '?pageSize=' + pageSize;
                }
            }

            window.location.href = url;
        });

        // 滚动到顶部功能
        var $scrollToTop = $('<button class="scroll-to-top" style="position: fixed; bottom: 2rem; right: 2rem; width: 3rem; height: 3rem; background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%); color: white; border: none; border-radius: 50%; font-size: 1.25rem; cursor: pointer; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; display: none; transition: all 0.3s ease;"><i class="fa fa-arrow-up"></i></button>');
        $('body').append($scrollToTop);

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $scrollToTop.fadeIn();
            } else {
                $scrollToTop.fadeOut();
            }
        });

        $scrollToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 600);
        });

        // 添加加载动画
        $('.jingling-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });

        // 搜索结果统计
        var totalCards = $('.jingling-card').length;
        if (totalCards > 0) {
            var $statsInfo = $('<div class="search-stats" style="background: white; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; color: #6b7280; font-size: 1.25rem;"><i class="fa fa-info-circle"></i> 共找到 ' + totalCards + ' 个灵工</div>');
            $('.jingling-card').first().before($statsInfo);
        }

        // 状态统计
        var statusCounts = {};
        $('.jingling-status-badge').each(function() {
            var status = $(this).text().trim();
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });

        console.log('状态统计:', statusCounts);

        // 延迟执行active状态设置，确保DOM完全加载
        setTimeout(function() {
            setActiveNavTab();
        }, 100);
    });

    // 添加自定义样式
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .search-stats {
                animation: fadeInUp 0.6s ease-out;
            }

            .empty-state {
                animation: fadeInUp 0.6s ease-out;
            }

            .jingling-card {
                animation: fadeInUp 0.6s ease-out both;
            }

            .scroll-to-top:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 15px rgba(0,0,0,0.2);
            }

            .jingling-action-btn:not(.qrcode) {
                margin-right: 0.5rem;
                margin-bottom: 0.5rem;
            }

            .jingling-card-header {
                transition: all 0.3s ease;
            }

            /* 增强active状态的视觉效果 */
            .jingling-index-nav-link.active {
                background: white !important;
                color: #8b5cf6 !important;
                border-bottom-color: #8b5cf6 !important;
                box-shadow: 0 -2px 4px rgba(139, 92, 246, 0.1) !important;
                font-weight: 700 !important;
            }

            .jingling-index-nav-link.active .jingling-index-nav-icon {
                color: #8b5cf6 !important;
            }

            /* 确保active状态在hover时保持 */
            .jingling-index-nav-link.active:hover {
                background: white !important;
                color: #8b5cf6 !important;
            }
        `)
        .appendTo('head');
</script>