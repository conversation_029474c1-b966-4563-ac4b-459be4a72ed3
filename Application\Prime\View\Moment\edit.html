<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化素材编辑页面样式 */
                .moment-edit-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .moment-edit-container {
                    max-width: 1600px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .moment-edit-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .moment-edit-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #06b6d4 0%, #0891b2 50%, #0e7490 100%);
                }

                .moment-edit-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .moment-edit-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .moment-edit-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .moment-edit-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .moment-edit-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .moment-edit-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .moment-edit-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .moment-edit-back-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .moment-edit-back-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化编辑布局 */
                .moment-edit-layout {
                    display: grid;
                    grid-template-columns: 1fr 400px;
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                @media (max-width: 1200px) {
                    .moment-edit-layout {
                        grid-template-columns: 1fr;
                        gap: 1.5rem;
                    }
                }

                /* 现代化表单卡片 */
                .moment-edit-form-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .moment-edit-form-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .moment-edit-form-header {
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .moment-edit-form-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .moment-edit-form-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .moment-edit-form-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .moment-edit-form-body {
                    padding: 2rem;
                }

                .moment-edit-form-section {
                    margin-bottom: 2rem;
                    padding-bottom: 2rem;
                    border-bottom: 1px solid #f1f5f9;
                }

                .moment-edit-form-section:last-child {
                    margin-bottom: 0;
                    padding-bottom: 0;
                    border-bottom: none;
                }

                .moment-edit-section-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1.5rem 0;
                }

                .moment-edit-section-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .moment-edit-form-group {
                    margin-bottom: 1.5rem;
                }

                .moment-edit-form-group:last-child {
                    margin-bottom: 0;
                }

                .moment-edit-form-label {
                    display: block;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 0.75rem;
                }

                .moment-edit-form-required {
                    color: #ef4444;
                    margin-right: 0.25rem;
                }

                .moment-edit-form-select,
                .moment-edit-form-input,
                .moment-edit-form-textarea {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-family: inherit;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: #f8fafc;
                }

                .moment-edit-form-select:focus,
                .moment-edit-form-input:focus,
                .moment-edit-form-textarea:focus {
                    outline: none;
                    border-color: #06b6d4;
                    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
                    background: white;
                }

                .moment-edit-form-textarea {
                    min-height: 200px;
                    resize: vertical;
                    line-height: 1.6;
                }

                .moment-edit-form-help {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-top: 0.5rem;
                    font-style: italic;
                }

                /* 现代化图片上传区域 */
                .moment-edit-images-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                    gap: 1.5rem;
                    margin-top: 1rem;
                }

                .moment-edit-image-upload {
                    position: relative;
                    border: 2px dashed #cbd5e1;
                    border-radius: 0.75rem;
                    padding: 1rem;
                    text-align: center;
                    transition: all 0.3s ease;
                    background: #f8fafc;
                    min-height: 150px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                }

                .moment-edit-image-upload:hover {
                    border-color: #06b6d4;
                    background: rgba(6, 182, 212, 0.05);
                }

                .moment-edit-image-upload.has-image {
                    border-style: solid;
                    border-color: #06b6d4;
                    background: white;
                }

                .moment-edit-upload-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                    margin-bottom: 0.75rem;
                }

                .moment-edit-upload-text {
                    font-size: 1.25rem;
                    color: #6b7280;
                    font-weight: 500;
                }

                .moment-edit-image-preview {
                    width: 100%;
                    height: 150px;
                    object-fit: cover;
                    border-radius: 0.5rem;
                    margin-bottom: 0.75rem;
                }

                .moment-edit-image-actions {
                    display: flex;
                    gap: 0.5rem;
                    justify-content: center;
                }

                .moment-edit-image-btn {
                    padding: 0.5rem 1rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .moment-edit-image-btn.btn-change {
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    color: white;
                }

                .moment-edit-image-btn.btn-remove {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    color: white;
                }

                .moment-edit-image-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                }

                /* 现代化预览区域 */
                .moment-edit-preview-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    position: sticky;
                    top: 2rem;
                    height: fit-content;
                    max-height: calc(100vh - 4rem);
                }

                .moment-edit-preview-header {
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .moment-edit-preview-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .moment-edit-preview-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .moment-edit-preview-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .moment-edit-preview-body {
                    padding: 0;
                    overflow: hidden;
                }

                /* 手机预览样式优化 */
                .moment-edit-phone-container {
                    background-color: #f7f7f7;
                    width: 100%;
                    max-width: 393px;
                    margin: 0 auto;
                    box-shadow: 0 0 20px rgba(0,0,0,0.15);
                    height: 600px;
                    position: relative;
                    overflow-y: auto;
                    overflow-x: hidden;
                    border-radius: 18px;
                    border: 2px solid #e2e8f0;
                }

                /* 操作按钮区域 */
                .moment-edit-form-actions {
                    display: flex;
                    gap: 1rem;
                    margin-top: 2rem;
                    padding-top: 2rem;
                    border-top: 1px solid #e2e8f0;
                    flex-wrap: wrap;
                }

                .moment-edit-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 2rem;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    min-width: 140px;
                    justify-content: center;
                }

                .moment-edit-action-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .moment-edit-action-btn.btn-primary {
                    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                    color: white;
                }

                .moment-edit-action-btn.btn-primary:hover {
                    color: white;
                }

                .moment-edit-action-btn.btn-secondary {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                }

                .moment-edit-action-btn.btn-secondary:hover {
                    color: white;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .moment-edit-container {
                        padding: 1rem;
                    }

                    .moment-edit-layout {
                        grid-template-columns: 1fr;
                    }

                    .moment-edit-preview-card {
                        position: static;
                        max-height: none;
                    }

                    .moment-edit-form-actions {
                        flex-direction: column;
                    }

                    .moment-edit-action-btn {
                        width: 100%;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .moment-edit-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .moment-edit-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .moment-edit-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }
            </style>

            <div class="moment-edit-wrapper">
                <div class="moment-edit-container">
                    <!-- 现代化页面标题 -->
                    <div class="moment-edit-header moment-edit-fade-in">
                        <div class="moment-edit-header-content">
                            <div class="moment-edit-title">
                                <div class="moment-edit-title-icon">
                                    <i class="fa fa-edit"></i>
                                </div>
                                <div class="moment-edit-title-text">
                                    <h1 class="moment-edit-title-main">
                                        <php>echo isset($row['id']) && $row['id'] > 0 ? '编辑素材' : '添加素材';</php>
                                    </h1>
                                    <p class="moment-edit-title-sub">Content Material Editor</p>
                                </div>
                            </div>
                            <div class="moment-edit-actions">
                                <a href="{:U('Moment/index')}" class="moment-edit-back-btn">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化编辑布局 -->
                    <div class="moment-edit-layout">
                        <!-- 左侧编辑区域 -->
                        <div class="moment-edit-fade-in-delay-1">
                            <div class="moment-edit-form-card">
                                <div class="moment-edit-form-header">
                                    <div class="moment-edit-form-icon">
                                        <i class="fa fa-edit"></i>
                                    </div>
                                    <h3 class="moment-edit-form-title">素材编辑</h3>
                                </div>
                                <div class="moment-edit-form-body">
                                    <form action="" method="post" class="moment-edit-form js-ajax-form" enctype="multipart/form-data" id="momentForm">
                                        <!-- 基本信息区域 -->
                                        <div class="moment-edit-form-section">
                                            <h4 class="moment-edit-section-title">
                                                <div class="moment-edit-section-icon">
                                                    <i class="fa fa-info-circle"></i>
                                                </div>
                                                基本信息
                                            </h4>

                                            <div class="moment-edit-form-group">
                                                <label class="moment-edit-form-label">
                                                    <span class="moment-edit-form-required">*</span>
                                                    关联项目
                                                </label>
                                                <select name="project_id" class="moment-edit-form-select" id="project" required>
                                                    <option value="">请选择项目</option>
                                                    <php>foreach($projects as $key => $val) {</php>
                                                        <option value="{$key}" {: $key == $row['project_id'] ? 'selected' : ''}>{$val}</option>
                                                        <php>}</php>
                                                </select>
                                                <div class="moment-edit-form-help">选择素材所属的项目分类</div>
                                            </div>

                                            <div class="moment-edit-form-group">
                                                <label class="moment-edit-form-label">
                                                    <span class="moment-edit-form-required">*</span>
                                                    关联岗位
                                                </label>
                                                <select name="post_id" class="moment-edit-form-select" id="post" required>
                                                    <php>if ($row['post_id']){</php>
                                                        <option value="{$key}" {: $key == $row['post_id'] ? 'selected' : ''}>{$val}</option>
                                                    <php>}else{</php>
                                                    <option value="">请先选择项目</option>
                                                    <php>}</php>
                                                </select>
                                                <div class="moment-edit-form-help">选择素材适用的岗位类型</div>
                                            </div>
                                        </div>

                                        <!-- 内容编辑区域 -->
                                        <div class="moment-edit-form-section">
                                            <h4 class="moment-edit-section-title">
                                                <div class="moment-edit-section-icon">
                                                    <i class="fa fa-file-text"></i>
                                                </div>
                                                文本内容
                                            </h4>

                                            <div class="moment-edit-form-group">
                                                <label class="moment-edit-form-label">
                                                    <span class="moment-edit-form-required">*</span>
                                                    朋友圈文本（支持emoji）
                                                </label>
                                                <textarea
                                                    name="content"
                                                    class="moment-edit-form-textarea"
                                                    placeholder="请输入朋友圈文本内容，支持换行和表情符号..."
                                                    id="content"
                                                    required
                                                >{$row['content']}</textarea>
                                                <div class="moment-edit-form-help">
                                                    <i class="fa fa-lightbulb-o"></i>
                                                    建议控制在200字以内，内容将实时显示在右侧预览区域
                                                </div>
                                                <div class="text-counter" style="text-align: right; margin-top: 0.5rem; font-size: 1.25rem; color: #6b7280;">
                                                    <span id="contentLength">0</span>/200 字符
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 图片上传区域 -->
                                        <div class="moment-edit-form-section">
                                            <h4 class="moment-edit-section-title">
                                                <div class="moment-edit-section-icon">
                                                    <i class="fa fa-image"></i>
                                                </div>
                                                图片素材
                                            </h4>

                                            <div class="moment-edit-form-group">
                                                <label class="moment-edit-form-label">
                                                    图片上传（最多9张）
                                                </label>
                                                <div class="moment-edit-form-help" style="margin-bottom: 1rem;">
                                                    <i class="fa fa-info-circle"></i>
                                                    支持JPG、PNG格式，建议尺寸不超过2MB，图片将按上传顺序显示
                                                </div>

                                                <div class="moment-edit-images-grid">
                                                    <!-- 图片1 -->
                                                    <div class="moment-edit-image-upload" id="imageUpload1">
                                                        <php>if($row['img1']) {</php>
                                                        <img src="http://we.zhongcaiguoke.com/{$row['img1']}" class="moment-edit-image-preview" alt="图片1">
                                                        <div class="moment-edit-image-actions">
                                                            <button type="button" class="moment-edit-image-btn btn-change" onclick="changeImage(1)">
                                                                <i class="fa fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="moment-edit-image-btn btn-remove" onclick="removeImage(1)">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        <php>} else {</php>
                                                        <div class="moment-edit-upload-icon">
                                                            <i class="fa fa-plus"></i>
                                                        </div>
                                                        <div class="moment-edit-upload-text">点击上传图片1</div>
                                                        <php>}</php>
                                                        <php>echo tpl_form_field_image('img1', $row['img1'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                                    </div>

                                                    <!-- 图片2 -->
                                                    <div class="moment-edit-image-upload" id="imageUpload2">
                                                        <php>if($row['img2']) {</php>
                                                        <img src="http://we.zhongcaiguoke.com/{$row['img2']}" class="moment-edit-image-preview" alt="图片2">
                                                        <div class="moment-edit-image-actions">
                                                            <button type="button" class="moment-edit-image-btn btn-change" onclick="changeImage(2)">
                                                                <i class="fa fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="moment-edit-image-btn btn-remove" onclick="removeImage(2)">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        <php>} else {</php>
                                                        <div class="moment-edit-upload-icon">
                                                            <i class="fa fa-plus"></i>
                                                        </div>
                                                        <div class="moment-edit-upload-text">点击上传图片2</div>
                                                        <php>}</php>
                                                        <php>echo tpl_form_field_image('img2', $row['img2'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                                    </div>

                                                    <!-- 图片3 -->
                                                    <div class="moment-edit-image-upload" id="imageUpload3">
                                                        <php>if($row['img3']) {</php>
                                                        <img src="http://we.zhongcaiguoke.com/{$row['img3']}" class="moment-edit-image-preview" alt="图片3">
                                                        <div class="moment-edit-image-actions">
                                                            <button type="button" class="moment-edit-image-btn btn-change" onclick="changeImage(3)">
                                                                <i class="fa fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="moment-edit-image-btn btn-remove" onclick="removeImage(3)">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        <php>} else {</php>
                                                        <div class="moment-edit-upload-icon">
                                                            <i class="fa fa-plus"></i>
                                                        </div>
                                                        <div class="moment-edit-upload-text">点击上传图片3</div>
                                                        <php>}</php>
                                                        <php>echo tpl_form_field_image('img3', $row['img3'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                                    </div>

                                                    <!-- 图片4 -->
                                                    <div class="moment-edit-image-upload" id="imageUpload4">
                                                        <php>if($row['img4']) {</php>
                                                        <img src="http://we.zhongcaiguoke.com/{$row['img4']}" class="moment-edit-image-preview" alt="图片4">
                                                        <div class="moment-edit-image-actions">
                                                            <button type="button" class="moment-edit-image-btn btn-change" onclick="changeImage(4)">
                                                                <i class="fa fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="moment-edit-image-btn btn-remove" onclick="removeImage(4)">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        <php>} else {</php>
                                                        <div class="moment-edit-upload-icon">
                                                            <i class="fa fa-plus"></i>
                                                        </div>
                                                        <div class="moment-edit-upload-text">点击上传图片4</div>
                                                        <php>}</php>
                                                        <php>echo tpl_form_field_image('img4', $row['img4'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                                    </div>

                                                    <!-- 图片5 -->
                                                    <div class="moment-edit-image-upload" id="imageUpload5">
                                                        <php>if($row['img5']) {</php>
                                                        <img src="http://we.zhongcaiguoke.com/{$row['img5']}" class="moment-edit-image-preview" alt="图片5">
                                                        <div class="moment-edit-image-actions">
                                                            <button type="button" class="moment-edit-image-btn btn-change" onclick="changeImage(5)">
                                                                <i class="fa fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="moment-edit-image-btn btn-remove" onclick="removeImage(5)">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        <php>} else {</php>
                                                        <div class="moment-edit-upload-icon">
                                                            <i class="fa fa-plus"></i>
                                                        </div>
                                                        <div class="moment-edit-upload-text">点击上传图片5</div>
                                                        <php>}</php>
                                                        <php>echo tpl_form_field_image('img5', $row['img5'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                                    </div>

                                                    <!-- 图片6 -->
                                                    <div class="moment-edit-image-upload" id="imageUpload6">
                                                        <php>if($row['img6']) {</php>
                                                        <img src="http://we.zhongcaiguoke.com/{$row['img6']}" class="moment-edit-image-preview" alt="图片6">
                                                        <div class="moment-edit-image-actions">
                                                            <button type="button" class="moment-edit-image-btn btn-change" onclick="changeImage(6)">
                                                                <i class="fa fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="moment-edit-image-btn btn-remove" onclick="removeImage(6)">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        <php>} else {</php>
                                                        <div class="moment-edit-upload-icon">
                                                            <i class="fa fa-plus"></i>
                                                        </div>
                                                        <div class="moment-edit-upload-text">点击上传图片6</div>
                                                        <php>}</php>
                                                        <php>echo tpl_form_field_image('img6', $row['img6'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                                    </div>

                                                    <!-- 图片7 -->
                                                    <div class="moment-edit-image-upload" id="imageUpload7">
                                                        <php>if($row['img7']) {</php>
                                                        <img src="http://we.zhongcaiguoke.com/{$row['img7']}" class="moment-edit-image-preview" alt="图片7">
                                                        <div class="moment-edit-image-actions">
                                                            <button type="button" class="moment-edit-image-btn btn-change" onclick="changeImage(7)">
                                                                <i class="fa fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="moment-edit-image-btn btn-remove" onclick="removeImage(7)">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        <php>} else {</php>
                                                        <div class="moment-edit-upload-icon">
                                                            <i class="fa fa-plus"></i>
                                                        </div>
                                                        <div class="moment-edit-upload-text">点击上传图片7</div>
                                                        <php>}</php>
                                                        <php>echo tpl_form_field_image('img7', $row['img7'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                                    </div>

                                                    <!-- 图片8 -->
                                                    <div class="moment-edit-image-upload" id="imageUpload8">
                                                        <php>if($row['img8']) {</php>
                                                        <img src="http://we.zhongcaiguoke.com/{$row['img8']}" class="moment-edit-image-preview" alt="图片8">
                                                        <div class="moment-edit-image-actions">
                                                            <button type="button" class="moment-edit-image-btn btn-change" onclick="changeImage(8)">
                                                                <i class="fa fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="moment-edit-image-btn btn-remove" onclick="removeImage(8)">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        <php>} else {</php>
                                                        <div class="moment-edit-upload-icon">
                                                            <i class="fa fa-plus"></i>
                                                        </div>
                                                        <div class="moment-edit-upload-text">点击上传图片8</div>
                                                        <php>}</php>
                                                        <php>echo tpl_form_field_image('img8', $row['img8'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                                    </div>

                                                    <!-- 图片9 -->
                                                    <div class="moment-edit-image-upload" id="imageUpload9">
                                                        <php>if($row['img9']) {</php>
                                                        <img src="http://we.zhongcaiguoke.com/{$row['img9']}" class="moment-edit-image-preview" alt="图片9">
                                                        <div class="moment-edit-image-actions">
                                                            <button type="button" class="moment-edit-image-btn btn-change" onclick="changeImage(9)">
                                                                <i class="fa fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="moment-edit-image-btn btn-remove" onclick="removeImage(9)">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        <php>} else {</php>
                                                        <div class="moment-edit-upload-icon">
                                                            <i class="fa fa-plus"></i>
                                                        </div>
                                                        <div class="moment-edit-upload-text">点击上传图片9</div>
                                                        <php>}</php>
                                                        <php>echo tpl_form_field_image('img9', $row['img9'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11]]);</php>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 操作按钮 -->
                                        <div class="moment-edit-form-actions">
                                            <button type="submit" class="moment-edit-action-btn btn-primary" id="saveBtn">
                                                <i class="fa fa-save"></i>
                                                <span><php>echo isset($row['id']) && $row['id'] > 0 ? '保存修改' : '添加素材';</php></span>
                                            </button>
                                            <a href="{:U('Moment/index')}" class="moment-edit-action-btn btn-secondary">
                                                <i class="fa fa-arrow-left"></i>
                                                <span>返回列表</span>
                                            </a>
                                        </div>
                                        <php>if(isset($row['id']) && $row['id'] > 0) {</php><input type="hidden" name="id" value="{$row['id']}"><php>}</php>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧预览区域 -->
                        <div class="moment-edit-fade-in-delay-2">
                            <div class="moment-edit-preview-card">
                                <div class="moment-edit-preview-header">
                                    <div class="moment-edit-preview-icon">
                                        <i class="fa fa-mobile"></i>
                                    </div>
                                    <h3 class="moment-edit-preview-title">朋友圈预览</h3>
                                </div>
                                <div class="moment-edit-preview-body">
                                    <div class="moment-edit-phone-container" id="phone-container">
                                        <!-- 手机状态栏 -->
                                        <div class="status-bar notch-design" style="height: 32px; background-color: #000; padding: 0 16px; display: flex; justify-content: space-between; align-items: center; border-top-left-radius: 18px; border-top-right-radius: 18px; position: relative;">
                                            <!-- 刘海屏设计 -->
                                            <div style="position: absolute; top: 0; left: 50%; transform: translateX(-50%); width: 120px; height: 28px; background-color: #000; border-bottom-left-radius: 14px; border-bottom-right-radius: 14px; display: flex; justify-content: center; align-items: center;">
                                                <div style="width: 8px; height: 8px; background-color: #333; border-radius: 50%; margin-right: 30px;"></div> <!-- 前置摄像头 -->
                                                <div style="width: 50px; height: 4px; background-color: #333; border-radius: 2px;"></div> <!-- 听筒 -->
                                            </div>
                                            <span style="color: #fff; font-size: 14px; margin-left: 8px;">10:30</span>
                                            <div style="display: flex; align-items: center; margin-right: 8px;">
                                                <span style="color: #fff; font-size: 12px; margin-right: 8px;">4G</span>
                                                <div style="width: 25px; height: 12px; border: 1px solid #fff; border-radius: 3px; position: relative; display: flex; align-items: center;">
                                                    <div style="height: 8px; width: 20px; background-color: #fff; margin-left: 2px; border-radius: 1px;"></div>
                                                    <div style="height: 8px; width: 2px; background-color: #fff; position: absolute; right: -3px; border-radius: 1px;"></div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 朋友圈导航栏 -->
                                        <div class="nav-bar" style="height: 48px; background-color: #fff; display: flex; justify-content: center; align-items: center; position: relative; border-bottom: 1px solid #eee;">
                                            <div style="position: absolute; left: 16px; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="15 18 9 12 15 6"></polyline>
                                                </svg>
                                            </div>
                                            <div style="font-size: 18px; font-weight: 500;">朋友圈</div>
                                            <div style="position: absolute; right: 16px; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <circle cx="12" cy="10" r="3"></circle>
                                                    <path d="M7 20.662V19c0-1 .1-1.9 1-2h8c.9.1 1 1 1 2v1.662"></path>
                                                </svg>
                                            </div>
                                        </div>

                                        <!-- 朋友圈内容预览 -->
                                        <div id="moment-preview" class="moment-preview">
                                            <div class="moment-item">
                                                <div class="header">
                                                    <img src="/static/stations/images/logozcgk.png" class="avatar">
                                                    <div class="author" id="preview-project">中才国科</div>
                                                </div>
                                                <div class="content" id="preview-content">在这里输入内容后将显示预览效果</div>
                                                <div class="images" id="preview-images"></div>
                                                <div class="footer">
                                                    <div class="time">刚刚</div>
                                                    <div class="actions">
                                                        <a href="javascript:void(0);" class="action-btn">赞</a>
                                                        <a href="javascript:void(0);" class="action-btn">评论</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 引入朋友圈预览相关样式 -->
            <style>
                /* 朋友圈样式 */
                .moment-preview {
                    background-color: #f2f2f2;
                    font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
                    height: 100%;
                    overflow-y: auto;
                }
                
                .moment-item {
                    background-color: #fff;
                    margin: 12px 0;
                    padding: 16px 16px 14px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
                }
                .moment-item .header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                }
                .moment-item .avatar {
                    width: 42px;
                    height: 42px;
                    border-radius: 4px;
                    margin-right: 10px;
                    background-color: #f6f6f6;
                    overflow: hidden;
                }
                .moment-item .author {
                    font-weight: 550;
                    font-size: 15px;
                    color: #576b95;
                }
                .moment-item .content {
                    font-size: 15px;
                    line-height: 1.5;
                    margin: 10px 0;
                    color: #333;
                    word-wrap: break-word;
                    padding: 2px 0;
                    letter-spacing: 0;
                    padding-left: 52px; /* 与头像左侧对齐：头像宽度42px + 右边距10px */
                }
                /* 段落样式 */
                .moment-item .content p {
                    margin: 0 0 0.8em 0;
                    padding: 0;
                }
                /* 最后一个段落不需要底部边距 */
                .moment-item .content p:last-child {
                    margin-bottom: 0;
                }
                /* 特殊标记样式 */
                .moment-item .content .emoji {
                    display: inline-block;
                    vertical-align: -0.1em;
                    width: 1.2em;
                    height: 1.2em;
                    margin: 0 0.1em;
                }
                /* 表情符号样式 */
                .moment-item .content .checkmark {
                    color: #07c160;
                    font-weight: bold;
                }
                .moment-item .footer {
                    display: flex;
                    justify-content: space-between;
                    color: #999;
                    font-size: 13px;
                    margin-top: 12px;
                    padding-top: 10px;
                    border-top: 1px solid #f1f1f1;
                }
                .moment-item .time {
                    color: #b2b2b2;
                    font-size: 14px;
                }
                .moment-item .actions {
                    display: flex;
                }
                .moment-item .action-btn {
                    color: #576b95;
                    margin-left: 15px;
                    text-decoration: none;
                    font-size: 14px;
                }
                /* 图片样式 */
                .moment-item .images {
                    display: flex;
                    flex-wrap: wrap;
                    margin-top: 10px;
                    padding-left: 52px; /* 与头像左侧对齐 */
                }
                /* 单张图片样式 */
                .moment-item .images.single-image .img-box {
                    width: 66%;
                    max-width: 200px;
                    position: relative;
                    margin-right: 0;
                    margin-bottom: 0;
                    padding-top: 66%;
                }
                /* 两张图片样式 */
                .moment-item .images.two-images .img-box {
                    width: 48%;
                    margin-right: 4%;
                    position: relative;
                    margin-bottom: 0;
                    padding-top: 48%;
                }
                .moment-item .images.two-images .img-box:nth-child(2n) {
                    margin-right: 0;
                }
                /* 三张及以上图片样式 */
                .moment-item .img-box {
                    width: 32%;
                    margin-right: 2%;
                    margin-bottom: 6px;
                    position: relative;
                    padding-top: 32%; /* 保持宽高比 */
                }
                .moment-item .img-box:nth-child(3n) {
                    margin-right: 0;
                }
                .moment-item .img-box img {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 3px;
                }
                /* 全文链接样式优化 */
                .read-more {
                    color: #576b95;
                    margin: 0;
                    display: block;
                    font-size: 15px;
                    line-height: 1.4;
                    padding: 8px 0;
                    font-weight: 400;
                    margin-left: 52px; /* 与头像左侧对齐 */
                }
                /* 无换行长文本缩略样式 */
                .content-truncated {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 100%;
                }
                /* 多行文本缩略样式 */
                .content-truncated-multi {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 5;
                    -webkit-box-orient: vertical;
                    max-height: calc(1.5em * 5);
                }
                
                /* 图片查看大图功能样式 */
                .img-preview-wrapper {
                    position: relative;
                    display: inline-block;
                    overflow: hidden;
                    cursor: pointer;
                }
                
                .img-zoom-icon {
                    position: absolute;
                    top: 0;
                    right: 0;
                    background-color: rgba(0, 0, 0, 0.5);
                    color: white;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 0 0 0 4px;
                    opacity: 0.7;
                    transition: opacity 0.3s;
                }
                
                .img-preview-wrapper:hover .img-zoom-icon {
                    opacity: 1;
                }
                
                /* 大图预览模态框样式 */
                .img-preview-modal {
                    display: none;
                    position: fixed;
                    z-index: 9999;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.85);
                    padding: 50px;
                    box-sizing: border-box;
                    align-items: center;
                    justify-content: center;
                }
                
                .img-preview-modal-content {
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
                }
                
                .img-preview-close {
                    position: absolute;
                    top: 20px;
                    right: 25px;
                    color: #f1f1f1;
                    font-size: 28px;
                    font-weight: bold;
                    cursor: pointer;
                }
                
                .img-preview-close:hover {
                    color: #bbb;
                }
            </style>
            
            <script>
            // 全局变量
            var project_id = "{:$row['project_id'] ?: 0}";
            var post_id = "{:$row['post_id'] ?: 0}";
            
            // 更新项目名称预览
            function updateProjectName() {
                var projectSelect = document.getElementById('project');
                if(!projectSelect) return;
                
                var selectedProject = projectSelect.options[projectSelect.selectedIndex].text;
                if (selectedProject && selectedProject !== '请选择项目') {
                    document.getElementById('preview-project').innerText = selectedProject;
                } else {
                    document.getElementById('preview-project').innerText = '中才国科';
                }
            }
            
            // 更新内容预览
            function updateContentPreview() {
                var textarea = document.querySelector('textarea[name="content"]');
                if(!textarea) return;
                
                var content = textarea.value;
                if (!content) {
                    content = '在这里输入内容后将显示预览效果';
                }
                
                // 处理换行和段落
                content = content.replace(/\n\n+/g, '</p><p>');
                content = content.replace(/\n/g, '<br>');
                content = '<p>' + content + '</p>';
                
                // 处理emoji
                var emojiMap = {
                    '✅': '<span class="checkmark">✓</span>',
                    '👉': '<span class="emoji">👉</span>',
                    '🔥': '<span class="emoji">🔥</span>',
                    '⭐': '<span class="emoji">⭐</span>',
                    '📢': '<span class="emoji">📢</span>',
                    '💰': '<span class="emoji">💰</span>',
                    '🌟': '<span class="emoji">🌟</span>'
                };
                
                for (var emoji in emojiMap) {
                    if (emojiMap.hasOwnProperty(emoji)) {
                        content = content.replace(new RegExp(emoji, 'g'), emojiMap[emoji]);
                    }
                }
                
                // 更新预览内容
                var previewContent = document.getElementById('preview-content');
                if(previewContent) {
                    previewContent.innerHTML = content;
                
                // 应用缩略效果
                handleContentFormat();
                }
            }
            
            // 处理内容格式和缩略
            function handleContentFormat() {
                var content = document.getElementById('preview-content');
                if(!content) return;
                
                // 移除旧的"全文"链接
                var oldReadMore = content.nextElementSibling;
                if (oldReadMore && oldReadMore.className === 'read-more') {
                    oldReadMore.remove();
                }
                
                // 移除旧的样式
                content.classList.remove('content-truncated');
                content.classList.remove('content-truncated-multi');
                content.style.maxHeight = '';
                
                // 检查内容长度和格式
                var textContent = content.textContent || content.innerText;
                var textLength = textContent.length;
                var hasParagraphs = content.querySelectorAll('p').length > 1;
                var hasLineBreaks = content.innerHTML.includes('<br');
                
                // 获取实际行数
                var lineHeight = parseInt(window.getComputedStyle(content).lineHeight) || 24;
                var contentHeight = content.offsetHeight;
                var lines = Math.ceil(contentHeight / lineHeight);
                
                // 创建"全文"链接
                var readMoreLink = document.createElement('a');
                readMoreLink.href = 'javascript:void(0);';
                readMoreLink.className = 'read-more';
                readMoreLink.textContent = '全文';
                
                // 优先处理多行内容 - 超过5行缩略
                if (lines > 5 || (hasParagraphs && content.querySelectorAll('p').length > 3)) {
                    content.classList.add('content-truncated-multi');
                    content.parentNode.insertBefore(readMoreLink, content.nextSibling);
                    
                    readMoreLink.onclick = function() {
                        if (this.textContent === '全文') {
                            content.classList.remove('content-truncated-multi');
                            this.textContent = '收起';
                        } else {
                            content.classList.add('content-truncated-multi');
                            this.textContent = '全文';
                            // 滚动到内容顶部
                            content.scrollIntoView({behavior: 'smooth', block: 'nearest'});
                        }
                    };
                }
                // 情况2: 无换行且长文本 - 单行缩略
                else if (textLength > 99 && !hasParagraphs && !hasLineBreaks) {
                    content.classList.add('content-truncated');
                    content.parentNode.insertBefore(readMoreLink, content.nextSibling);
                    
                    readMoreLink.onclick = function() {
                        if (this.textContent === '全文') {
                            content.classList.remove('content-truncated');
                            this.textContent = '收起';
                        } else {
                            content.classList.add('content-truncated');
                            this.textContent = '全文';
                        }
                    };
                }
            }
            
            // 更新图片预览
            function updateImagesPreview() {
                try {
                var previewImagesContainer = document.getElementById('preview-images');
                    if (!previewImagesContainer) return;
                    
                previewImagesContainer.innerHTML = '';
                
                    // 收集所有有值的图片 - 使用原生DOM API
                var images = [];
                for (var i = 1; i <= 9; i++) {
                    var imgInput = document.querySelector('input[name="img' + i + '"]');
                    if (imgInput && imgInput.value) {
                        images.push(imgInput.value);
                    }
                }
                
                // 根据图片数量添加样式
                if (images.length === 1) {
                    previewImagesContainer.className = 'images single-image';
                } else if (images.length === 2) {
                    previewImagesContainer.className = 'images two-images';
                } else {
                    previewImagesContainer.className = 'images';
                }
                
                // 创建图片预览
                for (var j = 0; j < images.length; j++) {
                    var imgBox = document.createElement('div');
                    imgBox.className = 'img-box';
                    
                    var img = document.createElement('img');
                    
                    // 使用完整URL
                    if (images[j].indexOf('http') === 0) {
                        img.src = images[j];
                    } else {
                        img.src = 'http://we.zhongcaiguoke.com/' + images[j];
                    }
                        
                        // 错误处理 - 如果图片加载失败，显示默认图
                        img.onerror = function() {
                            this.src = '/static/stations/images/logozcgk.png';
                            this.onerror = null; // 防止无限循环
                        };
                    
                    imgBox.appendChild(img);
                    previewImagesContainer.appendChild(imgBox);
                    }
                } catch (e) {
                    console.error('更新图片预览失败:', e);
                }
            }

            // 直接解决IllegalInvocation问题的最终修复方案
            (function(win) {
                // 原始的DOM事件处理方式替代jQuery的ready
                win.addEventListener('DOMContentLoaded', function() {
                    // 使用setTimeout避免竞态条件
                    setTimeout(function() {
                        try {
                            // 如果页面中有input[name^="img"]元素，则设置更新预览
                            var imgInputs = document.querySelectorAll('input[name^="img"]');
                            if (imgInputs.length > 0) {
                                updateImagesPreview();
                                
                                // 为每个图片输入框添加原生change事件
                                imgInputs.forEach(function(input) {
                                    input.addEventListener('change', function() {
                                        setTimeout(updateImagesPreview, 100);
                                    });
                                });
                            }
                            
                            // 内容预览更新
                            var contentTextarea = document.querySelector('textarea[name="content"]');
                            if (contentTextarea) {
                                updateContentPreview();
                                
                                contentTextarea.addEventListener('input', function() {
                                    updateContentPreview();
                                });
                            }
                            
                            // 项目变化
                            var projectSelect = document.getElementById('project');
                            if (projectSelect) {
                                updateProjectName();
                                
                                // 如果已经有选定的项目，初始化岗位下拉框
                                if (project_id > 0) {
                                    var xhr = new XMLHttpRequest();
                                    xhr.open('GET', '{:U("getPosts")}?project_id=' + encodeURIComponent(project_id), true);
                                    xhr.onload = function() {
                                        if (xhr.status === 200) {
                                            try {
                                                var res = JSON.parse(xhr.responseText);
                                                var html = '<option value="">请选择岗位</option>';
                                                res.forEach(function(v) {
                                                    if (post_id == v.id) {
                                                        html += '<option value="'+v.id+'" selected>'+v.job_name+'</option>';
                                                    } else {
                                                        html += '<option value="'+v.id+'">'+v.job_name+'</option>';
                                                    }
                                                });
                                                document.getElementById('post').innerHTML = html;
                                            } catch (e) {
                                                console.error('解析岗位数据失败', e);
                                            }
                                        }
                                    };
                                    xhr.send();
                                }
                                
                                projectSelect.addEventListener('change', function() {
                                    updateProjectName();
                                    
                                    // 发送AJAX请求获取关联岗位
                                    var pid = this.value;
                                    var xhr = new XMLHttpRequest();
                                    xhr.open('GET', '{:U("getPosts")}?project_id=' + encodeURIComponent(pid), true);
                                    xhr.onload = function() {
                                        if (xhr.status === 200) {
                                            try {
                                                var res = JSON.parse(xhr.responseText);
                                                var html = '<option value="">请选择岗位</option>';
                                                res.forEach(function(v) {
                                                    html += '<option value="'+v.id+'">'+v.job_name+'</option>';
                                                });
                                                document.getElementById('post').innerHTML = html;
                                                
                                                updateProjectName();
                                            } catch (e) {
                                                console.error('解析岗位数据失败', e);
                                            }
                                        }
                                    };
                                    xhr.send();
                                });
                            }
                            
                            // 定期更新预览
                            var previewInterval = setInterval(function() {
                                try {
                                    updateImagesPreview();
                                } catch (e) {
                                    console.error('自动更新预览失败', e);
                                    clearInterval(previewInterval);
                                }
                            }, 5000);
                            
                            // 表单提交处理
                            var form = document.getElementById('form1');
                            if (form) {
                                form.addEventListener('submit', function(e) {
                                    // 检查是否有正在处理的上传
                                    if (window.pendingUploads && Object.keys(window.pendingUploads).length > 0) {
                                        e.preventDefault();
                                        alert('图片正在处理中，请稍候再提交');
                                        return false;
                                    }
                                });
                            }
                        } catch (e) {
                            console.error('初始化界面失败:', e);
                        }
                    }, 10);
                });
            })(window);
            </script>
            
            <script>
            // 全局错误处理器 - 在主脚本之前运行
            (function() {
                // 保存原始错误处理函数
                var originalOnError = window.onerror;
                
                // 安装全局错误处理器
                window.onerror = function(message, source, lineno, colno, error) {
                    // 记录详细错误信息
                    console.error('JS错误:', message);
                    console.error('位置:', source, '行:', lineno, '列:', colno);
                    if (error && error.stack) {
                        console.error('堆栈:', error.stack);
                    }
                    
                    // 如果是Illegal invocation错误，尝试修复
                    if (message && message.indexOf('Illegal invocation') !== -1) {
                        console.warn('检测到Illegal invocation错误，正在尝试修复...');
                        
                        // 可以在这里注入修复代码
                        try {
                            var jQueryFunctions = ['ready', 'html', 'text', 'val', 'show', 'hide'];
                            if (window.jQuery) {
                                jQueryFunctions.forEach(function(funcName) {
                                    if (jQuery.fn[funcName]) {
                                        var origFunc = jQuery.fn[funcName];
                                        jQuery.fn[funcName] = function() {
                                            try {
                                                return origFunc.apply(this, arguments);
                                            } catch (e) {
                                                console.warn('jQuery函数执行失败，降级处理:', funcName);
                                                return this;
                                            }
                                        };
                                    }
                                });
                            }
                        } catch (fixError) {
                            console.error('修复尝试失败:', fixError);
                        }
                    }
                    
                    // 如果有原始错误处理器，调用它
                    if (typeof originalOnError === 'function') {
                        return originalOnError.apply(this, arguments);
                    }
                    
                    // 返回false表示错误未处理，允许默认处理器继续执行
                    return false;
                };
                
                // 确保DOM Ready事件在jQuery加载后执行
                window.addEventListener('DOMContentLoaded', function() {
                    // 使用setTimeout确保jQuery已完全加载
                    setTimeout(function() {
                        if (window.jQuery) {
                            // 完全替换jQuery的ready方法
                            jQuery.fn.ready = function(fn) {
                                // 使用原生DOM方法
                                if (document.readyState === 'complete') {
                                    setTimeout(function() { fn.call(document); }, 1);
                                } else {
                                    document.addEventListener('DOMContentLoaded', function() {
                                        fn.call(document);
                                    });
                                }
                                return this;
                            };
                        }
                    }, 0);
                });
            })();
            </script>

            <!-- 单独引入imageCompressor工具 -->
            <script>
            // 等待页面加载完成后，使用原生DOM方法添加图片压缩功能
            window.addEventListener('DOMContentLoaded', function() {
                // 确保完成所有DOM加载后再执行
                setTimeout(function() {
                    // 加载依赖模块
                    require(['util', 'imageCompressor'], function(util, imageCompressor) {
                        // 使用原生JavaScript，避免jQuery相关问题
                        var MAX_IMAGE_SIZE = 100 * 1024;
                        var compressedResults = {};
                        window.pendingUploads = {};
                        
                        // 设置图片压缩处理
                        function setupImageCompression() {
                            var fileInputs = document.querySelectorAll('input[type="file"]');
                            
                            fileInputs.forEach(function(input) {
                                // 防止重复设置
                                if (input.getAttribute('data-compression-setup')) return;
                                input.setAttribute('data-compression-setup', 'true');
                                
                                // 获取原始onchange属性
                                var originalOnChange = input.getAttribute('onchange');
                                input.removeAttribute('onchange');
                                
                                // 添加新的事件处理器
                                input.addEventListener('change', function(e) {
                            var file = this.files[0];
                            if (!file) return;
                            
                            // 检查文件类型是否为图片
                            if (!file.type.match(/^image\//)) {
                                if (originalOnChange) {
                                            // 使用Function构造器创建一个安全的函数
                                            try {
                                                var func = new Function('event', 'this', originalOnChange);
                                                func.call(this, e, this);
                                            } catch(err) {
                                                console.error('执行原始onchange失败:', err);
                                            }
                                }
                                return;
                            }
                            
                            // 标记处理中状态
                            var uploadId = 'upload-' + Date.now();
                                    window.pendingUploads[uploadId] = true;
                                    
                                    var self = this;
                                    var inputName = this.getAttribute('name');
                            
                            // 压缩图片
                            imageCompressor.compressImage(file, MAX_IMAGE_SIZE, function(result) {
                                compressedResults[inputName] = result;
                                
                                // 清除处理状态
                                delete window.pendingUploads[uploadId];
                                
                                // 如果图片被压缩，显示压缩前后对比
                                if (result.compressed) {
                                    setTimeout(function() {
                                        imageCompressor.showComparisonModal(result);
                                    }, 100);
                                    
                                    try {
                                        // 替换文件输入框中的文件
                                        var dataTransfer = new DataTransfer();
                                        dataTransfer.items.add(result.file);
                                        self.files = dataTransfer.files;
                                    } catch (err) {
                                        console.error('替换文件失败:', err);
                                    }
                                }
                                
                                // 安全执行原始的onchange
                                setTimeout(function() {
                                    if (originalOnChange) {
                                        try {
                                            var func = new Function('event', 'this', originalOnChange);
                                            func.call(self, {type: 'change'}, self);
                                        } catch (err) {
                                            console.error('执行原始onchange失败:', err);
                                            
                                            // 强制清理所有模态框，避免界面卡住
                                            $('.modal').modal('hide');
                                            $('.modal-backdrop').remove();
                                            $('body').removeClass('modal-open').css('padding-right', '');
                                        }
                                    }
                                }, 200);
                            });
                                });
                    });
                }
                
                        // 初始设置
                    setupImageCompression();
                        
                        // 监视DOM变化，处理新添加的文件输入框
                        var observer = new MutationObserver(function(mutations) {
                            var hasNewFileInputs = false;
                        
                        mutations.forEach(function(mutation) {
                            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                                    for (var i = 0; i < mutation.addedNodes.length; i++) {
                                        var node = mutation.addedNodes[i];
                                        if (node.nodeType !== 1) continue; // 只处理元素节点
                                        
                                        if (node.tagName === 'INPUT' && node.type === 'file') {
                                            hasNewFileInputs = true;
                                            break;
                                        }
                                        
                                        if (node.querySelector && node.querySelector('input[type="file"]')) {
                                            hasNewFileInputs = true;
                                            break;
                                        }
                                        }
                                    }
                                });
                            
                            if (hasNewFileInputs) {
                                setTimeout(setupImageCompression, 50);
                        }
                    });
                    
                    // 观察整个文档的变化
                    observer.observe(document.body, { 
                        childList: true, 
                        subtree: true 
                    });
                    
                        // 处理表单提交
                        document.querySelectorAll('form').forEach(function(form) {
                            form.addEventListener('submit', function(e) {
                                if (Object.keys(window.pendingUploads).length > 0) {
                            e.preventDefault();
                            util.message('图片正在处理中，请稍候再提交', '', 'info');
                            return false;
                        }
                    });
                        });
                    });
                }, 100);
            });
            </script>
            
            <!-- 添加图片查看大图功能 -->
            <script>
            $(function() {
                // 创建模态框
                if (!document.getElementById('imgPreviewModal')) {
                    var modal = document.createElement('div');
                    modal.id = 'imgPreviewModal';
                    modal.className = 'img-preview-modal';
                    modal.innerHTML = `
                        <span class="img-preview-close">&times;</span>
                        <img class="img-preview-modal-content" id="imgPreviewModalContent">
                    `;
                    document.body.appendChild(modal);
                    
                    // 添加关闭事件
                    document.querySelector('.img-preview-close').addEventListener('click', function() {
                        modal.style.display = 'none';
                    });
                    
                    modal.addEventListener('click', function(e) {
                        if (e.target === modal) {
                            modal.style.display = 'none';
                        }
                    });
                }
                
                // 为所有图片添加放大图标和点击事件
                function setupImageZoom() {
                    // 选择所有缩略图（表单中的图片预览）
                    var thumbnails = document.querySelectorAll('.img-responsive.img-thumbnail');
                    
                    thumbnails.forEach(function(img) {
                        var parent = img.parentNode;
                        
                        // 避免重复添加
                        if (parent.classList.contains('img-preview-wrapper')) {
                            return;
                        }
                        
                        // 创建包装容器并添加放大图标
                        var wrapper = document.createElement('div');
                        wrapper.className = 'img-preview-wrapper';
                        parent.insertBefore(wrapper, img);
                        wrapper.appendChild(img);
                        
                        var zoomIcon = document.createElement('div');
                        zoomIcon.className = 'img-zoom-icon';
                        zoomIcon.innerHTML = '<i class="fa fa-search-plus"></i>';
                        wrapper.appendChild(zoomIcon);
                        
                        // 添加点击事件显示大图
                        wrapper.addEventListener('click', function() {
                            var modal = document.getElementById('imgPreviewModal');
                            var modalImg = document.getElementById('imgPreviewModalContent');
                            modal.style.display = 'flex';
                            modalImg.src = img.src;
                        });
                    });
                }
                
                // 初始调用
                setupImageZoom();
                
                // 监听DOM变化，处理动态添加的图片
                var observer = new MutationObserver(function(mutations) {
                    var needsRefresh = false;
                    
                    mutations.forEach(function(mutation) {
                        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                            for (var i = 0; i < mutation.addedNodes.length; i++) {
                                var node = mutation.addedNodes[i];
                                if (node.nodeType !== 1) continue; // 只处理元素节点
                                
                                if (node.classList && node.classList.contains('img-thumbnail')) {
                                    needsRefresh = true;
                                    break;
                                }
                                
                                if (node.querySelector && node.querySelector('.img-thumbnail')) {
                                    needsRefresh = true;
                                    break;
                                }
                            }
                        }
                    });
                    
                    if (needsRefresh) {
                        setTimeout(setupImageZoom, 50);
                    }
                });
                
                // 观察整个文档的变化
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            });
            </script>
        </div>
    </div>
</div>
<include file="block/footer" />

<script>
    $(document).ready(function() {
        // 项目变化时加载对应岗位
        $('#project').change(function(){
            var pid = $(this).val();
            if(pid) {
                $.get('{:U("getPosts")}', {project_id:pid}, function(res){
                    var html = '<option value="">选择岗位</option>';
                    $.each(res, function(k,v){
                        html += '<option value="'+v.id+'">'+v.job_name+'</option>';
                    });
                    $('#post').html(html);
                    updatePreview(); // 更新预览
                });
            } else {
                $('#post').html('<option value="">选择岗位</option>');
                updatePreview(); // 更新预览
            }
        });

        // 页面加载时，如果已选择项目，加载对应岗位
        var project_id = '{$row.project_id}';
        if(project_id) {
            $.get('{:U("getPosts")}', {project_id:project_id}, function(res){
                var html = '<option value="">选择岗位</option>';
                var post_id = '{$row.post_id}';
                $.each(res, function(k,v){
                    if(post_id == v.id) {
                        html += '<option value="'+v.id+'" selected>'+v.job_name+'</option>';
                    } else {
                        html += '<option value="'+v.id+'">'+v.job_name+'</option>';
                    }
                });
                $('#post').html(html);
                updatePreview(); // 更新预览
            });
        }

        // 实时预览功能
        function updatePreview() {
            var content = $('#content').val();
            var project = $('#project option:selected').text();

            // 更新文本内容
            $('#preview-content').text(content || '在这里输入内容后将显示预览效果');

            // 更新项目名称
            if(project && project !== '请选择项目') {
                $('#preview-project').text(project);
            } else {
                $('#preview-project').text('中才国科');
            }

            // 更新图片预览
            updateImagePreview();

            // 更新字符计数
            updateCharacterCount();
        }

        // 更新图片预览
        function updateImagePreview() {
            var imagesHtml = '';
            for(var i = 1; i <= 9; i++) {
                var imgInput = $('input[name="img' + i + '"]');
                if(imgInput.length && imgInput.val()) {
                    var imgSrc = 'http://we.zhongcaiguoke.com/' + imgInput.val();
                    imagesHtml += '<img src="' + imgSrc + '" class="preview-image">';
                }
            }
            $('#preview-images').html(imagesHtml);
        }

        // 更新字符计数
        function updateCharacterCount() {
            var content = $('#content').val();
            var length = content.length;
            $('#contentLength').text(length);

            // 根据字符数量改变颜色
            var $counter = $('#contentLength').parent();
            if (length > 200) {
                $counter.css('color', '#ef4444');
            } else if (length > 150) {
                $counter.css('color', '#f59e0b');
            } else {
                $counter.css('color', '#6b7280');
            }
        }

        // 绑定事件
        $('#content, #project, #post').on('input change', updatePreview);

        // 页面加载时初始化预览
        updatePreview();

        // 监听图片上传变化
        $('input[type="file"]').on('change', function() {
            setTimeout(updateImagePreview, 1000); // 延迟更新，等待上传完成
        });

        // 表单提交增强
        $('#momentForm').on('submit', function(e) {
            var $form = $(this);
            var $submitBtn = $('#saveBtn');

            // 验证必填字段
            var project = $('#project').val();
            var post = $('#post').val();
            var content = $('#content').val().trim();

            if (!project) {
                e.preventDefault();
                alert('请选择关联项目');
                $('#project').focus();
                return false;
            }

            if (!post) {
                e.preventDefault();
                alert('请选择关联岗位');
                $('#post').focus();
                return false;
            }

            if (!content) {
                e.preventDefault();
                alert('请输入朋友圈文本内容');
                $('#content').focus();
                return false;
            }

            if (content.length > 200) {
                e.preventDefault();
                alert('文本内容不能超过200个字符，当前：' + content.length + ' 字符');
                $('#content').focus();
                return false;
            }

            // 显示提交状态
            $submitBtn.prop('disabled', true);
            var originalHtml = $submitBtn.html();
            $submitBtn.html('<i class="fa fa-spinner fa-spin"></i> <span>保存中...</span>');

            // 显示加载提示
            if (typeof layer !== 'undefined') {
                layer.msg('正在保存素材...', {icon: 16, time: 0});
            }

            // 如果10秒后还没有响应，恢复按钮状态
            setTimeout(function() {
                if ($submitBtn.prop('disabled')) {
                    $submitBtn.prop('disabled', false).html(originalHtml);
                    if (typeof layer !== 'undefined') {
                        layer.closeAll();
                        layer.msg('保存超时，请重试', {icon: 2});
                    }
                }
            }, 10000);

            return true;
        });

        // 键盘快捷键
        $(document).on('keydown', function(e) {
            // Ctrl + S 保存
            if (e.ctrlKey && e.keyCode === 83) {
                e.preventDefault();
                $('#momentForm').submit();
            }
            // ESC 返回列表
            else if (e.keyCode === 27) {
                if (confirm('确定要返回列表吗？未保存的更改将丢失。')) {
                    window.location.href = "{:U('Moment/index')}";
                }
            }
        });

        // 页面离开确认
        var formChanged = false;
        $('#momentForm input, #momentForm textarea, #momentForm select').on('change input', function() {
            formChanged = true;
        });

        $(window).on('beforeunload', function(e) {
            if (formChanged) {
                var message = '您有未保存的素材信息，确定要离开吗？';
                e.returnValue = message;
                return message;
            }
        });

        $('#momentForm').on('submit', function() {
            formChanged = false;
        });

        // 显示编辑提示
        setTimeout(function() {
            if (typeof layer !== 'undefined') {
                layer.tips('使用 Ctrl+S 快速保存，ESC 返回列表', '#saveBtn', {
                    tips: [1, '#06b6d4'],
                    time: 3000
                });
            }
        }, 2000);
    });

    // 添加自定义样式增强
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .moment-edit-form-card,
            .moment-edit-preview-card {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .moment-edit-form-textarea::-webkit-scrollbar {
                width: 8px;
            }

            .moment-edit-form-textarea::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 4px;
            }

            .moment-edit-form-textarea::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 4px;
            }

            .moment-edit-form-textarea::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }

            .moment-edit-image-upload {
                cursor: pointer;
            }

            .moment-edit-image-upload.has-image {
                cursor: default;
            }

            .moment-edit-image-upload:not(.has-image):hover {
                border-color: #06b6d4;
                background: rgba(6, 182, 212, 0.05);
            }
        `)
        .appendTo('head');
</script>

<script>
require(['primeImageCompressor'], function(primeImageCompressor) {
    primeImageCompressor.init();
});
</script>
