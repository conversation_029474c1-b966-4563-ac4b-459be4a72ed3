<?php
/**
 * 合成图片
 */
function mergeImg1($bg_img, $url, $img, $nickname, $toPath = null)
{

    if (!$url) return false;
    vendor('phpqrcode.phpqrcode');
    ob_start();
    \QRcode::png($url, false, QR_ECLEVEL_L, 8.5, 1);
    $qrimg = ob_get_contents();
    ob_end_clean();

    $bkim_size = getimagesize($bg_img);
    $type = $bkim_size['mime'];
    $f_map = ['image/png' => ['from' => 'imagecreatefrompng', 'img' => 'imagepng'], 'image/jpeg' => ['from' => 'imagecreatefromjpeg', 'img' => 'imagejpeg']];
    $bkim = $f_map[$type]['from']($bg_img);
    $qrim = imagecreatefromstring($qrimg);
    $qrimx = imagesx($qrim);
    $qrimy = imagesy($qrim);
    if ($nickname) {
        $color = imagecolorallocate($bkim, 0, 0, 0);
        imagettftext($bkim, 40, 1, 170, 1520, $color, STATIC_PATH."font/huawenheiti.ttf", $nickname);
    }


    if ($img) {
        $curl_obj = curl_init();
        curl_setopt($curl_obj, CURLOPT_URL, $img);
        curl_setopt($curl_obj, CURLOPT_HEADER, 0);
        curl_setopt($curl_obj, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl_obj, CURLOPT_TIMEOUT, 15);
        $result = curl_exec($curl_obj);
        curl_close($curl_obj);

        $avtim = imagecreatefromstring($result);
        $avtimx = imagesx($avtim);
        $avtimy = imagesy($avtim);

        import('Util.easyphpthumbnail');
        $obj = new easyphpthumbnail();
//        $obj->Backgroundcolor = '#fff';
        $obj->Clipcorner = array(2,48,0,1,1,1,1);
        $obj->Maketransparent = array(1,1,'#fff',1);
        $obj->im = $avtim;
        $obj->Thumbsize = 70;
        $obj->Framecolor = '#fff';
        $obj->size = [0 => $avtimx, 1 => $avtimy];
        $obj->thumbmaker();
        $avtim = $obj->thumb;
        imagecopymerge($bkim, $avtim, 50, 1465, 0, 0, 70, 70,100);
    }

    if ($toPath) {
        $toPath = SITE_PATH.$toPath;
        $f_map[$type]['img']($bkim, $toPath);
        return $toPath;
    } else {
        $f_map[$type]['img']($bkim);
    }
}

function enHash($id)
{
    $r = $id * 499 % 900 + 100;
    $code = strrev(numIdEncode(intval($r . $id), 3));
    return $code;
}

function deHash($code)
{
    $id = substr(numIdDecode(strrev($code)), 3);
    return $id;
}