<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title>{:$row['title']}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="/static/postcontent/styles/main.css">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="/static/postcontent/js/jquery.min.js"></script>
</head>
<body>
    <header class="header-phone d-md-none">
        <a href="{:U('index/index')}" class="btn-back"></a>
        <h3>{:$row['title']}</h3>
    </header>
    
    <div style="margin:20px 5px;border: 1px solid #eee;">
        <div style="padding:15px;">
            {:htmlspecialchars_decode($row['content'])}
        </div>
    </div>
    

    
    <script src="/static/postcontent/js/main.js"></script>
    
    <script>
        var title = '{:$row["title"]}';
        var img = 'https://c.zhongcaiguoke.com/static/stations/images/logozcgk.png'; // 默认图片路径，可根据实际情况调整
        var desc = '{$description}'; // 使用内容前100个字符作为描述
        var url = window.location.href;
        
        wx.config({
            debug: false, // 关闭调试模式
            appId: '{$appid}',
            timestamp: {:$wxconf['timestamp']?:0},
            nonceStr: '{$wxconf.noncestr}',
            signature: '{$wxconf.signature}',
            jsApiList: [
                'updateAppMessageShareData', 
                'onMenuShareTimeline',
                'onMenuShareAppMessage'
            ] // 修复分享接口列表
        });
        
        wx.ready(function () {
            // 分享给朋友（新版）
            wx.updateAppMessageShareData({ 
                title: title,
                desc: desc,
                link: url,
                imgUrl: img,
                success: function () {
                    console.log('分享内容设置成功');
                }
            });
            
            // 分享到朋友圈（兼容旧版）
            wx.onMenuShareTimeline({
                title: title,
                link: url,
                imgUrl: img,
                success: function () {
                    console.log('分享到朋友圈设置成功');
                }
            });
            
            // 分享给朋友（兼容旧版）
            wx.onMenuShareAppMessage({
                title: title,
                desc: desc,
                link: url,
                imgUrl: img,
                type: '', 
                dataUrl: '',
                success: function () {
                    console.log('分享给朋友设置成功');
                }
            });
        });
        
        wx.error(function (res) {
            // 静默处理错误
        });
    </script>
</body>
</html> 