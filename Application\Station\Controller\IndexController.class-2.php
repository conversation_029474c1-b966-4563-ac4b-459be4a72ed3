<?php

namespace Station\Controller;

use Common\Controller\WController;
use Common\Controller\WstationController;
use Util\AliSms;
use Util\HwSms;

class IndexController extends WstationController
{

    public function _initialize()
    {
        if ( ACTION_NAME != 'jobcontent') { //注册绑定

            parent::_initialize();
            $this->login();
            $openid = session('openid2');
            $userRow = D("User")->where(['openid' => $openid, 'service_id' => 1])->find();
            if (!$userRow) {
                session(null);
                return $this->redirect(U('/index/index'));
                die;
            }
            $this->userRow = $userRow;

            //D("User")->where(['id' => $userRow['id']])->save(['last_active_time' => time()]);  //记录最后活动时间

            //if ($userRow['last_login_time'] == 0) {
            //    D("User")->where(['id' => $userRow['id']])->save(['last_login_time' => time()]);
            //}

            if ($this->userRow['is_service_station'] == 0 && (ACTION_NAME != 'logins' && ACTION_NAME != 'logincode')) { //注册绑定
                return $this->redirect(U('index/logins'));
                die;
            }

            if ($userRow['self_service_station_id'] > 0) {
                D("service_station")->where(['id' => $userRow['self_service_station_id']])->save(['lastactive_time' => time()]);
            }

            //公告管理
            $adList = D("PageAd")->where(['status' => 1])->order('id desc')->select();
            $this->assign('adList', $adList);
        }
    }


    /**
     * 岗位内容
     * @return void
     */
    public function postcontent()
    {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $obj = D("Project");
        $row = $obj->where(['id' => $id])->find();
        if (!$row) $this->error('当前岗位无法找到！！！！');
        $projectList = D("Project")->where(['status' => 1])->field('id,name,content')->select();
        if (!C('LOCAL_DEBUG')) {
            $this->assign('wxconf', getWxConfig(null, $this->conf));
            $this->assign('appid', $this->conf['WX_APPID']);
        }
        $this->assign('projectList', $projectList);
        $this->assign('row', $row);
        $this->display();
    }

    /**
     * 首页
     */
    public function index()
    {
        if ($this->userRow['is_service_station'] == 0) { //注册绑定
            return $this->redirect(U('index/logins'));
            die;
        } elseif ($this->userRow['is_service_station'] == 2) { //审核通过
            $type = I('get.type', 0);
            $kwd = I('get.kwd', '');
            $qualification = I('get.qualification', 0);
            $where = ['status' => 1];
            if ($qualification > 0) {
                if ($qualification == 4) {
                    $where['qualification'] = ['egt', 4];

                } else {
                    $where['qualification'] = $qualification;
                }
            }
            if ($type > 0) {
                $categoryWhere = ['category' => $type];
                if ($type == 7) {
                    $categoryWhere = ['category' => ['in', [3, 5, 7]]];
                }
                $projectArrId = D("Project")->where($categoryWhere)->getField('id', true);
                if ($projectArrId) {
                    $where['project_id'] = ['in', $projectArrId];
                } else {
                    $where['project_id'] = -1;
                }
            }
            if (!empty($kwd)) {
                $where['job_name'] = ['like', '%' . $kwd . '%'];
            }
            $obj = D("ProjectPost");
            $this->assign('categoryList', D("Project")->category);

            $count = $obj->where($where)->count();
            $page = $this->page($count, 100);
            $list = $obj->limit($page->firstRow . ',' . $page->listRows)
                ->where($where)
                ->order('is_top desc,id desc')
                ->select();
            $this->assign('qualificationList', $obj->qualification);
            $this->assign('sexList', $obj->sex);
            $this->assign('categoryList', D("Project")->category);

            if ($list) {
                $projectArrId = array_unique(array_column($list, 'project_id'));
                $projectList = D("Project")->where(['id' => ['in', $projectArrId]])->getField('id,name,category', true);
                $this->assign('projectList', $projectList);

                $idArr = array_column($list, 'id');
                $projectJoinIdentityList = D("ProjectJoinIdentity")->where(['project_id' => ['in', $projectArrId], 'project_post_id' => ['in', $idArr], 'project_identity_id' => 3])->select();
                $projectJoinIdentity = [];
                foreach ($projectJoinIdentityList as $projectJoinIdentityRow) {
                    $projectJoinIdentity[$projectJoinIdentityRow['project_post_id']] = $projectJoinIdentityRow['cost'];
                }
                $this->assign('projectJoinIdentity', $projectJoinIdentity);
            }
            $this->assign('list', $list);
            $n = I('get.n', 0);
            if ($page->Current_page > 1 || $n == 1) {
                $this->display('list-index');
                exit;
            }
            $this->assign("page", $page);
            $obj = D("ServiceStation");
            $serviceStationRow = D("ServiceStation")
                ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
                ->find();
            $this->assign('serviceStationRow', $serviceStationRow);
            $startTime = strtotime(date("Y-m-d 00:00:00", time()));
            $endTime = strtotime(date("Y-m-d 23:59:59", time()));
            //今日粉丝数量
            $dayServiceNum = D("User")->where([
                'service_id' => 2,
                'service_station_id' => $this->userRow['self_service_station_id'],
                'subscribe' => ['between', [$startTime, $endTime]]
            ])->count();
            $this->assign('dayServiceNum', $dayServiceNum ?: 0);
            //今日简历
            $jobCount = D("UserJob")
                ->where(['service_station_id' => $this->userRow['self_service_station_id']])
                ->where(['create_time' => ['between', [$startTime, $endTime]]])
                ->count();
            $this->assign('jobCount', $jobCount ?: 0);
            //所有简历
            $alljobCount = D("UserJob")
                ->where(['service_station_id' => $this->userRow['self_service_station_id']])
                ->count();
            $this->assign('alljobCount', $alljobCount ?: 0);



            //下级服务站数量
            $refStationCount = D("ServiceStation")->where([
                'pid' => $this->userRow['self_service_station_id'],
                'status' => 1,
            ])->count();
            $this->assign('refStationCount', $refStationCount ?: 0);
            $this->assign('levelList', $obj->level);
            $this->assign('userRow', $this->userRow);
            $this->display();
        } elseif ($this->userRow['is_service_station'] == 1) { //待审核
            return $this->redirect(U('index/audits'));
            die;
        } else if ($this->userRow['is_service_station'] == 3) { //审核失败
            return $this->redirect(U('index/erroraudits'));
            die;
        }
    }

    /**
     * 上传简历
     */
    public function uploadJobFile() {
        header('Content-Type: application/json');
        $upload = new \Think\Upload(); // 实例化TP3.2上传类[14](@ref)
        $upload->maxSize   = 10 * 1024 * 1024; // 10MB
        $upload->exts      = array('jpg', 'png', 'doc', 'pdf', 'docx'); // 允许类型
        $upload->rootPath  = SITE_PATH.'/data/Job/station/';
        $upload->autoSub = false;
        $upload->savePath  = ''; // 按日期子目录存储
        $info = $upload->upload();
        $jobContent = I('post.jobcontent');
        if(!$info) {
            echo json_encode(['status'=>0, 'msg'=>$upload->getError()]);
        } else {
            // 返回完整文件路径和跳转URL
            $filePath = '/data/Job/station/'.$info['file']['savename'];
            $fileHash = md5_file(SITE_PATH . $filePath); // 计算文件哈希值
// 查询数据库中是否已存在相同哈希值的文件
            $existFile = D("UserJobDoc")->where(['file_hash' => $fileHash])->find();
            if ($existFile) {
                // 返回已有文件路径，避免重复存储
                echo json_encode([
                    'status' => 2,
                    'msg' => '当前简历已经存在，请勿重复提交',
                ]);die;
            }
            $userJobId = D("UserJob")->add([
                'user_id' => $this->userRow['id'],
                'remark' => $jobContent,
                'service_station_id' => $this->userRow['self_service_station_id'],
                'type' => 2,
                'create_time' => time(),
            ]);
            if ($userJobId) {
                D("UserJobDoc")->add([
                    'user_id' => $this->userRow['id'],
                    'user_job_id' => $userJobId,
                    'file_hash' => $fileHash,
                    'file_names' => $_FILES['file']['name'] ?: '',
                    'service_station_id' => $this->userRow['self_service_station_id'],
                    'content' => $filePath,
                    'status' => 1,
                    'create_time' => time(),
                ]);
                if (!empty($jobContent)) {
                    D("ServiceStationPlatformMessage")->add([
                        'service_station_id' => $this->userRow['self_service_station_id'],
                        'type' => 1,
                        'user_job_id' => $userJobId,
                        'content' => '求职诉求：'.$jobContent,
                        'create_time' => time(),
                    ]);
                }
            }
            echo json_encode([
                'status' => 1,
                'msg' => '上传成功',
                'path' => $filePath,
                'url' => U('index/joblist') // 生成TP3.2路由
            ]);
        }
    }

    public function getsorturl()
    {
        $insertId = D("ServiceStationUserJoin")->add([
            'service_station_id' => $this->userRow['self_service_station_id'],
            'user_id' => $this->userRow['id'],
            'create_time' => time(),
        ]);
        if ($insertId) {
            $code = D("Project")->enhash($insertId);
            $url = 'http://we.zhongcaiguoke.com/index/index/i/' . $code;
            return $this->success($url);
        }
        return $this->error('生成短链接错误!!!');
    }


    /**
     * 公告
     */
    public function ad()
    {
        $id = I('get.id', 0);
        if (!$id) return $this->redirect(U('index/index'));
        $row = D("PageAd")->where(['id' => $id, 'status' => 1])->order('id asc')->find();
        if (!$row) return $this->redirect(U('index/index'));
        $this->assign('row', $row);

        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }
        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }
 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->order('id desc')
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 用户管理
     */
    public function user()
    {
        $sortShowTime = I('get.show_time', 0);
        $sortShowNum = I('get.show_num', 0);
        $kwd = I('get.kwd', '');
        $this->assign('show_time', $sortShowTime);
        $this->assign('show_num', $sortShowNum);
        $this->assign('kwd', $kwd);

        $service_station_id = $this->userRow['self_service_station_id'];
        $serviceServiceStationRow = D("ServiceStation")->where(['id' => $service_station_id])->find();

        $where = ['service' => 2, 'service_station_id' => $serviceServiceStationRow['id']];
        if ($kwd != '') {
            $where['nickname'] = ['like', '%' . $kwd . '%'];
        }
        $obj = D("User");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $order = "id desc";
        if ($sortShowTime > 0) {
            if ($sortShowTime == 1) {
                $order = 'last_project_post_time asc,id desc';
            } else {
                $order = 'last_project_post_time desc,id desc';
            }
        }
        if ($sortShowNum > 0) {
            if ($sortShowNum == 1) {
                $order = 'show_num asc,id desc';
            } else {
                $order = 'show_num desc,id desc';
            }
        }
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order($order)
            ->select();
        if ($list) {
            $userJobList = [];
            $projectPostList = [];
            $userArrId = array_column($list, 'id');
            if ($userArrId) {

                $projectPostArrId = array_unique(array_column($list, 'last_project_post_id'));
                if ($projectPostArrId) {
                    $projectPostList = D("ProjectPost")->where(['id' => ['in', $projectPostArrId]])->getField('id,job_name', true);
                }
            }

            $unionidArr = array_column($list, 'unionid');
            //判断是否是服务站
            $unionidList = [];
            if ($unionidArr) {
                $unionidList = D("User")->where(['service_id' => 2, 'unionid' => ['in', $unionidArr]])->getField('unionid,self_service_station_id', true);
            }

            $this->assign('projectPostList', $projectPostList);
            $this->assign('userJobList', $userJobList);
            $this->assign('unionidList', $unionidList);

        }
        $this->assign('list', $list);
        if ($page->Current_page > 1) {
            $this->display('list-user');
            exit;
        }
        $usersCounts = $obj->where(['service' => 2, 'service_station_id' => $serviceServiceStationRow['id']])->count();
        $this->assign('usersCounts', $usersCounts);

 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $jobCount = D("UserJob")->where(['service_station_id' => $serviceServiceStationRow['id']])->count();
        $this->assign('jobCount', $jobCount);
        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 项目管理
     */
    public function projectlist()
    {
        $where = ['status' => 1];
        $obj = D("ProjectPost");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        if ($list) {
            $projectArrId = array_unique(array_column($list, 'project_id'));
            $projectList = D("Project")->where(['id' => ['in', $projectArrId]])->getField('id,name', true);
            $this->assign('projectList', $projectList);
        }
        $this->assign('list', $list);
        if ($page->Current_page > 1) {
            $this->display('list-projectlist');
            exit;
        }
        $this->assign("page", $page);
        $this->display();
    }

    public function jobcontent() {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $userJobDocRow = D("UserJobDoc")->where(['id' => $id])->find();
        if (!$userJobDocRow) $this->error('当前简历不存在');
        if (empty($userJobDocRow['html_content'])) $this->error('当前简历未分析成功，请稍后再来!!');
        $this->assign('userJobDocRow', $userJobDocRow);
        $this->display();
    }

    /**
     * remark
     */
    public function remark() {
        $id = I('get.id');
        $service_station_id = $this->userRow['self_service_station_id'];
        $jobRow = D("UserJob")->where(['id' => $id, 'service_station_id' => $service_station_id])->find();
        if (!$jobRow) $this->error("当前简历不存在！！");
        $where = [
            'service_station_id' => $service_station_id,
            'user_job_id' => $id,
        ];
        $jobList = D("ServiceStationPlatformMessage")->where($where)->select();
        $this->assign('jobList', $jobList);
        $this->assign('jobRow', $jobRow);
        $this->display();
    }

    public function commitremark() {
        $id = I('get.id');
        $service_station_id = $this->userRow['self_service_station_id'];
        $jobRow = D("UserJob")->where(['id' => $id, 'service_station_id' => $service_station_id])->find();
        if (!$jobRow) {
            echo json_encode(['status'=> 1, 'msg'=> "当前简历不存在"]);die;
        }
        if (IS_POST) {
            $jobContent = I('post.jobcontent');
            if (empty($jobContent)) {
                echo json_encode(['status'=> 1, 'msg'=> "内容不能成功!!!"]);die;
            }
            $insert = D("ServiceStationPlatformMessage")->add([
                'service_station_id' => $this->userRow['self_service_station_id'],
                'type' => 1,
                'user_job_id' => $jobRow['id'],
                'content' => $jobContent,
                'create_time' => time(),
            ]);
            if ($insert) {
                echo json_encode(['status'=> 0, 'msg'=> "添加内容成功"]);die;
            } else {
                echo json_encode(['status'=> 1, 'msg'=> "添加内容失败"]);die;
            }
        }


    }

    /**
     * 用户简历管理
     */
    public function joblist()
    {
        $sortShowTime = I('get.show_time', 0);
        $sortShowNum = I('get.show_num', 0);
        $jobState = I('get.job_state', ''); // 获取job_state参数
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $this->assign('type', $type);
        $this->assign('show_time', $sortShowTime);
        $this->assign('show_num', $sortShowNum);
        $this->assign('kwd', $kwd);

        $service_station_id = $this->userRow['self_service_station_id'];
        $serviceServiceStationRow = D("ServiceStation")->where(['id' => $service_station_id])->find();

        $where = ['service_station_id' => $serviceServiceStationRow['id'], 'is_job' => 1];
        if ($kwd != '') {
            $where['name'] = ['like', '%' . $kwd . '%'];
        }
        if ($type != '' && $type > 0) {
            $type = $type-1;
            $where['job_state'] = $type;
        }

        $order = "id desc";
        if ($sortShowTime > 0) {
            if ($sortShowTime == 1) {
                $order = 'last_project_post_time asc,id desc';
            } else {
                $order = 'last_project_post_time desc,id desc';
            }
        }
        if ($sortShowNum > 0) {
            if ($sortShowNum == 1) {
                $order = 'show_num asc,id desc';
            } else {
                $order = 'show_num desc,id desc';
            }
        }

        $service_station_id = $this->userRow['self_service_station_id'];
        $obj = D("UserJob");

        // 获取各状态的数量
        $newWhere = [
            'service_station_id' => $serviceServiceStationRow['id'], 'is_job' => 1
        ];
        $stateCounts = [
            'allcount' => $obj->where($newWhere)->count(), // 全部
            'communicating' => $obj->where($newWhere)->where(['job_state' => 0])->count(), // 沟通中
            'training'      => $obj->where($newWhere)->where(['job_state' => 1])->count(), // 培训中
            'onboarding'    => $obj->where($newWhere)->where(['job_state' => 2])->count(), // 入职中
            'terminated'    => $obj->where($newWhere)->where(['job_state' => 3])->count()  // 终止服务
        ];

        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order($order)
            ->select();
        $this->assign('serviceStatusList', $obj->service_status);
        $this->assign('stateCounts', $stateCounts); // 将状态数量分配到前端

        if ($list) {
            $userJobList = [];
            $projectPostList = [];
            $userArrId = array_column($list, 'user_id');
            $userLists = [];
            if ($userArrId) {
                $userListsArr = D("User")->where(['id' => ['in', $userArrId]])->select();
                foreach ($userListsArr as $userRow) {
                    $userLists[$userRow['id']] = $userRow;
                }
            }
            $this->assign('userLists', $userLists);

            $idArrId = array_unique(array_column($list, 'id'));
            if ($idArrId) {
                $userJobDocList = D("UserJobDoc")->where(['user_job_id' => ['in', $idArrId]])->getField('user_job_id,id,is_html,status,file_names,content,create_time', true);
            }
            $this->assign('userJobDocList', $userJobDocList);


            $projectPostArrId = array_unique(array_column($userLists, 'last_project_post_id'));
            if ($projectPostArrId) {
                $projectPostList = D("ProjectPost")->where(['id' => ['in', $projectPostArrId]])->getField('id,job_name', true);
            }

            $unionidArr = array_column($list, 'unionid');
            //判断是否是服务站
            $unionidList = [];
            if ($unionidArr) {
                $unionidList = D("User")->where(['service_id' => 1, 'unionid' => ['in', $unionidArr]])->getField('unionid,self_service_station_id', true);
            }

            $this->assign('projectPostList', $projectPostList);
            $this->assign('userJobList', $userJobList);
            $this->assign('unionidList', $unionidList);
                        
            
            

        }
        $this->assign('list', $list);

        if ($page->Current_page > 1) {
            $this->display('list-joblist');
            exit;
        }

 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);
        $this->assign("page", $page);

         // 控制器方法内
         $stateMap = array(
            0 => ['text' => '沟通中', 'style' => 'status-tag status-communicating'],
            1 => ['text' => '培训中', 'style' => 'status-tag status-training'],
            2 => ['text' => '已入职', 'style' => 'status-tag status-employed'],
            3 => ['text' => '服务终止', 'style' => 'status-tag status-terminated'],
        );
        $this->assign('stateMap', $stateMap);

        $this->display();
    }


    	/**
	 *  删除简历数据
	 */
    public function deljobdoc()
    {
        // 参数安全过滤
        $id = I('get.id', 0, 'intval');
        $serviceStationId = isset($this->userRow['self_service_station_id']) ? intval($this->userRow['self_service_station_id']) : 0;
    
        // 基础参数验证
        if ($id <= 0 || $serviceStationId <= 0) {
            $this->error('参数不合法');
        }
    
        // 初始化模型
        $userJobModel = D('UserJob');
        $userJobDocModel = D('UserJobDoc'); // 确认模型名称与实际一致
    
        // 查询要删除的记录（带权限校验）
        $jobWhere = [
            'id' => $id, 
            'service_station_id' => $serviceStationId
        ];
        $jobRow = $userJobModel->where($jobWhere)->find();
    
        if (!$jobRow) {
            $this->error('记录不存在或无权操作');
        }
    
        // 启用事务
        $userJobModel->startTrans();
        try {
            // 带条件删除主记录
            $deleteJob = $userJobModel->where($jobWhere)->delete();
            if ($deleteJob === false) {
                throw new Exception('主记录删除失败');
            }
    
            // 删除关联记录（使用参数绑定）
            $deleteDocs = $userJobDocModel->where(['user_job_id' => $id])->delete();
            if ($deleteDocs === false) {
                throw new Exception('关联记录删除失败');
            }
    
            // 提交事务
            $userJobModel->commit();
            $this->success('删除成功');
        } catch (Exception $e) {
            // 回滚事务
            $userJobModel->rollback();
            // 记录日志（实际开发中建议添加日志记录）
            // Log::write('删除失败：'.$e->getMessage());
            $this->error('删除失败，请稍后重试');
        }
    }

    /**
     * 登录页面
     */
    public function logins()
    {
        if ($this->userRow['self_service_station_id']) {
            return $this->redirect(U('index/index'));
            die;
        }

        if (IS_POST) {
            $data = I('post.');
            $mobile = $data['mobile'];
            $code = $data['code'];
            if (empty($data['mobile'])) $this->error('参数错误');
            $serviceStationRow = D("ServiceStation")->where(['mobile' => $data['mobile']])->find();
            if (!$serviceStationRow) $this->error('当前服务站不存在，请重新注册！！');
            if (empty($mobile)) $this->error('手机号未填写');
            if (empty($code)) $this->error('验证码未填写');
            if ($code == session('code_login_' . $mobile)) {
                $totalNum = D("User")->where(['service_id' => 1, 'self_service_station_id' => $serviceStationRow['id']])->count();
                $is_service_station = 1;
                switch ($serviceStationRow['status']) {
                    case 0 :
                        $is_service_station = 1;
                        break;
                    case 1:
                        $is_service_station = 2;
                        break;
                    case 2:
                        $is_service_station = 3;
                        break;
                }
                $updateData = [
                    'id' => $this->userRow['id'],
                    'self_service_station_id' => $serviceStationRow['id'],
                    'is_service_station' => $is_service_station,
                ];
                if ($totalNum == 0) {
                    $updateData['is_first_wechat'] = 1;
                }
                D("User")->save($updateData);
                D("User")->where(['id' => $this->userRow['id']])->save(['last_login_time' => time()]);

                $this->redirect('index/index');
            } else {
                $this->error('验证码未填写');
            }
        }
        $this->display();
    }

    /**
     * 设备管理
     */
    public function loginaccount()
    {
        if ($this->userRow['is_first_wechat'] != 1) $this->error('您当前无权限操作此页面!!!');
        $userLists = D("User")->where(['service_id' => 1, 'self_service_station_id' => $this->userRow['self_service_station_id']])->select();
        $this->assign("userLists", $userLists);

        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }
        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }
 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);

        $this->display();
    }

    /**
     * 用户操作日志
     */
    public function userlog()
    {
        if ($this->userRow['is_first_wechat'] != 1) $this->error('您当前无权限操作此页面!!!');
        $where = ['service_station_id' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStationDivideLog");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        if ($list) {
            $serviceStationList = [];
            $serviceStationArrId = array_unique(array_column($list, 'divide_service_station_id'));
            if ($serviceStationArrId) {
                $serviceStationArrId[] = $this->userRow['self_service_station_id'];
                $serviceStationList = D("ServiceStation")->where(['id' => ['in', $serviceStationArrId]])->getField('id,service_name', true);
            }
            $this->assign('serviceStationList', $serviceStationList);

            $userList = [];
            $userArrId = array_unique(array_column($list, 'user_id'));
            if ($userArrId) {
                $userList = D("User")->where(['id' => ['in', $userArrId]])->getField('id,nickname,headimgurl', true);
            }
            $this->assign('userList', $userList);
        }
        $this->assign('list', $list);
        if ($page->Current_page > 1) {
            $this->display('list-userlog');
            exit;
        }
 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);

        $this->display();
    }

    /**
     * 退出登录
     */
    public function outloginaccount()
    {
        if ($this->userRow['is_first_wechat'] != 1) $this->error('您当前无权限操作此页面!!!');
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误!!');
        $selfUserRow = D("User")->where(['id' => $id])->find();
        if ($selfUserRow && $selfUserRow['self_service_station_id'] == $this->userRow['self_service_station_id']) {
            $boolean = D("User")->save(['id' => $selfUserRow['id'], 'self_service_station_id' => 0, 'is_service_station' => 0, 'is_first_wechat' => 0]);
            if ($boolean) {
                return $this->success('退出登录成功!!', U('index/loginaccount'));
            }
        }
    }

    // 发送手机验证码
    public function logincode()
    {
        if ($this->userRow['self_service_station_id']) {
            $ret = ['code' => 3, "msg" => '当前微信已绑定其它的服务站！'];
            echo json_encode($ret);
            die;
        }

        $mobile = I("get.mobile");
        $ret['code'] = 0;
        $stataion = true;
        $serviceStationRow = D("ServiceStation")->where(['mobile' => $mobile, 'status' => 1])->find();
        if (!$serviceStationRow) {
            $ret = [
                'code' => 1,
                'msg' => '当前手机号不存在！',
            ];
        } else {
            $lasts = session('time_login_' . $mobile);
            if ($lasts and $lasts > time() - 60) {
                $ret = [
                    'code' => 2,
                    'msg' => '验证码已发送，请稍后请求！',
                ];
            } else {
                $code = mt_rand(10000, 99999);
                session('code_login_' . $mobile, $code);
                session('time_login_' . $mobile, time());
                $alisms = new AliSms();
                $result = $alisms->sendSms($code, $mobile);
                if (!$result) {
                    $ret = ['code' => 3, "msg" => '验证码发送失败！'];
                }
            }
        }
        echo json_encode($ret);
        die;
    }

    /**
     * 余额管理
     */
    public function money()
    {
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }
        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }
 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 金额详情管理
     */
    public function money_details()
    {
 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->display();
    }

    /**
     * 余额提现页面管理
     */
    public function money_card()
    {
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }
        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }
 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 资金明细页面管理
     */
    public function money_alldetails()
    {
        $type = I('get.type', '');
        $startTime = I('get.startTime', 0);
        $endTimes = I('get.endTime', 0);
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("StationMoney");
        if ($type > 0) {
            if ($type == 1) {
                $where['type'] = ['gt', 1];
            } else {
                $where['type'] = 1;
            }
        }
        if ($startTime == 0) {
            $startTime = strtotime(date("Y-m-01 00:00:00"));
        }
        if ($endTimes == 0) {
            $endTimes = strtotime("Y-m-d 23:59:59");
        }
        if ($startTime > 0 && $endTimes > 0) {
            $where['create_time'] = ['between', [$startTime, $endTimes]];
        }

        $this->assign('type', $type);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        $this->assign('typeList', $obj->type);
        $n = I('get.n', 0);
        if ($page->Current_page > 1 || $n == 1) {
            $this->display('list-money_alldetails');
            exit;
        }
  
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 提现进度页面管理
     */
    public function money_withdrawal()
    {
        $where = ['service_station_id' => $this->userRow['self_service_station_id'], 'type' => 1];
        $obj = D("StationMoney");
        $type = I('get.type');
        if ($type > 0) {
            $where['status'] = $type;
        }
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        $this->assign('typeList', $obj->type);
        $n = I('get.n');
        if ($page->Current_page > 1 || $n == 1) {
            $this->display('list-money_withdrawal');
            exit;
        }
 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 服务站管理
     */
    public function servicestation()
    {

        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }

        $refRow = $obj->where(['status' => 1, 'pid' => $this->userRow['self_service_station_id']])->find();
        $this->assign('refCount', $refRow ? 1 : 0);


        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }
 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }


    /**
     * 服务站资源包页面
     */
    public function servicestationbuy()
    {
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }
        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }
 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 服务站资源包划拨页面
     */
    public function to_station()
    {
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStation");
        $this->assign('statusList', $obj->status);
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误!!');
        $divideServiceStation = $obj->where(['id' => $id])->find();
        if (!$divideServiceStation) $this->error('当前服务站不存在!');
        if ($divideServiceStation['pid'] != $this->userRow['self_service_station_id']) $this->error('当前服务站不是您推荐的服务站!');
        if ($divideServiceStation['status'] != 1) $this->error('当前服务站审核未通过 ！');
 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();
            
        if ($serviceStationRow['is_out']!=1) $this->error('无资源包划拨权限!!');   

        $this->assign('id', $id);
        $this->assign('levelList', $obj->level);
        $this->assign('divideServiceStation', $divideServiceStation);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);
        $this->display();
    }

    /**
     * 划分服务站
     */
    public function divideservicestation()
    {
        if (IS_POST) {
            $id = I('get.id', 0);
            $divide_open_num = I('post.divide_open_num', 0);
            $obj = D("ServiceStation");
            $divideServiceStation = $obj->where(['id' => $id])->find();
            if (!$divideServiceStation) $this->error('当前服务站不存在!');
            if ($divideServiceStation['pid'] != $this->userRow['self_service_station_id']) $this->error('当前服务站不是您推荐的服务站!');
            if ($divideServiceStation['status'] != 1) $this->error('当前服务站审核未通过 ！');
            $serviceStationRow = D("ServiceStation")
                ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
                ->find();
            if ($serviceStationRow['is_out']!=1) $this->error('无资源包划拨权限!!');   
            $openNum = $serviceStationRow['open_num'] - $serviceStationRow['succ_open_num'];
            if ($openNum <= 0) $this->error('您的服务站资源包数量不足');
            if ($openNum - $divide_open_num < 0) $this->error('划拨数量不足');
            $insertId = D("ServiceStationDivideLog")->add([
                'user_id' => $this->userRow['id'],
                'service_station_id' => $serviceStationRow['id'],
                'divide_service_station_id' => $divideServiceStation['id'],
                'service_station_open_num' => $openNum,
                'divide_open_num' => $divide_open_num,
            ]);
            if ($insertId) {
                D("ServiceStation")->save(['id' => $divideServiceStation['id'], 'open_num' => $divide_open_num]);
                D("ServiceStation")->save(['id' => $serviceStationRow['id'], 'open_num' => ['exp','open_num-' . $divide_open_num], 'divide_open_num' => ['exp', 'divide_open_num+' . $divide_open_num]]);
                return $this->success('资源包数已划拨成功');
            }
        }
    }

    /**
     * 问答页面
     */
    public function qa()
    {
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['status' => 1, 'pid' => 0];
        $obj = D("QuestionCategory");

        $list = $obj
            ->where($where)
            ->order('sort asc,id desc')
            ->select();
        $navData = [];
        if ($list) {
            $categoryId = array_unique(array_column($list, 'id'));
            if ($categoryId) {
                $questionCategoryTwo = D("QuestionCategory")->where(['pid' => ['in', $categoryId]])->order('sort asc')->select();
                $questionCategoryTwoList = [];
                foreach ($questionCategoryTwo as $questionCategoryTwoRow) {
                    $questionCategoryTwoList[$questionCategoryTwoRow['pid']][$questionCategoryTwoRow['id']] = $questionCategoryTwoRow;
                }
            }
            $navData = [];

            foreach ($list as $row) {
                $subs = [];
                $questions = [];
//                foreach ($questionCategoryTwoList[$row['id']] as $questionCategoryTwoRow) {
//                    $name = $questionCategoryTwoRow['title'];
//                    $subs[] = $name;
//                    $qaList = [];
//
//                }
                $questionList = D("Question")->field('id,title')->where(['category_id' => ['like', '%,'.$row['id'].',%'], 'status' => 1])->select();
                $lists = [];
                foreach ($questionList as $questionRow) {
                    $lists[] = ['id' => $questionRow['id'], 'name' => $questionRow['title']];
                }
                $questions[$row['title']] = $lists;
                $navData[$row['title']] = [
//                    'subs' => $subs,
                    'questions' => $questions,
                ];
            }
        }
        $this->assign('navData', json_encode($navData, JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE));
        $this->assign('list', $list);
        $this->assign('questionCategoryTwoList', $questionCategoryTwoList);

 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);
        $this->display();
    }


    /**
     * 公告
     */
    public function qacontent()
    {
        $id = I('get.id', 0);
        if (!$id) return $this->redirect(U('index/index'));
        $row = D("Question")->where(['id' => $id, 'status' => 1])->find();
        if (!$row) return $this->redirect(U('index/index'));
        $this->assign('row', $row);

 
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);
        $this->display();
    }

    /**
     * 服务站审核
     */
    public function succservicestation()
    {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id])->find();
        if ($row['status'] == 1) $this->error('当前服务站已经审核通过');
        $pidRow = [];
        if ($row['pid'] > 0) {
            $pidRow = $obj->where(['id' => $row['pid']])->find();
        }
        if ($row['pid'] == $this->userRow['service_station_id'] && ($pidRow['open_num'] - $pidRow['succ_open_num']) > 0) {
            $code = D("Qrcode")->createNew(1, 1, $row['id']);
            if ($code) {
                $obj->save(['id' => $this->userRow['id'], 'succ_open_num' => ['exp', 'succ_open_num+1']]);
                $obj->save(['id' => $id, 'status' => 1]);
                if (!D("Qrcode")->where(['service_id' => 2, 'service_station_id' => $row['id']])->find()) {
                    $code = D("Qrcode")->createNew(2, 1, $row['id']);
                }
                if (!D("Qrcode")->where(['service_id' => 1, 'service_station_id' => $row['id']])->find()) {
                    $code = D("Qrcode")->createNew(1, 1, $row['id']);
                }
                D("User")->where(['self_service_station_id' => $id])->save(['is_service_station' => 2]);
                $this->success('审核通过');
            } else {
                $this->error('开通失败，请联系技术处理');
            }
        } else {
            $this->error('当前服务站的开通数量不足');
        }
        $this->error('参数错误!!');
    }

    /**
     * 审核失败
     */
    public function errservicestation()
    {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id])->find();
        if ($row['status'] > 0) $this->error('当前服务站已经审核');
        $obj->save(['id' => $id, 'status' => 2]);
        $this->success('审核状态已更新', U('index/servicestation'));
    }

    /**
     * 待审核
     * @return void
     */
    public function audits()
    {
        if ($this->userRow['is_service_station'] == 0) { //注册绑定
            return $this->redirect(U('index/logins'));
            die;
        } elseif ($this->userRow['is_service_station'] == 2) { //审核通过
            return $this->redirect(U('index/index'));
            die;
        } elseif ($this->userRow['is_service_station'] == 1) { //待审核
            $this->display();
        } else if ($this->userRow['is_service_station'] == 3) { //审核失败
            return $this->redirect(U('index/erroraudits'));
            die;
        }
    }

    /**
     * 审核失败
     * @return void
     */
    public function erroraudits()
    {
        if ($this->userRow['is_service_station'] == 0) { //注册绑定
            return $this->redirect(U('index/logins'));
            die;
        } elseif ($this->userRow['is_service_station'] == 2) { //审核通过
            return $this->redirect(U('index/index'));
            die;
        } elseif ($this->userRow['is_service_station'] == 1) { //待审核
            return $this->redirect(U('index/audits'));
            die;
        } else if ($this->userRow['is_service_station'] == 3) { //审核失败
            $this->display();
        }
    }

    /**
     * 注册子服务站
     * @return void
     */
    public function register()
    {
        if ($this->userRow['is_service_station'] == 0) {
            return $this->redirect(U('index/logins'));
        }
        if ($this->userRow && $this->userRow['self_service_station_id'] > 0) { //注册绑定
            $selfServiceStationRow = D("ServiceStation")->where(['id' => $this->userRow['self_service_station_id']])->find();
            if (($selfServiceStationRow['open_num'] - $selfServiceStationRow['succ_open_num']) <= 0) $this->error('开通服务站数量不足');
            if (IS_POST) {
                $data = I('post.');
                $mobile = $data['mobile'];
                $code = $data['code'];
                if (empty($data['mobile'])) $this->error('参数错误');
                $serviceStationRow = D("ServiceStation")->where(['mobile' => $data['mobile']])->find();
                if ($serviceStationRow) $this->error('当前手机号已经被注册');
                if (empty($mobile)) $this->error('手机号未填写');
                if (empty($code)) $this->error('验证码未填写');
                if (empty($data['business_license'])) $this->error("营业执照不能为空");
                if (empty($data['franchise_list'])) $this->error("加盟表不能为空");
                if ($code == session('code_' . $mobile)) {
                    $insertData = [
                        'mobile' => $data['mobile'],
                        'enterprise_name' => $data['enterprise_name'],
                        'business_license' => $data['business_license'],
                        'franchise_list' => $data['franchise_list'],
                        'pid' => $this->userRow['service_station_id'],
                        'service_name' => $data['service_name'],
                        'create_time' => time(),
                    ];
                    $insertId = D("ServiceStation")->add($insertData);
                    if ($insertId) {
                        return $this->success('注册服务站成功！', U('index/index'));
                    }
                } else {
                    return $this->error('验证码错误！');
                }
            }
            $this->display();
        } elseif ($this->userRow['is_service_station'] == 2) { //审核通过
            return $this->redirect(U('index/index'));
            die;
        } elseif ($this->userRow['is_service_station'] == 1) { //待审核
            return $this->redirect(U('index/audits'));
            die;
        } else if ($this->userRow['is_service_station'] == 3) { //审核失败
            $this->display();
        } else {
            return $this->redirect(U('index/logins'));
            die;
        }
    }

    /**
     * 图片上传
     */
    public function uploads()
    {
        $base64_image_content = I('post.img', '');
        if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $base64_image_content, $result)) {
            $type = $result[2]; // 图片类型，如 png、jpeg
            $base64_data = str_replace($result[1], '', $base64_image_content); // 获取真正的 Base64 数据
            $base64_data = base64_decode($base64_data); // 解码 Base64 数据
            $upload_dir = SITE_PATH . '/data/Material/'; // 上传目录
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0777, true); // 如果目录不存在，则创建
            }

            $file_name = uniqid() . '.' . $type; // 生成唯一的文件名
            $file_path = $upload_dir . $file_name; // 完整的文件路径
            $returnPath = '/data/Material/' . $file_name;
            if (file_put_contents($file_path, $base64_data)) {
                $this->success($returnPath);
                die;
            } else {
                die("图片保存失败");
                $this->error('图片保存失败');
            }
        }
        $this->error('图片上传错误');
    }

    // 发送手机验证码
    public function getcode()
    {
        $mobile = I("get.mobile");
        $ret['code'] = 0;
        $stataion = true;
        $usermobileRow = D("User")->where(['mobile' => $mobile, 'service_id' => 2])->find();
        if ($usermobileRow && $usermobileRow['id'] != $this->userRow['id']) {
            $ret = [
                'code' => 1,
                'msg' => '手机号已经被绑定！',
            ];
        } else {
            $last = session('time_' . $mobile);
            if ($last and $last > time() - 60) {
                $ret = [
                    'code' => 2,
                    'msg' => '验证码已发送，请稍后请求！',
                ];
            } else {
                $code = mt_rand(10000, 99999);
                session('code_' . $mobile, $code);
                session('time_' . $mobile, time());
                $alisms = new AliSms();
                $result = $alisms->sendSms($code, $mobile);
                if (!$result) {
                    $ret = ['code' => 3, "msg" => '验证码发送失败！'];
                }
            }
        }
        echo json_encode($ret);
        die;
    }

     /**
     * 法律
     */


    public function law()
    {
     $id = I('get.id', '13');
     if (!$id) $this->error('参数错误');
     $obj = D("page_list");
     $row = $obj->where(['id' => $id])->find();
     if (!$row) $this->error('无信息');
     $page_list = D("page_list")->where(['status' => 1])->field('id,title,content')->select();
     if (!C('LOCAL_DEBUG')) {
         $this->assign('wxconf', getWxConfig(null, $this->conf));
         $this->assign('appid', $this->conf['WX_APPID']);
     }
     $this->assign('projectList', $page_list);
     $this->assign('row', $row);
     $this->display();
    }

}
