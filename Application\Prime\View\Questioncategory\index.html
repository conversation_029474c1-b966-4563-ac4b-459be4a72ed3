<include file="block/hat" />
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<style type='text/css'>
				/* 现代化问题分类管理页面样式 */
				.questioncategory-index-wrapper {
					background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
					min-height: 100vh;
					padding: 0;
					margin: 0;
				}

				.questioncategory-index-container {
					max-width: 1400px;
					margin: 0 auto;
					padding: 2rem;
					background: transparent;
				}

				/* 现代化页面标题 */
				.questioncategory-index-header {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 2rem;
					overflow: hidden;
					position: relative;
				}

				.questioncategory-index-header::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4px;
					background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
				}

				.questioncategory-index-header-content {
					padding: 2rem;
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex-wrap: wrap;
					gap: 1rem;
				}

				.questioncategory-index-title {
					display: flex;
					align-items: center;
					gap: 1rem;
					margin: 0;
				}

				.questioncategory-index-title-icon {
					width: 3rem;
					height: 3rem;
					background: linear-gradient(135deg, #10b981 0%, #059669 100%);
					border-radius: 0.75rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.5rem;
				}

				.questioncategory-index-title-text {
					display: flex;
					flex-direction: column;
				}

				.questioncategory-index-title-main {
					font-size: 2rem;
					font-weight: 700;
					color: #1a202c;
					margin: 0;
					line-height: 1.2;
				}

				.questioncategory-index-title-sub {
					font-size: 1.5rem;
					color: #718096;
					margin: 0;
					font-weight: 400;
				}

				.questioncategory-index-actions {
					display: flex;
					align-items: center;
					gap: 1rem;
				}

				.questioncategory-index-search-toggle {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
					color: white;
					border: none;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-weight: 600;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
					text-decoration: none;
				}

				.questioncategory-index-search-toggle:hover {
					transform: translateY(-2px);
					box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
					color: white;
					text-decoration: none;
				}

				.questioncategory-index-add-btn {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					background: linear-gradient(135deg, #10b981 0%, #059669 100%);
					color: white;
					border: none;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-weight: 600;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
					text-decoration: none;
				}

				.questioncategory-index-add-btn:hover {
					transform: translateY(-2px);
					box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
					color: white;
					text-decoration: none;
				}

				/* 现代化搜索面板 */
				.questioncategory-index-search-panel {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 2rem;
					overflow: hidden;
					max-height: 0;
					opacity: 0;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.questioncategory-index-search-panel.show {
					max-height: 500px;
					opacity: 1;
				}

				.questioncategory-index-search-header {
					background: linear-gradient(135deg, #10b981 0%, #059669 100%);
					color: white;
					padding: 1.5rem 2rem;
					position: relative;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.questioncategory-index-search-header::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					height: 1px;
					background: rgba(255, 255, 255, 0.2);
				}

				.questioncategory-index-search-title {
					font-size: 1.75rem;
					font-weight: 600;
					margin: 0;
				}

				.questioncategory-index-search-icon {
					width: 2.5rem;
					height: 2.5rem;
					background: rgba(255, 255, 255, 0.2);
					border-radius: 0.5rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.25rem;
				}

				.questioncategory-index-search-body {
					padding: 2rem;
				}

				.questioncategory-index-search-form {
					display: grid;
					grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
					gap: 1.5rem;
					align-items: end;
				}

				.questioncategory-index-form-group {
					display: flex;
					flex-direction: column;
					gap: 0.5rem;
				}

				.questioncategory-index-form-label {
					font-size: 1.5rem;
					font-weight: 600;
					color: #374151;
				}

				.questioncategory-index-form-select,
				.questioncategory-index-form-input {
					padding: 0.75rem 1rem;
					border: 2px solid #e2e8f0;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-family: inherit;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					background: #f8fafc;
				}

				.questioncategory-index-form-select:focus,
				.questioncategory-index-form-input:focus {
					outline: none;
					border-color: #10b981;
					box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
					background: white;
				}

				.questioncategory-index-search-actions {
					display: flex;
					gap: 1rem;
					margin-top: 1rem;
				}

				.questioncategory-index-search-btn {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-weight: 600;
					text-decoration: none;
					border: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				}

				.questioncategory-index-search-btn:hover {
					transform: translateY(-2px);
					box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
					text-decoration: none;
				}

				.questioncategory-index-search-btn.btn-primary {
					background: linear-gradient(135deg, #10b981 0%, #059669 100%);
					color: white;
				}

				.questioncategory-index-search-btn.btn-primary:hover {
					color: white;
				}

				.questioncategory-index-search-btn.btn-secondary {
					background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
					color: white;
				}

				.questioncategory-index-search-btn.btn-secondary:hover {
					color: white;
				}

				/* 现代化导航标签 */
				.questioncategory-index-nav-container {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 2rem;
					overflow: hidden;
				}

				.questioncategory-index-nav-tabs {
					display: flex;
					background: #f8fafc;
					border-bottom: 1px solid #e2e8f0;
					margin: 0;
					padding: 0;
					list-style: none;
					overflow-x: auto;
				}

				.questioncategory-index-nav-item {
					flex: 1;
					min-width: 0;
				}

				.questioncategory-index-nav-link {
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 0.5rem;
					padding: 1.25rem 1.5rem;
					color: #718096;
					text-decoration: none;
					font-size: 1.5rem;
					font-weight: 600;
					border-bottom: 3px solid transparent;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					white-space: nowrap;
				}

				.questioncategory-index-nav-link:hover {
					color: #10b981;
					background: rgba(16, 185, 129, 0.05);
					text-decoration: none;
				}

				.questioncategory-index-nav-link.active {
					color: #10b981 !important;
					background: white !important;
					border-bottom-color: #10b981 !important;
					box-shadow: 0 -2px 4px rgba(16, 185, 129, 0.1) !important;
					font-weight: 700 !important;
				}

				.questioncategory-index-nav-link.active::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 3px;
					background: linear-gradient(90deg, #10b981 0%, #059669 100%);
					z-index: 1;
				}

				.questioncategory-index-nav-link.active .questioncategory-index-nav-icon {
					color: #10b981 !important;
					transform: scale(1.1);
				}

				.questioncategory-index-nav-icon {
					font-size: 1.25rem;
				}

				/* 现代化列表布局 */
				.questioncategory-index-list-container {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					overflow: hidden;
					margin-bottom: 2rem;
				}

				.questioncategory-list-header {
					background: linear-gradient(135deg, #10b981 0%, #059669 100%);
					color: white;
					padding: 1.5rem 2rem;
					position: relative;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.questioncategory-list-header::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					height: 1px;
					background: rgba(255, 255, 255, 0.2);
				}

				.questioncategory-list-title {
					font-size: 1.75rem;
					font-weight: 600;
					margin: 0;
				}

				.questioncategory-list-icon {
					width: 2.5rem;
					height: 2.5rem;
					background: rgba(255, 255, 255, 0.2);
					border-radius: 0.5rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.25rem;
				}

				.questioncategory-list-body {
					padding: 0;
				}

				.questioncategory-list-item {
					display: flex;
					align-items: center;
					padding: 1.5rem 2rem;
					border-bottom: 1px solid #f1f5f9;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
				}

				.questioncategory-list-item:last-child {
					border-bottom: none;
				}

				.questioncategory-list-item:hover {
					background: #f8fafc;
					transform: translateX(4px);
				}

				.questioncategory-list-item::before {
					content: '';
					position: absolute;
					left: 0;
					top: 0;
					bottom: 0;
					width: 4px;
					background: transparent;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.questioncategory-list-item:hover::before {
					background: linear-gradient(135deg, #10b981 0%, #059669 100%);
				}

				.questioncategory-item-id {
					width: 80px;
					flex-shrink: 0;
					font-size: 1.5rem;
					font-weight: 700;
					color: #10b981;
					text-align: center;
					background: #f0fdf4;
					padding: 0.5rem;
					border-radius: 0.5rem;
					border: 2px solid #dcfce7;
				}

				.questioncategory-item-content {
					flex: 1;
					margin-left: 2rem;
					min-width: 0;
				}

				.questioncategory-item-title {
					font-size: 1.75rem;
					font-weight: 600;
					color: #1f2937;
					margin: 0 0 0.5rem 0;
					line-height: 1.4;
					word-break: break-word;
				}

				.questioncategory-item-meta {
					display: flex;
					align-items: center;
					gap: 1.5rem;
					flex-wrap: wrap;
				}

				.questioncategory-item-parent {
					display: flex;
					align-items: center;
					gap: 0.5rem;
					font-size: 1.25rem;
					color: #6b7280;
				}

				.questioncategory-item-status {
					display: flex;
					align-items: center;
					gap: 0.5rem;
				}

				.questioncategory-status-badge {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.25rem 0.75rem;
					border-radius: 1rem;
					font-size: 1.25rem;
					font-weight: 600;
					color: white;
				}

				.questioncategory-status-badge.label-success {
					background: linear-gradient(135deg, #10b981 0%, #059669 100%);
				}

				.questioncategory-status-badge.label-warning {
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
				}

				.questioncategory-status-badge.label-danger {
					background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
				}

				.questioncategory-status-badge.label-default {
					background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
				}

				.questioncategory-item-sort {
					display: flex;
					align-items: center;
					gap: 0.5rem;
					font-size: 1.25rem;
					color: #6b7280;
				}

				.questioncategory-item-time {
					display: flex;
					align-items: center;
					gap: 0.5rem;
					font-size: 1.25rem;
					color: #6b7280;
				}

				.questioncategory-item-actions {
					margin-left: 2rem;
					flex-shrink: 0;
					display: flex;
					gap: 0.75rem;
					flex-wrap: wrap;
				}

				.questioncategory-item-actions .btn {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.5rem 1rem;
					border-radius: 0.5rem;
					font-size: 1.25rem;
					font-weight: 600;
					text-decoration: none;
					border: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
					white-space: nowrap;
				}

				.questioncategory-item-actions .btn:hover {
					transform: translateY(-2px);
					box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
					text-decoration: none;
				}

				/* 响应式设计 */
				@media (max-width: 768px) {
					.questioncategory-index-container {
						padding: 1rem;
					}

					.questioncategory-list-item {
						flex-direction: column;
						align-items: flex-start;
						gap: 1rem;
						padding: 1.5rem 1rem;
					}

					.questioncategory-item-content {
						margin-left: 0;
						width: 100%;
					}

					.questioncategory-item-meta {
						flex-direction: column;
						align-items: flex-start;
						gap: 0.75rem;
					}

					.questioncategory-item-actions {
						margin-left: 0;
						width: 100%;
						justify-content: flex-start;
					}

					.questioncategory-item-actions .btn {
						flex: 1;
						justify-content: center;
						min-width: 0;
					}
				}

				/* 动画效果 */
				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(20px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}

				.questioncategory-index-fade-in {
					animation: fadeInUp 0.6s ease-out;
				}

				.questioncategory-index-fade-in-delay-1 {
					animation: fadeInUp 0.6s ease-out 0.1s both;
				}

				.questioncategory-index-fade-in-delay-2 {
					animation: fadeInUp 0.6s ease-out 0.2s both;
				}

				.questioncategory-index-fade-in-delay-3 {
					animation: fadeInUp 0.6s ease-out 0.3s both;
				}
			</style>

			<div class="questioncategory-index-wrapper">
				<div class="questioncategory-index-container">
					<!-- 现代化页面标题 -->
					<div class="questioncategory-index-header questioncategory-index-fade-in">
						<div class="questioncategory-index-header-content">
							<div class="questioncategory-index-title">
								<div class="questioncategory-index-title-icon">
									<i class="fa fa-folder-open"></i>
								</div>
								<div class="questioncategory-index-title-text">
									<h1 class="questioncategory-index-title-main">问题分类管理</h1>
									<p class="questioncategory-index-title-sub">Question Category Management</p>
								</div>
							</div>
							<div class="questioncategory-index-actions">
								<button type="button" class="questioncategory-index-search-toggle" onclick="toggleSearchPanel()">
									<i class="fa fa-search"></i>
									<span>搜索筛选</span>
								</button>
								<button type="button" class="questioncategory-index-add-btn" onclick="showAddCategoryModal()">
									<i class="fa fa-plus"></i>
									<span>添加分类</span>
								</button>
							</div>
						</div>
					</div>

					<!-- 现代化搜索面板 -->
					<div class="questioncategory-index-search-panel questioncategory-index-fade-in-delay-1" id="searchPanel">
						<div class="questioncategory-index-search-header">
							<div class="questioncategory-index-search-icon">
								<i class="fa fa-search"></i>
							</div>
							<h3 class="questioncategory-index-search-title">搜索筛选</h3>
						</div>
						<div class="questioncategory-index-search-body">
							<form method="get" class="questioncategory-index-search-form" role="form">
								<div class="questioncategory-index-form-group">
									<label class="questioncategory-index-form-label">搜索字段</label>
									<select class="questioncategory-index-form-select" name="kw">
										<php>foreach($c_kw as $key=>$value){</php>
										<option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
										<php>}</php>
									</select>
								</div>
								<div class="questioncategory-index-form-group">
									<label class="questioncategory-index-form-label">搜索内容</label>
									<input class="questioncategory-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
								</div>
								<div class="questioncategory-index-form-group">
									<label class="questioncategory-index-form-label">上架状态</label>
									<select name="status" class="questioncategory-index-form-select">
										<option value="">全部状态</option>
										<php>foreach($statusList as $key => $status) {</php>
										<option value="{$key}" {:$_get['status'] != '' && $_get['status'] == $key ? "selected" : ''}>{$status.text}</option>
										<php>} </php>
									</select>
								</div>
								<div class="questioncategory-index-search-actions">
									<button type="submit" class="questioncategory-index-search-btn btn-primary">
										<i class="fa fa-search"></i>
										<span>搜索</span>
									</button>
									<a href="{:U('Questioncategory/index')}" class="questioncategory-index-search-btn btn-secondary">
										<i class="fa fa-refresh"></i>
										<span>重置</span>
									</a>
								</div>
							</form>
						</div>
					</div>

					<!-- 现代化导航标签 -->
					<div class="questioncategory-index-nav-container questioncategory-index-fade-in-delay-2">
						<ul class="questioncategory-index-nav-tabs">
							<li class="questioncategory-index-nav-item">
								<a href="{:U('Questioncategory/index')}" class="questioncategory-index-nav-link active">
									<i class="fa fa-list questioncategory-index-nav-icon"></i>
									<span>全部分类</span>
								</a>
							</li>
							<li class="questioncategory-index-nav-item">
								<a href="javascript:void(0)" class="questioncategory-index-nav-link quick-filter" data-filter="published">
									<i class="fa fa-check-circle questioncategory-index-nav-icon"></i>
									<span>已上架</span>
								</a>
							</li>
							<li class="questioncategory-index-nav-item">
								<a href="javascript:void(0)" class="questioncategory-index-nav-link quick-filter" data-filter="draft">
									<i class="fa fa-edit questioncategory-index-nav-icon"></i>
									<span>草稿</span>
								</a>
							</li>
							<li class="questioncategory-index-nav-item">
								<a href="javascript:void(0)" class="questioncategory-index-nav-link quick-filter" data-filter="disabled">
									<i class="fa fa-times-circle questioncategory-index-nav-icon"></i>
									<span>已禁用</span>
								</a>
							</li>
						</ul>
					</div>

					<!-- 现代化列表容器 -->
					<form action="" method="post">
						<div class="questioncategory-index-list-container questioncategory-index-fade-in-delay-3">
							<div class="questioncategory-list-header">
								<div class="questioncategory-list-icon">
									<i class="fa fa-list"></i>
								</div>
								<h3 class="questioncategory-list-title">分类列表</h3>
							</div>
							<div class="questioncategory-list-body">
								<php>foreach($list as $v) { </php>
								<div class="questioncategory-list-item">
									<!-- ID显示 -->
									<div class="questioncategory-item-id">
										#{$v.id}
									</div>

									<!-- 内容区域 -->
									<div class="questioncategory-item-content">
										<!-- 分类名称 -->
										<h4 class="questioncategory-item-title">
											{$v.title}
										</h4>

										<!-- 元信息 -->
										<div class="questioncategory-item-meta">
											<!-- 所属上级 -->
											<div class="questioncategory-item-parent">
												<i class="fa fa-sitemap"></i>
												<span>{:$v['pid'] == 0 ? '顶级分类' : $pidList[$v['pid']]}</span>
											</div>

											<!-- 状态 -->
											<div class="questioncategory-item-status">
												<span class="questioncategory-status-badge label-{:D('QuestionCategory')->status[$v['status']]['style']}">
													<i class="fa fa-circle"></i>
													<span>{:D("QuestionCategory")->status[$v['status']]['text']}</span>
												</span>
											</div>

											<!-- 排序 -->
											<div class="questioncategory-item-sort">
												<i class="fa fa-sort-numeric-asc"></i>
												<span>排序: {$v.sort}</span>
											</div>

											<!-- 创建时间 -->
											<div class="questioncategory-item-time">
												<i class="fa fa-clock-o"></i>
												<span>{:date("Y-m-d H:i:s", $v['created'])}</span>
											</div>
										</div>
									</div>

									<!-- 操作按钮 -->
									<div class="questioncategory-item-actions">
										<php>echo QuestioncategoryStatBtn($v['id'], $v['status'], $v['rec']);</php>
									</div>
								</div>
								<php>}</php>
							</div>
						</div>

						<!-- 分页信息 -->
						<div class="questioncategory-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem;">
							{$page}
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>

<!-- 添加分类模态框 -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" role="dialog" aria-labelledby="addCategoryModalLabel">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title" id="addCategoryModalLabel">
					<i class="fa fa-plus"></i> 添加问题分类
				</h4>
			</div>
			<div class="modal-body" style="padding: 2rem;">
				<form id="addCategoryForm" class="questioncategory-add-form">
					<!-- 分类名称 -->
					<div class="questioncategory-add-form-group">
						<label class="questioncategory-add-form-label">
							<div class="questioncategory-add-form-label-icon">
								<i class="fa fa-tag"></i>
							</div>
							分类名称
						</label>
						<input type="text" name="title" class="questioncategory-add-form-input" placeholder="请输入分类名称..." />
						<div class="questioncategory-add-form-help">
							<i class="fa fa-info-circle"></i>
							请输入简洁明了的分类名称，建议不超过20个字符
						</div>
					</div>

					<!-- 上级分类 -->
					<div class="questioncategory-add-form-group">
						<label class="questioncategory-add-form-label">
							<div class="questioncategory-add-form-label-icon">
								<i class="fa fa-sitemap"></i>
							</div>
							上级分类
						</label>
						<select name="pid" class="questioncategory-add-form-select">
							<option value="0">顶级分类</option>
							<!-- 这里需要动态加载分类列表 -->
						</select>
						<div class="questioncategory-add-form-help">
							<i class="fa fa-info-circle"></i>
							选择此分类的上级分类，如果是顶级分类请选择"顶级分类"
						</div>
					</div>

					<!-- 排序 -->
					<div class="questioncategory-add-form-group">
						<label class="questioncategory-add-form-label">
							<div class="questioncategory-add-form-label-icon">
								<i class="fa fa-sort-numeric-asc"></i>
							</div>
							排序
						</label>
						<input type="number" name="sort" class="questioncategory-add-form-input" value="1" min="1" max="999" />
						<div class="questioncategory-add-form-help">
							<i class="fa fa-info-circle"></i>
							数字越小排序越靠前，建议设置为1-999之间的整数
						</div>
					</div>

					<!-- 状态 -->
					<div class="questioncategory-add-form-group">
						<label class="questioncategory-add-form-label">
							<div class="questioncategory-add-form-label-icon">
								<i class="fa fa-toggle-on"></i>
							</div>
							分类状态
						</label>
						<div class="questioncategory-add-status-options">
							<div class="questioncategory-add-status-option">
								<input type="radio" name="status" value="1" id="add_status_1" checked>
								<label for="add_status_1" class="questioncategory-add-status-label">
									<i class="fa fa-check-circle"></i>
									<span>上架</span>
								</label>
							</div>
							<div class="questioncategory-add-status-option">
								<input type="radio" name="status" value="0" id="add_status_0">
								<label for="add_status_0" class="questioncategory-add-status-label">
									<i class="fa fa-edit"></i>
									<span>草稿</span>
								</label>
							</div>
						</div>
						<div class="questioncategory-add-form-help">
							<i class="fa fa-info-circle"></i>
							上架状态的分类将对用户可见，草稿状态仅管理员可见
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer" style="padding: 1.5rem 2rem; border-top: 1px solid #e2e8f0;">
				<button type="button" class="btn btn-default" data-dismiss="modal">
					<i class="fa fa-times"></i> 取消
				</button>
				<button type="button" class="btn btn-success" onclick="submitAddCategory()">
					<i class="fa fa-save"></i> 保存分类
				</button>
			</div>
		</div>
	</div>
</div>

<style>
	/* 添加分类表单样式 */
	.questioncategory-add-form {
		max-width: 100%;
	}

	.questioncategory-add-form-group {
		margin-bottom: 2rem;
	}

	.questioncategory-add-form-group:last-child {
		margin-bottom: 0;
	}

	.questioncategory-add-form-label {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-size: 1.5rem;
		font-weight: 600;
		color: #374151;
		margin-bottom: 0.75rem;
	}

	.questioncategory-add-form-label-icon {
		width: 1.5rem;
		height: 1.5rem;
		background: linear-gradient(135deg, #10b981 0%, #059669 100%);
		border-radius: 0.25rem;
		display: flex;
		align-items: center;
		justify-content: center;
		color: white;
		font-size: 0.75rem;
	}

	.questioncategory-add-form-input,
	.questioncategory-add-form-select {
		width: 100%;
		padding: 1rem 1.25rem;
		border: 2px solid #e2e8f0;
		border-radius: 0.75rem;
		font-size: 1.5rem;
		font-family: inherit;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		background: #f8fafc;
		color: #374151;
	}

	.questioncategory-add-form-input:focus,
	.questioncategory-add-form-select:focus {
		outline: none;
		border-color: #10b981;
		box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
		background: white;
	}

	.questioncategory-add-form-help {
		font-size: 1.25rem;
		color: #6b7280;
		margin-top: 0.5rem;
		display: flex;
		align-items: center;
		gap: 0.25rem;
	}

	.questioncategory-add-form-help i {
		color: #10b981;
	}

	.questioncategory-add-status-options {
		display: flex;
		gap: 1rem;
		flex-wrap: wrap;
	}

	.questioncategory-add-status-option {
		position: relative;
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.75rem 1.25rem;
		border: 2px solid #e2e8f0;
		border-radius: 0.75rem;
		background: #f8fafc;
		cursor: pointer;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		flex: 1;
		min-width: 120px;
	}

	.questioncategory-add-status-option:hover {
		border-color: #10b981;
		background: rgba(16, 185, 129, 0.05);
	}

	.questioncategory-add-status-option input[type="radio"] {
		display: none;
	}

	.questioncategory-add-status-label {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-size: 1.25rem;
		font-weight: 600;
		color: #374151;
		cursor: pointer;
		margin: 0;
		width: 100%;
		justify-content: center;
	}

	.questioncategory-add-status-option input[type="radio"]:checked + .questioncategory-add-status-label {
		color: #10b981;
	}

	.questioncategory-add-status-option input[type="radio"]:checked {
		& + .questioncategory-add-status-label {
			color: #10b981;
		}
		& ~ .questioncategory-add-status-option {
			border-color: #10b981;
			background: rgba(16, 185, 129, 0.1);
		}
	}

	.questioncategory-add-status-option.selected {
		border-color: #10b981 !important;
		background: rgba(16, 185, 129, 0.1) !important;
		color: #047857 !important;
	}

	.modal-content {
		border-radius: 1rem !important;
		border: none !important;
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
	}

	.modal-header {
		border-radius: 1rem 1rem 0 0 !important;
		border-bottom: none !important;
	}

	.modal-footer {
		border-radius: 0 0 1rem 1rem !important;
	}

	.modal-footer .btn {
		padding: 0.75rem 1.5rem;
		font-size: 1.5rem;
		font-weight: 600;
		border-radius: 0.75rem;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.modal-footer .btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
	}

	.modal-footer .btn-success {
		background: linear-gradient(135deg, #10b981 0%, #059669 100%);
		border: none;
		color: white;
	}

	.modal-footer .btn-success:hover {
		color: white;
	}

	/* 焦点状态增强 */
	.questioncategory-add-form-group.focused .questioncategory-add-form-label {
		color: #10b981 !important;
	}

	.questioncategory-add-form-group.focused .questioncategory-add-form-label-icon {
		background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
		transform: scale(1.1);
	}

	/* 模态框动画 */
	.modal.fade .modal-dialog {
		transform: translateY(-50px);
		transition: transform 0.3s ease-out;
	}

	.modal.fade.in .modal-dialog {
		transform: translateY(0);
	}

	/* 表单验证错误状态 */
	.questioncategory-add-form-input.error,
	.questioncategory-add-form-select.error {
		border-color: #ef4444 !important;
		box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
	}

	.questioncategory-add-form-help.error {
		color: #ef4444 !important;
	}

	.questioncategory-add-form-help.error i {
		color: #ef4444 !important;
	}
</style>

<script>
	$(document).ready(function() {
		// 设置导航标签active状态的函数
		function setActiveNavTab() {
			// 移除所有active状态
			$('.questioncategory-index-nav-link').removeClass('active');

			// 获取当前URL参数
			var urlParams = new URLSearchParams(window.location.search);
			var status = urlParams.get('status');

			// 根据status参数设置对应的active状态
			if (status === '1') {
				// 已上架状态
				$('.quick-filter[data-filter="published"]').addClass('active');
			} else if (status === '0') {
				// 草稿状态
				$('.quick-filter[data-filter="draft"]').addClass('active');
			} else if (status === '2') {
				// 已禁用状态
				$('.quick-filter[data-filter="disabled"]').addClass('active');
			} else {
				// 默认状态（全部）
				$('.questioncategory-index-nav-link').first().addClass('active');
			}
		}

		// 快速筛选功能
		$('.quick-filter').click(function(e) {
			e.preventDefault();
			var $this = $(this);
			var filter = $this.data('filter');
			var currentUrl = window.location.href.split('?')[0];
			var newUrl = currentUrl;

			// 立即更新视觉状态，提供即时反馈
			$('.questioncategory-index-nav-link').removeClass('active');
			$this.addClass('active');

			// 添加加载状态
			var originalHtml = $this.html();
			$this.html('<i class="fa fa-spinner fa-spin questioncategory-index-nav-icon"></i><span>加载中...</span>');

			// 获取当前的搜索参数
			var urlParams = new URLSearchParams(window.location.search);
			var searchParams = new URLSearchParams();

			// 保留搜索关键词等其他参数
			if (urlParams.get('kw')) {
				searchParams.set('kw', urlParams.get('kw'));
			}
			if (urlParams.get('val')) {
				searchParams.set('val', urlParams.get('val'));
			}

			// 添加状态参数
			switch(filter) {
				case 'published':
					searchParams.set('status', '1');
					break;
				case 'draft':
					searchParams.set('status', '0');
					break;
				case 'disabled':
					searchParams.set('status', '2');
					break;
				default:
					// 不添加status参数，显示全部
					break;
			}

			// 构建最终URL
			var paramString = searchParams.toString();
			if (paramString) {
				newUrl += '?' + paramString;
			}

			// 延迟跳转，让用户看到加载状态
			setTimeout(function() {
				window.location.href = newUrl;
			}, 300);
		});

		// 处理"全部分类"标签的点击事件
		$('.questioncategory-index-nav-link').not('.quick-filter').click(function(e) {
			var $this = $(this);
			var href = $this.attr('href');

			// 如果是指向当前页面的链接，处理active状态
			if (href && href.indexOf('Questioncategory/index') !== -1) {
				e.preventDefault();

				// 立即更新视觉状态
				$('.questioncategory-index-nav-link').removeClass('active');
				$this.addClass('active');

				// 添加加载状态
				var originalHtml = $this.html();
				$this.html('<i class="fa fa-spinner fa-spin questioncategory-index-nav-icon"></i><span>加载中...</span>');

				// 延迟跳转到不带参数的页面
				setTimeout(function() {
					window.location.href = href;
				}, 300);
			}
		});

		// 列表项悬停效果增强
		$('.questioncategory-list-item').hover(
			function() {
				$(this).addClass('hover-effect');
			},
			function() {
				$(this).removeClass('hover-effect');
			}
		);

		// 空状态处理
		var totalItems = $('.questioncategory-list-item').length;
		if (totalItems === 0) {
			var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-folder-open"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无分类数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的问题分类。</p><a href="' + '{:U("Questioncategory/edit")}' + '" class="btn btn-primary" style="padding: 0.75rem 1.5rem; font-size: 1.5rem;"><i class="fa fa-plus"></i> 添加分类</a></div>');
			$('.questioncategory-index-list-container .questioncategory-list-body').append($emptyState);
		}

		// 延迟执行active状态设置，确保DOM完全加载
		setTimeout(function() {
			setActiveNavTab();
		}, 100);

		// 保持原有的状态切换功能
		$('.js_status').click(function() {
			var that = $(this),
				url = that.attr('url');
			$.get(url, function(data) {
				window.location.reload();
			});
		});

		// 显示字段错误
		function showFieldError(selector, message) {
			var $field = $(selector);
			var $help = $field.siblings('.questioncategory-add-form-help');

			$field.addClass('error');
			$help.addClass('error');
			$help.html('<i class="fa fa-exclamation-triangle"></i> ' + message);
			$field.focus();

			layer.msg(message, {icon: 2});

			// 3秒后恢复正常状态
			setTimeout(function() {
				$field.removeClass('error');
				$help.removeClass('error');
				if (selector === 'input[name="title"]') {
					$help.html('<i class="fa fa-info-circle"></i> 请输入简洁明了的分类名称，建议不超过20个字符');
				} else if (selector === 'input[name="sort"]') {
					$help.html('<i class="fa fa-info-circle"></i> 数字越小排序越靠前，建议设置为1-999之间的整数');
				}
			}, 3000);
		}
	});

	// 搜索面板切换功能
	function toggleSearchPanel() {
		var panel = document.getElementById('searchPanel');
		if (panel.classList.contains('show')) {
			panel.classList.remove('show');
		} else {
			panel.classList.add('show');
		}
	}

	// 显示添加分类模态框
	function showAddCategoryModal() {
		// 重置表单
		$('#addCategoryForm')[0].reset();

		// 重置状态选择
		$('.questioncategory-add-status-option').removeClass('selected');
		$('#add_status_1').prop('checked', true);
		$('#add_status_1').closest('.questioncategory-add-status-option').addClass('selected');

		// 加载上级分类选项
		loadParentCategories();

		// 显示模态框
		$('#addCategoryModal').modal('show');
	}

	// 加载上级分类选项
	function loadParentCategories() {
		// 这里可以通过AJAX加载分类列表，暂时使用静态数据
		var $select = $('select[name="pid"]');
		$select.html('<option value="0">顶级分类</option>');

		// 如果需要动态加载，可以使用以下代码：
		/*
		$.ajax({
			url: '{:U("Questioncategory/getParentList")}',
			type: 'GET',
			dataType: 'json',
			success: function(data) {
				$select.html('<option value="0">顶级分类</option>');
				if (data && data.length > 0) {
					$.each(data, function(index, item) {
						$select.append('<option value="' + item.id + '">' + item.title + '</option>');
					});
				}
			},
			error: function() {
				console.log('加载上级分类失败');
			}
		});
		*/
	}

	// 提交添加分类表单
	function submitAddCategory() {
		var title = $('input[name="title"]').val().trim();
		var pid = $('select[name="pid"]').val();
		var sort = $('input[name="sort"]').val();
		var status = $('input[name="status"]:checked').val();

		// 清除之前的错误状态
		$('.questioncategory-add-form-input, .questioncategory-add-form-select').removeClass('error');
		$('.questioncategory-add-form-help').removeClass('error');

		// 表单验证
		if (!title) {
			showFieldError('input[name="title"]', '请输入分类名称');
			return false;
		}

		if (title.length > 50) {
			showFieldError('input[name="title"]', '分类名称不能超过50个字符');
			return false;
		}

		if (!sort || sort < 1 || sort > 999) {
			showFieldError('input[name="sort"]', '排序必须是1-999之间的整数');
			return false;
		}

		if (!status && status !== '0') {
			layer.msg('请选择分类状态', {icon: 2});
			return false;
		}

		// 显示加载状态
		var $submitBtn = $('.modal-footer .btn-success');
		var originalText = $submitBtn.html();
		$submitBtn.prop('disabled', true);
		$submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 保存中...');

		// 提交表单数据
		$.ajax({
			url: '{:U("Questioncategory/edit")}',
			type: 'POST',
			data: {
				title: title,
				pid: pid,
				sort: sort,
				status: status,
				submit: '提交'
			},
			timeout: 30000,
			success: function(response) {
				console.log('添加分类成功:', response);

				// 检查响应内容
				var responseText = typeof response === 'string' ? response : JSON.stringify(response);

				if (responseText.includes('成功') || responseText.includes('success')) {
					layer.msg('添加分类成功！', {icon: 1, time: 2000});
					setTimeout(function() {
						$('#addCategoryModal').modal('hide');
						window.location.reload();
					}, 2000);
				} else if (responseText.includes('错误') || responseText.includes('失败')) {
					layer.msg('添加分类失败，请重试', {icon: 2});
				} else {
					// 默认认为成功
					layer.msg('添加分类成功！', {icon: 1, time: 2000});
					setTimeout(function() {
						$('#addCategoryModal').modal('hide');
						window.location.reload();
					}, 2000);
				}
			},
			error: function(xhr, status, error) {
				console.error('添加分类失败:', {
					status: status,
					error: error,
					responseText: xhr.responseText
				});

				var errorMsg = '网络错误，请重试';
				if (status === 'timeout') {
					errorMsg = '请求超时，请重试';
				} else if (xhr.status === 404) {
					errorMsg = '页面不存在，请联系管理员';
				} else if (xhr.status === 500) {
					errorMsg = '服务器错误，请重试';
				}

				layer.msg(errorMsg, {icon: 2});
			},
			complete: function() {
				// 恢复按钮状态
				$submitBtn.prop('disabled', false);
				$submitBtn.html(originalText);
			}
		});
	}

	// 状态选择点击事件
	$('.questioncategory-add-status-option').on('click', function() {
		var $input = $(this).find('input[type="radio"]');
		$input.prop('checked', true);

		// 更新同组其他选项的样式
		$('.questioncategory-add-status-option').removeClass('selected');
		$(this).addClass('selected');
	});

	// 输入框焦点效果
	$('.questioncategory-add-form-input, .questioncategory-add-form-select').on('focus', function() {
		$(this).closest('.questioncategory-add-form-group').addClass('focused');
	}).on('blur', function() {
		$(this).closest('.questioncategory-add-form-group').removeClass('focused');
	});

	// 字符计数
	$('input[name="title"]').on('input', function() {
		var length = $(this).val().length;
		var maxLength = 50;
		var $help = $(this).siblings('.questioncategory-add-form-help');

		if (length > maxLength * 0.8) {
			$help.html('<i class="fa fa-exclamation-triangle" style="color: #f59e0b;"></i> 分类名称长度: ' + length + '/' + maxLength + ' 字符');
		} else {
			$help.html('<i class="fa fa-info-circle"></i> 请输入简洁明了的分类名称，建议不超过20个字符');
		}
	});
</script>

<include file="block/footer" />
