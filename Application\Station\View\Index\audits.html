<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>中才国科服务站</title>
    <style>
        /* 微信风格样式 */
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
            background: #f8f8f8;
        }

        /* 移动端容器 */
        .mobile-container {
            max-width: 450px;
            margin: 168px auto;
            display: block;
        }

        /* 桌面提示 */
        .desktop-alert {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            padding: 20px;
            text-align: center;
            z-index: 9999;
        }

        /* 微信顶部导航栏 */
        .weui-navbar {
            background: #07c160;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 18px;
        }

        /* 审核提示区域 */
        .alert-box {
            margin: 20px;
            background: white;
            border-radius: 8px;
            padding: 30px 15px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .alert-icon {
            font-size: 50px;
            color: #ffb300;
            margin-bottom: 15px;
        }

        .alert-text {
            color: #333;
            font-size: 18px;
            line-height: 1.6;
        }

        /* 版权信息 */
        .copyright {
            text-align: center;
            color: #999;
            font-size: 12px;
            padding: 20px;
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 450px;
        }

        @media only screen and (min-width: 768px) {
            .mobile-container {
                display: none;
            }
            .desktop-alert {
                display: block;
                padding-top: 100px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <!-- 桌面访问提示 -->
    <div class="desktop-alert">
        ⚠ 请使用手机访问本页面以获得最佳体验
    </div>

    <!-- 移动端内容 -->
    <div class="mobile-container">
        <div class="weui-navbar">服务站审核状态</div>
        
        <div class="alert-box">
            <div class="alert-icon">⏳</div>
            <div class="alert-text">
                <strong style="color: #e64340; font-size: 20px;">服务站正在审核中！</strong><br><br>
                预计3-5个工作日内完成审核
            </div>
        </div>

        <div class="copyright">
            copyright@ 中才国科
        </div>
    </div>

    <script>
        // 设备检测（可选）
        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }
        
        if (!isMobile()) {
            document.querySelector('.desktop-alert').style.display = 'block';
        }
    </script>
</body>
</html>