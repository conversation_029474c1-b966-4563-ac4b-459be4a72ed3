<?php
namespace Common\Controller;
use Think\Controller;

/**
 * 项目公用控制器
 */

class BaseController extends Controller {

    public function _initialize()
    {

    }

    /**
     * 验证码输出
     */
	public function verifyCode()
	{
        import('Util.Securimage');
        $options = array(
            'image_width' => 180,
            'image_height' => 35,
            'text_scale' => 0.6, //字体比例
        );
        $img = new \Securimage($options);
        $img->show();
	}

    /**
     * 验证验证码是否正确
     * @param string $code
     * @return boolean
     */
    public function checkCode($code, $namespace = null)
    {
        import('Util.Securimage');
        $img = new \Securimage();
        $namespace !== null && $img->namespace = $namespace;
        $chk = $img->check($code);
        return $chk;
    }

    protected function page($total_size = 1, $page_size = 10, $current_page = 1, $listrows = 6, $pageparam = '', $pagelink = '', $static = 1)
    {
        import("Org.Util.Page");
        if (is_numeric(I('get.psz'))) {
            $page_size = (int)I('get.psz');
        }
        if ($page_size == 0 || $page_size > 100) {
            $page_size = C("PAGE_LISTROWS");
        }
        if (empty($pageparam)) {
            $pageparam = C("VAR_PAGE");
        }
        $page = new \Page($total_size, $page_size, $current_page, $listrows, $pageparam, $pagelink, $static);
        $page->setLinkWraper('li');

        // 使用更现代的分页样式
        $page->SetPager(
            'default',
            '<div><ul class="pagination pagination-centered">{first}{prev}{liststart}{list}{listend}{next}{last}</ul></div>',
            array(
                "listlong" => "5",
                "first" => '<i class="iconfont" style="font-size: 12px;">首页</i>',
                "last" => '<i class="iconfont" style="font-size: 12px;">末页</i>',
                "prev" => '<i class="iconfont" style="font-size: 14px;">上一页</i>',
                "next" => '<i class="iconfont" style="font-size: 14px;">下一页</i>',
                "list" => "*",
                "currentclass" => "active",
                "disabledclass" => "disabled"
            )
        );

        return $page;
    }

}