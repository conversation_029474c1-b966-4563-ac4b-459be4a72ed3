<?php
/**
 * 工具类控制器
 */

namespace Pub\Controller;

use Think\Controller;
class FileController extends Controller
{

    public function _initialize() {

    }

    public function files() {
        import('Vendor.Filewen');
        $upload = new \Filewen();
        $upload->maxSize   =     0 ;// 设置附件上传大小
        $upload->exts      =     array('xlsx', 'mp4', 'mp3', 'xls', 'csv', 'rar', 'pdf', 'zip', 'png', 'jpg', 'doc', 'docx');// 设置附件上传类型
//$upload->saveName  =	 '';
        $upload->rootPath  =     'data/Material/'; // 设置附件上传根目录
        $upload->subName   =     array('date','Ymd'); // 子目录保存规则
// 上传文件
        $info   =   $upload->upload($_FILES);
        if(!$info){
            $res = [ 'error' => 1, 'message' => $upload->getError(), ];
            frameCallback(I("request.callback"), json_encode($res));
        }else{
            $info = $info['file'];
            $res = [ 'error' => 0,
                'message' => '',
                'path' => $upload->rootPath.$info['savepath'],
                'filename' => $upload->rootPath.$info['savepath'].$info['savename'],
                'url' => $upload->rootPath.$info['savepath'].$info['savename'],
            ];
            frameCallback(I("request.callback"), json_encode($res));
        }
    }
}
