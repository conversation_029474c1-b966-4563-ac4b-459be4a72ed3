<include file="block/hat" />
<script type="text/javascript" src="__ROOT__/static/js/lib/jquery-ui-1.10.3.min.js"></script>
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<ul class="nav nav-tabs">
				<li class="active"><a>权限设置</a></li>
			</ul>
			<style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
<div class="main">
	<form action="{:U('rbac/auth_post')}" method="post" class="form-horizontal form js-ajax-form" enctype="multipart/form-data" id="form1">
		<div class="panel panel-default">
			<div class="table_full">
				<table width="100%" id="dnd-example" style="margin:10px">
				{$categorys}
				</table>
			</div>
		</div>
		<div class="form-group col-sm-12">
			<input type="hidden" name="roleid" value="{$roleid}" />
			<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1 js-ajax-submit" />
		</div>
	</form>
</div>
		</div>
	</div>
</div>
<script src="__ROOT__/static/js/prime/common.js"></script>
<link rel="stylesheet" href="__ROOT__/static/js/app/layer/skin/layer.css" />
<script>
require(['layer']);
$(function(){
	Wind.css('treeTable');
	Wind.use('treeTable', function () {
		$("#dnd-example").treeTable({
			indent: 20
		});
	});
});
function checknode(obj) {
	var chk = $("input[type='checkbox']");
	var count = chk.length;
	var num = chk.index(obj);
	var level_top = level_bottom = chk.eq(num).attr('level');
	for (var i = num; i >= 0; i--) {
		var le = chk.eq(i).attr('level');
		if (eval(le) < eval(level_top)) {
			chk.eq(i).attr("checked", true);
			var level_top = level_top - 1;
		}
	}
	for (var j = num + 1; j < count; j++) {
		var le = chk.eq(j).attr('level');
		if (chk.eq(num).attr("checked") == "checked") {
			if (eval(le) > eval(level_bottom)){
				chk.eq(j).attr("checked", true);
			}
			else if (eval(le) == eval(level_bottom)){
				break;
			}
		} else {
			if (eval(le) > eval(level_bottom)){
				chk.eq(j).attr("checked", false);
			}else if(eval(le) == eval(level_bottom)){
				break;
			}
		}
	}
}
</script>
<include file="block/footer" />