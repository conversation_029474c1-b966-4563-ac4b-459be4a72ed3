<?php

namespace Home\Controller;

use Common\Controller\WController;
use \LaneWeChat\Core as WE;
use Util\AliSms;

class IndexController extends WController
{

    public function _initialize() {
        parent::_initialize();
        $this->login();
        $openid = session('openid');
        $userRow = D("User")->where(['openid' => $openid, 'service_id' => 2])->find();
        if (!$userRow) {
            session(null);
            return $this->redirect(U('/index/index'));
            die;
        }
        $this->userRow = $userRow;
    }


    public function indextwo() {
        //判断用户是否注册
        if (empty($this->userRow['mobile'])) {
            return $this->redirect(U('/index/logins'));
        }
        $this->display();
    }

    public function logins() {
        //判断用户是否注册
        if (!empty($this->userRow['mobile'])) {
            return $this->redirect(U('/index/index'));
        }

        if (IS_POST) {
            $data = I('post.');
            $mobile = $data['mobile'];
            $code = $data['code'];
            if (empty($data['mobile'])) $this->error('参数错误');
            if (empty($code)) $this->error('验证码未填写');
            if ($code == session('time_login_job_' . $mobile)) {
                $updateData = [
                    'id' => $this->userRow['id'],
                    'mobile' => $mobile,
                ];
                D("User")->save($updateData);
                D("User")->where(['id' => $this->userRow['id']])->save(['last_login_time' => time()]);
                $this->redirect('job/index');die;
            } else {
                $this->error('验证码未填写');
            }
        }
        $this->display();
    }

    // 发送手机验证码
    public function logincode()
    {
        $mobile = I("get.mobile");
        $ret['code'] = 0;
        $lasts = session('time_login_job_' . $mobile);
        if ($lasts and $lasts > time() - 60) {
            $ret = [
                'code' => 2,
                'msg' => '验证码已发送，请稍后请求！',
            ];
        } else {
            $code = mt_rand(10000, 99999);
            session('code_login_job_' . $mobile, $code);
            session('time_login_job_' . $mobile, time());
            $alisms = new AliSms();
            $result = $alisms->sendSms($code, $mobile);
            if (!$result) {
                $ret = ['code' => 3, "msg" => '验证码发送失败！'];
            }
        }
        echo json_encode($ret);
        die;
    }

    /**
     * 用户填写简历
     */
    public function index(){
        $code = I('get.i', '');
        if (empty($code)) $this->error('填写参数错误!!');
        $ids = D("Project")->dehash($code);
        $serviceStationUserJoinRow = D("ServiceStationUserJoin")->where(['id' => $ids])->find();
        if (!$serviceStationUserJoinRow || $serviceStationUserJoinRow['visit_user_id'] != 0) {
            if ($this->userRow['id'] != $serviceStationUserJoinRow['visit_user_id']) {
                $this->error('当前链接信息已被用户绑定');
            }
        }
        if ($this->userRow['service_station_id'] == 0) {
            D("User")->where(['id' => $this->userRow['id']])->save(['service_station_id' => $serviceStationUserJoinRow['service_station_id']]);
            $this->userRow['service_station_id'] = $serviceStationUserJoinRow['service_station_id'];
        }
        $obj = D("TempQrcode");
        $endTime = time()-1600;
        $tempQrcodeRow = $obj->where(['service_station_id'  => $serviceStationUserJoinRow['service_station_id'], 'service_id' => 2, 'created' => ['elt' => $endTime]])->find();
       if (!$tempQrcodeRow) {
           $tempQrcodeRow = $this->succTempQrcode($serviceStationUserJoinRow);
       }
       if (!$tempQrcodeRow) {
           $this->error('生成二维码失败，请刷新重试！！');
       }
       $this->assign('tempQrcodeRow', $tempQrcodeRow);
        $this->display();
    }

    public function succTempQrcode($serviceStationUserJoinRow) {
        $obj = D("TempQrcode");
        vendor('LaneWeChat.lanewechat');
        M()->startTrans();
        $service_id = 2;
        $max_scene_id = $obj->where(['service_id' => $service_id])->max('scene_id');
        if ($qrid = $obj->add([
            'service_id' => $service_id,
            'service_station_id' => $serviceStationUserJoinRow['service_station_id'],
            'scene_id' => $max_scene_id + 1,
            'created' => time(),
        ])) {
            $service = D('Service')->find($service_id);
            WE\Base::init([
                'WX_TOKEN' => $service['wx_token'],
                'WX_APPID' => $service['appid'],
                'WX_APPSECRET' => $service['secret'],
            ]);
            $resTicket = WE\Popularize::createTicket(1, 1800, $max_scene_id + 1);
            if (isset($resTicket['ticket'])) {
                $obj->save([
                    'id' => $qrid,
                    'ticket' => $resTicket['ticket'],
                    'url' => $resTicket['url'],
                    'use_type' => 1,
                    'relation_id' => $serviceStationUserJoinRow['id'],
                ]);
                M()->commit();
                return $obj->where(['id' => $qrid])->find();
            }
        }
        M()->rollback();
    }

    /**
     * 用户简历列表
     */
    public function userjob() {
        $obj = D("UserJob");
        $userRow = $obj->where(['user_id' => $this->userRow['id']])->find();
        if ($userRow) {
            return $this->redirect(U("job/index"));die;
        } else {
            return $this->redirect(U("index/logins"));die;
        }
        die;
        if (IS_POST) {
            $id = 0;
            $obj = D("UserJob");
            if ($data = $obj->create()) {
                // 处理照片上传

                if (!empty($_FILES['photo_path']['name'])) {
                    $upload = new \Think\Upload();
                    $upload->maxSize = 1048576; // 1MB
                    $upload->exts = array('jpg', 'png', 'jpeg');
                    $upload->rootPath = SITE_PATH . '/data/';
                    $upload->savePath = 'Job/';
                    $info = $upload->uploadOne($_FILES['photo_path']);
                    if ($info) {
                        $data['photo_path'] = $info['savepath'] . $info['savename'];
                    } else {
                        $this->error($upload->getError());
                    }
                }
                $insertId = 1;
                $dataTow = I('post.');

                // 保存数据
                if (!$id) {
                    $data['user_id'] = $this->userRow['id'];
                    $data['service_station_id'] = $this->userRow['service_station_id'];
                    $data['create_time'] = time();
                    $insertId = $obj->add($data);
                    if ($insertId) {
                        $dataTow = I('post.');
                        //教育
                        $educationDataAll[] =  [
                            'user_job_id' => $insertId,
                            'start_date' => $dataTow['start_date'],
                            'end_date' => $dataTow['end_date'],
                            'school_name' => $dataTow['school_name'],
                            'major' => $dataTow['major'],
                            'witness' => $dataTow['witness']
                        ];
                        for($i=2;$i<=10;$i++) {
                            if (isset($dataTow['school_name'.$i])) {
                                $educationDataAll[] =  [
                                    'user_job_id' => $insertId,
                                    'start_date' => $dataTow['start_date'.$i],
                                    'end_date' => $dataTow['end_date'.$i],
                                    'school_name' => $dataTow['school_name'.$i],
                                    'major' => $dataTow['major'.$i],
                                    'witness' => $dataTow['witness'.$i]
                                ];
                            } else {
                                break;
                            }
                        }
                        if ($educationDataAll) {
                            D("Education")->addAll($educationDataAll);
                        }
                        //工作经历
                        $wordDataAll[] = [
                            'user_job_id' => $insertId,
                            'start_date' => $dataTow['work_start_date'],
                            'end_date' => $dataTow['work_end_date'],
                            'company_name' => $dataTow['company_name'],
                            'position' => $dataTow['relationship_position'],
                            'leave_reason' => $dataTow['leave_reason']
                        ];
                        for($i=2;$i<=10;$i++) {
                            if (isset($dataTow['company_name'.$i])) {
                                $wordDataAll[] =  [
                                    'user_job_id' => $insertId,
                                    'start_date' => $dataTow['start_date'.$i],
                                    'end_date' => $dataTow['end_date'.$i],
                                    'company_name' => $dataTow['company_name'.$i],
                                    'position' => $dataTow['relationship_position'.$i],
                                    'leave_reason' => $dataTow['leave_reason'.$i]
                                ];
                            } else {
                                break;
                            }
                        }
                        if ($wordDataAll) {
                            D("WorkExperience")->addAll($wordDataAll);
                        }

                        //家庭成员
                        $familyMembersDataAll[] = [
                            'user_job_id' => $insertId,
                            'relationship' => $dataTow['relationship'],
                            'full_name' => $dataTow['full_name'],
                            'work_unit' => $dataTow['work_unit'],
                            'relationship_position' => $dataTow['relationship_position'],
                            'contact' => $dataTow['contact']
                        ];
                        for($i=2;$i<=10;$i++) {
                            if (isset($dataTow['full_name'.$i])) {
                                $familyMembersDataAll[] =  [
                                    'user_job_id' => $insertId,
                                    'relationship' => $dataTow['relationship'.$i],
                                    'full_name' => $dataTow['full_name'.$i],
                                    'work_unit' => $dataTow['work_unit'.$i],
                                    'relationship_position' => $dataTow['relationship_position'.$i],
                                    'contact' => $dataTow['contact'.$i]
                                ];
                            } else {
                                break;
                            }
                        }
                        if ($familyMembersDataAll) {
                            D("FamilyMembers")->addAll($familyMembersDataAll);
                        }

                        //技能
                        $skillsCertificatesDataAll[] = [
                            'user_job_id' => $insertId,
                            'certificate_type' => $dataTow['is_afraid_heights'],
                            'certificate_name' => $dataTow['certificate_name'],
                            'level' => $dataTow['level'],
                            'get_date' => $dataTow['get_date'],
                        ];
                        for($i=2;$i<=10;$i++) {
                            if (isset($dataTow['full_name'.$i])) {
                                $skillsCertificatesDataAll[] =  [
                                    'user_job_id' => $insertId,
                                    'certificate_type' => $dataTow['is_afraid_heights'.$i],
                                    'certificate_name' => $dataTow['certificate_name'.$i],
                                    'level' => $dataTow['level'.$i],
                                    'get_date' => $dataTow['get_date'.$i],
                                ];
                            } else {
                                break;
                            }
                        }
                        if ($skillsCertificatesDataAll) {
                            D("SkillsCertificates")->addAll($skillsCertificatesDataAll);
                        }
                    }
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("index/customer"));
                exit;
            } else {
                // 表单验证失败，保留用户填写的数据
                $this->assign('row', I('post.')); // 将 POST 数据传递到视图
                $this->error($obj->getError()); // 抛出错误
                exit;
            }
        }
        $this->display();
    }

    /**
     * 添加企微
     */
    public function customer() {
        $userJobRow = D('UserJob')->where(['service_station_id' => $this->userRow['service_station_id'], 'user_id' => $this->userRow['id']])->find();
        if (!$userJobRow) {
            return $this->error('未知参数错误！！！！');
        }
        if ($this->userRow['is_customer'] == 0) {
            $customerRow = D("Customer")->where(['status' => 1])->order('id asc')->find();
            if ($customerRow) {
                D("Customer")->where(['id' => $customerRow['id']])->setInc('bind_num');
                D("User")->save(['id' => $this->userRow['id'], 'is_customer' => $customerRow['id']]);
            }
        } else {
            $customerRow = D("Customer")->where(['id' => $this->userRow['is_customer']])->order('id asc')->find();
        }
        $this->assign('customerRow', $customerRow);
        $this->display();
    }

    public function pagelist() {
        $this->display();
    }
}
