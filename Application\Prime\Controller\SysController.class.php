<?php

namespace Prime\Controller;
use Common\Controller\PrimeController;

class SysController extends PrimeController
{
    
    public function _initialize()
    {
        parent::_initialize();
            $this->uid = session('admin_id');
    }

    /**
     * 修改密码
     */
    public function chpwd()
    {
        if (IS_POST) {
            extract(I("post."));
            do {
                if (!$org || !$new || !$renew) {
                    $msg = "请将密码填写完整"; break;
                }
                if ($new != $renew) {
                    $msg = "两次密码输入不一致"; break;
                }
                $res = D("Users")->where(['id'=>$this->uid])->find();
                if($res['pwd'] != makePwd($org, $res['salt'])) {
                    $msg = "原始密码输入有误"; break;
                }
                D("Users")->where(['id'=>$this->uid])->save(['pwd' => makePwd($new, $res['salt'])]);
                $this->success('密码修改成功');exit;
            } while (false);

            if (!empty($msg)) {
                $this->error($msg);
            }
        }
        $this->display();
    }

    /**
     * 清空缓存
     */
    public function refresh()
    {
        $dir = ['Cache', 'Data', 'Temp'];
        foreach ($dir as $v) {
            clearDir(RUNTIME_PATH."/$v", true);
        }
        @unlink(RUNTIME_PATH.'common~runtime.php');
        $this->success('缓存已更新');
    }

    public function conf()
    {
        $where = ['show' => ['eq', 1], ['group'=>1]];
        $count = D('Conf')->where($where)->count();
        $page = $this->page($count, 15);
        $this->list = D('Conf')->where($where)->limit($page->firstRow . ',' . $page->listRows)->select();

        $list2 = D('Conf')->where(['group' => 2])->select();

        $prft = D('Conf')->C("DIST_PRFT");
        $share = D('Conf')->C("DIST_SHARE");
        $brkg = D('Conf')->C("DIST_BRKG");

        $this->assign(['prft' => $prft,
            'list2' => $list2,
            'share' => $share,
            ]);

        $this->page = $page->show();
        $this->display();
    
    }

    /**
     * 添加/编辑 配置项
     */
    public function conf_edit()
    {
        C("TOKEN_ON", true);
        $MConf = D("Conf");
        $id = intval(I("request.id"));
        if ($id) {
            $row = $MConf->find($id);
            if (! $row) $this->error('参数错误', U("sys/conf"));
            $this->assign("row", $row);
        }
        if (IS_POST) {
            if (! $data = $MConf->create()) {
                $this->error($MConf->getError());
            } else {
                if (! $id) {
                    $id = $MConf->add($data);
                } else {
                    $data['id'] = $id;
                    $MConf->save($data);
                }
                $MConf->init(true);
                $this->success('配置更新成功', U('sys/conf')); exit;
            }
        }
        $this->display();
    }

    /**
     * 平台费率配置页面
     */
    public function platform_rate_config()
    {
        // 获取当前平台费率
        $currentRate = D('ZsbPostPrice')->getPlatformRate();

        // 获取最后修改记录
        $lastLog = D('PlatformRateLog')->where(['execute_status' => 2])
            ->order('execute_time DESC')
            ->find();

        // 获取受影响的价格配置数量
        $affectedCount = D('ZsbPostPrice')->where(['status' => 1])->count();

        $this->assign([
            'currentRate' => $currentRate,
            'lastLog' => $lastLog,
            'affectedCount' => $affectedCount
        ]);

        $this->display();
    }

    /**
     * 费率修改处理
     */
    public function platform_rate_update()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $newRate = I('post.new_rate', 0, 'floatval');
        $executeType = 1; // 固定为立即执行
        $notifyUsers = I('post.notify_users', 1, 'intval'); // 是否通知用户

        // 验证费率值
        if ($newRate <= 0 || $newRate > 1) {
            $this->ajaxReturn(['status' => 0, 'msg' => '费率值必须在0-1之间']);
        }

        // 获取当前费率
        $currentRate = D('ZsbPostPrice')->getPlatformRate();

        if (abs($newRate - $currentRate) < 0.0001) {
            $this->ajaxReturn(['status' => 0, 'msg' => '新费率与当前费率相同，无需修改']);
        }

        // 检查是否有正在执行的任务
        $runningTask = D('PlatformRateLog')->where([
            'execute_status' => 1
        ])->find();

        if ($runningTask) {
            $this->ajaxReturn(['status' => 0, 'msg' => '存在正在执行的任务，请等待完成']);
        }

        // 获取操作人信息
        $operatorInfo = D('Users')->where(['id' => $this->uid])->find();

        // 创建费率修改日志
        $logData = [
            'old_rate' => $currentRate,
            'new_rate' => $newRate,
            'operator_id' => $this->uid,
            'operator_name' => $operatorInfo['username'] ?: 'Unknown',
            'execute_type' => 1, // 固定为立即执行
            'execute_status' => 1, // 立即执行设为执行中
            'notify_users' => $notifyUsers,
            'create_time' => time(),
            'remark' => '费率从' . ($currentRate * 100) . '%修改为' . ($newRate * 100) . '%'
        ];

        $logId = D('PlatformRateLog')->add($logData);

        if (!$logId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '创建任务失败']);
        }

        // 立即执行
        $this->ajaxReturn(['status' => 1, 'msg' => '任务已创建，正在执行', 'log_id' => $logId, 'execute_type' => 'immediate']);
    }

    /**
     * 批量计算执行
     */
    public function batch_recalculate()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $logId = I('post.log_id', 0, 'intval');

        if (!$logId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '任务ID不能为空']);
        }

        // 获取任务信息
        $logInfo = D('PlatformRateLog')->where(['id' => $logId])->find();

        if (!$logInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '任务不存在']);
        }

        if ($logInfo['execute_status'] != 0 && $logInfo['execute_status'] != 1) {
            $this->ajaxReturn(['status' => 0, 'msg' => '任务状态不正确']);
        }

        // 更新平台费率配置
        $confModel = D('Conf');
        $confModel->where(['name' => 'platform_rate'])->save(['value' => $logInfo['new_rate']]);

        // 开始批量重新计算
        $result = D('ZsbPostPrice')->batchRecalculatePrices($logInfo['new_rate'], $logId);

        if ($result['status']) {
            // 更新任务状态为成功
            D('PlatformRateLog')->where(['id' => $logId])->save([
                'execute_status' => 2,
                'execute_time' => time(),
                'affected_count' => $result['affected_count']
            ]);

            // 如果需要通知用户
            if ($logInfo['notify_users']) {
                $this->notifyStationUsers($logId, $result['affected_count']);
            }

            $this->ajaxReturn([
                'status' => 1,
                'msg' => '批量计算完成',
                'affected_count' => $result['affected_count'],
                'success_count' => $result['success_count'],
                'error_count' => $result['error_count']
            ]);
        } else {
            // 更新任务状态为失败
            D('PlatformRateLog')->where(['id' => $logId])->save([
                'execute_status' => 3,
                'execute_time' => time(),
                'remark' => $logInfo['remark'] . ' | 执行失败：' . $result['msg']
            ]);

            $this->ajaxReturn(['status' => 0, 'msg' => '批量计算失败：' . $result['msg']]);
        }
    }

    /**
     * 计算进度查询
     */
    public function recalculate_progress()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $logId = I('get.log_id', 0, 'intval');

        if (!$logId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '任务ID不能为空']);
        }

        // 获取任务信息
        $logInfo = D('PlatformRateLog')->where(['id' => $logId])->find();

        if (!$logInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '任务不存在']);
        }

        // 获取进度信息
        $progress = D('ZsbPostPrice')->getRecalculateProgress($logId);

        $this->ajaxReturn([
            'status' => 1,
            'execute_status' => $logInfo['execute_status'],
            'progress' => $progress
        ]);
    }

    /**
     * 获取当前平台费率（AJAX接口）
     */
    public function get_platform_rate()
    {
        // 添加 CORS 支持
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $rate = D('ZsbPostPrice')->getPlatformRate();

        $this->ajaxReturn([
            'status' => 1,
            'rate' => $rate,
            'rate_percent' => ($rate * 100) . '%'
        ]);
    }



    /**
     * 推送通知给服务站用户（服务站导向模式）
     */
    private function notifyStationUsers($logId, $affectedCount)
    {
        // 获取任务信息
        $logInfo = D('PlatformRateLog')->where(['id' => $logId])->find();

        if (!$logInfo) {
            return false;
        }

        // 获取受影响的服务站用户
        $affectedUsers = D('ZsbPostPrice')->getAffectedStationUsers($logInfo['new_rate']);

        if (empty($affectedUsers)) {
            return true;
        }

        $currentTime = time();
        $stationIds = [];

        // 收集所有受影响的服务站ID
        foreach ($affectedUsers as $user) {
            $stationIds[] = $user['station_id'];
        }

        // 批量更新服务站的通知标记（排除已永久关闭通知的服务站）
        if (!empty($stationIds)) {
            M('service_station')->where([
                'id' => ['in', $stationIds],
                'price_change_notify' => 0  // 只更新未永久关闭通知的服务站
            ])->save([
                'price_change_notify' => 1
            ]);
        }

        return true;
    }

    /**
     * 清除服务站价格变化通知
     */
    public function clear_price_change_notify()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $stationId = I('post.station_id', 0, 'intval');
        $permanent = I('post.permanent', 0, 'intval'); // 是否永久清除

        if (!$stationId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '服务站ID不能为空']);
        }

        if ($permanent) {
            // 永久清除：清除数据库中的通知标记
            $updateData = [
                'price_change_notify' => 0
            ];

            $result = M('service_station')->where([
                'id' => $stationId
            ])->save($updateData);

            if ($result !== false) {
                $this->ajaxReturn(['status' => 1, 'msg' => '通知已永久清除']);
            } else {
                $this->ajaxReturn(['status' => 0, 'msg' => '永久清除失败']);
            }
        } else {
            // 临时清除：使用会话记录，不修改数据库
            $sessionKey = 'price_notify_temp_cleared_' . $stationId;
            session($sessionKey, time()); // 记录临时清除的时间戳

            $this->ajaxReturn(['status' => 1, 'msg' => '通知已临时清除']);
        }
    }

    /**
     * 获取服务站价格变化通知状态
     */
    public function get_price_change_notify_status()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $stationId = I('get.station_id', 0, 'intval');

        if (!$stationId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '服务站ID不能为空']);
        }

        try {
            // 获取通知状态
            $stationInfo = M('service_station')->where([
                'id' => $stationId
            ])->field('price_change_notify,zsb_type')->find();

            if (!$stationInfo) {
                $this->ajaxReturn(['status' => 0, 'msg' => '服务站不存在']);
                return;
            }

            // 检查是否显示通知
            $hasNotify = false;

            // 只有服务站类型才可能显示通知
            if ($stationInfo['zsb_type'] == 1 && $stationInfo['price_change_notify']) {
                // 检查是否被临时清除（会话中记录）
                $sessionKey = 'price_notify_temp_cleared_' . $stationId;
                $tempCleared = session($sessionKey);

                if (!$tempCleared) {
                    // 没有被临时清除，显示通知
                    $hasNotify = true;
                }
            }

            // 获取当前平台费率
            $currentRate = D('ZsbPostPrice')->getPlatformRate();

            // 获取最新的费率变更时间
            $changeTime = '';
            $lastLog = D('PlatformRateLog')->where(['execute_status' => 2])
                ->order('execute_time DESC')
                ->find();

            if ($lastLog && $lastLog['execute_time']) {
                $changeTime = date('Y-m-d H:i', $lastLog['execute_time']);
            }

            $this->ajaxReturn([
                'status' => 1,
                'has_notify' => $hasNotify,
                'current_rate' => $currentRate,
                'rate_percent' => ($currentRate * 100) . '%',
                'change_time' => $changeTime,
                'debug' => '查询成功'
            ]);

        } catch (Exception $e) {
            $this->ajaxReturn([
                'status' => 0,
                'msg' => '查询失败: ' . $e->getMessage(),
                'debug' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取费率变化影响预览
     */
    public function preview_rate_change_impact()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $newRate = I('get.new_rate', 0, 'floatval');

        if ($newRate <= 0 || $newRate > 1) {
            $this->ajaxReturn(['status' => 0, 'msg' => '费率值必须在0-1之间']);
        }

        // 获取影响预览
        $impact = D('ZsbPostPrice')->previewRateChangeImpact($newRate);

        $this->ajaxReturn([
            'status' => 1,
            'impact' => $impact
        ]);
    }

    /**
     * 获取操作历史
     */
    public function get_operation_history()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $limit = I('get.limit', 10, 'intval');

        // 获取费率修改历史
        $history = D('PlatformRateLog')->getRateChangeHistory($limit);

        $this->ajaxReturn([
            'status' => 1,
            'data' => $history
        ]);
    }

}