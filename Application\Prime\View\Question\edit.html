<include file="block/hat" />
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<style type='text/css'>
				/* 现代化问答编辑页面样式 */
				.question-edit-wrapper {
					width: 100%;
				}

				/* 现代化页面标题 */
				.question-edit-header {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 2rem;
					overflow: hidden;
					position: relative;
					width: 100%;
				}

				.question-edit-header::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4px;
					background: linear-gradient(90deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
				}

				.question-edit-header-content {
					padding: 2rem;
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex-wrap: wrap;
					gap: 1rem;
				}

				.question-edit-title {
					display: flex;
					align-items: center;
					gap: 1rem;
					margin: 0;
				}

				.question-edit-title-icon {
					width: 3rem;
					height: 3rem;
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
					border-radius: 0.75rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.5rem;
				}

				.question-edit-title-text {
					display: flex;
					flex-direction: column;
				}

				.question-edit-title-main {
					font-size: 2rem;
					font-weight: 700;
					color: #1a202c;
					margin: 0;
					line-height: 1.2;
				}

				.question-edit-title-sub {
					font-size: 1.5rem;
					color: #718096;
					margin: 0;
					font-weight: 400;
				}

				.question-edit-actions {
					display: flex;
					align-items: center;
					gap: 1rem;
				}

				.question-edit-back-btn {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
					color: white;
					border: none;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-weight: 600;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
					text-decoration: none;
				}

				.question-edit-back-btn:hover {
					transform: translateY(-2px);
					box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
					color: white;
					text-decoration: none;
				}

				/* 现代化表单容器 */
				.question-edit-form-container {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					overflow: hidden;
					width: 100%;
				}

				.question-edit-form-header {
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
					color: white;
					padding: 1.5rem 2rem;
					position: relative;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.question-edit-form-header::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					height: 1px;
					background: rgba(255, 255, 255, 0.2);
				}

				.question-edit-form-title {
					font-size: 1.75rem;
					font-weight: 600;
					margin: 0;
				}

				.question-edit-form-icon {
					width: 2.5rem;
					height: 2.5rem;
					background: rgba(255, 255, 255, 0.2);
					border-radius: 0.5rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.25rem;
				}

				.question-edit-form-body {
					padding: 2rem;
				}

				.question-edit-form-group {
					margin-bottom: 2rem;
				}

				.question-edit-form-group:last-child {
					margin-bottom: 0;
				}

				.question-edit-form-label {
					display: flex;
					align-items: center;
					gap: 0.5rem;
					font-size: 1.5rem;
					font-weight: 600;
					color: #374151;
					margin-bottom: 0.75rem;
				}

				.question-edit-form-label-icon {
					width: 1.5rem;
					height: 1.5rem;
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
					border-radius: 0.25rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 0.75rem;
				}

				.question-edit-form-input {
					width: 100%;
					padding: 1rem 1.25rem;
					border: 2px solid #e2e8f0;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-family: inherit;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					background: #f8fafc;
					color: #374151;
				}

				.question-edit-form-input:focus {
					outline: none;
					border-color: #f59e0b;
					box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
					background: white;
				}

				.question-edit-form-help {
					font-size: 1.25rem;
					color: #6b7280;
					margin-top: 0.5rem;
					display: flex;
					align-items: center;
					gap: 0.25rem;
				}

				.question-edit-form-help i {
					color: #f59e0b;
				}

				/* 分类选择样式 */
				.question-edit-category-options {
					display: grid;
					grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
					gap: 1rem;
					padding: 1rem;
					border: 2px solid #e2e8f0;
					border-radius: 0.75rem;
					background: #f8fafc;
					max-height: 300px;
					overflow-y: auto;
				}

				.question-edit-category-option {
					display: flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem;
					border: 2px solid #e2e8f0;
					border-radius: 0.5rem;
					background: white;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.question-edit-category-option:hover {
					border-color: #f59e0b;
					background: rgba(245, 158, 11, 0.05);
				}

				.question-edit-category-option.selected {
					border-color: #f59e0b !important;
					background: rgba(245, 158, 11, 0.1) !important;
					color: #b45309 !important;
				}

				.question-edit-category-option input[type="checkbox"] {
					display: none;
				}

				.question-edit-category-label {
					display: flex;
					align-items: center;
					gap: 0.5rem;
					font-size: 1.25rem;
					font-weight: 600;
					color: #374151;
					cursor: pointer;
					margin: 0;
					width: 100%;
				}

				.question-edit-category-option input[type="checkbox"]:checked + .question-edit-category-label {
					color: #b45309;
				}

				/* 富文本编辑器容器样式 */
				.question-edit-editor-container {
					border: 2px solid #e2e8f0;
					border-radius: 0.75rem;
					overflow: hidden;
					background: white;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					min-height: 400px;
				}

				.question-edit-editor-container:focus-within {
					border-color: #f59e0b;
					box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
				}

				/* 提交按钮样式 */
				.question-edit-submit-container {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					padding: 2rem;
					margin-top: 2rem;
					text-align: center;
					width: 100%;
				}

				.question-edit-submit-btn {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 1rem 2rem;
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
					color: white;
					border: none;
					border-radius: 0.75rem;
					font-size: 1.75rem;
					font-weight: 600;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.3);
					text-decoration: none;
					min-width: 200px;
					justify-content: center;
				}

				.question-edit-submit-btn:hover {
					transform: translateY(-2px);
					box-shadow: 0 8px 15px -3px rgba(245, 158, 11, 0.4);
					color: white;
					text-decoration: none;
				}

				.question-edit-submit-btn:active {
					transform: translateY(0);
				}

				/* 焦点状态增强 */
				.question-edit-form-group.focused .question-edit-form-label {
					color: #f59e0b !important;
				}

				.question-edit-form-group.focused .question-edit-form-label-icon {
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
					transform: scale(1.1);
				}

				/* 响应式设计 */
				@media (max-width: 768px) {
					.question-edit-header-content {
						flex-direction: column;
						align-items: flex-start;
					}

					.question-edit-actions {
						width: 100%;
						justify-content: flex-start;
					}

					.question-edit-form-body {
						padding: 1.5rem;
					}

					.question-edit-category-options {
						grid-template-columns: 1fr;
					}
				}

				/* 动画效果 */
				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(20px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}

				.question-edit-fade-in {
					animation: fadeInUp 0.6s ease-out;
				}

				.question-edit-fade-in-delay-1 {
					animation: fadeInUp 0.6s ease-out 0.1s both;
				}

				.question-edit-fade-in-delay-2 {
					animation: fadeInUp 0.6s ease-out 0.2s both;
				}
			</style>

			<!-- 现代化页面标题 -->
			<div class="question-edit-header question-edit-fade-in">
				<div class="question-edit-header-content">
					<div class="question-edit-title">
						<div class="question-edit-title-icon">
							<i class="fa fa-edit"></i>
						</div>
						<div class="question-edit-title-text">
							<h1 class="question-edit-title-main">问答{:$row ? '编辑' : '添加'}</h1>
							<p class="question-edit-title-sub">Question {:$row ? 'Edit' : 'Add'}</p>
						</div>
					</div>
					<div class="question-edit-actions">
						<a href="{:U('Question/index')}" class="question-edit-back-btn">
							<i class="fa fa-arrow-left"></i>
							<span>返回列表</span>
						</a>
					</div>
				</div>
			</div>

			<!-- 现代化表单容器 -->
			<form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
				<div class="question-edit-form-container question-edit-fade-in-delay-1">
					<div class="question-edit-form-header">
						<div class="question-edit-form-icon">
							<i class="fa fa-edit"></i>
						</div>
						<h3 class="question-edit-form-title">问答信息</h3>
					</div>
					<div class="question-edit-form-body">
						<!-- 问答名称 -->
						<div class="question-edit-form-group">
							<label class="question-edit-form-label">
								<div class="question-edit-form-label-icon">
									<i class="fa fa-question-circle"></i>
								</div>
								问答名称
							</label>
							<input type="text" name="title" class="question-edit-form-input" value="{$row.title}" placeholder="请输入问答名称..." />
							<div class="question-edit-form-help">
								<i class="fa fa-info-circle"></i>
								请输入简洁明了的问答名称，建议不超过200个字符
							</div>
						</div>

						<!-- 所属分类 -->
						<div class="question-edit-form-group">
							<label class="question-edit-form-label">
								<div class="question-edit-form-label-icon">
									<i class="fa fa-folder-open"></i>
								</div>
								所属问答分类
							</label>
							<div class="question-edit-category-options">
								<php>foreach($category as $val) {</php>
								<div class="question-edit-category-option {:in_array($val['id'], $row['category_array']) ? 'selected' : ''}">
									<input type="checkbox" name="category_id[]" {:in_array($val['id'], $row['category_array']) ? 'checked' : ''} value="{$val['id']}" id="cat_{$val['id']}">
									<label for="cat_{$val['id']}" class="question-edit-category-label">
										<i class="fa fa-folder-open"></i>
										<span>{$val['title']}</span>
									</label>
								</div>
								<php>}</php>
							</div>
							<div class="question-edit-form-help">
								<i class="fa fa-info-circle"></i>
								可以选择多个分类，至少选择一个分类
							</div>
						</div>

					<!-- 排序 -->
					<div class="question-edit-form-group">
						<label class="question-edit-form-label">
							<div class="question-edit-form-label-icon">
								<i class="fa fa-sort-numeric-asc"></i>
							</div>
							排序
						</label>
						<input type="number" name="sort" class="question-edit-form-input" value="{:$row ? $row['sort'] : '1'}" min="1" max="999" />
						<div class="question-edit-form-help">
							<i class="fa fa-info-circle"></i>
							数字越小排序越靠前，建议设置为1-999之间的整数
						</div>
					</div>

					<!-- 问答内容 -->
					<div class="question-edit-form-group">
						<label class="question-edit-form-label">
							<div class="question-edit-form-label-icon">
								<i class="fa fa-file-text"></i>
							</div>
							问答内容
						</label>
						<div class="question-edit-editor-container">
							<textarea class="form-control richtext" id="editor_id" name="content" role="3" placeholder="请填写问答内容...">{$row.content}</textarea>
						</div>
						<div class="question-edit-form-help">
							<i class="fa fa-info-circle"></i>
							支持富文本编辑，可以插入图片、链接等多媒体内容
						</div>
					</div>
				</div>
			</div>

				<!-- 提交按钮容器 -->
				<div class="question-edit-submit-container question-edit-fade-in-delay-2">
					<input type="hidden" name="id" value="{$row.id}"/>
					<button type="submit" name="submit" value="提交" class="question-edit-submit-btn">
						<i class="fa fa-save"></i>
						<span>{:$row ? '更新问答' : '添加问答'}</span>
					</button>
				</div>
			</form>
	</div>
</div>
<include file="block/footer" />
<script>
	$(document).ready(function() {
		// 初始化富文本编辑器
		require(['datetimepicker', 'jquery', 'kindeditor.main', 'kindeditor'], function( datetimepicker,$, u) {
			$(function() {
				var url = '/pub/util/uploads';
				KindEditor.create('#editor_id', {
					allowFileManager : false,
					imageSizeLimit : '30MB',
					uploadJson : url,
					minHeight : 300,
					formatUploadUrl:true
				});
			})
		});

		// 分类选择交互
		$('.question-edit-category-option').on('click', function() {
			var $input = $(this).find('input[type="checkbox"]');
			$input.prop('checked', !$input.prop('checked'));

			// 更新选中状态样式
			if ($input.prop('checked')) {
				$(this).addClass('selected');
			} else {
				$(this).removeClass('selected');
			}
		});

		// 输入框焦点效果
		$('.question-edit-form-input').on('focus', function() {
			$(this).closest('.question-edit-form-group').addClass('focused');
		}).on('blur', function() {
			$(this).closest('.question-edit-form-group').removeClass('focused');
		});

		// 字符计数功能
		$('input[name="title"]').on('input', function() {
			var length = $(this).val().length;
			var maxLength = 200;
			var $help = $(this).siblings('.question-edit-form-help');

			if (length > maxLength * 0.8) {
				$help.html('<i class="fa fa-exclamation-triangle" style="color: #f59e0b;"></i> 问答名称长度: ' + length + '/' + maxLength + ' 字符');
			} else {
				$help.html('<i class="fa fa-info-circle"></i> 请输入简洁明了的问答名称，建议不超过200个字符');
			}
		});

		// 表单提交验证
		$('#form1').on('submit', function(e) {
			var title = $('input[name="title"]').val().trim();
			var selectedCategories = $('input[name="category_id[]"]:checked').length;
			var sort = $('input[name="sort"]').val();
			var content = $('#editor_id').val().trim();

			// 验证问答名称
			if (!title) {
				layer.msg('请输入问答名称', {icon: 2});
				$('input[name="title"]').focus();
				return false;
			}

			if (title.length > 200) {
				layer.msg('问答名称不能超过200个字符', {icon: 2});
				$('input[name="title"]').focus();
				return false;
			}

			// 验证分类选择
			if (selectedCategories === 0) {
				layer.msg('请至少选择一个问答分类', {icon: 2});
				return false;
			}

			// 验证排序
			if (!sort || sort < 1 || sort > 999) {
				layer.msg('排序必须是1-999之间的整数', {icon: 2});
				$('input[name="sort"]').focus();
				return false;
			}

			// 验证内容
			if (!content) {
				layer.msg('请输入问答内容', {icon: 2});
				return false;
			}

			// 显示提交状态
			var $submitBtn = $('.question-edit-submit-btn');
			var originalText = $submitBtn.html();
			$submitBtn.prop('disabled', true);
			$submitBtn.html('<i class="fa fa-spinner fa-spin"></i><span>提交中...</span>');

			// 延迟恢复按钮状态（如果提交失败）
			setTimeout(function() {
				if ($submitBtn.prop('disabled')) {
					$submitBtn.prop('disabled', false);
					$submitBtn.html(originalText);
				}
			}, 10000);

			return true;
		});

		// 返回按钮确认
		$('.question-edit-back-btn').on('click', function(e) {
			var title = $('input[name="title"]').val().trim();
			var content = $('#editor_id').val().trim();

			// 如果有内容变更，提示用户
			if (title || content) {
				e.preventDefault();
				layer.confirm('确定要离开吗？未保存的内容将会丢失。', {
					btn: ['确定离开', '继续编辑'],
					icon: 3,
					title: '确认离开'
				}, function(index) {
					layer.close(index);
					window.location.href = $(e.target).closest('a').attr('href');
				});
			}
		});

		// 页面加载完成后的初始化
		setTimeout(function() {
			// 触发一次字符计数
			$('input[name="title"]').trigger('input');

			// 设置初始焦点
			if (!$('input[name="title"]').val()) {
				$('input[name="title"]').focus();
			}
		}, 500);
	});
</script>