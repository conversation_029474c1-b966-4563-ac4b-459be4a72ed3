﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <title>资金明细</title>
    <style>
        /* 复用设计系统变量 */
        :root {
            --primary-color: #00bf80;
            --expense-color: #ff4d4f;
            --secondary-color: #666;
            --card-bg: #fff;
            --border-color: #eee;
        }
        .container {
            max-width: 1200px;
            margin: 12px;
        }

        /* 过滤条件区域 */
        .filter-section {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        /* 日期选择器 */
        .date-picker {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .date-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            width: 140px;
            font-size: 14px;
        }

        /* 类型筛选 */
        .type-filter {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .type-btn {
            padding: 6px 12px;
            border-radius: 16px;
            border: 1px solid #ddd;
            background: #fff;
            cursor: pointer;
            transition: all 0.2s;
        }
        .type-btn.active {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        /* 明细列表 */
        .detail-list {
            background: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
        }
        .detail-item:last-child {
            border-bottom: none;
        }
        .detail-info {
            flex: 1;
        }
        .detail-title {
            font-size: 16px;
            margin-bottom: 4px;
        }
        .detail-time {
            color: var(--secondary-color);
            font-size: 12px;
        }
        .detail-amount {
            font-size: 16px;
            font-weight: 500;
            min-width: 120px;
            text-align: right;
        }
        .amount-income {
            color: var(--primary-color);
        }
        .amount-expense {
            color: var(--expense-color);
        }
        .amount-transfer {
            color: #007bff;
            font-weight: 500;
        }

        /* 空状态提示 */
        .empty-state {
            text-align: center;
            padding: 40px;
            color: var(--secondary-color);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .filter-section {
                flex-direction: column;
            }
            .date-picker {
                flex-wrap: wrap;
            }
            .date-input {
                width: 100%;
            }
        }
          /* 新增弹窗样式 */
          .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 24px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .modal-close {
            position: absolute;
            top: 12px;
            right: 12px;
            cursor: pointer;
            color: var(--secondary-color);
        }

        /* 分页控件 */
        .pagination {
            display: flex;
            gap: 8px;
            justify-content: center;
            padding: 16px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .pagination a, .pagination span {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 0 4px;
            cursor: pointer;
            background: white;
            color: #333;
            text-decoration: none;
            display: inline-block;
        }

        .pagination span.current {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination a:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            background: white;
        }

        .page-btn.active {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        /* 加载动画 */
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: var(--secondary-color);
        }
        .loading::after {
            content: " ";
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 3px solid var(--primary-color);
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        /* 返回箭头样式 */
.back-arrow {
    position: fixed;
    top: 115px;
    left: 15px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.2s;
}

.back-arrow::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    width: 12px;
    height: 12px;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: translateY(-50%) rotate(45deg);
}

/* 点击反馈效果 */
.back-arrow:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.7);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .back-arrow {
        top: 170px;
        left: 10px;
        width: 36px;
        height: 36px;
    }
    
    .back-arrow::before {
        left: 13px;
        width: 10px;
        height: 10px;
    }
}

    </style>
</head>
<body>
    <!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
    <include file="headers"/>
    <div class="container">
        <h1 style="margin-bottom: 24px; font-size: 24px;text-align: center;">资金明细</h1>

        <!-- 过滤条件 -->
        <div class="filter-section">
            <div class="date-picker">
                <input type="date" class="date-input" id="startDate">
                <span>至</span>
                <input type="date" class="date-input" id="endDate">
            </div>
            <div class="type-filter js_type">
                <button class="type-btn active" data-type="0">全部</button>
                <button class="type-btn" data-type="1">收入</button>
                <button class="type-btn" data-type="2">支出</button>
            </div>
        </div>
        <!-- 明细列表 -->
        <div class="detail-list" id="detailList">
            <include file="list-money_alldetails"/>
        </div>
        <!-- 弹窗 -->
        <div class="modal-overlay" id="modal">
            <div class="modal-content">
                <div class="modal-close" onclick="closeModal()">×</div>
                <h3 id="modalTitle"></h3>
                <div class="modal-body" id="modalContent"></div>
            </div>
        </div>
        <!-- 添加分页容器 -->
        <div class="pagination">
             {$page}
        </div>
    </div>
    <script>
        // 预先生成筛选API的URL地址
        var ajaxUrl = "{:U('Index/money_alldetails')}";
        var type = {:I('get.type', 0)};
        var page = {:I('get.p', 1)};
        var startTime = 0;
        var endTime = 0;

        // 加载资金明细记录的AJAX函数
        function ajaxLoadRecords(pageNum, recordType, sTime, eTime) {
            $('.detail-list').html('<p style="text-align:center; padding: 20px;">加载中...</p>'); 
            
            var url = ajaxUrl;
            var params = { 
                n: 1, 
                p: pageNum || 1, 
                type: recordType || type 
            };
            
            // 如果提供了时间参数，则添加到请求中
            if (sTime) params.startTime = sTime;
            if (eTime) params.endTime = eTime;
            
            $.get(url, params, function(htmlContent){
                $('.detail-list').html(htmlContent);
                // 更新分页内容
                $.get(url, $.extend({}, params, {showpage: 1}), function(pageHtml){
                    $('.pagination').html(pageHtml);
                    // 更新当前页码
                    page = pageNum || 1;
                });
            }, 'html').fail(function() {
                $('.detail-list').html('<p style="text-align:center; padding: 20px; color: red;">加载失败，请重试。</p>');
            });
        }

        // 初始化日期选择器
        function initDatePicker() {
            const today = new Date().toISOString().split('T')[0]
            const startDate = document.getElementById('startDate')
            const endDate = document.getElementById('endDate')

            // 设置默认日期为本月1日和当天
            startDate.value = new Date(new Date().setDate(1)).toISOString().split('T')[0]
            endDate.value = today
            endDate.min = startDate.value
            
            // 初始化时设置时间戳
            startTime = Math.floor(new Date(startDate.value + " 00:00:00").getTime() / 1000)
            endTime = Math.floor(new Date(endDate.value + " 23:59:59").getTime() / 1000)
            
            startDate.addEventListener('change', e => {
                endDate.min = e.target.value
                if (new Date(endDate.value) < new Date(e.target.value)) {
                    endDate.value = e.target.value
                }
                var newStartTimes = Math.floor(new Date(e.target.value + " 00:00:00").getTime() / 1000)
                if (newStartTimes != startTime) {
                    startTime = newStartTimes;
                    console.log("选择开始日期: " + e.target.value + ", 时间戳: " + startTime);
                    ajaxLoadRecords(1, type, startTime, endTime);
                }
            })

            endDate.addEventListener('change', e => {
                currentEnd = e.target.value
                console.log("选择结束日期: " + currentEnd);
                var newEndTimes = Math.floor(new Date(currentEnd + ' 23:59:59').getTime() / 1000)
                if (newEndTimes != endTime) {
                    endTime = newEndTimes;
                    console.log("结束时间戳: " + endTime);
                    ajaxLoadRecords(1, type, startTime, endTime);
                }
            })
        }

        $(function () {
            // 初始化日期选择器
            initDatePicker();
            
            // 类型筛选按钮点击事件
            $('.js_type button').click(function () {
                $(this).siblings().removeClass('active');
                $(this).addClass('active');
                var newType = $(this).attr('data-type');
                console.log("选择类型: " + newType);
                if (newType != type) {
                    type = newType;
                    ajaxLoadRecords(1, type, startTime, endTime);
                }
            });

            // 初始化时确保正确设置当前选中的类型按钮
            $('.js_type button[data-type="' + type + '"]').addClass('active').siblings().removeClass('active');
            
            // 添加分页点击事件处理
            $(document).on('click', '.pagination a', function(e) {
                e.preventDefault();
                var href = $(this).attr('href');
                var urlParams = new URLSearchParams(href.split('?')[1]);
                var targetPage = urlParams.get('p') || 1;
                ajaxLoadRecords(parseInt(targetPage), type, startTime, endTime); 
            });
            
            // 确保分页控件样式正确
            $('.pagination').css({
                'display': 'flex',
                'justify-content': 'center',
                'margin-top': '20px',
                'margin-bottom': '20px'
            });
        });

        // 返回逻辑（优先返回历史记录，失败则跳转首页）
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '{:U("index/money")}';
            }
        }

        // 禁用页面刷新时的缓存
        window.onpageshow = function(event) {
            if (event.persisted) {
                window.location.reload();
            }
        };

        var noticeSwiper = new Swiper('.noticeSwiper', {
            direction: 'vertical',
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            }
        })
        var noticeSwiper = new Swiper('.page0106Swiper', {
            pagination: {
                el: ".swiper-pagination",
            },

            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            }
        });
    </script>
</body>
</html>