﻿<!DOCTYPE html>
<html >
<head>
<title>服务站管理</title>
<meta name="Keywords" content="关键字">
<meta name="Description" content="内容">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
<meta content="no-cache,must-revalidate" http-equiv="Cache-Control">
<meta content="telephone=no, address=no" name="format-detection">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta content="no-cache" http-equiv="pragma">
<meta content="0" http-equiv="expires">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"> 
<link rel="stylesheet" type="text/css" href="/static/stations/css/css.css?v={:time()}" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
<script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
<script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <style>
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
            z-index: 999;
        }

        /* 大图样式 */
        .modal-img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }

        /* 缩略图样式 */
        .thumbnail {
            width: 200px;
            height: 200px;
            cursor: pointer;
            object-fit: cover;
        }

        /* 模态框遮罩层 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        /* 模态框内容 */
        .modal-content {
            background: white;
            padding: 32px;
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            position: relative;
            animation: modalSlide 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        }
    </style>
</head>
<body>
<include file="headers"/>
<div class="page0107box mb10">
        <div class="page0107">
            <div class="weap">
                <div class="boxs mb10" style="width: 100%;">
                    <div class="hd01">
                        <div class="search">
                            <form action="">
                                <input class="input" name="kwd" value="{:$kwd}" type="search" placeholder="搜索" style="width: 100%;">
                                <button class="inbtn"><i class="iconfont icon-31sousuo"></i></button> 
                            </form>
                        </div>
                    </div>
                    <div class="hd03">
                        <ul>
                            <li data-type="" class="{:empty($type) ? 'on' : ''} ">
                                <a>全部</a>
                            </li>
                            <li data-type="3" class="{:$type == 3 ? 'on' : ''} ">
                                <a>审核中</a>
                            </li>
                            <li data-type="2" class="{:$type == 2 ? 'on' : ''} ">
                                <a>未过审</a>
                            </li> 
                            <li data-type="1" class="{:$type== 1 ? 'on' : ''} ">
                                <a>已过审</a>
                            </li>
                             
                        </ul>
                    </div>   
                </div>
                <div class="bd">
                    <ul>
                        <php>if ($refCount == 0) {</php>
                        <li style="text-align: center;font-size: 13px;color: darkgray;padding: 20px 0;">
                            您当前没有完成服务站推荐加盟工作任务   
                        </li>
                        <php>} else {</php>
                            <php>if (empty($list)) {</php>
                                <li style="text-align: center;font-size: 13px;color: darkgray;padding: 20px 0;">无相关服务站数据</li>
                                <php>}else{</php>
                                <include file="list-servicestation"/>
                        <php>}}</php>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="footernav">
        <div class="box">
            <ul>
                <php>if ($serviceStationRow['open_num']> 0) {</php>
                <li>
                    <a href="{:U('index/register')}">
                        <div class="ico"><span class="iconfont icon-jiajianzujianjiahao"></span></div>
                        <h3>开通服务站({:$serviceStationRow['open_num']})</h3>
                    </a>
                </li>
                <php>}</php>
                <php>if ($serviceStationRow['resources'] > 0) {</php>
                <li>
                    <a href="{:U('index/servicestationbuy')}">
                        <div class="ico"><span class="iconfont icon-tongji"><i></i></span></div>
                        <h3>技术服务采购包</h3>
                    </a>
                </li>
                <php>}</php>
            </ul>
        </div>
        </div>
<!-- 弹窗层 -->
<div class="modal">
    <img class="modal-img">
</div>
</body>


<script type="text/javascript">
var kwd = '{$kwd}';
var type = '{$type}';
var url = '{:U("index/servicestation")}';
$(document).ready(function(){
    var noticeSwiper = new Swiper('.noticeSwiper', {
        direction: 'vertical',
		loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    })
    var noticeSwiper = new Swiper('.page0106Swiper', {
        pagination: {
          el: ".swiper-pagination",
        },

		loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    });
	$(".page0107 .hd02 li").click(function(){
        $(this).siblings().removeClass("on");
       $(this).addClass("on");
   });
   $(".page0107 .hd03 li").click(function(){
       var newType = $(this).attr('data-type');
        $(this).siblings().removeClass("on");
       $(this).addClass("on");
       if (newType != type) {
           window.location.href = url + '?kwd='+kwd+"&type="+newType;
           return
       }
   });


    $('.js_kwd').blur(function () {
        var  new_kwd = $(this).val();
        if (new_kwd != kwd) {
            window.location.href = url + '?kwd='+new_kwd+"&type="+type;
            return
        }
    })

    // 获取元素
    const thumbnail = document.querySelector('.thumbnail');
    const modal = document.querySelector('.modal');
    const modalImg = document.querySelector('.modal-img');
    $("body").on('click', '.thumbnail', function () {
        modal.style.display = 'flex';
        modalImg.src = $(this).attr('src');
    })

    // 点击任意位置关闭弹窗
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

});
</script>
</body>
</html>