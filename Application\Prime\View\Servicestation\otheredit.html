<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Servicestation/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">其他文件{:$row ? '编辑' : '添加'}</div>
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">文件名称</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="file_name" class="form-control" value="{$row.file_name}" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style='color:red'>*</span>文件</label>
                            <div class="col-sm-9 col-xs-12">
                                <php>
                                    echo tpl_form_field_file('file_url', $row['file_url'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                </php>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="form-group col-sm-12">
					<input type="hidden" name="id" value="{$row.id}"/>
					<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			    </div>
            </div>
            </form>
        </div>
    </div>
</div>
<include file="block/footer" />
<script>

    //function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
            u.editor($('.richtext1')[0]);

        })
    });
</script>