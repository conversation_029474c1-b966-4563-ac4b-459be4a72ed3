<?php
namespace Common\Model;

use Think\Model;

class RecruitmentNoticeModel extends Model
{
    protected $tableName = 'recruitment_notice';
    
    protected $_auto = [
        ['create_time', 'time', 1, 'function'],
        ['update_time', 'time', 2, 'function'],
    ];

    protected $_validate = [
        ['title', 'require', '公告标题不能为空！'],
        ['company_name', 'require', '招聘单位不能为空！'],
    ];

    public $status = [
        '0' => ['text' => '停用', 'style' => 'danger'],
        '1' => ['text' => '启用', 'style' => 'success'],
    ];

    /**
     * 获取招聘公告列表
     */
    public function getNoticeList($where = [], $page = 1, $pageSize = 20)
    {
        $count = $this->where($where)->count();
        $list = $this->where($where)
            ->order('id DESC')
            ->page($page, $pageSize)
            ->select();

        return [
            'list' => $list,
            'count' => $count,
            'page' => $page,
            'pageSize' => $pageSize
        ];
    }

    /**
     * 获取招聘公告关联的岗位
     */
    public function getNoticePosts($noticeId)
    {
        $sql = "SELECT p.*, rnp.id as relation_id 
                FROM z_recruitment_notice_post rnp 
                LEFT JOIN z_project_post p ON rnp.post_id = p.id 
                WHERE rnp.notice_id = %d AND p.status = 1";
        
        return $this->query($sql, [$noticeId]);
    }

    /**
     * 添加岗位关联
     */
    public function addNoticePosts($noticeId, $postIds)
    {
        if (empty($postIds)) {
            return true;
        }

        // 先删除原有关联
        M('RecruitmentNoticePost')->where(['notice_id' => $noticeId])->delete();

        // 添加新关联
        $data = [];
        foreach ($postIds as $postId) {
            $data[] = [
                'notice_id' => $noticeId,
                'post_id' => $postId,
                'create_time' => time()
            ];
        }

        return M('RecruitmentNoticePost')->addAll($data);
    }

    /**
     * 删除招聘公告及相关数据
     */
    public function deleteNotice($id)
    {
        $this->startTrans();
        try {
            // 删除公告
            $this->delete($id);
            
            // 删除岗位关联
            M('RecruitmentNoticePost')->where(['notice_id' => $id])->delete();
            
            // 删除岗位要求
            M('PostRequirements')->where(['notice_id' => $id])->delete();
            
            // 删除匹配记录
            M('ResumePostMatch')->where(['notice_id' => $id])->delete();
            
            $this->commit();
            return true;
        } catch (Exception $e) {
            $this->rollback();
            return false;
        }
    }
}
