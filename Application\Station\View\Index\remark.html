﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>简历沟通记录</title>
    <script src="/static/js/jquery2_2_4.min.js"></script>
    <script src="/static/js/layer/layer.m.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        }

        body {
            background: #ededed;
            min-height: 100vh;
            max-width: 750px;
            margin: 0 auto;
            position: relative;
        }

        /* 顶部导航栏 - 微信风格 */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            max-width: 750px;
            height: 56px;
            background: #393a3e;
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 500;
            z-index: 100;
            border-bottom: 1px solid #2c2c2c;
        }

        /* 内容区域 - 微信风格 */
        .container {
            margin-top: 56px;
            padding: 0;
            padding-bottom: 120px;
            background: #ededed;
            min-height: calc(100vh - 56px);
        }

        /* 输入区域 - 微信风格 */
        .input-area {
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 750px;
            background: #f7f7f7;
            padding: 8px 12px;
            border-top: 1px solid #d9d9d9;
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }

        /* 输入框 - 微信风格 */
        .input-box {
            flex: 1;
            min-height: 36px;
            max-height: 100px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            resize: none;
            font-size: 16px;
            background: #fff;
            line-height: 20px;
            overflow-y: auto;
        }

        .input-box:focus {
            outline: none;
            border-color: #07c160;
        }

        /* 提交按钮 - 微信风格 */
        .submit-btn {
            background: #07c160;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease;
            min-width: 60px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .submit-btn:active {
            background: #06ad56;
        }

        .submit-btn:disabled {
            background: #c9c9c9;
            cursor: not-allowed;
        }

        .submit-btn.loading {
            color: transparent;
            position: relative;
        }

        .submit-btn.loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            border: 2px solid #fff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 消息提示动画 */
        @keyframes messageSlideIn {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        @keyframes messageSlideOut {
            0% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .header {
                padding: 0 15px;
                font-size: 16px;
            }

            .container {
                padding: 15px;
            }

            .input-area {
                padding: 15px;
            }

            .resume-info {
                padding: 12px 15px;
                font-size: 13px;
            }

            .resume-item {
                padding: 6px 10px;
            }

            .bubble-left,
            .bubble-right {
                padding: 12px 15px;
            }
        }

        /* 记录列表 */
        .record-list {
            margin-bottom: 20px;
        }

        /* 单条记录 */
        .record-item {
            margin-bottom: 30px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .record-content {
            background: #07c160;
            color: white;
            padding: 12px;
            border-radius: 8px;
            max-width: 80%;
            word-break: break-word;
            position: relative;
            margin: 5px 0;
        }

        .record-time {
            font-size: 12px;
            color: #999;
            margin-top: 14px;
 
        }
        /* 简历信息样式 - 微信风格 */
        .resume-info {
            position: fixed;
            top: 56px;
            width: 100%;
            max-width: 750px;
            background: #fff;
            padding: 12px 16px;
            border-bottom: 1px solid #e5e5e5;
            z-index: 99;
            display: flex;
            gap: 16px;
            font-size: 13px;
            flex-wrap: wrap;
        }

        .resume-item {
            display: flex;
            align-items: center;
            color: #576b95;
        }

        .resume-label {
            color: #888;
            margin-right: 4px;
        }

        .resume-value {
            color: #333;
            font-weight: 500;
        }

        /* 调整原有容器间距 */
        .container {
            margin-top: 96px;
        }

                /* 新增聊天记录样式 */
                .record-list {
            margin-bottom: 30px;
        }

        /* 消息项基础样式 */
        .message-item {
            display: flex;
            margin: 5px 0;
            max-width: 98%;
        }

        /* 对方消息（左侧） */
        .message-left {
            align-self: flex-start;
            flex-direction: row;
        }

        /* 己方消息（右侧） */
        .message-right {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        /* 头像样式 */
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            margin: 0 8px;
        }

        /* 消息内容区域 */
        .message-content {
            max-width: calc(100% - 60px);
        }

        /* 消息容器 */
        .message-container {
            padding: 8px 16px;
            display: flex;
            flex-direction: column;
        }

        /* 消息组 */
        .message-group {
            margin-bottom: 16px;
        }

        /* 消息头部（昵称和时间） */
        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 12px;
        }

        .message-header.left {
            justify-content: flex-start;
        }

        .message-header.right {
            justify-content: flex-end;
        }

        .sender-name {
            color: #888;
            font-weight: 500;
        }

        /* 不同身份的发送者名称样式 */
        .sender-self {
            color: #576b95;
            font-weight: 600;
        }

        .sender-platform {
            color: #e74c3c;
            font-weight: 600;
            background: #ffeaea;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }

        .sender-station {
            color: #3498db;
            font-weight: 600;
            background: #e8f4fd;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }

        .sender-zjb {
            color: #f39c12;
            font-weight: 600;
            background: #fef9e7;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }

        .sender-unknown {
            color: #95a5a6;
            font-weight: 500;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }

        .message-time {
            color: #b2b2b2;
            margin-left: 8px;
            font-size: 11px;
        }

        .message-header.right .message-time {
            margin-left: 0;
            margin-right: 8px;
            order: -1;
        }

        /* 消息气泡容器 */
        .bubble-container {
            display: flex;
        }

        .bubble-container.left {
            justify-content: flex-start;
        }

        .bubble-container.right {
            justify-content: flex-end;
        }

        /* 对方消息气泡 - 微信风格 */
        .bubble-left {
            background: #fff;
            color: #333;
            border-radius: 8px;
            padding: 10px 12px;
            position: relative;
            max-width: 70%;
            word-wrap: break-word;
            line-height: 1.4;
            font-size: 16px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .bubble-left::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 12px;
            width: 0;
            height: 0;
            border: 8px solid transparent;
            border-right-color: #fff;
            border-left: none;
        }

        /* 己方消息气泡 - 微信风格 */
        .bubble-right {
            background: #95ec69;
            color: #333;
            border-radius: 8px;
            padding: 10px 12px;
            position: relative;
            max-width: 70%;
            word-wrap: break-word;
            line-height: 1.4;
            font-size: 16px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .bubble-right::after {
            content: '';
            position: absolute;
            right: -8px;
            top: 12px;
            width: 0;
            height: 0;
            border: 8px solid transparent;
            border-left-color: #95ec69;
            border-right: none;
        }

        /* 时间分隔线 */
        .time-divider {
            text-align: center;
            margin: 16px 0;
        }

        .time-divider span {
            background: #dcdcdc;
            color: #999;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            display: inline-block;
        }
          /* 新增上传相关样式 */
          .upload-area {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: #f0f0f0;
            color: #666;
            border: 1px solid #e5e5e5;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
        }

        .preview-files {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            max-width: 70%;
        }

        .file-item {
            position: relative;
            margin: 2px;
        }

        .file-preview {
            max-width: 80px;
            max-height: 80px;
            border-radius: 6px;
            border: 1px solid #eee;
        }

        .file-card {
            background: #f8f8f8;
            padding: 8px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .file-name {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 12px;
        }

        .file-type {
            width: 24px;
            height: 24px;
        }

        /* 附件消息样式 */
        .attachment-msg {
            background: #fff;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #eee;
        }

        .download-btn {
            color: #07c160;
            text-decoration: none;
            font-size: 14px;
            margin-top: 5px;
            display: inline-block;
        }

                    
        /* 返回箭头样式 - 微信风格 */
        .back-arrow {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgb(128, 116, 116);
            color:#07c160;
        }

        .back-arrow::before {
            content: '';
            width: 12px;
            height: 12px;
            border-left: 2px solid #fff;
            border-bottom: 2px solid #fff;
            transform: rotate(45deg);
        }

        .back-arrow:active {
            opacity: 0.7;
        }

        /* 图片尺寸控制 */
        .bubble-left img, .bubble-right img {
            max-width: 200px;
            max-height: 200px;
            width: auto;
            height: auto;
            border-radius: 8px;
            margin: 5px 0;
            display: block;
            cursor: pointer;
            transition: opacity 0.2s;
        }

        .bubble-left img:hover, .bubble-right img:hover {
            opacity: 0.8;
        }

        /* 图片预览遮罩层 */
        .image-preview-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .image-preview-container {
            position: relative;
            max-width: 90%;
            max-height: 90%;
        }

        .image-preview-img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
        }

        .image-preview-close {
            position: absolute;
            top: -40px;
            right: 0;
            color: white;
            font-size: 30px;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.5);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .back-arrow {
                top: 220px;
                left: 10px;
                width: 36px;
                height: 36px;

            }

            .back-arrow::before {
                left: 13px;
                width: 10px;
                height: 10px;
            }

            /* 移动端图片尺寸进一步限制 */
            .bubble-left img, .bubble-right img {
                max-width: 150px;
                max-height: 150px;
            }

            /* 移动端图片预览优化 */
            .image-preview-container {
                max-width: 95%;
                max-height: 95%;
            }

            .image-preview-close {
                top: -35px;
                width: 35px;
                height: 35px;
                font-size: 24px;
            }
        }

    </style>
</head>
<body>
    <div class="header">
        <div class="back-arrow" onclick="goBack()"></div>
        {:$jobRow['name']} 简历沟通记录
    </div>
    <!-- 新增简历信息区域 -->
    <div class="resume-info">
        <div class="resume-item">
            <span class="resume-label">性别</span>
            <span class="resume-value">{:$jobRow['gender'] ?  : "--"}</span>
        </div>
        <div class="resume-item">
            <span class="resume-label">学历</span>
            <span class="resume-value">{:$jobRow['education_level'] ? : '--'}</span>
        </div>
        <div class="resume-item">
            <span class="resume-label">专业</span>
            <span class="resume-value">{:$jobRow['major'] ? : '--'}</span>
        </div>
        <div class="resume-item">
            <span class="resume-label">工作经验</span>
            <span class="resume-value">{:$jobRow['work_experience_years'] ? : 0}年</span>
        </div>
    </div>
<!-- 简历沟通消息三方沟通记录（平台、服务站、招就办） -->
    <div class="container">
        <div class="message-container" id="recordList"><BR><BR>
            <php>foreach ($jobList as $jobRows) {</php>
                <php>
                // 判断是否为己方消息
                $isMyMessage = ($jobRows['service_station_id'] == $service_station_id);

                // 获取发送者身份信息
                $senderInfo = '';
                $senderClass = '';

                if ($jobRows['type'] == 1) {
                    // 服务站或招就办发送的消息
                    if (isset($serviceStationMap[$jobRows['service_station_id']])) {
                        $station = $serviceStationMap[$jobRows['service_station_id']];
                        if ($station['zsb_type'] == 1) {
                            $senderInfo = $isMyMessage ? '您' : '服务站-' . $station['service_name'];
                            $senderClass = $isMyMessage ? 'sender-self' : 'sender-station';
                        } else {
                            $senderInfo = $isMyMessage ? '您' : '招就办-' . $station['contract_name'];
                            $senderClass = $isMyMessage ? 'sender-self' : 'sender-zjb';
                        }
                    } else {
                        $senderInfo = $isMyMessage ? '您' : '未知服务站';
                        $senderClass = $isMyMessage ? 'sender-self' : 'sender-unknown';
                    }
                } else {
                    // 平台发送的消息
                    $senderInfo = '平台';
                    $senderClass = 'sender-platform';
                    $isMyMessage = false; // 平台消息始终显示为对方消息
                }
                </php>

                <div class="message-group">
                    <php>if ($isMyMessage) {</php>
                        <!-- 己方消息 -->
                        <div class="message-header right">
                            <span class="message-time">{:date("m月d日 H:i", $jobRows['create_time'])}</span>
                            <span class="sender-name {:$senderClass}">{:$senderInfo}</span>
                        </div>
                        <div class="bubble-container right">
                            <div class="bubble-right">
                                {:htmlspecialchars_decode($jobRows['content'])}
                            </div>
                        </div>
                    <php>} else {</php>
                        <!-- 对方消息 -->
                        <div class="message-header left">
                            <span class="sender-name {:$senderClass}">{:$senderInfo}</span>
                            <span class="message-time">{:date("m月d日 H:i", $jobRows['create_time'])}</span>
                        </div>
                        <div class="bubble-container left">
                            <div class="bubble-left">
                                {:htmlspecialchars_decode($jobRows['content'])}
                            </div>
                        </div>
                    <php>}</php>
                </div>
            <php>}</php>

            <php>if ($jobRow['need_reply']) {</php>
            <div class="time-divider">
                <span>平台等待您回复信息</span>
            </div>
            <php>}</php>
            <BR><BR>
        </div>
    </div>
    <div class="input-area">
        <textarea class="input-box" placeholder="请输入沟通信息..." id="contentInput" rows="1"></textarea>
        <button class="submit-btn" onclick="addRecord()">发送</button>
    </div>

    <!-- 图片预览遮罩层 -->
    <div class="image-preview-overlay" id="imagePreviewOverlay" onclick="closeImagePreview()">
        <div class="image-preview-container" onclick="event.stopPropagation()">
            <div class="image-preview-close" onclick="closeImagePreview()">&times;</div>
            <img class="image-preview-img" id="imagePreviewImg" src="" alt="预览图片">
        </div>
    </div>

    <script>


        // 防重复提交标志
        let isSubmitting = false;

        // 添加记录（现代化版本，防重复提交）
        function addRecord() {
            // 防重复提交检查
            if (isSubmitting) {
                showMessage('正在提交中，请稍候...', 'warning');
                return;
            }

            const input = document.getElementById('contentInput');
            const submitBtn = document.querySelector('.submit-btn');
            const content = input.value.trim();

            if (!content) {
                showMessage('请输入内容', 'error');
                input.focus();
                return;
            }

            if (content.length > 1000) {
                showMessage('内容不能超过1000个字符', 'error');
                input.focus();
                return;
            }

            // 设置提交状态
            isSubmitting = true;
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');
            submitBtn.textContent = '提交中...';

            const formData = new FormData();
            formData.append('jobcontent', content);

            const xhr = new XMLHttpRequest();

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    // 重置提交状态
                    isSubmitting = false;
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('loading');
                    submitBtn.textContent = '提交';

                    if (xhr.status === 200) {
                        try {
                            const res = JSON.parse(xhr.responseText);
                            if (res.status === 1) {
                                showMessage(res.msg || '提交失败', 'error');
                            } else {
                                showMessage(res.msg || '提交成功', 'success');
                                input.value = '';
                                // 延迟刷新页面，让用户看到成功提示
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1500);
                            }
                        } catch (e) {
                            showMessage('响应数据格式错误', 'error');
                        }
                    } else {
                        showMessage('网络错误，请重试', 'error');
                    }
                }
            };

            xhr.onerror = function() {
                // 重置提交状态
                isSubmitting = false;
                submitBtn.disabled = false;
                submitBtn.classList.remove('loading');
                submitBtn.textContent = '提交';
                showMessage('网络连接失败，请检查网络', 'error');
            };

            xhr.ontimeout = function() {
                // 重置提交状态
                isSubmitting = false;
                submitBtn.disabled = false;
                submitBtn.classList.remove('loading');
                submitBtn.textContent = '提交';
                showMessage('请求超时，请重试', 'error');
            };

            xhr.timeout = 30000; // 30秒超时
            xhr.open('POST', '{:U("index/commitremark", ["id" => $jobRow["id"]])}', true);
            xhr.send(formData);
        }

        // 微信风格消息提示函数
        function showMessage(message, type = 'info') {
            // 移除现有的消息提示
            const existingMsg = document.querySelector('.message-toast');
            if (existingMsg) {
                existingMsg.remove();
            }

            const toast = document.createElement('div');
            toast.className = `message-toast message-${type}`;
            toast.textContent = message;

            // 微信风格样式
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 10000;
                animation: messageSlideIn 0.3s ease-out;
                max-width: 80%;
                text-align: center;
            `;

            document.body.appendChild(toast);

            // 2秒后自动移除
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.animation = 'messageSlideOut 0.3s ease-in';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 300);
                }
            }, 2000);
        }

        // 返回逻辑（优先返回历史记录，失败则跳转首页）
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/'; // 修改为你的默认回退地址
            }
        }

        // 图片预览功能
        function openImagePreview(imgSrc) {
            const overlay = document.getElementById('imagePreviewOverlay');
            const previewImg = document.getElementById('imagePreviewImg');
            previewImg.src = imgSrc;
            overlay.style.display = 'flex';
            document.body.style.overflow = 'hidden'; // 禁止背景滚动
        }

        function closeImagePreview() {
            const overlay = document.getElementById('imagePreviewOverlay');
            overlay.style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复背景滚动
        }

        // 页面加载后自动滚动到底部并绑定图片点击事件
        $(document).ready(function(){
            $('html, body').scrollTop($(document).height());

            // 为所有聊天中的图片添加点击事件
            $('.bubble-left img, .bubble-right img').on('click', function() {
                const imgSrc = $(this).attr('src');
                if (imgSrc) {
                    openImagePreview(imgSrc);
                }
            });

            // 微信风格功能初始化
            initWechatFeatures();
        });

        // 微信风格功能初始化
        function initWechatFeatures() {
            const input = document.getElementById('contentInput');
            if (input) {
                // 自动调整输入框高度（微信风格）
                input.addEventListener('input', function() {
                    this.style.height = 'auto';
                    const newHeight = Math.min(this.scrollHeight, 100);
                    this.style.height = newHeight + 'px';

                    // 调整输入区域高度
                    const inputArea = this.parentElement;
                    inputArea.style.paddingTop = newHeight > 36 ? '12px' : '8px';
                    inputArea.style.paddingBottom = newHeight > 36 ? '12px' : '8px';
                });

                // 输入框失去焦点时重置高度
                input.addEventListener('blur', function() {
                    if (!this.value.trim()) {
                        this.style.height = '36px';
                        this.parentElement.style.paddingTop = '8px';
                        this.parentElement.style.paddingBottom = '8px';
                    }
                });
            }

            // 添加页面可见性检测，防止后台重复提交
            document.addEventListener('visibilitychange', function() {
                if (document.hidden && isSubmitting) {
                    console.log('页面已隐藏，请勿重复提交');
                }
            });

            // 滚动到底部
            scrollToBottom();
        }

        // 滚动到底部
        function scrollToBottom() {
            setTimeout(function() {
                window.scrollTo(0, document.body.scrollHeight);
            }, 100);
        }
    </script>
</body>
</html>