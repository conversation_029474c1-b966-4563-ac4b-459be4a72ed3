<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化单页编辑页面样式 */
                .pagelist-edit-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .pagelist-edit-container {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .pagelist-edit-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .pagelist-edit-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
                }

                .pagelist-edit-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .pagelist-edit-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .pagelist-edit-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .pagelist-edit-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .pagelist-edit-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .pagelist-edit-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .pagelist-edit-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .pagelist-edit-back-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .pagelist-edit-back-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化表单容器 */
                .pagelist-edit-form-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    margin-bottom: 2rem;
                }

                .pagelist-edit-form-header {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .pagelist-edit-form-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .pagelist-edit-form-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .pagelist-edit-form-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .pagelist-edit-form-body {
                    padding: 2rem;
                }

                .pagelist-edit-form-group {
                    margin-bottom: 2rem;
                }

                .pagelist-edit-form-group:last-child {
                    margin-bottom: 0;
                }

                .pagelist-edit-form-label {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 0.75rem;
                }

                .pagelist-edit-form-label-icon {
                    width: 1.5rem;
                    height: 1.5rem;
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    border-radius: 0.25rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 0.75rem;
                }

                .pagelist-edit-form-input {
                    width: 100%;
                    padding: 1rem 1.25rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-family: inherit;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: #f8fafc;
                    color: #374151;
                }

                .pagelist-edit-form-input:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                    background: white;
                }

                .pagelist-edit-form-textarea {
                    width: 100%;
                    padding: 1rem 1.25rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-family: inherit;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: #f8fafc;
                    color: #374151;
                    line-height: 1.5;
                    min-height: 400px;
                    resize: vertical;
                }

                .pagelist-edit-form-textarea:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                    background: white;
                }

                .pagelist-edit-form-help {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-top: 0.5rem;
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;
                }

                .pagelist-edit-form-help i {
                    color: #3b82f6;
                }

                /* 富文本编辑器容器样式 */
                .pagelist-edit-editor-container {
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    overflow: hidden;
                    background: white;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .pagelist-edit-editor-container:focus-within {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
            </style>

            <div class="pagelist-edit-wrapper">
                <div class="pagelist-edit-container">
                    <!-- 现代化页面标题 -->
                    <div class="pagelist-edit-header pagelist-edit-fade-in">
                        <div class="pagelist-edit-header-content">
                            <div class="pagelist-edit-title">
                                <div class="pagelist-edit-title-icon">
                                    <i class="fa fa-{:$row ? 'edit' : 'plus'}"></i>
                                </div>
                                <div class="pagelist-edit-title-text">
                                    <h1 class="pagelist-edit-title-main">单页{:$row ? '编辑' : '添加'}</h1>
                                    <p class="pagelist-edit-title-sub">Page {:$row ? 'Edit' : 'Create'}</p>
                                </div>
                            </div>
                            <div class="pagelist-edit-actions">
                                <a href="{:U('Pagelist/index')}" class="pagelist-edit-back-btn">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化表单 -->
                    <form action="" method="post" class="pagelist-edit-form" enctype="multipart/form-data" id="pagelistEditForm">
                        <div class="pagelist-edit-form-container pagelist-edit-fade-in-delay-1">
                            <div class="pagelist-edit-form-header">
                                <div class="pagelist-edit-form-icon">
                                    <i class="fa fa-file-text-o"></i>
                                </div>
                                <h3 class="pagelist-edit-form-title">单页信息</h3>
                            </div>
                            <div class="pagelist-edit-form-body">
                                <!-- 单页名称 -->
                                <div class="pagelist-edit-form-group">
                                    <label class="pagelist-edit-form-label">
                                        <div class="pagelist-edit-form-label-icon">
                                            <i class="fa fa-heading"></i>
                                        </div>
                                        单页名称
                                    </label>
                                    <input type="text" name="title" class="pagelist-edit-form-input" value="{$row.title}" placeholder="请输入单页名称..." />
                                    <div class="pagelist-edit-form-help">
                                        <i class="fa fa-info-circle"></i>
                                        请输入简洁明了的单页名称，建议不超过50个字符
                                    </div>
                                </div>

                                <!-- 单页内容 -->
                                <div class="pagelist-edit-form-group">
                                    <label class="pagelist-edit-form-label">
                                        <div class="pagelist-edit-form-label-icon">
                                            <i class="fa fa-file-text"></i>
                                        </div>
                                        单页内容
                                    </label>
                                    <div class="pagelist-edit-editor-container">
                                        <textarea name="content" rows="4" class="pagelist-edit-form-textarea richtext">{$row.content}</textarea>
                                    </div>
                                    <div class="pagelist-edit-form-help">
                                        <i class="fa fa-info-circle"></i>
                                        支持富文本编辑，可以插入图片、链接等多媒体内容
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="pagelist-edit-actions-container pagelist-edit-fade-in-delay-2">
                            <div class="pagelist-edit-actions-left">
                                <div style="display: flex; align-items: center; gap: 0.5rem; color: #6b7280; font-size: 1.25rem;">
                                    <i class="fa fa-info-circle"></i>
                                    <span>请仔细检查信息后提交</span>
                                </div>
                            </div>
                            <div class="pagelist-edit-actions-right">
                                <input type="hidden" name="id" value="{$row.id}"/>
                                <a href="{:U('Pagelist/index')}" class="pagelist-edit-btn btn-secondary">
                                    <i class="fa fa-times"></i>
                                    <span>取消</span>
                                </a>
                                <button type="button" class="pagelist-edit-btn btn-success" onclick="previewPage()">
                                    <i class="fa fa-eye"></i>
                                    <span>预览</span>
                                </button>
                                <button type="submit" name="submit" class="pagelist-edit-btn btn-primary">
                                    <i class="fa fa-save"></i>
                                    <span>{:$row ? '更新' : '保存'}</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white;">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="previewModalLabel">
                    <i class="fa fa-eye"></i> 单页预览
                </h4>
            </div>
            <div class="modal-body" id="previewContent" style="padding: 2rem; min-height: 300px;">
                <!-- 预览内容将在这里显示 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <i class="fa fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    /* 操作按钮区域样式 */
    .pagelist-edit-actions-container {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #e2e8f0;
        padding: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .pagelist-edit-actions-left {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .pagelist-edit-actions-right {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .pagelist-edit-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        font-size: 1.5rem;
        font-weight: 600;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        min-width: 120px;
        justify-content: center;
    }

    .pagelist-edit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        text-decoration: none;
    }

    .pagelist-edit-btn.btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
    }

    .pagelist-edit-btn.btn-primary:hover {
        color: white;
    }

    .pagelist-edit-btn.btn-secondary {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        color: white;
    }

    .pagelist-edit-btn.btn-secondary:hover {
        color: white;
    }

    .pagelist-edit-btn.btn-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }

    .pagelist-edit-btn.btn-success:hover {
        color: white;
    }

    /* 响应式设计 */
    @media (max-width: 1024px) {
        .pagelist-edit-container {
            padding: 1.5rem;
        }

        .pagelist-edit-header-content {
            flex-direction: column;
            text-align: center;
        }
    }

    @media (max-width: 768px) {
        .pagelist-edit-container {
            padding: 1rem;
        }

        .pagelist-edit-title-main {
            font-size: 1.75rem;
        }

        .pagelist-edit-title-sub {
            font-size: 1.25rem;
        }

        .pagelist-edit-actions-container {
            flex-direction: column;
            align-items: stretch;
        }

        .pagelist-edit-actions-left,
        .pagelist-edit-actions-right {
            width: 100%;
            justify-content: center;
        }

        .pagelist-edit-btn {
            width: 100%;
        }
    }

    /* 动画效果 */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .pagelist-edit-fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    .pagelist-edit-fade-in-delay-1 {
        animation: fadeInUp 0.6s ease-out 0.1s both;
    }

    .pagelist-edit-fade-in-delay-2 {
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }
</style>

<script>
    $(document).ready(function() {
        // 表单验证和提交
        $('#pagelistEditForm').on('submit', function(e) {
            e.preventDefault();

            var title = $('input[name="title"]').val().trim();
            var content = $('textarea[name="content"]').val().trim();

            // 表单验证
            if (!title) {
                layer.msg('请输入单页名称', {icon: 2});
                $('input[name="title"]').focus();
                return false;
            }

            if (title.length > 100) {
                layer.msg('单页名称不能超过100个字符', {icon: 2});
                $('input[name="title"]').focus();
                return false;
            }

            if (!content) {
                layer.msg('请输入单页内容', {icon: 2});
                $('textarea[name="content"]').focus();
                return false;
            }

            // 显示加载状态
            var $submitBtn = $(this).find('button[type="submit"]');
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true);
            $submitBtn.html('<i class="fa fa-spinner fa-spin"></i><span>保存中...</span>');

            // 提交表单
            var formData = new FormData(this);

            $.ajax({
                url: window.location.href,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                timeout: 30000,
                success: function(response) {
                    console.log('表单提交成功:', response);

                    // 检查响应内容
                    var responseText = typeof response === 'string' ? response : JSON.stringify(response);

                    if (responseText.includes('成功') || responseText.includes('success')) {
                        layer.msg('保存成功！', {icon: 1, time: 2000});
                        setTimeout(function() {
                            window.location.href = '{:U("Pagelist/index")}';
                        }, 2000);
                    } else if (responseText.includes('错误') || responseText.includes('失败')) {
                        layer.msg('保存失败，请重试', {icon: 2});
                    } else {
                        // 默认认为成功
                        layer.msg('保存成功！', {icon: 1, time: 2000});
                        setTimeout(function() {
                            window.location.href = '{:U("Pagelist/index")}';
                        }, 2000);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('表单提交失败:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });

                    var errorMsg = '网络错误，请重试';
                    if (status === 'timeout') {
                        errorMsg = '请求超时，请重试';
                    } else if (xhr.status === 404) {
                        errorMsg = '页面不存在，请联系管理员';
                    } else if (xhr.status === 500) {
                        errorMsg = '服务器错误，请重试';
                    }

                    layer.msg(errorMsg, {icon: 2});
                },
                complete: function() {
                    // 恢复按钮状态
                    $submitBtn.prop('disabled', false);
                    $submitBtn.html(originalText);
                }
            });
        });

        // 输入框焦点效果
        $('.pagelist-edit-form-input, .pagelist-edit-form-textarea').on('focus', function() {
            $(this).closest('.pagelist-edit-form-group').addClass('focused');
        }).on('blur', function() {
            $(this).closest('.pagelist-edit-form-group').removeClass('focused');
        });

        // 字符计数
        $('input[name="title"]').on('input', function() {
            var length = $(this).val().length;
            var maxLength = 100;
            var $help = $(this).siblings('.pagelist-edit-form-help');

            if (length > maxLength * 0.8) {
                $help.html('<i class="fa fa-exclamation-triangle" style="color: #f59e0b;"></i> 单页名称长度: ' + length + '/' + maxLength + ' 字符');
            } else {
                $help.html('<i class="fa fa-info-circle"></i> 请输入简洁明了的单页名称，建议不超过50个字符');
            }
        });
    });

    // 预览功能
    function previewPage() {
        var title = $('input[name="title"]').val().trim();
        var content = $('textarea[name="content"]').val().trim();

        if (!title) {
            layer.msg('请先输入单页名称', {icon: 2});
            return;
        }

        if (!content) {
            layer.msg('请先输入单页内容', {icon: 2});
            return;
        }

        // 构建预览内容
        var previewHtml = '<div style="padding: 1rem;">';
        previewHtml += '<div style="border-bottom: 2px solid #e2e8f0; padding-bottom: 1rem; margin-bottom: 2rem;">';
        previewHtml += '<h2 style="font-size: 2rem; font-weight: 600; color: #1f2937; margin: 0;">' + title + '</h2>';
        previewHtml += '</div>';
        previewHtml += '<div style="line-height: 1.6; color: #374151; font-size: 1.5rem;">';
        previewHtml += content.replace(/\n/g, '<br>');
        previewHtml += '</div>';
        previewHtml += '</div>';

        $('#previewContent').html(previewHtml);
        $('#previewModal').modal('show');
    }

    // 添加自定义样式
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .pagelist-edit-form-group.focused .pagelist-edit-form-label {
                color: #3b82f6 !important;
            }

            .pagelist-edit-form-group.focused .pagelist-edit-form-label-icon {
                background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
                transform: scale(1.1);
            }

            .modal-content {
                border-radius: 1rem !important;
                border: none !important;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
            }

            .modal-header {
                border-radius: 1rem 1rem 0 0 !important;
                border-bottom: none !important;
            }

            .modal-footer {
                border-top: 1px solid #e2e8f0 !important;
                border-radius: 0 0 1rem 1rem !important;
            }
        `)
        .appendTo('head');
</script>

<include file="block/footer" />

<script>
    // 保留原有的富文本编辑器功能
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
            u.editor($('.richtext1')[0]);
        })
    });
</script>