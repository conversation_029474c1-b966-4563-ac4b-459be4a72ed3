<php>if ($list) { foreach($list as $v) {</php>


<li>
    <div class="a">
        <a class="title">
            <php>if ($v['is_top'] == 1) {</php>
            <i style="padding: 2px 5px;font-size: 13px;">置顶</i>
            <php>}</php>
            <i style="background-color: #10b35e;font-size: 12px;padding: 2px 5px;" id="comtepy_{:$v['id']}">{:$categoryList[$projectList[$v['project_id']]['category']]}</i>
            <h3><div id="texttitle_{:$v['id']}" class="ui-nowrap-multi" style="line-height: 1.3; max-height: 2.6em;">{:$projectList[$v['project_id']]['name']} {:$v['job_name']}</div></h3> 
        </a>
 

        <div class="prices">
            <php>if($v['is_free']==1){</php>
                <div style="font-style:normal; background-color:#10b35e; color:#fff; padding:4px; border-radius:3px; margin-right:5px;font-size: 13px;">免费公益</div>
                <php>}</php>
            <div style="font-size:14px; border:1px solid #db5410; color:#db5410;margin-right:3px; padding:0 5px;float: left;">
                {:$v['min_age']?:0}-{:$v['max_age']?:0}岁 {:$sexList[$v['sex']]['text']} {:$qualificationList[$v['qualification']]['text']} {:$v['major']?:''} </div>
        </div>


        <php>if (!empty($v['tag'])) {</php>
        <div class="prices">
            <php>
                $tagArr = explode(',',$v['tag']);
                foreach ($tagArr as $tagRows){
            </php>
            <div style="font-size:14px; border:1px solid #db5410; color:#db5410;margin-right:3px; padding:0 2px;float: left;margin-bottom: 5px;">
                {:$tagRows}
            </div>
            <php>}</php>
        </div>
        <php>}</php>

        <php>
            if (!empty($v['img_url_one']) || !empty($v['img_url_two']) || !empty($v['img_url_three'])) {</php>
        <div class="imgs clearfix">
            <php>if (!empty($v['img_url_one'])) {</php>
            <a href="javascript:void(0);" ><img class="thumbnail" style="height: 104px;border: 1px solid rgb(74, 69, 69);" src="http://we.zhongcaiguoke.com/{:$v['img_url_one']}" alt=""></a>
            <php>}</php>
            <php>if (!empty($v['img_url_two'])) {</php>
            <a href="javascript:void(0);" ><img class="thumbnail" style="height: 104px;border: 1px solid rgb(74, 69, 69);" src="http://we.zhongcaiguoke.com/{:$v['img_url_two']}" alt=""></a>
            <php>}</php>
            <php>if (!empty($v['img_url_three'])) {</php>
            <a href="javascript:void(0);"><img class="thumbnail" style="height: 104px;border: 1px solid rgb(74, 69, 69);" src="http://we.zhongcaiguoke.com/{:$v['img_url_three']}" alt=""></a>
            <php>}</php>
        </div>
        <php>}</php>
        <php>if (!empty(htmlspecialchars_decode($v['other_content']))){</php>

            <div class="text-container">
                <div class="collapsible-text collapsed js_copy" id="textContent_{:$v['id']}" data-id="{:$v['id']}">
                    {:trim(htmlspecialchars_decode($v['other_content']))}
                </div>
                <div class="toggle-btn" onclick="toggleText({:$v['id']})">

                    <div class="arrow" style="width: 100%;text-align: center;color: #15461f;font-size: 13px;background-color: #fdf9f9;margin-top: 4px;padding-top:5px;margin-bottom:2px;">
                        >>展开/收起内容<<
                    </div>
                </div>
            </div>
 
            <php>}</php>
    
        <div class="states">
            <div class="state"><span> </span></div>
                <div class="righta">

                    <a href="javascript:void(0);" class="a2"><i class="iconfont icon-bianji"></i>报名培训</a>


                    <a href="javascript:void(0)" data-id="{:$v['id']}" class="a2 js_copy">
                    <i class="iconfont icon-24_beizhu"></i>复制岗位信息</a>

           

                </div>
        </div>
        <div class="sr">
            <div class="p"><span>已报名 {:$v['sign_up_num'] ? : 0} 人</span><span>服务中 {:$v['service_num'] ?:0} 人</span><span>已服务 {:$v['succ_service_num'] ? : 0} 人</span></div>
        </div>

    </div>
</li>
<php>}}</php>



