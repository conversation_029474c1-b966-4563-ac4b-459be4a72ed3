<?php

namespace Station\Controller;

use Think\Controller;
use Think\Log;

use \LaneWeChat\Core as WE;

//带参二维码关注
class CallbackController extends Controller
{

    public $conf = '';

    private $request;
    public $id = 1;

    public function _initialize()
    {
        $row = D("Service")->where(['id' => $this->id])->find();
        if (!$row) return;
        $this->conf = [
            'WX_TOKEN' => $row['wx_token'],
            'WX_APPID' => $row['appid'],
            'WX_APPSECRET' => $row['secret'],
        ];
        vendor('LaneWeChat.lanewechat');
        $raw = file_get_contents('php://input');
        $xml = (array)simplexml_load_string($raw, 'SimpleXMLElement', LIBXML_NOCDATA);
        $this->request = array_change_key_case($xml, CASE_LOWER);
        log::record(json_encode($this->request));
        We\Base::init($this->conf);
        if (!WE\Wechat::validateSignature($this->conf['WX_TOKEN'])) {
            dolog('error/wx/call2', 'failed to validateSignature, raw:' . $raw . '; get:' . serialize(I("get.")));
        }
        dolog('error/wx/call1_sub2', json_encode($this->request));
    }

    /**
     * @descrpition 入口
     */
    public function index()
    {
        if (empty($this->request)) return;
        $this->switchType();
    }

    /**
     * @descrpition 分发请求
     */
    private function switchType()
    {
        $request = $this->request;
        switch ($request['msgtype']) {
            case 'event':
                $request['event'] = strtolower($request['event']);
                switch ($request['event']) {
                    case 'scan':
                    case 'subscribe':
                        $this->subscribe();
                        break;
                    case 'unsubscribe':
                        $this->unSubscribe();
                        break;
                    case 'click':
                    default:
                        break;
                }
                break;
            case 'text':
            default:
                break;
        }
    }


    public function upladomedia($qrcode, $conf = [])
    {
        $simg = file_get_contents(qnimg($qrcode));
        if (!$simg) {

        }
        $finfo = getimagesizefromstring($simg);
        $ext = substr($finfo['mime'], 6);
        if ($ext == 'jpeg') $ext = 'jpg';
        $name = md5($simg) . ".$ext";
        $path = SITE_PATH . C("UPLOADPATH") . '/' . md5($simg) . $name;
        $res = file_put_contents($path, $simg);
        $resRow = WE\Media::upload($path);
        @unlink($path);
        if (isset($resRow['errcode']) && $resRow['errcode']) {
            dolog('error/wx/uplaodimg', json_encode($resRow, JSON_UNESCAPED_UNICODE)); // 日志记录
            return false;
        } else {
            return $resRow['media_id'];
        }
    }


    /**
     * 关注
     */
    private function subscribe()
    {
        if (isset($this->request['ticket'])) {
            //是带参二维码进来的 判断是否是永久二维码
            $qrcodeRow = D("Qrcode")->where(['ticket' => trim($this->request['ticket'])])->find();
            if ($qrcodeRow) { //是永久二维码
                log::record("data:" . json_encode($this->request));
                $this->defaultReply($qrcodeRow['id']);
                return;
            } else { //判断是否是临时二维码
                $this->defaultReply();
            }
        } else {
            $this->defaultReply();
        }
        return;
    }

    /**
     * 永久带参默认关注回复
     */
    public function defaultReply($qrcode_id = 0)
    {
        $id = $this->id;
        $sub_content = D("Service")->where(['id' => $id])->getField('sub_content');
        $row = D("User")->where(['openid' => $this->request['fromusername']])->find();
        if ($row || !$row['subscribe_status']) {
            D("User")->save(['id' => $row['id'], 'subscribe_status' => 1]);
        }
        $replaceName = '';
        $qrcodeRow = [];
        if ($qrcode_id > 0) {
            $qrcodeRow = D("Qrcode")->where(['id' => $qrcode_id])->find();
            if ($qrcodeRow) {
                $serviceStationRow = D("ServiceStation")->where(['id' => $qrcodeRow['service_station_id']])->find();
                if ($serviceStationRow) {
                    $replaceName = $serviceStationRow['service_name'];
                }
            }
        }
        if ($row) {

        } else {
            $wx_userinfo = WE\UserManage::getUserInfo($this->request['fromusername']);
            if (isset($wx_userinfo['errcode']) && $wx_userinfo['errcode']) {
                dolog('error/wx/call', "failed to getuserinfo, return data:" . json_encode($wx_userinfo, JSON_UNESCAPED_UNICODE)); // 日志记录
                exit;
            }
            dolog('error/wx/call', "11111:" . json_encode($wx_userinfo, JSON_UNESCAPED_UNICODE)); // 日志记录

            $data_wx = [
                'openid' => (string)$this->request['fromusername'],
                'unionid' => $wx_userinfo['unionid'],
                'service_id' => $id,
                'qrcode_id' => $qrcode_id,
                'service_station_id' => $qrcodeRow ? $qrcodeRow['service_station_id'] : 0,
                'nickname' => (string)$wx_userinfo['nickname'],
                'headimgurl' => $wx_userinfo['headimgurl'],
                'sex' => (int)$wx_userinfo['sex'],
                'province' => (string)$wx_userinfo['province'],
                'city' => (string)$wx_userinfo['city'],
                'country' => (string)$wx_userinfo['country'],
                'subscribe_status' => 1,
                'subscribe' => time(),
                'created' => time(),
                'updated' => time(),
            ];
            try {
                D("User")->add($data_wx);
            } catch (\Exception $e) {

            }

        }

        $content = htmlspecialchars_decode(preg_replace('/\r\n/', "\n", $sub_content));
        $content = str_replace('%渠道名称%', $replaceName, $content);
        if (!empty($content)) {
            echo WE\ResponsePassive::text($this->request['fromusername'], $this->request['tousername'], $content);
        }
    }

    /**
     * 取消关注
     */
    private function unSubscribe()
    {
        $openid = $this->request['fromusername'];
        $user = D('User')->where(['openid' => $openid])->find();
        if ($user && $user['subscribe_status']) {
            D('User')->save(['id' => $user['id'], 'subscribe_status' => 0, 'unsubscribe' => time()]);
        }

    }

    public function __destruct()
    {
        parent::__destruct();
        if (WE\Wechat::isValid() && WE\Wechat::validateSignature($this->conf['WX_TOKEN'])) {
            echo $_GET['echostr'];
        } else {
            echo ""; // 默认返回，临时方案
        }
    }

}

// EOF
