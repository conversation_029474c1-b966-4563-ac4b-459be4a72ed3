<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Servicestation/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">服务站{:$row ? '编辑' : '添加'}</div>
                    <div class="panel-body">

                        <div class="form-group">
							<label class="col-xs-12 col-sm-3 col-md-2 control-label">公司名称</label>
							<div class="col-sm-9 col-xs-12">
								<input type="text" name="enterprise_name" class="form-control" value="{$row.enterprise_name}" />
							</div>
                        </div>


                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">信用代码</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="credit_code" class="form-control" value="{$row.credit_code}" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">电子邮件</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="email" class="form-control" value="{$row.email}" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">手机号</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="mobile" class="form-control" value="{$row.mobile}" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">所属上级ID</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="pid" class="form-control" value="{$row.pid}" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">联络人姓名</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="contract_name" class="form-control" value="{$row.contract_name}" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">联络人身份证</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="contract_card" class="form-control" value="{$row.contract_card}" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">服务站级别</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="level" class="form-control sp_input ">
                                    <option value="0" >请选择服务站级别</option>
                                    <php>foreach($levelList as $key => $val) {</php>
                                    <option value="{$key}" {: $key == $row['level'] ? 'selected' : ''}>{$val.text}</option>
                                    <php>}</php>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">类型</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="type" class="form-control sp_input ">
                                    <option value="0" >请选择类型</option>
                                    <php>foreach($typeList as $key => $val) {</php>
                                    <option value="{$key}" {: $key == $row['type'] ? 'selected' : ''}>{$val.text}</option>
                                    <php>}</php>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">资源包介绍</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="resources" class="form-control sp_input ">
                                    <option value=" " >请选择类型</option>
                                    <php>foreach($resourcesList as $key => $val) {</php>
                                    <option value="{$key}" {: $key == $row['resources'] ? 'selected' : ''}>{$val.text}</option>
                                    <php>}</php>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">资源包划拨</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="is_out" class="form-control sp_input ">
                                    <option value=" " >请选择</option>
                                    <php>foreach($isoutList as $key => $val) {</php>
                                    <option value="{$key}" {: $key == $row['is_out'] ? 'selected' : ''}>{$val.text}</option>
                                    <php>}</php>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">是否外部显示</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="show" class="form-control sp_input ">
                                    <option value=" " >请选择</option>
                                    <php>foreach($isshowList as $key => $val) {</php>
                                    <option value="{$key}" {: $key == $row['show'] ? 'selected' : ''}>{$val.text}</option>
                                    <php>}</php>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">内部用户</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="users_id" class="form-control sp_input ">
                                    <option value="0" >请内部用户</option>
                                    <php>foreach($usersList as $key => $val) {</php>
                                    <option value="{$key}" {: $key == $row['users_id'] ? 'selected' : ''}>{$val}</option>
                                    <php>}</php>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">手机号</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="mobile" class="form-control" value="{$row.mobile}" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">合同编号</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="contract_number" class="form-control" value="{$row.contract_number}" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">通讯地址</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="mail_address" class="form-control" value="{$row.mail_address}" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">服务站名称</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="service_name" class="form-control" value="{$row.service_name}" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">服务站编号</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="service_number" class="form-control" value="{$row.service_number}" />
                            </div>
                        </div>

                        <!--div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">允许开启服务站数量</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="open_num" class="form-control" value="{$row.open_num}" />
                            </div>
                        </div-->

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style='color:red'>*</span>合作协议</label>
                            <div class="col-sm-9 col-xs-12">
                                <php>
                                    echo tpl_form_field_file('cooperation_agreement', $row['cooperation_agreement'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                </php>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style='color:red'>*</span>营业执照</label>
                            <div class="col-sm-9 col-xs-12">
                                <php>
                                    echo tpl_form_field_image('business_license',  $row['business_license'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                </php>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style='color:red'>*</span>申请加盟表</label>
                            <div class="col-sm-9 col-xs-12">
                                <php>
                                    echo tpl_form_field_image('franchise_list', $row['franchise_list'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                </php>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style='color:red'>*</span>展示图片1</label>
                            <div class="col-sm-9 col-xs-12">
                                <php>
                                    echo tpl_form_field_image('img_url_one', $row['img_url_one'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                </php>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style='color:red'>*</span>展示图片2</label>
                            <div class="col-sm-9 col-xs-12">
                                <php>
                                    echo tpl_form_field_image('img_url_two', $row['img_url_two'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                </php>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style='color:red'>*</span>展示图片3</label>
                            <div class="col-sm-9 col-xs-12">
                                <php>
                                    echo tpl_form_field_image('img_url_three', $row['img_url_three'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                </php>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">备注说明</label>
                            <div class="col-sm-9 col-xs-12">
                                <textarea name="content" rows="4" class="col-sm-9 col-xs-12 richtext" >{$row.content}</textarea>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="form-group col-sm-12">
					<input type="hidden" name="id" value="{$row.id}"/>
					<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			    </div>
            </div>
            </form>
        </div>
    </div>
</div>
<include file="block/footer" />
<script>

    //function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
            u.editor($('.richtext1')[0]);

        })
    });

    require(['primeImageCompressor'], function(primeImageCompressor) {
        primeImageCompressor.init();
    });
</script>