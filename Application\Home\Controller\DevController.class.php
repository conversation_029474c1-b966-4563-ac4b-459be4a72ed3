<?php

namespace Home\Controller;

use Think\Controller;

/**
 * 开发者模式控制器
 * 用于在生产环境中激活开发者模式，跳过微信检测
 */
class DevController extends Controller
{
    /**
     * 开发者模式激活页面
     */
    public function activate()
    {
        $key = I('get.key', '');
        $action = I('get.action', '');
        
        // 处理激活请求
        if ($key) {
            $result = $this->activateDeveloperMode($key);
            if ($result['success']) {
                $this->success($result['message'], null, 3);
            } else {
                $this->error($result['message']);
            }
            return;
        }
        
        // 处理清除请求
        if ($action === 'clear') {
            $this->clearDeveloperMode();
            $this->success('开发者模式已清除', null, 3);
            return;
        }
        
        // 显示激活页面
        $this->display();
    }
    
    /**
     * 激活开发者模式
     * @param string $key 密钥
     * @return array
     */
    private function activateDeveloperMode($key)
    {
        // 获取配置的开发者密钥
        $validKey = C('DEVELOPER_MODE_KEY');
        if (empty($validKey)) {
            return [
                'success' => false,
                'message' => '开发者模式未配置'
            ];
        }
        
        // 验证密钥
        if ($key !== $validKey) {
            // 记录失败尝试
            $this->logFailedAttempt();
            return [
                'success' => false,
                'message' => '密钥错误'
            ];
        }
        
        // 设置开发者模式Cookie
        $cookieName = 'dev_mode_' . md5($validKey);
        $cookieValue = md5($key . time() . $_SERVER['HTTP_USER_AGENT']);
        $expireTime = time() + (30 * 24 * 3600); // 30天有效期
        
        // 设置Cookie
        setcookie($cookieName, $cookieValue, $expireTime, '/', '', false, true);
        
        // 记录激活日志
        $this->logActivation();
        
        return [
            'success' => true,
            'message' => '开发者模式已激活，有效期30天'
        ];
    }
    
    /**
     * 清除开发者模式
     */
    private function clearDeveloperMode()
    {
        $validKey = C('DEVELOPER_MODE_KEY');
        if ($validKey) {
            $cookieName = 'dev_mode_' . md5($validKey);
            setcookie($cookieName, '', time() - 3600, '/', '', false, true);
        }
        
        // 记录清除日志
        $this->logClear();
    }
    
    /**
     * 检查开发者模式状态
     */
    public function status()
    {
        // 安全检查：如果没有配置开发者密钥，直接返回未激活状态
        $validKey = C('DEVELOPER_MODE_KEY');
        if (empty($validKey)) {
            $data = [
                'developer_mode' => false,
                'configured' => false,
                'message' => '开发者模式未配置'
            ];
            header('Content-Type: application/json');
            echo json_encode($data, JSON_UNESCAPED_UNICODE);
            exit;
        }

        $isDeveloper = $this->checkDeveloperMode();
        $host = $_SERVER['HTTP_HOST'] ?? '';
        $isProduction = in_array($host, [
            'we.zhongcaiguoke.com',
            'station.zhongcaiguoke.com',
            'zhongcaiguoke.com',
            'www.zhongcaiguoke.com'
        ]);

        $data = [
            'developer_mode' => $isDeveloper,
            'configured' => true,
            'is_production' => $isProduction,
            'client_ip' => get_client_ip(),
            'current_hour' => (int)date('H'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'is_wechat' => isWX(),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        // 生产环境额外信息
        if ($isProduction) {
            $allowedIPs = C('DEVELOPER_MODE_ALLOWED_IPS');
            $timeLimit = C('DEVELOPER_MODE_TIME_LIMIT');

            $data['ip_allowed'] = !empty($allowedIPs) && in_array(get_client_ip(), $allowedIPs);
            $data['time_allowed'] = true;

            if (!empty($timeLimit)) {
                $currentHour = (int)date('H');
                $data['time_allowed'] = $currentHour >= $timeLimit['start'] && $currentHour <= $timeLimit['end'];
                $data['time_limit'] = $timeLimit;
            }

            $data['allowed_ips'] = $allowedIPs ?? [];
        }

        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 检测当前是否为开发者模式
     * @return bool
     */
    private function checkDeveloperMode()
    {
        $validKey = C('DEVELOPER_MODE_KEY');
        if (empty($validKey)) {
            return false;
        }
        
        $cookieName = 'dev_mode_' . md5($validKey);
        return !empty($_COOKIE[$cookieName]);
    }
    
    /**
     * 记录激活日志
     */
    private function logActivation()
    {
        $logData = [
            'action' => 'activate',
            'ip' => get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'time' => date('Y-m-d H:i:s')
        ];
        
        dolog('developer_mode/activate', json_encode($logData, JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * 记录清除日志
     */
    private function logClear()
    {
        $logData = [
            'action' => 'clear',
            'ip' => get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'time' => date('Y-m-d H:i:s')
        ];
        
        dolog('developer_mode/clear', json_encode($logData, JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * 记录失败尝试
     */
    private function logFailedAttempt()
    {
        $logData = [
            'action' => 'failed_attempt',
            'ip' => get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'time' => date('Y-m-d H:i:s')
        ];
        
        dolog('developer_mode/failed', json_encode($logData, JSON_UNESCAPED_UNICODE));
    }
}
