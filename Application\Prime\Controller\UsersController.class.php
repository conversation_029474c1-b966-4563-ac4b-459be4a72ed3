<?php

namespace Prime\Controller;
use Common\Controller\PrimeController;

class UsersController extends PrimeController
{
    
    public function _initialize()
    {
        parent::_initialize();
    }

    public function index()
    {
		$count = D('Users')->where(array("user_type"=>1))->count();
		$page = $this->page($count, 20);
		$users = D('Users') ->where(["user_type"=>1])->order("id DESC")->limit($page->firstRow.','.$page->listRows)->select();
        $roles = focus(D('Role')->where(['status' => 1])->field(['id','name'])->select(), 'id');
		$this->assign("page", $page->show());
		$this->assign("roles",$roles);
		$this->assign("list",$users);
		$this->display();
	}

    /**
     * 新增服务站
     */
    public function addservicestation() {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误!');
        $obj = D("Users");
        $row = $obj->where(['id' => $id, 'status' => 1])->find();
        if (!$row) $this->error('当前用户不存在!!');
        $serviceStationRow = D("ServiceStation")->where(['users_id' => $row['id']])->find();
        if ($serviceStationRow) $this->error('当前用户已经开通过服务站!');
        $insertData = [
            'enterprise_name' => $row['username'],
            'email' => $row['emails'],
            'mobile' => $row['mobile'],
            'pid' => 0,
            'contract_name' => $row['username'],
            'contract_card' => $row['card'],
            'level' => 0,
            'type' => 2,
            'sources' => 2,
            'users_id' => $row['id'],
            'contract_number' => 0,
            'mail_address' => '中才国科（湖南）教育科技有限公司',
            'service_name' => '中才国科-'.$row['username'],
            'service_number' => 0,
            'create_time' => time(),
        ];
        $insertId = D("ServiceStation")->add($insertData);
        if ($insertId) {
            $this->success('添加服务站成功', U('servicestation/index'));
        } else {
            $this->error('用户开通服务站失败，请联系技术查看');
        }
    }

    /**
     *  添加/编辑 角色
     */
    public function edit()
    {
        C("TOKEN_ON", true);
        $id = intval(I("get.id"));
        $act = 'add';
        if ($id) {
            if ($id == 1) $this->error("超级管理员角色不能被修改！");
            $row = D("Users")->where(array("id" => $id))->find();
            if (!$row) $this->error("该角色不存在！");
            $act = 'save';
        }

        if (IS_POST) {
            if (!I('post.role_id')) $this->error('请选择角色');
    		if ($data = D("Users")->create()) {

                $data['role_id'] = I('post.role_id');
                if (! $id) {
                    $data['salt'] = makeSalt();
                    $data['pwd'] = makePwd($data['pwd'], $data['salt']);
                } else if (!empty($data['pwd'])) {
                    $data['pwd'] = makePwd($data['pwd'], $row['salt']);
                }
    			if (D("Users")->$act($data) !== false) {
    				$this->success("操作成功", U("users/index"));
    			} else {
    				$this->error("操作失败！");
    			}
    		} else {
    			$this->error(D("Users")->getError());
    		}
        }
		$roles = M('Role')->where("status=1")->order("id desc")->select();
		$this->assign("roles", $roles);
        $this->assign("row", $row);
        $this->display();
    }

	/**
	 *  删除
	 */
    function del()
    {
		$id = intval(I("get.id"));
		if($id == 1) $this->error("最高管理员不能删除！");
		if (M('Users')->where("id=$id")->delete() !== false) {
			$this->success("删除成功！");
		} else {
			$this->error("删除失败！");
		}
	}

}