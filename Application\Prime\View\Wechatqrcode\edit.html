<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化微信二维码编辑页面样式 */
                .wechatqrcode-edit-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .wechatqrcode-edit-container {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .wechatqrcode-edit-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .wechatqrcode-edit-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
                }

                .wechatqrcode-edit-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .wechatqrcode-edit-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .wechatqrcode-edit-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .wechatqrcode-edit-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .wechatqrcode-edit-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .wechatqrcode-edit-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .wechatqrcode-edit-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .wechatqrcode-edit-back-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .wechatqrcode-edit-back-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化表单容器 */
                .wechatqrcode-edit-form-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    margin-bottom: 2rem;
                }

                .wechatqrcode-edit-form-header {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .wechatqrcode-edit-form-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .wechatqrcode-edit-form-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .wechatqrcode-edit-form-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .wechatqrcode-edit-form-body {
                    padding: 2rem;
                }

                .wechatqrcode-edit-form-group {
                    margin-bottom: 2rem;
                }

                .wechatqrcode-edit-form-group:last-child {
                    margin-bottom: 0;
                }

                .wechatqrcode-edit-form-label {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 0.75rem;
                }

                .wechatqrcode-edit-form-label-icon {
                    width: 1.5rem;
                    height: 1.5rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    border-radius: 0.25rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 0.75rem;
                }

                .wechatqrcode-edit-form-textarea {
                    width: 100%;
                    padding: 1rem 1.25rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-family: inherit;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: #f8fafc;
                    color: #374151;
                    line-height: 1.5;
                    min-height: 120px;
                    resize: vertical;
                }

                .wechatqrcode-edit-form-textarea:focus {
                    outline: none;
                    border-color: #10b981;
                    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
                    background: white;
                }

                .wechatqrcode-edit-form-help {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-top: 0.5rem;
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;
                }

                .wechatqrcode-edit-form-help i {
                    color: #10b981;
                }

                /* 图片上传区域样式 */
                .wechatqrcode-edit-image-container {
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    overflow: hidden;
                    background: white;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .wechatqrcode-edit-image-container:focus-within {
                    border-color: #10b981;
                    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
                }

                /* 权重选择样式 */
                .wechatqrcode-edit-weight-options {
                    display: flex;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                .wechatqrcode-edit-weight-option {
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.25rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    background: #f8fafc;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .wechatqrcode-edit-weight-option:hover {
                    border-color: #10b981;
                    background: rgba(16, 185, 129, 0.05);
                }

                .wechatqrcode-edit-weight-option input[type="radio"] {
                    position: absolute;
                    opacity: 0;
                    width: 0;
                    height: 0;
                }

                .wechatqrcode-edit-weight-option input[type="radio"]:checked + .wechatqrcode-edit-weight-label {
                    border-color: #10b981;
                    background: rgba(16, 185, 129, 0.1);
                    color: #047857;
                }

                .wechatqrcode-edit-weight-label {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    width: 100%;
                    cursor: pointer;
                }

                .wechatqrcode-edit-weight-badge {
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1rem;
                    font-weight: 600;
                    color: white;
                }

                .wechatqrcode-edit-weight-badge.label-danger {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                }

                .wechatqrcode-edit-weight-badge.label-warning {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                }

                .wechatqrcode-edit-weight-badge.label-default {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                }

                /* 状态选择样式 */
                .wechatqrcode-edit-status-options {
                    display: flex;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                .wechatqrcode-edit-status-option {
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.25rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    background: #f8fafc;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    font-size: 1.25rem;
                    font-weight: 600;
                    min-width: 120px;
                    justify-content: center;
                }

                .wechatqrcode-edit-status-option:hover {
                    border-color: #10b981;
                    background: rgba(16, 185, 129, 0.05);
                }

                .wechatqrcode-edit-status-option input[type="radio"] {
                    position: absolute;
                    opacity: 0;
                    width: 0;
                    height: 0;
                }

                .wechatqrcode-edit-status-option input[type="radio"]:checked + .wechatqrcode-edit-status-label {
                    border-color: #10b981;
                    background: rgba(16, 185, 129, 0.1);
                    color: #047857;
                }

                .wechatqrcode-edit-status-label {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    width: 100%;
                    cursor: pointer;
                    justify-content: center;
                }
            </style>

            <div class="wechatqrcode-edit-wrapper">
                <div class="wechatqrcode-edit-container">
                    <!-- 现代化页面标题 -->
                    <div class="wechatqrcode-edit-header wechatqrcode-edit-fade-in">
                        <div class="wechatqrcode-edit-header-content">
                            <div class="wechatqrcode-edit-title">
                                <div class="wechatqrcode-edit-title-icon">
                                    <i class="fa fa-{:$row ? 'edit' : 'plus'}"></i>
                                </div>
                                <div class="wechatqrcode-edit-title-text">
                                    <h1 class="wechatqrcode-edit-title-main">微信二维码{:$row ? '编辑' : '添加'}</h1>
                                    <p class="wechatqrcode-edit-title-sub">WeChat QR Code {:$row ? 'Edit' : 'Create'}</p>
                                </div>
                            </div>
                            <div class="wechatqrcode-edit-actions">
                                <a href="{:U('Wechatqrcode/index')}" class="wechatqrcode-edit-back-btn">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化表单 -->
                    <form action="" method="post" class="wechatqrcode-edit-form" enctype="multipart/form-data" id="wechatqrcodeEditForm">
                        <div class="wechatqrcode-edit-form-container wechatqrcode-edit-fade-in-delay-1">
                            <div class="wechatqrcode-edit-form-header">
                                <div class="wechatqrcode-edit-form-icon">
                                    <i class="fa fa-qrcode"></i>
                                </div>
                                <h3 class="wechatqrcode-edit-form-title">微信二维码信息</h3>
                            </div>
                            <div class="wechatqrcode-edit-form-body">
                                <!-- 二维码图片 -->
                                <div class="wechatqrcode-edit-form-group">
                                    <label class="wechatqrcode-edit-form-label">
                                        <div class="wechatqrcode-edit-form-label-icon">
                                            <i class="fa fa-image"></i>
                                        </div>
                                        二维码图片
                                    </label>
                                    <div class="wechatqrcode-edit-image-container">
                                        <php>echo tpl_form_field_image('image_path', $row['image_path'], '', ['type'=>4, 'extras' => ['text' => 'readonly', 'moments' => 11], 'tabs' => ['upload' => 'active']]);</php>
                                    </div>
                                    <div class="wechatqrcode-edit-form-help">
                                        <i class="fa fa-info-circle"></i>
                                        请上传清晰的微信好友二维码图片，建议尺寸为正方形
                                    </div>
                                </div>

                                <!-- 提示文字 -->
                                <div class="wechatqrcode-edit-form-group">
                                    <label class="wechatqrcode-edit-form-label">
                                        <div class="wechatqrcode-edit-form-label-icon">
                                            <i class="fa fa-comment"></i>
                                        </div>
                                        提示文字
                                    </label>
                                    <textarea name="tip_text" class="wechatqrcode-edit-form-textarea" rows="3" placeholder="请输入二维码下方显示的提示文字">{$row.tip_text}</textarea>
                                    <div class="wechatqrcode-edit-form-help">
                                        <i class="fa fa-info-circle"></i>
                                        此文字将显示在二维码下方，提示用户扫码添加好友的原因或好处
                                    </div>
                                </div>

                                <!-- 权重设置 -->
                                <div class="wechatqrcode-edit-form-group">
                                    <label class="wechatqrcode-edit-form-label">
                                        <div class="wechatqrcode-edit-form-label-icon">
                                            <i class="fa fa-star"></i>
                                        </div>
                                        权重等级
                                    </label>
                                    <div class="wechatqrcode-edit-weight-options">
                                        <php>foreach($weightLevels as $weight => $level) {</php>
                                        <div class="wechatqrcode-edit-weight-option">
                                            <input type="radio" name="weight" value="{$weight}" id="weight_{$weight}" <if condition="(isset($row['weight']) && $row['weight'] == $weight) || (!isset($row['weight']) && $weight == 2)">checked="checked"</if>>
                                            <label for="weight_{$weight}" class="wechatqrcode-edit-weight-label">
                                                <i class="fa fa-star"></i>
                                                <span class="wechatqrcode-edit-weight-badge label-{$level.style}">{$level.text}</span>
                                                <span>权重</span>
                                            </label>
                                        </div>
                                        <php>}</php>
                                    </div>
                                    <div class="wechatqrcode-edit-form-help">
                                        <i class="fa fa-info-circle"></i>
                                        权重越高被随机选中的概率越大，建议根据二维码质量设置
                                    </div>
                                </div>

                                <!-- 状态设置 -->
                                <div class="wechatqrcode-edit-form-group">
                                    <label class="wechatqrcode-edit-form-label">
                                        <div class="wechatqrcode-edit-form-label-icon">
                                            <i class="fa fa-toggle-on"></i>
                                        </div>
                                        使用状态
                                    </label>
                                    <div class="wechatqrcode-edit-status-options">
                                        <div class="wechatqrcode-edit-status-option">
                                            <input type="radio" name="status" value="1" id="status_1" <if condition="$row.status neq 0">checked="checked"</if>>
                                            <label for="status_1" class="wechatqrcode-edit-status-label">
                                                <i class="fa fa-check-circle"></i>
                                                <span>启用</span>
                                            </label>
                                        </div>
                                        <div class="wechatqrcode-edit-status-option">
                                            <input type="radio" name="status" value="0" id="status_0" <if condition="$row.status eq 0">checked="checked"</if>>
                                            <label for="status_0" class="wechatqrcode-edit-status-label">
                                                <i class="fa fa-times-circle"></i>
                                                <span>禁用</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="wechatqrcode-edit-form-help">
                                        <i class="fa fa-info-circle"></i>
                                        只有启用状态的二维码才会被随机展示给用户
                                    </div>
                                </div>
                            </div>

                        <!-- 操作按钮 -->
                        <div class="wechatqrcode-edit-actions-container wechatqrcode-edit-fade-in-delay-2">
                            <div class="wechatqrcode-edit-actions-left">
                                <div style="display: flex; align-items: center; gap: 0.5rem; color: #6b7280; font-size: 1.25rem;">
                                    <i class="fa fa-info-circle"></i>
                                    <span>请仔细检查信息后提交</span>
                                </div>
                            </div>
                            <div class="wechatqrcode-edit-actions-right">
                                <input type="hidden" name="id" value="{$row.id}"/>
                                <a href="{:U('Wechatqrcode/index')}" class="wechatqrcode-edit-btn btn-secondary">
                                    <i class="fa fa-times"></i>
                                    <span>取消</span>
                                </a>
                                <button type="button" class="wechatqrcode-edit-btn btn-success" onclick="previewQrcode()">
                                    <i class="fa fa-eye"></i>
                                    <span>预览</span>
                                </button>
                                <button type="submit" name="submit" class="wechatqrcode-edit-btn btn-primary">
                                    <i class="fa fa-save"></i>
                                    <span>{:$row ? '更新' : '保存'}</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="previewModalLabel">
                    <i class="fa fa-eye"></i> 二维码预览
                </h4>
            </div>
            <div class="modal-body" id="previewContent" style="padding: 2rem; min-height: 300px; text-align: center;">
                <!-- 预览内容将在这里显示 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <i class="fa fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    /* 操作按钮区域样式 */
    .wechatqrcode-edit-actions-container {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #e2e8f0;
        padding: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .wechatqrcode-edit-actions-left {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .wechatqrcode-edit-actions-right {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .wechatqrcode-edit-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        font-size: 1.5rem;
        font-weight: 600;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        min-width: 120px;
        justify-content: center;
    }

    .wechatqrcode-edit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        text-decoration: none;
    }

    .wechatqrcode-edit-btn.btn-primary {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }

    .wechatqrcode-edit-btn.btn-primary:hover {
        color: white;
    }

    .wechatqrcode-edit-btn.btn-secondary {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        color: white;
    }

    .wechatqrcode-edit-btn.btn-secondary:hover {
        color: white;
    }

    .wechatqrcode-edit-btn.btn-success {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
    }

    .wechatqrcode-edit-btn.btn-success:hover {
        color: white;
    }

    /* 响应式设计 */
    @media (max-width: 1024px) {
        .wechatqrcode-edit-container {
            padding: 1.5rem;
        }

        .wechatqrcode-edit-header-content {
            flex-direction: column;
            text-align: center;
        }
    }

    @media (max-width: 768px) {
        .wechatqrcode-edit-container {
            padding: 1rem;
        }

        .wechatqrcode-edit-title-main {
            font-size: 1.75rem;
        }

        .wechatqrcode-edit-title-sub {
            font-size: 1.25rem;
        }

        .wechatqrcode-edit-actions-container {
            flex-direction: column;
            align-items: stretch;
        }

        .wechatqrcode-edit-actions-left,
        .wechatqrcode-edit-actions-right {
            width: 100%;
            justify-content: center;
        }

        .wechatqrcode-edit-btn {
            width: 100%;
        }

        .wechatqrcode-edit-weight-options,
        .wechatqrcode-edit-status-options {
            flex-direction: column;
        }

        .wechatqrcode-edit-weight-option,
        .wechatqrcode-edit-status-option {
            width: 100%;
        }
    }

    /* 动画效果 */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .wechatqrcode-edit-fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    .wechatqrcode-edit-fade-in-delay-1 {
        animation: fadeInUp 0.6s ease-out 0.1s both;
    }

    .wechatqrcode-edit-fade-in-delay-2 {
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }
</style>

<script>
    $(document).ready(function() {
        // 表单验证和提交
        $('#wechatqrcodeEditForm').on('submit', function(e) {
            e.preventDefault();

            var imagePath = $('input[name="image_path"]').val();
            var tipText = $('textarea[name="tip_text"]').val().trim();
            var weight = $('input[name="weight"]:checked').val();
            var status = $('input[name="status"]:checked').val();

            // 表单验证
            if (!imagePath) {
                layer.msg('请上传二维码图片', {icon: 2});
                return false;
            }

            if (!weight) {
                layer.msg('请选择权重等级', {icon: 2});
                return false;
            }

            if (!status && status !== '0') {
                layer.msg('请选择使用状态', {icon: 2});
                return false;
            }

            if (tipText.length > 500) {
                layer.msg('提示文字不能超过500个字符', {icon: 2});
                $('textarea[name="tip_text"]').focus();
                return false;
            }

            // 显示加载状态
            var $submitBtn = $(this).find('button[type="submit"]');
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true);
            $submitBtn.html('<i class="fa fa-spinner fa-spin"></i><span>保存中...</span>');

            // 提交表单
            var formData = new FormData(this);

            $.ajax({
                url: window.location.href,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                timeout: 30000,
                success: function(response) {
                    console.log('表单提交成功:', response);

                    // 检查响应内容
                    var responseText = typeof response === 'string' ? response : JSON.stringify(response);

                    if (responseText.includes('成功') || responseText.includes('success')) {
                        layer.msg('保存成功！', {icon: 1, time: 2000});
                        setTimeout(function() {
                            window.location.href = '{:U("Wechatqrcode/index")}';
                        }, 2000);
                    } else if (responseText.includes('错误') || responseText.includes('失败')) {
                        layer.msg('保存失败，请重试', {icon: 2});
                    } else {
                        // 默认认为成功
                        layer.msg('保存成功！', {icon: 1, time: 2000});
                        setTimeout(function() {
                            window.location.href = '{:U("Wechatqrcode/index")}';
                        }, 2000);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('表单提交失败:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });

                    var errorMsg = '网络错误，请重试';
                    if (status === 'timeout') {
                        errorMsg = '请求超时，请重试';
                    } else if (xhr.status === 404) {
                        errorMsg = '页面不存在，请联系管理员';
                    } else if (xhr.status === 500) {
                        errorMsg = '服务器错误，请重试';
                    }

                    layer.msg(errorMsg, {icon: 2});
                },
                complete: function() {
                    // 恢复按钮状态
                    $submitBtn.prop('disabled', false);
                    $submitBtn.html(originalText);
                }
            });
        });

        // 输入框焦点效果
        $('.wechatqrcode-edit-form-textarea').on('focus', function() {
            $(this).closest('.wechatqrcode-edit-form-group').addClass('focused');
        }).on('blur', function() {
            $(this).closest('.wechatqrcode-edit-form-group').removeClass('focused');
        });

        // 字符计数
        $('textarea[name="tip_text"]').on('input', function() {
            var length = $(this).val().length;
            var maxLength = 500;
            var $help = $(this).siblings('.wechatqrcode-edit-form-help');

            if (length > maxLength * 0.8) {
                $help.html('<i class="fa fa-exclamation-triangle" style="color: #f59e0b;"></i> 提示文字长度: ' + length + '/' + maxLength + ' 字符');
            } else {
                $help.html('<i class="fa fa-info-circle"></i> 此文字将显示在二维码下方，提示用户扫码添加好友的原因或好处');
            }
        });

        // 权重和状态选择效果
        $('.wechatqrcode-edit-weight-option, .wechatqrcode-edit-status-option').on('click', function() {
            var $input = $(this).find('input[type="radio"]');
            $input.prop('checked', true);

            // 更新同组其他选项的样式
            var name = $input.attr('name');
            $('input[name="' + name + '"]').closest('.wechatqrcode-edit-weight-option, .wechatqrcode-edit-status-option').removeClass('selected');
            $(this).addClass('selected');
        });

        // 初始化选中状态
        $('input[type="radio"]:checked').each(function() {
            $(this).closest('.wechatqrcode-edit-weight-option, .wechatqrcode-edit-status-option').addClass('selected');
        });
    });

    // 预览功能
    function previewQrcode() {
        var imagePath = $('input[name="image_path"]').val();
        var tipText = $('textarea[name="tip_text"]').val().trim();
        var weight = $('input[name="weight"]:checked').val();
        var weightText = $('input[name="weight"]:checked').closest('.wechatqrcode-edit-weight-option').find('.wechatqrcode-edit-weight-badge').text();
        var status = $('input[name="status"]:checked').val();
        var statusText = $('input[name="status"]:checked').closest('.wechatqrcode-edit-status-option').find('span').last().text();

        if (!imagePath) {
            layer.msg('请先上传二维码图片', {icon: 2});
            return;
        }

        // 构建预览内容
        var previewHtml = '<div style="padding: 1rem;">';
        previewHtml += '<div style="text-align: center; margin-bottom: 2rem;">';
        previewHtml += '<img src="' + imagePath + '" alt="二维码预览" style="max-width: 300px; max-height: 300px; border-radius: 0.5rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">';
        previewHtml += '</div>';

        if (tipText) {
            previewHtml += '<div style="text-align: center; margin-bottom: 1.5rem;">';
            previewHtml += '<p style="color: #6b7280; font-size: 1.5rem; line-height: 1.6;">' + tipText + '</p>';
            previewHtml += '</div>';
        }

        previewHtml += '<div style="display: flex; justify-content: space-around; margin-top: 2rem;">';
        previewHtml += '<div style="text-align: center;">';
        previewHtml += '<p style="color: #374151; font-weight: 600; margin-bottom: 0.5rem;">权重等级</p>';
        previewHtml += '<span style="padding: 0.25rem 0.75rem; border-radius: 1rem; background: #10b981; color: white; font-size: 1.25rem;">' + weightText + '</span>';
        previewHtml += '</div>';
        previewHtml += '<div style="text-align: center;">';
        previewHtml += '<p style="color: #374151; font-weight: 600; margin-bottom: 0.5rem;">使用状态</p>';
        previewHtml += '<span style="padding: 0.25rem 0.75rem; border-radius: 1rem; background: ' + (status === '1' ? '#10b981' : '#6b7280') + '; color: white; font-size: 1.25rem;">' + statusText + '</span>';
        previewHtml += '</div>';
        previewHtml += '</div>';
        previewHtml += '</div>';

        $('#previewContent').html(previewHtml);
        $('#previewModal').modal('show');
    }

    // 添加自定义样式
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .wechatqrcode-edit-form-group.focused .wechatqrcode-edit-form-label {
                color: #10b981 !important;
            }

            .wechatqrcode-edit-form-group.focused .wechatqrcode-edit-form-label-icon {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
                transform: scale(1.1);
            }

            .wechatqrcode-edit-weight-option.selected,
            .wechatqrcode-edit-status-option.selected {
                border-color: #10b981 !important;
                background: rgba(16, 185, 129, 0.1) !important;
                color: #047857 !important;
            }

            .modal-content {
                border-radius: 1rem !important;
                border: none !important;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
            }

            .modal-header {
                border-radius: 1rem 1rem 0 0 !important;
                border-bottom: none !important;
            }

            .modal-footer {
                border-top: 1px solid #e2e8f0 !important;
                border-radius: 0 0 1rem 1rem !important;
            }
        `)
        .appendTo('head');
</script>

<!-- 保留原有的图片压缩功能 -->
<script>
window.addEventListener('DOMContentLoaded', function() {
    // 确保完成所有DOM加载后再执行
    setTimeout(function() {
        // 加载依赖模块
        require(['util', 'imageCompressor'], function(util, imageCompressor) {
            // 使用原生JavaScript，避免jQuery相关问题
            var MAX_IMAGE_SIZE = 100 * 1024;
            var compressedResults = {};
            window.pendingUploads = {};

            // 设置图片压缩处理
            function setupImageCompression() {
                var fileInputs = document.querySelectorAll('input[type="file"]');

                fileInputs.forEach(function(input) {
                    // 防止重复设置
                    if (input.getAttribute('data-compression-setup')) return;
                    input.setAttribute('data-compression-setup', 'true');

                    // 获取原始onchange属性
                    var originalOnChange = input.getAttribute('onchange');
                    input.removeAttribute('onchange');

                    // 添加新的事件处理器
                    input.addEventListener('change', function(e) {
                        var file = this.files[0];
                        if (!file) return;

                        // 检查文件类型是否为图片
                        if (!file.type.match(/^image\//)) {
                            if (originalOnChange) {
                                // 使用Function构造器创建一个安全的函数
                                try {
                                    var func = new Function('event', 'this', originalOnChange);
                                    func.call(this, e, this);
                                } catch(err) {
                                    console.error('执行原始onchange失败:', err);
                                }
                            }
                            return;
                        }

                        // 标记处理中状态
                        var uploadId = 'upload-' + Date.now();
                        window.pendingUploads[uploadId] = true;

                        var self = this;
                        var inputName = this.getAttribute('name');

                        // 压缩图片
                        imageCompressor.compressImage(file, MAX_IMAGE_SIZE, function(result) {
                            compressedResults[inputName] = result;

                            // 清除处理状态
                            delete window.pendingUploads[uploadId];

                            // 如果图片被压缩，显示压缩前后对比
                            if (result.compressed) {
                                setTimeout(function() {
                                    imageCompressor.showComparisonModal(result);
                                }, 100);

                                try {
                                    // 替换文件输入框中的文件
                                    var dataTransfer = new DataTransfer();
                                    dataTransfer.items.add(result.file);
                                    self.files = dataTransfer.files;
                                } catch (err) {
                                    console.error('替换文件失败:', err);
                                }
                            }

                            // 安全执行原始的onchange
                            setTimeout(function() {
                                if (originalOnChange) {
                                    try {
                                        var func = new Function('event', 'this', originalOnChange);
                                        func.call(self, {type: 'change'}, self);
                                    } catch (err) {
                                        console.error('执行原始onchange失败:', err);
                                    }
                                }
                            }, 200);
                        });
                    });
                });
            }

            // 初始设置
            setupImageCompression();

            // 监听DOM变化，为新添加的文件输入框设置处理器
            var observer = new MutationObserver(function(mutations) {
                var hasNewFileInputs = false;

                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                        for (var i = 0; i < mutation.addedNodes.length; i++) {
                            var node = mutation.addedNodes[i];
                            if (node.nodeType !== 1) continue; // 只处理元素节点

                            if (node.tagName === 'INPUT' && node.type === 'file') {
                                hasNewFileInputs = true;
                                break;
                            }

                            if (node.querySelector && node.querySelector('input[type="file"]')) {
                                hasNewFileInputs = true;
                                break;
                            }
                        }
                    }
                });

                if (hasNewFileInputs) {
                    setTimeout(setupImageCompression, 50);
                }
            });

            // 观察整个文档的变化
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // 处理表单提交
            document.querySelectorAll('form').forEach(function(form) {
                form.addEventListener('submit', function(e) {
                    if (Object.keys(window.pendingUploads).length > 0) {
                        e.preventDefault();
                        util.message('图片正在处理中，请稍候再提交', '', 'info');
                        return false;
                    }
                });
            });
        });
    }, 100);
});
</script>

<include file="block/footer" /> 