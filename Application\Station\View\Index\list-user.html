<php>if ($list) { foreach($list as $v) {</php>
<li>
    <a href="javascript:void(0)" class="a">
        <div class="img"><img src="{:$v['headimgurl']."2"}"> </div>
        <div class="txt">
            <div class="title">
                <span class="sp1">{:$v['nickname'] ?: ''}</span>
                <php>if (isset($userJobList[$v['id']])){</php>
                    <span class="sig-ding">简历</span>
                    <php>if ($v['is_customer']) {</php>
                    <span class="sig-bang">云客服</span>
                    <php>}</php>
                <php>}</php>
                <php>if ($unionidList[$v['unionid']] && $unionidList[$v['unionid']] > 0) {</php>
                <span class="sig-vip4">服务站</span>
                <php>}</php>
            </div>
            
            <div class="p1">
                <php>if ($v['last_project_post_id'] > 0) {</php>
                <div class="sp1">2分钟前查看</div>
                <div class="sp2">{:$projectPostList[$v['last_project_post_id']]}</div>
                <php>}else{</php>
                    <div class="sp1"> </div>
                <php>}</php>
            </div>
            
            <div class="p2">
                <span class="sp21">查看 {:$v['show_num'] ? : 0}</span>
                
                <span class="sp2">最后浏览 
                    <php>if ($v['last_active_time'] > 0){</php>{:date("Y-m-d H:i:s", $v['last_active_time'])}<php>}else{</php>-<php>}</php>
                </span>
                
            </div>
           
        </div>
    </a>
    <div class="xxa" style="margin-right: 28px;"><a href="#" title="简历"><i class="iconfont icon-flow-determine"></i></a></div>
    <div class="xxa"><a href="tel:{:$v['mobile']}" title="拨打电话"><i class="iconfont icon-dianhua"></i></a></div>
</li>
<php>}}</php>