<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化招就办编辑页面样式 */
                .serviceambassador-edit-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .serviceambassador-edit-container {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .serviceambassador-edit-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .serviceambassador-edit-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .serviceambassador-edit-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .serviceambassador-edit-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .serviceambassador-edit-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .serviceambassador-edit-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .serviceambassador-edit-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .serviceambassador-edit-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .serviceambassador-edit-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .serviceambassador-edit-back-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .serviceambassador-edit-back-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .serviceambassador-edit-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .serviceambassador-edit-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .serviceambassador-edit-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .serviceambassador-edit-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .serviceambassador-edit-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .serviceambassador-edit-nav-link.active {
                    color: #667eea;
                    background: white;
                    border-bottom-color: #667eea;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1);
                }

                .serviceambassador-edit-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .serviceambassador-edit-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化表单卡片 */
                .serviceambassador-edit-form-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .serviceambassador-edit-form-section {
                    margin-bottom: 2rem;
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }

                .serviceambassador-edit-section-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .serviceambassador-edit-section-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .serviceambassador-edit-section-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .serviceambassador-edit-section-body {
                    padding: 2rem;
                }

                /* 现代化表单组 */
                .serviceambassador-edit-form-group {
                    margin-bottom: 2rem;
                }

                .serviceambassador-edit-form-group:last-child {
                    margin-bottom: 0;
                }

                .serviceambassador-edit-form-label {
                    display: block;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 0.75rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .serviceambassador-edit-form-label .required {
                    color: #ef4444;
                    font-size: 1.25rem;
                }

                .serviceambassador-edit-form-input {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    line-height: 1.5;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                }

                .serviceambassador-edit-form-input:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .serviceambassador-edit-form-select {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    line-height: 1.5;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                    cursor: pointer;
                }

                .serviceambassador-edit-form-select:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .serviceambassador-edit-form-textarea {
                    width: 100%;
                    min-height: 120px;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    line-height: 1.5;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                    resize: vertical;
                }

                .serviceambassador-edit-form-textarea:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .serviceambassador-edit-help-text {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-top: 0.5rem;
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;
                }

                /* 文件上传区域 */
                .serviceambassador-edit-upload-section {
                    background: #f8fafc;
                    border: 2px dashed #d1d5db;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    text-align: center;
                    transition: all 0.3s ease;
                }

                .serviceambassador-edit-upload-section:hover {
                    border-color: #667eea;
                    background: #f0f9ff;
                }

                /* 提交按钮区域 */
                .serviceambassador-edit-submit-section {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    padding: 2rem;
                    text-align: center;
                    margin-top: 2rem;
                }

                .serviceambassador-edit-submit-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 1rem 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                    min-width: 200px;
                }

                .serviceambassador-edit-submit-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                }

                .serviceambassador-edit-submit-btn:active {
                    transform: translateY(0);
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .serviceambassador-edit-container {
                        padding: 1.5rem;
                    }

                    .serviceambassador-edit-header-content {
                        flex-direction: column;
                        text-align: center;
                    }
                }

                @media (max-width: 768px) {
                    .serviceambassador-edit-container {
                        padding: 1rem;
                    }

                    .serviceambassador-edit-nav-tabs {
                        flex-direction: column;
                    }

                    .serviceambassador-edit-nav-item {
                        flex: none;
                    }

                    .serviceambassador-edit-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .serviceambassador-edit-nav-link.active {
                        border-bottom: none;
                        border-right-color: #667eea;
                    }

                    .serviceambassador-edit-title-main {
                        font-size: 1.75rem;
                    }

                    .serviceambassador-edit-title-sub {
                        font-size: 1.25rem;
                    }

                    .serviceambassador-edit-section-body {
                        padding: 1.5rem;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .serviceambassador-edit-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .serviceambassador-edit-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .serviceambassador-edit-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .serviceambassador-edit-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="serviceambassador-edit-wrapper">
                <div class="serviceambassador-edit-container">
                    <!-- 现代化页面标题 -->
                    <div class="serviceambassador-edit-header serviceambassador-edit-fade-in">
                        <div class="serviceambassador-edit-header-content">
                            <div class="serviceambassador-edit-title">
                                <div class="serviceambassador-edit-title-icon">
                                    <php>if($row) {</php>
                                    <i class="fa fa-edit"></i>
                                    <php>} else {</php>
                                    <i class="fa fa-plus"></i>
                                    <php>}</php>
                                </div>
                                <div class="serviceambassador-edit-title-text">
                                    <h1 class="serviceambassador-edit-title-main">招就办{:$row ? '编辑' : '添加'}</h1>
                                    <p class="serviceambassador-edit-title-sub">Service Ambassador {:$row ? 'Edit' : 'Add'}</p>
                                </div>
                            </div>
                            <div class="serviceambassador-edit-actions">
                                <a href="{:U('Serviceambassador/index')}" class="serviceambassador-edit-back-btn">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="serviceambassador-edit-nav-container serviceambassador-edit-fade-in-delay-1">
                        <ul class="serviceambassador-edit-nav-tabs">
                            <li class="serviceambassador-edit-nav-item">
                                <a href="{:U('Serviceambassador/index')}" class="serviceambassador-edit-nav-link">
                                    <i class="fa fa-list serviceambassador-edit-nav-icon"></i>
                                    <span>招就办管理</span>
                                </a>
                            </li>
                            <li class="serviceambassador-edit-nav-item">
                                <a href="#" class="serviceambassador-edit-nav-link active">
                                    <php>if($row) {</php>
                                    <i class="fa fa-edit serviceambassador-edit-nav-icon"></i>
                                    <span>编辑招就办</span>
                                    <php>} else {</php>
                                    <i class="fa fa-plus serviceambassador-edit-nav-icon"></i>
                                    <span>添加招就办</span>
                                    <php>}</php>
                                </a>
                            </li>
                            <li class="serviceambassador-edit-nav-item">
                                <a href="{:U('Sys/platform_rate_config')}" class="serviceambassador-edit-nav-link">
                                    <i class="fa fa-cog serviceambassador-edit-nav-icon"></i>
                                    <span>平台费率配置</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 现代化表单 -->
                    <form action="" method="post" class="form" enctype="multipart/form-data" id="form1">
                        <!-- 基本信息区域 -->
                        <div class="serviceambassador-edit-form-section serviceambassador-edit-fade-in-delay-2">
                            <div class="serviceambassador-edit-section-header">
                                <div class="serviceambassador-edit-section-icon">
                                    <i class="fa fa-user"></i>
                                </div>
                                <h3 class="serviceambassador-edit-section-title">基本信息</h3>
                            </div>
                            <div class="serviceambassador-edit-section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-building"></i>
                                                招就办名称
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" name="service_name" class="serviceambassador-edit-form-input" value="{$row.service_name}" placeholder="请输入招就办名称" required />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-user"></i>
                                                联系人姓名
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" name="contract_name" class="serviceambassador-edit-form-input" value="{$row.contract_name}" placeholder="请输入联系人姓名" required />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-phone"></i>
                                                手机号码
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" name="mobile" class="serviceambassador-edit-form-input" value="{$row.mobile}" placeholder="请输入手机号码" required />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-id-card"></i>
                                                身份证号
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" name="contract_card" class="serviceambassador-edit-form-input" value="{$row.contract_card}" placeholder="请输入身份证号" required />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-envelope"></i>
                                                电子邮件
                                            </label>
                                            <input type="email" name="email" class="serviceambassador-edit-form-input" value="{$row.email}" placeholder="请输入电子邮件" />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-map-marker"></i>
                                                通讯地址
                                            </label>
                                            <input type="text" name="mail_address" class="serviceambassador-edit-form-input" value="{$row.mail_address}" placeholder="请输入通讯地址" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 组织信息区域 -->
                        <div class="serviceambassador-edit-form-section serviceambassador-edit-fade-in-delay-3">
                            <div class="serviceambassador-edit-section-header">
                                <div class="serviceambassador-edit-section-icon">
                                    <i class="fa fa-sitemap"></i>
                                </div>
                                <h3 class="serviceambassador-edit-section-title">组织信息</h3>
                            </div>
                            <div class="serviceambassador-edit-section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-building-o"></i>
                                                所属服务站
                                            </label>
                                            <select name="zsb_ref_station" class="serviceambassador-edit-form-select">
                                                <option value="">请选择所属服务站</option>
                                                <php>foreach($stationList as $station) {</php>
                                                <option value="{$station.id}" {: $station['id'] == $row['zsb_ref_station'] ? 'selected' : ''}>{$station.service_name}</option>
                                                <php>}</php>
                                            </select>
                                            <div class="serviceambassador-edit-help-text">
                                                <i class="fa fa-info-circle"></i>
                                                如不选择则归属总部管理
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-star"></i>
                                                招就办级别
                                                <span class="required">*</span>
                                            </label>
                                            <select name="level" class="serviceambassador-edit-form-select" required>
                                                <option value="1" {: 1 == $row['level'] ? 'selected' : ''}>招就办主任</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 证件资料区域 -->
                        <div class="serviceambassador-edit-form-section serviceambassador-edit-fade-in-delay-3">
                            <div class="serviceambassador-edit-section-header">
                                <div class="serviceambassador-edit-section-icon">
                                    <i class="fa fa-file-image-o"></i>
                                </div>
                                <h3 class="serviceambassador-edit-section-title">证件资料</h3>
                            </div>
                            <div class="serviceambassador-edit-section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-camera"></i>
                                                证件照片
                                                <span class="required">*</span>
                                            </label>
                                            <div class="serviceambassador-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_image('zsb_zj_pic', $row['zsb_zj_pic'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-id-card"></i>
                                                身份证人像面
                                                <span class="required">*</span>
                                            </label>
                                            <div class="serviceambassador-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_image('zsb_sfz_rx', $row['zsb_sfz_rx'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-id-card-o"></i>
                                                身份证国徽面
                                                <span class="required">*</span>
                                            </label>
                                            <div class="serviceambassador-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_image('zsb_sfz_gh', $row['zsb_sfz_gh'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-certificate"></i>
                                                人力资源资质证书
                                                <span class="required">*</span>
                                            </label>
                                            <div class="serviceambassador-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_image('zsb_rlzyzs', $row['zsb_rlzyzs'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 合同文件区域 -->
                        <div class="serviceambassador-edit-form-section serviceambassador-edit-fade-in-delay-3">
                            <div class="serviceambassador-edit-section-header">
                                <div class="serviceambassador-edit-section-icon">
                                    <i class="fa fa-file-text-o"></i>
                                </div>
                                <h3 class="serviceambassador-edit-section-title">合同文件</h3>
                            </div>
                            <div class="serviceambassador-edit-section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-file-pdf-o"></i>
                                                服务合同
                                                <span class="required">*</span>
                                            </label>
                                            <div class="serviceambassador-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_file('zsb_fwzht', $row['zsb_fwzht'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="serviceambassador-edit-form-group">
                                            <label class="serviceambassador-edit-form-label">
                                                <i class="fa fa-shield"></i>
                                                5年无犯罪记录
                                                <span class="required">*</span>
                                            </label>
                                            <div class="serviceambassador-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_file('zsb_wfzzm', $row['zsb_wfzzm'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 备注说明区域 -->
                        <div class="serviceambassador-edit-form-section serviceambassador-edit-fade-in-delay-3">
                            <div class="serviceambassador-edit-section-header">
                                <div class="serviceambassador-edit-section-icon">
                                    <i class="fa fa-comment"></i>
                                </div>
                                <h3 class="serviceambassador-edit-section-title">备注说明</h3>
                            </div>
                            <div class="serviceambassador-edit-section-body">
                                <div class="serviceambassador-edit-form-group">
                                    <label class="serviceambassador-edit-form-label">
                                        <i class="fa fa-edit"></i>
                                        备注内容
                                    </label>
                                    <textarea name="content" rows="6" class="serviceambassador-edit-form-textarea richtext" placeholder="请输入备注说明...">{$row.content}</textarea>
                                    <div class="serviceambassador-edit-help-text">
                                        <i class="fa fa-info-circle"></i>
                                        可以添加关于招就办的其他说明信息
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮区域 -->
                        <div class="serviceambassador-edit-submit-section serviceambassador-edit-fade-in-delay-3">
                            <input type="hidden" name="id" value="{$row.id}"/>
                            <input type="hidden" name="type" value="2"/>
                            <button type="submit" name="submit" class="serviceambassador-edit-submit-btn">
                                <php>if($row) {</php>
                                <i class="fa fa-save"></i>
                                <span>保存修改</span>
                                <php>} else {</php>
                                <i class="fa fa-plus"></i>
                                <span>添加招就办</span>
                                <php>}</php>
                            </button>
                            <div style="margin-top: 1rem; color: #6b7280; font-size: 1.25rem;">
                                <i class="fa fa-info-circle"></i>
                                请确保所有必填信息已正确填写
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // 表单验证增强
        $('#form1').on('submit', function(e) {
            var isValid = true;
            var firstErrorField = null;

            // 检查必填字段
            $(this).find('input[required], select[required], textarea[required]').each(function() {
                var $field = $(this);
                var value = $field.val().trim();

                if (!value) {
                    isValid = false;
                    $field.css('border-color', '#ef4444');

                    if (!firstErrorField) {
                        firstErrorField = $field;
                    }
                } else {
                    $field.css('border-color', '#e5e7eb');
                }
            });

            if (!isValid) {
                e.preventDefault();

                // 滚动到第一个错误字段
                if (firstErrorField) {
                    $('html, body').animate({
                        scrollTop: firstErrorField.offset().top - 100
                    }, 500);
                    firstErrorField.focus();
                }

                // 显示错误提示
                layer.msg('请填写所有必填字段', {icon: 2, time: 3000});
                return false;
            }

            // 显示提交状态
            var $submitBtn = $('.serviceambassador-edit-submit-btn');
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

            // 如果验证通过，允许表单提交
            return true;
        });

        // 实时验证
        $('input[required], select[required], textarea[required]').on('blur', function() {
            var $field = $(this);
            var value = $field.val().trim();

            if (!value) {
                $field.css('border-color', '#ef4444');
            } else {
                $field.css('border-color', '#10b981');
            }
        });

        // 手机号格式验证
        $('input[name="mobile"]').on('input', function() {
            var value = $(this).val();
            var phoneRegex = /^1[3-9]\d{9}$/;

            if (value && !phoneRegex.test(value)) {
                $(this).css('border-color', '#f59e0b');
            } else if (value) {
                $(this).css('border-color', '#10b981');
            }
        });

        // 身份证号格式验证
        $('input[name="contract_card"]').on('input', function() {
            var value = $(this).val();
            var idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;

            if (value && !idCardRegex.test(value)) {
                $(this).css('border-color', '#f59e0b');
            } else if (value) {
                $(this).css('border-color', '#10b981');
            }
        });

        // 邮箱格式验证
        $('input[name="email"]').on('input', function() {
            var value = $(this).val();
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (value && !emailRegex.test(value)) {
                $(this).css('border-color', '#f59e0b');
            } else if (value) {
                $(this).css('border-color', '#10b981');
            }
        });

        // 文件上传区域增强
        $('.serviceambassador-edit-upload-section').on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('drag-over');
        });

        $('.serviceambassador-edit-upload-section').on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('drag-over');
        });

        // 添加拖拽样式
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .serviceambassador-edit-upload-section.drag-over {
                    border-color: #667eea !important;
                    background: #f0f9ff !important;
                    transform: scale(1.02);
                }
            `)
            .appendTo('head');

        // 表单字段动画效果
        $('.serviceambassador-edit-form-input, .serviceambassador-edit-form-select, .serviceambassador-edit-form-textarea').on('focus', function() {
            $(this).parent().addClass('field-focused');
        });

        $('.serviceambassador-edit-form-input, .serviceambassador-edit-form-select, .serviceambassador-edit-form-textarea').on('blur', function() {
            $(this).parent().removeClass('field-focused');
        });

        // 添加聚焦样式
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .field-focused {
                    transform: translateY(-2px);
                    transition: all 0.3s ease;
                }
            `)
            .appendTo('head');

        // 进度指示器
        var totalSections = $('.serviceambassador-edit-form-section').length;
        var completedSections = 0;

        function updateProgress() {
            var progress = (completedSections / totalSections) * 100;
            // 这里可以添加进度条显示逻辑
        }

        // 检查每个区域的完成状态
        $('.serviceambassador-edit-form-section').each(function() {
            var $section = $(this);
            var requiredFields = $section.find('input[required], select[required], textarea[required]');
            var filledFields = 0;

            requiredFields.each(function() {
                if ($(this).val().trim()) {
                    filledFields++;
                }
            });

            if (filledFields === requiredFields.length && requiredFields.length > 0) {
                completedSections++;
                $section.addClass('section-completed');
            }
        });

        updateProgress();

        // 添加完成状态样式
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .section-completed .serviceambassador-edit-section-header {
                    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%) !important;
                }
                .section-completed .serviceambassador-edit-section-icon {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
                }
            `)
            .appendTo('head');

        // 滚动到顶部功能
        var $scrollToTop = $('<button class="scroll-to-top" style="position: fixed; bottom: 2rem; right: 2rem; width: 3rem; height: 3rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 50%; font-size: 1.25rem; cursor: pointer; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; display: none; transition: all 0.3s ease;"><i class="fa fa-arrow-up"></i></button>');
        $('body').append($scrollToTop);

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $scrollToTop.fadeIn();
            } else {
                $scrollToTop.fadeOut();
            }
        });

        $scrollToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 600);
        });
    });
</script>
<include file="block/footer" />
<script>
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
        })
    });

    require(['primeImageCompressor'], function(primeImageCompressor) {
        primeImageCompressor.init();
    });
</script>
