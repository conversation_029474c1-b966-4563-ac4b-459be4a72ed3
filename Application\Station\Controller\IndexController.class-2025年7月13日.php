<?php

namespace Station\Controller;

use Common\Controller\WController;
use Common\Controller\WstationController;
use Util\AliSms;
use Util\HwSms;

class IndexController extends WstationController
{

    public function _initialize()
    {
        if ( ACTION_NAME != 'jobcontent') { //注册绑定

            parent::_initialize();
            $this->login();
            $openid = session('openid2');
            $userRow = D("User")->where(['openid' => $openid, 'service_id' => 1])->find();
            if (!$userRow) {
                session(null);
                return $this->redirect(U('/index/index'));
                die;
            }
            $this->userRow = $userRow;

            //D("User")->where(['id' => $userRow['id']])->save(['last_active_time' => time()]);  //记录最后活动时间

            //if ($userRow['last_login_time'] == 0) {
            //    D("User")->where(['id' => $userRow['id']])->save(['last_login_time' => time()]);
            //}

            if ($this->userRow['is_service_station'] == 0 && (ACTION_NAME != 'logins' && ACTION_NAME != 'logincode')) { //注册绑定
                return $this->redirect(U('index/logins'));
                die;
            }

            if ($userRow['self_service_station_id'] > 0) {
                D("service_station")->where(['id' => $userRow['self_service_station_id']])->save(['lastactive_time' => time()]);

                // 执行查询
                $jobmsgcount = D("user_job")->where(['service_station_id' => $userRow['self_service_station_id'],'need_reply' => 1])->count();

                // 查询价格变化通知状态（只对服务站用户显示）
                $priceChangeNotify = 0;
                $serviceStationInfo = D("service_station")->where(['id' => $userRow['self_service_station_id']])->find();
                if ($serviceStationInfo && $serviceStationInfo['zsb_type'] == 1) {
                    // 只有服务站用户才显示价格变化通知
                    $priceChangeNotify = $serviceStationInfo['price_change_notify'] ? 1 : 0;
                }
                $this->assign('priceChangeNotify', $priceChangeNotify);
            }

            //公告管理
            $adList = D("PageAd")->where(['status' => 1])->order('id desc')->select();
            $this->assign('adList', $adList);
            $this->assign('jobmsgcount', $jobmsgcount?:0);
        }
    }


    /**
     * 岗位内容
     * @return void
     */
    public function postcontent()
    {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $obj = D("Project");
        $row = $obj->where(['id' => $id])->find();
        if (!$row) $this->error('当前岗位无法找到！！！！');
        $projectList = D("Project")->where(['status' => 1])->field('id,name,content')->select();
        if (!C('LOCAL_DEBUG')) {
            $this->assign('wxconf', getWxConfig(null, $this->conf));
            $this->assign('appid', $this->conf['WX_APPID']);
        }
        $this->assign('projectList', $projectList);
        $this->assign('row', $row);
        $this->display();
    }

    /**
     * 首页
     */
    public function index()
    {
        if ($this->userRow['is_service_station'] == 0) { //注册绑定
            return $this->redirect(U('index/logins'));
            die;
        } elseif ($this->userRow['is_service_station'] == 2) { //审核通过

            // 检查当前用户是否为招就办
            $currentStation = D("ServiceStation")->where([
                'id' => $this->userRow['self_service_station_id'],
                'status' => 1
            ])->find();

            if ($currentStation && $currentStation['zsb_type'] == 2) {
                // 招就办用户，显示招就办专用界面
                return $this->zsbIndex();
            }
            $type = I('get.type', 0);
            $kwd = I('get.kwd', '');
            $qualification = I('get.qualification', 0);
            $where = ['status' => 1];
            if ($qualification > 0) {
                if ($qualification == 4) {
                    $where['qualification'] = ['egt', 4];

                } else {
                    $where['qualification'] = $qualification;
                }
            }
            if ($type > 0) {
                $categoryWhere = ['category' => $type];
                if ($type == 7) {
                    $categoryWhere = ['category' => ['in', [3, 5, 7]]];
                }
                $projectArrId = D("Project")->where($categoryWhere)->getField('id', true);
                if ($projectArrId) {
                    $where['project_id'] = ['in', $projectArrId];
                } else {
                    $where['project_id'] = -1;
                }
            }
            if (!empty($kwd)) {
                $where['job_name'] = ['like', '%' . $kwd . '%'];
            }
            $obj = D("ProjectPost");
            $this->assign('categoryList', D("Project")->category);

            $count = $obj->where($where)->count();
            $page = $this->page($count, 100);
            $list = $obj->limit($page->firstRow . ',' . $page->listRows)
                ->where($where)
                ->order('is_top desc,top_time desc,id desc')
                ->select();
            $this->assign('qualificationList', $obj->qualification);
            $this->assign('sexList', $obj->sex);
            $this->assign('categoryList', D("Project")->category);

            if ($list) {
                $projectArrId = array_unique(array_column($list, 'project_id'));
                $projectList = D("Project")->where(['id' => ['in', $projectArrId]])->getField('id,name,category', true);
                $this->assign('projectList', $projectList);

                $idArr = array_column($list, 'id');
                $projectJoinIdentityList = D("ProjectJoinIdentity")->where(['project_id' => ['in', $projectArrId], 'project_post_id' => ['in', $idArr], 'project_identity_id' => 3])->select();
                $projectJoinIdentity = [];
                foreach ($projectJoinIdentityList as $projectJoinIdentityRow) {
                    $projectJoinIdentity[$projectJoinIdentityRow['project_post_id']] = $projectJoinIdentityRow['cost'];
                }
                $this->assign('projectJoinIdentity', $projectJoinIdentity);
            }
            $this->assign('list', $list);
            $n = I('get.n', 0);
            if ($page->Current_page > 1 || $n == 1) {
                $this->display('list-index');
                exit;
            }
            $this->assign("page", $page);
            $obj = D("ServiceStation");
            $serviceStationRow = D("ServiceStation")
                ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
                ->find();
            $this->assign('serviceStationRow', $serviceStationRow);
            $startTime = strtotime(date("Y-m-d 00:00:00", time()));
            $endTime = strtotime(date("Y-m-d 23:59:59", time()));
            //今日粉丝数量
            $dayServiceNum = D("User")->where([
                'service_id' => 2,
                'service_station_id' => $this->userRow['self_service_station_id'],
                'subscribe' => ['between', [$startTime, $endTime]]
            ])->count();
            $this->assign('dayServiceNum', $dayServiceNum ?: 0);
            //今日简历
            $jobCount = D("UserJob")
                ->where(['service_station_id' => $this->userRow['self_service_station_id']])
                ->where(['create_time' => ['between', [$startTime, $endTime]]])
                ->count();
            $this->assign('jobCount', $jobCount ?: 0);
            //所有简历
            $alljobCount = D("UserJob")
                ->where(['service_station_id' => $this->userRow['self_service_station_id']])
                ->count();
            $this->assign('alljobCount', $alljobCount ?: 0);



            //下级服务站数量
            $refStationCount = D("ServiceStation")->where([
                'pid' => $this->userRow['self_service_station_id'],
                'status' => 1,
            ])->count();
            $this->assign('refStationCount', $refStationCount ?: 0);

            // 检查是否有下级服务站
            $hasSubServiceStations = $refStationCount > 0;
            $this->assign('hasSubServiceStations', $hasSubServiceStations);

            $this->assign('levelList', $obj->level);
            $this->assign('userRow', $this->userRow);
            $this->display();
        } elseif ($this->userRow['is_service_station'] == 1) { //待审核
            return $this->redirect(U('index/audits'));
            die;
        } else if ($this->userRow['is_service_station'] == 3) { //审核失败
            return $this->redirect(U('index/erroraudits'));
            die;
        }
    }

     /**
     * 详情首页
     */
    public function allindex()
    {
        if ($this->userRow['is_service_station'] == 0) { //注册绑定
            return $this->redirect(U('index/logins'));
            die;
        } elseif ($this->userRow['is_service_station'] == 2) { //审核通过

            // 检查当前用户是否为招就办
            $currentStation = D("ServiceStation")->where([
                'id' => $this->userRow['self_service_station_id'],
                'status' => 1
            ])->find();

            $isZsb = ($currentStation && $currentStation['zsb_type'] == 2);
            $this->assign('isZsb', $isZsb);

            $type = I('get.type', 0);
            $kwd = I('get.kwd', '');
            $qualification = I('get.qualification', 0);

            // 根据用户角色设置不同的查询条件
            if ($isZsb) {
                // 招就办用户：只显示已配置价格的岗位
                $priceModel = D("ZsbPostPrice");
                $priceList = $priceModel->where([
                    'zsb_id' => $this->userRow['self_service_station_id'],
                    'status' => 1
                ])->select();

                $postIds = array_column($priceList, 'post_id');

                // 使用别名查询以支持JOIN
                $where = ['pp.status' => 1];
                if (!empty($postIds)) {
                    $where['pp.id'] = ['in', $postIds];
                } else {
                    // 如果没有配置价格的岗位，显示空列表
                    $where['pp.id'] = -1;
                }
            } else {
                // 服务站用户：显示所有岗位（原有逻辑）
                $where = ['status' => 1];
            }

            if ($qualification > 0) {
                $qualificationField = $isZsb ? 'pp.qualification' : 'qualification';
                if ($qualification == 4) {
                    $where[$qualificationField] = ['egt', 4];
                } else {
                    $where[$qualificationField] = $qualification;
                }
            }
            if ($type > 0) {
                $categoryWhere = ['category' => $type];
                if ($type == 7) {
                    $categoryWhere = ['category' => ['in', [3, 5, 7]]];
                }
                $projectArrId = D("Project")->where($categoryWhere)->getField('id', true);
                if ($projectArrId) {
                    $projectIdField = $isZsb ? 'pp.project_id' : 'project_id';
                    $where[$projectIdField] = ['in', $projectArrId];
                } else {
                    $projectIdField = $isZsb ? 'pp.project_id' : 'project_id';
                    $where[$projectIdField] = -1;
                }
            }
            if (!empty($kwd)) {
                $jobNameField = $isZsb ? 'pp.job_name' : 'job_name';
                $where[$jobNameField] = ['like', '%' . $kwd . '%'];
            }
            $obj = D("ProjectPost");
            $this->assign('categoryList', D("Project")->category);

            // 根据用户角色执行不同的查询
            if ($isZsb) {
                // 招就办用户：使用JOIN查询获取项目信息
                // 为count查询转换条件（去掉表别名）
                $countWhere = [];
                foreach ($where as $key => $value) {
                    $newKey = str_replace('pp.', '', $key);
                    $countWhere[$newKey] = $value;
                }
                $count = $obj->where($countWhere)->count();
                $page = $this->page($count, 100);
                $list = $obj->alias('pp')
                    ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
                    ->field('pp.*, p.name as project_name, p.category')
                    ->limit($page->firstRow . ',' . $page->listRows)
                    ->where($where)
                    ->order('pp.is_top desc,pp.top_time desc,pp.id desc')
                    ->select();
            } else {
                // 服务站用户：原有查询逻辑
                $count = $obj->where($where)->count();
                $page = $this->page($count, 100);
                $list = $obj->limit($page->firstRow . ',' . $page->listRows)
                    ->where($where)
                    ->order('is_top desc,top_time desc,id desc')
                    ->select();
            }
            $this->assign('qualificationList', $obj->qualification);
            $this->assign('sexList', $obj->sex);
            $this->assign('categoryList', D("Project")->category);

            if ($list) {
                if ($isZsb) {
                    // 招就办用户：项目信息已通过JOIN获取，不需要额外查询
                    $projectList = [];
                    foreach ($list as $item) {
                        if (!isset($projectList[$item['project_id']])) {
                            $projectList[$item['project_id']] = [
                                'name' => $item['project_name'],
                                'category' => $item['category']
                            ];
                        }
                    }
                    $this->assign('projectList', $projectList);

                    // 为招就办用户添加价格信息（用于内部逻辑，不在视图显示）
                    if (isset($priceList) && !empty($priceList)) {
                        $priceMap = [];
                        foreach ($priceList as $price) {
                            $priceMap[$price['post_id']] = $price;
                        }

                        $priceModel = D("ZsbPostPrice");
                        foreach ($list as &$item) {
                            if (isset($priceMap[$item['id']])) {
                                // 转换价格单位为元（内部使用，不显示）
                                $priceData = $priceModel->processPriceDataForDisplay($priceMap[$item['id']]);
                                $item['zsb_cost_price'] = $priceData['cost_price'];
                                $item['zsb_sale_price'] = $priceData['sale_price'];
                                $item['zsb_commission'] = $priceData['commission'];
                            }
                        }
                        // 取消引用，防止后续操作意外修改$list中的最后一个元素
                        unset($item);
                    }
                } else {
                    // 服务站用户：原有逻辑
                    $projectArrId = array_unique(array_column($list, 'project_id'));
                    $projectList = D("Project")->where(['id' => ['in', $projectArrId]])->getField('id,name,category', true);
                    $this->assign('projectList', $projectList);

                    $idArr = array_column($list, 'id');
                    $projectJoinIdentityList = D("ProjectJoinIdentity")->where(['project_id' => ['in', $projectArrId], 'project_post_id' => ['in', $idArr], 'project_identity_id' => 3])->select();
                    $projectJoinIdentity = [];
                    foreach ($projectJoinIdentityList as $projectJoinIdentityRow) {
                        $projectJoinIdentity[$projectJoinIdentityRow['project_post_id']] = $projectJoinIdentityRow['cost'];
                    }
                    $this->assign('projectJoinIdentity', $projectJoinIdentity);
                }
            }
            $this->assign('list', $list);
            $n = I('get.n', 0);
            if ($page->Current_page > 1 || $n == 1) {
                $this->display('all-list-index');
                exit;
            }
            $this->assign("page", $page);
            $obj = D("ServiceStation");
            $serviceStationRow = D("ServiceStation")
                ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
                ->find();
            $this->assign('serviceStationRow', $serviceStationRow);
            $startTime = strtotime(date("Y-m-d 00:00:00", time()));
            $endTime = strtotime(date("Y-m-d 23:59:59", time()));
            //今日粉丝数量
            $dayServiceNum = D("User")->where([
                'service_id' => 2,
                'service_station_id' => $this->userRow['self_service_station_id'],
                'subscribe' => ['between', [$startTime, $endTime]]
            ])->count();
            $this->assign('dayServiceNum', $dayServiceNum ?: 0);
            //今日简历
            $jobCount = D("UserJob")
                ->where(['service_station_id' => $this->userRow['self_service_station_id']])
                ->where(['create_time' => ['between', [$startTime, $endTime]]])
                ->count();
            $this->assign('jobCount', $jobCount ?: 0);
            //所有简历
            $alljobCount = D("UserJob")
                ->where(['service_station_id' => $this->userRow['self_service_station_id']])
                ->count();
            $this->assign('alljobCount', $alljobCount ?: 0);



            //下级服务站数量
            $refStationCount = D("ServiceStation")->where([
                'pid' => $this->userRow['self_service_station_id'],
                'status' => 1,
            ])->count();
            $this->assign('refStationCount', $refStationCount ?: 0);

            // 检查是否有下级服务站
            $hasSubServiceStations = $refStationCount > 0;
            $this->assign('hasSubServiceStations', $hasSubServiceStations);

            $this->assign('levelList', $obj->level);
            $this->assign('userRow', $this->userRow);

            // 检查价格变化通知状态（仅服务站用户）
            $hasNotify = false;
            if (!$isZsb && $serviceStationRow && $serviceStationRow['zsb_type'] == 1) {
                // 检查是否有价格变化通知
                if ($serviceStationRow['price_change_notify']) {
                    // 检查是否被临时清除（会话中记录）
                    $sessionKey = 'price_notify_temp_cleared_' . $serviceStationRow['id'];
                    $tempCleared = session($sessionKey);

                    if (!$tempCleared) {
                        $hasNotify = true;
                    }
                }
            }

            $this->assign('hasNotify', $hasNotify);

            // 设置导航栏红点显示状态（与index方法保持一致）
            $priceChangeNotify = 0;
            if (!$isZsb && $serviceStationRow && $serviceStationRow['zsb_type'] == 1) {
                // 检查是否有价格变化通知且未被临时清除
                if ($serviceStationRow['price_change_notify']) {
                    $sessionKey = 'price_notify_temp_cleared_' . $serviceStationRow['id'];
                    $tempCleared = session($sessionKey);

                    if (!$tempCleared) {
                        $priceChangeNotify = 1;
                    }
                }
            }
            $this->assign('priceChangeNotify', $priceChangeNotify);

            $this->display();
        } elseif ($this->userRow['is_service_station'] == 1) { //待审核
            return $this->redirect(U('index/audits'));
            die;
        } else if ($this->userRow['is_service_station'] == 3) { //审核失败
            return $this->redirect(U('index/erroraudits'));
            die;
        }
    }

    /**
     * 上传简历
     */
    public function uploadJobFile() {
        header('Content-Type: application/json');

        // 清理输出缓冲区，确保没有额外输出
        if (ob_get_level()) {
            ob_clean();
        }

        // 临时关闭错误显示和页面追踪，避免额外输出混入JSON响应
        error_reporting(0);
        ini_set('display_errors', 0);

        // 关闭ThinkPHP页面追踪
        C('SHOW_PAGE_TRACE', false);

        // 获取当前用户所属服务站信息
        $currentStation = D("ServiceStation")->where([
            'id' => $this->userRow['self_service_station_id'],
            'status' => 1
        ])->find();

        // 确定简历类型：招就办用户上传的简历自动标记为招就办简历
        $resumeType = 1; // 默认为自有简历
        if ($currentStation && $currentStation['zsb_type'] == 2) {
            $resumeType = 2; // 招就办简历
        }

        $upload = new \Think\Upload(); // 实例化TP3.2上传类
        $upload->maxSize   = 10 * 1024 * 1024; // 10MB
        $upload->exts      = array('docx'); // 仅允许docx格式
        $upload->rootPath  = SITE_PATH.'/data/Job/station/';
        $upload->autoSub = false;
        $upload->savePath  = ''; // 按日期子目录存储
        $info = $upload->upload();
        $jobContent = I('post.jobcontent');

        if(!$info) {
            echo json_encode(['status'=>0, 'msg'=>$upload->getError()]);
            return;
        }

        // 获取上传文件信息
        $filePath = '/data/Job/station/'.$info['file']['savename'];
        $fullFilePath = SITE_PATH . $filePath;
        $fileHash = md5_file($fullFilePath);

        // 检查文件是否重复
        $existFile = D("UserJobDoc")->where(['file_hash' => $fileHash])->find();
        if ($existFile) {
            // 删除重复文件
            if (file_exists($fullFilePath)) {
                unlink($fullFilePath);
            }
            echo json_encode([
                'status' => 2,  // 使用状态码2表示重复文件
                'msg' => '当前简历已经存在，请勿重复提交'
            ]);
            return;
        }

        // 进行水印检测（Upload类已限制只能上传docx文件）
        $templateDetected = false;
        $detectionMethod = 'none';
        $detectionScore = 0;

        try {
            // 检查ZipArchive扩展是否可用
            if (!extension_loaded('zip')) {
                throw new Exception('系统不支持Word文档处理，请联系管理员');
            }

            // 引入水印检测类
            import('Common.Lib.WordProcessor');
            import('Common.Lib.ResumeTemplateDetector');
            import('Common.Lib.WatermarkLogger');

            // 检查文件是否存在且可读
            if (!file_exists($fullFilePath) || !is_readable($fullFilePath)) {
                throw new Exception('上传的文件损坏或不可读');
            }

            $detector = new \ResumeTemplateDetector($fullFilePath);
            $detectionResult = $detector->detect();

            $templateDetected = $detectionResult['is_official_template'];
            $detectionMethod = $detectionResult['detection_method'];
            $detectionScore = $detectionResult['validation_score'];

            // 记录检测结果到日志
            $detector->logDetectionResult(
                $detectionResult,
                $this->userRow['id'],
                $_FILES['file']['name']
            );

            // 记录调试信息
            \WatermarkLogger::logDebug('简历模板检测完成', [
                'user_id' => $this->userRow['id'],
                'file_name' => $_FILES['file']['name'],
                'file_size' => filesize($fullFilePath),
                'detection_result' => $detectionResult
            ]);

        } catch (Exception $e) {
            // 检测失败时清理文件
            error_log('简历模板检测失败: ' . $e->getMessage());

            if (file_exists($fullFilePath)) {
                unlink($fullFilePath);
            }

            // 根据异常类型提供具体的错误信息
            $errorMsg = $this->getDetailedErrorMessage($e->getMessage());

            echo json_encode([
                'status' => 0,
                'msg' => $errorMsg['message'],
                'error_type' => $errorMsg['type'],
                'help_url' => $errorMsg['help_url'],
                'template_url' => 'http://c.zhongcaiguoke.com/data/%E5%BA%94%E8%81%98%E4%BA%BA%E5%91%98%E6%8A%A5%E5%90%8D%E8%A1%A8%EF%BC%88%E9%80%9A%E7%94%A8%EF%BC%89.docx'
            ]);
            return;
        }

        // 检查是否检测到标准模板
        if (!$templateDetected) {
            // 删除未通过检测的文件
            if (file_exists($fullFilePath)) {
                unlink($fullFilePath);
            }

            // 根据检测方法提供具体的失败信息
            $errorInfo = $this->getTemplateDetectionErrorInfo($detectionMethod, $detectionScore);

            echo json_encode([
                'status' => 0,
                'msg' => $errorInfo['message'],
                'error_type' => 'template_detection_failed',
                'detection_score' => $detectionScore,
                'suggestions' => $errorInfo['suggestions'],
                'template_url' => 'http://c.zhongcaiguoke.com/data/%E5%BA%94%E8%81%98%E4%BA%BA%E5%91%98%E6%8A%A5%E5%90%8D%E8%A1%A8%EF%BC%88%E9%80%9A%E7%94%A8%EF%BC%89.docx'
            ]);
            return;
        }

        // 【修复1补充】模板验证通过后，立即进行快速身份证预检查
        $quickIdCardCheck = $this->quickExtractIdCard($fullFilePath);
        if ($quickIdCardCheck['found'] && !empty($quickIdCardCheck['id_card'])) {
            $duplicateCheck = $this->checkIdCardDuplicate($quickIdCardCheck['id_card'], $this->userRow['self_service_station_id']);
            if ($duplicateCheck['is_duplicate']) {
                // 删除文件
                if (file_exists($fullFilePath)) {
                    unlink($fullFilePath);
                }

                // 根据是否同一服务站显示不同提示信息
                if ($duplicateCheck['station_name'] === '当前服务站（重复提交）') {
                    $message = '该身份证号码的简历已存在，请勿重复提交';
                    $errorType = 'duplicate_submission';
                } else {
                    $message = '该学员已被其他服务站【' . $duplicateCheck['station_name'] . '】锁定并服务';
                    $errorType = 'student_locked';
                }

                echo json_encode([
                    'status' => 0,
                    'msg' => $message,
                    'error_type' => $errorType
                ]);
                return;
            }
        }

        // 水印检测通过，进行简历内容验证
        $verifyResult = $this->verifyResumeContent($filePath);
        if (!$verifyResult['success']) {
            // 验证失败，删除文件
            if (file_exists($fullFilePath)) {
                unlink($fullFilePath);
            }
            echo json_encode([
                'status' => 0,
                'msg' => '简历验证失败：' . $verifyResult['error'],
                'error_type' => 'verification_failed'
            ]);
            return;
        }

        $verifyData = $verifyResult['data'];

        // 检查是否需要弹出错误（根据具体缺失字段返回不同错误类型）
        if ($verifyData['IsStop']) {
            // 删除文件
            if (file_exists($fullFilePath)) {
                unlink($fullFilePath);
            }

            $message = isset($verifyData['Message']) ? $verifyData['Message'] : '请正确填写联系电话和邮箱';
            $missingTypes = isset($verifyData['MissingTypes']) ? $verifyData['MissingTypes'] : [];

            // 根据具体缺失的字段确定错误类型
            $errorType = 'contact_info_missing'; // 默认类型

            if (count($missingTypes) == 1) {
                // 只缺失一种类型
                switch ($missingTypes[0]) {
                    case 'id_card':
                        $errorType = 'invalid_id_card';
                        break;
                    case 'email':
                        $errorType = 'email_missing';
                        break;
                    case 'phone':
                        $errorType = 'phone_missing';
                        break;
                }
            } elseif (in_array('id_card', $missingTypes)) {
                // 包含身份证错误，优先显示身份证错误
                $errorType = 'invalid_id_card';
            } else {
                // 联系方式错误（邮箱和/或电话）
                $errorType = 'contact_info_missing';
            }

            echo json_encode([
                'status' => 0,
                'msg' => $message,
                'error_type' => $errorType,
                'missing_types' => $missingTypes // 调试信息
            ]);
            return;
        }

        // 【修复1】提前进行身份证重复检查 - 在内容验证通过后立即检查，避免后续不必要的处理
        $idCard = '';
        if (isset($verifyData['IDCard']) && !is_array($verifyData['IDCard']) && !empty($verifyData['IDCard'])) {
            $idCard = $verifyData['IDCard'];

            // 验证身份证合法性（统一字段名）
            $idCardValid = isset($verifyData['IDCardverif']) ? $verifyData['IDCardverif'] : false;

            if (!$idCardValid) {
                // 删除文件
                if (file_exists($fullFilePath)) {
                    unlink($fullFilePath);
                }
                echo json_encode([
                    'status' => 0,
                    'msg' => '身份证号码无效，请检查简历中的身份证信息',
                    'error_type' => 'invalid_id_card'
                ]);
                return;
            }

            // 【修复1】立即检查身份证是否已被锁定，避免后续处理浪费
            $duplicateCheck = $this->checkIdCardDuplicate($idCard, $this->userRow['self_service_station_id']);
            if ($duplicateCheck['is_duplicate']) {
                // 删除文件
                if (file_exists($fullFilePath)) {
                    unlink($fullFilePath);
                }

                // 根据是否同一服务站显示不同提示信息
                if ($duplicateCheck['station_name'] === '当前服务站（重复提交）') {
                    $message = '该身份证号码的简历已存在，请勿重复提交';
                    $errorType = 'duplicate_submission';
                } else {
                    $message = '该学员已被其他服务站【' . $duplicateCheck['station_name'] . '】锁定并服务';
                    $errorType = 'student_locked';
                }

                echo json_encode([
                    'status' => 0,
                    'msg' => $message,
                    'error_type' => $errorType
                ]);
                return;
            }
        }

        // 【修复2】使用数据库事务确保数据一致性
        $db = M(); // 获取数据库连接
        $db->startTrans(); // 开始事务

        try {
            // 保存简历记录
            $userJobId = D("UserJob")->add([
                'user_id' => $this->userRow['id'],
                'remark' => $jobContent,
                'service_station_id' => $this->userRow['self_service_station_id'],
                'type' => 2,
                'resume_type' => $resumeType,
                'create_time' => time(),
            ]);

            if (!$userJobId) {
                throw new Exception('简历记录保存失败');
            }

            // 保存简历文档记录
            $docResult = D("UserJobDoc")->add([
                'user_id' => $this->userRow['id'],
                'user_job_id' => $userJobId,
                'file_hash' => $fileHash,
                'file_names' => $_FILES['file']['name'] ?: '',
                'service_station_id' => $this->userRow['self_service_station_id'],
                'content' => $filePath,
                'status' => 1,
                'create_time' => time(),
            ]);

            if (!$docResult) {
                throw new Exception('简历文档记录保存失败');
            }

            // 保存求职诉求消息（如果有）
            if (!empty($jobContent)) {
                $messageResult = D("ServiceStationPlatformMessage")->add([
                    'service_station_id' => $this->userRow['self_service_station_id'],
                    'type' => 1,
                    'user_job_id' => $userJobId,
                    'content' => '求职诉求：'.$jobContent,
                    'create_time' => time(),
                ]);

                if (!$messageResult) {
                    throw new Exception('求职诉求消息保存失败');
                }
            }

            // 提交事务
            $db->commit();

            // 事务成功后，尝试提取并保存简历信息（这个操作失败不影响主流程）
            $this->extractAndSaveResumeInfo($fullFilePath, $userJobId, $docResult);

        } catch (Exception $e) {
            // 回滚事务
            $db->rollback();

            // 清理文件
            if (file_exists($fullFilePath)) {
                unlink($fullFilePath);
            }

            // 记录错误日志
            \Think\Log::write('简历保存事务失败：' . $e->getMessage(), 'ERROR');

            echo json_encode([
                'status' => 0,
                'msg' => '简历保存失败，请重试'
            ]);
            return;
        }

        // 返回成功结果
        $responseData = [
            'status' => 1,
            'msg' => '简历上传成功',
            'path' => $filePath,
            'url' => '/index/joblist',
            'template_detected' => $templateDetected,
            'detection_method' => $detectionMethod,
            'detection_score' => $detectionScore
        ];

        echo json_encode($responseData);
    }





    /**
     * 提取并保存简历信息
     * @param string $filePath 文件完整路径
     * @param int $userJobId 用户简历ID
     * @param int $docId 文档记录ID
     */
    private function extractAndSaveResumeInfo($filePath, $userJobId, $docId) {
        try {
            // 检查文件是否存在
            if (!file_exists($filePath)) {
                \Think\Log::write('简历文件不存在：' . $filePath, 'ERROR');
                return;
            }

            // 引入WordProcessor类
            import('Common.Lib.WordProcessor');

            $wordProcessor = new \WordProcessor($filePath);

            if (!$wordProcessor->open()) {
                \Think\Log::write('无法打开Word文档：' . $filePath, 'WARN');
                return;
            }

            $content = $wordProcessor->getDocumentContent();
            $wordProcessor->close();

            if ($content === false) {
                \Think\Log::write('无法提取文档内容：' . $filePath, 'WARN');
                return;
            }

            // 提取基本信息
            $extractedInfo = $this->extractBasicInfoFromContent($content);

            if (!empty($extractedInfo)) {
                // 【修复2补充】信息提取更新也使用事务保护
                $db = M();
                $db->startTrans();

                try {
                    // 更新用户简历信息
                    $updateResult = $this->updateUserJobWithExtractedInfo($userJobId, $extractedInfo);

                    if ($updateResult === false) {
                        throw new Exception('简历信息更新失败');
                    }

                    $db->commit();
                    \Think\Log::write('身份证号提取成功，用户简历ID：' . $userJobId, 'INFO');

                } catch (Exception $e) {
                    $db->rollback();
                    \Think\Log::write('简历信息更新事务失败：' . $e->getMessage(), 'ERROR');
                }
            }

        } catch (Exception $e) {
            \Think\Log::write('简历信息提取失败：' . $e->getMessage(), 'ERROR');
        }
    }

    /**
     * 从文档内容中提取身份证号
     * @param string $content 文档内容
     * @return array 提取的信息数组
     */
    private function extractBasicInfoFromContent($content) {
        $extractedInfo = [];

        // 只提取身份证号（支持"身份证号"和"身份证号码"两种格式）
        if (preg_match('/身份证号码?[：:]?\s*([0-9X]{15,18})/u', $content, $matches)) {
            $extractedInfo['id_number'] = trim($matches[1]);
        }

        return $extractedInfo;
    }

    /**
     * 快速提取身份证号（用于早期重复检查）
     * @param string $filePath 文件路径
     * @return array 包含是否找到和身份证号的数组
     */
    private function quickExtractIdCard($filePath) {
        $result = [
            'found' => false,
            'id_card' => ''
        ];

        try {
            // 引入WordProcessor类
            import('Common.Lib.WordProcessor');

            $wordProcessor = new \WordProcessor($filePath);

            if (!$wordProcessor->open()) {
                return $result;
            }

            $content = $wordProcessor->getDocumentContent();
            $wordProcessor->close();

            if ($content === false) {
                return $result;
            }

            // 快速提取身份证号
            if (preg_match('/身份证号码?[：:]?\s*([0-9X]{15,18})/u', $content, $matches)) {
                $idCard = trim(strtoupper($matches[1]));
                // 进行基本的身份证校验
                if ($this->validateIdCard($idCard)) {
                    $result['found'] = true;
                    $result['id_card'] = $idCard;
                }
            }

        } catch (Exception $e) {
            // 快速检查失败不影响主流程，只记录日志
            \Think\Log::write('快速身份证提取失败：' . $e->getMessage(), 'WARN');
        }

        return $result;
    }

    /**
     * 使用提取的信息更新用户简历
     * @param int $userJobId 用户简历ID
     * @param array $extractedInfo 提取的信息
     * @return bool|int 更新结果
     */
    private function updateUserJobWithExtractedInfo($userJobId, $extractedInfo) {
        if (empty($extractedInfo)) {
            return true; // 没有信息需要更新，返回成功
        }

        // 只更新非空的字段
        $updateData = ['id' => $userJobId];

        foreach ($extractedInfo as $field => $value) {
            if (!empty($value)) {
                $updateData[$field] = $value;
            }
        }

        if (count($updateData) > 1) { // 除了id之外还有其他字段
            $updateData['update_time'] = time();
            $result = D("UserJob")->save($updateData);

            if ($result !== false) {
                \Think\Log::write('简历信息更新成功，更新字段：' . implode(',', array_keys($extractedInfo)), 'INFO');
                return $result;
            } else {
                \Think\Log::write('简历信息更新失败，用户简历ID：' . $userJobId, 'ERROR');
                return false;
            }
        }

        return true; // 没有字段需要更新
    }

    /**
     * 获取详细的错误信息
     * @param string $errorMessage 原始错误信息
     * @return array 包含详细错误信息的数组
     */
    private function getDetailedErrorMessage($errorMessage) {
        $errorInfo = [
            'type' => 'system_error',
            'message' => '简历上传失败，请重试',
            'help_url' => ''
        ];

        if (strpos($errorMessage, 'ZipArchive') !== false || strpos($errorMessage, 'zip') !== false) {
            $errorInfo = [
                'type' => 'file_format_error',
                'message' => '文件格式错误！请确保上传的是Word文档(.docx格式)，不支持.doc或其他格式',
                'help_url' => ''
            ];
        } elseif (strpos($errorMessage, '不支持') !== false) {
            $errorInfo = [
                'type' => 'system_support_error',
                'message' => '系统暂时无法处理Word文档，请联系管理员或稍后重试',
                'help_url' => ''
            ];
        } elseif (strpos($errorMessage, '损坏') !== false || strpos($errorMessage, '不可读') !== false) {
            $errorInfo = [
                'type' => 'file_corrupted_error',
                'message' => '文件已损坏或无法读取！请重新下载官方模板，重新填写后上传',
                'help_url' => ''
            ];
        }

        return $errorInfo;
    }

    /**
     * 验证简历内容（本地验证，使用phpword）
     * @param string $filePath 简历文件路径
     * @return array 验证结果
     */
    private function verifyResumeContent($filePath) {
        $result = [
            'success' => false,
            'data' => null,
            'error' => '验证失败'
        ];

        try {
            // 使用文件的完整路径
            $fullFilePath = SITE_PATH . $filePath;

            if (!file_exists($fullFilePath)) {
                $result['error'] = '文件不存在';
                return $result;
            }

            // 使用WordProcessor读取文档内容
            import('Common.Lib.WordProcessor');
            $wordProcessor = new \WordProcessor($fullFilePath);

            if (!$wordProcessor->open()) {
                $result['error'] = '无法打开Word文档';
                return $result;
            }

            // 获取文档文本内容
            $documentContent = $wordProcessor->getDocumentContent();
            $wordProcessor->close();

            if ($documentContent === false) {
                $result['error'] = '无法读取文档内容';
                return $result;
            }

            // 本地验证手机号、邮箱、身份证
            $validationResult = $this->validateResumeContentLocally($documentContent);

            $result['success'] = true;
            $result['data'] = $validationResult;
            $result['error'] = null;

        } catch (Exception $e) {
            $result['error'] = '验证过程发生异常: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * 本地验证简历内容（手机号、邮箱、身份证）
     * @param string $content 文档内容
     * @return array 验证结果
     */
    private function validateResumeContentLocally($content) {
        // 初始化结果
        $result = [
            'IsEmail' => false,
            'IsPhoneNumber' => false,
            'IsStop' => false,
            'IDCard' => '',
            'IDCardverif' => false,
            'Email' => '',
            'PhoneNumber' => '',
            'Message' => ''
        ];

        // 手机号正则表达式（中国大陆手机号，严格匹配11位数字）
        // 修改：使用更严格的匹配模式，避免匹配到文本中间的数字序列
        $phonePattern = '/(?<!\d)1[3-9]\d{9}(?!\d)/';
        if (preg_match($phonePattern, $content, $phoneMatches)) {
            $phoneNumber = trim($phoneMatches[0]);
            // 二次验证：确保是标准的11位手机号格式
            if (preg_match('/^1[3-9]\d{9}$/', $phoneNumber)) {
                $result['IsPhoneNumber'] = true;
                $result['PhoneNumber'] = $phoneNumber;
            }
        }

        // 邮箱验证：先用正则表达式提取，再用PHP内置函数验证
        // 修改：结合正则提取和内置函数验证，提高准确性
        $emailPattern = '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/';
        if (preg_match($emailPattern, $content, $emailMatches)) {
            $email = trim($emailMatches[0]);
            // 使用PHP内置函数进行二次验证，更可靠
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $result['IsEmail'] = true;
                $result['Email'] = $email;
            }
        }

        // 身份证号正则表达式（18位，严格匹配）
        // 修改：使用更严格的匹配模式，避免匹配到文本中间的数字序列
        $idCardPattern = '/(?<!\d)[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx](?!\d)/';
        if (preg_match($idCardPattern, $content, $idCardMatches)) {
            $idCard = trim(strtoupper($idCardMatches[0])); // 统一转换为大写
            // 进行完整的身份证校验
            if ($this->validateIdCard($idCard)) {
                $result['IDCard'] = $idCard;
                $result['IDCardverif'] = true;
            }
        }

        // 验证必填信息：邮箱、手机号、身份证都必须存在且有效
        $missingFields = [];
        $missingTypes = []; // 记录缺失字段的类型

        if (!$result['IsEmail']) {
            $missingFields[] = '有效邮箱地址';
            $missingTypes[] = 'email';
        }

        if (!$result['IsPhoneNumber']) {
            $missingFields[] = '有效联系电话';
            $missingTypes[] = 'phone';
        }

        // 检查身份证号是否存在且有效
        if (empty($result['IDCard']) || !$result['IDCardverif']) {
            $missingFields[] = '有效身份证号';
            $missingTypes[] = 'id_card';
        }

        // 如果有任何必填信息缺失或无效，则停止处理
        if (!empty($missingFields)) {
            $result['IsStop'] = true;
            $result['MissingTypes'] = $missingTypes; // 保存缺失类型信息
            if (count($missingFields) == 1) {
                $result['Message'] = '请正确填写' . $missingFields[0];
            } else {
                $result['Message'] = '请正确填写' . implode('、', $missingFields);
            }
        }

        return $result;
    }



    /**
     * 验证身份证号码的合法性
     * @param string $idCard 身份证号
     * @return bool 是否合法
     */
    private function validateIdCard($idCard) {
        if (strlen($idCard) !== 18) {
            return false;
        }

        // 验证前17位是否为数字
        if (!ctype_digit(substr($idCard, 0, 17))) {
            return false;
        }

        // 验证最后一位校验码
        $weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        $checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

        $sum = 0;
        for ($i = 0; $i < 17; $i++) {
            $sum += intval($idCard[$i]) * $weights[$i];
        }

        $checkCode = $checkCodes[$sum % 11];
        $lastChar = strtoupper($idCard[17]);

        return $checkCode === $lastChar;
    }

    /**
     * 检查身份证是否已被锁定
     * @param string $idCard 身份证号
     * @param int $currentStationId 当前服务站ID
     * @return array 检查结果
     */
    private function checkIdCardDuplicate($idCard, $currentStationId) {
        $result = [
            'is_duplicate' => false,
            'station_name' => '',
            'station_id' => 0
        ];

        if (empty($idCard) || strlen($idCard) !== 18) {
            return $result;
        }

        try {
            // 查询是否存在相同身份证号的简历记录
            $existingJob = D("UserJob")->where([
                'id_number' => $idCard
            ])->find();

            if ($existingJob) {
                // 获取服务站信息
                $stationInfo = D("ServiceStation")->where([
                    'id' => $existingJob['service_station_id']
                ])->find();

                if ($stationInfo) {
                    $result['is_duplicate'] = true;

                    // 如果是同一服务站，提示重复提交
                    if ($existingJob['service_station_id'] == $currentStationId) {
                        $result['station_name'] = '当前服务站（重复提交）';
                    } else {
                        // 如果是不同服务站，显示具体服务站名称
                        $result['station_name'] = $stationInfo['service_name'] ?: $stationInfo['enterprise_name'];
                    }

                    $result['station_id'] = $stationInfo['id'];
                }
            }

        } catch (Exception $e) {
            // 记录错误日志，但不影响主流程
            error_log('身份证重复检查异常: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * 获取模板检测失败的详细信息
     * @param string $detectionMethod 检测方法
     * @param int $detectionScore 检测分数
     * @return array 包含详细错误信息的数组
     */
    private function getTemplateDetectionErrorInfo($detectionMethod, $detectionScore) {
        $errorInfo = [
            'type' => 'template_not_detected',
            'message' => '未检测到官方模板标识',
            'suggestions' => []
        ];

        if ($detectionMethod === 'none' || $detectionScore === 0) {
            $errorInfo = [
                'type' => 'no_watermark',
                'message' => '❌ 检测失败：您上传的文档不是官方标准模板',
                'suggestions' => [
                    '1. 请点击"📥 下载标准模板"获取最新的官方模板',
                    '2. 使用官方模板重新填写简历信息',
                    '3. 确保保存为.docx格式后上传',
                    '4. 不要复制粘贴到其他文档中'
                ]
            ];
        } elseif ($detectionMethod === 'structure' && $detectionScore < 80) {
            $errorInfo = [
                'type' => 'structure_mismatch',
                'message' => '❌ 检测失败：文档结构不符合官方模板要求',
                'suggestions' => [
                    '1. 请重新下载最新版官方模板',
                    '2. 不要删除或修改模板中的任何字段',
                    '3. 只填写内容，不要改变格式和结构',
                    '4. 确保所有必填字段都已填写'
                ]
            ];
        } elseif ($detectionScore > 0 && $detectionScore < 70) {
            $errorInfo = [
                'type' => 'partial_match',
                'message' => '❌ 检测失败：文档可能被修改过或不是最新版本',
                'suggestions' => [
                    '1. 请下载最新版官方模板重新填写',
                    '2. 不要使用复制粘贴的方式转移内容',
                    '3. 确保使用Word软件直接编辑模板',
                    '4. 保存时选择.docx格式'
                ]
            ];
        }

        return $errorInfo;
    }

    public function getsorturl()
    {
        $insertId = D("ServiceStationUserJoin")->add([
            'service_station_id' => $this->userRow['self_service_station_id'],
            'user_id' => $this->userRow['id'],
            'create_time' => time(),
        ]);
        if ($insertId) {
            $code = D("Project")->enhash($insertId);
            $url = 'http://we.zhongcaiguoke.com/index/index/i/' . $code;
            return $this->success($url);
        }
        return $this->error('生成短链接错误!!!');
    }


    /**
     * 公告
     */
    public function ad()
    {
        $id = I('get.id', 0);
        if (!$id) return $this->redirect(U('index/index'));
        $row = D("PageAd")->where(['id' => $id, 'status' => 1])->order('id asc')->find();
        if (!$row) return $this->redirect(U('index/index'));
        $this->assign('row', $row);

        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }
        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }

        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->order('id desc')
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 用户管理
     */
    public function user()
    {
        $sortShowTime = I('get.show_time', 0);
        $sortShowNum = I('get.show_num', 0);
        $kwd = I('get.kwd', '');
        $this->assign('show_time', $sortShowTime);
        $this->assign('show_num', $sortShowNum);
        $this->assign('kwd', $kwd);

        $service_station_id = $this->userRow['self_service_station_id'];
        $serviceServiceStationRow = D("ServiceStation")->where(['id' => $service_station_id])->find();

        $where = ['service' => 2, 'service_station_id' => $serviceServiceStationRow['id']];
        if ($kwd != '') {
            $where['nickname'] = ['like', '%' . $kwd . '%'];
        }
        $obj = D("User");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $order = "id desc";
        if ($sortShowTime > 0) {
            if ($sortShowTime == 1) {
                $order = 'last_project_post_time asc,id desc';
            } else {
                $order = 'last_project_post_time desc,id desc';
            }
        }
        if ($sortShowNum > 0) {
            if ($sortShowNum == 1) {
                $order = 'show_num asc,id desc';
            } else {
                $order = 'show_num desc,id desc';
            }
        }
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order($order)
            ->select();
        if ($list) {
            $userJobList = [];
            $projectPostList = [];
            $userArrId = array_column($list, 'id');
            if ($userArrId) {

                $projectPostArrId = array_unique(array_column($list, 'last_project_post_id'));
                if ($projectPostArrId) {
                    $projectPostList = D("ProjectPost")->where(['id' => ['in', $projectPostArrId]])->getField('id,job_name', true);
                }
            }

            $unionidArr = array_column($list, 'unionid');
            //判断是否是服务站
            $unionidList = [];
            if ($unionidArr) {
                $unionidList = D("User")->where(['service_id' => 2, 'unionid' => ['in', $unionidArr]])->getField('unionid,self_service_station_id', true);
            }

            $this->assign('projectPostList', $projectPostList);
            $this->assign('userJobList', $userJobList);
            $this->assign('unionidList', $unionidList);

        }
        $this->assign('list', $list);
        if ($page->Current_page > 1) {
            $this->display('list-user');
            exit;
        }
        $usersCounts = $obj->where(['service' => 2, 'service_station_id' => $serviceServiceStationRow['id']])->count();
        $this->assign('usersCounts', $usersCounts);


        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $jobCount = D("UserJob")->where(['service_station_id' => $serviceServiceStationRow['id']])->count();
        $this->assign('jobCount', $jobCount);
        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 项目管理
     */
    public function projectlist()
    {
        $where = ['status' => 1];
        $obj = D("ProjectPost");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        if ($list) {
            $projectArrId = array_unique(array_column($list, 'project_id'));
            $projectList = D("Project")->where(['id' => ['in', $projectArrId]])->getField('id,name', true);
            $this->assign('projectList', $projectList);
        }
        $this->assign('list', $list);
        if ($page->Current_page > 1) {
            $this->display('list-projectlist');
            exit;
        }
        $this->assign("page", $page);
        $this->display();
    }

    public function jobcontent() {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $userJobDocRow = D("UserJobDoc")->where(['id' => $id])->find();
        if (!$userJobDocRow) $this->error('当前简历不存在');
        if (empty($userJobDocRow['html_content'])) $this->error('当前简历未分析成功，请稍后再来!!');
        $this->assign('userJobDocRow', $userJobDocRow);
        $this->display();
    }

    /**
     * remark
     */
    public function remark() {
        $id = I('get.id');
        $service_station_id = $this->userRow['self_service_station_id'];
        $jobRow = D("UserJob")->where(['id' => $id, 'service_station_id' => $service_station_id])->find();
        if (!$jobRow) $this->error("当前简历不存在！！");
        $where = [
            'service_station_id' => $service_station_id,
            'user_job_id' => $id,
        ];
        $jobList = D("ServiceStationPlatformMessage")->where($where)->select();
        $this->assign('jobList', $jobList);
        $this->assign('jobRow', $jobRow);
        $this->display();
    }

    public function commitremark() {
        $id = I('get.id');
        $service_station_id = $this->userRow['self_service_station_id'];
        $jobRow = D("UserJob")->where(['id' => $id, 'service_station_id' => $service_station_id])->find();
        if (!$jobRow) {
            echo json_encode(['status'=> 1, 'msg'=> "当前简历不存在"]);die;
        }
        if (IS_POST) {
            $jobContent = I('post.jobcontent');
            if (empty($jobContent)) {
                echo json_encode(['status'=> 1, 'msg'=> "内容不能成功!!!"]);die;
            }
            $insert = D("ServiceStationPlatformMessage")->add([
                'service_station_id' => $this->userRow['self_service_station_id'],
                'type' => 1,
                'user_job_id' => $jobRow['id'],
                'content' => $jobContent,
                'create_time' => time(),
            ]);
            if ($insert) {
                D("UserJob")->where(['id' => $jobRow['id']])->save(['is_reply' => 1,'need_reply' => 0,'msg_reply_time' => time()]); //更新简历最后回复时间
                echo json_encode(['status'=> 0, 'msg'=> "添加内容成功"]);die;
            } else {
                echo json_encode(['status'=> 1, 'msg'=> "添加内容失败"]);die;
            }
        }


    }

    /**
     * 用户简历管理
     */
    public function joblist()
    {
        $sortShowTime = I('get.show_time', 0);
        $sortShowNum = I('get.show_num', 0);
        $jobState = I('get.job_state', ''); // 获取job_state参数
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $resumeType = I('get.resume_type', 0, 'intval'); // 获取简历类型参数

        $this->assign('type', $type);
        $this->assign('show_time', $sortShowTime);
        $this->assign('show_num', $sortShowNum);
        $this->assign('kwd', $kwd);
        $this->assign('resume_type', $resumeType);

        $service_station_id = $this->userRow['self_service_station_id'];
        $serviceServiceStationRow = D("ServiceStation")->where(['id' => $service_station_id])->find();

        // 获取当前用户所属服务站信息，判断用户类型
        $currentStation = D("ServiceStation")->where([
            'id' => $this->userRow['self_service_station_id'],
            'status' => 1
        ])->find();

        // 构建查询条件 - 根据用户类型决定查询范围
        $serviceStationIds = [$serviceServiceStationRow['id']]; // 默认包含当前服务站

        if ($currentStation && $currentStation['zsb_type'] == 1) {
            // 服务站用户：需要查询自己和下属招就办的简历
            $zsbList = D("ServiceStation")->where([
                'zsb_ref_station' => $service_station_id,
                'zsb_type' => 2,
                'status' => 1
            ])->getField('id', true);

            if ($zsbList) {
                $serviceStationIds = array_merge($serviceStationIds, $zsbList);
            }
        }

        $where = ['service_station_id' => ['in', $serviceStationIds]];
        if ($kwd != '') {
            $where['name'] = ['like', '%' . $kwd . '%'];
        }
        if ($type != '' && $type > 0) {
            $type = $type-1;
            $where['job_state'] = $type;
        }

        // 简历类型筛选逻辑
        if ($currentStation) {
            if ($currentStation['zsb_type'] == 2) {
                // 招就办用户只能看到自己的招就办简历
                $where['resume_type'] = 2;
            } elseif ($currentStation['zsb_type'] == 1 && $resumeType > 0) {
                // 服务站用户可以筛选简历类型
                $where['resume_type'] = $resumeType;
            }
        }

        $order = "id desc";
        if ($sortShowTime > 0) {
            if ($sortShowTime == 1) {
                $order = 'last_project_post_time asc,id desc';
            } else {
                $order = 'last_project_post_time desc,id desc';
            }
        }
        if ($sortShowNum > 0) {
            if ($sortShowNum == 1) {
                $order = 'show_num asc,id desc';
            } else {
                $order = 'show_num desc,id desc';
            }
        }

        $service_station_id = $this->userRow['self_service_station_id'];
        $obj = D("UserJob");

        // 获取各状态的数量 - 使用相同的服务站ID范围
        $newWhere = [
            'service_station_id' => ['in', $serviceStationIds]
        ];
        $stateCounts = [
            'allcount' => $obj->where($newWhere)->count(), // 全部
            'communicating' => $obj->where($newWhere)->where(['job_state' => 0])->count(), // 沟通中
            'training'      => $obj->where($newWhere)->where(['job_state' => 1])->count(), // 培训中
            'onboarding'    => $obj->where($newWhere)->where(['job_state' => 2])->count(), // 入职中
            'terminated'    => $obj->where($newWhere)->where(['job_state' => 3])->count()  // 终止服务
        ];

        $count = $obj->where($where)->count();
        $page = $this->page($count, 50); // 修改为每页50条记录
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order($order)
            ->select();
        $this->assign('serviceStatusList', $obj->service_status);
        $this->assign('stateCounts', $stateCounts); // 将状态数量分配到前端

        if ($list) {
            $userJobList = [];
            $projectPostList = [];
            $userArrId = array_column($list, 'user_id');
            $userLists = [];
            if ($userArrId) {
                $userListsArr = D("User")->where(['id' => ['in', $userArrId]])->select();
                foreach ($userListsArr as $userRow) {
                    $userLists[$userRow['id']] = $userRow;
                }
            }
            $this->assign('userLists', $userLists);

            $idArrId = array_unique(array_column($list, 'id'));
            if ($idArrId) {
                $userJobDocList = D("UserJobDoc")->where(['user_job_id' => ['in', $idArrId]])->getField('user_job_id,id,is_html,status,file_names,content,create_time,check_doc_succ', true);

                // 获取已报名培训的学员信息
                // 注意：这里我们需要获取的是简历ID，而不是user_job_id
                // 修改查询逻辑，获取所有已报名培训的学员ID，不限于特定岗位
                $trainingOrderList = D("TrainingOrder")->where([
                    'order_status' => ['not in', ['closed']] // 排除已关闭的订单
                ])->getField('user_job_id', true);

                // **新增：获取不能报名培训的简历ID（基于用户服务状态）**
                $cannotEnrollJobIds = [];

                // 1. 获取当前页面所有简历的手机号
                $currentJobIds = array_column($list, 'id');
                $phoneMap = D("UserJob")->where(['id' => ['in', $currentJobIds]])->getField('id,phone', true);

                // 2. 获取这些手机号对应的用户服务状态（只获取自己服务站下的用户）
                $phones = array_values($phoneMap);
                \Think\Log::write('当前页面所有简历的手机号: ' . json_encode($phones), 'INFO');
                if (!empty($phones)) {
                    $restrictedUsers = D("User")->where([
                        'mobile' => ['in', $phones],
                        'service_station_id' => $this->userRow['self_service_station_id'], // 只获取自己服务站下的用户
                        'service_status' => ['in', [1, 2]] // 培训中(1) 和 已入职(2)
                    ])->getField('mobile', true);
                    \Think\Log::write("self_service_station_id: ".json_decode($userRow['self_service_station_id']));
                    \Think\Log::write('对应手机号的状态: ' . json_encode(D("User")->where(['mobile' => ['in', $phones], 'service_station_id' => $this->userRow['self_service_station_id']])->getField('mobile,service_status', true)), 'INFO');
                    \Think\Log::write('不能报名培训的学员ID(基于用户状态): ' . json_encode($restrictedUsers), 'INFO');
                    // 3. 将不能报名的用户手机号对应的简历ID加入限制列表
                    foreach ($phoneMap as $jobId => $phone) {
                        if (in_array($phone, $restrictedUsers)) {
                            $cannotEnrollJobIds[] = $jobId;
                        }
                    }
                }

                // 调试信息
                if ($trainingOrderList) {
                    \Think\Log::write('已报名培训的学员ID: ' . json_encode($trainingOrderList), 'INFO');
                }
                if ($cannotEnrollJobIds) {
                    \Think\Log::write('不能报名培训的学员ID(基于用户状态): ' . json_encode($cannotEnrollJobIds), 'INFO');
                }

                $this->assign('trainingOrderList', $trainingOrderList);
                $this->assign('cannotEnrollJobIds', $cannotEnrollJobIds);
            }
            $this->assign('userJobDocList', $userJobDocList);


            $projectPostArrId = array_unique(array_column($userLists, 'last_project_post_id'));
            if ($projectPostArrId) {
                $projectPostList = D("ProjectPost")->where(['id' => ['in', $projectPostArrId]])->getField('id,job_name', true);
            }

            $unionidArr = array_column($list, 'unionid');
            //判断是否是服务站
            $unionidList = [];
            if ($unionidArr) {
                $unionidList = D("User")->where(['service_id' => 1, 'unionid' => ['in', $unionidArr]])->getField('unionid,self_service_station_id', true);
            }

            $this->assign('projectPostList', $projectPostList);
            $this->assign('userJobList', $userJobList);
            $this->assign('unionidList', $unionidList);




        }
        $this->assign('list', $list);

        if ($page->Current_page > 1) {
            $this->display('list-joblist');
            exit;
        }


        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);
        $this->assign("page", $page->show());

         // 控制器方法内
         $stateMap = array(
            0 => ['text' => '沟通中', 'style' => 'status-tag status-communicating'],
            1 => ['text' => '培训中', 'style' => 'status-tag status-training'],
            2 => ['text' => '已入职', 'style' => 'status-tag status-employed'],
            3 => ['text' => '服务终止', 'style' => 'status-tag status-terminated'],
        );
        $this->assign('stateMap', $stateMap);

        // 实例化 LinggongBinding 模型并获取 platformTypes
        $linggongBindingModel = D("LinggongBinding");
        $this->assign('linggongPlatformTypes', $linggongBindingModel->platformTypes);

        $this->display();
    }


    	/**
	 *  删除简历数据
	 */
    public function deljobdoc()
    {
        // 参数安全过滤
        $id = I('get.id', 0, 'intval');
        $serviceStationId = isset($this->userRow['self_service_station_id']) ? intval($this->userRow['self_service_station_id']) : 0;

        // 基础参数验证
        if ($id <= 0 || $serviceStationId <= 0) {
            $this->error('参数不合法');
        }

        // 初始化模型
        $userJobModel = D('UserJob');
        $userJobDocModel = D('UserJobDoc'); // 确认模型名称与实际一致

        // 查询要删除的记录（带权限校验）
        $jobWhere = [
            'id' => $id,
            'service_station_id' => $serviceStationId
        ];
        $jobRow = $userJobModel->where($jobWhere)->find();

        if (!$jobRow) {
            $this->error('记录不存在或无权操作');
        }

        // 启用事务
        $userJobModel->startTrans();
        try {
            // 带条件删除主记录
            $deleteJob = $userJobModel->where($jobWhere)->delete();
            if ($deleteJob === false) {
                throw new \Exception('主记录删除失败');
            }

            // 删除关联记录（使用参数绑定）
            $deleteDocs = $userJobDocModel->where(['user_job_id' => $id])->delete();
            if ($deleteDocs === false) {
                throw new \Exception('关联记录删除失败');
            }

            // 提交事务
            $userJobModel->commit();
            $this->success('删除成功');
        } catch (\Exception $e) {
            // 回滚事务
            $userJobModel->rollback();
            // 记录日志（实际开发中建议添加日志记录）
            // Log::write('删除失败：'.$e->getMessage());
            $this->error('删除失败，请稍后重试');
        }
    }

    /**
     * 登录页面
     */
    public function logins()
    {
        if ($this->userRow['self_service_station_id']) {
            return $this->redirect(U('index/index'));
            die;
        }

        if (IS_POST) {
            $data = I('post.');
            $mobile = $data['mobile'];
            $code = $data['code'];
            if (empty($data['mobile'])) $this->error('参数错误');
            $serviceStationRow = D("ServiceStation")->where(['mobile' => $data['mobile']])->find();
            if (!$serviceStationRow) $this->error('当前服务站不存在，请重新注册！！');
            if (empty($mobile)) $this->error('手机号未填写');
            if (empty($code)) $this->error('验证码未填写');
            if ($code == session('code_login_' . $mobile)) {
                $totalNum = D("User")->where(['service_id' => 1, 'self_service_station_id' => $serviceStationRow['id']])->count();
                $is_service_station = 1;
                switch ($serviceStationRow['status']) {
                    case 0 :
                        $is_service_station = 1;
                        break;
                    case 1:
                        $is_service_station = 2;
                        break;
                    case 2:
                        $is_service_station = 3;
                        break;
                }
                $updateData = [
                    'id' => $this->userRow['id'],
                    'self_service_station_id' => $serviceStationRow['id'],
                    'is_service_station' => $is_service_station,
                ];
                if ($totalNum == 0) {
                    $updateData['is_first_wechat'] = 1;
                }
                D("User")->save($updateData);
                D("User")->where(['id' => $this->userRow['id']])->save(['last_login_time' => time()]);

                $this->redirect('index/index');
            } else {
                $this->error('验证码未填写');
            }
        }
        $this->display();
    }

    /**
     * 设备管理
     */
    public function loginaccount()
    {
        if ($this->userRow['is_first_wechat'] != 1) $this->error('您当前无权限操作此页面!!!');
        $userLists = D("User")->where(['service_id' => 1, 'self_service_station_id' => $this->userRow['self_service_station_id']])->select();
        $this->assign("userLists", $userLists);

        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }
        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }

        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);

        $this->display();
    }

    /**
     * 用户操作日志
     */
    public function userlog()
    {
        if ($this->userRow['is_first_wechat'] != 1) $this->error('您当前无权限操作此页面!!!');
        $where = ['service_station_id' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStationDivideLog");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        if ($list) {
            $serviceStationList = [];
            $serviceStationArrId = array_unique(array_column($list, 'divide_service_station_id'));
            if ($serviceStationArrId) {
                $serviceStationArrId[] = $this->userRow['self_service_station_id'];
                $serviceStationList = D("ServiceStation")->where(['id' => ['in', $serviceStationArrId]])->getField('id,service_name', true);
            }
            $this->assign('serviceStationList', $serviceStationList);

            $userList = [];
            $userArrId = array_unique(array_column($list, 'user_id'));
            if ($userArrId) {
                $userList = D("User")->where(['id' => ['in', $userArrId]])->getField('id,nickname,headimgurl', true);
            }
            $this->assign('userList', $userList);
        }
        $this->assign('list', $list);
        if ($page->Current_page > 1) {
            $this->display('list-userlog');
            exit;
        }

        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);

        $this->display();
    }

    /**
     * 退出登录
     */
    public function outloginaccount()
    {
        if ($this->userRow['is_first_wechat'] != 1) $this->error('您当前无权限操作此页面!!!');
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误!!');
        $selfUserRow = D("User")->where(['id' => $id])->find();
        if ($selfUserRow && $selfUserRow['self_service_station_id'] == $this->userRow['self_service_station_id']) {
            $boolean = D("User")->save(['id' => $selfUserRow['id'], 'self_service_station_id' => 0, 'is_service_station' => 0, 'is_first_wechat' => 0]);
            if ($boolean) {
                return $this->success('退出登录成功!!', U('index/loginaccount'));
            }
        }
    }

    // 发送手机验证码
    public function logincode()
    {
        if ($this->userRow['self_service_station_id']) {
            $ret = ['code' => 3, "msg" => '当前微信已绑定其它的服务站！'];
            echo json_encode($ret);
            die;
        }

        $mobile = I("get.mobile");
        $ret['code'] = 0;
        $stataion = true;
        $serviceStationRow = D("ServiceStation")->where(['mobile' => $mobile, 'status' => 1])->find();
        if (!$serviceStationRow) {
            $ret = [
                'code' => 1,
                'msg' => '当前手机号不存在！',
            ];
        } else {
            $lasts = session('time_login_' . $mobile);
            if ($lasts and $lasts > time() - 60) {
                $ret = [
                    'code' => 2,
                    'msg' => '验证码已发送，请稍后请求！',
                ];
            } else {
                $code = mt_rand(10000, 99999);
                session('code_login_' . $mobile, $code);
                session('time_login_' . $mobile, time());
                $alisms = new AliSms();
                $result = $alisms->sendSms($code, $mobile);
                if (!$result) {
                    $ret = ['code' => 3, "msg" => '验证码发送失败！'];
                }
            }
        }
        echo json_encode($ret);
        die;
    }

    /**
     * 余额管理
     */
    public function money()
    {
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id']];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }
        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }

        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        // 获取当月额度使用情况 - 仅对非招就办用户
        if ($serviceStationRow['zsb_type'] != 2) {
            $monthlyQuotaModel = D("MonthlyWithdrawalQuota");
            $stationId = $this->userRow['self_service_station_id'];
            $quota = $monthlyQuotaModel->getStationMonthlyQuota($stationId);

            // 格式化为友好显示
            $quotaData = [
                'total_quota' => number_format($quota['total_quota'], 2),
                'used_amount' => number_format($quota['used_amount'], 2),
                'remaining_quota' => number_format($quota['remaining_quota'], 2),
                'is_exceed' => $quota['used_amount'] >= $quota['total_quota'],
                'percent' => min(100, round($quota['used_amount'] / $quota['total_quota'] * 100)), // 使用百分比
                'year_month' => substr($quota['year_month'], 0, 4) . '年' . substr($quota['year_month'], 4, 2) . '月'
            ];

            $this->assign('quota', $quotaData);
        }

        // 检查京东京灵平台灵工绑定状态
        $bindingModel = D("LinggongBinding");
        $bindingInfo = $bindingModel->checkUserBinding($this->userRow['id'], 1); // 1 = 京东京灵平台
        $isJdBound = !empty($bindingInfo) && $bindingInfo['status'] == 1;

        // 如果未绑定，生成绑定二维码
        $bindingQrCode = '';
        if (!$isJdBound) {
            $bindingQrCode = $bindingModel->generateQrCode($this->userRow['id'], 1); // 1 = 京东京灵平台
        }

        $this->assign('isJdBound', $isJdBound);
        $this->assign('bindingQrCode', $bindingQrCode);
        $this->assign('bindingInfo', $bindingInfo);

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 金额详情管理
     */
    public function money_details()
    {

        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->display();
    }

    /**
     * 余额提现页面管理
     */
    public function money_card()
    {
        // 获取用户关联的服务站
        $stationId = $this->userRow['self_service_station_id'];
        if (!$stationId) {
            $this->error("未找到您的服务站信息");
        }

        $serviceStationModel = D("ServiceStation");
        $serviceStationRow = $serviceStationModel->where(['id' => $stationId, 'status' => 1])->find();
        if (!$serviceStationRow) {
            $this->error("服务站信息无效或未审核");
        }

        // 检查是否为招就办用户，招就办用户不允许提现
        if ($serviceStationRow['zsb_type'] == 2) {
            $this->error("招就办用户不支持提现功能，如有疑问请联系客服");
        }
        // 计算可提现余额 (直接使用 price 字段)
        $availableBalance = $serviceStationRow['price'] ?: 0;
        $this->assign('availableBalance', $availableBalance);

        // 移除平台类型GET参数获取
        // $platform = I('get.platform', '');
        // $this->assign('platform', $platform);

        // 获取银行卡列表
        $bankCardModel = D("BankCard");
        $bankCardList = $bankCardModel->where(['service_station_id' => $stationId, 'status' => 1])->order('is_default desc, id desc')->select();
        $this->assign('bankCardList', $bankCardList);
        $this->assign('hasBankCards', !empty($bankCardList)); // 添加标志

        // 获取已审核通过的灵工平台绑定信息
        $linggongBindingModel = D("LinggongBinding");
        $linggongBinding = $linggongBindingModel->where([
            'user_id' => $this->userRow['id'],
            'status' => 1 // 只获取已审核通过的
        ])->find();
        $this->assign('linggongBinding', $linggongBinding); // 传递绑定信息（可能为空）
        $this->assign('hasApprovedLinggong', !empty($linggongBinding)); // 添加标志

        // 获取提现手续费率并传递给前端
        $withdrawalRequestModel = D("WithdrawalRequest");
        $serviceFeeRate = $withdrawalRequestModel::getServiceFeeRate();
        $this->assign('serviceFeeRate', $serviceFeeRate);
        $this->assign('serviceFeeRatePercent', $serviceFeeRate * 100); // 百分比形式

        // 获取免手续费额度并传递给前端
        $freeLimitValue = isset($serviceStationRow['free_withdrawal_limit']) ? floatval($serviceStationRow['free_withdrawal_limit']) : 0;
        $this->assign('freeLimitValue', $freeLimitValue);

        // 获取每月额度信息
        $monthlyQuotaModel = D("MonthlyWithdrawalQuota");
        $quotaInfo = $monthlyQuotaModel->getStationMonthlyQuota($stationId);
        $this->assign('quotaInfo', $quotaInfo);

        // 判断额度是否已用完
        $isQuotaExhausted = $quotaInfo['remaining_quota'] <= 0;
        $this->assign('isQuotaExhausted', $isQuotaExhausted);

        if (IS_POST) {
            $amount = I('post.amount', 0, 'floatval');
            $bankCardId = I('post.bank_card_id', 0, 'intval');
            $platform = I('post.platform', '', 'trim');
            $linggongBindingId = I('post.linggong_binding_id', 0, 'intval');

            // Input validation
            if ($amount <= 0) {
                $this->error("请输入有效的提现金额");
            }

            // 根据平台类型验证提现账户
            if ($platform == 'linggong') {
                if (!$linggongBindingId) {
                    $this->error("请选择提现到的灵工平台账户");
                }
                // 验证灵工平台账户有效性
                $linggongBindingModel = D("LinggongBinding");
                $selectedLinggong = $linggongBindingModel->where([
                    'id' => $linggongBindingId,
                    'user_id' => $this->userRow['id'],
                    'status' => 1
                ])->find();

                if (!$selectedLinggong) {
                    $this->error("选择的灵工平台账户无效或不属于您");
                }
            } else {
                // 默认提现到银行卡
                if (!$bankCardId) {
                    $this->error("请选择提现到的银行卡");
                }
                // 验证银行卡有效性
                $bankCardModel = D("BankCard");
                $selectedCard = $bankCardModel->where([
                    'id' => $bankCardId,
                    'service_station_id' => $stationId,
                    'status' => 1
                ])->find();

                if (!$selectedCard) {
                    $this->error("选择的银行卡无效或不属于您");
                }
            }

            if ($amount > $availableBalance) { // Check against available balance
                $this->error("提现金额不能超过可提现余额");
            }
            // 检查单笔限额 (示例，可以配置)
            $maxWithdrawal = 50000;
            if ($amount > $maxWithdrawal) {
                $this->error("单笔提现金额不能超过 " . $maxWithdrawal);
            }

            // 获取免服务费额度
            $freeLimit = isset($serviceStationRow['free_withdrawal_limit']) ? floatval($serviceStationRow['free_withdrawal_limit']) : 0;
            $isFeeFree = false; // 标记是否免手续费

            // 判断是否在免费额度内
            if ($freeLimit > 0 && $amount <= $freeLimit) {
                $serviceFee = 0;
                $actualAmount = $amount;
                $isFeeFree = true;
            } else {
                // 原有逻辑计算手续费
                $withdrawalRequestModel = D("WithdrawalRequest"); // 模型实例化移到前面或确保已实例化
                $feeRate = $withdrawalRequestModel::getServiceFeeRate();
                $serviceFee = round($amount * $feeRate, 2);
                $actualAmount = $amount - $serviceFee;
            }

            // 计算服务费和实际到账金额 (注释掉或删除原有计算逻辑)
            // $withdrawalRequestModel = D("WithdrawalRequest");
            // $feeRate = $withdrawalRequestModel::getServiceFeeRate();
            // $serviceFee = round($amount * $feeRate, 2);
            // $actualAmount = $amount - $serviceFee;

            $stationMoneyModel = D("StationMoney");
            $monthlyQuotaModel = D("MonthlyWithdrawalQuota");

            // 启动事务
            $withdrawalRequestModel->startTrans();
            try {
                // 计算提现对额度的影响
                list($platformTaxAmount, $userInvoiceAmount, $quotaInfo) = $monthlyQuotaModel->calculateQuotaImpact($stationId, $amount);

                // 根据平台类型设置税费处理方式
                if ($platform == 'linggong') {
                    // 灵工平台：强制设置为平台负责完税
                    $taxHandling = 1; // 1: 平台负责完税

                    // 检查额度是否足够
                    if ($quotaInfo['remaining_quota'] <= 0) {
                        throw new \Exception('当前完税额度已用完，无法使用灵工平台提现');
                    }

                    // 如果额度不足以覆盖全部金额，也不允许使用灵工平台
                    if ($amount > $quotaInfo['remaining_quota']) {
                        throw new \Exception('提现金额超出可用完税额度，无法使用灵工平台提现');
                    }

                    // 全部由平台负责完税
                    $platformTaxAmount = $amount;
                    $userInvoiceAmount = 0;
                } else {
                    // 银行卡：强制设置为用户开票
                    $taxHandling = 2; // 2: 用户开票

                    // 全部由用户负责开票
                    $platformTaxAmount = 0;
                    $userInvoiceAmount = $amount;
                }

                // 检查本月额度使用情况 - 仅在灵工平台提现时更新额度
                if ($platform == 'linggong') {
                    list($isExceedQuota, $updatedQuota, $exceededAmount) = $monthlyQuotaModel->updateQuotaUsage($stationId, $platformTaxAmount);
                }

                // 1. 创建提现申请记录
                $requestData = [
                    'service_station_id' => $stationId,
                    'user_id' => $this->userRow['id'],
                    'amount' => $amount,
                    'service_fee' => $serviceFee, // 添加服务费
                    'actual_amount' => $actualAmount, // 添加实际到账金额
                    'tax_handling' => $taxHandling, // 设置税费处理方式
                    'platform_tax_amount' => $platformTaxAmount, // 平台负责完税金额
                    'user_invoice_amount' => $userInvoiceAmount, // 用户需开票金额
                    'application_quota_total' => $quotaInfo['total_quota'], // 申请时的总额度
                    'application_quota_used' => $quotaInfo['used_amount'], // 申请时已使用额度
                    'application_quota_remaining' => $quotaInfo['remaining_quota'], // 申请时剩余额度
                    'request_time' => time(), // 显式设置申请时间
                ];

                // 添加平台类型和账户信息
                if ($platform == 'linggong') {
                    $requestData['platform_type'] = 'linggong';
                    $requestData['linggong_binding_id'] = $linggongBindingId;
                    $requestData['bank_card_id'] = 0; // 不使用银行卡
                } else {
                    $requestData['platform_type'] = 'bank';
                    $requestData['bank_card_id'] = $bankCardId;
                    $requestData['linggong_binding_id'] = 0; // 不使用灵工平台
                }

                $requestId = $withdrawalRequestModel->add($requestData);
                if (!$requestId) {
                    throw new \Exception('创建提现申请失败: ' . $withdrawalRequestModel->getError());
                }

                // 2. 冻结服务站金额: 增加 freeze_price, 减少 price (可用余额)
                $freezeResult = $serviceStationModel->where(['id' => $stationId])
                                                     ->save([
                                                         'freeze_price' => ['exp', 'freeze_price + ' . $amount],
                                                         'price' => ['exp', 'price - ' . $amount]
                                                     ]);
                if ($freezeResult === false) {
                     throw new \Exception('冻结金额失败');
                }

                // 如果本次提现免手续费，则扣减免费额度
                if ($isFeeFree) {
                    $deductLimitResult = $serviceStationModel->where(['id' => $stationId])
                                                             ->setDec('free_withdrawal_limit', $amount);
                    if ($deductLimitResult === false) {
                        \Think\Log::write('扣减服务站免服务费额度失败: 服务站ID=' . $stationId . ', 金额=' . $amount, 'ERR');
                        // 可以选择性地抛出异常使事务回滚
                        // throw new \Exception('扣减免服务费额度失败');
                    }
                     // 可选：重新检查额度，防止并发导致额度变为负数
                     $checkLimitStation = $serviceStationModel->field('id,  free_withdrawal_limit')->find($stationId);
                     if ($checkLimitStation && $checkLimitStation['free_withdrawal_limit'] < 0) {
                         \Think\Log::write('服务站免服务费额度出现负数: 服务站ID=' . $stationId . ', 额度=' . $checkLimitStation['free_withdrawal_limit'], 'WARN');
                         // 考虑修正为0
                         // $serviceStationModel->where(['id' => $stationId])->save(['free_withdrawal_limit' => 0]);
                     }
                }

                // 重新检查余额，防止并发问题导致超额提现 (检查可用余额 price 是否 < 0)
                $updatedStationRow = $serviceStationModel->field('id, price, freeze_price, total_price')->find($stationId);
                 if ($updatedStationRow['price'] < 0) {
                    throw new \Exception('账户可用余额不足以完成冻结');
                 }

                // 3. (可选) 添加资金流水记录
                $moneyData = [
                    'service_station_id' => $stationId,
                    'type' => 1, // 1:提现
                    'money' => $amount, // $amount已经是元单位，z_station_money表存储元单位
                    'withdrawal_request_id' => $requestId, // 关联提现申请ID
                    'create_time' => time(),
                ];
                 $moneyLogId = $stationMoneyModel->add($moneyData);
                 if (!$moneyLogId) {
                     // 记录详细错误信息以便调试
                     \Think\Log::write('添加资金流水失败: ' . $stationMoneyModel->getError() . ', SQL: ' . $stationMoneyModel->getLastSql(), 'ERR');
                     throw new \Exception('添加资金流水失败: ' . $stationMoneyModel->getError());
                 }

                // 提交事务
                $withdrawalRequestModel->commit();
                $this->success("提现申请提交成功，请等待财务审核", U('index/money_withdrawal'));

            } catch (\Exception $e) {
                // 回滚事务
                $withdrawalRequestModel->rollback();
                $this->error("操作失败：" . $e->getMessage());
            }

        } else {
            // GET 请求
            $this->assign('serviceStationRow', $serviceStationRow); // 传递服务站信息到视图
            $this->assign('userRow', $this->userRow);

            // 如果是灵工平台提现，检查额度是否足够
            if ($platform == 'linggong') {
                // 获取每月额度信息
                $monthlyQuotaModel = D("MonthlyWithdrawalQuota");
                $quotaInfo = $monthlyQuotaModel->getStationMonthlyQuota($stationId);

                // 如果额度不足，不允许使用灵工平台提现
                if ($quotaInfo['remaining_quota'] <= 0) {
                    $this->error('当前完税额度已用完，无法使用灵工平台提现，请选择银行卡提现', U('index/money_card'));
                }
            }

            $this->display();
        }
    }

    /**
     * 实时计算税费分配（AJAX接口）
     */
    public function check_tax_distribution() {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $amount = I('post.amount', 0, 'floatval');
        $platform = I('post.platform', 'bank', 'trim'); // 获取平台类型

        if ($amount <= 0) {
            $this->ajaxReturn(['status' => 0, 'info' => '请输入有效的提现金额']);
        }

        $stationId = $this->userRow['self_service_station_id'];
        if (!$stationId) {
            $this->ajaxReturn(['status' => 0, 'info' => '未找到您的服务站信息']);
        }

        // Fetch station info including free limit
        $serviceStationModel = D("ServiceStation");
        $stationInfo = $serviceStationModel->field('id, free_withdrawal_limit')->find($stationId);
        // Don't fail immediately if stationInfo not found, handle freeLimit potentially being 0
        $freeLimit = ($stationInfo && isset($stationInfo['free_withdrawal_limit'])) ? floatval($stationInfo['free_withdrawal_limit']) : 0.0;

        // 获取每月额度信息 (for tax calculation) - This needs to be fetched separately
        $monthlyQuotaModel = D("MonthlyWithdrawalQuota");
        // 获取完整的当月额度信息
        $quotaInfo = $monthlyQuotaModel->getStationMonthlyQuota($stationId);

        // 将quotaInfo中的字段合并到stationInfo中，以便后续使用
        if ($stationInfo && $quotaInfo) {
            $stationInfo['remaining_quota'] = $quotaInfo['remaining_quota'];
            $stationInfo['total_quota'] = $quotaInfo['total_quota'];
            $stationInfo['used_amount'] = $quotaInfo['used_amount'];
            $stationInfo['year_month'] = $quotaInfo['year_month'];
        }

        // 记录额度判断逻辑
        \Think\Log::write('提现额度检查 - stationId: ' . $stationId . ', amount: ' . $amount .
                        ', platform: ' . $platform . ', remaining_quota: ' . $quotaInfo['remaining_quota'], 'INFO');

        // 根据平台类型设置税费处理方式
        if ($platform == 'linggong') {
            // 灵工平台：强制设置为平台负责完税
            $taxHandling = 1; // 1: 平台负责完税

            // 检查额度是否足够
            if ($stationInfo['remaining_quota'] <= 0) {
                $this->ajaxReturn([
                    'status' => 0,
                    'info' => '当前完税额度已用完，无法使用灵工平台提现'
                ]);
            }

            // 如果额度不足以覆盖全部金额，也返回错误
            if ($amount > $stationInfo['remaining_quota']) {
                $this->ajaxReturn([
                    'status' => 0,
                    'info' => '提现金额超出可用完税额度 ¥' . $stationInfo['remaining_quota'] . '，无法使用灵工平台提现'
                ]);
            }

            // 全部由平台负责完税
            $platformTaxAmount = $amount;
            $userInvoiceAmount = 0;
        } else {
            // 银行卡：强制设置为用户开票
            $taxHandling = 2; // 2: 用户开票

            // 全部由用户负责开票
            $platformTaxAmount = 0;
            $userInvoiceAmount = $amount;
        }

        // Calculate service fee considering free limit
        $withdrawalRequestModel = D("WithdrawalRequest"); // Ensure model is available
        $serviceFee = 0.0; // Default to 0
        $actualAmount = $amount; // Default to full amount

        if (!($freeLimit > 0 && $amount <= $freeLimit)) {
            // Only calculate fee if not within free limit
            $serviceFeeRate = $withdrawalRequestModel::getServiceFeeRate();
            $serviceFee = round($amount * $serviceFeeRate, 2);
            $actualAmount = $amount - $serviceFee;
        }
        // $serviceFee and $actualAmount now hold the correct values

        // 获取税费处理方式的文本描述
        $taxHandlingText = $withdrawalRequestModel->tax_handling[$taxHandling]['text'];

        $data = [
            'status' => 1,
            'info' => '计算成功',
            'data' => [
                'tax_handling' => $taxHandling,
                'tax_handling_text' => $taxHandlingText,
                'platform_tax_amount' => $platformTaxAmount,
                'user_invoice_amount' => $userInvoiceAmount,
                'service_fee' => floatval($serviceFee),
                'actual_amount' => floatval($actualAmount),
                'amount' => $amount,
                'quota_info' => [
                    'total_quota' => floatval($quotaInfo['total_quota']),
                    'used_amount' => floatval($quotaInfo['used_amount']),
                    'remaining_quota' => floatval($quotaInfo['remaining_quota']),
                    'year_month' => $quotaInfo['year_month']
                ]
            ]
        ];

        $this->ajaxReturn($data);
    }

    /**
     * 资金明细页面管理
     */
    public function money_alldetails()
    {
        $type = I('get.type', '');
        $startTime = I('get.startTime', 0);
        $endTimes = I('get.endTime', 0);
        // Correctly filter by service_station_id
        $where = ['service_station_id' => $this->userRow['self_service_station_id']];
        $obj = D("StationMoney");

        // 修复类型筛选逻辑，使其与前端按钮定义匹配
        if ($type !== '') {
            $type = intval($type);
            if ($type == 1) { // 收入: 包括类型2(下级贡献)、3(简历)、4(资金返还)
                $where['type'] = ['in', [2, 3, 4]];
            } elseif ($type == 2) { // 支出: 对应类型1(提现)
                $where['type'] = 1;
            }
            // type=0 表示全部，不需要特殊处理
        }

        // 修正日期筛选逻辑
        if ($startTime == 0) {
            $startTime = strtotime(date("Y-m-01 00:00:00"));
        }
        if ($endTimes == 0) {
            // 修复：添加date()函数
            $endTimes = strtotime(date("Y-m-d 23:59:59"));
        }
        if ($startTime > 0 && $endTimes > 0) {
            $where['create_time'] = ['between', [$startTime, $endTimes]];
        }

        $this->assign('type', $type);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 10); // 修改为每页10条记录
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        $this->assign('typeList', $obj->type);

        // 添加对 showpage 参数的处理
        $showpage = I('get.showpage', 0);
        if ($showpage == 1) {
            // 只返回分页HTML
            echo $page->show();
            exit;
        }

        $n = I('get.n', 0);
        if ($page->Current_page > 1 || $n == 1) {
            // 返回列表HTML (AJAX加载或分页后)
            $this->display('list-money_alldetails');
            exit;
        }

        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        // 传递分页HTML到视图
        $this->assign("page", $page->show());
        // 传递总页数到视图，便于前端处理
        $this->assign("totalPages", $page->Total_Pages);
        $this->display();
    }

    /**
     * 提现进度页面管理
     */
    public function money_withdrawal()
    {
        $stationId = $this->userRow['self_service_station_id'];

        // 检查是否为招就办用户，招就办用户不允许查看提现进度
        $serviceStationModel = D("ServiceStation");
        $serviceStationRow = $serviceStationModel->where(['id' => $stationId, 'status' => 1])->find();
        if ($serviceStationRow && $serviceStationRow['zsb_type'] == 2) {
            $this->error("招就办用户不支持提现功能，如有疑问请联系客服");
        }

        $where = ['wr.service_station_id' => $stationId]; // 使用别名 wr
        $obj = D("WithdrawalRequest"); // 查询主表 z_withdrawal_request

        $type = I('get.type'); // 这里的 type 对应提现状态 status
        if ($type > 0) {
            $where['wr.status'] = $type; // 使用别名
        }

        $count = $obj->alias('wr')->where($where)->count();
        $page = $this->page($count, 10); // 每页显示10条记录

        $list = $obj->alias('wr')
            ->join('LEFT JOIN __BANK_CARD__ bc ON wr.bank_card_id = bc.id') // 关联银行卡表
            ->join('LEFT JOIN __LINGGONG_BINDING__ lb ON wr.linggong_binding_id = lb.id') // 关联灵工绑定表
            ->field('wr.*, bc.bank_name, bc.card_number, bc.account_holder, lb.bank_name as linggong_bank_name, lb.bank_account_last4 as linggong_last4, lb.platform_type as linggong_platform_type') // 添加 lb.platform_type
            ->where($where)
            ->limit($page->firstRow . ',' . $page->listRows)
            ->order('wr.request_time desc') // 按申请时间降序
            ->select();

        // 数据处理，例如银行卡号脱敏
        if ($list) {
            foreach ($list as &$item) {
                if ($item['platform_type'] == 'bank' && !empty($item['card_number'])) {
                    // 显示银行卡后四位
                    $item['card_number_masked'] = '**** **** **** ' . substr($item['card_number'], -4);
                } elseif ($item['platform_type'] == 'linggong'){
                    // 灵工平台信息已在查询中获取 (linggong_bank_name, linggong_last4)
                } else {
                     $item['card_number_masked'] = 'N/A'; // 默认或未知情况
                     $item['bank_name'] = 'N/A'; // 确保bank_name也有默认值
                }
                 // 可以根据需要添加更多处理逻辑，如状态文本转换
            }
            unset($item); // 解除最后一个元素的引用
        }

        // 实例化 LinggongBinding 模型并获取 platformTypes
        $linggongBindingModel = D("LinggongBinding");
        $this->assign('linggongPlatformTypes', $linggongBindingModel->platformTypes);

        $this->assign('list', $list);
        $this->assign('statusList', $obj->status); // 使用 WithdrawalRequestModel 的状态定义

        // 添加对 showpage 参数的处理
        $showpage = I('get.showpage', 0);
        if ($showpage == 1) {
            // 只返回分页HTML
            echo $page->show();
            exit;
        }

        $n = I('get.n');
        if ($page->Current_page > 1 || $n == 1) {
             // Ajax 请求或非第一页，只返回列表部分
            $this->display('list-money_withdrawal');
            exit;
        }

        // 首次加载页面需要服务站信息（如果视图中用到的话）
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $stationId, 'status' => 1])
            ->find();
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        // 传递分页HTML到视图
        $this->assign("page", $page->show());
        // 传递总页数到视图，便于前端处理
        $this->assign("totalPages", $page->Total_Pages);

        $this->display();
    }

    /**
     * 服务站管理
     */
    public function servicestation()
    {

        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id'], 'zsb_type' => 1];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }

        $refRow = $obj->where(['status' => 1, 'pid' => $this->userRow['self_service_station_id'], 'zsb_type' => 1])->find();
        $this->assign('refCount', $refRow ? 1 : 0);


        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }

        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 招就办管理
     */
    public function zjb()
    {
        // 权限验证：只有服务站用户才能管理招就办
        if (!$this->validateUserPermission(self::PERMISSION_ZJB_MANAGE)) {
            $this->handlePermissionDenied(self::PERMISSION_ZJB_MANAGE);
            return;
        }

        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['zsb_ref_station' => $this->userRow['self_service_station_id'],'zsb_type' => 2];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }

        $refRow = $obj->where(['status' => 1,'zsb_type' => 2, 'zsb_ref_station' => $this->userRow['self_service_station_id']])->find();
        $this->assign('refCount', $refRow ? 1 : 0);


        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }

        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display('zjb');
    }


    /**
     * 服务站资源包页面
     */
    public function servicestationbuy()
    {
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id'], 'zsb_type' => 1];
        $obj = D("ServiceStation");
        if (!empty($kwd)) {
            $where['service_name'] = ['like', '%' . $kwd . '%'];
        }
        if (!empty($type)) {
            if ($type == 3) {
                $where['status'] = 0;
            } else {
                $where['status'] = $type;
            }
        }
        $this->assign('type', $type);
        $this->assign('kwd', $kwd);
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $list = $obj->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('id desc')
            ->select();
        $this->assign('list', $list);
        $this->assign('statusList', $obj->status);
        if ($page->Current_page > 1) {
            $this->display('list-servicestation');
            exit;
        }

        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', $obj->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->assign("page", $page);
        $this->display();
    }

    /**
     * 服务站资源包划拨页面
     */
    public function to_station()
    {
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['pid' => $this->userRow['self_service_station_id'], 'zsb_type' => 1];
        $obj = D("ServiceStation");
        $this->assign('statusList', $obj->status);
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误!!');
        $divideServiceStation = $obj->where(['id' => $id])->find();
        if (!$divideServiceStation) $this->error('当前服务站不存在!');
        if ($divideServiceStation['pid'] != $this->userRow['self_service_station_id']) $this->error('当前服务站不是您推荐的服务站!');
        if ($divideServiceStation['status'] != 1) $this->error('当前服务站审核未通过 ！');

        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        if ($serviceStationRow['is_out']!=1) $this->error('无资源包划拨权限!!');

        $this->assign('id', $id);
        $this->assign('levelList', $obj->level);
        $this->assign('divideServiceStation', $divideServiceStation);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);
        $this->display();
    }

    /**
     * 划分服务站
     */
    public function divideservicestation()
    {
        if (IS_POST) {
            $id = I('get.id', 0);
            $divide_open_num = I('post.divide_open_num', 0);
            $obj = D("ServiceStation");
            $divideServiceStation = $obj->where(['id' => $id])->find();
            if (!$divideServiceStation) $this->error('当前服务站不存在!');
            if ($divideServiceStation['pid'] != $this->userRow['self_service_station_id']) $this->error('当前服务站不是您推荐的服务站!');
            if ($divideServiceStation['status'] != 1) $this->error('当前服务站审核未通过 ！');
            $serviceStationRow = D("ServiceStation")
                ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
                ->find();
            if ($serviceStationRow['is_out']!=1) $this->error('无资源包划拨权限!!');
            $openNum = $serviceStationRow['open_num'];
            if ($openNum <= 0) $this->error('您的服务站资源包数量不足');
            if ($openNum - $divide_open_num < 0) $this->error('划拨数量不足');
            $insertId = D("ServiceStationDivideLog")->add([
                'user_id' => $this->userRow['id'],
                'service_station_id' => $serviceStationRow['id'],
                'divide_service_station_id' => $divideServiceStation['id'],
                'service_station_open_num' => $openNum,
                'divide_open_num' => $divide_open_num,
            ]);
            if ($insertId) {
                D("ServiceStation")->save(['id' => $divideServiceStation['id'], 'open_num' => $divide_open_num]);
                D("ServiceStation")->save(['id' => $serviceStationRow['id'], 'open_num' => ['exp','open_num-' . $divide_open_num], 'divide_open_num' => ['exp', 'divide_open_num+' . $divide_open_num]]);
                return $this->success('资源包数已划拨成功');
            }
        }
    }

    /**
     * 问答页面
     */
    public function qa()
    {
        $kwd = I('get.kwd', '');
        $type = I('get.type', '');
        $where = ['status' => 1, 'pid' => 0];
        $obj = D("QuestionCategory");

        $list = $obj
            ->where($where)
            ->order('sort asc,id desc')
            ->select();
        $navData = [];
        if ($list) {
            $categoryId = array_unique(array_column($list, 'id'));
            if ($categoryId) {
                $questionCategoryTwo = D("QuestionCategory")->where(['pid' => ['in', $categoryId]])->order('sort asc')->select();
                $questionCategoryTwoList = [];
                foreach ($questionCategoryTwo as $questionCategoryTwoRow) {
                    $questionCategoryTwoList[$questionCategoryTwoRow['pid']][$questionCategoryTwoRow['id']] = $questionCategoryTwoRow;
                }
            }
            $navData = [];

            foreach ($list as $row) {
                $subs = [];
                $questions = [];
//                foreach ($questionCategoryTwoList[$row['id']] as $questionCategoryTwoRow) {
//                    $name = $questionCategoryTwoRow['title'];
//                    $subs[] = $name;
//                    $qaList = [];
//
//                }
                $questionList = D("Question")->field('id,title')->where(['category_id' => ['like', '%,'.$row['id'].',%'], 'status' => 1])->select();
                $lists = [];
                foreach ($questionList as $questionRow) {
                    $lists[] = ['id' => $questionRow['id'], 'name' => $questionRow['title']];
                }
                $questions[$row['title']] = $lists;
                $navData[$row['title']] = [
//                    'subs' => $subs,
                    'questions' => $questions,
                ];
            }
        }
        $this->assign('navData', json_encode($navData, JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE));
        $this->assign('list', $list);
        $this->assign('questionCategoryTwoList', $questionCategoryTwoList);


        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);
        $this->display();
    }


    /**
     * 公告
     */
    public function qacontent()
    {
        $id = I('get.id', 0);
        if (!$id) return $this->redirect(U('index/index'));
        $row = D("Question")->where(['id' => $id, 'status' => 1])->find();
        if (!$row) return $this->redirect(U('index/index'));
        $this->assign('row', $row);


        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);
        $this->display();
    }

    /**
     * 服务站审核
     */
    public function succservicestation()
    {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id])->find();
        if ($row['status'] == 1) $this->error('当前服务站已经审核通过');
        $pidRow = [];
        if ($row['pid'] > 0) {
            $pidRow = $obj->where(['id' => $row['pid']])->find();
        }
        // 检查是否为招就办类型
        $isZjb = ($row['zsb_type'] == 2);

        // 服务站需要检查资源包，招就办不需要
        $canApprove = $isZjb || ($row['pid'] == $this->userRow['service_station_id'] && ($pidRow['open_num'] - $pidRow['succ_open_num']) > 0);

        if ($canApprove) {
            $code = D("Qrcode")->createNew(1, 1, $row['id']);
            if ($code) {
                // 只有服务站审核通过才消耗资源包
                if (!$isZjb) {
                    $obj->save(['id' => $this->userRow['id'], 'succ_open_num' => ['exp', 'succ_open_num+1']]);
                }
                $obj->save(['id' => $id, 'status' => 1]);
                if (!D("Qrcode")->where(['service_id' => 2, 'service_station_id' => $row['id']])->find()) {
                    $code = D("Qrcode")->createNew(2, 1, $row['id']);
                }
                if (!D("Qrcode")->where(['service_id' => 1, 'service_station_id' => $row['id']])->find()) {
                    $code = D("Qrcode")->createNew(1, 1, $row['id']);
                }
                D("User")->where(['self_service_station_id' => $id])->save(['is_service_station' => 2]);
                $this->success('审核通过');
            } else {
                $this->error('开通失败，请联系技术处理');
            }
        } else {
            // 根据类型显示不同的错误信息
            if ($isZjb) {
                $this->error('招就办审核失败');
            } else {
                $this->error('当前服务站的开通数量不足');
            }
        }
        $this->error('参数错误!!');
    }

    /**
     * 审核失败
     */
    public function errservicestation()
    {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id])->find();
        if ($row['status'] > 0) $this->error('当前服务站已经审核');
        $obj->save(['id' => $id, 'status' => 2]);
        $this->success('审核状态已更新', U('index/servicestation'));
    }

    /**
     * 待审核
     * @return void
     */
    public function audits()
    {
        if ($this->userRow['is_service_station'] == 0) { //注册绑定
            return $this->redirect(U('index/logins'));
            die;
        } elseif ($this->userRow['is_service_station'] == 2) { //审核通过
            return $this->redirect(U('index/index'));
            die;
        } elseif ($this->userRow['is_service_station'] == 1) { //待审核
            $this->display();
        } else if ($this->userRow['is_service_station'] == 3) { //审核失败
            return $this->redirect(U('index/erroraudits'));
            die;
        }
    }

    /**
     * 审核失败
     * @return void
     */
    public function erroraudits()
    {
        if ($this->userRow['is_service_station'] == 0) { //注册绑定
            return $this->redirect(U('index/logins'));
            die;
        } elseif ($this->userRow['is_service_station'] == 2) { //审核通过
            return $this->redirect(U('index/index'));
            die;
        } elseif ($this->userRow['is_service_station'] == 1) { //待审核
            return $this->redirect(U('index/audits'));
            die;
        } else if ($this->userRow['is_service_station'] == 3) { //审核失败
            $this->display();
        }
    }

    /**
     * 注册子服务站或招就办
     * @return void
     */
    public function register()
    {
        if ($this->userRow['is_service_station'] == 0) {
            return $this->redirect(U('index/logins'));
        }

        // 获取注册类型参数 1=服务站 2=招就办
        $registerType = I('get.type', 1);
        $this->assign('registerType', $registerType);

        if ($this->userRow && $this->userRow['self_service_station_id'] > 0) { //注册绑定
            $selfServiceStationRow = D("ServiceStation")->where(['id' => $this->userRow['self_service_station_id']])->find();

            // 只有服务站注册需要检查资源包，招就办注册不需要（页面加载时检查）
            if (!IS_POST && $registerType == 1 && ($selfServiceStationRow['open_num'] - $selfServiceStationRow['succ_open_num']) <= 0) {
                $this->error('开通服务站数量不足');
            }

            // 如果是GET请求且为招就办注册，传递服务站信息到前端
            if (!IS_POST && $registerType == 2) {
                // 获取当前用户的服务站信息
                $currentStationId = $this->userRow['self_service_station_id'];
                if ($currentStationId) {
                    $currentStationRow = D("ServiceStation")
                        ->where(['id' => $currentStationId, 'status' => 1])
                        ->find();
                    if ($currentStationRow) {
                        $this->assign('currentStationRow', $currentStationRow);
                    }
                }
            }

            if (IS_POST) {
                $data = I('post.');
                $mobile = $data['mobile'];
                $code = $data['code'];
                $registerType = intval($data['register_type']); // 从表单获取类型

                // 保存表单数据到session，以便验证失败时恢复
                session('register_form_data', $data);

                // POST提交时再次检查资源包（只有服务站注册需要）
                if ($registerType == 1 && ($selfServiceStationRow['open_num'] - $selfServiceStationRow['succ_open_num']) <= 0) {
                    $this->error('开通服务站数量不足');
                }

                // 基础验证
                if (empty($data['mobile'])) $this->error('参数错误');
                $serviceStationRow = D("ServiceStation")->where(['mobile' => $data['mobile']])->find();
                if ($serviceStationRow) $this->error('当前手机号已经被注册');
                if (empty($mobile)) $this->error('手机号未填写');
                if (empty($code)) $this->error('验证码未填写');

                // 根据类型进行不同的验证
                if ($registerType == 1) {
                    // 服务站验证
                    if (empty($data['business_license'])) $this->error("营业执照不能为空");
                    if (empty($data['franchise_list'])) $this->error("加盟表不能为空");
                } else if ($registerType == 2) {
                    // 招就办验证
                    if (empty($data['contract_name'])) $this->error("联系人姓名不能为空");
                    if (empty($data['id_card'])) $this->error("身份证号不能为空");
                    if (strlen($data['id_card']) != 18) $this->error("身份证号格式不正确");
                    if (empty($data['email'])) $this->error("电子邮件不能为空");
                    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) $this->error("电子邮件格式不正确");
                    if (empty($data['mail_address'])) $this->error("通讯地址不能为空");
                    if (empty($data['zsb_zj_pic'])) $this->error("证件照不能为空");
                    if (empty($data['zsb_sfz_rx'])) $this->error("身份证人像面不能为空");
                    if (empty($data['zsb_sfz_gh'])) $this->error("身份证国徽面不能为空");
                }

                // 验证码校验 - 添加详细调试日志
                $sessionCode = session('code_' . $mobile);
                $sessionTime = session('time_' . $mobile);
                $currentTime = time();
                $codeExpired = $sessionTime && ($currentTime - $sessionTime > 300); // 5分钟过期

                $this->writeLog('verify_debug', "验证码校验 - 手机号: {$mobile}, 用户输入: {$code}, Session存储: " . ($sessionCode ? $sessionCode : 'null') . ", 发送时间: " . ($sessionTime ? date('Y-m-d H:i:s', $sessionTime) : 'null') . ", 是否过期: " . ($codeExpired ? '是' : '否'));

                // 开发环境跳过验证码验证，或者验证码正确且未过期
                $isValidCode = (defined('APP_DEBUG') && APP_DEBUG) || ($code == $sessionCode && !$codeExpired);

                $this->writeLog('verify_debug', "验证码校验结果 - 手机号: {$mobile}, 是否通过: " . ($isValidCode ? '是' : '否') . ", APP_DEBUG: " . (defined('APP_DEBUG') && APP_DEBUG ? '是' : '否'));

                if ($isValidCode) {
                    // 验证码校验通过，清除session中的验证码（确保一次性使用）
                    session('code_' . $mobile, null);
                    $this->writeLog('verify_debug', "验证码校验通过，已清除session - 手机号: {$mobile}");

                    // 处理招就办名称：如果是招就办注册，自动生成名称
                    $serviceName = $data['service_name'];
                    if ($registerType == 2) {
                        // 获取当前用户的服务站信息和联系人姓名
                        $currentStationId = $this->userRow['self_service_station_id'];
                        $contactName = $data['contract_name']; // 联系人姓名
                        if ($currentStationId && !empty($contactName)) {
                            $currentStationRow = D("ServiceStation")
                                ->where(['id' => $currentStationId, 'status' => 1])
                                ->find();
                            if ($currentStationRow) {
                                // 自动生成招就办名称：服务站名称 - 联系人姓名
                                $serviceName = $currentStationRow['service_name'] . '-' . $contactName;
                            }
                        }
                    }

                    // 基础数据
                    $insertData = [
                        'mobile' => $data['mobile'],
                        'service_name' => $serviceName,
                        'pid' => $this->userRow['self_service_station_id'],
                        'create_time' => time(),
                        'zsb_type' => $registerType,
                    ];

                    // 根据类型添加特定字段
                    if ($registerType == 1) {
                        // 服务站字段
                        $insertData['enterprise_name'] = $data['enterprise_name'];
                        $insertData['business_license'] = $data['business_license'];
                        $insertData['franchise_list'] = $data['franchise_list'];
                    } else if ($registerType == 2) {
                        // 招就办字段
                        $insertData['contract_name'] = $data['contract_name'];
                        $insertData['contract_card'] = $data['id_card']; // 身份证号
                        $insertData['email'] = $data['email']; // 电子邮件
                        $insertData['mail_address'] = $data['mail_address'];
                        $insertData['zsb_ref_station'] = $this->userRow['self_service_station_id'];
                        $insertData['zsb_zj_pic'] = $data['zsb_zj_pic'];
                        $insertData['zsb_sfz_rx'] = $data['zsb_sfz_rx'];
                        $insertData['zsb_sfz_gh'] = $data['zsb_sfz_gh'];
                        $insertData['zsb_rlzyzs'] = $data['zsb_rlzyzs'] ?? '';
                        $insertData['zsb_fwzht'] = $data['zsb_fwzht'] ?? '';
                        $insertData['zsb_wfzzm'] = $data['zsb_wfzzm'] ?? '';
                        $insertData['level'] = 1; // 招就办默认等级为1
                        // 招就办使用个人名称作为企业名称
                        $insertData['enterprise_name'] = $data['contract_name'];
                    }

                    $insertId = D("ServiceStation")->add($insertData);
                    if ($insertId) {
                        // 注册成功，清除保存的表单数据
                        session('register_form_data', null);
                        $successMsg = $registerType == 1 ? '注册服务站成功！' : '注册招就办成功！';
                        return $this->success($successMsg, U('index/index'));
                    }
                } else {
                    // 提供更详细的错误信息
                    if (!$sessionCode) {
                        $this->writeLog('verify_error', "验证码校验失败 - 手机号: {$mobile}, 原因: 未找到验证码");
                        return $this->error('验证码不存在，请重新获取验证码！');
                    } elseif ($codeExpired) {
                        $this->writeLog('verify_error', "验证码校验失败 - 手机号: {$mobile}, 原因: 验证码已过期");
                        return $this->error('验证码已过期，请重新获取验证码！');
                    } else {
                        $this->writeLog('verify_error', "验证码校验失败 - 手机号: {$mobile}, 原因: 验证码不匹配");
                        return $this->error('验证码错误，请检查后重新输入！');
                    }
                }
            }

            // 获取保存的表单数据（如果有的话）
            $savedFormData = session('register_form_data');
            if ($savedFormData) {
                $this->assign('savedFormData', $savedFormData);
            }

            $this->display();
        } elseif ($this->userRow['is_service_station'] == 2) { //审核通过
            return $this->redirect(U('index/index'));
            die;
        } elseif ($this->userRow['is_service_station'] == 1) { //待审核
            return $this->redirect(U('index/audits'));
            die;
        } else if ($this->userRow['is_service_station'] == 3) { //审核失败
            $this->display();
        } else {
            return $this->redirect(U('index/logins'));
            die;
        }
    }

    /**
     * 文件上传（支持图片和PDF）
     */
    public function uploads()
    {
        $base64_content = I('post.img', '');

        // 支持图片格式
        if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $base64_content, $result)) {
            $type = $result[2]; // 图片类型，如 png、jpeg
            $base64_data = str_replace($result[1], '', $base64_content);
            $base64_data = base64_decode($base64_data);
            $upload_dir = SITE_PATH . '/data/Material/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            $file_name = uniqid() . '.' . $type;
            $file_path = $upload_dir . $file_name;
            $returnPath = '/data/Material/' . $file_name;
            if (file_put_contents($file_path, $base64_data)) {
                $this->success($returnPath);
                die;
            } else {
                die("图片保存失败");
                $this->error('图片保存失败');
            }
        }

        // 支持PDF格式
        if (preg_match('/^(data:\s*application\/pdf;base64,)/', $base64_content, $result)) {
            $base64_data = str_replace($result[1], '', $base64_content);
            $base64_data = base64_decode($base64_data);
            $upload_dir = SITE_PATH . '/data/Material/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            $file_name = uniqid() . '.pdf';
            $file_path = $upload_dir . $file_name;
            $returnPath = '/data/Material/' . $file_name;
            if (file_put_contents($file_path, $base64_data)) {
                $this->success($returnPath);
                die;
            } else {
                die("PDF保存失败");
                $this->error('PDF保存失败');
            }
        }

        $this->error('文件格式不支持，仅支持图片和PDF文件');
    }

    // 发送手机验证码
    public function getcode()
    {
        $mobile = I("get.mobile");
        $ret['code'] = 0;
        $stataion = true;
        $usermobileRow = D("User")->where(['mobile' => $mobile, 'service_id' => 2])->find();
        if ($usermobileRow && $usermobileRow['id'] != $this->userRow['id']) {
            $ret = [
                'code' => 1,
                'msg' => '手机号已经被绑定！',
            ];
        } else {
            $last = session('time_' . $mobile);
            if ($last and $last > time() - 60) {
                $ret = [
                    'code' => 2,
                    'msg' => '验证码已发送，请稍后请求！',
                ];
            } else {
                $code = mt_rand(10000, 99999);
                session('code_' . $mobile, $code);
                session('time_' . $mobile, time());

                // 记录验证码存储信息
                $this->writeLog('verify_debug', "验证码存储 - 手机号: {$mobile}, 验证码: {$code}, Session Key: code_{$mobile}");

                // 发送短信验证码 - 使用HTTP方式避免SDK兼容性问题
                // 记录调试信息
                $this->writeLog('sms_debug', "开始发送短信验证码 - 手机号: {$mobile}, 验证码: {$code}");

                // 使用HTTP方式发送短信，使用验证码模板SMS_479770174
                $result = $this->sendVerificationCode($code, $mobile);

                // 记录发送结果
                $this->writeLog('sms_debug', "短信发送结果: " . ($result ? '成功' : '失败'));

                if ($result) {
                    $ret = ['code' => 0, "msg" => '验证码已发送'];
                    $this->writeLog('sms_success', "短信发送成功 - 手机号: {$mobile}");
                } else {
                    $ret = ['code' => 3, "msg" => '验证码发送失败！'];
                    $this->writeLog('sms_error', "短信发送失败 - 手机号: {$mobile}, 验证码: {$code}");
                }
            }
        }
        echo json_encode($ret);
        die;
    }

     /**
     * 法律
     */


    public function law()
    {
     $id = I('get.id', '13');
     if (!$id) $this->error('参数错误');
     $obj = D("page_list");
     $row = $obj->where(['id' => $id])->find();
     if (!$row) $this->error('无信息');
     $page_list = D("page_list")->where(['status' => 1])->field('id,title,content')->select();
     if (!C('LOCAL_DEBUG')) {
         $this->assign('wxconf', getWxConfig(null, $this->conf));
         $this->assign('appid', $this->conf['WX_APPID']);
     }
     $this->assign('projectList', $page_list);
     $this->assign('row', $row);
     $this->display();
    }

        /**
     * 朋友圈素材
     */
    public function moments(){
        // 获取朋友圈素材列表，只获取status为1（上架状态）的素材
        $momentsList = M('moment')->where(['status' => 1])->order('create_time DESC')->select();

        // 处理每个素材的图片和内容
        foreach ($momentsList as &$item) {
            // 获取项目名称和项目头像（pyq_logo）
            if (!empty($item['project_id'])) {
                $project = M('project')->where(['id' => $item['project_id']])->find();
                $item['project_name'] = $project ? $project['name'] : '未知项目';
                // 使用项目的pyq_logo字段作为头像路径，如果为空则使用默认路径
                $item['avatar'] = !empty($project['pyq_logo']) ? $project['pyq_logo'] : '/static/stations/images/logozcgk.png';
            } else {
                $item['project_name'] = '中才国科';
                $item['avatar'] = '/static/stations/images/logozcgk.png';
            }

            // 优化内容格式，处理换行和标题
            if (!empty($item['content'])) {
                $content = $item['content'];

                // 识别并处理常见的emoji表情符号
                $emojiMap = [
                    '✅' => '✅',
                    '👉' => '👉',
                    '🔥' => '🔥',
                    '⭐' => '⭐',
                    '📢' => '📢',
                    '💰' => '💰',
                    '🌟' => '🌟'
                ];

                foreach ($emojiMap as $emoji => $replacement) {
                    $content = str_replace($emoji, $replacement, $content);
                }

                // 处理段落
                $paragraphs = explode("\n\n", $content);
                $formattedContent = '';

                foreach ($paragraphs as $index => $para) {
                    if (trim($para) !== '') {
                        // 处理段落内的单个换行为<br>
                        $para = str_replace("\n", "<br>", $para);

                        // 第一段如果包含【】可能是标题，但仍然保留在内容中，前端会处理
                        $formattedContent .= "<p>{$para}</p>";
                    }
                }

                $item['content'] = $formattedContent;
            }

            // 处理图片列表
            $item['image_list'] = [];
            // 收集所有非空图片
            for ($i = 1; $i <= 9; $i++) {
                $imgField = 'img' . $i;
                if (!empty($item[$imgField])) {
                    $item['image_list'][] = $item[$imgField];
                }
            }
        }
        // 取消引用，防止后续操作意外修改$momentsList中的最后一个元素
        unset($item);

        $this->assign('momentsList', $momentsList);
        $this->display('Moments/index');
    }

    /**
     * 查询当月提现额度使用情况
     */
    public function check_quota() {
        $stationId = $this->userRow['self_service_station_id'];
        if (!$stationId) {
            $this->error("未找到您的服务站信息");
        }

        // 获取服务站信息
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();

        // 检查是否为招就办用户，招就办用户不允许查看提现额度
        if ($serviceStationRow && $serviceStationRow['zsb_type'] == 2) {
            $this->error("招就办用户不支持提现功能，如有疑问请联系客服");
        }

        $monthlyQuotaModel = D("MonthlyWithdrawalQuota");

        // 获取当月额度使用情况
        $quota = $monthlyQuotaModel->getStationMonthlyQuota($stationId);

        // 格式化为友好显示
        $data = [
            'total_quota' => number_format($quota['total_quota'], 2),
            'used_amount' => number_format($quota['used_amount'], 2),
            'remaining_quota' => number_format($quota['remaining_quota'], 2),
            'is_exceed' => $quota['used_amount'] >= $quota['total_quota'],
            'year_month' => substr($quota['year_month'], 0, 4) . '年' . substr($quota['year_month'], 4, 2) . '月'
        ];

        // AJAX请求返回JSON数据
        if (IS_AJAX) {
            $this->ajaxReturn(['status' => 1, 'data' => $data]);
        }

        // 页面请求返回视图
        $this->assign('quota', $data);

        // 添加用户和服务站信息，用于显示头像和级别
        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->display('quota_usage');
    }

    /**
     * 京东京灵平台灵工绑定状态查询
     */
    public function checkJinglingBinding()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $userId = $this->userRow['id'];
        $stationId = $this->userRow['self_service_station_id'];

        if (empty($userId) || empty($stationId)) {
            $this->ajaxReturn(['status' => 0, 'msg' => '未找到用户或服务站信息']);
        }

        $jdBindingModel = D("LinggongBinding");
        $bindingInfo = $jdBindingModel->checkUserBinding($userId);

        if (!$bindingInfo) {
            // 未绑定，生成绑定二维码
            $bindingQrCode = $jdBindingModel->generateQrCode($userId);
            $this->ajaxReturn([
                'status' => 0,
                'is_bound' => false,
                'qr_code' => $bindingQrCode
            ]);
        } else {
            // 已有绑定记录
            $this->ajaxReturn([
                'status' => 1,
                'is_bound' => $bindingInfo['status'] == 1,
                'binding_status' => $bindingInfo['status'],
                'binding_status_text' => $jdBindingModel->status[$bindingInfo['status']],
                'binding_info' => $bindingInfo
            ]);
        }
    }

    /**
     * 提交京东京灵平台灵工绑定信息
     */
    public function submitJinglingBinding()
    {
        if (!IS_POST) {
            $this->display('jd_binding_form');
            return;
        }

        $userId = $this->userRow['id'];
        $stationId = $this->userRow['self_service_station_id'];

        if (empty($userId) || empty($stationId)) {
            $this->error('未找到用户或服务站信息');
        }

        // 获取表单数据
        $workerName = I('post.worker_name', '', 'trim');
        $idCard = I('post.id_card', '', 'trim');
        $phone = I('post.phone', '', 'trim');
        $bankName = I('post.bank_name', '', 'trim');
        $bankAccountLast4 = I('post.bank_account_last4', '', 'trim');

        // 验证数据
        if (empty($workerName)) {
            $this->error('请输入灵工姓名');
        }
        if (empty($idCard) || strlen($idCard) != 18) {
            $this->error('请输入有效的18位身份证号');
        }
        if (empty($phone) || !preg_match('/^1[3-9]\d{9}$/', $phone)) {
            $this->error('请输入有效的手机号');
        }
        if (empty($bankName)) {
            $this->error('请输入结算银行');
        }
        if (empty($bankAccountLast4) || !preg_match('/^\d{4}$/', $bankAccountLast4)) {
            $this->error('请输入正确的银行账号后四位');
        }

        $jdBindingModel = D("LinggongBinding");

        // 检查是否已有绑定记录
        $existingBinding = $jdBindingModel->checkUserBinding($userId);

        if ($existingBinding) {
            // 已有记录，更新信息
            $updateData = [
                'worker_name' => $workerName,
                'id_card' => $idCard,
                'phone' => $phone,
                'bank_name' => $bankName,
                'bank_account_last4' => $bankAccountLast4,
                'status' => 0, // 重置为待审核状态
                'remark' => '' // 清空之前的备注
            ];

            $result = $jdBindingModel->updateBinding($existingBinding['id'], $updateData);
            if ($result) {
                $this->success('京灵绑定信息已更新，请等待审核', U('Index/money'));
            } else {
                $this->error('京灵绑定信息更新失败，请重试');
            }
        } else {
            // 新增绑定记录
            $bindingData = [
                'user_id' => $userId,
                'service_station_id' => $stationId,
                'worker_name' => $workerName,
                'id_card' => $idCard,
                'phone' => $phone,
                'bank_name' => $bankName,
                'bank_account_last4' => $bankAccountLast4,
                'qr_code' => $jdBindingModel->generateQrCode($userId)
            ];

            $result = $jdBindingModel->addBinding($bindingData);
            if ($result) {
                $this->success('京灵绑定信息已提交，请等待审核', U('Index/money'));
            } else {
                $this->error('京灵绑定信息提交失败，请重试');
            }
        }
    }

    /**
     * 京东京灵绑定信息填写页面
     */
    public function jdBindingForm()
    {
        $userId = $this->userRow['id'];
        $stationId = $this->userRow['self_service_station_id'];

        if (empty($userId) || empty($stationId)) {
            $this->error('未找到用户或服务站信息');
        }

        // 检查是否已有绑定信息
        $jdBindingModel = D("LinggongBinding");
        $bindingInfo = $jdBindingModel->checkUserBinding($userId);

        $this->assign('bindingInfo', $bindingInfo);
        $this->assign('userRow', $this->userRow);

        $this->display('jd_binding_form');
    }

    /**
     * 获取服务站信息（用于招就办名称自动生成）
     */
    public function getStationInfo()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $stationId = $this->userRow['self_service_station_id'];
        if (!$stationId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '未找到服务站信息']);
        }

        // 获取服务站信息
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $stationId, 'status' => 1])
            ->find();

        if (!$serviceStationRow) {
            $this->ajaxReturn(['status' => 0, 'msg' => '服务站信息不存在']);
        }

        // 注意：这里需要从表单获取联系人姓名，所以暂时返回空，让前端动态生成
        // 实际的招就办名称将在前端输入联系人姓名后动态生成

        $this->ajaxReturn([
            'status' => 1,
            'data' => [
                'station_name' => $serviceStationRow['service_name']
            ]
        ]);
    }

    /**
     * 灵工绑定表单页面
     */
    public function linggongBindingForm()
    {
        $userId = $this->userRow['id'];
        $stationId = $this->userRow['self_service_station_id'];

        if (empty($userId) || empty($stationId)) {
            $this->error('未找到用户或服务站信息');
        }

        $bindingModel = D("LinggongBinding");
        $bindingInfo = $bindingModel->checkUserBinding($userId);

        $this->assign('bindingInfo', $bindingInfo);
        $this->display();
    }

    /**
     * 提交灵工绑定信息
     */
    public function submitLinggongBinding()
    {
        if (!IS_POST) {
            $this->error('非法请求');
        }

        $userId = $this->userRow['id'];
        $stationId = $this->userRow['self_service_station_id'];

        if (empty($userId) || empty($stationId)) {
            $this->error('未找到用户或服务站信息');
        }

        // 获取表单数据
        $platformType = I('post.platform_type', 1, 'intval');
        $workerName = I('post.worker_name', '', 'trim');
        $idCard = I('post.id_card', '', 'trim');
        $phone = I('post.phone', '', 'trim');
        $bankName = I('post.bank_name', '', 'trim');
        $bankAccountLast4 = I('post.bank_account_last4', '', 'trim');

        // 简单验证
        if (empty($workerName) || empty($idCard) || empty($phone) || empty($bankName) || empty($bankAccountLast4)) {
            $this->error('请填写所有必填项');
        }

        // 验证身份证号
        if (strlen($idCard) != 18) {
            $this->error('请输入正确的18位身份证号');
        }

        // 验证手机号
        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            $this->error('请输入正确的11位手机号');
        }

        // 验证银行账号后四位
        if (!preg_match('/^\d{4}$/', $bankAccountLast4)) {
            $this->error('请输入正确的银行账号后四位');
        }

        $bindingModel = D("LinggongBinding");
        $existingBinding = $bindingModel->checkUserBinding($userId);

        if ($existingBinding) {
            // 已有记录，更新信息
            $updateData = [
                'worker_name' => $workerName,
                'id_card' => $idCard,
                'phone' => $phone,
                'bank_name' => $bankName,
                'bank_account_last4' => $bankAccountLast4,
                'platform_type' => $platformType,
                'status' => 0, // 重置为待审核状态
                'remark' => '' // 清空之前的备注
            ];

            $result = $bindingModel->updateBinding($existingBinding['id'], $updateData);
            if ($result) {
                $this->success('灵工绑定信息已更新，请等待审核', U('Index/money'));
            } else {
                $this->error('灵工绑定信息更新失败，请重试');
            }
        } else {
            // 新增绑定记录
            $bindingData = [
                'user_id' => $userId,
                'service_station_id' => $stationId,
                'worker_name' => $workerName,
                'id_card' => $idCard,
                'phone' => $phone,
                'bank_name' => $bankName,
                'bank_account_last4' => $bankAccountLast4,
                'platform_type' => $platformType,
                'qr_code' => $bindingModel->generateQrCode($userId, $platformType)
            ];

            $result = $bindingModel->addBinding($bindingData);
            if ($result) {
                $this->success('灵工绑定信息已提交，请等待审核', U('Index/money'));
            } else {
                $this->error('灵工绑定信息提交失败，请重试');
            }
        }
    }

    /**
     * 检查灵工绑定状态
     */
    public function checkLinggongBinding()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $userId = $this->userRow['id'];
        $stationId = $this->userRow['self_service_station_id'];

        if (empty($userId) || empty($stationId)) {
            $this->ajaxReturn(['status' => 0, 'msg' => '未找到用户或服务站信息']);
        }

        $bindingModel = D("LinggongBinding");
        $bindingInfo = $bindingModel->checkUserBinding($userId);

        if (!$bindingInfo) {
            // 未绑定
            $this->ajaxReturn([
                'status' => 0,
                'is_bound' => false,
                'platform_types' => $bindingModel->platformTypes
            ]);
        } else {
            // 已有绑定记录
            $this->ajaxReturn([
                'status' => 1,
                'is_bound' => $bindingInfo['status'] == 1,
                'binding_status' => $bindingInfo['status'],
                'binding_status_text' => $bindingModel->status[$bindingInfo['status']],
                'platform_type' => $bindingInfo['platform_type'],
                'platform_type_text' => $bindingModel->platformTypes[$bindingInfo['platform_type']] ?? '未知平台',
                'binding_info' => $bindingInfo
            ]);
        }
    }

    /**
     * 简单的日志记录方法
     */
    private function writeLog($type, $message) {
        $logDir = SITE_PATH . '/data/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        $logFile = $logDir . '/' . $type . '.log';
        $logMessage = date('Y-m-d H:i:s') . ' - ' . $message . PHP_EOL;
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }

    /**
     * 清除注册表单数据的AJAX接口
     */
    public function clearRegisterData() {
        session('register_form_data', null);
        $this->ajaxReturn(['status' => 1, 'msg' => '数据已清除']);
    }

    /**
     * 发送验证码短信 - 使用HTTP方式避免SDK兼容性问题
     * 参考AliSms类的sendSms方法实现，使用验证码模板SMS_479770174
     */
    private function sendVerificationCode($code, $mobile, $tempcode = 'SMS_479770174') {
        try {
            // 引入现有的HTTP工具类
            require_once SITE_PATH . '/Core/Library/Vendor/LaneWeChat/core/curl.lib.php';

            // 阿里云短信服务配置 - 使用与AliSms类相同的配置
            $accessKeyId = 'LTAI5tDcTSoBjm3knJTo4PfE';
            $accessKeySecret = '******************************';

            // 构建模板参数
            $tempText = "{\"code\":\"".$code."\"}";

            // 记录请求参数
            $this->writeLog('sms_debug', "构建请求参数 - 手机号: {$mobile}, 模板: {$tempcode}, 参数: {$tempText}");

            // 转换为阿里云API格式的请求参数
            $apiParams = array(
                'Action' => 'SendSms',
                'Version' => '2017-05-25',
                'RegionId' => 'cn-hangzhou',
                'PhoneNumbers' => $mobile,
                'SignName' => '中才国科',
                'TemplateCode' => $tempcode,
                'TemplateParam' => $tempText,
                'AccessKeyId' => $accessKeyId,
                'Format' => 'JSON',
                'Timestamp' => gmdate('Y-m-d\TH:i:s\Z'),
                'SignatureMethod' => 'HMAC-SHA1',
                'SignatureVersion' => '1.0',
                'SignatureNonce' => uniqid()
            );

            // 生成签名
            $signature = $this->generateSignature($apiParams, $accessKeySecret);
            $apiParams['Signature'] = $signature;

            // 记录API调用
            $this->writeLog('sms_debug', "调用阿里云API - URL: https://dysmsapi.aliyuncs.com/");

            // 使用HTTP工具类发送请求
            $res = \LaneWeChat\Core\Curl::callWebServer('https://dysmsapi.aliyuncs.com/', $apiParams, 'get', true, false);

            // 记录响应
            $this->writeLog('sms_debug', "API响应: " . (is_array($res) ? json_encode($res) : $res));

            // 处理响应
            if ($res === false) {
                $this->writeLog('sms_error', "HTTP请求失败 - 手机号: {$mobile}");
                return false;
            }

            // 检查响应结果
            if (isset($res['Code']) && $res['Code'] == 'OK') {
                $this->writeLog('sms_success', "短信发送成功 - 手机号: {$mobile}, RequestId: " . (isset($res['RequestId']) ? $res['RequestId'] : ''));
                return true;
            } else {
                $errorMsg = isset($res['Message']) ? $res['Message'] : '未知错误';
                $this->writeLog('sms_error', "短信发送失败 - 手机号: {$mobile}, 错误: {$errorMsg}, 完整响应: " . json_encode($res));
                return false;
            }
        } catch (\Exception $e) {
            $this->writeLog('sms_error', "短信发送异常 - 手机号: {$mobile}, 异常: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成阿里云API签名
     */
    private function generateSignature($params, $accessKeySecret) {
        // 排序参数
        ksort($params);

        // 构建待签名字符串
        $stringToSign = '';
        foreach ($params as $key => $value) {
            if ($key !== 'Signature') {
                $stringToSign .= '&' . $this->percentEncode($key) . '=' . $this->percentEncode($value);
            }
        }
        $stringToSign = substr($stringToSign, 1); // 去掉第一个&

        $stringToSign = 'GET&' . $this->percentEncode('/') . '&' . $this->percentEncode($stringToSign);

        // 计算签名
        $signature = base64_encode(hash_hmac('sha1', $stringToSign, $accessKeySecret . '&', true));

        return $signature;
    }

    /**
     * URL编码
     */
    private function percentEncode($str) {
        $res = urlencode($str);
        $res = preg_replace('/\+/', '%20', $res);
        $res = preg_replace('/\*/', '%2A', $res);
        $res = preg_replace('/%7E/', '~', $res);
        return $res;
    }

    /**
     * 免服务费额度页面
     */
    public function free_limit_info()
    {
        $stationId = $this->userRow['self_service_station_id'];
        if (!$stationId) {
            $this->error("未找到您的服务站信息");
        }

        $serviceStationModel = D("ServiceStation");
        // Fetch necessary fields including the new limit
        $serviceStationRow = $serviceStationModel->field('id, service_name, free_withdrawal_limit, level, zsb_type') // Added level for consistency if layout needs it
                                                 ->where(['id' => $stationId, 'status' => 1])
                                                 ->find();
        if (!$serviceStationRow) {
            $this->error("服务站信息无效或未审核");
        }

        // 检查是否为招就办用户，招就办用户不允许查看免手续费额度
        if ($serviceStationRow['zsb_type'] == 2) {
            $this->error("招就办用户不支持提现功能，如有疑问请联系客服");
        }

        // Convert limit from cents (分) to yuan (元) for display
        $freeLimitValue = isset($serviceStationRow['free_withdrawal_limit']) ? floatval($serviceStationRow['free_withdrawal_limit']) : 0.0;

        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('freeLimitValue', $freeLimitValue); // Use new variable name
        $this->assign('userRow', $this->userRow); // Needed for layout
        $this->assign('levelList', $serviceStationModel->level); // Assign level list if layout needs it

        $this->display(); // Renders free_limit_info.html by convention
    }

    /**
     * 招就办价格配置页面
     */
    public function price_config()
    {
        // 权限验证：只有服务站用户才能配置价格
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG);
            return;
        }

        // 获取当前服务站信息
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'zsb_type' => 1, 'status' => 1])
            ->find();

        if (!$serviceStationRow) {
            $this->error('服务站信息不存在或未审核通过');
        }

        // 获取该服务站下的招就办列表
        $zsbList = D("ServiceStation")
            ->where([
                'zsb_ref_station' => $this->userRow['self_service_station_id'],
                'zsb_type' => 2,
                'status' => 1
            ])
            ->field('id, service_name, contract_name, mobile')
            ->select();

        // 获取可用岗位列表
        $postList = D("ProjectPost")
            ->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.status' => 1, 'pp.is_zsb_available' => 1])
            ->field('pp.id, pp.job_name, pp.service_price, p.name as project_name')
            ->select();

        $this->assign('zsbList', $zsbList ?: []);
        $this->assign('postList', $postList ?: []);
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('userRow', $this->userRow);

        $this->display('price-config');
    }

    /**
     * 获取招就办岗位价格列表 (AJAX)
     */
    public function getPriceListAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 权限验证：只有服务站用户才能获取价格列表
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG, true);
            return;
        }

        $zsbId = I('get.zsb_id', 0, 'intval');
        $page = I('get.page', 1, 'intval');
        $limit = I('get.limit', 20, 'intval');

        if (!$zsbId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办ID不能为空']);
        }

        // 验证招就办是否属于当前服务站
        $zsbInfo = D("ServiceStation")->where([
            'id' => $zsbId,
            'zsb_ref_station' => $this->userRow['self_service_station_id'],
            'zsb_type' => 2
        ])->find();

        if (!$zsbInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办不存在或无权限']);
        }

        $priceModel = D("ZsbPostPrice");
        $result = $priceModel->getZsbPostPriceList($zsbId, [], $page, $limit);

        $this->ajaxReturn([
            'status' => 1,
            'data' => $result
        ]);
    }

    /**
     * 获取项目列表 (AJAX)
     */
    public function getProjectListAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 权限验证：只有服务站用户才能获取项目列表
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG, true);
            return;
        }

        $projectModel = D("Project");
        $projectList = $projectModel->where(['status' => 1])
            ->field('id, name, category')
            ->order('id DESC')
            ->select();

        $this->ajaxReturn([
            'status' => 1,
            'data' => $projectList ?: []
        ]);
    }

    /**
     * 根据项目ID获取岗位列表 (AJAX)
     */
    public function getPostListByProjectAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 权限验证：只有服务站用户才能获取岗位列表
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG, true);
            return;
        }

        $projectId = I('get.project_id', 0, 'intval');
        if (!$projectId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '项目ID不能为空']);
        }

        $postModel = D("ProjectPost");
        $postList = $postModel->where([
            'project_id' => $projectId,
            'status' => 1,
            'is_zsb_available' => 1
        ])
        ->field('id, job_name, service_price')
        ->order('id DESC')
        ->select();

        $this->ajaxReturn([
            'status' => 1,
            'data' => $postList ?: []
        ]);
    }

    /**
     * 获取单个岗位的价格配置 (AJAX)
     */
    public function getPostPriceAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 权限验证：只有服务站用户才能获取价格配置
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG, true);
            return;
        }

        $zsbId = I('get.zsb_id', 0, 'intval');
        $postId = I('get.post_id', 0, 'intval');

        if (!$zsbId || !$postId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办ID和岗位ID不能为空']);
        }

        // 验证招就办是否属于当前服务站
        $zsbInfo = D("ServiceStation")->where([
            'id' => $zsbId,
            'zsb_ref_station' => $this->userRow['self_service_station_id'],
            'zsb_type' => 2
        ])->find();

        if (!$zsbInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办不存在或无权限']);
        }

        // 使用新的方法获取完整的价格信息（包括基准成本价和平台服务费）
        $priceModel = D("ZsbPostPrice");

        // 初始化系统配置
        $priceModel->initSystemConfig();

        $priceInfo = $priceModel->getPostPriceInfo($zsbId, $postId, true);

        if (!$priceInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '岗位不存在或不可用']);
        }

        $this->ajaxReturn([
            'status' => 1,
            'data' => $priceInfo
        ]);
    }

    /**
     * 获取岗位基准成本价 (AJAX)
     */
    public function getBaseCostAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 权限验证：只有服务站用户才能查看基准成本价
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG, true);
            return;
        }

        $postId = I('get.post_id', 0, 'intval');

        if (!$postId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '岗位ID不能为空']);
        }

        $priceModel = D("ZsbPostPrice");
        $baseCost = $priceModel->getBaseCost($postId);
        // 基准成本价数据库存储单位就是元，不需要转换

        $this->ajaxReturn([
            'status' => 1,
            'data' => [
                'post_id' => $postId,
                'base_cost' => $baseCost,
                'has_base_cost' => $baseCost > 0 ? 1 : 0
            ]
        ]);
    }

    /**
     * 设置招就办岗位价格 (AJAX)
     */
    public function setPriceAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 权限验证：只有服务站用户才能设置价格
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG, true);
            return;
        }

        // 注意：前端传入的价格单位为元，Model层会自动转换为分存储
        $data = [
            'zsb_id' => I('post.zsb_id', 0, 'intval'),
            'post_id' => I('post.post_id', 0, 'intval'),
            'cost_price' => I('post.cost_price', 0, 'intval'), // 元单位
            'sale_price' => I('post.sale_price', 0, 'intval'), // 元单位
            'status' => I('post.status', 1, 'intval')
        ];

        // 验证招就办是否属于当前服务站
        $zsbInfo = D("ServiceStation")->where([
            'id' => $data['zsb_id'],
            'zsb_ref_station' => $this->userRow['self_service_station_id'],
            'zsb_type' => 2
        ])->find();

        if (!$zsbInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办不存在或无权限']);
        }

        $priceModel = D("ZsbPostPrice");

        // 验证数据
        if (!$priceModel->validatePriceConfig($data)) {
            $this->ajaxReturn(['status' => 0, 'msg' => $priceModel->getError()]);
        }

        // 设置价格
        $result = $priceModel->setZsbPostPrice($data);
        if ($result) {
            $this->ajaxReturn(['status' => 1, 'msg' => '价格设置成功']);
        } else {
            $this->ajaxReturn(['status' => 0, 'msg' => $priceModel->getError() ?: '价格设置失败']);
        }
    }

    /**
     * 招就办专用首页
     */
    private function zsbIndex()
    {
        $type = I('get.type', 0);
        $kwd = I('get.kwd', '');
        $qualification = I('get.qualification', 0);

        // 获取当前招就办信息
        $zsbInfo = D("ServiceStation")->where([
            'id' => $this->userRow['self_service_station_id'],
            'zsb_type' => 2,
            'status' => 1
        ])->find();

        if (!$zsbInfo) {
            $this->error('招就办信息不存在或未审核通过');
        }

        // 获取招就办可见的岗位（已配置价格的岗位）
        $priceModel = D("ZsbPostPrice");
        $priceList = $priceModel->where([
            'zsb_id' => $this->userRow['self_service_station_id'],
            'status' => 1
        ])->select();

        $postIds = array_column($priceList, 'post_id');
        $priceMap = [];
        foreach ($priceList as $price) {
            $priceMap[$price['post_id']] = $price;
        }

        // 构建岗位查询条件
        $where = ['pp.status' => 1];
        if (!empty($postIds)) {
            $where['pp.id'] = ['in', $postIds];
        } else {
            // 如果没有配置价格的岗位，显示空列表
            $where['pp.id'] = -1;
        }

        if ($qualification > 0) {
            if ($qualification == 4) {
                $where['pp.qualification'] = ['egt', 4];
            } else {
                $where['pp.qualification'] = $qualification;
            }
        }

        if ($type > 0) {
            $categoryWhere = ['category' => $type];
            if ($type == 7) {
                $categoryWhere = ['category' => ['in', [3, 5, 7]]];
            }
            $projectArrId = D("Project")->where($categoryWhere)->getField('id', true);
            if ($projectArrId) {
                $where['pp.project_id'] = ['in', $projectArrId];
            } else {
                $where['pp.project_id'] = -1;
            }
        }

        if (!empty($kwd)) {
            $where['pp.job_name'] = ['like', '%' . $kwd . '%'];
        }

        $obj = D("ProjectPost");
        // 为count查询转换条件（去掉表别名）
        $countWhere = [];
        foreach ($where as $key => $value) {
            $newKey = str_replace('pp.', '', $key);
            $countWhere[$newKey] = $value;
        }
        $count = $obj->where($countWhere)->count();
        $page = $this->page($count, 100);
        $list = $obj->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->field('pp.*, p.name as project_name, p.category')
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->order('pp.is_top desc,pp.top_time desc,pp.id desc')
            ->select();

        // 为岗位列表添加招就办价格信息
        if ($list) {
            $priceModel = D("ZsbPostPrice");
            foreach ($list as &$item) {
                if (isset($priceMap[$item['id']])) {
                    // 转换价格单位为元
                    $priceData = $priceModel->processPriceDataForDisplay($priceMap[$item['id']]);
                    $item['zsb_cost_price'] = $priceData['cost_price'];
                    $item['zsb_sale_price'] = $priceData['sale_price'];
                    $item['zsb_commission'] = $priceData['commission'];
                }
            }
            // 取消引用，防止后续操作意外修改$list中的最后一个元素
            unset($item);

            // 由于已经通过JOIN获取了项目信息，不需要再次查询
            $projectList = [];
            foreach ($list as $item) {
                if (!isset($projectList[$item['project_id']])) {
                    $projectList[$item['project_id']] = [
                        'name' => $item['project_name'],
                        'category' => $item['category']
                    ];
                }
            }
            $this->assign('projectList', $projectList);
        }

        $this->assign('qualificationList', $obj->qualification);
        $this->assign('sexList', $obj->sex);
        $this->assign('categoryList', D("Project")->category);
        $this->assign('list', $list);
        $this->assign('isZsb', true); // 标识为招就办用户
        $this->assign('zsbInfo', $zsbInfo);

        $n = I('get.n', 0);
        if ($page->Current_page > 1 || $n == 1) {
            $this->display('list-index');
            exit;
        }

        $this->assign("page", $page);
        $this->assign('serviceStationRow', $zsbInfo);
        $this->assign('userRow', $this->userRow);

        // 招就办统计数据
        $startTime = strtotime(date("Y-m-d 00:00:00", time()));
        $endTime = strtotime(date("Y-m-d 23:59:59", time()));

        //今日简历
        $jobCount = D("UserJob")
            ->where(['service_station_id' => $this->userRow['self_service_station_id']])
            ->where(['create_time' => ['between', [$startTime, $endTime]]])
            ->count();
        $this->assign('jobCount', $jobCount ?: 0);

        //所有简历
        $alljobCount = D("UserJob")
            ->where(['service_station_id' => $this->userRow['self_service_station_id']])
            ->count();
        $this->assign('alljobCount', $alljobCount ?: 0);

        // 可用岗位数量
        $availablePostCount = count($postIds);
        $this->assign('availablePostCount', $availablePostCount);

        $this->assign('levelList', D("ServiceStation")->level);

        // 招就办不显示价格变化通知（只有服务站才显示）
        $hasNotify = false;
        $notifyTime = null;



        $this->assign('hasNotify', $hasNotify);
        $this->assign('notifyTime', $notifyTime);
        $this->display();
    }

    /**
     * 批量设置招就办岗位价格 (AJAX)
     */
    public function batchSetPriceAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 权限验证：只有服务站用户才能批量设置价格
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG, true);
            return;
        }

        $zsbId = I('post.zsb_id', 0, 'intval');
        $priceList = I('post.price_list', [], '');

        if (!$zsbId || empty($priceList)) {
            $this->ajaxReturn(['status' => 0, 'msg' => '参数不完整']);
        }

        // 验证招就办是否属于当前服务站
        $serviceStationModel = D("ServiceStation");
        if (!$serviceStationModel->validateZsbBelongsToStation($zsbId, $this->userRow['self_service_station_id'])) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办不存在或无权限']);
        }

        $priceModel = D("ZsbPostPrice");
        $result = $priceModel->batchSetPrice($zsbId, $priceList);

        if ($result) {
            $this->ajaxReturn(['status' => 1, 'msg' => '批量设置成功']);
        } else {
            $this->ajaxReturn(['status' => 0, 'msg' => $priceModel->getError() ?: '批量设置失败']);
        }
    }

    /**
     * 获取招就办下所有岗位及价格配置信息 (AJAX) - 用于卡片式展示
     */
    public function getAllPostsWithPriceAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 权限验证：只有服务站用户才能获取岗位列表
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG, true);
            return;
        }

        $zsbId = I('get.zsb_id', 0, 'intval');
        $projectId = I('get.project_id', 0, 'intval'); // 可选的项目筛选
        $keyword = I('get.keyword', '', 'trim'); // 可选的关键词搜索

        if (!$zsbId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办ID不能为空']);
        }

        // 验证招就办是否属于当前服务站
        $zsbInfo = D("ServiceStation")->where([
            'id' => $zsbId,
            'zsb_ref_station' => $this->userRow['self_service_station_id'],
            'zsb_type' => 2
        ])->find();

        if (!$zsbInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办不存在或无权限']);
        }

        // 构建岗位查询条件
        $postWhere = [
            'pp.status' => 1,
            'pp.is_zsb_available' => 1
        ];

        if ($projectId) {
            $postWhere['pp.project_id'] = $projectId;
        }

        if ($keyword) {
            $postWhere['pp.job_name'] = ['like', '%' . $keyword . '%'];
        }

        // 获取岗位列表及项目信息
        $postList = D("ProjectPost")
            ->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where($postWhere)
            ->field('pp.*, p.name as project_name, p.category')
            ->order('pp.project_id ASC, pp.id DESC')
            ->select();

        if (!$postList) {
            $this->ajaxReturn([
                'status' => 1,
                'data' => [
                    'posts' => [],
                    'projects' => []
                ]
            ]);
        }

        // 使用新的方法获取完整的价格信息（包括基准成本价和平台服务费）
        $priceModel = D("ZsbPostPrice");

        // 初始化系统配置
        $priceModel->initSystemConfig();

        // 组装数据
        $result = [];
        foreach ($postList as $post) {
            // 获取完整的价格信息
            $priceInfo = $priceModel->getPostPriceInfo($zsbId, $post['id'], true);

            if ($priceInfo) {
                $result[] = [
                    'id' => $post['id'],
                    'job_name' => $post['job_name'],
                    'service_price' => $post['service_price'], // 数据库存储单位就是元，直接使用
                    'max_price' => $post['max_price'], // 数据库存储单位就是元，直接使用
                    'project_id' => $post['project_id'],
                    'project_name' => $post['project_name'],
                    'category' => $post['category'],
                    'is_configured' => $priceInfo['is_configured'],
                    'cost_price' => $priceInfo['cost_price'],
                    'sale_price' => $priceInfo['sale_price'],
                    'commission' => $priceInfo['commission'],
                    'platform_fee' => $priceInfo['platform_fee'],
                    'base_cost' => $priceInfo['base_cost'],
                    'price_status' => $priceInfo['status']
                ];
            }
        }

        // 获取项目列表用于筛选
        $projectIds = array_unique(array_column($postList, 'project_id'));
        $projects = D("Project")->where([
            'id' => ['in', $projectIds],
            'status' => 1
        ])->field('id, name, category')->select();

        $this->ajaxReturn([
            'status' => 1,
            'data' => [
                'posts' => $result,
                'projects' => $projects ?: []
            ]
        ]);
    }

    /**
     * 获取招就办可用岗位列表 (AJAX)
     */
    public function getAvailablePostsAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 权限验证：只有服务站用户才能获取可用岗位列表
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG, true);
            return;
        }

        $zsbId = I('get.zsb_id', 0, 'intval');

        if (!$zsbId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办ID不能为空']);
        }

        // 验证招就办是否属于当前服务站
        $serviceStationModel = D("ServiceStation");
        if (!$serviceStationModel->validateZsbBelongsToStation($zsbId, $this->userRow['self_service_station_id'])) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办不存在或无权限']);
        }

        $priceModel = D("ZsbPostPrice");
        $availablePosts = $priceModel->getAvailablePostsForZsb($zsbId);

        $this->ajaxReturn([
            'status' => 1,
            'data' => $availablePosts ?: []
        ]);
    }

    /**
     * 删除招就办岗位价格配置 (AJAX)
     */
    public function deletePriceAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 权限验证：只有服务站用户才能删除价格配置
        if (!$this->validateUserPermission(self::PERMISSION_PRICE_CONFIG)) {
            $this->handlePermissionDenied(self::PERMISSION_PRICE_CONFIG, true);
            return;
        }

        $zsbId = I('post.zsb_id', 0, 'intval');
        $postId = I('post.post_id', 0, 'intval');

        if (!$zsbId || !$postId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '参数不完整']);
        }

        // 验证招就办是否属于当前服务站
        $serviceStationModel = D("ServiceStation");
        if (!$serviceStationModel->validateZsbBelongsToStation($zsbId, $this->userRow['self_service_station_id'])) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办不存在或无权限']);
        }

        $priceModel = D("ZsbPostPrice");
        $result = $priceModel->where([
            'zsb_id' => $zsbId,
            'post_id' => $postId
        ])->delete();

        if ($result) {
            $this->ajaxReturn(['status' => 1, 'msg' => '删除成功']);
        } else {
            $this->ajaxReturn(['status' => 0, 'msg' => '删除失败']);
        }
    }

    /**
     * 权限类型常量定义
     */
    const PERMISSION_ZJB_MANAGE = 'zjb_manage';           // 招就办管理权限
    const PERMISSION_PRICE_CONFIG = 'price_config';       // 价格配置权限
    const PERMISSION_ZJB_REGISTER = 'zjb_register';       // 注册招就办权限
    const PERMISSION_ZJB_VIEW = 'zsb_view';               // 招就办查看权限
    const PERMISSION_STATION_ONLY = 'station_only';       // 仅服务站权限

    /**
     * 验证当前用户权限
     * @param string $action 操作类型
     * @return bool
     */
    private function validateUserPermission($action = 'default')
    {
        // 基础权限验证 - 必须是已审核通过的用户
        if ($this->userRow['is_service_station'] != 2) {
            return false;
        }

        $currentStation = D("ServiceStation")->where([
            'id' => $this->userRow['self_service_station_id'],
            'status' => 1
        ])->find();

        if (!$currentStation) {
            return false;
        }

        // 根据操作类型进行不同的权限验证
        switch ($action) {
            case self::PERMISSION_ZJB_MANAGE:
            case self::PERMISSION_PRICE_CONFIG:
            case self::PERMISSION_ZJB_REGISTER:
            case self::PERMISSION_STATION_ONLY:
                // 只有服务站可以执行管理操作
                return $currentStation['zsb_type'] == 1;

            case self::PERMISSION_ZJB_VIEW:
                // 招就办只能查看自己的数据
                return $currentStation['zsb_type'] == 2;

            default:
                return true;
        }
    }

    /**
     * 权限检查失败时的统一处理
     * @param string $action 操作类型
     * @param bool $isAjax 是否为AJAX请求
     */
    private function handlePermissionDenied($action = '', $isAjax = false)
    {
        $message = '您没有权限执行此操作';

        // 根据权限类型提供更具体的错误信息
        switch ($action) {
            case self::PERMISSION_ZJB_MANAGE:
                $message = '只有服务站用户才能管理招就办';
                break;
            case self::PERMISSION_PRICE_CONFIG:
                $message = '只有服务站用户才能配置价格';
                break;
            case self::PERMISSION_ZJB_REGISTER:
                $message = '只有服务站用户才能注册招就办';
                break;
            case self::PERMISSION_STATION_ONLY:
                $message = '此功能仅限服务站用户使用';
                break;
        }

        if ($isAjax) {
            $this->ajaxReturn(['status' => 0, 'msg' => $message]);
        } else {
            $this->error($message);
        }
    }

    /**
     * 获取当前平台费率（Station模块API）
     */
    public function get_platform_rate()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $rate = D('ZsbPostPrice')->getPlatformRate();

        $this->ajaxReturn([
            'status' => 1,
            'rate' => $rate,
            'rate_percent' => ($rate * 100) . '%'
        ]);
    }

    /**
     * 获取服务站价格变化通知状态（Station模块API）
     */
    public function get_price_change_notify_status()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 获取当前服务站ID
        $stationId = $this->userRow['self_service_station_id'];

        if (!$stationId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '服务站ID不能为空']);
        }

        try {
            // 获取通知状态
            $stationInfo = M('service_station')->where([
                'id' => $stationId
            ])->field('price_change_notify,zsb_type')->find();

            if (!$stationInfo) {
                $this->ajaxReturn(['status' => 0, 'msg' => '服务站不存在']);
                return;
            }

            // 检查是否显示通知
            $hasNotify = false;

            // 只有服务站类型才可能显示通知
            if ($stationInfo['zsb_type'] == 1 && $stationInfo['price_change_notify']) {
                // 检查是否被临时清除（会话中记录）
                $sessionKey = 'price_notify_temp_cleared_' . $stationId;
                $tempCleared = session($sessionKey);

                if (!$tempCleared) {
                    // 没有被临时清除，显示通知
                    $hasNotify = true;
                }
            }

            // 获取当前平台费率
            $currentRate = D('ZsbPostPrice')->getPlatformRate();

            // 获取最新的费率变更时间
            $changeTime = '';
            $lastLog = D('PlatformRateLog')->where(['execute_status' => 2])
                ->order('execute_time DESC')
                ->find();

            if ($lastLog && $lastLog['execute_time']) {
                $changeTime = date('Y-m-d H:i', $lastLog['execute_time']);
            }

            $this->ajaxReturn([
                'status' => 1,
                'has_notify' => $hasNotify,
                'current_rate' => $currentRate,
                'rate_percent' => ($currentRate * 100) . '%',
                'change_time' => $changeTime,
                'debug' => '查询成功'
            ]);

        } catch (\Exception $e) {
            $this->ajaxReturn([
                'status' => 0,
                'msg' => '查询失败: ' . $e->getMessage(),
                'debug' => $e->getMessage()
            ]);
        }
    }

    /**
     * 清除服务站价格变化通知
     */
    public function clear_price_change_notify()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $stationId = I('post.station_id', 0, 'intval');
        $permanent = I('post.permanent', 0, 'intval'); // 是否永久清除

        if (!$stationId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '服务站ID不能为空']);
        }

        // 验证当前用户是否有权限操作该服务站
        if ($stationId != $this->userRow['self_service_station_id']) {
            $this->ajaxReturn(['status' => 0, 'msg' => '无权限操作该服务站']);
        }

        if ($permanent) {
            // 永久清除：清除数据库中的通知标记
            $updateData = [
                'price_change_notify' => 0
            ];

            $result = M('service_station')->where([
                'id' => $stationId
            ])->save($updateData);

            if ($result !== false) {
                $this->ajaxReturn(['status' => 1, 'msg' => '通知已永久清除']);
            } else {
                $this->ajaxReturn(['status' => 0, 'msg' => '永久清除失败']);
            }
        } else {
            // 临时清除：使用会话记录，不修改数据库
            $sessionKey = 'price_notify_temp_cleared_' . $stationId;
            session($sessionKey, time()); // 记录临时清除的时间戳

            $this->ajaxReturn(['status' => 1, 'msg' => '通知已临时清除']);
        }
    }

    /**
     * 获取招就办余额信息
     */
    public function getZjbBalance()
    {
        // 验证招就办权限
        $currentStation = D("ServiceStation")->where([
            'id' => $this->userRow['self_service_station_id'],
            'status' => 1
        ])->find();

        if (!$currentStation || $currentStation['zsb_type'] != 2) {
            $this->ajaxReturn(['status' => 0, 'msg' => '权限不足，只有招就办用户可以查看余额']);
        }

        $stationId = $this->userRow['self_service_station_id'];
        $stationInfo = D("ServiceStation")->where(['id' => $stationId])->find();

        if (!$stationInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办信息不存在']);
        }

        $data = [
            'total_price' => number_format(($stationInfo['total_price'] ?: 0) / 100, 2),
            'available_price' => number_format(($stationInfo['price'] ?: 0) / 100, 2),
            'freeze_price' => number_format(($stationInfo['freeze_price'] ?: 0) / 100, 2)
        ];

        $this->ajaxReturn(['status' => 1, 'data' => $data]);
    }

    /**
     * 冻结招就办余额
     */
    public function freezeZjbBalance()
    {
        // 验证招就办权限
        $currentStation = D("ServiceStation")->where([
            'id' => $this->userRow['self_service_station_id'],
            'status' => 1
        ])->find();

        if (!$currentStation || $currentStation['zsb_type'] != 2) {
            $this->ajaxReturn(['status' => 0, 'msg' => '权限不足']);
        }

        $amount = I('post.amount', 0, 'intval'); // 冻结金额（分）
        if ($amount <= 0) {
            $this->ajaxReturn(['status' => 0, 'msg' => '冻结金额必须大于0']);
        }

        $stationId = $this->userRow['self_service_station_id'];
        $stationInfo = D("ServiceStation")->where(['id' => $stationId])->find();

        if (!$stationInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办信息不存在']);
        }

        // 检查可用余额是否足够
        if ($stationInfo['price'] < $amount) {
            $this->ajaxReturn(['status' => 0, 'msg' => '可用余额不足']);
        }

        // 执行冻结操作
        $result = D("ServiceStation")->where(['id' => $stationId])->save([
            'price' => $stationInfo['price'] - $amount,
            'freeze_price' => $stationInfo['freeze_price'] + $amount
        ]);

        if ($result !== false) {
            $this->ajaxReturn(['status' => 1, 'msg' => '余额冻结成功']);
        } else {
            $this->ajaxReturn(['status' => 0, 'msg' => '余额冻结失败']);
        }
    }

    /**
     * 解冻招就办余额
     */
    public function unfreezeZjbBalance()
    {
        // 验证招就办权限
        $currentStation = D("ServiceStation")->where([
            'id' => $this->userRow['self_service_station_id'],
            'status' => 1
        ])->find();

        if (!$currentStation || $currentStation['zsb_type'] != 2) {
            $this->ajaxReturn(['status' => 0, 'msg' => '权限不足']);
        }

        $amount = I('post.amount', 0, 'intval'); // 解冻金额（分）
        if ($amount <= 0) {
            $this->ajaxReturn(['status' => 0, 'msg' => '解冻金额必须大于0']);
        }

        $stationId = $this->userRow['self_service_station_id'];
        $stationInfo = D("ServiceStation")->where(['id' => $stationId])->find();

        if (!$stationInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办信息不存在']);
        }

        // 检查冻结余额是否足够
        if ($stationInfo['freeze_price'] < $amount) {
            $this->ajaxReturn(['status' => 0, 'msg' => '冻结余额不足']);
        }

        // 执行解冻操作
        $result = D("ServiceStation")->where(['id' => $stationId])->save([
            'price' => $stationInfo['price'] + $amount,
            'freeze_price' => $stationInfo['freeze_price'] - $amount
        ]);

        if ($result !== false) {
            $this->ajaxReturn(['status' => 1, 'msg' => '余额解冻成功']);
        } else {
            $this->ajaxReturn(['status' => 0, 'msg' => '余额解冻失败']);
        }
    }

    /**
     * 代收计算器页面
     */
    public function calculator()
    {
        // ThinkPHP路径参数接收方式
        $postname = I('param.postname', '');
        $price = I('param.price', 0, 'intval');
        $mincharge = I('param.mincharge', 0, 'intval');
        $maxcharge = I('param.maxcharge', 0, 'intval');

        // 如果路径参数为空，尝试GET参数
        if (empty($postname)) {
            $postname = I('get.postname', '');
        }
        if (empty($price)) {
            $price = I('get.price', 0, 'intval');
        }

        if (empty($mincharge)) {
            $mincharge = I('get.mincharge', 0, 'intval');
        }

        if (empty($maxcharge)) {
            $maxcharge = I('get.maxcharge', 0, 'intval');
        }

        // 传递参数到模板
        $this->assign('postname', $postname);
        $this->assign('price', $price);
        $this->assign('mincharge', $mincharge);
        $this->assign('maxcharge', $maxcharge);

        $this->display();
    }

    /**
     * 获取可切换的服务站列表（仅限特定用户）
     */
    public function get_switchable_stations()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 检查用户权限 - 仅限userid=2、68
        if (!in_array($this->userRow['id'], [2, 68])) {
            $this->ajaxReturn(['status' => 0, 'msg' => '无权限访问']);
        }

        try {
            // 获取所有可用的服务站（包括服务站和招就办）
            $stations = D("ServiceStation")->where([
                'status' => 1  // 只获取审核通过的服务站
            ])->field('id,service_name,zsb_type,users_id')->order('zsb_type ASC, id ASC')->select();

            $stationList = [];
            foreach ($stations as $station) {
                // 获取服务站对应用户的头像
                $userInfo = D("User")->where(['id' => $station['users_id']])->field('headimgurl')->find();
                $avatar = ($userInfo && $userInfo['headimgurl']) ? $userInfo['headimgurl'] : '/static/stations/images/default-avatar.svg';

                $stationList[] = [
                    'id' => $station['id'],
                    'name' => $station['service_name'],
                    'type' => $station['zsb_type'] == 1 ? '服务站' : '招就办',
                    'type_text' => $station['zsb_type'] == 1 ? '管理员' : '创建者',
                    'avatar' => $avatar,
                    'is_current' => $station['id'] == $this->userRow['self_service_station_id']
                ];
            }

            $this->ajaxReturn([
                'status' => 1,
                'data' => $stationList,
                'current_station_id' => $this->userRow['self_service_station_id']
            ]);

        } catch (Exception $e) {
            \Think\Log::write('获取可切换服务站列表失败: ' . $e->getMessage(), 'ERROR');
            $this->ajaxReturn(['status' => 0, 'msg' => '获取服务站列表失败']);
        }
    }

    /**
     * 切换服务站（仅限特定用户）
     */
    public function switch_station()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 检查用户权限 - 仅限userid=2、68
        if (!in_array($this->userRow['id'], [2, 68])) {
            $this->ajaxReturn(['status' => 0, 'msg' => '无权限访问']);
        }

        $stationId = I('post.station_id', 0);
        if (!$stationId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '服务站ID不能为空']);
        }

        try {
            // 验证目标服务站是否存在且可用
            $targetStation = D("ServiceStation")->where([
                'id' => $stationId,
                'status' => 1
            ])->find();

            if (!$targetStation) {
                $this->ajaxReturn(['status' => 0, 'msg' => '目标服务站不存在或未审核通过']);
            }

            // 更新用户的服务站关联
            $result = D("User")->where(['id' => $this->userRow['id']])->save([
                'self_service_station_id' => $stationId,
                'service_station_id' => $stationId  // 同时更新service_station_id字段
            ]);

            if ($result !== false) {
                // 记录切换日志
                \Think\Log::write("用户{$this->userRow['id']}切换服务站: {$this->userRow['self_service_station_id']} -> {$stationId}", 'INFO');

                $this->ajaxReturn([
                    'status' => 1,
                    'msg' => '切换成功',
                    'station_name' => $targetStation['service_name']
                ]);
            } else {
                $this->ajaxReturn(['status' => 0, 'msg' => '切换失败，请重试']);
            }

        } catch (Exception $e) {
            \Think\Log::write('切换服务站失败: ' . $e->getMessage(), 'ERROR');
            $this->ajaxReturn(['status' => 0, 'msg' => '切换失败，系统错误']);
        }
    }

}
