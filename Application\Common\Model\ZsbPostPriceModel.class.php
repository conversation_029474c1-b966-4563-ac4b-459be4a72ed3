<?php
namespace Common\Model;

use Think\Model;

/**
 * 招就办岗位价格配置模型
 */
class ZsbPostPriceModel extends Model
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_UPDATE, 'function'],
    ];

    /**
     * 状态定义
     */
    public $status = [
        '0' => ['text' => '不开放', 'style' => 'danger'],
        '1' => ['text' => '开放', 'style' => 'success'],
    ];

    /**
     * 获取招就办岗位价格列表
     * @param int $zsbId 招就办ID
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param bool $convertToYuan 是否转换为元单位显示，默认true
     * @return array
     */
    public function getZsbPostPriceList($zsbId, $condition = [], $page = 1, $limit = 20, $convertToYuan = true)
    {
        // 构建岗位查询条件
        $postWhere = ['pp.status' => 1, 'pp.is_zsb_available' => 1];
        if (!empty($condition)) {
            $postWhere = array_merge($postWhere, $condition);
        }

        // 获取所有可用岗位的总数
        $postModel = D('ProjectPost');
        $count = $postModel->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where($postWhere)
            ->count();

        $offset = ($page - 1) * $limit;

        // 获取所有可用岗位，左连接价格配置表（移除项目身份成本表的JOIN以避免重复）
        $list = $postModel->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->join('LEFT JOIN __ZSB_POST_PRICE__ zpp ON pp.id = zpp.post_id AND zpp.zsb_id = ' . intval($zsbId))
            ->field('pp.id as post_id, pp.job_name, pp.service_price as post_service_price, pp.max_price, p.name as project_name,
                     COALESCE(zpp.id, 0) as price_config_id,
                     COALESCE(zpp.cost_price, 0) as cost_price,
                     COALESCE(zpp.sale_price, 0) as sale_price,
                     COALESCE(zpp.commission, 0) as commission,
                     COALESCE(zpp.status, 0) as status,
                     CASE WHEN zpp.id IS NOT NULL THEN 1 ELSE 0 END as is_configured')
            ->where($postWhere)
            ->limit($offset, $limit)
            ->order('pp.id DESC')
            ->select();

        // 转换价格单位为元并计算平台服务费
        if (!empty($list)) {
            foreach ($list as &$item) {
                // 单独获取基准成本价（避免JOIN导致的重复问题）
                $item['base_cost'] = $this->getBaseCost($item['post_id']);

                // service_price和max_price数据库存储单位就是元，不需要转换
                $item['service_price'] = $item['post_service_price'];
                // max_price保持原值

                if ($item['is_configured'] && $convertToYuan) {
                    $item = $this->processPriceDataForDisplay($item);

                    // 计算平台服务费（用于显示）
                    if ($item['sale_price'] > 0) {
                        $salePriceCents = $this->convertYuanToCents($item['sale_price']);
                        $platformFeeCents = $this->calculatePlatformFee($salePriceCents, $item['post_service_price']);
                        $item['platform_fee'] = $this->convertCentsToYuan($platformFeeCents);
                    } else {
                        $item['platform_fee'] = 0;
                    }
                } else {
                    $item['platform_fee'] = 0;
                }
            }
        }

        return [
            'list' => $list ?: [],
            'total' => $count,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($count / $limit)
        ];
    }

    /**
     * 元转分
     * @param float $yuan 元金额
     * @return int 分金额
     */
    public function convertYuanToCents($yuan)
    {
        return intval($yuan * 100);
    }

    /**
     * 分转元
     * @param int $cents 分金额
     * @return float 元金额
     */
    public function convertCentsToYuan($cents)
    {
        return round($cents / 100, 2);
    }

    /**
     * 处理价格数据单位转换（元转分）
     * @param array $data 包含价格的数据数组
     * @return array 转换后的数据
     */
    public function processPriceDataForStorage($data)
    {
        if (isset($data['cost_price'])) {
            $data['cost_price'] = $this->convertYuanToCents($data['cost_price']);
        }
        if (isset($data['sale_price'])) {
            $data['sale_price'] = $this->convertYuanToCents($data['sale_price']);
        }
        return $data;
    }

    /**
     * 处理价格数据单位转换（分转元）
     * @param array $data 包含价格的数据数组
     * @return array 转换后的数据
     */
    public function processPriceDataForDisplay($data)
    {
        if (isset($data['cost_price'])) {
            $data['cost_price'] = $this->convertCentsToYuan($data['cost_price']);
        }
        if (isset($data['sale_price'])) {
            $data['sale_price'] = $this->convertCentsToYuan($data['sale_price']);
        }
        if (isset($data['commission'])) {
            // DEBUG: 记录佣金转换前后的值
            $originalCommission = $data['commission'];
            // 佣金是decimal(10,2)类型，但存储的是分单位的整数
            $data['commission'] = $this->convertCentsToYuan($data['commission']);
            \Think\Log::write('佣金转换: 原值(分)=' . $originalCommission . ' => 转换后(元)=' . $data['commission'], 'INFO');
        }
        return $data;
    }

    /**
     * 设置招就办岗位价格
     * @param array $data 价格数据（元单位）
     * @return bool|int
     */
    public function setZsbPostPrice($data)
    {
        // 数据验证
        if (empty($data['zsb_id']) || empty($data['post_id'])) {
            $this->error = '招就办ID和岗位ID不能为空';
            return false;
        }

        // DEBUG: 记录输入的价格数据（元单位）
        \Think\Log::write('设置价格 - 输入数据(元): cost_price=' . $data['cost_price'] . ', sale_price=' . $data['sale_price'], 'INFO');

        // 获取岗位信息（用于计算平台服务费）
        $postInfo = D('ProjectPost')->where([
            'id' => $data['post_id'],
            'status' => 1,
            'is_zsb_available' => 1
        ])->find();

        if (!$postInfo) {
            $this->error = '岗位不存在或不可用';
            return false;
        }

        // 转换单位：元转分
        $originalData = $data;
        $data = $this->processPriceDataForStorage($data);

        // DEBUG: 记录转换后的价格数据（分单位）
        \Think\Log::write('设置价格 - 转换后数据(分): cost_price=' . $data['cost_price'] . ', sale_price=' . $data['sale_price'], 'INFO');

        if ($data['sale_price'] < $data['cost_price']) {
            $this->error = '对外价不能低于成本价';
            return false;
        }

        // 使用新的佣金计算逻辑
        $servicePrice = $postInfo['service_price']; // 最低报价（元单位）

        // 计算平台服务费（分单位）
        $platformFee = $this->calculatePlatformFee($data['sale_price'], $servicePrice);

        // 计算招就办佣金（分单位）
        $commission = $this->calculateCommission($data['sale_price'], $data['cost_price'], $platformFee);

        // DEBUG: 记录计算过程
        \Think\Log::write('佣金计算 - 对外价(分):' . $data['sale_price'] . ', 成本价(分):' . $data['cost_price'] . ', 平台费(分):' . $platformFee . ', 佣金(分):' . $commission, 'INFO');

        // 检查佣金值是否超出decimal(10,2)的存储范围
        if ($commission > 99999999) {
            \Think\Log::write('警告：计算的佣金值（' . $commission . '）超出decimal(10,2)最大存储范围，将被截断', 'WARN');
            $data['commission'] = 99999999;
        } else {
            $data['commission'] = $commission;
        }

        // DEBUG: 记录最终的佣金值
        \Think\Log::write('最终存储的佣金(分): ' . $data['commission'] . ' (计算值: ' . $commission . ')', 'INFO');

        // 保存平台服务费到fee字段（分单位）
        $data['fee'] = $platformFee;

        // DEBUG: 记录平台服务费
        \Think\Log::write('保存平台服务费(分): ' . $data['fee'], 'INFO');

        // 检查是否已存在配置
        $existing = $this->where([
            'zsb_id' => $data['zsb_id'],
            'post_id' => $data['post_id']
        ])->find();

        if ($existing) {
            // 更新现有配置
            $data['id'] = $existing['id'];
            $data['update_time'] = time();
            return $this->save($data);
        } else {
            // 新增配置
            $data['create_time'] = time();
            return $this->add($data);
        }
    }

    /**
     * 获取招就办岗位价格
     * @param int $zsbId 招就办ID
     * @param int $postId 岗位ID
     * @param bool $convertToYuan 是否转换为元单位显示，默认true
     * @return array|false
     */
    public function getZsbPostPrice($zsbId, $postId, $convertToYuan = true)
    {
        $result = $this->where([
            'zsb_id' => $zsbId,
            'post_id' => $postId,
            'status' => 1
        ])->find();

        if ($result && $convertToYuan) {
            $result = $this->processPriceDataForDisplay($result);
        }

        return $result;
    }

    /**
     * 获取岗位的完整价格信息（包括基准成本价、平台服务费等）
     * @param int $zsbId 招就办ID
     * @param int $postId 岗位ID
     * @param bool $convertToYuan 是否转换为元单位显示，默认true
     * @return array|false
     */
    public function getPostPriceInfo($zsbId, $postId, $convertToYuan = true)
    {
        // 获取岗位基本信息
        $postInfo = D('ProjectPost')->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.id' => $postId, 'pp.status' => 1, 'pp.is_zsb_available' => 1])
            ->field('pp.id, pp.job_name, pp.service_price, pp.max_price, p.name as project_name')
            ->find();

        if (!$postInfo) {
            return false;
        }

        // 获取基准成本价
        $baseCost = $this->getBaseCost($postId);

        // 获取价格配置
        $priceConfig = $this->where([
            'zsb_id' => $zsbId,
            'post_id' => $postId
        ])->find();

        $result = [
            'post_id' => $postInfo['id'],
            'job_name' => $postInfo['job_name'],
            'project_name' => $postInfo['project_name'],
            'service_price' => $postInfo['service_price'], // 数据库存储单位就是元，不需要转换
            'max_price' => $postInfo['max_price'], // 数据库存储单位就是元，不需要转换
            'base_cost' => $baseCost, // 基准成本价数据库存储单位就是元，不需要转换
            'is_configured' => $priceConfig ? 1 : 0,
            'cost_price' => 0,
            'sale_price' => 0,
            'commission' => 0,
            'platform_fee' => 0,
            'status' => 0
        ];

        if ($priceConfig) {
            $result['cost_price'] = $convertToYuan ? $this->convertCentsToYuan($priceConfig['cost_price']) : $priceConfig['cost_price'];
            $result['sale_price'] = $convertToYuan ? $this->convertCentsToYuan($priceConfig['sale_price']) : $priceConfig['sale_price'];
            $result['commission'] = $convertToYuan ? $this->convertCentsToYuan($priceConfig['commission']) : $priceConfig['commission'];
            $result['status'] = $priceConfig['status'];

            // 计算平台服务费
            if ($priceConfig['sale_price'] > 0) {
                $platformFeeCents = $this->calculatePlatformFee($priceConfig['sale_price'], $postInfo['service_price']);
                $result['platform_fee'] = $convertToYuan ? $this->convertCentsToYuan($platformFeeCents) : $platformFeeCents;
            }
        }

        return $result;
    }

    /**
     * 批量设置价格
     * @param int $zsbId 招就办ID
     * @param array $priceList 价格列表
     * @return bool
     */
    public function batchSetPrice($zsbId, $priceList)
    {
        $this->startTrans();
        try {
            foreach ($priceList as $priceData) {
                $priceData['zsb_id'] = $zsbId;
                if (!$this->setZsbPostPrice($priceData)) {
                    throw new \Exception($this->getError());
                }
            }
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            $this->error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取招就办可用岗位列表（未配置价格的岗位）
     * @param int $zsbId 招就办ID
     * @return array
     */
    public function getAvailablePostsForZsb($zsbId)
    {
        $postModel = D('ProjectPost');
        $configuredPostIds = $this->where(['zsb_id' => $zsbId])->getField('post_id', true);

        $where = ['pp.status' => 1, 'pp.is_zsb_available' => 1];
        if (!empty($configuredPostIds)) {
            $where['pp.id'] = ['not in', $configuredPostIds];
        }

        $list = $postModel->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->field('pp.id, pp.job_name, pp.service_price, pp.max_price, p.name as project_name')
            ->where($where)
            ->select();

        // service_price和max_price数据库存储单位就是元，不需要转换
        // 保持原值即可

        return $list;
    }

    /**
     * 获取基准成本价（从项目身份成本表获取服务站身份的成本价）
     * @param int $postId 岗位ID
     * @return int 基准成本价（元单位），如果不存在返回0
     */
    public function getBaseCost($postId)
    {
        // 先获取岗位信息，确保使用正确的项目ID
        $post = D('ProjectPost')->where(['id' => $postId])->find();
        if (!$post) {
            return 0;
        }

        // 使用项目ID和岗位ID双重条件查询，确保数据一致性
        $projectJoinIdentity = D("ProjectJoinIdentity")->where([
            'project_id' => $post['project_id'],
            'project_post_id' => $postId,
            'project_identity_id' => 3, // 服务站身份
        ])->order('cost ASC')->find();

        // 注意：z_project_join_identity.cost字段存储的是元单位
        return $projectJoinIdentity ? intval($projectJoinIdentity['cost']) : 0;
    }

    /**
     * 获取平台费率配置
     * @return float 平台费率，默认0.30（30%）
     */
    public function getPlatformRate()
    {
        $conf = D('Conf')->where([
            'name' => 'platform_rate'
        ])->find();

        return $conf ? floatval($conf['value']) : 0.2;
    }

    /**
     * 初始化系统配置（如果不存在则创建）
     * @return bool
     */
    public function initSystemConfig()
    {
        $confModel = D('Conf');

        // 检查平台费率配置是否存在
        $platformRateExists = $confModel->where(['name' => 'platform_rate'])->find();
        if (!$platformRateExists) {
            $confModel->add([
                'group' => 1,
                'name' => 'platform_rate',
                'title' => '平台费率',
                'value' => '0.2',
                'desc' => '平台抽成比例，默认20%',
                'show' => 1
            ]);
        }

        // 检查最大佣金配置是否存在
        $maxCommissionExists = $confModel->where(['name' => 'max_commission'])->find();
        if (!$maxCommissionExists) {
            $confModel->add([
                'group' => 1,
                'name' => 'max_commission',
                'title' => '最大佣金',
                'value' => '99999999',
                'desc' => '佣金上限(分)',
                'show' => 0
            ]);
        }

        return true;
    }

    /**
     * 计算平台服务费
     * @param int $salePrice 对外报价（分单位）
     * @param int $servicePrice 最低报价（元单位）
     * @param float $platformRate 平台费率
     * @return int 平台服务费（分单位）
     */
    public function calculatePlatformFee($salePrice, $servicePrice, $platformRate = null)
    {
        if ($platformRate === null) {
            $platformRate = $this->getPlatformRate();
        }

        // 将最低报价转换为分单位
        $servicePriceCents = $this->convertYuanToCents($servicePrice);

        // 计算平台服务费：max(0, (对外报价 - 最低报价×100)) × 平台费率
        $platformFee = max(0, ($salePrice - $servicePriceCents)) * $platformRate;

        return intval($platformFee);
    }

    /**
     * 计算招就办佣金（新公式）
     * @param int $salePrice 对外报价（分单位）
     * @param int $costPrice 服务站成本价（分单位）
     * @param int $platformFee 平台服务费（分单位）
     * @return int 招就办佣金（分单位）
     */
    public function calculateCommission($salePrice, $costPrice, $platformFee)
    {
        // 新佣金计算公式：对外报价 - 服务站成本价 - 平台服务费
        return $salePrice - $costPrice - $platformFee;
    }

    /**
     * 验证价格配置的合法性
     * @param array $data 价格数据
     * @return bool
     */
    public function validatePriceConfig($data)
    {
        // 基础字段验证
        if (empty($data['zsb_id']) || empty($data['post_id'])) {
            $this->error = '招就办ID和岗位ID不能为空';
            return false;
        }

        if (!is_numeric($data['cost_price']) || $data['cost_price'] <= 0) {
            $this->error = '成本价必须为正整数';
            return false;
        }

        if (!is_numeric($data['sale_price']) || $data['sale_price'] <= 0) {
            $this->error = '对外价必须为正整数';
            return false;
        }

        if ($data['sale_price'] < $data['cost_price']) {
            $this->error = '对外价不能低于成本价';
            return false;
        }

        // 验证招就办是否存在且未被禁用
        $zsbExists = D('ServiceStation')->where([
            'id' => $data['zsb_id'],
            'zsb_type' => 2,
            'status' => 1,
            'is_disabled' => 0  // 检查禁用状态
        ])->find();

        if (!$zsbExists) {
            $this->error = '招就办不存在、未审核通过或已被禁用';
            return false;
        }

        // 验证岗位是否存在且可用，同时获取服务价格
        $postInfo = D('ProjectPost')->where([
            'id' => $data['post_id'],
            'status' => 1,
            'is_zsb_available' => 1
        ])->find();

        if (!$postInfo) {
            $this->error = '岗位不存在或不可用';
            return false;
        }

        // 获取基准成本价进行验证
        $baseCost = $this->getBaseCost($data['post_id']); // 元单位
        if ($baseCost > 0) {
            // 基准成本价是元单位，直接比较
            if ($data['cost_price'] < $baseCost) {
                $this->error = '服务站成本价不得低于基准成本价（' . $baseCost . '元）';
                return false;
            }
        }

        // 计算新的佣金并验证
        $costPriceCents = $this->convertYuanToCents($data['cost_price']);
        $salePriceCents = $this->convertYuanToCents($data['sale_price']);
        $servicePrice = $postInfo['service_price'];

        // 计算平台服务费
        $platformFee = $this->calculatePlatformFee($salePriceCents, $servicePrice);

        // 计算招就办佣金
        $commission = $this->calculateCommission($salePriceCents, $costPriceCents, $platformFee);

        // 验证佣金不能为负数
        if ($commission < 0) {
            $platformFeeYuan = $this->convertCentsToYuan($platformFee);
            $this->error = '招就办佣金不能为负数。当前平台服务费为' . $platformFeeYuan . '元，请调整价格配置';
            return false;
        }

        // 检查佣金是否超出数据库存储范围
        if ($commission > 99999999) {
            $this->error = '佣金值过大，超出系统支持范围。请减小对外价与成本价的差额';
            \Think\Log::write('价格验证失败：计算的佣金值（' . $commission . '分）超出最大存储范围', 'WARN');
            return false;
        }

        return true;
    }

    /**
     * 批量重新计算价格
     * @param float $newPlatformRate 新的平台费率
     * @param int $logId 日志ID
     * @return array
     */
    public function batchRecalculatePrices($newPlatformRate, $logId)
    {
        $this->startTrans();

        try {
            // 获取所有需要重新计算的价格配置
            $priceConfigs = $this->alias('zpp')
                ->join('LEFT JOIN __PROJECT_POST__ pp ON zpp.post_id = pp.id')
                ->field('zpp.*, pp.service_price')
                ->where(['zpp.status' => 1])
                ->select();

            if (empty($priceConfigs)) {
                $this->rollback();
                return ['status' => false, 'msg' => '没有需要重新计算的价格配置'];
            }

            $successCount = 0;
            $errorCount = 0;
            $totalCount = count($priceConfigs);

            // 分批处理，每批100条记录
            $batchSize = 100;
            $batches = array_chunk($priceConfigs, $batchSize);

            foreach ($batches as $batch) {
                foreach ($batch as $config) {
                    try {
                        // 重新计算平台服务费
                        $platformFee = $this->calculatePlatformFee(
                            $config['sale_price'],
                            $config['service_price'],
                            $newPlatformRate
                        );

                        // 重新计算招就办佣金
                        $commission = $this->calculateCommission(
                            $config['sale_price'],
                            $config['cost_price'],
                            $platformFee
                        );

                        // 更新数据库
                        $updateResult = $this->where(['id' => $config['id']])->save([
                            'fee' => $platformFee,
                            'commission' => $commission,
                            'update_time' => time()
                        ]);

                        if ($updateResult !== false) {
                            $successCount++;
                        } else {
                            $errorCount++;
                            \Think\Log::write('批量计算失败 - 配置ID:' . $config['id'], 'ERROR');
                        }

                    } catch (\Exception $e) {
                        $errorCount++;
                        \Think\Log::write('批量计算异常 - 配置ID:' . $config['id'] . ', 错误:' . $e->getMessage(), 'ERROR');
                    }
                }

                // 每批处理完后稍作休息，避免数据库压力过大
                usleep(10000); // 10毫秒
            }

            $this->commit();

            return [
                'status' => true,
                'affected_count' => $totalCount,
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'msg' => "批量计算完成，成功{$successCount}条，失败{$errorCount}条"
            ];

        } catch (\Exception $e) {
            $this->rollback();
            \Think\Log::write('批量计算事务失败:' . $e->getMessage(), 'ERROR');
            return ['status' => false, 'msg' => '批量计算失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取受影响的价格配置统计
     * @param float $newPlatformRate 新的平台费率
     * @return array
     */
    public function getAffectedPricesStats($newPlatformRate)
    {
        // 获取所有启用的价格配置数量
        $totalCount = $this->where(['status' => 1])->count();

        // 计算预估执行时间（按每秒处理50条记录估算）
        $estimatedSeconds = ceil($totalCount / 50);
        $estimatedTime = $estimatedSeconds > 60 ?
            ceil($estimatedSeconds / 60) . '分钟' :
            $estimatedSeconds . '秒';

        return [
            'total_count' => $totalCount,
            'estimated_time' => $estimatedTime,
            'estimated_seconds' => $estimatedSeconds
        ];
    }

    /**
     * 计算费率变化影响预览
     * @param float $newPlatformRate 新的平台费率
     * @return array
     */
    public function previewRateChangeImpact($newPlatformRate)
    {
        $currentRate = $this->getPlatformRate();

        // 获取所有价格配置进行预览计算
        $priceConfigs = $this->alias('zpp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON zpp.post_id = pp.id')
            ->field('zpp.sale_price, zpp.cost_price, zpp.commission as current_commission, pp.service_price')
            ->where(['zpp.status' => 1])
            ->select();

        if (empty($priceConfigs)) {
            return [
                'affected_count' => 0,
                'commission_change' => 0,
                'platform_fee_change' => 0
            ];
        }

        $totalCommissionChange = 0;
        $totalPlatformFeeChange = 0;

        foreach ($priceConfigs as $config) {
            // 计算当前平台服务费
            $currentPlatformFee = $this->calculatePlatformFee(
                $config['sale_price'],
                $config['service_price'],
                $currentRate
            );

            // 计算新的平台服务费
            $newPlatformFee = $this->calculatePlatformFee(
                $config['sale_price'],
                $config['service_price'],
                $newPlatformRate
            );

            // 计算新的佣金
            $newCommission = $this->calculateCommission(
                $config['sale_price'],
                $config['cost_price'],
                $newPlatformFee
            );

            // 累计变化
            $totalCommissionChange += ($newCommission - $config['current_commission']);
            $totalPlatformFeeChange += ($newPlatformFee - $currentPlatformFee);
        }

        return [
            'affected_count' => count($priceConfigs),
            'commission_change' => $this->convertCentsToYuan($totalCommissionChange),
            'platform_fee_change' => $this->convertCentsToYuan($totalPlatformFeeChange),
            'commission_change_cents' => $totalCommissionChange,
            'platform_fee_change_cents' => $totalPlatformFeeChange
        ];
    }

    /**
     * 获取需要通知的服务站用户列表
     * @param float $newPlatformRate 新的平台费率
     * @return array
     */
    public function getAffectedStationUsers($newPlatformRate)
    {
        // 获取有价格配置的服务站用户（排除禁用的招就办）
        $stationUsers = $this->alias('zpp')
            ->join('LEFT JOIN __SERVICE_STATION__ ss ON zpp.zsb_id = ss.id')
            ->join('LEFT JOIN __SERVICE_STATION__ parent_ss ON ss.zsb_ref_station = parent_ss.id')
            ->field('parent_ss.id as station_id, parent_ss.service_name, COUNT(zpp.id) as price_count')
            ->where(['zpp.status' => 1, 'ss.zsb_type' => 2, 'ss.status' => 1, 'ss.is_disabled' => 0])
            ->group('parent_ss.id')
            ->having('price_count > 0')
            ->select();

        return $stationUsers ?: [];
    }

    /**
     * 获取重新计算进度
     * @param int $logId 日志ID
     * @return array
     */
    public function getRecalculateProgress($logId)
    {
        // 这里可以通过缓存或其他方式来跟踪进度
        // 简化实现，返回基本信息
        $logInfo = D('PlatformRateLog')->where(['id' => $logId])->find();

        if (!$logInfo) {
            return ['progress' => 0, 'status' => 'not_found'];
        }

        switch ($logInfo['execute_status']) {
            case 0:
                return ['progress' => 0, 'status' => 'pending', 'message' => '等待执行'];
            case 1:
                return ['progress' => 50, 'status' => 'running', 'message' => '正在执行中...'];
            case 2:
                return ['progress' => 100, 'status' => 'completed', 'message' => '执行完成', 'affected_count' => $logInfo['affected_count']];
            case 3:
                return ['progress' => 0, 'status' => 'failed', 'message' => '执行失败'];
            case 4:
                return ['progress' => 0, 'status' => 'cancelled', 'message' => '已取消'];
            default:
                return ['progress' => 0, 'status' => 'unknown', 'message' => '未知状态'];
        }
    }


}
