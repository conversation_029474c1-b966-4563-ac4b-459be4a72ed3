<script>require(['bootstrap']);</script>

<style>
/* 现代化页脚样式 */
.modern-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    padding: 30px 0 20px;
    margin-top: 50px;
    position: relative;
}

.modern-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-info {
    display: flex;
    align-items: center;
    color: #6c757d;
    font-size: 14px;
}

.footer-logo {
    margin-right: 15px;
    font-size: 18px;
    font-weight: bold;
    color: #667eea;
}

.footer-text {
    margin: 0;
    line-height: 1.5;
}

.footer-text a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.footer-text a:hover {
    color: #764ba2;
    text-decoration: none;
}

.footer-stats {
    display: flex;
    align-items: center;
    gap: 20px;
    color: #6c757d;
    font-size: 13px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-item i {
    color: #667eea;
    font-size: 14px;
}

.footer-links {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.footer-link {
    color: #6c757d;
    text-decoration: none;
    font-size: 13px;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #667eea;
    text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .footer-stats {
        justify-content: center;
        flex-wrap: wrap;
    }

    .footer-links {
        justify-content: center;
    }
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    margin-top: 15px;
    color: #667eea;
    font-weight: 500;
}
</style>

<footer class="modern-footer" role="footer">
    <div class="footer-content">
        <div class="footer-info">
            <div class="footer-logo">
                <i class="fa fa-cube"></i>
            </div>
            <div>
                <p class="footer-text">
                    Powered by <a href="" target="_blank">中才国科</a> &copy; {:date('Y')}
                </p>
                <div class="footer-links">
                    <a href="#" class="footer-link">
                        <i class="fa fa-shield"></i> 隐私政策
                    </a>
                    <a href="#" class="footer-link">
                        <i class="fa fa-file-text"></i> 使用条款
                    </a>
                    <a href="#" class="footer-link">
                        <i class="fa fa-question-circle"></i> 帮助中心
                    </a>
                </div>
            </div>
        </div>

        <div class="footer-stats">
            <div class="stat-item">
                <i class="fa fa-clock-o"></i>
                <span>最后更新: {:date('Y-m-d H:i')}</span>
            </div>
            <div class="stat-item">
                <i class="fa fa-user"></i>
                <span>当前用户: <?=session('admin_name')?></span>
            </div>
            <div class="stat-item">
                <i class="fa fa-server"></i>
                <span>系统版本: v2.0</span>
            </div>
        </div>
    </div>
</footer>
</body>
</html>
<script>
	ajaxBoolean = true;
	require(["daterangepicker", "layer"], function($, layer){
		$(function() {
			// 现代化的Ajax处理
			$('.js_ajax').click(function () {
				if (ajaxBoolean === false) {
					return;
				}
				ajaxBoolean = false;
				var that = $(this),
						url = that.attr('url'),
						confirm = that.attr('confirm'),
						tipText = that.attr('tipText'),
						layerConfirm = that.attr('layerConfirm'),
						confirmBool = false;

				if(tipText == '' || tipText == undefined){
					tipText = '<div style="text-align: center; padding: 20px; font-size: 16px;">确定执行此操作？</div>';
				}else{
					tipText = `<div style="padding: 20px; font-size: 14px;">${tipText}</div>`;
				}

				if (confirm !== undefined) {
					layer.confirm('', {
						title: '<i class="fa fa-question-circle" style="color: #667eea;"></i> 操作确认',
						content: tipText,
						closeBtn: true,
						btn: ['<i class="fa fa-check"></i> 确定', '<i class="fa fa-times"></i> 取消'],
						btnAlign: 'c',
						skin: 'modern-confirm',
						area: ['400px', 'auto']
					}, function (index) {
						sendAajax(url, layerConfirm);
						layer.close(index);
					}, function () {
						ajaxBoolean = true;
					});
				} else {
					sendAajax(url, layerConfirm);
				}
			});

			function sendAajax(url, layerConfirm) {
				layer.closeAll();

				// 显示现代化加载动画
				var loadingIndex = layer.open({
					type: 1,
					title: false,
					closeBtn: false,
					area: ['200px', '150px'],
					skin: 'modern-loading',
					content: `
						<div style="text-align: center; padding: 30px;">
							<div class="loading-spinner"></div>
							<div class="loading-text">处理中...</div>
						</div>
					`
				});

				$.ajax({
					type: "GET",
					url: url,
					cache: false,
					dataType: 'JSON',
					timeout: 30000
				}).then(function (data) {
					layer.close(loadingIndex);

					if (data.status == 1) {
						layer.msg('<i class="fa fa-check-circle"></i> 操作成功', {
							icon: 1,
							time: 2000,
							skin: 'modern-success'
						});
						setTimeout(function () {
							window.location.reload();
						}, 1500);
					} else {
						if(layerConfirm){
							layer.confirm('', {
								title: '<i class="fa fa-exclamation-triangle" style="color: #f39c12;"></i> 操作结果',
								content: `<div style="padding: 20px; font-size: 14px;">${data.info}</div>`,
								closeBtn: true,
								btn: ['<i class="fa fa-refresh"></i> 刷新页面'],
								btnAlign: 'c',
								skin: 'modern-error',
								area: ['400px', 'auto']
							}, function (index) {
								window.location.reload();
							});
						} else {
							layer.msg('<i class="fa fa-exclamation-circle"></i> ' + data.info, {
								icon: 2,
								time: 3000,
								skin: 'modern-error'
							});
							setTimeout(function () {
								window.location.reload();
							}, 2000);
						}
					}
					ajaxBoolean = true;
				}).catch(function(xhr, status, error) {
					layer.close(loadingIndex);
					layer.msg('<i class="fa fa-exclamation-triangle"></i> 网络错误，请重试', {
						icon: 2,
						time: 3000,
						skin: 'modern-error'
					});
					ajaxBoolean = true;
				});
			}

			// 添加现代化样式到layer
			$('head').append(`
				<style>
				.modern-confirm .layui-layer-title {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
					border: none;
					font-weight: 600;
				}
				.modern-confirm .layui-layer-btn {
					text-align: center;
					padding: 15px;
				}
				.modern-confirm .layui-layer-btn a {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					border: none;
					border-radius: 6px;
					color: white;
					font-weight: 500;
					margin: 0 5px;
					padding: 8px 20px;
				}
				.modern-confirm .layui-layer-btn a:hover {
					background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
				}
				.modern-loading .layui-layer-content {
					background: rgba(255, 255, 255, 0.95);
					backdrop-filter: blur(10px);
					border-radius: 12px;
				}
				.modern-success .layui-layer-content {
					color: #28a745;
					font-weight: 500;
				}
				.modern-error .layui-layer-content {
					color: #dc3545;
					font-weight: 500;
				}
				</style>
			`);
		});
	});
</script>