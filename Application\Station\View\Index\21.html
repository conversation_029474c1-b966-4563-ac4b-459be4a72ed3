﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <title>{:$row['title']}</title>
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <style>
        :root {
            --primary-color: #00bf80;
            --secondary-color: #666;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, system-ui, sans-serif;
        }

        
        .container {
          
            margin: 12px;
        }

        .container ul li {
            margin: auto;
            padding:0 10px;
            list-style:disc;
        }


        /* 新版头部 */
        .header {
            margin-top: 28px;
            margin-bottom: 24px;
            text-align: center;
        }
        h1 {
            font-size: 22px;
            color: #000;
            margin-bottom: 8px;
            text-align: center;
        }
        .delay-notice {
            color: #e67e22;
            background: #fdf6ec;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        .zje{
            font-size: 16px;
            color: #000;
            margin-bottom: 8px;
            font-weight: bold;
        }
        /* 金额区域升级版 */
        .amount-section {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            text-align: center;
        }
        .amount-group {
            margin-bottom: 16px;
        }
        .total-amount {
            font-size: 36px;
            color: var(--primary-color);
            font-weight: 600;
            line-height: 1.3;
        }
        .available-amount {
            font-size: 16px;
            color: #999696;
            margin-top: 4px;
        }
        .time-tip {
            color: var(--secondary-color);
            font-size: 14px;
            margin: 12px 0 24px;
        }

        /* 按钮组优化 */
        .action-btns {
            display: grid;
            gap: 12px;
        }
        .btn {
            padding: 14px;
            border-radius: 6px;
            border: 1px solid var(--primary-color);
            background: #59c567;
            color: #fff;
            font-size: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            position: relative;
        }
        .btn::after {
            position: absolute;
            right: 14px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,191,128,0.1);
            color: var(--primary-color);
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 2px;
        }

        /* 双栏布局优化 */
        .columns {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 24px;
            margin-top: 20px;
        }

        /* 左侧菜单新增图标 */
        .menu-section {
            background: #fff;
            border-radius: 8px;
            padding: 16px;
        }
        .menu-item {
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .menu-item::before {
            content: "•";
            color: var(--primary-color);
        }

        /* 温馨提示样式升级 */
        .tips-section {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
        }
        .tips-list li {
            color: #a8a6a6;
            margin-bottom: 16px;
            padding-left: 24px;
            text-indent: -24px;
            
        }
        .tips-list li::before {
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .columns {
                grid-template-columns: 1fr;
            }
            .total-amount {
                font-size: 28px;
            }
            .btn {
                font-size: 18px;
                padding: 12px;
            }
        }
               
 /* 返回箭头样式 */
.back-arrow {
    position: fixed;
    top: 115px;
    left: 15px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.2s;
}

.back-arrow::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    width: 12px;
    height: 12px;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: translateY(-50%) rotate(45deg);
}

/* 点击反馈效果 */
.back-arrow:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.7);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .back-arrow {
        top: 170px;
        left: 10px;
        width: 36px;
        height: 36px;
    }
    
    .back-arrow::before {
        left: 13px;
        width: 10px;
        height: 10px;
    }
}
    </style>
</head>
<body>
<!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
    <include file="headers"/>
 
    <div class="container">
 
        <div class="amount-section">
            <div class="amount-group">
                <H1 style="text-align: left;font-size: 16px;font-weight: bold;margin-top: 18px;">{:$row['title']}</H1>
                <div class="available-amount" style="font-size: 12px;text-align:right;">{:date("Y年m月d日 H:i:s", $row['created'])}</div>
            </div>
            <div class="time-tip" style="text-align: left;color: #000;font-size: 14px;line-height: 25px;">
                {:htmlspecialchars_decode($row['content'])}
            </div>
            
            
        </div>

 
    </div>
    <script type="text/javascript">
        var kwd = '{$kwd}';
        var url = "{:U('index/joblist')}"
        var sortShowTime = '{:$show_time}';
        var sortShowNum = '{:$show_num}';
        $(document).ready(function(){
        $(".tabs-hd02 ul li").click(function(){
          $(this).siblings().removeClass("on");
          $(this).addClass("on");
        });
        $(".tabs-hd02 ul li").click(function(){
          $(this).siblings().removeClass("on");
          $(this).addClass("on");
        });
        
            $(".screen01 li").click(function(){
                var typeOnOrUp = 0;
                var dataType = $(this).attr('data-type');
                if($(this).is('.ondown')){
                    $(this).removeClass('ondown');
                    $(this).siblings().removeClass("ondown");
                    $(this).siblings().removeClass('onup');
                    $(this).addClass('onup');
                    typeOnOrUp = 1;
                } else {
                    $(this).removeClass('onup');
                    $(this).siblings().removeClass("ondown");
                    $(this).siblings().removeClass('onup');
                    $(this).addClass('ondown');
                    typeOnOrUp = 2;
                }
                if (dataType == 'sortShowTime') {
                    sortShowNum = 0;
                    sortShowTime = typeOnOrUp;
                } else {
                    sortShowTime = 0;
                    sortShowNum = typeOnOrUp;
                }
                window.location.href = url + '?kwd='+kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum;
                return
            });
            $('.js_kwd').blur(function () {
                var  new_kwd = $(this).val();
                if (new_kwd != kwd) {
                    window.location.href = url + '?kwd='+new_kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum;
                    return
                }
            })
        
            var noticeSwiper = new Swiper('.noticeSwiper', {
                direction: 'vertical',
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                }
            })
            var noticeSwiper = new Swiper('.page0106Swiper', {
                pagination: {
                    el: ".swiper-pagination",
                },
        
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                }
            });
         
        });

                // 返回逻辑（优先返回历史记录，失败则跳转首页）
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = '/'; // 修改为你的默认回退地址
    }
}

        </script>
</body>
</html>