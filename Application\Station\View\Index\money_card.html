﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <title>余额提现</title>
    <style>
        /* 复用之前的样式变量 */
        :root {
            --primary-color: #00bf80;
            --secondary-color: #666;
            --card-bg: #fff;
            --border-color: #eee;
        }
 
        .container {
            max-width: 1200px;
            margin: 12px;
        }

        /* 新版提现卡片 */
        .withdraw-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        /* 金额输入区 */
        .amount-group {
            margin-bottom: 24px;
        }
        .amount-limit {
            font-size: 16px;
            color: var(--secondary-color);
            margin-bottom: 8px;
        }
        .amount-value {
            font-size: 24px;
            color: var(--primary-color);
            font-weight: 600;
        }
        .available-amount {
            color: #e67e22;
            font-size: 14px;
            margin: 12px 0;
        }
        .tip-text {
            color: var(--secondary-color);
            font-size: 12px;
            line-height: 1.5;
        }

        /* 银行卡选择 */
        .bank-section {
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 16px;
            margin-bottom: 16px;
        }
        .bank-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .bank-name {
            font-size: 16px;
            color: #000;
        }
        .manage-link {
            color: var(--primary-color);
            font-size: 14px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .manage-link::after {
            content: "›";
            font-size: 18px;
        }
        .bank-info {
            color: var(--secondary-color);
            font-size: 14px;
            letter-spacing: 1px;
        }

        /* 确认按钮 */
        .confirm-btn {
            width: 100%;
            padding: 16px;
            background: var(--primary-color);
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        .confirm-btn:hover {
            opacity: 0.9;
        }
        .reminder-text {
            text-align: center;
            color: var(--secondary-color);
            font-size: 12px;
            margin-top: 12px;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .amount-value {
                font-size: 20px;
            }
            .bank-name {
                font-size: 14px;
            }
        }
         /* 新增输入框样式 */
         .amount-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            margin: 12px 0;
            transition: border-color 0.2s;
        }
        .amount-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0,191,128,0.1);
        }
        .input-tip {
            color: var(--secondary-color);
            font-size: 12px;
            margin-bottom: 8px;
        }
               
     /* 按钮组优化 */
     .action-btns {
            display: grid;
            gap: 12px;
            margin: 10px;
        }
        .btn {
            padding: 14px;
            border-radius: 6px;
            border: 1px solid var(--primary-color);
            background: #59c567;
            color: #fff;
            font-size: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            position: relative;
        }
        .btn::after {
            position: absolute;
            right: 14px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,191,128,0.1);
            color: var(--primary-color);
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 2px;
        }
        
                /* 返回箭头样式 */
.back-arrow {
    position: fixed;
    top: 115px;
    left: 15px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.2s;
}

.back-arrow::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    width: 12px;
    height: 12px;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: translateY(-50%) rotate(45deg);
}

/* 点击反馈效果 */
.back-arrow:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.7);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .back-arrow {
        top: 190px;
        left: 10px;
        width: 36px;
        height: 36px;
    }
    
    .back-arrow::before {
        left: 13px;
        width: 10px;
        height: 10px;
    }
}

/* Add styles for radio button options if needed */
.bank-card-options {
    display: flex;
    flex-direction: column;
    gap: 15px; /* Space between card options */
}
.bank-card-option {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #eee;
    cursor: pointer;
    transition: border-color 0.2s, box-shadow 0.2s;
}
.bank-card-option:hover {
    border-color: #ddd;
}
.bank-card-option input[type="radio"] {
    margin-right: 15px;
    transform: scale(1.2);
    accent-color: var(--primary-color);
}
.bank-card-option label {
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}
.card-bank-name {
    font-weight: 500;
    color: #333;
}
.card-number {
    color: #666;
    font-size: 0.9em;
    margin-left: 10px;
}
.default-tag {
    background-color: var(--primary-color);
    color: white;
    font-size: 0.7em;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 10px;
    font-weight: normal;
}

        /* 新增税费处理预览样式 */
        .tax-preview {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border: 1px solid #eee;
        }
        .tax-preview-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .tax-preview-badge {
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 4px;
            color: white;
        }
        .badge-success {
            background-color: #28a745;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #333;
        }
        .badge-info {
            background-color: #17a2b8;
        }
        .tax-preview-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px dashed #eee;
        }
        .tax-preview-item:last-child {
            border-bottom: none;
        }
        .tax-preview-label {
            color: #666;
        }
        .tax-preview-value {
            font-weight: 500;
            color: #333;
        }
        .tax-preview-value.highlight {
            color: #e67e22;
        }
        
        /* 额度使用进度条 */
        .quota-progress-container {
            margin: 15px 0;
            background-color: #f0f0f0;
            border-radius: 5px;
            height: 10px;
            position: relative;
        }
        .quota-progress-bar {
            height: 100%;
            border-radius: 5px;
            background-color: var(--primary-color);
            transition: width 0.3s;
        }
        .quota-progress-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            display: flex;
            justify-content: space-between;
        }
        .quota-progress-message {
            margin-top: 8px;
            font-size: 13px;
            color: #e74c3c;
        }
        
        /* 到账方式按钮样式 */
        .method-buttons {
            display: flex;
            gap: 10px; /* 按钮间距 */
            margin-bottom: 15px;
        }
        .method-btn {
            flex-grow: 1; /* 让按钮平分宽度 */
            padding: 10px 15px;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            border: 1px solid #ddd;
            background-color: #f8f8f8;
            color: #666;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s, border-color 0.2s;
        }
        .method-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            font-weight: bold;
        }
        .method-btn:disabled {
            background-color: #eee;
            color: #aaa;
            cursor: not-allowed;
            border-color: #eee;
        }
        .method-btn:disabled span {
            color: #aaa; /* 确保禁用时内部文字也是灰色 */
        }
    </style>
</head>
<body>
    <!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
    <include file="headers"/>
    <div class="container">
        <form method="post">

        <div class="withdraw-card">
            <h1 style="margin-bottom: 24px; font-size: 24px;text-align: center;color: #000;">
                余额提现
            </h1>
            <div class="amount-group">
                <div class="amount-limit">单笔提现额度 ¥50000.00</div>
                 <!-- 修改后的输入框部分 -->
                 <div class="input-tip">输入提现金额（整数）</div>
                 <input type="number" 
                        class="amount-input"
                        placeholder="请输入提现金额"
                        min="1"
                        max="50000"
                        pattern="\d*"
                        inputmode="numeric"
                        oninput="this.value=this.value.replace(/[^0-9]/g,''); updateCalculations(); validateAmount();"
                        name="amount"
                        id="withdrawAmount"
                        required>
                <div class="available-amount">可提现金额 ¥{$availableBalance}</div>
                
                <!-- 添加验证错误提示 -->
                <div id="amountError" style="color: #e74c3c; font-size: 12px; margin-top: 5px; display: none;"></div>
                
                <!-- 额度使用进度 -->
                <div class="quota-container">
                    <div style="margin: 15px 0 5px; display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 14px; font-weight: 500;">{$quotaInfo.year_month}月额度使用情况</span>
                        <a href="{:U('index/check_quota')}" style="font-size: 12px; color: var(--primary-color); text-decoration: none;">
                            查看详情 >
                        </a>
                    </div>
                    <div class="quota-progress-container">
                        <div class="quota-progress-bar" style="width: {$quotaInfo['used_amount']/$quotaInfo['total_quota']*100}%;"></div>
                    </div>
                    <div class="quota-progress-label">
                        <span>已用: ¥{$quotaInfo.used_amount}</span>
                        <span>总额度: ¥{$quotaInfo.total_quota}</span>
                    </div>
                    <div class="quota-progress-message" style="display:{$isQuotaExhausted ? 'block' : 'none'};">
                        注意: 当月平台完税额度已用完，提现需开具发票
                    </div>
                </div>
                
                <!-- 添加服务费和实际到账金额显示 -->
                <div class="fee-calculation" style="margin-top: 15px; padding: 10px; background-color: #f8f8f8; border-radius: 4px;">
                    <div style="display: flex; justify-content: space-between; font-size: 14px; margin-bottom: 5px;">
                        <span>技术服务费({$serviceFeeRatePercent}%):</span>
                        <span id="serviceFee">¥0.00</span>
                    </div>
                    <div id="feeLimitNote" style="font-size: 12px; color: #28a745; text-align: right; margin-bottom: 5px; display: <if condition="$freeLimitValue gt 0">block<else/>none</if>;">
                        您的免手续费额度剩余: ¥{$freeLimitValue}
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 16px; font-weight: bold; color: #ff6b00;">
                        <span>实际到账金额:</span>
                        <span id="actualAmount">¥0.00</span>
                    </div>
                </div>
                
                <!-- 税费处理预览区域 -->
                <div id="taxPreview" class="tax-preview" style="display: none;">
                    <div class="tax-preview-title">
                        <span>税费处理方式</span>
                        <span id="taxHandlingBadge" class="tax-preview-badge badge-warning">用户开票</span> <!-- 默认显示银行卡方式 -->
                    </div>
                    <div class="tax-preview-item">
                        <span class="tax-preview-label">平台负责完税金额:</span>
                        <span id="platformTaxAmount" class="tax-preview-value">¥0.00</span>
                    </div>
                    <div class="tax-preview-item">
                        <span class="tax-preview-label">用户需开票金额:</span>
                        <span id="userInvoiceAmount" class="tax-preview-value">¥0.00</span>
                    </div>
                    <div class="tax-preview-item">
                        <span class="tax-preview-label">提现合计金额:</span>
                        <span id="totalAmount" class="tax-preview-value highlight">¥0.00</span>
                    </div>
                </div>

                <div id="withdrawalInstructionsWrapper" style="margin-top: 15px;">
                    <div class="bank-header" style="margin-bottom: 5px;">
                        <div class="bank-name">提现说明</div>
                    </div>
                    <p class="tip-text" id="withdrawalInstructions">
                        <!-- 说明内容将由 JS 动态填充 -->
                    </p>
                </div>
            </div>

            <!-- 到账方式选择 (按钮样式) -->
            <div class="bank-section" style="border-bottom: none; margin-bottom: 15px;">
                <div class="bank-header">
                    <div class="bank-name">到账方式</div>
                </div>
                <div class="method-buttons">
                    <button type="button" class="method-btn active" data-platform="bank">银行卡</button>
                    <button type="button" class="method-btn" data-platform="linggong" <if condition="!$hasApprovedLinggong">disabled</if>>
                        灵工平台 <if condition="!$hasApprovedLinggong"><span style="font-size: 12px; color: inherit;">(不可用)</span></if>
                    </button>
                </div>
            </div>

            <!-- 银行卡账户列表 -->
            <div class="bank-section" id="bankAccountSection">
                <div class="bank-header">
                    <div class="bank-name">选择银行卡</div>
                </div>
                <if condition="$hasBankCards">
                    <div class="bank-card-options">
                        <foreach name="bankCardList" item="vo">
                            <div class="bank-card-option">
                                <input type="radio" name="bank_card_id" id="card_{$vo.id}" value="{$vo.id}" <eq name="vo.is_default" value="1">checked</eq> required>
                                <!-- 移除隐藏的 platform input -->
                                <label for="card_{$vo.id}">
                                    <span class="card-bank-name">{$vo.bank_name}</span>
                                    <span class="card-number">**** **** **** {:substr($vo['card_number'], -4)}</span>
                                    <eq name="vo.is_default" value="1"><span class="default-tag">默认</span></eq>
                                </label>
                            </div>
                        </foreach>
                    </div>
                <else />
                     <div class="bank-info">请先在电脑端后台添加银行卡</div>
                </if>
            </div>

            <!-- 灵工平台账户列表 -->
            <div class="bank-section" id="linggongAccountSection" style="display: none;">
                <div class="bank-header">
                    <div class="bank-name">选择灵工平台账户</div>
                </div>
                <if condition="$hasApprovedLinggong">
                    <div class="bank-card-options">
                        <div class="bank-card-option">
                            <input type="radio" name="linggong_binding_id" id="linggong_{$linggongBinding.id}" value="{$linggongBinding.id}" checked required>
                            <!-- 移除隐藏的 platform input -->
                            <label for="linggong_{$linggongBinding.id}">
                                <span class="card-bank-name">{$linggongBinding.bank_name}</span>
                                <span class="card-number">**** {$linggongBinding.bank_account_last4}</span>
                                <span class="default-tag">
                                    <if condition="$linggongBinding['platform_type'] eq 1">
                                        京东京灵
                                    <elseif condition="$linggongBinding['platform_type'] eq 2" />
                                        云账户
                                    <else />
                                        未知平台
                                    </if>
                                </span>
                            </label>
                        </div>
                    </div>
                    <p class="tip-text" style="color: #e74c3c; margin-top: 10px;">注意：灵工平台提现仅在当月完税额度（¥{$quotaInfo.remaining_quota}）内可用。</p>
                <else />
                    <!-- 此处理论上不会显示，因为 linggong 单选按钮会被禁用 -->
                    <div class="bank-info">您还未绑定灵工平台账户或绑定未通过审核。</div>
                </if>
            </div>
            
            <!-- 隐藏的 Platform 字段 -->
            <input type="hidden" name="platform" id="platformInput" value="bank">

            <button class="confirm-btn" type="submit">确认提现</button>
            <div class="reminder-text">今日可提现3次</div>

        </div>
        </form>
    </div>
 
    <script>
        // 将 showError 函数移到顶层作用域
        function showError(message) {
            const errorDiv = document.getElementById('amountError');
            const amountInput = document.getElementById('withdrawAmount');
            const submitBtn = document.querySelector('.confirm-btn');

            if (errorDiv) {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }
            if (amountInput) {
                amountInput.style.borderColor = '#e74c3c';
            }
            if (submitBtn) {
                submitBtn.disabled = true;
            }
        }

        // 提现说明模板
        const instructionsTemplates = {
            bank: `
                <span style="color: #ffc107; font-weight: bold;">银行卡提现：用户开票，税费需由用户自行承担。</span><br>
                1、提现申请提交之后，平台财务进行审核。<br>
                2、每月9.8万内平台负责完税（通过云账户灵活用工平台以个人经营形式完税）；每月9.8万超出部分的提现流程为：申请提现 --> 财务审核 --> 您开发票 --> 财务转账。<br>
                3、如每月平均提现需求额度超出9.8万建议认证公司收款。
            `,
            linggong: `
                <span style="color: #28a745; font-weight: bold;">灵工平台提现：平台负责完税，税费由平台承担。</span><br>
                注意：灵工平台提现仅限于当月完税额度（¥{$quotaInfo.remaining_quota}）内使用，超出额度请选择银行卡提现。<br>
                1、提现申请提交之后，平台财务进行审核。<br>
                2、每月9.8万内平台负责完税（通过云账户灵活用工平台以个人经营形式完税）；每月9.8万超出部分的提现流程为：申请提现 --> 财务审核 --> 您开发票 --> 财务转账。<br>
                3、如每月平均提现需求额度超出9.8万建议认证公司收款。
            `
        };

        // 更新提现说明
        function updateWithdrawalInstructions(platform) {
            const instructionsDiv = document.getElementById('withdrawalInstructions');
            if (instructionsDiv) {
                instructionsDiv.innerHTML = instructionsTemplates[platform] || instructionsTemplates.bank;
            }
        }

        // 输入控制增强
        document.querySelector('.amount-input').addEventListener('keydown', function(e) {
            // 阻止非数字字符输入
            if(['e', 'E', '+', '-', '.'].includes(e.key)) {
                e.preventDefault();
            }
        });
        
        // 验证金额输入
        function validateAmount() {
            const amountInput = document.getElementById('withdrawAmount');
            const errorDiv = document.getElementById('amountError');
            const submitBtn = document.querySelector('.confirm-btn');
            const amount = parseInt(amountInput.value, 10);
            const availableBalance = {$availableBalance};
            const maxLimit = 50000;
            const minLimit = 1;
            
            // 重置错误状态 - 但保留按钮禁用状态，直到验证通过
            // 只有当错误不是由validateAmount本身产生时才清除
             if (errorDiv.textContent && !errorDiv.textContent.includes("金额") && !errorDiv.textContent.includes("整数")) {
                 // 如果是AJAX或后端错误，先不清，让showError覆盖
             } else {
                 errorDiv.style.display = 'none';
                 errorDiv.textContent = '';
                 amountInput.style.borderColor = '';
             }
             // 默认允许提交，除非验证失败
             submitBtn.disabled = false; 
            
            // 执行验证
            if (!amountInput.value) {
                // 空值不显示错误，但禁用按钮
                submitBtn.disabled = true;
                return;
            }
            
            if (isNaN(amount) || amount !== parseFloat(amountInput.value)) {
                showError('请输入整数金额'); // 调用全局 showError
                return;
            }
            
            if (amount < minLimit) {
                showError('提现金额不能小于 ' + minLimit + ' 元'); // 调用全局 showError
                return;
            }
            
            if (amount > maxLimit) {
                showError('单笔提现金额不能超过 ' + maxLimit + ' 元'); // 调用全局 showError
                return;
            }
            
            if (amount > availableBalance) {
                showError('提现金额不能超过可提现余额'); // 调用全局 showError
                return;
            }
            
            // 验证通过 - 不需要 showError，按钮状态已在上面重置
            // function showError(message) { ... } // 删除内部的 showError 定义
        }
        
        // 计算并更新服务费和实际到账金额
        function updateFeeCalculation() {
            const amount = parseFloat(document.getElementById('withdrawAmount').value) || 0;
            const feeRate = {$serviceFeeRate}; // 使用后端传递的费率
            const freeLimit = {$freeLimitValue}; // 使用后端传递的免费额度
            
            let serviceFee = 0;
            let actualAmount = amount;
            
            // 判断是否需要收取手续费
            if (amount > freeLimit || freeLimit <= 0) {
                // 超出免费额度或无免费额度，按正常收费
                serviceFee = Math.round(amount * feeRate * 100) / 100; // 保留两位小数
                actualAmount = amount - serviceFee;
            } else {
                // 在免费额度内，不收取手续费
                serviceFee = 0;
                actualAmount = amount;
            }
            
            document.getElementById('serviceFee').textContent = '¥' + serviceFee.toFixed(2);
            document.getElementById('actualAmount').textContent = '¥' + actualAmount.toFixed(2);
            
            // 根据是否免手续费更新显示
            if (amount > 0 && amount <= freeLimit && freeLimit > 0) {
                // 添加免手续费标识
                const feeElem = document.getElementById('serviceFee');
                feeElem.innerHTML = '<span style="text-decoration: line-through;">¥' + (Math.round(amount * feeRate * 100) / 100).toFixed(2) + '</span> <span style="color: #28a745;">¥0.00 (免手续费)</span>';
            }
            
            // 显示/隐藏免手续费额度提示
            const feeLimitNote = document.getElementById('feeLimitNote');
            if (feeLimitNote) {
                if (freeLimit > 0) {
                    feeLimitNote.style.display = 'block';
                } else {
                    feeLimitNote.style.display = 'none';
                }
            }
        }
        
        // 查询并更新税费处理方式
        function updateTaxDistribution() {
            const amountInput = document.getElementById('withdrawAmount'); // 声明 amountInput
            const amount = parseFloat(amountInput.value) || 0;
            const errorDiv = document.getElementById('amountError');
            const submitBtn = document.querySelector('.confirm-btn');
            
            // 获取当前选择的平台类型 - 从隐藏字段获取
            const platform = document.getElementById('platformInput').value || 'bank'; 
            // document.getElementById('platformInput').value = platform; // 这行不再需要，因为platform是从它读取的
            
            // 更新提现说明
            updateWithdrawalInstructions(platform);
            
            if (amount <= 0) {
                document.getElementById('taxPreview').style.display = 'none';
                // 如果金额为0，清除之前的AJAX错误信息
                if (errorDiv.textContent.includes("无法使用灵工平台提现") || errorDiv.textContent.includes("用户开票") || errorDiv.textContent.includes("平台负责完税")) {
                    errorDiv.style.display = 'none';
                    errorDiv.textContent = '';
                    amountInput.style.borderColor = '';
                    submitBtn.disabled = false; // Re-enable submit if amount is valid otherwise
                    validateAmount(); // Re-run validation
                }
                return;
            }
            
            // 显示税费预览区域
            document.getElementById('taxPreview').style.display = 'block';
            
            // 发送AJAX请求获取税费分配
            $.ajax({
                url: '{:U("index/check_tax_distribution")}',
                type: 'POST',
                dataType: 'json',
                data: { 
                    amount: amount,
                    platform: platform  // 使用从DOM获取的platform值
                },
                success: function(response) {
                     // 不再无条件清除错误状态
                     // errorDiv.style.display = 'none';
                     // errorDiv.textContent = '';
                     // amountInput.style.borderColor = ''; 
                     // submitBtn.disabled = false; // 让 validateAmount 控制按钮状态
                    
                    if (response.status) {
                        // 清除可能由 AJAX error 回调或后端 status=0 设置的错误
                        // 但保留由 validateAmount 设置的错误
                        if (errorDiv.textContent.includes("计算税费信息失败") || errorDiv.textContent.includes("无法使用灵工平台提现")) {
                             errorDiv.style.display = 'none';
                             errorDiv.textContent = '';
                             amountInput.style.borderColor = ''; 
                        }
                        // 重新验证一次以确定按钮状态
                        validateAmount(); 
                        
                        const data = response.data;
                        
                        // 更新显示
                        document.getElementById('platformTaxAmount').textContent = '¥' + (parseFloat(data.platform_tax_amount) || 0).toFixed(2);
                        document.getElementById('userInvoiceAmount').textContent = '¥' + (parseFloat(data.user_invoice_amount) || 0).toFixed(2);
                        document.getElementById('totalAmount').textContent = '¥' + (parseFloat(data.amount) || 0).toFixed(2);
                        
                        // 设置税费处理方式标签
                        const taxHandlingBadge = document.getElementById('taxHandlingBadge');
                        taxHandlingBadge.textContent = data.tax_handling_text;
                        
                        // 根据税费处理方式设置标签样式
                        taxHandlingBadge.className = 'tax-preview-badge';
                        switch(data.tax_handling) {
                            case 1:
                                taxHandlingBadge.classList.add('badge-success'); // 平台完税
                                break;
                            case 2:
                                taxHandlingBadge.classList.add('badge-warning'); // 用户开票
                                break;
                        }
                    } else {
                        // 显示后端返回的错误信息 (例如额度不足)
                        // document.getElementById('taxPreview').style.display = 'none'; // 保留税费区域可能更好？
                        showError(response.info); // 调用 showError 来显示错误并禁用按钮
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX请求失败:', error);
                     // 显示通用错误
                     errorDiv.textContent = '计算税费信息失败，请稍后重试';
                     errorDiv.style.display = 'block';
                     submitBtn.disabled = true;
                     amountInput.style.borderColor = '#e74c3c'; // 使用 amountInput
                }
            });
        }
        
        // 合并更新所有计算结果
        function updateCalculations() {
            updateFeeCalculation();
            updateTaxDistribution();
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化提现说明
            updateWithdrawalInstructions('bank');

            updateCalculations();
            validateAmount();
            
            // 移除旧的 radio change 事件监听
            // document.querySelectorAll('input[name="withdrawal_method"]').forEach(radio => { ... });

            // 添加新的 button click 事件监听
            document.querySelectorAll('.method-btn').forEach(button => {
                button.addEventListener('click', function() {
                    if (this.disabled) {
                        return; // 如果按钮被禁用，则不执行任何操作
                    }
                    
                    const selectedPlatform = this.getAttribute('data-platform');
                    document.getElementById('platformInput').value = selectedPlatform;
                    
                    // 更新按钮激活状态
                    document.querySelectorAll('.method-btn').forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // 切换账户显示区域
                    if (selectedPlatform === 'bank') {
                        document.getElementById('bankAccountSection').style.display = 'block';
                        document.getElementById('linggongAccountSection').style.display = 'none';
                        // 确保银行卡有选中的 (如果列表不为空)
                        if(document.querySelector('input[name="bank_card_id"]')) {
                           if(!document.querySelector('input[name="bank_card_id"]:checked')){
                               document.querySelector('input[name="bank_card_id"]').checked = true;
                           }
                        } 
                    } else {
                        document.getElementById('bankAccountSection').style.display = 'none';
                        document.getElementById('linggongAccountSection').style.display = 'block';
                         // 确保灵工账户有选中的 (如果列表不为空)
                        if(document.querySelector('input[name="linggong_binding_id"]')) {
                            if(!document.querySelector('input[name="linggong_binding_id"]:checked')){
                               document.querySelector('input[name="linggong_binding_id"]').checked = true;
                           }
                        } 
                    }
                    
                    // 重新计算税费等
                    updateCalculations();
                    validateAmount(); // 重新验证，因为平台切换可能影响错误状态
                });
            });
            
            // 表单提交前验证
            document.querySelector('form').addEventListener('submit', function(e) {
                validateAmount();
                if (document.querySelector('.confirm-btn').disabled) {
                    e.preventDefault();
                    return false;
                }
                // 确保选中了账户
                const platform = document.getElementById('platformInput').value;
                let accountSelected = false;
                if (platform === 'bank') {
                    accountSelected = !!document.querySelector('input[name="bank_card_id"]:checked');
                    if (!accountSelected && document.querySelector('input[name="bank_card_id"]')) {
                         alert('请选择提现到的银行卡');
                         e.preventDefault();
                         return false;
                    }
                } else if (platform === 'linggong') {
                    accountSelected = !!document.querySelector('input[name="linggong_binding_id"]:checked');
                     if (!accountSelected && document.querySelector('input[name="linggong_binding_id"]')) {
                         alert('请选择提现到的灵工平台账户');
                         e.preventDefault();
                         return false;
                    }
                }
            });
        });
        
        // 返回逻辑（优先返回历史记录，失败则跳转首页）
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/'; // 修改为你的默认回退地址
            }
        }

        // Swiper logic if needed
        // var noticeSwiper = new Swiper(...);
    </script>
</body>
</html>