<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 服务站和平台简历沟通管理
 * Class MessageController
 * @package Prime\Controller
 */
class MessageController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    public function index() {
        $c_kw = [
            'service_station_id' => '服务站ID',
            'user_job_id' => '简历ID',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_type = I("get.type");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);
        if ($s_type != '') $where['type'] = $s_type;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            $where[$s_kw] = $s_val;
        }
        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['create_time'] = ['between', [$s_start, $s_end]];
        }

        //公用搜索
        $contions = I('get.');
        $contionsWhere = ['type'];
        foreach ($contions as $whereKey => $row) {
            if (in_array($whereKey, $contionsWhere) && $row != '') {
                $where[$whereKey] = $row;
            }
        }

        $obj = D("ServiceStationPlatformMessage");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        if ($list) {
            //简历ID
            $jobArrId = array_unique(array_column($list, 'user_job_id'));
            $jobList = [];
            if ($jobArrId) {
                $jobList = D("UserJob")->where(['id' => ['in', $jobArrId]])->getField('id,name', true);
            }
            $this->assign('jobList', $jobList);
            //服务站ID
            $serviceStationArrid =  array_unique(array_column($list, 'service_station_id'));
            $serviceStationList = [];
            if ($jobArrId) {
                $serviceStationList = D("ServiceStation")->where(['id' => ['in', $serviceStationArrid]])->getField('id,service_name', true);
            }
            $this->assign('serviceStationList', $serviceStationList);
        }
        if ($s_kw == 'user_job_id' ) {
            $userJobId = $s_val;
        } else {
            $userJobId = 0;
        }

        $this->assign('userJobId', $userJobId);
        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '0');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '0');
        $this->assign('_get', I('get.'));
        $this->assign('typeList', $obj->type);
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 添加编辑
     */
    public function edit() {
        $id = intval(I('get.id'));
        $obj = D("ServiceStationPlatformMessage");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        } else {
           $this->error('参数错误');
        }
        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("message/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $jobRow = D("UserJob")->where(['id' => $row['user_job_id']])->find();
        $this->assign('jobRow', $jobRow);
        //服务站ID
        $serviceStationRow = D("ServiceStation")->where(['id' => $row['service_station_id']])->find();
        $this->assign('serviceStationRow', $serviceStationRow);

        $this->display();
    }

    /**
     * 回复功能
     */
    public function reply() {
        $user_job_id = intval(I('get.user_job_id'));
        $obj = D("ServiceStationPlatformMessage");
        if (!$user_job_id) $this->error('参数错误!!');
        $jobRow = D("UserJob")->where(['id' => $user_job_id])->find();

        if (IS_POST) {
            if ($data = $obj->create()) {
                $data['create_time'] = time();
                $data['type'] = 2; // 平台回复
                $data['service_station_id'] = $jobRow['service_station_id'];
                $data['user_job_id'] =$user_job_id;
                $obj->add($data);
                $this->success("添加留言成功", U("message/index")."?kw=user_job_id&val=".$user_job_id);
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->assign('jobRow', $jobRow);
        //服务站ID
        $serviceStationRow = D("ServiceStation")->where(['id' => $jobRow['service_station_id']])->find();
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->display();
    }
}