<?php

namespace Prime\Controller;

use Common\Controller\PrimeController;

class ServiceambassadorController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 招就办列表
     */
    public function index() {
        $c_kw = [
            'service_name' => '招就办名称',
            'contract_name' => '联系人姓名',
            'mobile' => '手机号码',
            'id' => 'ID',
        ];
        $where = ['zsb.zsb_type' => 2]; // 只显示招就办类型
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_station = I("get.station", ''); // 添加服务站筛选参数

        // 修复排序参数处理，确保字段名有效
        $s_sort = I("get.sort", 'id');
        // 允许的排序字段白名单
        $allowedSortFields = ['id', 'service_name', 'contract_name', 'mobile', 'create_time', 'status'];
        if (empty($s_sort) || !in_array($s_sort, $allowedSortFields)) {
            $s_sort = 'id'; // 默认按ID排序
        }

        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);
        
        if ($s_status != '') $where['zsb.status'] = $s_status;
        if ($s_station != '') $where['zsb.zsb_ref_station'] = $s_station; // 添加服务站筛选条件
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['service_name', 'contract_name']))  {
                $where['zsb.' . $s_kw] = ['like', "%$s_val%"];
            }  else {
                $where['zsb.' . $s_kw] = $s_val;
            }
        }

        if ($s_start && $s_end) {
            $where['zsb.create_time'] = ['between', [$s_start, $s_end]];
        } elseif ($s_start) {
            $where['zsb.create_time'] = ['egt', $s_start];
        } elseif ($s_end) {
            $where['zsb.create_time'] = ['elt', $s_end];
        }

        $obj = D("ServiceStation");

        // 为count查询构建简单的where条件（不带表别名）
        $countWhere = [];
        foreach ($where as $key => $value) {
            $newKey = str_replace('zsb.', '', $key);
            $countWhere[$newKey] = $value;
        }
        $count = $obj->where($countWhere)->count();
        $page = $this->page($count, 20); // 每页显示20条记录
        $sort_param = sortParam($s_sort, 'desc');

        // 关联查询服务站信息
        $list = $obj->alias('zsb')
            ->join('LEFT JOIN __SERVICE_STATION__ ss ON zsb.zsb_ref_station = ss.id AND ss.zsb_type = 1')
            ->field('zsb.*, ss.service_name as ref_station_name, COALESCE(zsb.is_disabled, 0) as is_disabled')
            ->where($where)
            ->order("zsb.{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->select();

        // 计算统计数据 - 基于所有招就办数据，不受搜索条件影响
        $baseWhere = ['zsb_type' => 2]; // 只统计招就办类型
        $stateCounts = [
            'total' => $obj->where($baseWhere)->count(), // 总数
            'pending' => $obj->where($baseWhere)->where(['status' => 0])->count(), // 待审核
            'approved' => $obj->where($baseWhere)->where(['status' => 1])->count(), // 审核成功
            'rejected' => $obj->where($baseWhere)->where(['status' => 2])->count()  // 审核失败
        ];

        // 为招就办提供专门的级别定义
        $zsbLevelList = [
            '1' => ['text' => '招就办主任', 'style' => 'success']
        ];
        // 获取服务站列表用于筛选
        $stationList = D("ServiceStation")->where([
            'zsb_type' => 1,
            'status' => 1
        ])->field('id, service_name')->order('id DESC')->select();

        $this->assign('levelList', $zsbLevelList);
        $this->assign('typeList', $obj->type);
        $this->assign('statusList', $obj->status);
        $this->assign('disabledStatusList', $obj->disabledStatus); // 添加禁用状态列表
        $this->assign('stationList', $stationList); // 添加服务站列表
        $this->assign('stateCounts', $stateCounts); // 将统计数据分配到前端
        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '0');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '0');
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 添加编辑招就办
     */
    public function edit() {
        $id = intval(I('get.id'));
        $obj = D("ServiceStation");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            // 确保只能编辑招就办类型的记录
            if ($row['zsb_type'] != 2) $this->error('该记录不是招就办类型');
            $this->assign('row',$row);
        }
        if (IS_POST) {
            if ($data = $obj->create()) {
                // 强制设置为招就办类型
                $data['zsb_type'] = 2;
                if (!$id) {
                    $data['create_time'] = time();
                    $insertId = $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("serviceambassador/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        
        // 为招就办提供专门的级别定义
        $zsbLevelList = [
            '1' => ['text' => '招就办主任', 'style' => 'success']
        ];

        // 获取服务站列表供选择
        $stationList = D("ServiceStation")->where(['zsb_type' => 1, 'status' => 1])
            ->field('id, service_name')
            ->order('id DESC')
            ->select();

        $this->assign('levelList', $zsbLevelList);
        $this->assign('typeList', $obj->type);
        $this->assign('statusList', $obj->status);
        $this->assign('stationList', $stationList);

        // 解决左侧菜单问题：手动设置当前菜单
        $menuModel = D("Menu");

        // 查找Serviceambassador/index菜单项（这里使用index作为基础，因为edit可能没有单独的菜单项）
        $menuItem = $menuModel->where(['app' => 'Prime', 'model' => 'Serviceambassador', 'action' => 'index'])->find();
        \Think\Log::write('Searching for menu: app=Prime, model=Serviceambassador, action=index. Result: '.($menuItem ? 'Found ID='.$menuItem['id'] : 'Not Found'), 'INFO');

        if ($menuItem) {
            // 手动构建完整的菜单信息
            $parentId = $menuItem['parentid'];
            $bootId = 0;

            \Think\Log::write('Menu item parent ID: '.$parentId, 'INFO');

            // 向上查找父级菜单，直到找到根菜单（parentid=0的菜单）
            if ($parentId > 0) {
                $bootId = $menuModel->getBoot($parentId);
                \Think\Log::write('Menu bootId for Serviceambassador/index: '.$bootId, 'INFO');
            } else {
                $bootId = $menuItem['id']; // 如果当前就是顶级菜单
                \Think\Log::write('Menu item is top level, using its ID as bootId: '.$bootId, 'INFO');
            }

            // 构建完整的菜单信息
            $menuInfo = [
                'id' => $menuItem['id'],
                'parentid' => $parentId,
                'boot_id' => $bootId ? $bootId : $menuItem['id'] // 如果bootId为0，则使用当前菜单ID
            ];

            \Think\Log::write('Setting menu info: '.json_encode($menuInfo), 'INFO');

            // 重新分配菜单变量到模板
            $this->assign('cur_menu', $menuInfo);

            // 确保main_menus变量中包含这个boot_id
            $mainMenus = session('main_menus');
            if ($mainMenus && !isset($mainMenus[$menuInfo['boot_id']])) {
                \Think\Log::write('Warning: boot_id '.$menuInfo['boot_id'].' not found in main_menus session variable', 'WARN');
                // 获取菜单树并重新设置session
                $mainMenus = $menuModel->menu_json();
                session('main_menus', $mainMenus);
                \Think\Log::write('Refreshed main_menus in session', 'INFO');
            }
        } else {
            \Think\Log::write('Failed to find menu item for Serviceambassador/index', 'WARN');
        }

        $this->display();
    }

    /**
     * 状态变更
     */
    public function cgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id, 'zsb_type' => 2])->find();
        if (!$row) $this->error('招就办记录不存在');
        if ($row['status'] == 1) $this->error('当前招就办已经审核通过');
        if (!in_array($status, [0,1,2])) $this->error('参数错误 ');
        
        $obj->save(['id' => $id, 'status' => $status]);
        $this->success('修改成功');
    }

    /**
     * 删除招就办
     */
    public function delete() {
        $id = I('get.id');
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id, 'zsb_type' => 2])->find();
        if (!$row) $this->error('招就办记录不存在');
        
        $obj->where(['id' => $id])->delete();
        $this->success('删除成功');
    }

    /**
     * 招就办价格配置页面
     */
    public function price_config()
    {
        $zsbId = I('get.zsb_id', 0, 'intval');
        if (!$zsbId) {
            $this->error('招就办ID不能为空');
        }

        // 验证招就办是否存在
        $obj = D("ServiceStation");
        $zsbInfo = $obj->where(['id' => $zsbId, 'zsb_type' => 2])->find();
        if (!$zsbInfo) {
            $this->error('招就办不存在');
        }

        // 获取价格配置列表（自动转换为元单位显示）
        $priceModel = D("ZsbPostPrice");
        $priceList = $priceModel->getZsbPostPriceList($zsbId, [], 1, 20, true);
        
        // DEBUG: 记录价格列表中的佣金值
        \Think\Log::write('价格列表佣金值: ' . json_encode(array_column($priceList['list'], 'commission')), 'INFO');
        
        // 获取可用岗位列表，包含服务价格信息
        $availablePosts = $priceModel->getAvailablePostsForZsb($zsbId);

        // 注意：服务价格在数据库中存储的就是元单位，不需要转换
        if ($availablePosts) {
            foreach ($availablePosts as &$post) {
                $post['service_price_yuan'] = $post['service_price']; // 直接使用，无需转换
            }
        }

        $this->assign('zsbInfo', $zsbInfo);
        $this->assign('priceList', $priceList);
        $this->assign('availablePosts', $availablePosts);
        $this->assign('statusList', $priceModel->status);

        // 解决左侧菜单问题：手动设置当前菜单
        $menuModel = D("Menu");

        // 查找Serviceambassador/index菜单项（这里使用index作为基础，因为price_config可能没有单独的菜单项）
        $menuItem = $menuModel->where(['app' => 'Prime', 'model' => 'Serviceambassador', 'action' => 'index'])->find();
        \Think\Log::write('Searching for menu: app=Prime, model=Serviceambassador, action=index. Result: '.($menuItem ? 'Found ID='.$menuItem['id'] : 'Not Found'), 'INFO');

        if ($menuItem) {
            // 手动构建完整的菜单信息
            $parentId = $menuItem['parentid'];
            $bootId = 0;

            \Think\Log::write('Menu item parent ID: '.$parentId, 'INFO');

            // 向上查找父级菜单，直到找到根菜单（parentid=0的菜单）
            if ($parentId > 0) {
                $bootId = $menuModel->getBoot($parentId);
                \Think\Log::write('Menu bootId for Serviceambassador/price_config: '.$bootId, 'INFO');
            } else {
                $bootId = $menuItem['id']; // 如果当前就是顶级菜单
                \Think\Log::write('Menu item is top level, using its ID as bootId: '.$bootId, 'INFO');
            }

            // 构建完整的菜单信息
            $menuInfo = [
                'id' => $menuItem['id'],
                'parentid' => $parentId,
                'boot_id' => $bootId ? $bootId : $menuItem['id'] // 如果bootId为0，则使用当前菜单ID
            ];

            \Think\Log::write('Setting menu info: '.json_encode($menuInfo), 'INFO');

            // 重新分配菜单变量到模板
            $this->assign('cur_menu', $menuInfo);

            // 确保main_menus变量中包含这个boot_id
            $mainMenus = session('main_menus');
            if ($mainMenus && !isset($mainMenus[$menuInfo['boot_id']])) {
                \Think\Log::write('Warning: boot_id '.$menuInfo['boot_id'].' not found in main_menus session variable', 'WARN');
                // 获取菜单树并重新设置session
                $mainMenus = $menuModel->menu_json();
                session('main_menus', $mainMenus);
                \Think\Log::write('Refreshed main_menus in session', 'INFO');
            }
        } else {
            \Think\Log::write('Failed to find menu item for Serviceambassador/price_config', 'WARN');
        }

        $this->display();
    }

    /**
     * 设置招就办岗位价格 (AJAX)
     */
    public function setPriceAjax()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        // 注意：前端传入的价格单位为元，Model层会自动转换为分存储
        $data = [
            'zsb_id' => I('post.zsb_id', 0, 'intval'),
            'post_id' => I('post.post_id', 0, 'intval'),
            'cost_price' => I('post.cost_price', 0, 'intval'), // 元单位
            'sale_price' => I('post.sale_price', 0, 'intval'), // 元单位
            'status' => I('post.status', 1, 'intval')
        ];

        // 验证招就办是否存在
        $zsbInfo = D("ServiceStation")->where([
            'id' => $data['zsb_id'],
            'zsb_type' => 2
        ])->find();

        if (!$zsbInfo) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办不存在']);
        }

        $priceModel = D("ZsbPostPrice");

        // 验证数据
        if (!$priceModel->validatePriceConfig($data)) {
            $this->ajaxReturn(['status' => 0, 'msg' => $priceModel->getError()]);
        }

        // 设置价格
        $result = $priceModel->setZsbPostPrice($data);
        if ($result) {
            $this->ajaxReturn(['status' => 1, 'msg' => '价格设置成功']);
        } else {
            $this->ajaxReturn(['status' => 0, 'msg' => $priceModel->getError() ?: '价格设置失败']);
        }
    }

    /**
     * 设置招就办禁用状态
     */
    public function setZsbDisabled() {
        $id = I('get.id');
        $disabled = I('get.disabled');
        if (!$id) $this->error('参数错误');

        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id, 'zsb_type' => 2])->find();
        if (!$row) $this->error('招就办不存在');

        if (!in_array($disabled, [0, 1])) $this->error('参数错误');

        $result = $obj->setZsbDisabledStatus($id, $disabled);
        if ($result) {
            $statusText = $disabled ? '禁用' : '启用';
            $this->success($statusText . '成功');
        } else {
            $this->error('操作失败');
        }
    }
}
