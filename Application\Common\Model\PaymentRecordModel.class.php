<?php
namespace Common\Model;

use Think\Model;

class PaymentRecordModel extends Model
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['pay_time', 'time', self::MODEL_INSERT, 'function'],
    ];

    // 支付渠道
    public $pay_channel = [
        'offline' => '线下支付',
        'alipay' => '支付宝',
        'wechat' => '微信支付',
        'bank' => '银行转账',
        'free' => '公益免费',
        'manual' => '手动录入',
    ];

    // 支付类型（新增）
    public $pay_type = [
        'intent' => ['text' => '意向金', 'style' => 'info'],
        'partial' => ['text' => '部分付款', 'style' => 'warning'],
        'full' => ['text' => '全额付款', 'style' => 'success'],
        'refund' => ['text' => '退款', 'style' => 'danger']
    ];

    /**
     * 创建支付记录
     * @param array $data 支付数据
     * @return int|bool 成功返回记录ID，失败返回false
     */
    public function createRecord($data)
    {
        if ($this->create($data)) {
            return $this->add();
        }

        return false;
    }

    /**
     * 获取订单支付记录
     * @param int $orderId 订单ID
     * @return array 支付记录
     */
    public function getRecordsByOrderId($orderId)
    {
        $list = $this->where(['order_id' => $orderId])->order('id desc')->select();

        if (!$list) {
            return [];
        }

        foreach ($list as &$item) {
            $item['pay_channel_text'] = isset($this->pay_channel[$item['pay_channel']]) ?
                $this->pay_channel[$item['pay_channel']] : $item['pay_channel'];
            $item['pay_time_text'] = date('Y-m-d H:i:s', $item['pay_time']);

            // 新增：支付类型信息
            $payType = $item['pay_type'] ?? 'full';
            $item['pay_type_text'] = $this->pay_type[$payType]['text'] ?? '未知';
            $item['pay_type_style'] = $this->pay_type[$payType]['style'] ?? 'default';
            $item['pay_amount_yuan'] = round($item['pay_amount'] / 100, 2);
        }

        return $list;
    }

    /**
     * 获取订单的分阶段付款金额（新增）
     * @param int $orderId 订单ID
     * @return array 分阶段付款统计
     */
    public function getOrderPaymentAmounts($orderId)
    {
        // 获取意向金总额
        $intentAmount = $this->where([
            'order_id' => $orderId,
            'pay_type' => 'intent'
        ])->sum('pay_amount') ?: 0;

        // 获取部分付款总额
        $partialAmount = $this->where([
            'order_id' => $orderId,
            'pay_type' => 'partial'
        ])->sum('pay_amount') ?: 0;

        // 获取全额付款总额
        $fullAmount = $this->where([
            'order_id' => $orderId,
            'pay_type' => 'full'
        ])->sum('pay_amount') ?: 0;

        // 获取退款总额
        $refundAmount = $this->where([
            'order_id' => $orderId,
            'pay_type' => 'refund'
        ])->sum('pay_amount') ?: 0;

        // 计算净付款金额
        $totalPaid = $intentAmount + $partialAmount + $fullAmount - $refundAmount;

        return [
            'intent_amount' => $intentAmount, // 分单位
            'partial_amount' => $partialAmount, // 分单位
            'full_amount' => $fullAmount, // 分单位
            'refund_amount' => $refundAmount, // 分单位
            'total_paid' => $totalPaid, // 分单位
            'intent_amount_yuan' => round($intentAmount / 100, 2), // 元单位
            'partial_amount_yuan' => round($partialAmount / 100, 2), // 元单位
            'full_amount_yuan' => round($fullAmount / 100, 2), // 元单位
            'refund_amount_yuan' => round($refundAmount / 100, 2), // 元单位
            'total_paid_yuan' => round($totalPaid / 100, 2) // 元单位
        ];
    }

    /**
     * 创建付款记录（新增）
     * @param array $data 付款数据
     * @return int|bool 成功返回记录ID，失败返回false
     */
    public function createPaymentRecord($data)
    {
        // 验证必要字段
        if (empty($data['order_id']) || empty($data['pay_amount']) || !isset($data['pay_type'])) {
            \Think\Log::write('创建付款记录失败：缺少必要字段', 'ERROR');
            return false;
        }

        // 设置默认值
        $data['pay_channel'] = $data['pay_channel'] ?? 'manual';
        $data['pay_time'] = $data['pay_time'] ?? time();
        $data['remark'] = $data['remark'] ?? '';

        // 确保金额为分单位
        if (is_float($data['pay_amount']) && $data['pay_amount'] < 10000) {
            // 可能是元单位，转换为分单位
            $data['pay_amount'] = intval($data['pay_amount'] * 100);
        }

        if ($this->create($data)) {
            $recordId = $this->add();
            if ($recordId) {
                \Think\Log::write('创建付款记录成功 record_id=' . $recordId . ' order_id=' . $data['order_id'] . ' amount=' . $data['pay_amount'] . ' type=' . $data['pay_type'], 'INFO');
                return $recordId;
            }
        }

        \Think\Log::write('创建付款记录失败 order_id=' . $data['order_id'], 'ERROR');
        return false;
    }
}
