<include file="block/hat" />
<script type="text/javascript" src="__ROOT__/static/js/lib/jquery-ui-1.10.3.min.js"></script>

<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<style type='text/css'>
				/* 重置和基础样式 */
				* {
					box-sizing: border-box;
				}

				body {
					font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
					line-height: 1.6;
					color: #2d3748;
				}

				.rbac-edit-container {
					padding: 2rem;
					background: #f7fafc;
					min-height: 100vh;
					font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
				}

				/* 现代化标签页 */
				.rbac-tabs {
					background: white;
					border-radius: 1rem 1rem 0 0;
					box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					border-bottom: none;
					overflow: hidden;
					margin-bottom: 0;
				}

				.rbac-tab-list {
					display: flex;
					margin: 0;
					padding: 0;
					list-style: none;
				}

				.rbac-tab-item {
					flex: 1;
				}

				.rbac-tab-link {
					display: block;
					padding: 1.5rem 2rem;
					color: #4a5568;
					text-decoration: none;
					font-weight: 600;
					font-size: 1.5rem;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					border-right: 1px solid #e2e8f0;
					position: relative;
					text-align: center;
				}

				.rbac-tab-item:last-child .rbac-tab-link {
					border-right: none;
				}

				.rbac-tab-link:hover {
					background: #f7fafc;
					color: #667eea;
					text-decoration: none;
				}

				.rbac-tab-link.active {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.rbac-tab-link.active::after {
					content: '';
					position: absolute;
					bottom: -1px;
					left: 0;
					right: 0;
					height: 2px;
					background: white;
				}

				/* 页面头部 */
				.rbac-page-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin: 2rem 0;
					flex-wrap: wrap;
					gap: 1.5rem;
				}

				.rbac-page-title-section {
					display: flex;
					flex-direction: column;
					gap: 0.5rem;
				}

				.rbac-page-title {
					font-size: 2rem;
					font-weight: 700;
					color: #1a202c;
					margin: 0;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.rbac-page-subtitle {
					color: #4a5568;
					font-size: 1.5rem;
					margin: 0;
				}

				.rbac-page-actions {
					display: flex;
					gap: 1rem;
					flex-wrap: wrap;
				}

				/* 现代化表单卡片 */
				.rbac-form-card {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					overflow: hidden;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
				}

				.rbac-form-card::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4px;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				}

				.rbac-form-header {
					padding: 2rem 2rem 1rem 2rem;
					border-bottom: 1px solid #f1f5f9;
				}

				.rbac-form-title {
					font-size: 1.5rem;
					font-weight: 600;
					color: #1a202c;
					margin: 0;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.rbac-form-description {
					color: #4a5568;
					font-size: 1.5rem;
					margin: 0.5rem 0 0 0;
				}

				.rbac-form-body {
					padding: 2rem;
				}

				/* 现代化表单组 */
				.rbac-form-group {
					margin-bottom: 2rem;
				}

				.rbac-form-label {
					display: block;
					font-weight: 600;
					color: #2d3748;
					margin-bottom: 0.75rem;
					font-size: 1.5rem;
					display: flex;
					align-items: center;
					gap: 0.5rem;
				}

				.rbac-form-label .required {
					color: #f56565;
					font-weight: 700;
				}

				.rbac-form-control {
					width: 100%;
					padding: 0.75rem 1rem;
					border: 2px solid #e2e8f0;
					border-radius: 0.5rem;
					font-size: 1.5rem;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					background: white;
					font-family: inherit;
				}

				.rbac-form-control:focus {
					outline: none;
					border-color: #667eea;
					box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
					background: #fafafa;
				}

				.rbac-form-control:hover {
					border-color: #cbd5e0;
				}

				.rbac-textarea {
					resize: vertical;
					min-height: 120px;
				}

				.rbac-select {
					background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
					background-position: right 0.5rem center;
					background-repeat: no-repeat;
					background-size: 1.5em 1.5em;
					padding-right: 2.5rem;
					appearance: none;
				}

				/* 现代化单选按钮 */
				.rbac-radio-group {
					display: flex;
					gap: 1.5rem;
					flex-wrap: wrap;
				}

				.rbac-radio-item {
					display: flex;
					align-items: center;
					gap: 0.75rem;
					padding: 0.75rem 1rem;
					border: 2px solid #e2e8f0;
					border-radius: 0.5rem;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					background: white;
					min-width: 120px;
				}

				.rbac-radio-item:hover {
					border-color: #667eea;
					background: #f7fafc;
				}

				.rbac-radio-item.active {
					border-color: #667eea;
					background: rgba(102, 126, 234, 0.1);
				}

				.rbac-radio-input {
					width: 1.25rem;
					height: 1.25rem;
					border: 2px solid #e2e8f0;
					border-radius: 50%;
					position: relative;
					appearance: none;
					cursor: pointer;
					transition: all 0.3s ease;
				}

				.rbac-radio-input:checked {
					border-color: #667eea;
					background: #667eea;
				}

				.rbac-radio-input:checked::after {
					content: '';
					position: absolute;
					top: 50%;
					left: 50%;
					width: 6px;
					height: 6px;
					border-radius: 50%;
					background: white;
					transform: translate(-50%, -50%);
				}

				.rbac-radio-label {
					font-weight: 500;
					color: #2d3748;
					cursor: pointer;
				}

				/* 表单帮助文本 */
				.rbac-help-text {
					font-size: 1.5rem;
					color: #718096;
					margin-top: 0.5rem;
				}

				.rbac-error-text {
					font-size: 1.5rem;
					color: #f56565;
					margin-top: 0.5rem;
				}

				/* 现代化按钮 */
				.rbac-btn {
					display: inline-flex;
					align-items: center;
					justify-content: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					border: none;
					border-radius: 0.5rem;
					font-weight: 600;
					font-size: 1.5rem;
					text-decoration: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					overflow: hidden;
					font-family: inherit;
				}

				.rbac-btn:before {
					content: '';
					position: absolute;
					top: 0;
					left: -100%;
					width: 100%;
					height: 100%;
					background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.rbac-btn:hover:before {
					left: 100%;
				}

				.rbac-btn-primary {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.rbac-btn-primary:hover {
					background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
					transform: translateY(-1px);
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					color: white;
					text-decoration: none;
				}

				.rbac-btn-secondary {
					background: #f1f5f9;
					color: #4a5568;
					border: 1px solid #e2e8f0;
				}

				.rbac-btn-secondary:hover {
					background: #e2e8f0;
					transform: translateY(-1px);
					color: #2d3748;
					text-decoration: none;
				}

				.rbac-btn-lg {
					padding: 1rem 2rem;
					font-size: 1.5rem;
				}

				/* 表单操作区域 */
				.rbac-form-actions {
					padding: 1.5rem 2rem 2rem 2rem;
					background: #f7fafc;
					border-top: 1px solid #e2e8f0;
					display: flex;
					gap: 1rem;
					justify-content: flex-end;
					flex-wrap: wrap;
				}

				/* 响应式设计 */
				@media (max-width: 768px) {
					.rbac-edit-container {
						padding: 1.5rem;
					}

					.rbac-page-header {
						flex-direction: column;
						align-items: flex-start;
					}

					.rbac-page-title {
						font-size: 1.5rem;
					}

					.rbac-form-header,
					.rbac-form-body,
					.rbac-form-actions {
						padding: 1.5rem;
					}

					.rbac-radio-group {
						flex-direction: column;
					}

					.rbac-radio-item {
						min-width: auto;
					}

					.rbac-form-actions {
						flex-direction: column;
					}

					.rbac-btn {
						width: 100%;
						justify-content: center;
					}
				}

				/* 动画效果 */
				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(20px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}

				.rbac-fade-in {
					animation: fadeInUp 0.5s ease-out;
				}

				.rbac-fade-in-delay-1 {
					animation: fadeInUp 0.5s ease-out 0.1s both;
				}

				.rbac-fade-in-delay-2 {
					animation: fadeInUp 0.5s ease-out 0.2s both;
				}

				/* 表单验证状态 */
				.rbac-form-control.error {
					border-color: #f56565;
					box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
				}

				.rbac-form-control.success {
					border-color: #48bb78;
					box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
				}

				/* 加载状态 */
				.rbac-btn.loading {
					pointer-events: none;
					opacity: 0.7;
				}

				.rbac-btn.loading::after {
					content: '';
					position: absolute;
					width: 16px;
					height: 16px;
					margin: auto;
					border: 2px solid transparent;
					border-top-color: currentColor;
					border-radius: 50%;
					animation: spin 1s linear infinite;
				}

				@keyframes spin {
					0% { transform: rotate(0deg); }
					100% { transform: rotate(360deg); }
				}
			</style>

			<div class="rbac-edit-container rbac-fade-in">
				<!-- 现代化标签页 -->
				<div class="rbac-tabs">
					<ul class="rbac-tab-list">
						<li class="rbac-tab-item">
							<a href="{:U('rbac/edit')}" class="rbac-tab-link <php>if(ACTION_NAME=='edit' && !I('get.id')) echo 'active'</php>">
								<i class="fa fa-plus"></i>
								添加部门
							</a>
						</li>
						<li class="rbac-tab-item">
							<a href="{:U('rbac/index')}" class="rbac-tab-link <php>if(ACTION_NAME=='index') echo 'active'</php>">
								<i class="fa fa-building"></i>
								部门管理
							</a>
						</li>
					</ul>
				</div>

				<!-- 页面头部 -->
				<div class="rbac-page-header" style="margin-top: 2rem;">
					<div class="rbac-page-title-section">
						<h1 class="rbac-page-title">
							<i class="fa fa-<php>echo I('get.id') ? 'edit' : 'plus';</php>"></i>
							<php>echo I('get.id') ? '编辑部门' : '添加部门';</php>
						</h1>
						<p class="rbac-page-subtitle">
							<php>echo I('get.id') ? '修改部门信息和权限配置' : '创建新的部门并设置基本信息';</php>
						</p>
					</div>
					<div class="rbac-page-actions">
						<a href="{:U('rbac/index')}" class="rbac-btn rbac-btn-secondary">
							<i class="fa fa-arrow-left"></i>
							返回列表
						</a>
					</div>
				</div>

				<!-- 现代化表单 -->
				<div class="rbac-form-card rbac-fade-in-delay-1">
					<div class="rbac-form-header">
						<h2 class="rbac-form-title">
							<i class="fa fa-info-circle"></i>
							部门基本信息
						</h2>
						<p class="rbac-form-description">
							请填写部门的基本信息，带 <span style="color: #f56565;">*</span> 的字段为必填项
						</p>
					</div>

					<form action="" method="post" class="js-ajax-form" enctype="multipart/form-data" id="rbac-form">
						<div class="rbac-form-body">
							<!-- 部门名称 -->
							<div class="rbac-form-group">
								<label class="rbac-form-label">
									<i class="fa fa-building"></i>
									部门名称
									<span class="required">*</span>
								</label>
								<input type="text"
									   name="name"
									   class="rbac-form-control"
									   value="{$row.name}"
									   placeholder="请输入部门名称"
									   required />
								<div class="rbac-help-text">部门名称将显示在组织架构中，建议使用简洁明了的名称</div>
							</div>

							<!-- 部门描述 -->
							<div class="rbac-form-group">
								<label class="rbac-form-label">
									<i class="fa fa-file-text"></i>
									部门描述
								</label>
								<textarea name="remark"
										  class="rbac-form-control rbac-textarea"
										  placeholder="请输入部门描述信息（可选）">{$row.remark}</textarea>
								<div class="rbac-help-text">详细描述部门的职能和职责，有助于其他用户了解部门作用</div>
							</div>

							<!-- 上级部门 -->
							<div class="rbac-form-group">
								<label class="rbac-form-label">
									<i class="fa fa-sitemap"></i>
									所属上级部门
								</label>
								<select name="pid" class="rbac-form-control rbac-select">
									<php>foreach($pidList as $key => $val) {</php>
									<option value="{$key}" {: $key== $row['pid'] ? 'selected' : ''}>{$val}</option>
									<php>}</php>
								</select>
								<div class="rbac-help-text">选择该部门的上级部门，用于构建组织层级关系</div>
							</div>

							<!-- 部门状态 -->
							<div class="rbac-form-group">
								<label class="rbac-form-label">
									<i class="fa fa-toggle-on"></i>
									部门状态
								</label>
								<div class="rbac-radio-group">
									<label class="rbac-radio-item <php>if($row['status']) echo 'active';</php>">
										<input type="radio"
											   name="status"
											   value="1"
											   class="rbac-radio-input"
											   <php>if($row['status']) echo 'checked';</php> />
										<span class="rbac-radio-label">
											<i class="fa fa-check-circle" style="color: #48bb78;"></i>
											正常
										</span>
									</label>
									<label class="rbac-radio-item <php>if(!$row['status']) echo 'active';</php>">
										<input type="radio"
											   name="status"
											   value="0"
											   class="rbac-radio-input"
											   <php>if(!$row['status']) echo 'checked';</php> />
										<span class="rbac-radio-label">
											<i class="fa fa-times-circle" style="color: #f56565;"></i>
											禁用
										</span>
									</label>
								</div>
								<div class="rbac-help-text">禁用的部门将无法正常使用系统功能</div>
							</div>
						</div>

						<!-- 表单操作区域 -->
						<div class="rbac-form-actions">
							<input type="hidden" name="id" value="{$row.id}" />
							<button type="button" onclick="history.back()" class="rbac-btn rbac-btn-secondary">
								<i class="fa fa-times"></i>
								返回
							</button>
							<button type="submit" name="submit" class="rbac-btn rbac-btn-primary rbac-btn-lg js-ajax-submit">
								<i class="fa fa-save"></i>
								<php>echo I('get.id') ? '保存修改' : '添加部门';</php>
							</button>
						</div>
					</form>
				</div>

				<!-- 操作提示卡片 -->
				<div class="rbac-form-card rbac-fade-in-delay-2" style="margin-top: 1.5rem;">
					<div class="rbac-form-header">
						<h2 class="rbac-form-title">
							<i class="fa fa-lightbulb-o"></i>
							操作提示
						</h2>
					</div>
					<div class="rbac-form-body">
						<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
							<div style="padding: 1rem; background: #f0fff4; border-left: 4px solid #48bb78; border-radius: 0.5rem;">
								<h4 style="margin: 0 0 0.5rem 0; color: #2f855a; font-size: 1.5rem; font-weight: 600;">
									<i class="fa fa-check-circle"></i>
									创建成功后
								</h4>
								<p style="margin: 0; color: #2f855a; font-size: 1.5rem;">
									部门创建成功后，您可以为该部门配置具体的权限和分配用户
								</p>
							</div>
							<div style="padding: 1rem; background: #fffbf0; border-left: 4px solid #ed8936; border-radius: 0.5rem;">
								<h4 style="margin: 0 0 0.5rem 0; color: #c05621; font-size: 1.5rem; font-weight: 600;">
									<i class="fa fa-exclamation-triangle"></i>
									注意事项
								</h4>
								<p style="margin: 0; color: #c05621; font-size: 1.5rem;">
									部门名称一旦创建建议不要频繁修改，以免影响用户使用体验
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>

		</div>
	</div>
</div>

<script src="__ROOT__/static/js/prime/common.js"></script>
<link rel="stylesheet" href="__ROOT__/static/js/app/layer/skin/layer.css" />

<script>
	require(['layer'], function(layer) {
		$(function(){
			// 表单验证和交互
			var $form = $('#rbac-form');
			var $submitBtn = $('.js-ajax-submit');

			// 单选按钮交互
			$('.rbac-radio-item').click(function() {
				var $item = $(this);
				var $input = $item.find('input[type="radio"]');
				var name = $input.attr('name');

				// 移除同组其他选项的active状态
				$('input[name="' + name + '"]').closest('.rbac-radio-item').removeClass('active');

				// 设置当前选项为active
				$item.addClass('active');
				$input.prop('checked', true);
			});

			// 表单字段实时验证
			$('.rbac-form-control').on('input blur', function() {
				var $field = $(this);
				var value = $field.val().trim();
				var isRequired = $field.prop('required');

				// 移除之前的验证状态
				$field.removeClass('error success');
				$field.siblings('.rbac-error-text').remove();

				if (isRequired && !value) {
					$field.addClass('error');
					$field.after('<div class="rbac-error-text"><i class="fa fa-exclamation-circle"></i> 此字段为必填项</div>');
				} else if (value) {
					$field.addClass('success');
				}
			});

			// 表单提交处理
			$form.on('submit', function(e) {
				e.preventDefault();

				// 验证必填字段
				var hasError = false;
				$('.rbac-form-control[required]').each(function() {
					var $field = $(this);
					var value = $field.val().trim();

					if (!value) {
						$field.addClass('error');
						$field.siblings('.rbac-error-text').remove();
						$field.after('<div class="rbac-error-text"><i class="fa fa-exclamation-circle"></i> 此字段为必填项</div>');
						hasError = true;
					}
				});

				if (hasError) {
					layer.msg('请填写所有必填字段', {icon: 2});
					return false;
				}

				// 设置提交按钮为加载状态
				$submitBtn.addClass('loading');
				$submitBtn.prop('disabled', true);
				var originalText = $submitBtn.html();
				$submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

				// 提交表单
				$.ajax({
					url: $form.attr('action') || window.location.href,
					type: 'POST',
					data: $form.serialize(),
					dataType: 'json',
					success: function(response) {
						if (response.status === 1 || response.code === 1) {
							layer.msg(response.info || '操作成功', {icon: 1}, function() {
								window.location.href = "{:U('rbac/index')}";
							});
						} else {
							layer.msg(response.info || '操作失败', {icon: 2});
						}
					},
					error: function() {
						layer.msg('网络错误，请重试', {icon: 2});
					},
					complete: function() {
						// 恢复提交按钮状态
						$submitBtn.removeClass('loading');
						$submitBtn.prop('disabled', false);
						$submitBtn.html(originalText);
					}
				});
			});

			// 页面加载动画
			setTimeout(function() {
				$('.rbac-form-card').addClass('rbac-fade-in');
			}, 100);

			// 表单字段聚焦效果
			$('.rbac-form-control').on('focus', function() {
				$(this).closest('.rbac-form-group').addClass('focused');
			}).on('blur', function() {
				$(this).closest('.rbac-form-group').removeClass('focused');
			});

			// 自动保存草稿（可选功能）
			var autoSaveTimer;
			$('.rbac-form-control').on('input', function() {
				clearTimeout(autoSaveTimer);
				autoSaveTimer = setTimeout(function() {
					// 这里可以实现自动保存草稿的功能
					console.log('Auto save draft...');
				}, 3000);
			});

			// 键盘快捷键
			$(document).on('keydown', function(e) {
				// Ctrl+S 保存
				if (e.ctrlKey && e.keyCode === 83) {
					e.preventDefault();
					$form.submit();
				}
				// Esc 取消
				if (e.keyCode === 27) {
					if (confirm('确定要取消编辑吗？未保存的更改将丢失。')) {
						history.back();
					}
				}
			});

			// 表单数据变化检测
			var originalFormData = $form.serialize();
			var hasUnsavedChanges = false;

			$('.rbac-form-control').on('input change', function() {
				hasUnsavedChanges = ($form.serialize() !== originalFormData);
			});

			// 页面离开提醒
			$(window).on('beforeunload', function() {
				if (hasUnsavedChanges) {
					return '您有未保存的更改，确定要离开吗？';
				}
			});

			// 成功提交后清除未保存标记
			$form.on('submit', function() {
				hasUnsavedChanges = false;
			});
		});
	});
</script>

<include file="block/footer" />