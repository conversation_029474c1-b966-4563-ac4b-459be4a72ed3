<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>收益计算器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="http://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: linear-gradient(135deg, #2c3e50 0%, #4a6491 100%);
            min-height: 100vh;
            padding: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #333;
        }
        
        .wechat-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #fff;
            z-index: 1000;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 30px;
            text-align: center;
        }
        
        .wechat-container i {
            font-size: 5rem;
            color: #07C160;
            margin-bottom: 20px;
        }
        
        .wechat-container h2 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .wechat-container p {
            font-size: 1rem;
            color: #7f8c8d;
            line-height: 1.6;
            max-width: 300px;
        }
        
        .container {
            width: 100%;
            max-width: 480px;
            background: #fff;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            display: none;
            position: relative;
        }
        
        .share-warning {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            padding: 12px;
            text-align: center;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            z-index: 100;
        }
        
        .share-warning i {
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        header {
            background: linear-gradient(to right, #07C160, #05a854);
            color: white;
            padding: 25px 20px;
            text-align: center;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 40px;
        }
        
        .avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 15px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        
        .avatar i {
            font-size: 2.5rem;
            color: #07C160;
        }
        
        h1 {
            font-size: 1.8rem;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .subtitle {
            font-size: 0.95rem;
            opacity: 0.9;
            max-width: 320px;
            margin: 0 auto;
            line-height: 1.5;
        }
        
        .content {
            padding: 20px;
        }
        
        .section-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #07C160;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-title i {
            color: #07C160;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1rem;
        }
        
        .input-container {
            position: relative;
        }
        
        input {
            width: 100%;
            padding: 14px 15px 14px 50px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 1.1rem;
            transition: all 0.3s;
            background: #f8f9fa;
        }
        
        input:focus {
            outline: none;
            border-color: #07C160;
            background: #fff;
            box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.2);
        }
        
        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #07C160;
            font-size: 1.2rem;
        }
        
        .input-unit {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 1rem;
        }
        
        .result-cards {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-top: 10px;
        }
        
        .result-card {
            background: #fff;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #07C160;
        }
        
        .result-card.your {
            border-left-color: #3498db;
        }
        
        .result-card.intermediary {
            border-left-color: #9b59b6;
        }
        
        .result-title {
            font-size: 1rem;
            font-weight: 600;
            color: #7f8c8d;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .result-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 5px 0;
        }
        
        .positive {
            color: #27ae60;
        }
        
        .negative {
            color: #e74c3c;
        }
        
        .explanation {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px dashed #eee;
            color: #7f8c8d;
            font-size: 0.85rem;
            line-height: 1.5;
        }
        
        .btn-calculate {
            background: linear-gradient(to right, #07C160, #05a854);
            color: white;
            border: none;
            padding: 16px 25px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 12px;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            box-shadow: 0 4px 10px rgba(7, 193, 96, 0.4);
            margin: 25px 0 15px;
        }
        
        .btn-calculate:active {
            transform: translateY(2px);
            box-shadow: 0 2px 5px rgba(7, 193, 96, 0.4);
        }
        
        .formula-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
        }
        
        .formula-title {
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .formula-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-top: 10px;
        }
        
        .formula-card {
            background: #fff;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border-left: 3px solid #07C160;
        }
        
        .formula-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            color: #07C160;
            font-weight: 600;
        }
        
        .formula-content {
            font-size: 0.95rem;
            line-height: 1.5;
            color: #2c3e50;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: rgba(255,255,255,0.7);
            font-size: 0.85rem;
            background: rgba(0,0,0,0.2);
            border-radius: 0 0 20px 20px;
        }
        
        .share-block-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.85);
            z-index: 1000;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 30px;
            text-align: center;
            color: white;
        }
        
        .share-block-content {
            background: #fff;
            border-radius: 20px;
            padding: 30px;
            max-width: 320px;
            color: #333;
        }
        
        .share-block-content i {
            font-size: 3rem;
            color: #e74c3c;
            margin-bottom: 20px;
        }
        
        @media (max-width: 480px) {
            header {
                padding: 20px 15px;
            }
            
            h1 {
                font-size: 1.6rem;
            }
            
            .subtitle {
                font-size: 0.9rem;
            }
            
            .content {
                padding: 15px;
            }
        }

                /* 返回箭头样式 */
.back-arrow {
    position: fixed;
    top: 115px;
    left: 15px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.2s;
}

.back-arrow::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    width: 12px;
    height: 12px;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: translateY(-50%) rotate(45deg);
}

/* 点击反馈效果 */
.back-arrow:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.7);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .back-arrow {
        top: 140px;
        left: 10px;
        width: 36px;
        height: 36px;
    }
    
    .back-arrow::before {
        left: 13px;
        width: 10px;
        height: 10px;
    }
}
    </style>
</head>
<body>
                <!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
    <!-- 微信提示页面 -->
    <div class="wechat-container" id="wechat-alert">
        <i class="fab fa-weixin"></i>
        <h2>请在微信中打开</h2>
        <p>此页面专为微信优化，请在微信内置浏览器中访问以获得最佳体验</p>
        <p style="margin-top: 20px; color: #e74c3c;">检测到您当前不在微信中，无法使用本工具</p>
    </div>
    
    <!-- 分享阻止覆盖层 -->
    <div class="share-block-overlay" id="share-block">
        <div class="share-block-content">
            <i class="fas fa-ban"></i>
            <h3>禁止分享</h3>
            <p>此页面包含敏感数据，禁止分享！</p>
            <p style="margin-top: 15px; font-size: 0.9rem; color: #7f8c8d;">
                请关闭分享菜单继续使用本工具
            </p>
            <button class="btn-calculate" style="margin-top: 20px;" onclick="document.getElementById('share-block').style.display='none'">
                <i class="fas fa-times"></i> 关闭提示
            </button>
        </div>
    </div>



    <!-- 主计算器容器 -->
    <div class="container" id="calculator">
        <div class="share-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <span>此页面包含敏感数据，禁止分享给他人！</span>
        </div>
        
        <header>
   
            <h1 style="margin-bottom: 12px;">收益计算器</h1>
                     <div class="avatar">
                <i class="fas fa-calculator"></i>
            </div>
            <h4 style="color:#000;margin-bottom: 12px;">{:$postname}</h4>
            <p class="subtitle">输入参数，实时计算您与合伙人的收益与成本</p>
        </header>
        
        <div class="content">
            <h3 class="section-title"><i class="fas fa-edit"></i> 参数设置说明<BR>
              <div style="font-size: 14PX;color: #c4570e;">1、您给合伙人的价格的不能低于收费区间最低价；<br>2、合伙人对外实际收费金额不得高于收费区间最高价1.5倍</div>

            </h3>

            
            <div class="input-group" style="display: none;">
                <label for="service-cost"><i class="fas fa-tag"></i> 服务站成本价</label>
                <div class="input-container">
                    <i class="fas fa-yuan-sign input-icon"></i>
                    <input type="number" id="service-cost" placeholder="输入服务站成本价" value="{:$price}">
                    <span class="input-unit">元</span>
                </div>
            </div>
            
                        
            <div class="input-group" style="display: none;">
                <label for="min-charge"><i class="fas fa-arrow-down"></i> 收费区间最低价</label>
                <div class="input-container">
                    <i class="fas fa-yuan-sign input-icon"></i>
                    <input type="number" id="min-charge" placeholder="输入最低收费价格" value="{:$mincharge}">
                    <span class="input-unit">元</span>
                </div>
            </div>


            <div class="input-group">
                <label for="intermediary-cost"><i class="fas fa-handshake"></i> 您给合伙人的价格</label>
                    <div style="width:100%;font-size: 14px;text-align: right;">可修改，默认为区间最低价+2W（{:$mincharge}）</div>
                <div class="input-container">
                    <i class="fas fa-yuan-sign input-icon"></i>
                    <input type="number" id="intermediary-cost" placeholder="输入您给合伙人的价格" value="{:$mincharge+20000 ?: 0}">
                    <span class="input-unit">元</span>
                </div>
            </div>

            
            <div class="input-group">
                <label for="actual-charge"><i class="fas fa-credit-card"></i> 合伙人对外实际收费金额</label>
                    <div style="width:100%;font-size: 14px;text-align: right;">可修改，默认为区间最高价的1.5倍（最高{:$maxcharge*1.5}）</div>
                <div class="input-container">
                    <i class="fas fa-yuan-sign input-icon"></i>
                    <input type="number" id="actual-charge" placeholder="输入合伙人对外实际收费金额" value="{:$maxcharge*1.5}">
                    <span class="input-unit">元</span>
                </div>
            </div>
            
            <button id="calculate-btn" class="btn-calculate">
                <i class="fas fa-calculator"></i> 计算成本与收益
            </button>
            
            <h2 class="section-title" style="margin-top: 25px;"><i class="fas fa-chart-line"></i> 计算结果</h2>
            
            <div class="result-cards">
                            
                <div class="result-card intermediary">
                    <div class="result-title" style="color:rgb(229, 76, 10);">
                        <i class="fas fa-hand-holding-usd"></i> 合伙人收益
                    </div>
                    <div class="result-value positive" id="intermediary-profit">350.00</div>
                    <div class="explanation">
                        计算公式: 实际收费金额 - 合伙人成本
                    </div>
                </div>                    
                <div class="result-card your">
                    <div class="result-title" style="color:red;">
                        <i class="fas fa-coins"></i> 您的收益
                    </div>
                    <div class="result-value positive" id="your-profit">610.00</div>
                    <div class="explanation">
                        计算公式: 合伙人成本 - 您的成本
                    </div>
                </div>


                <div class="result-card your">
                    <div class="result-title">
                        <i class="fas fa-user"></i> 您的成本
                    </div>
                    <div class="result-value" id="your-cost">1,040.00</div>
                    <div class="explanation">
                        计算公式: (实际收费金额 - 收费区间最低价) × 0.2 + 服务站成本价
                    </div>
                </div>
                
                <div class="result-card">
                    <div class="result-title">
                        <i class="fas fa-users"></i> 合伙人成本
                    </div>
                    <div class="result-value" id="intermediary-cost-result">1,650.00</div>
                    <div class="explanation">
                        计算公式: (实际收费金额 - 合伙人成本价) × 0.3 + 合伙人成本价
                    </div>
                </div>

            </div>
            
            <div class="formula-section">
                <div class="formula-title">
                    <i class="fas fa-info-circle"></i> 计算公式说明
                </div>
                <div class="formula-container">
                    <div class="formula-card">
                        <div class="formula-header">
                            <i class="fas fa-calculator"></i>
                            <span>您的成本</span>
                        </div>
                        <div class="formula-content">
                            您的成本 = (实际收费金额 - 收费区间最低价) × 0.2 + 服务站成本价
                        </div>
                    </div>
                    
                    <div class="formula-card">
                        <div class="formula-header">
                            <i class="fas fa-calculator"></i>
                            <span>合伙人成本</span>
                        </div>
                        <div class="formula-content">
                            合伙人成本 = (实际收费金额 - 合伙人成本价) × 0.3 + 合伙人成本价
                        </div>
                    </div>
                    
                    <div class="formula-card">
                        <div class="formula-header">
                            <i class="fas fa-calculator"></i>
                            <span>您的收益</span>
                        </div>
                        <div class="formula-content">
                            您的收益 = 合伙人成本 - 您的成本
                        </div>
                    </div>
                    
                    <div class="formula-card">
                        <div class="formula-header">
                            <i class="fas fa-calculator"></i>
                            <span>合伙人收益</span>
                        </div>
                        <div class="formula-content">
                            合伙人收益 = 实际收费金额 - 合伙人成本
                        </div>
                    </div>
                </div>
            </div>

                            <div class="formula-section">
                <div class="formula-title">
                    <i class="fas fa-info-circle"></i> <B style="color:red;">特别声明</B>
                </div>
   <div class="formula-container">
                     
                        <div class="formula-header" style="color:red;">
                            <span>该工具服务站内部免费使用，不构成任何层级和经营决策。</span>
                        </div>
                    
                    </div>
            </div>    



              <div class="formula-section">
                <div class="formula-title">
                    <i class="fas fa-info-circle"></i> 使用场景说明
                </div>
                <div class="formula-container">
                    <div class="formula-card">
                        <div class="formula-header">
                            <span>计算器开发目的</span>
                        </div>
                        <div class="formula-content">
                            方便您与合伙人快速计算收益而设计的工具。
                        </div>
                    </div>

                    <div class="formula-card">
                        <div class="formula-header">
                            <span>使用方法</span>
                        </div>
                        <div class="formula-content">
                            您跟常规合伙人针对【{:$postname}】岗位确定给他的成本价，在计算器中输入给他的成本价，再输入他想要对外收费的价格，就可以计算出他的收益和您的收益。
                        </div>
                    </div>

                                        
                    <div class="formula-card">
                        <div class="formula-header">
                            <span>计算方式说明</span>
                        </div>
                        <div class="formula-content">
                            <B>根据上方计算公式自动为您快速计算：</B>合伙人的成本价基础上超出的培训费用跟您七三分，合伙人7您3。
                        </div>
                    </div>
                    

                      <div class="formula-card">
                        <div class="formula-header">
                            <span>合作晋升通道（可选。您定条件）</span>
                        </div>
                        <div class="formula-content">
                            常规合伙人 ->  招就办主任
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>专为常规合伙人设计 | 禁止分享 | 敏感数据保护</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检测是否在微信中打开
            function isWechatBrowser() {
                const ua = navigator.userAgent.toLowerCase();
                return ua.indexOf('micromessenger') !== -1;
            }

            const wechatAlert = document.getElementById('wechat-alert');
            const calculator = document.getElementById('calculator');
            const shareBlock = document.getElementById('share-block');

            if (isWechatBrowser()) {
                if (calculator) calculator.style.display = 'block';
                if (wechatAlert) wechatAlert.style.display = 'none';

                // 初始化微信JS-SDK
                initWechatSDK();
            } else {
                if (wechatAlert) wechatAlert.style.display = 'flex';
                if (calculator) calculator.style.display = 'none';
            }
            
            // 获取DOM元素
            const serviceCostInput = document.getElementById('service-cost');
            const intermediaryCostInput = document.getElementById('intermediary-cost');
            const minChargeInput = document.getElementById('min-charge');
            const actualChargeInput = document.getElementById('actual-charge');
            const calculateBtn = document.getElementById('calculate-btn');

            const yourCostElement = document.getElementById('your-cost');
            const intermediaryCostResultElement = document.getElementById('intermediary-cost-result');
            const yourProfitElement = document.getElementById('your-profit');
            const intermediaryProfitElement = document.getElementById('intermediary-profit');

            // 初始化计算
            try {
                calculateResults();
            } catch (error) {
                console.error('初始化计算出错:', error);
            }

            // 添加事件监听器
            if (calculateBtn) {
                calculateBtn.addEventListener('click', calculateResults);
            }

            // 为所有输入框添加输入事件监听
            const inputs = [serviceCostInput, intermediaryCostInput, minChargeInput, actualChargeInput];
            inputs.forEach(input => {
                if (input) {
                    input.addEventListener('input', calculateResults);
                    input.addEventListener('focus', function() {
                        this.select();
                    });
                }
            });
            
            // 计算函数
            function calculateResults() {
                try {
                    // 获取输入值并转换为浮点数
                    const serviceCost = parseFloat(serviceCostInput.value) || 0;
                    const intermediaryCost = parseFloat(intermediaryCostInput.value) || 0;
                    const minCharge = parseFloat(minChargeInput.value) || 0;
                    const actualCharge = parseFloat(actualChargeInput.value) || 0;

                    // 计算您的成本
                    const yourCost = (actualCharge - minCharge) * 0.2 + serviceCost;

                    // 计算中间人成本
                    const intermediaryCostResult = (actualCharge - intermediaryCost) * 0.3 + intermediaryCost;

                    // 计算您的收益
                    const yourProfit = intermediaryCostResult - yourCost;

                    // 计算中间人收益
                    const intermediaryProfit = actualCharge - intermediaryCostResult;

                    // 更新结果到页面
                    if (yourCostElement) {
                        yourCostElement.textContent = yourCost.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                    }

                    if (intermediaryCostResultElement) {
                        intermediaryCostResultElement.textContent = intermediaryCostResult.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                    }

                    if (yourProfitElement) {
                        yourProfitElement.textContent = yourProfit.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                        yourProfitElement.className = yourProfit >= 0 ? 'result-value positive' : 'result-value negative';
                    }

                    if (intermediaryProfitElement) {
                        intermediaryProfitElement.textContent = intermediaryProfit.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                        intermediaryProfitElement.className = intermediaryProfit >= 0 ? 'result-value positive' : 'result-value negative';
                    }

                    // 添加动画效果
                    if (yourCostElement && yourCostElement.parentElement) {
                        yourCostElement.parentElement.style.animation = 'none';
                        setTimeout(() => {
                            yourCostElement.parentElement.style.animation = 'pulse 0.5s';
                        }, 10);
                    }
                } catch (error) {
                    console.error('计算过程中出错:', error);
                }
            }
            
            // 初始化微信JS-SDK
            function initWechatSDK() {
                if (typeof wx === 'undefined') {
                    return;
                }

                try {
                    // 在实际使用中，这里需要从服务器获取签名
                    // 以下为示例代码，实际应用中需要后端支持
                    wx.config({
                        debug: false, // 关闭调试模式
                        appId: 'YOUR_APP_ID', // 必填，公众号的唯一标识
                        timestamp: Math.floor(Date.now() / 1000), // 必填，生成签名的时间戳
                        nonceStr: 'randomString', // 必填，生成签名的随机串
                        signature: 'SIGNATURE', // 必填，签名
                        jsApiList: [
                            'hideOptionMenu',
                            'hideMenuItems',
                            'showMenuItems',
                            'onMenuShareAppMessage',
                            'onMenuShareTimeline',
                            'onMenuShareQQ',
                            'onMenuShareWeibo',
                            'onMenuShareQZone'
                        ] // 必填，需要使用的JS接口列表
                    });
                } catch (error) {
                    console.error('wx.config 调用失败:', error);
                }
                
                wx.ready(function() {
                    try {
                        // 隐藏微信分享菜单
                        wx.hideOptionMenu();

                        // 或者隐藏特定菜单项
                        wx.hideMenuItems({
                            menuList: [
                                'menuItem:share:appMessage',
                                'menuItem:share:timeline',
                                'menuItem:share:qq',
                                'menuItem:share:weiboApp',
                                'menuItem:share:QZone'
                            ]
                        });

                        // 监听分享事件
                        wx.onMenuShareAppMessage({
                            title: '服务收益计算器',
                            desc: '计算您与中间人的成本与收益',
                            link: window.location.href,
                            imgUrl: 'https://example.com/logo.png',
                            success: function() {
                                // 用户确认分享后执行的回调函数
                            },
                            cancel: function() {
                                // 用户取消分享后执行的回调函数
                            }
                        });

                        // 当用户尝试分享时显示阻止提示
                        wx.onMenuShareTimeline(function() {
                            if (shareBlock) shareBlock.style.display = 'flex';
                            return false;
                        });

                        wx.onMenuShareAppMessage(function() {
                            if (shareBlock) shareBlock.style.display = 'flex';
                            return false;
                        });
                    } catch (error) {
                        console.error('设置微信功能时出错:', error);
                    }
                });

                wx.error(function(res) {
                    console.error('微信JS-SDK初始化失败', res);
                });
            }
            
            // 添加脉冲动画
            const style = document.createElement('style');
            style.innerHTML = `
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.03); }
                    100% { transform: scale(1); }
                }
                .result-card {
                    animation: pulse 0.5s ease;
                }
            `;
            document.head.appendChild(style);
            
            // 监听页面可见性变化（当用户返回页面时）
            document.addEventListener('visibilitychange', function() {
                if (document.visibilityState === 'visible') {
                    // 用户返回页面，检查是否尝试过分享
                    if (localStorage.getItem('shareAttempted')) {
                        shareBlock.style.display = 'flex';
                        localStorage.removeItem('shareAttempted');
                    }
                }
            });
            
            // 模拟分享阻止（实际应用中应使用微信JS-SDK）
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
            });
            
            // 当页面失去焦点时（用户可能打开了分享菜单）
            window.addEventListener('blur', function() {
                localStorage.setItem('shareAttempted', 'true');
            });
        });


                // 返回逻辑（优先返回历史记录，失败则跳转首页）
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/'; // 修改为你的默认回退地址
            }
        }
    </script>

    <script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取页面中的min-charge值（即{:$mincharge}）
    const minCharge = parseFloat(document.getElementById('min-charge').value) || 0;

    // 获取实际收费金额的最大限制值（即{:$maxcharge*1.5}）
    const maxChargeLimit = parseFloat(document.getElementById('actual-charge').value) || 0;

    // 获取需要控制的输入框
    const intermediaryCostInput = document.getElementById('intermediary-cost');
    const actualChargeInput = document.getElementById('actual-charge');
    
    // 为输入框添加失去焦点事件（用户完成输入后验证）
    intermediaryCostInput.addEventListener('blur', function() {
        validateIntermediaryCost();
        calculateResults();
    });
    
    actualChargeInput.addEventListener('blur', function() {
        validateActualCharge();
        calculateResults();
    });
    
    // 为输入框添加回车键事件
    intermediaryCostInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            validateIntermediaryCost();
            calculateResults();
            this.blur(); // 移出焦点
        }
    });
    
    actualChargeInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            validateActualCharge();
            calculateResults();
            this.blur(); // 移出焦点
        }
    });
    
    // 给中间人价格的验证函数
    function validateIntermediaryCost() {
        const value = parseFloat(intermediaryCostInput.value) || 0;

        if (value < minCharge) {
            showToast(`给中间人的价格不能低于 ${minCharge.toLocaleString()} 元`, 'warning');
            intermediaryCostInput.value = minCharge;
        }
    }

    // 实际收费金额的验证函数
    function validateActualCharge() {
        let value = parseFloat(actualChargeInput.value) || 0;
        let changed = false;

        if (value < minCharge) {
            showToast(`实际收费金额不能低于 ${minCharge.toLocaleString()} 元`, 'warning');
            value = minCharge;
            changed = true;

        }

        if (value > maxChargeLimit) {
            showToast(`实际收费金额不能高于 ${maxChargeLimit.toLocaleString()} 元`, 'warning');
            value = maxChargeLimit;
            changed = true;
        }

        if (changed) {
            actualChargeInput.value = value;
        }
    }
    
    // 显示友好的提示消息
    function showToast(message, type) {
        // 移除可能存在的旧提示
        const existingToast = document.getElementById('custom-toast');
        if (existingToast) existingToast.remove();
        
        // 创建提示元素
        const toast = document.createElement('div');
        toast.id = 'custom-toast';
        toast.textContent = message;
        
        // 设置样式
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'warning' ? '#e74c3c' : '#27ae60'};
            color: white;
            padding: 12px 24px;
            border-radius: 30px;
            font-size: 14px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: toastFadeIn 0.3s, toastFadeOut 0.3s 2.7s;
            display: flex;
            align-items: center;
            gap: 8px;
        `;
        
        // 添加图标
        const icon = document.createElement('i');
        icon.className = type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle';
        toast.prepend(icon);
        
        document.body.appendChild(toast);
        
        // 3秒后移除提示
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 3000);
    }
    
    // 添加提示动画样式
    const style = document.createElement('style');
    style.innerHTML = `
        @keyframes toastFadeIn {
            from { opacity: 0; transform: translate(-50%, -20px); }
            to { opacity: 1; transform: translate(-50%, 0); }
        }
        @keyframes toastFadeOut {
            from { opacity: 1; transform: translate(-50%, 0); }
            to { opacity: 0; transform: translate(-50%, -20px); }
        }
    `;
    document.head.appendChild(style);
});
</script>
</body>
</html>