﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>    
    <title>提现进度</title>
    <style>
        :root {
            --primary-color: #00bf80;
            --success-color: #00bf80;
            --processing-color: #ff9900;
            --failed-color: #ff4d4f;
            --secondary-color: #666;
            --card-bg: #fff;
            --border-color: #eee;
        }

 

        .container {
            max-width: 800px;
            margin: 12px;
            padding: 20px;
        }

        /* 头部样式 */
        .header {
            margin-bottom: 24px;
        }
        .title {
            font-size: 24px;
            color: #000;
            margin-bottom: 8px;
        }
        .subtitle {
            color: var(--secondary-color);
            font-size: 16px;
        }

        /* 筛选控件 */
        .filter-group {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .filter-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid #ddd;
            background: #fff;
            cursor: pointer;
            transition: all 0.2s;
        }
        .filter-btn.active {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        /* 记录卡片 */
        .record-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border-left: 4px solid;
        }
        .record-card.success { border-color: var(--success-color); }
        .record-card.processing { border-color: var(--processing-color); }
        .record-card.failed { border-color: var(--failed-color); }

        .bank-info {
            font-size: 16px;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .time-status {
            color: var(--secondary-color);
            font-size: 14px;
        }
        .status-label { font-weight: 500; }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: flex-end;
            align-items: flex-end;
        }
        .modal-content {
            background: #fff;
            width: 100%;
            max-height: 70vh;
            padding: 20px;
            border-radius: 16px 16px 0 0;
            transform: translateY(100%);
            transition: transform 0.3s;
        }
        .modal-show .modal-content {
            transform: translateY(0);
        }

        /* 分页控件 */
        .pagination {
            display: flex;
            gap: 8px;
            justify-content: center;
            margin-top: 20px;
            margin-bottom: 20px;
            padding: 16px;
        }

        .pagination a, .pagination span {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 0 4px;
            cursor: pointer;
            background: white;
            color: #333;
            text-decoration: none;
            display: inline-block;
        }

        .pagination span.current {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination a:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .container { padding: 16px; }
            .bank-info { flex-direction: column; align-items: flex-start; }
        }

                /* 返回箭头样式 */
.back-arrow {
    position: fixed;
    top: 115px;
    left: 15px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.2s;
}

.back-arrow::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    width: 12px;
    height: 12px;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: translateY(-50%) rotate(45deg);
}

/* 点击反馈效果 */
.back-arrow:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.7);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .back-arrow {
        top: 200px;
        left: 10px;
        width: 36px;
        height: 36px;
    }
    
    .back-arrow::before {
        left: 13px;
        width: 10px;
        height: 10px;
    }
}

        /* Add styles for different status colors based on the new status */
        .record-card.status-1 { border-left-color: #ffc107; } /* 待审核 - warning */
        .record-card.status-2 { border-left-color: #17a2b8; } /* 待打款 - info */
        .record-card.status-3 { border-left-color: #dc3545; } /* 已驳回 - danger */
        .record-card.status-4 { border-left-color: #28a745; } /* 打款完成 - success */
        .record-card.status-5 { border-left-color: #dc3545; } /* 打款失败 - danger */
        .record-card.status-6 { border-left-color: #007bff; } /* 待开票 - primary */
        
        .remark-text {
            font-size: 13px;
            color: #777;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px dashed #eee;
        }
    </style>
</head>
<body>
<!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
<!-- 存储钱包页面URL的隐藏元素 -->
<input type="hidden" id="walletUrl" value="{:U('index/money')}">
    <include file="headers"/>
    <div class="container">
        <div class="header" style="text-align: center;">
            <h1 class="title">提现记录</h1>
        </div>

        <div class="filter-group js_type" id="filterGroup">
            <button class="filter-btn active" data-id="0">全部</button>
            <button class="filter-btn" data-id="1">待审核</button>
            <button class="filter-btn" data-id="2">待打款</button>
            <button class="filter-btn" data-id="6">待开票</button>
            <button class="filter-btn" data-id="4">打款完成</button>
            <button class="filter-btn" data-id="3">已驳回</button>
            <button class="filter-btn" data-id="5">打款失败</button>
        </div>

        <div id="recordList">
            <include file="list-money_withdrawal"/>
        </div>
        
        <div class="pagination">
             {$page}
        </div>
        
        <!-- 弹窗 -->
        <div class="modal-overlay" id="modal">
            <div class="modal-content">
                <h3>到账说明</h3>
                <p>1. 提现申请提交后，通常需要1-3个工作日到账</p>
                <p>2. 如遇节假日或银行系统升级，到账时间可能顺延</p>
                <button onclick="closeModal()">关闭</button>
            </div>
        </div>
    </div>

<script>
var type = {:I('get.type', 0)};
var page = {:I('get.p', 1)};

// 检测页面来源并处理历史记录
$(function() {
    // 检查是否是从提现申请成功页面跳转过来的
    var referrer = document.referrer;
    
    // 检查referrer是否包含跳转URL并且消息中含有"提现申请"
    if (referrer && 
        (referrer.indexOf('dispatch_jump') !== -1 || 
         referrer.indexOf('jump.html') !== -1)) {
        
        // 替换当前历史记录，使返回按钮能直接返回到钱包页面
        var moneyUrl = "{:U('index/money')}";
        history.replaceState(null, document.title, window.location.href);
        
        // 修改返回按钮行为，确保返回到钱包页面
        window.defaultBackFunction = window.goBack;
        window.goBack = function() {
            window.location.href = moneyUrl;
        };
    }
    
    // 确保分页控件样式正确
    $('.pagination').css({
        'display': 'flex',
        'justify-content': 'center',
        'margin-top': '20px',
        'margin-bottom': '20px'
    });
});

// Back button logic
function goBack() {
    // 直接返回到钱包页面，而不是使用history.back()
    var walletUrl = document.getElementById('walletUrl').value;
    window.location.href = walletUrl;
}

// Function to load records via AJAX
function ajaxLoadRecords(pageNum, statusType) {
    $('#recordList').html('<p style="text-align:center; padding: 20px;">加载中...</p>'); 
    
    var url = '{:U("index/money_withdrawal")}';
    var params = { n: 1, p: pageNum, type: statusType };
    
    $.get(url, params, function(htmlContent){
        $('#recordList').html(htmlContent);
        // 更新分页内容
        $.get(url, $.extend({}, params, {showpage: 1}), function(pageHtml){
            $('.pagination').html(pageHtml);
            page = pageNum;
            
            // 确保分页控件样式正确
            $('.pagination').css({
                'display': 'flex',
                'justify-content': 'center',
                'margin-top': '20px',
                'margin-bottom': '20px'
            });
        });
    }, 'html').fail(function() {
         $('#recordList').html('<p style="text-align:center; padding: 20px; color: red;">加载失败，请重试。</p>');
    });
}

$(function () {
    $('.js_type button').click(function () {
        $(this).siblings().removeClass('active');
        $(this).addClass('active');
        var newType = $(this).attr('data-id');
        type = newType;
        ajaxLoadRecords(1, type);
    });
    
    var initialType = '{:I("get.type", 0)}';
    $('.js_type button').removeClass('active');
    $('.js_type button[data-id="' + initialType + '"]').addClass('active');
    
    $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        var href = $(this).attr('href');
        var urlParams = new URLSearchParams(href.split('?')[1]);
        var targetPage = urlParams.get('p') || 1;
        ajaxLoadRecords(parseInt(targetPage), type); 
    });
});

</script>
</body>
</html>