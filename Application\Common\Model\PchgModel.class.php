<?php

namespace Common\Model;

use Think\Model;

class PchgModel extends Model
{
    protected $_auto = [ 
        ['created', 'time', 1, 'function'],
    ];

    /**
     *
     */
    public function _save()
    {
        do {
            $data = $this->create();
            if (! $data) break;
            if ($data['point'] <= 0) break;
            $data['admin_id'] = session('admin_id');
            $this->startTrans();
            $id = $this->add($data);
            if (! $id) break;
            $lid = D('PointLog')->addLog($data['user_id'], $data['point'], 4, $id);
            if (! $lid) break;
            $data = [ 'id' => $data['user_id'], 'point' => ['exp', "point+".$data['point']]];
            $res = D('User')->save($data);
            if (! $res) break;
            $this->commit();
            $flag = 1;
        } while (false);
        if (empty($flag)) {
            $this->rollback();
            return false;
        }
        return true;
    }


}
