<?php
/**
 * 微信好友二维码管理控制器
 */
namespace Prime\Controller;

use Common\Controller\PrimeController;
use Think\Upload;

class WechatqrcodeController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
        
        // 手动设置当前菜单
        $menu = D("Menu")->where(['model' => 'Wechatqrcode', 'action' => 'index'])->find();
        if ($menu) {
            $cur_menu = D("Menu")->curMenu(1);
            $cur_menu['id'] = $menu['id'];
            $cur_menu['parentid'] = $menu['parentid'];
            $cur_menu['boot_id'] = D("Menu")->getBoot($menu['parentid']);
            $this->assign("cur_menu", $cur_menu);
        }
    }
    
    public function index()
    {
        $where = [];
        $obj = D("WechatQrcode");

        // 处理状态筛选
        $status = I('get.status');
        if ($status !== '' && is_numeric($status)) {
            $where['status'] = intval($status);
        }

        // 处理权重筛选
        $weight = I('get.weight');
        if ($weight === 'high') {
            $where['weight'] = 1; // 高权重
        }

        // 处理搜索关键词
        $kw = I('get.kw');
        $val = I('get.val');
        if ($kw && $val !== '') {
            switch ($kw) {
                case 'id':
                    $where['id'] = intval($val);
                    break;
                case 'tip_text':
                    $where['tip_text'] = ['like', "%{$val}%"];
                    break;
                default:
                    break;
            }
        }

        $count = $obj->where($where)->count();
        $page = $this->page($count, 10);

        $list = $obj->order("id desc")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();

        // 搜索条件选项
        $c_kw = [
            'id' => 'ID',
            'tip_text' => '提示文字',
        ];

        $this->assign('list', $list);
        $this->assign('weightLevels', $obj->weightLevels);
        $this->assign('c_kw', $c_kw);
        $this->assign('_get', I('get.'));
        $this->assign("page", $page->show());
        $this->display();
    }
    
    public function edit()
    {
        $id = I('get.id', 0, 'intval');
        $obj = D("WechatQrcode");
        
        if (IS_POST) {
            $data = I('post.');
            
            // 处理tpl_form_field_image上传组件提交的图片
            // image_path可能是新上传的临时文件，也可能是已有路径
            if (!empty($data['image_path'])) {
                // 检查是否是新上传的临时文件（以/attachment/temp/开头）
                if (strpos($data['image_path'], '/attachment/temp/') === 0) {
                    // 临时文件完整路径
                    $temp_file = SITE_PATH . $data['image_path'];
                    
                    // 确保目标目录存在
                    $target_dir = SITE_PATH . '/data/wechat_qrcode/';
                    if (!is_dir($target_dir)) {
                        mkdir($target_dir, 0777, true);
                    }
                    
                    // 生成新文件名
                    $file_ext = pathinfo($temp_file, PATHINFO_EXTENSION);
                    $new_file_name = date('YmdHis') . '_' . rand(10000, 99999) . '.' . $file_ext;
                    $target_path = $target_dir . $new_file_name;
                    
                    // 移动文件
                    if (file_exists($temp_file) && rename($temp_file, $target_path)) {
                        $data['image_path'] = '/data/wechat_qrcode/' . $new_file_name;
                    } else {
                        $this->error('图片处理失败，请重试');
                    }
                }
                // 如果不是临时文件路径，则保留原有的image_path值
            } else if (empty($data['id'])) {
                $this->error('请上传二维码图片');
            }
            
            // 确保权重是有效值
            if (!isset($data['weight']) || !in_array($data['weight'], array_keys($obj->weightLevels))) {
                $data['weight'] = 2; // 默认中等权重
            }
            
            $result = $obj->saveQrcode($data);
            if ($result !== false) {
                $this->success('保存成功', U('wechatqrcode/index'));
            } else {
                $this->error('保存失败');
            }
        }
        
        if ($id) {
            $row = $obj->find($id);
            $this->assign('row', $row);
        }
        
        $this->assign('weightLevels', $obj->weightLevels);
        $this->display();
    }
    
    public function delete()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误');
        }
        
        $result = D("WechatQrcode")->where(['id' => $id])->delete();
        if ($result) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    
    public function toggle()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误');
        }
        
        $status = I('get.status', 0, 'intval') ? 0 : 1;
        $result = D("WechatQrcode")->save(['id' => $id, 'status' => $status]);
        
        if ($result !== false) {
            $this->success('操作成功');
        } else {
            $this->error('操作失败');
        }
    }
} 