<!DOCTYPE html>
<html>
  <head>
    <title>培训订单详情</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
      name="viewport"
    />
    <meta content="no-cache,must-revalidate" http-equiv="Cache-Control" />
    <meta content="telephone=no, address=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta content="no-cache" http-equiv="pragma" />
    <meta content="0" http-equiv="expires" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/css.css?v={:time()}"
      media="all"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/swiper-bundle.css"
      media="all"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/iconfont.css"
      media="all"
    />
    <script
      type="text/javascript"
      src="/static/stations/js/jquery-1.9.1.min.js"
    ></script>
    <script
      type="text/javascript"
      src="/static/stations/js/swiper-bundle.min.js"
    ></script>
    <script src="/static/js/layer/layer.js"></script>
    <script>
      // 全局错误处理
      window.onerror = function (message, source, lineno, colno, error) {
        console.error("JS错误: ", message, "在", source, "行:", lineno);
        return false;
      };

      // 确保layer对象可用
      window.showMessage = function (msg, type) {
        if (window.layer && typeof window.layer.msg === "function") {
          layer.msg(msg, { icon: type || 2 });
        } else {
          alert(msg);
        }
      };
    </script>
  </head>
  <style>
    .container {
      margin: 10px;
    }

    /* 顶部导航栏 */
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #fff;
      padding: 12px 15px;
      border-radius: 8px;
      margin-bottom: 15px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .back-button {
      display: flex;
      align-items: center;
      color: #333;
      font-size: 15px;
      text-decoration: none;
    }

    .back-button i {
      margin-right: 5px;
    }

    .page-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin: 0;
    }

    /* 面板样式 */
    .detail-panel {
      background: #fff;
      border-radius: 8px;
      margin-bottom: 15px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }

    .panel-header {
      padding: 12px 15px;
      background-color: #f9f9f9;
      border-bottom: 1px solid #eee;
    }

    .panel-title {
      margin: 0;
      font-size: 16px;
      font-weight: bold;
      color: #333;
      display: flex;
      align-items: center;
    }

    .panel-title i {
      margin-right: 8px;
      color: #07c160;
    }

    .panel-body {
      padding: 15px;
    }

    /* 信息项样式 */
    .info-row {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -10px;
    }

    .info-col {
      flex: 0 0 50%;
      max-width: 50%;
      padding: 0 10px;
      margin-bottom: 12px;
    }

    .info-item {
      display: flex;
      flex-direction: column;
    }

    .info-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 4px;
    }

    /* 奖励标签附加说明样式 */
    .reward-label-suffix {
      font-size: 11px;
      color: #999;
      font-weight: normal;
      margin-left: 2px;
    }

    /* 移动端进一步优化 */
    @media (max-width: 480px) {
      .reward-label-suffix {
        font-size: 10px;
      }
    }

    .info-value {
      font-size: 15px;
      color: #333;
      font-weight: 500;
    }

    /* 状态标签 */
    .status-tag {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 12px;
      margin-right: 5px;
    }

    .status-pending {
      background: #e6f3ff;
      color: #007aff;
    }

    .status-paid {
      background: #e6ffe6;
      color: #07c160;
    }

    .status-unpaid {
      background: #ffe6e6;
      color: #ff3b30;
    }

    .status-completed {
      background: #e6ffe6;
      color: #07c160;
    }

    .status-processing {
      background: #fff7e6;
      color: #ff9500;
    }

    .status-jobdocself {
      background: #e6f7ff;
      color: #1890ff;
    }

    /* 表格样式 */
    .detail-table {
      width: 100%;
      border-collapse: collapse;
    }

    .detail-table th {
      background-color: #f5f5f5;
      padding: 10px;
      text-align: left;
      font-weight: 500;
      color: #333;
      border-bottom: 1px solid #eee;
    }

    .detail-table td {
      padding: 10px;
      border-bottom: 1px solid #eee;
    }

    .detail-table tr:last-child td {
      border-bottom: none;
    }

    /* 进度条样式 */
    .progress-container {
      margin-bottom: 20px;
    }

    .progress-bar-wrapper {
      height: 10px;
      background-color: #f0f0f0;
      border-radius: 5px;
      overflow: hidden;
      margin-bottom: 15px;
    }

    .progress-bar-fill {
      height: 100%;
      background-color: #07c160;
      border-radius: 5px;
    }

    .progress-steps {
      display: flex;
      justify-content: space-between;
    }

    .progress-step {
      text-align: center;
      flex: 1;
    }

    .step-icon {
      font-size: 20px;
      margin-bottom: 5px;
    }

    .step-text {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }

    .step-desc {
      font-size: 10px;
      color: #999;
      margin-top: 2px;
      min-height: 12px;
    }

    .step-active .step-icon,
    .step-active .step-text {
      color: #07c160;
    }

    .step-active .step-desc {
      color: #07c160;
      font-weight: 500;
    }
  </style>
  <body>
    <include file="Index/headers"/>

    <div class="container">
      <div class="detail-header">
        <a href="{:U('training/index')}" class="back-button">
          <i class="iconfont icon-fanhui"></i> 返回列表
        </a>
        <h1 class="page-title">培训订单详情</h1>
        <div style="width: 24px"></div>
      </div>

      <div class="detail-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <i class="iconfont icon-info"></i> 订单基本信息
          </h3>
        </div>
        <div class="panel-body">
          <div class="info-row">
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">订单ID</div>
                <div class="info-value">
                  {$order.id}
                  <!-- 招就办订单标签（只对非招就办用户显示） -->
                  <php>if($userRow['zsb_type'] != 2) {</php>
                  <notempty name="zsb_info">
                    <span class="status-tag status-success" style="margin-left: 8px;">招就办订单</span>
                  </notempty>
                  <php>}</php>
                </div>
              </div>
            </div>
            <!-- 招就办信息（只对非招就办用户显示） -->
            <php>if($userRow['zsb_type'] != 2) {</php>
            <notempty name="zsb_info">
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">订单所属招就办</div>
                <div class="info-value">{$zsb_info.service_name|default="未知招就办"}</div>
              </div>
            </div>
            </notempty>
            <php>}</php>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">创建时间</div>
                <div class="info-value">
                  {:date('Y-m-d H:i:s', $order['create_time'])}
                </div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">业务阶段</div>
                <div class="info-value">
                  <span class="status-tag status-{$main_status[$order['main_status']]['style']}">
                    <i class="{$main_status[$order['main_status']]['icon']}"></i> {$main_status[$order['main_status']]['text']}
                  </span>
                </div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">更新时间</div>
                <div class="info-value">
                  {:date('Y-m-d H:i:s', $order['update_time'])}
                </div>
              </div>
            </div>
            <!-- 服务审查阶段显示具体状态：服务完成或服务终止 -->
            <php>if($order['main_status'] == 'service_review' && in_array($order['sub_status'], ['completed', 'terminated'])) {</php>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">服务结果</div>
                <div class="info-value">
                  <span class="status-tag status-{$order['sub_status'] == 'completed' ? 'success' : 'danger'}">{$sub_status[$order['sub_status']]['text']}</span>
                </div>
              </div>
            </div>
            <php>}</php>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">状态更新时间</div>
                <div class="info-value">
                  <php>if(!empty($order['status_update_time'])) {</php>
                  {:date('Y-m-d H:i:s', $order['status_update_time'])}
                  <php>} else {</php>
                  -
                  <php>}</php>
                </div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">报名费</div>
                <div
                  class="info-value"
                  style="color: #ff3b30; font-weight: bold"
                >
                  {:number_format($order['fee_amount'] / 100, 2)} 元
                </div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">
                  {$rewardLabel}<span class="reward-label-suffix">{$rewardLabelSuffix}</span>
                </div>
                <div
                  class="info-value"
                  style="color: #07c160; font-weight: bold"
                >
                  {:number_format($rewardAmount, 2)} 元
                </div>
              </div>
            </div>
            <!-- 招就办订单显示真正的服务站收益 -->
            <php>if($userRow['zsb_type'] != 2 && !empty($realStationProfit) && $realStationProfit > 0) {</php>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">服务站收益</div>
                <div
                  class="info-value"
                  style="color: #ff9500; font-weight: bold"
                >
                  {:number_format($realStationProfit, 2)} 元
                </div>
              </div>
            </div>
            <php>}</php>
          </div>
        </div>
      </div>

      <div class="detail-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <i class="iconfont icon-user"></i> 学员信息
          </h3>
        </div>
        <div class="panel-body">
          <div class="info-row">
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">学员ID</div>
                <div class="info-value">{$user.id}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">身份证号</div>
                <div class="info-value">{$user.idcard}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">姓名</div>
                <div class="info-value" style="font-weight: bold">
                  {$user.realname}
                  <php
                    >if(isset($user['from_resume']) && $user['from_resume'])
                    {</php
                  >
                  <span
                    class="status-tag status-jobdocself"
                    style="font-size: 10px; margin-left: 5px"
                    >简历来源</span
                  >
                  <php>}</php>
                </div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">服务状态</div>
                <div class="info-value">
                  <span class="status-tag status-processing"
                    >{$order.status_desc}</span
                  >
                </div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">手机号</div>
                <div class="info-value">{$user.mobile}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="detail-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <i class="iconfont icon-project"></i> 培训项目信息
          </h3>
        </div>
        <div class="panel-body">
          <div class="info-row">
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">项目ID</div>
                <div class="info-value">{$project.id}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">岗位ID</div>
                <div class="info-value">{$post.id}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">项目名称</div>
                <div class="info-value" style="font-weight: bold">
                  {$project.name}
                </div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">岗位名称</div>
                <div class="info-value" style="font-weight: bold">
                  <php>if(isset($post['job_name'])) {</php>
                  {$post.job_name}
                  <php>} else {</php>
                  {$post.name}
                  <php>}</php>
                </div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">项目分类</div>
                <div class="info-value">
                  <php>
                    $categoryList = [ '1' => '央企', '2' => '国企', '3' =>
                    '事业单位', '4' => '上市公司', '5' => '民营企业', '6' =>
                    '出国', '7' => '其他', ]; $categoryText =
                    isset($categoryList[$project['category']]) ?
                    $categoryList[$project['category']] : '未知';
                  </php>
                  {$categoryText}
                </div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <div class="info-label">
                  <notempty name="zsb_info">
                    岗位报价
                    <else />
                    岗位报价区间
                  </notempty>
                </div>
                <div
                  class="info-value"
                  style="color: #ff3b30; font-weight: bold"
                >
                  <php>
                    // 显示报价区间格式
                    if(isset($post['service_price']) && isset($post['max_price']) && $post['max_price'] > 0) {
                        // 如果有两个价格，显示价格区间
                        if($post['service_price'] != $post['max_price']) {
                            echo number_format($post['service_price'], 0) . ' - ' . number_format($post['max_price'], 0) . ' 元';
                        } else {
                            // 如果两个价格相同，只显示一个价格
                            echo number_format($post['service_price'], 0) . ' 元';
                        }
                    } elseif(isset($post['service_price']) && $post['service_price'] > 0) {
                        // 只有一个价格
                        echo number_format($post['service_price'], 0) . ' 元';
                    } else {
                        // 兼容旧数据或价格为0的情况
                        if(isset($post['price']) && $post['price'] > 0) {
                            echo number_format($post['price'], 0) . ' 元';
                        } else {
                            echo '待定';
                        }
                    }
                  </php>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="detail-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <i class="iconfont icon-pay"></i> 支付记录
          </h3>
        </div>
        <div class="panel-body">
          <table class="detail-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>支付渠道</th>
                <th>支付金额</th>
                <th>支付时间</th>
              </tr>
            </thead>
            <tbody>
              <notempty name="payment_records">
                <foreach name="payment_records" item="record">
                  <tr>
                    <td>{$record.id}</td>
                    <td>
                      <span class="status-tag status-paid"
                        >{$record.pay_channel_text}</span
                      >
                    </td>
                    <td>
                      <span style="color: #ff3b30; font-weight: bold">
                        {:number_format($record['pay_amount']/100, 2)} 元
                      </span>
                    </td>
                    <td>{$record.pay_time_text}</td>
                  </tr>
                </foreach>
                <else />
                <tr>
                  <td colspan="4" style="text-align: center; color: #999">
                    <i class="iconfont icon-info"></i> 暂无支付记录
                  </td>
                </tr>
              </notempty>
            </tbody>
          </table>
        </div>
      </div>

      <div class="detail-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <i class="iconfont icon-process"></i> 订单状态流程
          </h3>
        </div>
        <div class="panel-body">
          <php>
            // 基于新的二级状态系统计算进度
            $progress = 0;
            $currentMainStatus = $order['main_status'];

            if ($currentMainStatus == 'communication') $progress = 25;
            elseif ($currentMainStatus == 'training') $progress = 50;
            elseif ($currentMainStatus == 'employment') $progress = 75;
            elseif ($currentMainStatus == 'service_review') $progress = 100;
          </php>

          <div class="progress-container">
            <div class="progress-bar-wrapper">
              <div class="progress-bar-fill" style="width: {$progress}%;"></div>
            </div>

            <div class="progress-steps">
              <div
                class="progress-step <php>if($progress >= 25) echo 'step-active';</php>"
              >
                <div class="step-icon">
                  <i class="iconfont icon-chat"></i>
                </div>
                <div class="step-text">沟通流程</div>
                <div class="step-desc">
                  <!-- station模块只显示一级状态，不显示详细状态 -->
                </div>
              </div>
              <div
                class="progress-step <php>if($progress >= 50) echo 'step-active';</php>"
              >
                <div class="step-icon">
                  <i class="iconfont icon-book"></i>
                </div>
                <div class="step-text">培训流程</div>
                <div class="step-desc">
                  <!-- station模块只显示一级状态，不显示详细状态 -->
                </div>
              </div>
              <div
                class="progress-step <php>if($progress >= 75) echo 'step-active';</php>"
              >
                <div class="step-icon">
                  <i class="iconfont icon-work"></i>
                </div>
                <div class="step-text">入职流程</div>
                <div class="step-desc">
                  <!-- station模块只显示一级状态，不显示详细状态 -->
                </div>
              </div>
              <div
                class="progress-step <php>if($progress >= 100) echo 'step-active';</php>"
              >
                <div class="step-icon">
                  <i class="iconfont icon-check"></i>
                </div>
                <div class="step-text">服务审查</div>
                <div class="step-desc">
                  <php>
                    if ($currentMainStatus == 'service_review' && in_array($order['sub_status'], ['completed', 'terminated'])) {
                      echo $sub_status[$order['sub_status']]['text'];
                    }
                  </php>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部空间，不需要重复的返回按钮 -->
    <div style="height: 20px"></div>
  </body>
</html>
