<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="/static/job/styles/mui.picker.all.css" />
    <link rel="stylesheet" href="/static/job/styles/swiper.min.css">
    <link rel="stylesheet" href="/static/job/styles/main.css">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="/static/job/js/jquery.min.js"></script>
</head>

<body>
<header>
    <div class="header-box">
        <a href="{:U('index/index')}" class="btn-back"></a>
        <h3>服务站管理</h3>
    </div>
</header>
<div class="g-header-space">
    <div class="header-space"></div>
</div>
<div class="container">
    <div class="list-head">
        <div class="list-item li1">公司信息</div>
        <div class="list-item li2">电话号码</div>
        <div class="list-item li2">状态</div>
        <div class="list-item li3">操作</div>
    </div>
    <include file="list-servicestation"/>
</div>
<script src="/static/job/js/swiper.min.js"></script>
<script src="/static/job/js/mui.min.js"></script>
<script src="/static/job/js/mui.picker.min.js"></script>
<script src="/static/job/js/main.js"></script>
<script>
    $(function () {
        $('.money').on('click', '.status', function(){
            var tip = $(this).data('tip');
            if (tip) layer.open({content: tip});
        });
        $(window).trigger("scroll");
    });
    var page=1,pages=<?= (int)$page->Total_Pages ?>;
    function loadmore(){
        if(page<pages){
            page+=1;
            $.get('/index/servicestation?p=' + page, function(str){
                $('.money').append(str);
            }, 'html');
        } else if (page==pages) {
            page+=1;
            setTimeout("$('.container').append('<p class=\"nomore\">------ 没有了 ------</p>')", 500);
        }
    }
    $(window).scroll(function(){
        var scrollTop = $(this).scrollTop();     //滚动条距离顶部的高度
        var scrollHeight = $(document).height(); //当前页面的总高度
        var clientHeight = $(this).height();     //当前可视的页面高度
        // console.log("top:"+scrollTop+",doc:"+scrollHeight+",client:"+clientHeight);
        if(scrollTop + clientHeight >= scrollHeight){
            loadmore();
        }else if(scrollTop<=0){
        }
    });
</script>
</body>

</html>