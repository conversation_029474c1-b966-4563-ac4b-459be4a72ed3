<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化灵工审核页面样式 */
                .jingling-review-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .jingling-review-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .jingling-review-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .jingling-review-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #f59e0b 0%, #f97316 50%, #fb923c 100%);
                }

                .jingling-review-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .jingling-review-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .jingling-review-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .jingling-review-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .jingling-review-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .jingling-review-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .jingling-review-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .jingling-review-back-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .jingling-review-back-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 审核状态指示器 */
                .jingling-review-status-indicator {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .jingling-review-status-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .jingling-review-status-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .jingling-review-status-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .jingling-review-status-body {
                    padding: 2rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .jingling-review-status-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 1rem 2rem;
                    border-radius: 1rem;
                    font-size: 1.75rem;
                    font-weight: 700;
                    border: 3px solid;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }

                .jingling-review-status-badge.status-pending {
                    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                    color: #92400e;
                    border-color: #f59e0b;
                }

                .jingling-review-status-badge.status-approved {
                    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
                    color: #065f46;
                    border-color: #10b981;
                }

                .jingling-review-status-badge.status-rejected {
                    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
                    color: #991b1b;
                    border-color: #ef4444;
                }

                /* 现代化信息卡片 */
                .jingling-review-info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                .jingling-review-info-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .jingling-review-info-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .jingling-review-info-card-header {
                    background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .jingling-review-info-card-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .jingling-review-info-card-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .jingling-review-info-card-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .jingling-review-info-card-body {
                    padding: 2rem;
                }

                .jingling-review-info-item {
                    display: flex;
                    align-items: flex-start;
                    gap: 1rem;
                    margin-bottom: 1.5rem;
                    padding-bottom: 1.5rem;
                    border-bottom: 1px solid #f1f5f9;
                    font-size: 1.5rem;
                }

                .jingling-review-info-item:last-child {
                    margin-bottom: 0;
                    padding-bottom: 0;
                    border-bottom: none;
                }

                .jingling-review-info-label {
                    color: #6b7280;
                    font-weight: 600;
                    min-width: 120px;
                    flex-shrink: 0;
                }

                .jingling-review-info-value {
                    color: #374151;
                    font-weight: 600;
                    flex: 1;
                    word-break: break-all;
                }

                .jingling-review-id-card {
                    font-family: 'Courier New', monospace;
                    font-size: 1.5rem;
                    letter-spacing: 0.05em;
                    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
                    border: 1px solid #0ea5e9;
                    border-radius: 0.5rem;
                    padding: 0.5rem 0.75rem;
                    color: #0c4a6e;
                    font-weight: 700;
                }

                .jingling-review-phone {
                    font-family: 'Courier New', monospace;
                    font-size: 1.5rem;
                    letter-spacing: 0.05em;
                    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
                    border: 1px solid #22c55e;
                    border-radius: 0.5rem;
                    padding: 0.5rem 0.75rem;
                    color: #15803d;
                    font-weight: 700;
                }

                .jingling-review-platform-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    border: 2px solid;
                }

                .jingling-review-platform-badge.platform-jingdong {
                    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
                    color: #1e40af;
                    border-color: #3b82f6;
                }

                .jingling-review-platform-badge.platform-yunzhanghu {
                    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
                    color: #065f46;
                    border-color: #10b981;
                }

                /* 现代化审核表单 */
                .jingling-review-form-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    margin-bottom: 2rem;
                }

                .jingling-review-form-header {
                    background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .jingling-review-form-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .jingling-review-form-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .jingling-review-form-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .jingling-review-form-body {
                    padding: 2rem;
                }

                .jingling-review-form-group {
                    margin-bottom: 2rem;
                }

                .jingling-review-form-label {
                    display: block;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 1rem;
                }

                .jingling-review-radio-group {
                    display: flex;
                    gap: 1.5rem;
                    margin-bottom: 1rem;
                    flex-wrap: wrap;
                }

                .jingling-review-radio-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 1rem 1.5rem;
                    background: #f8fafc;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #6b7280;
                    min-width: 120px;
                    justify-content: center;
                }

                .jingling-review-radio-item:hover {
                    background: #f1f5f9;
                    border-color: #cbd5e1;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }

                .jingling-review-radio-item.selected {
                    background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
                    border-color: #f59e0b;
                    color: white;
                    box-shadow: 0 4px 6px rgba(245, 158, 11, 0.3);
                }

                .jingling-review-radio-item.selected.approve {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    border-color: #10b981;
                    box-shadow: 0 4px 6px rgba(16, 185, 129, 0.3);
                }

                .jingling-review-radio-item.selected.reject {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    border-color: #ef4444;
                    box-shadow: 0 4px 6px rgba(239, 68, 68, 0.3);
                }

                .jingling-review-radio-input {
                    display: none;
                }

                .jingling-review-radio-icon {
                    width: 1.5rem;
                    height: 1.5rem;
                    border: 2px solid currentColor;
                    border-radius: 50%;
                    position: relative;
                    flex-shrink: 0;
                }

                .jingling-review-radio-item.selected .jingling-review-radio-icon::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 0.75rem;
                    height: 0.75rem;
                    background: currentColor;
                    border-radius: 50%;
                }

                .jingling-review-textarea {
                    width: 100%;
                    min-height: 120px;
                    padding: 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-family: inherit;
                    resize: vertical;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: #f8fafc;
                }

                .jingling-review-textarea:focus {
                    outline: none;
                    border-color: #f59e0b;
                    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
                    background: white;
                }

                .jingling-review-textarea::placeholder {
                    color: #9ca3af;
                    font-style: italic;
                }

                .jingling-review-form-actions {
                    display: flex;
                    gap: 1rem;
                    margin-top: 2rem;
                    padding-top: 2rem;
                    border-top: 1px solid #e2e8f0;
                    flex-wrap: wrap;
                }

                .jingling-review-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 2rem;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    min-width: 140px;
                    justify-content: center;
                }

                .jingling-review-action-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .jingling-review-action-btn.btn-primary {
                    background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
                    color: white;
                }

                .jingling-review-action-btn.btn-primary:hover {
                    color: white;
                }

                .jingling-review-action-btn.btn-secondary {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                }

                .jingling-review-action-btn.btn-secondary:hover {
                    color: white;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .jingling-review-container {
                        padding: 1.5rem;
                    }

                    .jingling-review-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .jingling-review-info-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .jingling-review-container {
                        padding: 1rem;
                    }

                    .jingling-review-title-main {
                        font-size: 1.75rem;
                    }

                    .jingling-review-title-sub {
                        font-size: 1.25rem;
                    }

                    .jingling-review-radio-group {
                        flex-direction: column;
                    }

                    .jingling-review-form-actions {
                        flex-direction: column;
                    }

                    .jingling-review-action-btn {
                        width: 100%;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .jingling-review-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .jingling-review-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .jingling-review-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .jingling-review-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="jingling-review-wrapper">
                <div class="jingling-review-container">
                    <!-- 现代化页面标题 -->
                    <div class="jingling-review-header jingling-review-fade-in">
                        <div class="jingling-review-header-content">
                            <div class="jingling-review-title">
                                <div class="jingling-review-title-icon">
                                    <i class="fa fa-gavel"></i>
                                </div>
                                <div class="jingling-review-title-text">
                                    <h1 class="jingling-review-title-main">灵工绑定审核</h1>
                                    <p class="jingling-review-title-sub">Worker Binding Review</p>
                                </div>
                            </div>
                            <div class="jingling-review-actions">
                                <a href="{:U('Jingling/index')}" class="jingling-review-back-btn">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 审核状态指示器 -->
                    <div class="jingling-review-status-indicator jingling-review-fade-in-delay-1">
                        <div class="jingling-review-status-header">
                            <div class="jingling-review-status-icon">
                                <i class="fa fa-info-circle"></i>
                            </div>
                            <h2 class="jingling-review-status-title">当前审核状态</h2>
                        </div>
                        <div class="jingling-review-status-body">
                            <if condition="$binding.status eq 0">
                                <div class="jingling-review-status-badge status-pending">
                                    <i class="fa fa-clock-o"></i>
                                    <span>待审核</span>
                                </div>
                            <elseif condition="$binding.status eq 1" />
                                <div class="jingling-review-status-badge status-approved">
                                    <i class="fa fa-check-circle"></i>
                                    <span>已通过</span>
                                </div>
                            <elseif condition="$binding.status eq 2" />
                                <div class="jingling-review-status-badge status-rejected">
                                    <i class="fa fa-times-circle"></i>
                                    <span>已拒绝</span>
                                </div>
                            </if>
                        </div>
                    </div>
                    <!-- 信息卡片网格 -->
                    <div class="jingling-review-info-grid jingling-review-fade-in-delay-2">
                        <!-- 灵工基本信息卡片 -->
                        <div class="jingling-review-info-card">
                            <div class="jingling-review-info-card-header">
                                <div class="jingling-review-info-card-icon">
                                    <i class="fa fa-user"></i>
                                </div>
                                <h3 class="jingling-review-info-card-title">灵工基本信息</h3>
                            </div>
                            <div class="jingling-review-info-card-body">
                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">平台类型：</span>
                                    <span class="jingling-review-info-value">
                                        <if condition="$binding.platform_type eq 1">
                                            <span class="jingling-review-platform-badge platform-jingdong">
                                                <i class="fa fa-shopping-cart"></i>
                                                京东京灵
                                            </span>
                                        <elseif condition="$binding.platform_type eq 2" />
                                            <span class="jingling-review-platform-badge platform-yunzhanghu">
                                                <i class="fa fa-cloud"></i>
                                                云账户
                                            </span>
                                        <else />
                                            <span class="jingling-review-platform-badge">
                                                <i class="fa fa-question-circle"></i>
                                                未知平台
                                            </span>
                                        </if>
                                    </span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">灵工姓名：</span>
                                    <span class="jingling-review-info-value">{$binding.worker_name}</span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">身份证号：</span>
                                    <span class="jingling-review-info-value jingling-review-id-card">{$binding.id_card}</span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">手机号：</span>
                                    <span class="jingling-review-info-value jingling-review-phone">{$binding.phone}</span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">结算银行：</span>
                                    <span class="jingling-review-info-value">{$binding.bank_name}</span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">银行账号：</span>
                                    <span class="jingling-review-info-value">尾号 {$binding.bank_account_last4}</span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">提交时间：</span>
                                    <span class="jingling-review-info-value">{:date('Y-m-d H:i:s', $binding['create_time'])}</span>
                                </div>

                                <if condition="$binding.remark neq ''">
                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">审核备注：</span>
                                    <span class="jingling-review-info-value" style="color: #ef4444; font-weight: 700; background: #fef2f2; padding: 0.5rem; border-radius: 0.5rem; border: 1px solid #fecaca;">{$binding.remark}</span>
                                </div>
                                </if>
                            </div>
                        </div>

                        <!-- 关联信息卡片 -->
                        <div class="jingling-review-info-card">
                            <div class="jingling-review-info-card-header">
                                <div class="jingling-review-info-card-icon">
                                    <i class="fa fa-link"></i>
                                </div>
                                <h3 class="jingling-review-info-card-title">关联信息</h3>
                            </div>
                            <div class="jingling-review-info-card-body">
                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">用户ID：</span>
                                    <span class="jingling-review-info-value">#{$binding.user_id}</span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">用户昵称：</span>
                                    <span class="jingling-review-info-value">{$binding.user_info.nickname}</span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">用户手机：</span>
                                    <span class="jingling-review-info-value jingling-review-phone">{$binding.user_info.mobile}</span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">服务站ID：</span>
                                    <span class="jingling-review-info-value">#{$binding.service_station_id}</span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">服务站名称：</span>
                                    <span class="jingling-review-info-value">{$binding.station_info.service_name}</span>
                                </div>

                                <div class="jingling-review-info-item">
                                    <span class="jingling-review-info-label">绑定状态：</span>
                                    <span class="jingling-review-info-value">
                                        <if condition="$binding.status eq 0">
                                            <span style="color: #f59e0b; font-weight: 600;">
                                                <i class="fa fa-clock-o"></i> 待审核
                                            </span>
                                        <elseif condition="$binding.status eq 1" />
                                            <span style="color: #10b981; font-weight: 600;">
                                                <i class="fa fa-check-circle"></i> 已通过
                                            </span>
                                        <elseif condition="$binding.status eq 2" />
                                            <span style="color: #ef4444; font-weight: 600;">
                                                <i class="fa fa-times-circle"></i> 已拒绝
                                            </span>
                                        </if>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 审核表单 -->
                    <if condition="$binding.status eq 0">
                    <div class="jingling-review-form-card jingling-review-fade-in-delay-3">
                        <div class="jingling-review-form-header">
                            <div class="jingling-review-form-icon">
                                <i class="fa fa-gavel"></i>
                            </div>
                            <h3 class="jingling-review-form-title">审核操作</h3>
                        </div>
                        <div class="jingling-review-form-body">
                            <form method="post" action="{:U('Jingling/review', ['id' => $binding['id']])}" id="reviewForm">
                                <div class="jingling-review-form-group">
                                    <label class="jingling-review-form-label">
                                        <i class="fa fa-check-circle" style="color: #10b981; margin-right: 0.5rem;"></i>
                                        审核结果
                                    </label>
                                    <div class="jingling-review-radio-group">
                                        <label class="jingling-review-radio-item selected approve" data-value="1">
                                            <input type="radio" name="status" value="1" class="jingling-review-radio-input" checked>
                                            <div class="jingling-review-radio-icon"></div>
                                            <span>通过</span>
                                        </label>
                                        <label class="jingling-review-radio-item reject" data-value="2">
                                            <input type="radio" name="status" value="2" class="jingling-review-radio-input">
                                            <div class="jingling-review-radio-icon"></div>
                                            <span>拒绝</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="jingling-review-form-group">
                                    <label for="remark" class="jingling-review-form-label">
                                        <i class="fa fa-comment" style="color: #6b7280; margin-right: 0.5rem;"></i>
                                        审核备注
                                    </label>
                                    <textarea
                                        class="jingling-review-textarea"
                                        id="remark"
                                        name="remark"
                                        placeholder="请输入审核备注，如拒绝原因等（选填）"
                                    ></textarea>
                                    <div style="font-size: 1.25rem; color: #6b7280; margin-top: 0.5rem;">
                                        <i class="fa fa-info-circle"></i>
                                        如选择拒绝，建议填写拒绝原因以便用户了解
                                    </div>
                                </div>

                                <div class="jingling-review-form-actions">
                                    <button type="submit" class="jingling-review-action-btn btn-primary" id="submitBtn">
                                        <i class="fa fa-check"></i>
                                        <span>提交审核结果</span>
                                    </button>
                                    <a href="{:U('Jingling/index')}" class="jingling-review-action-btn btn-secondary">
                                        <i class="fa fa-arrow-left"></i>
                                        <span>返回列表</span>
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                    <else />
                    <!-- 已审核状态 -->
                    <div class="jingling-review-form-card jingling-review-fade-in-delay-3">
                        <div class="jingling-review-form-header">
                            <div class="jingling-review-form-icon">
                                <i class="fa fa-info-circle"></i>
                            </div>
                            <h3 class="jingling-review-form-title">审核已完成</h3>
                        </div>
                        <div class="jingling-review-form-body">
                            <div style="text-align: center; padding: 2rem;">
                                <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;">
                                    <i class="fa fa-check-circle"></i>
                                </div>
                                <h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">该灵工绑定已完成审核</h3>
                                <p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">审核状态和结果请查看上方信息卡片。</p>
                                <a href="{:U('Jingling/index')}" class="jingling-review-action-btn btn-secondary">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    </if>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />

<script>
    $(document).ready(function() {
        // 现代化单选按钮交互
        $('.jingling-review-radio-item').click(function() {
            var $group = $(this).closest('.jingling-review-radio-group');
            var value = $(this).data('value');

            // 移除所有选中状态
            $group.find('.jingling-review-radio-item').removeClass('selected');

            // 添加选中状态到当前项
            $(this).addClass('selected');

            // 设置对应的radio按钮为选中
            $(this).find('input[type="radio"]').prop('checked', true);

            // 根据选择的值更新样式
            if (value === '1') {
                $(this).addClass('approve').removeClass('reject');
            } else if (value === '2') {
                $(this).addClass('reject').removeClass('approve');
                // 如果选择拒绝，提示填写备注
                setTimeout(function() {
                    $('#remark').focus();
                    if ($('#remark').val().trim() === '') {
                        $('#remark').attr('placeholder', '请填写拒绝原因，以便用户了解（建议填写）');
                    }
                }, 300);
            }
        });

        // 表单提交增强
        $('#reviewForm').on('submit', function(e) {
            var $form = $(this);
            var $submitBtn = $('#submitBtn');
            var selectedStatus = $('input[name="status"]:checked').val();
            var remark = $('#remark').val().trim();

            // 如果选择拒绝但没有填写备注，给出提示
            if (selectedStatus === '2' && remark === '') {
                e.preventDefault();

                // 高亮备注框
                $('#remark').css({
                    'border-color': '#f59e0b',
                    'box-shadow': '0 0 0 3px rgba(245, 158, 11, 0.2)'
                }).focus();

                // 显示提示信息
                if (typeof layer !== 'undefined') {
                    layer.msg('选择拒绝时建议填写拒绝原因', {icon: 0, time: 3000});
                } else {
                    alert('选择拒绝时建议填写拒绝原因');
                }

                // 3秒后移除高亮
                setTimeout(function() {
                    $('#remark').css({
                        'border-color': '#e2e8f0',
                        'box-shadow': 'none'
                    });
                }, 3000);

                return false;
            }

            // 确认提交
            var statusText = selectedStatus === '1' ? '通过' : '拒绝';
            var confirmMessage = '确定要' + statusText + '该灵工绑定申请吗？';

            if (remark !== '') {
                confirmMessage += '\n\n审核备注：' + remark;
            }

            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }

            // 显示提交状态
            $submitBtn.prop('disabled', true);
            var originalHtml = $submitBtn.html();
            $submitBtn.html('<i class="fa fa-spinner fa-spin"></i> <span>提交中...</span>');

            // 显示加载提示
            if (typeof layer !== 'undefined') {
                layer.msg('正在提交审核结果...', {icon: 16, time: 0});
            }

            // 如果3秒后还没有响应，恢复按钮状态
            setTimeout(function() {
                if ($submitBtn.prop('disabled')) {
                    $submitBtn.prop('disabled', false).html(originalHtml);
                    if (typeof layer !== 'undefined') {
                        layer.closeAll();
                        layer.msg('提交超时，请重试', {icon: 2});
                    }
                }
            }, 10000);

            return true;
        });

        // 备注框增强
        $('#remark').on('focus', function() {
            $(this).css({
                'border-color': '#f59e0b',
                'box-shadow': '0 0 0 3px rgba(245, 158, 11, 0.1)',
                'background': 'white'
            });
        }).on('blur', function() {
            $(this).css({
                'border-color': '#e2e8f0',
                'box-shadow': 'none',
                'background': '#f8fafc'
            });
        });

        // 字符计数
        $('#remark').on('input', function() {
            var length = $(this).val().length;
            var $counter = $('#remarkCounter');

            if ($counter.length === 0) {
                $counter = $('<div id="remarkCounter" style="text-align: right; font-size: 1.25rem; color: #6b7280; margin-top: 0.5rem;"></div>');
                $(this).after($counter);
            }

            $counter.text(length + ' 字符');

            if (length > 200) {
                $counter.css('color', '#ef4444');
            } else if (length > 150) {
                $counter.css('color', '#f59e0b');
            } else {
                $counter.css('color', '#6b7280');
            }
        });

        // 卡片悬停效果增强
        $('.jingling-review-info-card').hover(
            function() {
                $(this).find('.jingling-review-info-card-header').css('transform', 'scale(1.02)');
            },
            function() {
                $(this).find('.jingling-review-info-card-header').css('transform', 'scale(1)');
            }
        );

        // 添加加载动画
        $('.jingling-review-info-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });

        // 滚动到顶部功能
        var $scrollToTop = $('<button class="scroll-to-top" style="position: fixed; bottom: 2rem; right: 2rem; width: 3rem; height: 3rem; background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); color: white; border: none; border-radius: 50%; font-size: 1.25rem; cursor: pointer; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; display: none; transition: all 0.3s ease;"><i class="fa fa-arrow-up"></i></button>');
        $('body').append($scrollToTop);

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $scrollToTop.fadeIn();
            } else {
                $scrollToTop.fadeOut();
            }
        });

        $scrollToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 600);
        });

        // 键盘快捷键
        $(document).on('keydown', function(e) {
            // Ctrl + Enter 提交表单
            if (e.ctrlKey && e.keyCode === 13) {
                if ($('#reviewForm').length > 0) {
                    $('#reviewForm').submit();
                }
            }
            // ESC 返回列表
            else if (e.keyCode === 27) {
                if (confirm('确定要返回列表吗？未保存的更改将丢失。')) {
                    window.location.href = "{:U('Jingling/index')}";
                }
            }
        });

        // 页面离开确认
        var formChanged = false;
        $('#reviewForm input, #reviewForm textarea').on('change input', function() {
            formChanged = true;
        });

        $(window).on('beforeunload', function(e) {
            if (formChanged && $('#reviewForm').length > 0) {
                var message = '您有未保存的审核信息，确定要离开吗？';
                e.returnValue = message;
                return message;
            }
        });

        $('#reviewForm').on('submit', function() {
            formChanged = false;
        });

        // 显示审核提示
        if ($('#reviewForm').length > 0) {
            setTimeout(function() {
                if (typeof layer !== 'undefined') {
                    layer.tips('使用 Ctrl+Enter 快速提交，ESC 返回列表', '#submitBtn', {
                        tips: [1, '#f59e0b'],
                        time: 3000
                    });
                }
            }, 2000);
        }
    });

    // 添加自定义样式增强
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .jingling-review-info-card {
                animation: fadeInUp 0.6s ease-out both;
            }

            .jingling-review-form-card {
                animation: fadeInUp 0.6s ease-out both;
            }

            .scroll-to-top:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 15px rgba(0,0,0,0.2);
            }

            .jingling-review-radio-item {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .jingling-review-radio-item:active {
                transform: scale(0.98);
            }

            .jingling-review-info-card-header {
                transition: all 0.3s ease;
            }

            .jingling-review-textarea::-webkit-scrollbar {
                width: 8px;
            }

            .jingling-review-textarea::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 4px;
            }

            .jingling-review-textarea::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 4px;
            }

            .jingling-review-textarea::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }
        `)
        .appendTo('head');
</script>