<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Servicestation/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="panel panel-info">
                    <div class="panel-body">
                       服务站：{$serviceStationlRow.service_name}
                    </div>
                </div>
                <form action="" method="post" >
                    <div class="panel panel-default">
                        <div class="panel-body table-responsive">
                            <table class="table table-hover">
                                <thead class="navbar-inner">
                                    <tr>
                                        <th width="75px">#ID</th>
                                        <th>购买数量</th>
                                        <th>购买方式</th>
                                        <th>总金额</th>
                                        <th>实付金额</th>
                                        <th>最后更新时间</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>if (empty($list)) {</php>
                                        <tr>
                                            <td colspan="7" style="text-align: center;line-height: 66px;">
                                                无 {$serviceStationlRow.service_name} 服务报购买记录！
                                            </td></tr>
                                        <php>}else{</php>
                                    <php>foreach($list as $v) { </php>
                                    <tr>
                                        <td>{$v.id}</td>
                                        <td>{:$v['buy_open_num']}</td>
                                        <td>{:$buyList[$v['buy_type']]['text']}</td>
                                        <td>{:$v['money']}</td>
                                        <td>{:$v['pay_money']}</td>
                                        <td>{:date('Y-m-d H:i:s', $v['last_edittime'])}</td>
                                        <td>{:date('Y-m-d H:i:s', $v['create_time'])}</td>
                                        <td>
                                            <a href="{:U('servicestation/editbuy', ['id' => $v['id']])}" class="btn btnc btn-primary">编辑</a><br>
                                        </td>
                                    </tr>
                                    <php>}}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />

<script>
	require(["daterangepicker"], function($){

		$('.js_status').click(function() {
			var that = $(this),
				url = that.attr('url');
			$.get(url, function(data) {
				window.location.reload();
			});

		})
	});
</script>
