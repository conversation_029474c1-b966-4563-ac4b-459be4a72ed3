﻿<!DOCTYPE html>
<html >
<head>
<title>首页</title>
<meta name="Keywords" content="关键字">
<meta name="Description" content="内容">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
<meta content="no-cache,must-revalidate" http-equiv="Cache-Control">
<meta content="telephone=no, address=no" name="format-detection">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta content="no-cache" http-equiv="pragma">
<meta content="0" http-equiv="expires">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"> 
<link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
<script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
<script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
</head>
<body>
    <div class="header02">
        <div class="box">
            <div class="left">
                <a href="{:U('index/index')}" class="goto"><i class="iconfont icon-jiantou-copy"></i></a>
            </div>
            <div class="center">
                我的用户
            </div>
        </div>
    </div> 
    <div class="tabs-hd02">
        <div class="weap">
            <ul>
                <li class="on">
                    <span class="a">全部(888888)</span>
                </li>
                <li>
                    <span class="a">已订阅(888)</span>
                </li>
                <li>
                    <span class="a">管理员(88)</span>
                </li>
                <li>
                    <span class="a">管理员(88)</span>
                </li>
            </ul>
        </div>
    </div>
    <div class="bar03">
        <div class="weap">
            <div class="left"><form action="">
                <input class="input" name="" type="search" placeholder="搜微信昵称">
                <button class="inbtn"><i class="iconfont icon-31sousuo"></i></button>
            </form></div>
<!--            <div class="right">-->
<!--                <div class="dates"><span>时间不限 <i class="iconfont icon-xialajiantouxiao"></i></span></div>-->
<!--            </div>-->
        </div>
    </div>
    <div class="screen01 mb10">
        <div class="weap">
            <ul >

                <li class="ondown">
                    查看时间<span class="arrow"><i class="iconfont icon-shangjiantou up"></i><i class="iconfont icon-xiajiantou down"></i></span>
                </li>
                <li>
                    查看次数<span class="arrow"><i class="iconfont icon-shangjiantou up"></i><i class="iconfont icon-xiajiantou down"></i></span>
                </li>
                <li>
                    跟团次数<span class="arrow"><i class="iconfont icon-shangjiantou up"></i><i class="iconfont icon-xiajiantou down"></i></span>
                </li>
                <li>
                    消费总额<span class="arrow"><i class="iconfont icon-shangjiantou up"></i><i class="iconfont icon-xiajiantou down"></i></span>
                </li>
            </ul>
        </div>
    </div>
    <div class="list02 mb10"> 
            <ul id="users">
                <include file="list-user"/>

            </ul>
        </div>
    </div>
<script type="text/javascript">
$(document).ready(function(){
$(".tabs-hd02 ul li").click(function(){
  $(this).siblings().removeClass("on");
  $(this).addClass("on");
});
$(".tabs-hd02 ul li").click(function(){
  $(this).siblings().removeClass("on");
  $(this).addClass("on");
});

$(".screen01 li").click(function(){
    if($(this).is('.ondown')){
		$(this).removeClass('ondown');
        $(this).addClass('onup');
    }
	else
	{  $(this).removeClass('onup');
		$(this).addClass('ondown');
    }
});


 
});
</script>
    <script>
        $(function () {
            $('.money').on('click', '.status', function(){
                var tip = $(this).data('tip');
                if (tip) layer.open({content: tip});
            });
            $(window).trigger("scroll");
        });
        var page=1,pages=<?= (int)$page->Total_Pages ?>;
        function loadmore(){
            if(page<pages){
                page+=1;
                $.get('/index/user?p=' + page, function(str){
                    $('#users').append(str);
                }, 'html');
            } else if (page==pages) {
                page+=1;
                setTimeout("$('.container').append('<p class=\"nomore\">------ 没有了 ------</p>')", 500);
            }
        }
        $(window).scroll(function(){
            var scrollTop = $(this).scrollTop();     //滚动条距离顶部的高度
            var scrollHeight = $(document).height(); //当前页面的总高度
            var clientHeight = $(this).height();     //当前可视的页面高度
            // console.log("top:"+scrollTop+",doc:"+scrollHeight+",client:"+clientHeight);
            if(scrollTop + clientHeight >= scrollHeight){
                loadmore();
            }else if(scrollTop<=0){
            }
        });
    </script>
</body>
</html>