<php>if($serviceStationRow['zsb_type']==2){</php>
<div class="bg01">
<div class="topbar">
  <div class="box">
    <div class="weap">
      <div class="logo"><a href="javascript:void(0);"><img src="{:$userRow['headimgurl']."2"}" alt=""></a></div>
      <div style="margin-top: 5px;">
        <php>if(mb_strlen($serviceStationRow['service_name'], 'utf-8') <= 8){</php>
          <h3 style="font-weight: bold;padding-left: 5px;">{:$serviceStationRow['service_name']}</h3>
        <php>}else{</php>
            <h1 style="font-weight: bold;padding-left: 5px;">{:$serviceStationRow['service_name']}</h1>
        <php>}</php>

        <script src="/static/js/layer/layer.m.js"></script>
        <div class="grade"><a href="javascript:void(0);">招就办主任</a></div>
      </div>

    </div>
    <a href="{:U('index/law', ['id' => '13'])}" class="gohome" style="color: red;margin-top: 10px;">特别声明<i class="iconfont icon-jinrujiantouxiao"></i></a>
  </div>
</div>
<div class="nav">
  <div class="weap">
    <ul class="clearfix">
      <li>
        <a href="{:U('index/index')}">
          <div class="ico"><img src="/static/stations/images/ico-a0.png" alt=""></div>
          <div class="title">
           管理中心
          </div>
        </a>
      </li>
      <li>
        <a href="{:U('index/joblist')}">
          <div class="ico"><img src="/static/stations/images/ico-a03.png" alt="">{:$jobmsgcount ? '<i></i>' : ''}</div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="/index/joblist"){</php> style="font-weight:bold;"<php>}</php>>
            简历管理
          </div>
        </a>
      </li>
    <li>
        <a href="{:U('training/index')}">
          <div class="ico"><img src="/static/stations/images/bm.png" alt=""></div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="training/index"){</php> style="font-weight:bold;"<php>}</php>>
            培训管理
          </div>
        </a>
      </li>
    </ul>
  </div>

  <php>}else{</php>
<div class="bg01">
<div class="topbar">
  <div class="box">
    <div class="weap">
      <div class="logo"><a href="javascript:void(0);"><img src="{:$userRow['headimgurl']."2"}" alt=""></a></div>
      <div style="margin-top: 5px;">
        <php>if(mb_strlen($serviceStationRow['service_name'], 'utf-8') <= 8){</php>
          <h3 style="font-weight: bold;padding-left: 5px;">{:$serviceStationRow['service_name']}
           <php>if(in_array($userRow['id'], [2, 68, 75])){</php>
          <a class="cjz" href="javascript:void(0);" style="font-weight: normal;color:rgb(229, 122, 14);"  onclick="showStationSwitcher()"><i class="iconfont icon-zuoyoujiantou"></i></a>
          <php>}</php>

          </h3>
        <php>}else{</php>
            <h1 style="font-weight: bold;padding-left: 5px;">{:$serviceStationRow['service_name']}
           <php>if(in_array($userRow['id'], [2, 68, 75])){</php>
          <a class="cjz" href="javascript:void(0);" style="font-weight: normal;color:rgb(229, 122, 14);"  onclick="showStationSwitcher()"><i class="iconfont icon-zuoyoujiantou"></i></a>
          <php>}</php>

            </h1>
        <php>}</php>
        <script src="/static/js/layer/layer.m.js"></script>
         <div class="grade">
          <a href="javascript:void(0);">服务站</a>
           <php>if ($userRow['is_first_wechat'] == 1 && $serviceStationRow['zsb_type']==1){</php> 
            <a href="{:U('index/loginaccount')}" style="color:#666;border-color: #000;">登录管理</a>  
            <php>}</php>
          </div>
      </div>
    </div>
    <a href="{:U('index/law', ['id' => '13'])}" class="gohome" style="color: red;margin-top: 10px;">特别声明<i class="iconfont icon-jinrujiantouxiao"></i></a>
  </div>
</div>
<div class="nav">
  <div class="weap">
    <ul class="clearfix">
      <li>
        <a href="{:U('index/index')}">
          <div class="ico"><img src="/static/stations/images/ico-a0.png" alt=""></div>
          <div class="title">
           管理中心
          </div>
        </a>
      </li>
      <li>
        <a href="{:U('index/servicestation')}">
          <div class="ico"><img src="/static/stations/images/ico-a04.png" alt=""></div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="/index/servicestation"){</php> style="font-weight:bold;"<php>}</php>>
            服务站
          </div>
        </a>
      </li>
      <li>
        <a href="{:U('index/zjb')}" id="zjbNavBtn">
          <div class="ico"><img src="/static/stations/images/tgy.png" alt=""><i id="zjbNavNotifyDot" style="display: none;"></i></div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="/index/zjb"){</php> style="font-weight:bold;"<php>}</php>>
            招就办
          </div>
        </a>
      </li>
      <li>
        <a href="{:U('index/joblist')}">
          <div class="ico"><img src="/static/stations/images/ico-a03.png" alt="">{:$jobmsgcount ? '<i></i>' : ''}</div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="/index/joblist"){</php> style="font-weight:bold;"<php>}</php>>
            简历管理
          </div>
        </a>
      </li>
            <li>
        <a href="{:U('training/index')}">
          <div class="ico"><img src="/static/stations/images/bm.png" alt=""></div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="training/index"){</php> style="font-weight:bold;"<php>}</php>>
            培训管理
          </div>
        </a>
      </li>
    </ul>
  </div>
    <php>}</php>
</div>

<php>if ($adList && $serviceStationRow['zsb_type']==1) {</php>
<div class="notice mb10">
  <div class="weap">
    <div class="swiper noticeSwiper">
      <i class="iconfont icon-gonggao"></i>
      <div class="swiper-wrapper">
        <php>foreach($adList as $adRow) {</php>
        <div class="swiper-slide"><a class="a" href="{:U('index/ad', ['id' => $adRow['id']])}">{:$adRow['title']}<i class="iconfont icon-jinrujiantouxiao"></i></a></div>
        <php>}</php>
      </div>
    </div>
  </div>
</div>
<php>}</php>

<script>
  // 任务问好
function workwenhao() {

layer.closeAll();
layer.open({
    style : "width:80%",
    shadeClose : false,
    content: "<div class='t-container'><h1>推荐服务站数量对应服务站等级</h1><div class='table-container'><table><thead><tr><th>数量</th><th>级别</th><th>公益补贴</th><th>服务补贴</th></tr></thead><tbody><tr><td>1个</td><td>普通</td><td>-</td><td>-</td></tr><tr><td>2个</td><td class='highlight'>V1级别</td><td class='highlight'>10%</td><td class='highlight'>2%</td></tr><tr><td>5个</td><td class='highlight'>V2级别</td><td class='highlight'>20%</td><td class='highlight'>3%</td></tr><tr><td>10个</td><td class='highlight'>V3级别</td><td class='highlight'>30%</td><td class='highlight'>5%</td></tr></tbody></table></div><h2><strong>公益补贴：</strong>是指您推荐的服务站参与新能源或其他公益实习获得政策补贴返费后，平台额外给予您的补贴奖励，不影响被您推荐的服务站基础收益。<p style='color:#00bf80;padding-top: 8px;'>举个例子，您推荐的所有服务站有1000人参与公益实习并领取工资，平台在获得国家政策补贴之后，您推荐的服务站总获得1000*100=100,000元奖励，您作为推荐人，按照您的服务站级别对应获得平台额外给予补贴奖励为（V1级别：10,000元；V2级别：20,000元；V3级别：30,000元）。</p></h2><h2><strong>服务补贴：</strong>是指您推荐的服务站在平台有且不限于产生业绩时，平台额外给予您的服务奖励，不影响被您推荐的服务站基础收益。</h2><h2><strong>特别声明：</strong>所有服务站为平行合作关系，无上下层级；推荐人无推荐提成、无介绍费。平台仅接受“熟带熟”推荐方式才能加盟成为服务站。</h2></div>",
    btn: ['我知道了'],
    yes: function (index) {
    layer.closeAll();
    window.location.reload();
    },                  
    });                   
}
</script>