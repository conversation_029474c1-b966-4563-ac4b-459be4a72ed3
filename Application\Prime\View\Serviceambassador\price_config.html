<include file="block/hat" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化价格配置页面样式 */
                .price-config-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .price-config-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .price-config-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .price-config-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .price-config-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .price-config-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .price-config-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .price-config-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .price-config-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .price-config-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                /* 现代化导航标签 */
                .price-config-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .price-config-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .price-config-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .price-config-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .price-config-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .price-config-nav-link.active {
                    color: #667eea;
                    background: white;
                    border-bottom-color: #667eea;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1);
                }

                .price-config-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .price-config-nav-icon {
                    font-size: 1.25rem;
                }

                /* 招就办信息卡片 */
                .zsb-info-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .zsb-info-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
                }

                .zsb-info-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .zsb-info-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .zsb-info-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .zsb-info-body {
                    padding: 2rem;
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 1.5rem;
                }

                .zsb-info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.5rem;
                }

                .zsb-info-label {
                    color: #6b7280;
                    font-weight: 500;
                    min-width: 80px;
                }

                .zsb-info-value {
                    color: #374151;
                    font-weight: 600;
                    flex: 1;
                }

                /* 价格配置卡片 */
                .price-config-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .price-config-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .price-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 1.5rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .price-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .price-card-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .price-card-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .price-card-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin: 0;
                }

                .price-card-job-name {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .price-card-project-name {
                    font-size: 1.25rem;
                    color: rgba(255, 255, 255, 0.9);
                    margin: 0.25rem 0 0 0;
                    font-weight: 400;
                }

                .price-card-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .price-status-badge {
                    padding: 0.5rem 1rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                }

                .price-status-badge.status-open {
                    background: rgba(16, 185, 129, 0.9);
                    border-color: rgba(16, 185, 129, 0.5);
                }

                .price-status-badge.status-closed {
                    background: rgba(239, 68, 68, 0.9);
                    border-color: rgba(239, 68, 68, 0.5);
                }

                .price-card-body {
                    padding: 2rem;
                }

                .price-info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1.5rem;
                    margin-bottom: 2rem;
                }

                .price-info-item {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    border: 1px solid #e2e8f0;
                    text-align: center;
                    transition: all 0.3s ease;
                }

                .price-info-item:hover {
                    background: #f1f5f9;
                    transform: translateY(-2px);
                }

                .price-info-label {
                    font-size: 1.25rem;
                    color: #6b7280;
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                }

                .price-info-value {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                }

                .price-info-value.cost-price {
                    color: #ef4444;
                }

                .price-info-value.sale-price {
                    color: #3b82f6;
                }

                .price-info-value.platform-fee {
                    color: #f59e0b;
                }

                .price-info-value.commission {
                    color: #10b981;
                }

                .price-card-actions {
                    display: flex;
                    gap: 1rem;
                    margin-top: 2rem;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                    justify-content: center;
                }

                .price-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                    text-decoration: none;
                }

                .price-action-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 空状态样式 */
                .empty-state {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    padding: 4rem 2rem;
                    text-align: center;
                    margin-bottom: 2rem;
                }

                .empty-state-icon {
                    width: 4rem;
                    height: 4rem;
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 2rem;
                    margin: 0 auto 1.5rem;
                }

                .empty-state-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 0.5rem;
                }

                .empty-state-text {
                    font-size: 1.5rem;
                    color: #6b7280;
                    margin-bottom: 2rem;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .price-config-container {
                        padding: 1.5rem;
                    }

                    .price-config-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .price-info-grid {
                        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    }
                }

                @media (max-width: 768px) {
                    .price-config-container {
                        padding: 1rem;
                    }

                    .price-config-nav-tabs {
                        flex-direction: column;
                    }

                    .price-config-nav-item {
                        flex: none;
                    }

                    .price-config-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .price-config-nav-link.active {
                        border-bottom: none;
                        border-right-color: #667eea;
                    }

                    .price-config-title-main {
                        font-size: 1.75rem;
                    }

                    .price-config-title-sub {
                        font-size: 1.25rem;
                    }

                    .price-card-header {
                        flex-direction: column;
                        gap: 1rem;
                        text-align: center;
                    }

                    .zsb-info-body {
                        grid-template-columns: 1fr;
                    }

                    .price-info-grid {
                        grid-template-columns: 1fr;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .price-config-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .price-config-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .price-config-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .price-config-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="price-config-wrapper">
                <div class="price-config-container">
                    <!-- 现代化页面标题 -->
                    <div class="price-config-header price-config-fade-in">
                        <div class="price-config-header-content">
                            <div class="price-config-title">
                                <div class="price-config-title-icon">
                                    <i class="fa fa-cog"></i>
                                </div>
                                <div class="price-config-title-text">
                                    <h1 class="price-config-title-main">价格配置</h1>
                                    <p class="price-config-title-sub">Price Configuration Management</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="price-config-nav-container price-config-fade-in-delay-1">
                        <ul class="price-config-nav-tabs">
                            <li class="price-config-nav-item">
                                <a href="{:U('Serviceambassador/index')}" class="price-config-nav-link">
                                    <i class="fa fa-list price-config-nav-icon"></i>
                                    <span>招就办管理</span>
                                </a>
                            </li>
                            <li class="price-config-nav-item">
                                <a href="{:U('Serviceambassador/edit')}" class="price-config-nav-link">
                                    <i class="fa fa-plus price-config-nav-icon"></i>
                                    <span>添加招就办</span>
                                </a>
                            </li>
                            <li class="price-config-nav-item">
                                <a href="#" class="price-config-nav-link active">
                                    <i class="fa fa-cog price-config-nav-icon"></i>
                                    <span>价格配置</span>
                                </a>
                            </li>
                            <li class="price-config-nav-item">
                                <a href="{:U('Sys/platform_rate_config')}" class="price-config-nav-link">
                                    <i class="fa fa-cogs price-config-nav-icon"></i>
                                    <span>平台费率配置</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 招就办信息卡片 -->
                    <div class="zsb-info-card price-config-fade-in-delay-2">
                        <div class="zsb-info-header">
                            <div class="zsb-info-icon">
                                <i class="fa fa-user"></i>
                            </div>
                            <h3 class="zsb-info-title">招就办信息</h3>
                        </div>
                        <div class="zsb-info-body">
                            <div class="zsb-info-item">
                                <span class="zsb-info-label">招就办名称：</span>
                                <span class="zsb-info-value">{$zsbInfo.service_name}</span>
                            </div>
                            <div class="zsb-info-item">
                                <span class="zsb-info-label">联系人：</span>
                                <span class="zsb-info-value">{$zsbInfo.contract_name}</span>
                            </div>
                            <div class="zsb-info-item">
                                <span class="zsb-info-label">联系电话：</span>
                                <span class="zsb-info-value">{$zsbInfo.mobile}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 价格配置列表 -->
                    <div class="price-config-card price-config-fade-in-delay-3">
                        <php>if(!empty($priceList['list'])) {</php>
                        <php>foreach($priceList['list'] as $price) {</php>
                        <div class="price-card">
                            <!-- 卡片头部 -->
                            <div class="price-card-header">
                                <div class="price-card-title">
                                    <h3 class="price-card-job-name">{$price.job_name}</h3>
                                    <p class="price-card-project-name">{$price.project_name}</p>
                                </div>
                                <div class="price-card-status">
                                    <php>if($price['status'] == 1) {</php>
                                    <span class="price-status-badge status-open">
                                        <i class="fa fa-check-circle"></i>
                                        开放
                                    </span>
                                    <php>} else {</php>
                                    <span class="price-status-badge status-closed">
                                        <i class="fa fa-times-circle"></i>
                                        不开放
                                    </span>
                                    <php>}</php>
                                </div>
                            </div>

                            <!-- 卡片主体 -->
                            <div class="price-card-body">
                                <div class="price-info-grid">
                                    <div class="price-info-item">
                                        <div class="price-info-label">
                                            <i class="fa fa-money"></i>
                                            成本价
                                        </div>
                                        <div class="price-info-value cost-price">¥{$price.cost_price}</div>
                                    </div>
                                    <div class="price-info-item">
                                        <div class="price-info-label">
                                            <i class="fa fa-tag"></i>
                                            对外价
                                        </div>
                                        <div class="price-info-value sale-price">¥{$price.sale_price}</div>
                                    </div>
                                    <div class="price-info-item">
                                        <div class="price-info-label">
                                            <i class="fa fa-cog"></i>
                                            平台服务费
                                        </div>
                                        <div class="price-info-value platform-fee">
                                            <php>if(isset($price['platform_fee']) && $price['platform_fee'] > 0) {</php>
                                            ¥{$price.platform_fee}
                                            <php>} else {</php>
                                            --
                                            <php>}</php>
                                        </div>
                                    </div>
                                    <div class="price-info-item">
                                        <div class="price-info-label">
                                            <i class="fa fa-star"></i>
                                            招就办提成
                                        </div>
                                        <div class="price-info-value commission">¥{$price.commission}</div>
                                    </div>
                                </div>

                                <div class="price-info-item" style="background: #f0f9ff; border-color: #0ea5e9;">
                                    <div class="price-info-label" style="color: #0369a1;">
                                        <i class="fa fa-clock-o"></i>
                                        创建时间
                                    </div>
                                    <div class="price-info-value" style="color: #0369a1; font-size: 1.5rem;">{:date('Y-m-d H:i', $price['create_time'])}</div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="price-card-actions">
                                    <button class="price-action-btn" onclick="editPrice({$price.post_id}, '{$price.job_name}', {$price.cost_price}, {$price.sale_price}, {$price.status}, <php>echo isset($price['service_price']) ? $price['service_price'] : 0;</php>, <php>echo isset($price['max_price']) ? $price['max_price'] : 0;</php>, <php>echo isset($price['base_cost']) ? $price['base_cost'] : 0;</php>)">
                                        <i class="fa fa-edit"></i>
                                        <span>编辑价格</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <php>}</php>
                        <php>} else {</php>
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fa fa-exclamation-triangle"></i>
                            </div>
                            <h3 class="empty-state-title">暂无价格配置</h3>
                            <p class="empty-state-text">请添加岗位价格配置以开始管理。</p>
                        </div>
                        <php>}</php>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 现代化编辑价格弹窗 -->
<div class="modal fade" id="editPriceModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="border-radius: 1rem; border: none; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 1rem 1rem 0 0; padding: 2rem; border-bottom: none;">
                <button type="button" class="close" data-dismiss="modal" style="color: white; opacity: 0.8; font-size: 2rem; text-shadow: none;">&times;</button>
                <h4 class="modal-title" style="font-size: 2rem; font-weight: 600; margin: 0; display: flex; align-items: center; gap: 0.75rem;">
                    <i class="fa fa-edit"></i>
                    编辑岗位价格
                </h4>
            </div>
            <div class="modal-body" style="padding: 2rem; background: #f8fafc;">
                <form id="editPriceForm" style="background: white; border-radius: 0.75rem; padding: 2rem; margin-bottom: 1rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <input type="hidden" name="zsb_id" value="{$zsbInfo.id}">
                    <input type="hidden" name="post_id" id="editPostId">

                    <!-- 岗位信息区域 -->
                    <div style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border-radius: 0.75rem; padding: 1.5rem; margin-bottom: 2rem; border: 1px solid #0ea5e9;">
                        <h5 style="color: #0369a1; font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fa fa-info-circle"></i>
                            岗位信息
                        </h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div style="margin-bottom: 1rem;">
                                    <label style="color: #0369a1; font-weight: 500; font-size: 1.25rem; margin-bottom: 0.5rem; display: block;">岗位名称</label>
                                    <p id="editPostName" style="color: #1e40af; font-weight: 600; font-size: 1.5rem; margin: 0; padding: 0.5rem; background: rgba(59, 130, 246, 0.1); border-radius: 0.5rem;">--</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div style="margin-bottom: 1rem;">
                                    <label style="color: #0369a1; font-weight: 500; font-size: 1.25rem; margin-bottom: 0.5rem; display: block;">报价区间</label>
                                    <p id="editPriceRange" style="color: #1e40af; font-weight: 600; font-size: 1.5rem; margin: 0; padding: 0.5rem; background: rgba(59, 130, 246, 0.1); border-radius: 0.5rem;">--</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div style="margin-bottom: 1rem;">
                                    <label style="color: #0369a1; font-weight: 500; font-size: 1.25rem; margin-bottom: 0.5rem; display: block;">基准成本价</label>
                                    <p id="editBaseCost" style="color: #1e40af; font-weight: 600; font-size: 1.5rem; margin: 0; padding: 0.5rem; background: rgba(59, 130, 246, 0.1); border-radius: 0.5rem;">--</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 价格设置区域 -->
                    <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border-radius: 0.75rem; padding: 1.5rem; margin-bottom: 2rem; border: 1px solid #f59e0b;">
                        <h5 style="color: #92400e; font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fa fa-edit"></i>
                            价格设置
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div style="margin-bottom: 1.5rem;">
                                    <label style="color: #92400e; font-weight: 500; font-size: 1.25rem; margin-bottom: 0.5rem; display: block;">
                                        <i class="fa fa-money" style="margin-right: 0.5rem;"></i>
                                        成本价（元）
                                    </label>
                                    <input type="text" name="cost_price" id="editCostPrice"
                                           style="border: 2px solid #f59e0b; border-radius: 0.5rem; padding: 0.75rem; font-size: 1.5rem; font-weight: 600; width: 100%; background: white;"
                                           required oninput="validatePositiveIntegerWithFormatting(this)" onblur="applyPriceFormatting(this)" placeholder="请输入正整数">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div style="margin-bottom: 1.5rem;">
                                    <label style="color: #92400e; font-weight: 500; font-size: 1.25rem; margin-bottom: 0.5rem; display: block;">
                                        <i class="fa fa-tag" style="margin-right: 0.5rem;"></i>
                                        对外价（元）
                                    </label>
                                    <input type="text" name="sale_price" id="editSalePrice"
                                           style="border: 2px solid #f59e0b; border-radius: 0.5rem; padding: 0.75rem; font-size: 1.5rem; font-weight: 600; width: 100%; background: white;"
                                           required oninput="validatePositiveIntegerWithFormatting(this)" onblur="applyPriceFormatting(this)" placeholder="请输入正整数">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收益计算区域 -->
                    <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border-radius: 0.75rem; padding: 1.5rem; margin-bottom: 2rem; border: 1px solid #10b981;">
                        <h5 style="color: #065f46; font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fa fa-calculator"></i>
                            收益计算
                        </h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div style="text-align: center; padding: 1rem; background: rgba(16, 185, 129, 0.1); border-radius: 0.5rem; margin-bottom: 1rem;">
                                    <label style="color: #065f46; font-weight: 500; font-size: 1.25rem; margin-bottom: 0.5rem; display: block;">
                                        <i class="fa fa-cog" style="margin-right: 0.5rem;"></i>
                                        平台服务费
                                    </label>
                                    <span id="editPlatformFeeDisplay" style="color: #047857; font-weight: 700; font-size: 2rem; display: block;">--</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div style="text-align: center; padding: 1rem; background: rgba(16, 185, 129, 0.1); border-radius: 0.5rem; margin-bottom: 1rem;">
                                    <label style="color: #065f46; font-weight: 500; font-size: 1.25rem; margin-bottom: 0.5rem; display: block;">
                                        <i class="fa fa-star" style="margin-right: 0.5rem;"></i>
                                        招就办提成
                                    </label>
                                    <span id="editCommissionDisplay" style="color: #047857; font-weight: 700; font-size: 2rem; display: block;">0 元</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div style="text-align: center; padding: 1rem; background: rgba(16, 185, 129, 0.1); border-radius: 0.5rem; margin-bottom: 1rem;">
                                    <label style="color: #065f46; font-weight: 500; font-size: 1.25rem; margin-bottom: 0.5rem; display: block;">
                                        <i class="fa fa-trophy" style="margin-right: 0.5rem;"></i>
                                        您的收益
                                    </label>
                                    <span id="editStationProfitDisplay" style="color: #047857; font-weight: 700; font-size: 2rem; display: block;">--</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 警告提示区域 -->
                    <div id="editWarningAlert" style="display: none; background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%); border: 1px solid #ef4444; border-radius: 0.75rem; padding: 1rem; margin-bottom: 2rem;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fa fa-exclamation-triangle" style="color: #dc2626; font-size: 1.5rem;"></i>
                            <span id="editWarningText" style="color: #dc2626; font-weight: 600; font-size: 1.25rem;"></span>
                        </div>
                    </div>

                    <!-- 状态设置区域 -->
                    <div style="background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); border-radius: 0.75rem; padding: 1.5rem; margin-bottom: 1rem; border: 1px solid #9ca3af;">
                        <h5 style="color: #374151; font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fa fa-toggle-on"></i>
                            状态设置
                        </h5>
                        <div style="margin-bottom: 1rem;">
                            <label style="color: #374151; font-weight: 500; font-size: 1.25rem; margin-bottom: 0.5rem; display: block;">
                                <i class="fa fa-cog" style="margin-right: 0.5rem;"></i>
                                开放状态
                            </label>
                            <select name="status" id="editStatus" style="border: 2px solid #9ca3af; border-radius: 0.5rem; padding: 0.75rem; font-size: 1.5rem; font-weight: 600; width: 100%; background: white;">
                                <php>foreach($statusList as $k => $v) {</php>
                                <option value="{$k}">{$v.text}</option>
                                <php>}</php>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="background: #f8fafc; border-radius: 0 0 1rem 1rem; padding: 1.5rem 2rem; border-top: 1px solid #e2e8f0; display: flex; justify-content: flex-end; gap: 1rem;">
                <button type="button" class="btn btn-default" data-dismiss="modal" style="padding: 0.75rem 1.5rem; font-size: 1.5rem; font-weight: 600; border-radius: 0.5rem; border: 2px solid #d1d5db; background: white; color: #374151;">
                    <i class="fa fa-times" style="margin-right: 0.5rem;"></i>
                    取消
                </button>
                <button type="button" class="btn btn-primary" onclick="saveEditPrice()" style="padding: 0.75rem 1.5rem; font-size: 1.5rem; font-weight: 600; border-radius: 0.5rem; border: none; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);">
                    <i class="fa fa-save" style="margin-right: 0.5rem;"></i>
                    保存
                </button>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />

<script>
// 全局变量：动态平台费率
var dynamicPlatformRate = 0.30; // 默认值，页面加载时会更新

// 页面加载完成后获取动态平台费率
$(document).ready(function() {
    // 获取动态平台费率
    getDynamicPlatformRate();
});

// 获取动态平台费率
function getDynamicPlatformRate() {
    $.ajax({
        url: '{:U("sys/get_platform_rate")}',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                dynamicPlatformRate = response.rate;
                console.log('动态平台费率已更新:', dynamicPlatformRate);

                // 如果页面上有显示费率的地方，可以更新显示
                updatePlatformRateDisplay();
            } else {
                console.warn('获取平台费率失败，使用默认值:', dynamicPlatformRate);
            }
        },
        error: function() {
            console.warn('获取平台费率网络错误，使用默认值:', dynamicPlatformRate);
        }
    });
}

// 更新页面上的平台费率显示
function updatePlatformRateDisplay() {
    // 如果页面上有显示平台费率的元素，可以在这里更新
    var ratePercent = (dynamicPlatformRate * 100).toFixed(1) + '%';

    // 更新所有显示平台费率的地方
    $('.platform-rate-display').text(ratePercent);

    // 如果当前有编辑表单打开，重新计算显示
    if ($('#editModal').hasClass('in')) {
        updateEditCalculation();
    }
}
</script>

<script>

// 全局变量
var currentPostServicePrice = 0; // 当前岗位的最低报价
var currentPostMaxPrice = 0; // 当前岗位的最高报价
var currentBaseCost = 0; // 当前岗位的基准成本价

// 格式化金额显示（添加千分符）
function formatMoney(amount) {
    if (amount === null || amount === undefined || amount === '') {
        return '0';
    }

    var num = parseFloat(amount);
    if (isNaN(num)) {
        return '0';
    }

    // 转换为整数（因为价格都是整数）
    num = Math.round(num);

    // 添加千分符
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 价格格式化核心函数：将价格向下取整到千位
function formatPriceToThousands(value) {
    if (!value || value === '') {
        return 0;
    }

    var num = parseInt(value);
    if (isNaN(num) || num <= 0) {
        return 0;
    }

    // 向下取整到千位（保留千位以上的数字，去除百位、十位、个位）
    return Math.floor(num / 1000) * 1000;
}

// 格式化报价区间显示
function formatPriceRange(servicePrice, maxPrice) {
    if (!servicePrice || servicePrice <= 0) {
        return '报价区间：待定';
    }

    if (!maxPrice || maxPrice <= servicePrice) {
        return '报价区间：' + formatMoney(servicePrice) + '元';
    }

    return '报价区间：' + formatMoney(servicePrice) + '-' + formatMoney(maxPrice) + '元';
}

// 获取输入框的纯数字值（移除千分符）
function getNumericValue(input) {
    if (!input || !input.value) {
        return 0;
    }

    // 移除千分符和其他非数字字符，只保留数字
    var value = input.value.toString().replace(/[^\d]/g, '');
    return parseInt(value) || 0;
}

// 显示验证错误提示
function showValidationError(message) {
    $('#editWarningText').text(message);
    $('#editWarningAlert').removeClass('alert-info alert-warning').addClass('alert-danger').show();

    // 3秒后自动隐藏
    setTimeout(function() {
        $('#editWarningAlert').fadeOut();
    }, 3000);
}

// 实时验证正整数输入并应用格式化
function validatePositiveIntegerWithFormatting(input) {
    // 移除所有非数字字符
    var value = input.value.replace(/[^\d]/g, '');

    // 如果为空，直接返回
    if (!value) {
        input.value = '';
        input.setCustomValidity('');
        input.style.borderColor = '';
        calculateEditCommission();
        return;
    }

    var intValue = parseInt(value);
    if (isNaN(intValue) || intValue <= 0) {
        input.setCustomValidity('请输入正整数');
        input.style.borderColor = '#dc3545';
        return;
    }

    // 实时添加千分符显示
    var formattedDisplay = formatMoney(intValue);

    // 保存光标位置
    var cursorPosition = input.selectionStart;
    var oldLength = input.value.length;

    // 更新输入框值为千分符格式
    input.value = formattedDisplay;

    // 恢复光标位置（考虑千分符的影响）
    var newLength = input.value.length;
    var lengthDiff = newLength - oldLength;
    var newCursorPosition = cursorPosition + lengthDiff;

    // 确保光标位置在有效范围内
    if (newCursorPosition < 0) newCursorPosition = 0;
    if (newCursorPosition > newLength) newCursorPosition = newLength;

    // 设置光标位置
    setTimeout(function() {
        input.setSelectionRange(newCursorPosition, newCursorPosition);
    }, 0);

    // 如果是成本价输入框，验证是否不低于基准成本价（使用原始输入值）
    if (input.id === 'editCostPrice') {
        if (currentBaseCost > 0 && intValue < currentBaseCost) {
            input.setCustomValidity('成本价不得低于基准成本价（' + formatMoney(currentBaseCost) + '元）');
            input.style.borderColor = '#dc3545';
            return;
        }
    }

    // 如果是对外价输入框，验证是否不超过最高报价
    if (input.id === 'editSalePrice') {
        if (currentPostMaxPrice > 0 && intValue > currentPostMaxPrice) {
            input.setCustomValidity('对外价不能超过最高报价（' + formatMoney(currentPostMaxPrice) + '元）');
            input.style.borderColor = '#dc3545';
            return;
        }
    }

    // 验证通过，清除所有提示
    input.setCustomValidity('');
    input.style.borderColor = '#28a745';

    // 重新计算佣金
    calculateEditCommission();
}

// 应用价格格式化（失去焦点时触发）
function applyPriceFormatting(input) {
    if (input.value && input.value !== '') {
        var originalValue = getNumericValue(input);
        var formattedValue = formatPriceToThousands(originalValue);

        if (formattedValue > 0) {
            input.value = formatMoney(formattedValue);
        } else {
            input.value = '';
        }

        // 重新计算佣金
        calculateEditCommission();
    }
}

// 计算编辑时的佣金和平台服务费
function calculateEditCommission() {
    var costPrice = getNumericValue(document.getElementById('editCostPrice')) || 0;
    var salePrice = getNumericValue(document.getElementById('editSalePrice')) || 0;

    // 隐藏警告提示
    $('#editWarningAlert').hide();

    // 只有当两个价格都大于0时才计算佣金
    if (costPrice <= 0 || salePrice <= 0) {
        $('#editCommissionDisplay').html('<span class="text-muted">请输入完整价格信息</span>');
        $('#editPlatformFeeDisplay').text('--');
        return;
    }

    // 计算平台服务费：max(0, (对外价 - 最低报价)) × 动态平台费率
    var platformFee = Math.max(0, (salePrice - currentPostServicePrice)) * dynamicPlatformRate;

    // 计算招就办主任提成：对外价 - 成本价 - 平台服务费
    var commission = salePrice - costPrice - platformFee;

    // 计算服务站收益：成本价 - 基准成本价
    var stationProfit = 0;
    if (currentBaseCost > 0 && costPrice > 0) {
        stationProfit = costPrice - currentBaseCost;
    }

    // 更新平台服务费显示
    $('#editPlatformFeeDisplay').text(formatMoney(platformFee));

    // 更新服务站收益显示
    if (currentBaseCost > 0 && costPrice > 0) {
        var profitColor = stationProfit >= 0 ? '#28a745' : '#dc3545';
        $('#editStationProfitDisplay').html('<span style="color: ' + profitColor + ';">' + formatMoney(stationProfit) + '</span>');
    } else {
        $('#editStationProfitDisplay').text('--');
    }

    // 更新佣金显示和警告提示
    if (commission < 0) {
        $('#editCommissionDisplay').html('<span style="color: #dc3545;">' + formatMoney(commission) + '</span>');
        // 显示负佣金警告
        $('#editWarningText').text('佣金为负数（' + formatMoney(commission) + '元），请调整价格配置。当前平台服务费为' + formatMoney(platformFee) + '元。');
        $('#editWarningAlert').removeClass('alert-warning alert-info').addClass('alert-danger').show();
    } else {
        $('#editCommissionDisplay').html('<span style="color: #28a745;">' + formatMoney(commission) + '</span>');
        // 如果佣金较低但为正数，显示提示信息
        if (commission > 0 && commission < 100) {
            $('#editWarningText').text('佣金较低（' + formatMoney(commission) + '元），建议适当调整价格以获得更好收益。');
            $('#editWarningAlert').removeClass('alert-danger alert-warning').addClass('alert-info').show();
        }
    }

    // 在页面上显示计算详情，用于调试
    console.log('计算详情：对外价=' + salePrice + ', 成本价=' + costPrice + ', 平台服务费=' + platformFee + ', 佣金=' + commission);
}

// 编辑价格
function editPrice(postId, postName, costPrice, salePrice, status, servicePrice, maxPrice, baseCost) {
    // 设置全局变量
    currentPostServicePrice = servicePrice || 0;
    currentPostMaxPrice = maxPrice || 0;
    currentBaseCost = baseCost || 0;

    $('#editPostId').val(postId);
    $('#editPostName').text(postName);

    // 显示报价区间
    $('#editPriceRange').text(formatPriceRange(currentPostServicePrice, currentPostMaxPrice));

    // 显示基准成本价
    if (currentBaseCost > 0) {
        $('#editBaseCost').text(formatMoney(currentBaseCost) + '元');
    } else {
        $('#editBaseCost').text('--');
    }

    // 设置价格值并应用格式化
    if (costPrice > 0) {
        $('#editCostPrice').val(formatMoney(costPrice));
    } else {
        $('#editCostPrice').val('');
    }

    if (salePrice > 0) {
        $('#editSalePrice').val(formatMoney(salePrice));
    } else {
        $('#editSalePrice').val('');
    }

    $('#editStatus').val(status);

    // 隐藏警告提示
    $('#editWarningAlert').hide();

    calculateEditCommission();
    $('#editPriceModal').modal('show');
}

// 保存编辑
function saveEditPrice() {
    // 获取纯数字值（移除千分符）
    var costPriceValue = getNumericValue(document.getElementById('editCostPrice'));
    var salePriceValue = getNumericValue(document.getElementById('editSalePrice'));

    // 应用格式化到纯数字值
    if (costPriceValue) {
        costPriceValue = formatPriceToThousands(parseInt(costPriceValue));
    }
    if (salePriceValue) {
        salePriceValue = formatPriceToThousands(parseInt(salePriceValue));
    }

    var formData = {
        zsb_id: $('input[name="zsb_id"]').val(),
        post_id: $('#editPostId').val(),
        cost_price: costPriceValue,
        sale_price: salePriceValue,
        status: $('#editStatus').val()
    };

    // 验证
    if (!formData.cost_price || !formData.sale_price) {
        showValidationError('请填写成本价和对外价');
        return;
    }

    // 验证是否为正整数
    var costPrice = parseInt(formData.cost_price);
    var salePrice = parseInt(formData.sale_price);

    if (isNaN(costPrice) || costPrice <= 0) {
        showValidationError('成本价必须为正整数');
        return;
    }

    if (isNaN(salePrice) || salePrice <= 0) {
        showValidationError('对外价必须为正整数');
        return;
    }

    if (salePrice < costPrice) {
        showValidationError('对外价不能低于成本价');
        return;
    }

    // 验证对外价不能超过最高报价
    if (currentPostMaxPrice > 0 && salePrice > currentPostMaxPrice) {
        showValidationError('对外价不能超过最高报价（' + formatMoney(currentPostMaxPrice) + '元）');
        return;
    }

    // 验证成本价不得低于基准成本价
    if (currentBaseCost > 0 && costPrice < currentBaseCost) {
        showValidationError('成本价不得低于基准成本价（' + formatMoney(currentBaseCost) + '元）');
        return;
    }

    // 验证招就办主任提成不能为负数
    var platformFee = Math.max(0, (salePrice - currentPostServicePrice)) * dynamicPlatformRate;
    var commission = salePrice - costPrice - platformFee;
    if (commission < 0) {
        showValidationError('招就办主任提成不能为负数。当前平台服务费为' + formatMoney(platformFee) + '元，请调整价格配置');
        return;
    }

    // 显示加载状态
    var $btn = $('.modal-footer .btn-primary');
    var originalText = $btn.text();
    $btn.text('保存中...').prop('disabled', true);

    $.ajax({
        url: '{:U("serviceambassador/setPriceAjax")}',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                alert('保存成功');
                location.reload();
            } else {
                alert(response.msg || '保存失败');
            }
        },
        error: function() {
            alert('网络错误，请重试');
        },
        complete: function() {
            // 恢复按钮状态
            $btn.text(originalText).prop('disabled', false);
        }
    });
}


</script>
