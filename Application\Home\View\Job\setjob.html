<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="/static/job/styles/mui.picker.all.css" />
    <link rel="stylesheet" href="/static/job/styles/swiper.min.css">
    <link rel="stylesheet" href="/static/job/styles/main.css">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="/static/job/js/jquery.min.js"></script>
    <style>
        /* 添加在main.css中 */
        .upload-card {
            display: block;
            width: 120px;
            height: 160px;
            border: 2px dashed #ddd;
            background: #fff;
            position: relative;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .upload-card:hover {
            border-color: #007bff;
        }

        .upload-tip {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #333;
            pointer-events: none;
        }

        .main-tip {
            display: block;
            font-size: 16px;
            line-height: 1.4;
        }

        .sub-tip {
            display: block;
            font-size: 12px;
            color: #666;
        }

        .preview-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: none;
        }

        .upload-del {
            position: absolute;
            right: -10px;
            top: -10px;
            display: none;
        }
    </style>
</head>

<body>

    <header>
        <div class="header-box">
            <a href="{:U('job/index')}" class="btn-back"></a>
            <h3>个人基本信息</h3>
        </div>
    </header>
    <div class="g-header-space">
        <div class="header-space"></div>
    </div>
    <form method="post" enctype="multipart/form-data" >
    <section class="uc-wrap uc-base">

        <ul class="form">
            <!-- 新增图片上传项 -->
            <li style="padding-bottom: 15px">
                <div class="txt" style="float: left;">证件照</div>
                <div class="con" style="float: left; margin-left: 30px;">
                    <input type="file" name="photo_path" id="idPhotoUpload" accept="image/*" class="upload-input" hidden>
                    <label for="idPhotoUpload" class="upload-card">
                        <div class="upload-tip"  style="display: {:$userJobRow['photo_path'] ? 'none' : 'block'}">
                            <span class="main-tip">添加</span>
                            <span class="sub-tip">证件照</span>
                        </div>
                        <img src=" {:$userJobRow['photo_path'] ? : ''}" style="display: {:$userJobRow['photo_path'] ? 'block' : 'none'}" class="preview-img" alt="证件照预览">
                    </label>
                    <a href="javascript:void(0);" class="btn-delete upload-del" style="display: {:$userJobRow['photo_path'] ? 'block' : 'none'}">
                        <img src="/static/job/images/delete.png" class="uc-icon36" alt="删除"/>
                    </a>
                </div>
            </li>
            <li>
                <div class="txt" >姓名</div>
                <input type="text" class="uc-input"   name="name" placeholder="请输入" value="{:$userJobRow['name'] ?:''}" />
            </li>
            <li>
                <div class="txt">性别</div>
                <div class="con">
                    <input type="text" class="uc-input" name="gender" placeholder="请输入" value="{:$userJobRow['gender'] ?:''}" readonly id="useData001" />
                    <a href="javascript:void(0);" class="btn-delete"><img src="/static/job/images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
            </li>
            <li>
                <div class="txt">电话</div>
                <input type="text" class="uc-input" name="phone" placeholder="请输入" value="{:$userJobRow['phone'] ?:''}" />
            </li>
            <li>
                <div class="txt">出生年月</div>
                <div class="con">
                    <input type="text" class="uc-input" name="birthdate" placeholder="请选择" value="{:$userJobRow['birthdate'] ?:''}" readonly id="useDate001" />
                    <a href="javascript:void(0);" class="btn-delete"><img src="/static/job/images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
            </li>
            <li>
                <div class="txt">婚姻状况</div>
                <div class="con">
                    <input type="text" name="marital_status" class="uc-input" placeholder="请选择" value="{:$userJobRow['marital_status'] ?:''}" readonly id="useData003" />
                    <a href="javascript:void(0);" class="btn-delete"><img src="/static/job/images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
            </li>
            <li>
                <div class="txt">身份证</div>
                <input type="text" class="uc-input" name="id_number" placeholder="请输入" value="{:$userJobRow['id_number'] ?:''}" />
            </li>
            <li>
                <div class="txt">籍贯</div>
                <input type="text" class="uc-input" name="registered" placeholder="请输入" value="{:$userJobRow['registered'] ?:''}" />
            </li>
            <li>
                <div class="txt">民族</div>
                <input type="text" class="uc-input" name="nation" placeholder="请输入" value="{:$userJobRow['nation'] ?:''}" />
            </li>
            <li>
                <div class="txt">政治面貌</div>
                <input type="text" class="uc-input" name="political_status" placeholder="请输入" value="{:$userJobRow['political_status'] ?:''}" />
            </li>
            <li>
                <div class="txt">健康状况</div>
                <input type="text" class="uc-input" name="health_status" placeholder="请输入" value="{:$userJobRow['health_status'] ?:''}" />
            </li>
            <li>
                <div class="txt">身高（CM）</div>
                <input type="text" class="uc-input" name="height" placeholder="请输入" value="{:$userJobRow['height'] ?:''}" />
            </li>
            <li>
                <div class="txt">体重（KG）</div>
                <input type="text" class="uc-input"  name="weight" placeholder="请输入" value="{:$userJobRow['weight'] ?:''}" />
            </li>

            <li>
                <div class="txt">视力（有无色盲色弱，如无则填正常）</div>
                <input type="text" class="uc-input" name="vision" placeholder="请输入" value="{:$userJobRow['vision'] ?:''}" />
            </li>
            <li>
                <div class="txt">听力（是否正常）</div>
                <input type="text" class="uc-input" name="hearing" placeholder="请输入" value="{:$userJobRow['hearing'] ?:''}" />
            </li>
            <li>
                <div class="txt">是否恐高</div>
                <div class="con">
                    <input type="text" class="uc-input" name="is_afraid_heights" placeholder="请选择" value="{:$userJobRow['is_afraid_heights'] ? '恐高':'不恐高'}" readonly id="useData005" />
                    <a href="javascript:void(0);" class="btn-delete"><img src="/static/job/images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
            </li>
            <li>
                <div class="txt">求职意向</div>
                <input type="text" class="uc-input" name="applied_position" placeholder="请输入" value="{:$userJobRow['applied_position'] ?:''}" />
            </li>
            <li>
                <div class="txt">学历</div>
                <input type="text" class="uc-input" name="education_level" placeholder="请输入" value="{:$userJobRow['education_level'] ?:''}" />
            </li>

            <li>
                <div class="txt">专业技术职称</div>
                <input type="text" class="uc-input" name="professional" placeholder="请输入" value="{:$userJobRow['professional'] ?:''}" />
            </li>
            <li>
                <div class="txt">毕业院校</div>
                <input type="text" class="uc-input" name="graduate_school" placeholder="请输入" value="{:$userJobRow['graduate_school'] ?:''}" />
            </li>
            <li>
                <div class="txt">所学专业</div>
                <input type="text" class="uc-input" name="major" placeholder="请输入" value="{:$userJobRow['major'] ?:''}" />
            </li>
            <li>
                <div class="txt">毕业时间</div>
                <div class="con">
                    <input type="text" class="uc-input" name="graduation_time" placeholder="请选择" value="{:$userJobRow['graduation_time'] ?:''}" readonly id="useDate006" />
                    <a href="javascript:void(0);" class="btn-delete"><img src="/static/job/images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
 
            </li>
            <li>
                <div class="txt">参加工作时间</div>
                <div class="con">
                    <input type="text" class="uc-input" name="time_to_work" placeholder="请选择" value="{:$userJobRow['time_to_work'] ?:''}" readonly id="useDate007" />
                    <a href="javascript:void(0);" class="btn-delete"><img src="/static/job/images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
            </li>
        </ul>
        <div class="g-fixedOperate">
            <button type="submit" class="uc-btn blue">保存信息</button>
            <!--a href="" class="uc-btn gray">删除</a-->
        </div>

    </section>
    </form>



    <script src="/static/job/js/swiper.min.js"></script>
    <script src="/static/job/js/mui.min.js"></script>
    <script src="/static/job/js/mui.picker.min.js"></script>
    <script src="/static/job/js/main.js"></script>
    <script>
        $(function() {})



        var userPicker001 = new mui.PopPicker();
        userPicker001.setData([{
                value: '1',
                text: '男'
            },
            {
                value: '2',
                text: '女'
            },
        ]);

        $("body").on("click", "#useData001", function() {
            setTimeout(function() {
                userPicker001.show(function(items) {
                    console.log(items[0]);
                    $("#useData001").val(items[0].text);
                });
            }, 200);
        });




        var start_time_picker = new mui.DtPicker({
            "type": "month",
            "beginYear": 1970,
            "endYear": 2020
        });
        $("body").on("click", "#useDate001", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDate001").val(items.text);
                });
            }, 200);
        });


        var start_time_picker2 = new mui.DtPicker({
            "type": "month",
            "beginYear": 2000,
            "endYear": new Date().getFullYear() + 1
        });
        $("body").on("click", "#useDate006", function() {
            setTimeout(function() {
                start_time_picker2.show(function(items) {
                    $("#useDate006").val(items.text);
                });
            }, 200);
        });


        var start_time_picker3 = new mui.DtPicker({
            "type": "month",
            "beginYear": 2010,
            "endYear": new Date().getFullYear()
        });
        $("body").on("click", "#useDate007", function() {
            setTimeout(function() {
                start_time_picker3.show(function(items) {
                    $("#useDate007").val(items.text);
                });
            }, 200);
        });



        var userPicker003 = new mui.PopPicker();
        userPicker003.setData([{
                value: 'ywj',
                text: '未婚'
            },
            {
                value: 'aaa',
                text: '已婚'
            },
            {
                value: 'ljb',
                text: '保密'
            },
        ]);
        $("body").on("click", "#useData003", function() {
            setTimeout(function() {
                userPicker003.show(function(items) {
                    console.log(items[0]);
                    $("#useData003").val(items[0].text);
                });
            }, 200);
        });

        var userPicker005 = new mui.PopPicker();
        userPicker005.setData([{
            value: '1',
            text: '恐高'
         },
            {
                value: 'aaa',
                text: '不恐高'
            }
        ]);
        $("body").on("click", "#useData005", function() {
            setTimeout(function() {
                userPicker005.show(function(items) {
                    console.log(items[0]);
                    $("#useData005").val(items[0].text);
                });
            }, 200);
        });


        var userPicker004 = new mui.PopPicker();
        userPicker004.setData([{
                value: 'ywj',
                text: '政治面貌1'
            },
            {
                value: 'aaa',
                text: '政治面貌2'
            },
            {
                value: 'ljb',
                text: '政治面貌3'
            },
            {
                value: 'ymt',
                text: '政治面貌4'
            },
            {
                value: 'shq',
                text: '政治面貌5'
            },
            {
                value: 'zbh',
                text: '政治面貌6'
            },
            {
                value: 'zhy',
                text: '政治面貌7'
            }
        ]);
        $("body").on("click", "#useData004", function() {
            setTimeout(function() {
                userPicker004.show(function(items) {
                    console.log(items[0]);
                    $("#useData004").val(items[0].text);
                });
            }, 200);
        });

        $("body").on('click', '.btn-delete', function(event) {
            event.preventDefault();
            $(this).siblings('.uc-input').val('')
        });


        // 添加在现有JS中
        $('#idPhotoUpload').change(function(e) {
            const file = e.target.files[0];
            if (!file) return;

            const $card = $(this).siblings('.upload-card');
            const $img = $card.find('.preview-img');
            const reader = new FileReader();

            reader.onload = function(e) {
                $img.attr('src', e.target.result)
                    .show().siblings('.upload-tip').hide();
                $card.siblings('.upload-del').fadeIn();
            }
            reader.readAsDataURL(file);
        });

        $('.upload-del').click(function() {
            const $card = $(this).siblings('.upload-card');
            $card.find('.preview-img').attr('src', '#').hide()
                .siblings('.upload-tip').show();
            $('#idPhotoUpload').val('');
            $(this).fadeOut();
        });
    </script>
</body>

</html>