<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="/static/job/styles/mui.picker.all.css" />
    <link rel="stylesheet" href="/static/job/styles/swiper.min.css">
    <link rel="stylesheet" href="/static/job/styles/main.css">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="/static/job/js/jquery.min.js"></script>
</head>

<body>

    <header>
        <div class="header-box">
            <a href="{:U('/job/index')}" class="btn-back"></a>
            <h3>{:$typeList[$type]}</h3>
        </div>
    </header>
    <div class="g-header-space">
        <div class="header-space"></div>
    </div>

    <section class="uc-wrap uc-otherForm">

        <ul class="form">
            <li>
                <div class="txt">{:$typeList[$type]}</div><input type="text" class="uc-input" placeholder="请输入（选填）" value=""  id="useDataA001" />
            </li>
        </ul>
        <div class="g-fixedOperate">
            <a href="" class="uc-btn blue">保存信息</a>
            <a href="" class="uc-btn gray">删除</a>
        </div>
    </section>



    <script src="/static/job/js/swiper.min.js"></script>
    <script src="/static/job/js/mui.min.js"></script>
    <script src="/static/job/js/mui.picker.min.js"></script>
    <script src="/static/job/js/main.js"></script>
    <script>
        $(function() {})
    </script>
</body>

</html>