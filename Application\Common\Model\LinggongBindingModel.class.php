<?php
namespace Common\Model;
use Think\Model;

/**
 * 灵工绑定模型
 */
class LinggongBindingModel extends Model {
    protected $tableName = 'linggong_binding';
    
    // 状态定义
    public $status = [
        0 => '待审核',
        1 => '已通过',
        2 => '已拒绝'
    ];
    
    // 平台类型定义
    public $platformTypes = [
        1 => '京东京灵',
        2 => '云账户'
    ];
    
    /**
     * 检查用户是否已绑定灵工
     * @param int $userId 用户ID
     * @param int $platformType 平台类型，默认全部
     * @return array|bool 绑定记录或false
     */
    public function checkUserBinding($userId, $platformType = null) {
        if (empty($userId)) {
            return false;
        }
        
        $where = ['user_id' => $userId];
        if (!is_null($platformType)) {
            $where['platform_type'] = $platformType;
        }
        
        $binding = $this->where($where)->find();
        return $binding ? $binding : false;
    }
    
    /**
     * 检查服务站是否已绑定灵工
     * @param int $stationId 服务站ID
     * @param int $platformType 平台类型，默认全部
     * @return array|bool 绑定记录或false
     */
    public function checkStationBinding($stationId, $platformType = null) {
        if (empty($stationId)) {
            return false;
        }
        
        $where = ['service_station_id' => $stationId];
        if (!is_null($platformType)) {
            $where['platform_type'] = $platformType;
        }
        
        $binding = $this->where($where)->find();
        return $binding ? $binding : false;
    }
    
    /**
     * 添加绑定信息
     * @param array $data 绑定数据
     * @return int|bool 新增ID或false
     */
    public function addBinding($data) {
        if (empty($data) || !is_array($data)) {
            return false;
        }
        
        // 设置默认状态和时间
        $data['status'] = isset($data['status']) ? $data['status'] : 0;
        $data['platform_type'] = isset($data['platform_type']) ? $data['platform_type'] : 1; // 默认京东京灵
        $data['create_time'] = time();
        $data['update_time'] = time();
        
        return $this->add($data);
    }
    
    /**
     * 更新绑定信息
     * @param int $id 绑定记录ID
     * @param array $data 更新数据
     * @return bool 是否成功
     */
    public function updateBinding($id, $data) {
        if (empty($id) || empty($data) || !is_array($data)) {
            return false;
        }
        
        // 设置更新时间
        $data['update_time'] = time();
        
        return $this->where(['id' => $id])->save($data) !== false;
    }
    
    /**
     * 审核绑定信息
     * @param int $id 绑定记录ID
     * @param int $status 审核状态：1=通过，2=拒绝
     * @param string $remark 备注
     * @return bool 是否成功
     */
    public function reviewBinding($id, $status, $remark = '') {
        if (empty($id) || !in_array($status, [1, 2])) {
            return false;
        }
        
        $data = [
            'status' => $status,
            'update_time' => time()
        ];
        
        if (!empty($remark)) {
            $data['remark'] = $remark;
        }
        
        return $this->where(['id' => $id])->save($data) !== false;
    }
    
    /**
     * 生成绑定二维码
     * @param int $userId 用户ID
     * @param int $platformType 平台类型
     * @return string 二维码URL
     */
    public function generateQrCode($userId, $platformType = 1) {
        // 返回固定的二维码图片路径
        return '/static/stations/images/jdjlQrcode.png';
    }
} 