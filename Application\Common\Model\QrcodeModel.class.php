<?php
/**
 *
 */
namespace Common\Model;

use Think\Model;
use \LaneWeChat\Core as WE;

class QrcodeModel extends CommonModel
{

    // 未使用的二维码可以分配给任何用途，停用的不使用功能暂时也不分配
    public $use_type = [
        '1' => ['text' => '服务站', 'style' => 'info'],
    ];

    // 查找二维码
    public function get($service_id, $scene_id)
    {
        return M('Qrcode')->where([
            'service_id' => $service_id,
            'scene_id' => $scene_id,
        ])->find();
    }


    // 生成永久二维码
    public function createNew($service_id, $useType, $relationId)
    {
        vendor('LaneWeChat.lanewechat');
        M()->startTrans();
        $max_scene_id = $this->where(['service_id' => $service_id])->max('scene_id');
        if ($qrid = $this->add([
            'service_id' => $service_id,
            'service_station_id' => $relationId,
            'scene_id' => $max_scene_id + 1,
            'created' => time(),
        ])) {
            $service = D('Service')->find($service_id);
            WE\Base::init([
                'WX_TOKEN' => $service['wx_token'],
                'WX_APPID' => $service['appid'],
                'WX_APPSECRET' => $service['secret'],
            ]);
            $resTicket = WE\Popularize::createTicket(2, 30, $max_scene_id + 1);
            if (isset($resTicket['ticket'])) {
                $this->save([
                    'id' => $qrid,
                    'ticket' => $resTicket['ticket'],
                    'url' => $resTicket['url'],
                    'use_type' => $useType,
                    'relation_id' => $relationId,
                ]);
                M()->commit();
                return $this->find($qrid);
            }
        }
        M()->rollback();
        return false;
    }

    // 生成临时二维码
    public function createTemp($service_id, $scene_id)
    {
        vendor('LaneWeChat.lanewechat');
        $service = D('Service')->find($service_id);
        WE\Base::init([
            'WX_TOKEN' => $service['wx_token'],
            'WX_APPID' => $service['appid'],
            'WX_APPSECRET' => $service['secret'],
        ]);
        return WE\Popularize::createTicket(1, 2592000, $scene_id);
    }

    /**
     * @param $service_id
     * @param $use_type
     * @param $project_id
     * @param int $agent_id
     * @return array|false|mixed|string|null
     */
    // 分配使用
    public function apply($service_id, $use_type, $project_id, $agent_id = 0)
    {
        // 找到一个未被使用的二维码
        $code = $this->where([
            'service_id' => $service_id,
            'use_type' => 1,
        ])->order('id asc')->find();


        // 没有的话创建一个，最多尝试 3 次
        $try = 0;
        while (!$code and $try < 3) {
            $code = $this->createNew($service_id);
            $try ++;
            usleep(2000);
        }
        // 更新二维码已使用
        $code and $this->save([
            'id' => $code['id'],
            'project_id' => $project_id,
            'agent_id' => $agent_id,
            'use_type' => $use_type,
            'use_time' => time(),
            'scan_time' => time(),
        ]);
        return $code;
    }

    // 停用一个二维码
    public function stop($id)
    {
        return $this->save(['id' => $id, 'use_type' => 0]);
    }


    /*
   **
   * 合成图片
   */
    function mergePoster($bg_img, $qr_url, $size, $x, $y, $toPath = null, $types, $content = '', $sizeData = [], $img = '', $sizeData1 = [])
    {
        vendor('phpqrcode.phpqrcode');
        ob_start();
        \QRcode::png($qr_url, false, QR_ECLEVEL_L, 22.3, 1);
        $qr_img = ob_get_contents();
        ob_end_clean();
        header("Content-type:text/html;charset=utf-8");

        $bkim_size = getimagesize($bg_img);
        $type = $bkim_size['mime'];
        $f_map = ['image/png' => ['from' => 'imagecreatefrompng', 'img' => 'imagepng'], 'image/jpeg' => ['from' => 'imagecreatefromjpeg', 'img' => 'imagejpeg']];
        $bkim = $f_map[$type]['from']($bg_img);

        $avtim = imagecreatefromstring($qr_img);
        //$avtimx = imagesx($avtim);
        //$avtimy = imagesy($avtim);
        $avtimx = imagesx($avtim);
        $avtimy = imagesy($avtim);

        //文字位置处理
        if ($types == 4) {
            if (mb_strlen($content) == 2) {
                $color = imagecolorallocate($bkim, 124, 124, 124);
                imagettftext($bkim, 12, 0, $sizeData ? $sizeData['x'] : 219, $sizeData ? $sizeData['y'] : 933, $color, STATIC_PATH . "font/huawenheiti.ttf", $content);
            } else {
                $color = imagecolorallocate($bkim, 124, 124, 124);
                imagettftext($bkim, 12, 0, $sizeData ? $sizeData['x'] : 213, $sizeData ? $sizeData['y'] : 933, $color, STATIC_PATH . "font/huawenheiti.ttf", $content);
            }
        }

        if ($types == 1) {
            $color = imagecolorallocate($bkim, 124, 124, 124);
            imagettftext($bkim, 12, 0, $sizeData ? $sizeData['x'] : 192,  $sizeData ? $sizeData['y'] : 933, $color, STATIC_PATH . "font/huawenheiti.ttf",  $content);
        }

        if ($types == 3) {
            $color = imagecolorallocate($bkim, 124, 124, 124);
            imagettftext($bkim, 12, 0, $sizeData ? $sizeData['x'] : 223, $sizeData ? $sizeData['y'] :  933, $color, STATIC_PATH . "font/huawenheiti.ttf",  $content);
        }

        if ($types == 2) {
            $color = imagecolorallocate($bkim, 124, 124, 124);
            imagettftext($bkim, 12, 0, $sizeData ? $sizeData['x'] : 193, $sizeData ? $sizeData['y'] : 933, $color, STATIC_PATH . "font/huawenheiti.ttf", $content);
        }


        if ($sizeData1) {
            $color = imagecolorallocate($bkim, 124, 124, 124);
            imagettftext($bkim, 12, 0, $sizeData1 ? $sizeData1['x'] : 193, $sizeData1 ? $sizeData1['y'] : 933, $color, STATIC_PATH . "font/huawenheiti.ttf", $sizeData1['content']);
        }



        import('Util.easyphpthumbnail');
        $obj = new \easyphpthumbnail();

        // $obj->Backgroundcolor = '#0000FF';
        // $obj->Clipcorner = array(1,1,0,1,1,1,1);
        // $obj->Maketransparent = array(1,1,'#0000FF',1);
        $obj->im = $avtim;
        $obj->size = [0 => $avtimx, 1 => $avtimy];
        $obj->Thumbsize = $size;
        $obj->thumbmaker();
        $avtim = $obj->thumb;

        imagecopymerge($bkim, $avtim, $x, $y, 0, 0, $size, $size, 100);


        if ($img) {
            $curl_obj = curl_init();
            curl_setopt($curl_obj, CURLOPT_URL, $img);
            curl_setopt($curl_obj, CURLOPT_HEADER, 0);
            curl_setopt($curl_obj, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl_obj, CURLOPT_TIMEOUT, 15);
            $result = curl_exec($curl_obj);
            curl_close($curl_obj);

            $avtims = imagecreatefromstring($result);
            $avtimsx = imagesx($avtims);
            $avtimsy = imagesy($avtims);


            $obj->Backgroundcolor = '#0000FF';
            $obj->Clipcorner = array(2,48,0,1,1,1,1);
            $obj->Maketransparent = array(1,1,'#0000FF',1);
            $obj->im = $avtims;
            $obj->size = [0 => $avtimsx, 1 => $avtimsy];
            $obj->thumbmaker();
            $avtims = $obj->thumb;
            imagecopymerge($bkim, $avtims, 35, 40, 0, 0, $avtimsx, $avtimsy,100);
        }

        if ($toPath) {
            $toPath = SITE_PATH . $toPath;
            $f_map[$type]['img']($bkim, $toPath);
            return $toPath;
        } else {
            $f_map[$type]['img']($bkim);
        }
    }

    /**
     * 生成海报图
     * type 1：海报图1 2：海报图2
     *
     */
    public function createQrcodePoster($type, $createQrcodeId) {
        $qrcodeRow = D("Qrcode")->where(['use_type' => 1, 'relation_id' => $createQrcodeId])->find();
        if (!$qrcodeRow) { //有带参数
            $qrcodeRow = D("Qrcode")->createNew(1, 1, $createQrcodeId);
        }

        $agent  = D("Agent")->where(['id' => $createQrcodeId])->find();
        $mobiles = substr_replace($agent['mobile'], '****', 3, 4);
        $content = '推荐人：'.$mobiles;
        if ($type == 1) {
            //生成图片1
            $qrcodePath = "/data/up/agent/" . $createQrcodeId."_1" . '.jpg';
            if (file_exists(SITE_PATH.$qrcodePath)) {
                $qrcode = __HOST__.$qrcodePath;
            } else {
                $img = D("Conf")->C("SYS_OUT_QRCODE_BG_TWO");
                $img = qnimg($img);
                $sizeData = [
                    'x' => 30,
                    'y' => 884,
                ];
                // $qrurl = 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=' . urlencode($qrcode['ticket']);
                $qrurl = $qrcodeRow['url'];
                if (strpos($agent['headimg'], '/132') !== false) {
                    $headimg = $agent['headimg'];
                } else {
                    $headimg = $agent['headimg'] . '2';
                }
                if (strpos($agent['headimg'], '/132') !== false) {
                    $headimg = str_replace('/132', '/96', $headimg);
                }
                $sizeData1 = [
                    'content' => '有效期：永久有效',
                    'x' => 380,
                    'y' => 884,
                ];

                $s = D("Qrcode")->mergePoster($img, $qrurl, 245, 149, 520, $qrcodePath, 2, $content, $sizeData, $headimg, $sizeData1);
                $qrcode = __HOST__.$qrcodePath;
            }
        } else {
            //生成图片2
            $qrcodePath = "/data/up/agent/" . $createQrcodeId . '.jpg';
            if (file_exists(SITE_PATH.$qrcodePath)) {
                $qrcode = __HOST__.$qrcodePath;
            } else {
                $img = D("Conf")->C("SYS_OUT_QRCODE_BG");
                $img = qnimg($img);

                // $qrurl = 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=' . urlencode($qrcode['ticket']);
                $qrurl = $qrcodeRow['url'];
                $s = D("Qrcode")->mergePoster($img, $qrurl, 245, 149, 520, $qrcodePath, 1, $content);
                $qrcode = __HOST__.$qrcodePath;
            }
        }
    }

    /**
     * 生成临时二维码并返回图片
     * @param $type
     * @param $relationId
     * @return string
     */
    public function createTempToImg($type, $relationId) {
        $img = '';
        if ($type == 1) { //推荐
            $wx_service_scenes_id = 1;
        } elseif ($type == 2) { //逛街
            $wx_service_scenes_id = 7;
        } else { //详情
            $wx_service_scenes_id = 8;
        }
        $serviceId = D("WxServiceScenesJoinService")->where([
            'wx_service_scenes_id' => $wx_service_scenes_id,
            'status' => 1,
        ])->order('rand()')->getField('service_id');
        if (!$serviceId) return $img;
        $qrcodeRow = $this->createTemp($serviceId, $relationId);
        if ($qrcodeRow && $qrcodeRow['ticket']) {
            D("WxServiceTempSceneQrcode")->save([
                'id' => $relationId,
                'service_id' => $serviceId,
                'ticket' => $qrcodeRow['ticket'],
                'url' => $qrcodeRow['url'],
                'expired_time' => time() + $qrcodeRow['expire_seconds'] - 86400*3,
            ]);
            $img = $qrcodeRow['ticket'];
        }
        return $img;
    }

    public function saveQrcodeBase64($qrcodeRow, $agentId) {
        $ticket = $qrcodeRow['ticket'];
        $link = 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=' . $ticket;
        $link = 'data:image/png;base64,' . chunk_split(base64_encode(file_get_contents($link)));
        $redis = redis();
        //公众号永久带参二维码
        $keys = 'agent_service_qrcode_'.$agentId;
        $redis->set($keys, $link);
        D("Qrcode")->save(['id' => $qrcodeRow['id'], 'qrcode_base64' => $link]);
        return "scucess";
    }

    public function getQrcodeBase64($agentId) {
        $redis = redis();
        $keys = 'agent_service_qrcode_'.$agentId;
        $qrcodeBase64 = $redis->get($keys);
        return $qrcodeBase64;
    }

}