﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提现进度</title>
    <style>
        :root {
            --primary-color: #00bf80;
            --success-color: #00bf80;
            --processing-color: #ff9900;
            --failed-color: #ff4d4f;
            --secondary-color: #666;
            --card-bg: #fff;
            --border-color: #eee;
        }

        body {
            background: #f5f5f5;
            margin: 0;
            font-family: -apple-system, system-ui, sans-serif;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .header {
            margin-bottom: 24px;
        }
        .title {
            font-size: 24px;
            color: #000;
            margin-bottom: 8px;
        }
        .subtitle {
            color: var(--secondary-color);
            font-size: 16px;
        }

        /* 筛选控件 */
        .filter-group {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .filter-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid #ddd;
            background: #fff;
            cursor: pointer;
            transition: all 0.2s;
        }
        .filter-btn.active {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        /* 记录卡片 */
        .record-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border-left: 4px solid;
        }
        .record-card.success { border-color: var(--success-color); }
        .record-card.processing { border-color: var(--processing-color); }
        .record-card.failed { border-color: var(--failed-color); }

        .bank-info {
            font-size: 16px;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .time-status {
            color: var(--secondary-color);
            font-size: 14px;
        }
        .status-label { font-weight: 500; }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: flex-end;
            align-items: flex-end;
        }
        .modal-content {
            background: #fff;
            width: 100%;
            max-height: 70vh;
            padding: 20px;
            border-radius: 16px 16px 0 0;
            transform: translateY(100%);
            transition: transform 0.3s;
        }
        .modal-show .modal-content {
            transform: translateY(0);
        }

        /* 分页控件 */
        .pagination {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 20px;
        }
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .container { padding: 16px; }
            .bank-info { flex-direction: column; align-items: flex-start; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">余额提现记录</h1>
            <div class="subtitle">提现流程：
                提现申请----财务审核（处理中）----开具发票（待开票）----财务付款（成功/失败）</div>
        </div>

        <div class="filter-group" id="filterGroup">
            <button class="filter-btn active" data-status="all">全部</button>
            <button class="filter-btn" data-status="processing">处理中</button>
            <button class="filter-btn" data-status="invoice">待开票</button>
            <button class="filter-btn" data-status="success">成功</button>
            <button class="filter-btn" data-status="failed">失败</button>
        </div>

        <div id="recordList"></div>
        <div class="pagination" id="pagination"></div>

        <!-- 弹窗 -->
        <div class="modal-overlay" id="modal">
            <div class="modal-content">
                <h3>到账说明</h3>
                <p>1. 提现申请提交后，通常需要1-3个工作日到账</p>
                <p>2. 如遇节假日或银行系统升级，到账时间可能顺延</p>
                <button onclick="closeModal()">关闭</button>
            </div>
        </div>
    </div>

<script>
// 模拟数据
const records = Array.from({length: 150}, (_, i) => ({
    id: i + 1,
    bank: '长沙银行股份有限公司天心支行（0001）',
    time: `2025-${String(Math.floor(Math.random()*12 + 1)).padStart(2,'0')}-${String(Math.floor(Math.random()*28 + 1)).padStart(2,'0')} ${Math.floor(Math.random()*24)}:${Math.floor(Math.random()*60)}:${Math.floor(Math.random()*60)}`,
    status: [ 'processing', 'invoice', 'success','failed'][Math.floor(Math.random()*4)],
    amount: (Math.random()*10000).toFixed(2)
}))

let currentPage = 1
const perPage = 100
let currentStatus = 'all'

// 初始化
renderRecords()
setupPagination()

function renderRecords() {
    const filtered = records.filter(r => currentStatus === 'all' || r.status === currentStatus)
    const sliced = filtered.slice((currentPage - 1)*perPage, currentPage*perPage)
    
    document.getElementById('recordList').innerHTML = sliced.map(record => `
        <div class="record-card ${record.status}">
            <div class="bank-info">
                <span>提现金${record.bank}</span>
                <div class="time-status">
                    <div class="time-stamp">${record.time}</div>
                    <div class="status-label">
                        ${record.status === 'success' ? '转账成功' : 
                        record.status === 'processing' ? `预计剩余时间：<span class="countdown" data-time="${record.time}"></span>` : 
                        record.status === 'invoice' ? '待开票'  : 
                         '提现失败'}
                    </div>
                </div>
            </div>
        </div>
    `).join('')

    updateCountdowns()
}

// 倒计时更新
function updateCountdowns() {
    document.querySelectorAll('.countdown').forEach(el => {
        const targetTime = new Date(el.dataset.time).getTime()
        const now = new Date().getTime()
        const diff = targetTime - now
        
        if(diff > 0) {
            const days = Math.floor(diff / (1000 * 60 * 60 * 24))
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
            el.innerHTML = `${days}天${hours}小时`
        } else {
            el.parentElement.innerHTML = '已超时'
        }
    })
}

// 分页控制
function setupPagination() {
    const total = records.length
    const pages = Math.ceil(total / perPage)
    const html = Array.from({length: pages}, (_, i) => 
        `<button class="page-btn ${i+1 === currentPage ? 'active' : ''}" 
                onclick="changePage(${i+1})">${i+1}</button>`
    ).join('')
    document.getElementById('pagination').innerHTML = html
}

// 事件监听
document.getElementById('filterGroup').addEventListener('click', e => {
    if(e.target.classList.contains('filter-btn')) {
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'))
        e.target.classList.add('active')
        currentStatus = e.target.dataset.status
        currentPage = 1
        renderRecords()
        setupPagination()
    }
})

// 弹窗控制
document.querySelector('.subtitle').addEventListener('click', () => {
    document.getElementById('modal').style.display = 'flex'
    setTimeout(() => document.getElementById('modal').classList.add('modal-show'), 10)
})

function closeModal() {
    document.getElementById('modal').classList.remove('modal-show')
    setTimeout(() => document.getElementById('modal').style.display = 'none', 300)
}

// 分页切换
function changePage(page) {
    currentPage = page
    renderRecords()
    window.scrollTo(0, 0)
}

// 每10秒更新倒计时
setInterval(updateCountdowns, 10000)
</script>
</body>
</html>