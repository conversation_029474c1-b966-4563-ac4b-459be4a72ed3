<?php

namespace Common\Controller;

use Think\Controller;

/**
 * 管理系统公用控制器
 */
class PrimeController extends BaseController
{

    public function _initialize()
    {
        parent::_initialize();
    	if($this->loggedIn()) {
    		$id = session('admin_id');
    		if(!$this->check_access($id)) {
    			$this->error("您没有访问权限！");
    			exit();
            }
    		$user = M("Users")->where("id=$id")->find();

            $cur_menu = D("Menu")->curMenu(1);
            if (session('main_menus')) {
                $main_menus = session('main_menus');
            } else {
                $main_menus = D("Menu")->menu_json();
                session('main_menus', $main_menus);
            }
            if(!$cur_menu) {
                $cur_menu['boot_id'] = key($main_menus);
            }

    		$this->assign("admin", $user);
    		$this->assign("main_menus", $main_menus);
    		$this->assign("cur_menu", $cur_menu);
    	} else {
    		if(IS_AJAX) {
    			$this->error("您还没有登录！", U("prime/public/login"));
    		} else {
    			header("Location:".U("prime/public/login"));
    			exit;
    		}
    	}
    }

    private function check_access($uid)
    {
    	//如果用户角色是1，则无需判断
    	if ($uid == 1) return true;
    	$rule = MODULE_NAME.CONTROLLER_NAME.ACTION_NAME;
    	$no_need_check_rules = ["PrimeIndexindex", "PrimeMainindex", 'PrimeSyschpwd', 'PrimeSysrefresh'];
    	if (!in_array($rule, $no_need_check_rules)) {
    		return authCheck($uid);
    	} else {
    		return true;
    	}
    }

    public function loggedIn()
    {
        return session('admin_id') ? true : false;
    }

    /**
     * 初始化后台菜单
     */
    public function initMenu()
    {
        $Menu = F("Menu");
        if (!$Menu) {
            $Menu = D("Menu")->menu_cache();
        }
        return $Menu;
    }

    /**
     *  排序 排序字段为sort数组 POST 排序字段为:sort
     */
    protected function _sort($model)
    {
        if (!is_object($model)) {
            return false;
        }
        $pk = $model->getPk();
        $ids = $_POST['sort'];
        foreach ($ids as $key => $r) {
            $data['sort'] = $r;
            $model->where([$pk => $key])->save($data);
        }
        return true;
    }

}
