<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化消息管理页面样式 */
                .message-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .message-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .message-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .message-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .message-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .message-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .message-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .message-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .message-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .message-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .message-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .message-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                    text-decoration: none;
                }

                .message-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .message-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .message-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .message-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .message-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .message-index-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .message-index-nav-link.active {
                    color: #667eea !important;
                    background: white !important;
                    border-bottom-color: #667eea !important;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1) !important;
                    font-weight: 700 !important;
                }

                .message-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .message-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 搜索面板 - 默认隐藏 */
                .message-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    display: none;
                    animation: slideDown 0.3s ease-out;
                }

                .message-index-search-panel.show {
                    display: block;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .message-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .message-index-search-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .message-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .message-index-search-close {
                    background: none;
                    border: none;
                    color: #718096;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                }

                .message-index-search-close:hover {
                    background: #f1f5f9;
                    color: #374151;
                }

                .message-index-search-body {
                    padding: 2rem;
                }

                /* 现代化消息卡片 */
                .message-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .message-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .message-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                }

                .message-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .message-header h4 {
                    margin: 0;
                    font-size: 1.75rem;
                    font-weight: 600;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .urgent-indicator {
                    background: rgba(239, 68, 68, 0.9);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 500;
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    animation: pulse 2s infinite;
                }

                @keyframes pulse {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.8; }
                }

                .job-id {
                    font-size: 1.25rem;
                    color: rgba(255, 255, 255, 0.9);
                    margin-top: 0.5rem;
                    font-weight: 400;
                }

                .message-body {
                    padding: 2rem;
                }

                .resume-info {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    margin-bottom: 2rem;
                    border: 1px solid #e2e8f0;
                }

                .resume-info h5 {
                    color: #374151;
                    font-size: 1.5rem;
                    font-weight: 600;
                    margin: 0 0 1rem 0;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .job-demand {
                    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                    border: 1px solid #f59e0b;
                    border-radius: 0.5rem;
                    padding: 1rem;
                    margin-bottom: 1rem;
                    color: #92400e;
                    font-size: 1.5rem;
                }

                .info-row {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                }

                .info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                }

                .info-label {
                    color: #6b7280;
                    font-weight: 500;
                    min-width: 80px;
                }

                .info-value {
                    color: #374151;
                    font-weight: 600;
                }

                /* 沟通记录区域 */
                .communication-section {
                    background: white;
                    border-radius: 0.75rem;
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }

                .communication-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1rem 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .communication-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .message-count {
                    background: #667eea;
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 500;
                }

                .communication-body {
                    padding: 1.5rem;
                    max-height: 300px;
                    overflow-y: auto;
                }

                .message-item {
                    display: flex;
                    padding: 10px;
                    gap: 0.75rem;
                    margin-bottom: 1rem;
                    animation: messageSlideIn 0.3s ease-out;
                }

                @keyframes messageSlideIn {
                    from {
                        opacity: 0;
                        transform: translateY(10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .message-avatar {
                    width: 2.5rem;
                    height: 2.5rem;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1rem;
                    font-weight: 600;
                    color: white;
                    flex-shrink: 0;
                }

                .avatar-platform {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .avatar-station {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                }

                .avatar-zjb {
                    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
                }

                .avatar-unknown {
                    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
                }

                .message-content {
                    flex: 1;
                    min-width: 0;
                }

                .message-meta {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 0.25rem;
                }

                .sender-name {
                    font-size: 1.25rem;
                    font-weight: 600;
                    color: #374151;
                }

                .message-time {
                    font-size: 1rem;
                    color: #9ca3af;
                }

                .message-text {
                    font-size: 1.5rem;
                    color: #6b7280;
                    line-height: 1.5;
                    word-wrap: break-word;
                }

                /* 快速回复区域 */
                .quick-reply-section {
                    background: #f8fafc;
                    border-top: 1px solid #e2e8f0;
                    padding: 1.5rem;
                }

                .quick-reply-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    background: #667eea;
                    color: white;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    text-decoration: none;
                }

                .quick-reply-toggle:hover {
                    background: #5a67d8;
                    color: white;
                    text-decoration: none;
                }

                .quick-reply-form {
                    margin-top: 1rem;
                    display: none;
                    animation: slideDown 0.3s ease-out;
                }

                .quick-reply-form.show {
                    display: block;
                }

                .quick-reply-textarea {
                    width: 100%;
                    min-height: 80px;
                    padding: 0.75rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    line-height: 1.5;
                    resize: vertical;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                }

                .quick-reply-textarea:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .quick-reply-actions {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 1rem;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                .quick-reply-options {
                    display: flex;
                    align-items: center;
                    gap: 1.5rem;
                    flex-wrap: wrap;
                }

                .quick-reply-checkbox-container {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    color: #374151;
                }

                .quick-reply-checkbox {
                    width: 1.25rem;
                    height: 1.25rem;
                    accent-color: #667eea;
                }

                .quick-reply-submit {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .quick-reply-submit:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                }

                .quick-reply-submit:disabled {
                    background: #9ca3af;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: none;
                }

                /* 操作按钮区域 */
                .action-buttons {
                    display: flex;
                    gap: 1rem;
                    margin-top: 2rem;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                    flex-wrap: wrap;
                }

                .action-buttons .btn {
                    font-size: 1.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    border: none;
                    text-decoration: none;
                }

                .action-buttons .btn:hover {
                    transform: translateY(-1px);
                    text-decoration: none;
                }

                .btn-success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
                }

                .btn-success:hover {
                    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
                    color: white;
                }

                .btn-info {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
                }

                .btn-info:hover {
                    box-shadow: 0 8px 15px -3px rgba(59, 130, 246, 0.4);
                    color: white;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .message-index-container {
                        padding: 1.5rem;
                    }

                    .message-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .info-row {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .message-index-container {
                        padding: 1rem;
                    }

                    .message-index-nav-tabs {
                        flex-direction: column;
                    }

                    .message-index-nav-item {
                        flex: none;
                    }

                    .message-index-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .message-index-nav-link.active {
                        border-bottom: none !important;
                        border-right-color: #667eea !important;
                    }

                    .message-index-title-main {
                        font-size: 1.75rem;
                    }

                    .message-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .action-buttons {
                        flex-direction: column;
                    }

                    .quick-reply-actions {
                        flex-direction: column;
                        align-items: stretch;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .message-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .message-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .message-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .message-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }

                .message-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    position: relative;
                }
                .message-header h4 {
                    margin: 0;
                    font-size: 18px;
                    font-weight: 600;
                }
                .message-header .job-id {
                    font-size: 14px;
                    opacity: 0.9;
                    margin-top: 5px;
                }

                .urgent-indicator {
                    position: absolute;
                    top: 15px;
                    right: 20px;
                    background: #ff4757;
                    color: white;
                    padding: 5px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    font-weight: bold;
                    animation: pulse 2s infinite;
                }

                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }

                .message-body {
                    padding: 25px;
                }

                .resume-info {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 20px;
                    margin-bottom: 20px;
                }
                .resume-info h5 {
                    color: #2c3e50;
                    margin-bottom: 15px;
                    font-weight: 600;
                }
                .info-row {
                    display: flex;
                    flex-wrap: wrap;
                    margin: -5px;
                }
                .info-item {
                    flex: 1;
                    min-width: 200px;
                    padding: 5px;
                    margin-bottom: 8px;
                }
                .info-label {
                    font-weight: 500;
                    color: #555;
                    display: inline-block;
                    min-width: 80px;
                }
                .info-value {
                    color: #333;
                }

                .job-demand {
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 6px;
                    padding: 12px;
                    margin-bottom: 15px;
                    color: #856404;
                }
                .job-demand strong {
                    color: #d63031;
                }

                .communication-section {
                    border-top: 1px solid #eee;
                    padding-top: 20px;
                }
                .communication-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;
                }
                .communication-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #2c3e50;
                }
                .message-count {
                    background: #3498db;
                    color: white;
                    padding: 3px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                }

                .message-item {
                    display: flex;
                    margin-bottom: 15px;
                    align-items: flex-start;
                    position: relative;
                }

                /* 消息删除按钮 */
                .message-delete-btn {
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    width: 20px;
                    height: 20px;
                    background: #ef4444;
                    color: white;
                    border: none;
                    border-radius: 50%;
                    font-size: 12px;
                    cursor: pointer;
                    display: none;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.2s ease;
                    z-index: 10;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                }

                .message-delete-btn:hover {
                    background: #dc2626;
                    transform: scale(1.1);
                }

                .message-item:hover .message-delete-btn {
                    display: flex;
                }
                .message-avatar {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    font-weight: bold;
                    margin-right: 12px;
                    flex-shrink: 0;
                }
                .avatar-platform {
                    background: #e74c3c;
                    color: white;
                }
                .avatar-station {
                    background: #27ae60;
                    color: white;
                }
                .avatar-zjb {
                    background: #f39c12;
                    color: white;
                }
                .avatar-unknown {
                    background: #95a5a6;
                    color: white;
                }

                .message-content {
                    flex: 1;
                    background: #f8f9fa;
                    border-radius: 12px;
                    padding: 12px 15px;
                    position: relative;
                }
                .message-content::before {
                    content: '';
                    position: absolute;
                    left: -8px;
                    top: 15px;
                    width: 0;
                    height: 0;
                    border-top: 8px solid transparent;
                    border-bottom: 8px solid transparent;
                    border-right: 8px solid #f8f9fa;
                }

                .message-meta {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8px;
                }
                .sender-name {
                    font-weight: 600;
                    color: #2c3e50;
                    font-size: 14px;
                }
                .message-time {
                    font-size: 12px;
                    color: #7f8c8d;
                }
                .message-text {
                    color: #2c3e50;
                    line-height: 1.5;
                    font-size: 14px;
                }

                .action-buttons {
                    text-align: right;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    margin-top: 20px;
                }
                .action-buttons .btn {
                    margin-left: 10px;
                }

                .search-panel {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    border: none;
                    border-radius: 12px;
                    margin-bottom: 30px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .search-form {
                    padding: 25px;
                }
                .search-form .form-group {
                    margin-right: 15px;
                    margin-bottom: 15px;
                    display: inline-flex;
                    align-items: center;
                }
                .search-form .control-label {
                    margin-right: 8px;
                    margin-bottom: 0;
                    white-space: nowrap;
                    font-weight: 500;
                    color: #555;
                }
                .search-form .form-control {
                    border-radius: 6px;
                    border: 1px solid #ddd;
                    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
                }
                .search-form .btn {
                    border-radius: 6px;
                    padding: 8px 20px;
                    margin-right: 10px;
                }

                /* 滚动条样式 */
                .communication-section div::-webkit-scrollbar {
                    width: 6px;
                }
                .communication-section div::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 3px;
                }
                .communication-section div::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 3px;
                }
                .communication-section div::-webkit-scrollbar-thumb:hover {
                    background: #a8a8a8;
                }

                /* 消息展开/收起功能 */
                .message-toggle {
                    text-align: center;
                    margin: 10px 0;
                }
                .message-toggle a {
                    color: #007bff;
                    text-decoration: none;
                    font-size: 12px;
                }
                .message-toggle a:hover {
                    text-decoration: underline;
                }

                /* 状态指示器 */
                .status-indicator {
                    display: inline-block;
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-right: 5px;
                }
                .status-replied {
                    background: #28a745;
                }
                .status-pending {
                    background: #dc3545;
                    animation: blink 1.5s infinite;
                }
                @keyframes blink {
                    0%, 50% { opacity: 1; }
                    51%, 100% { opacity: 0.3; }
                }

                @media (max-width: 768px) {
                    .info-item {
                        min-width: 100%;
                    }
                    .message-item {
                        flex-direction: column;
                    }
                    .message-avatar {
                        margin-bottom: 10px;
                        align-self: flex-start;
                    }
                    .message-content::before {
                        display: none;
                    }
                }
            </style>

            <div class="message-index-wrapper">
                <div class="message-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="message-index-header message-index-fade-in">
                        <div class="message-index-header-content">
                            <div class="message-index-title">
                                <div class="message-index-title-icon">
                                    <i class="fa fa-comments"></i>
                                </div>
                                <div class="message-index-title-text">
                                    <h1 class="message-index-title-main">简历沟通管理</h1>
                                    <p class="message-index-title-sub">Message Management System</p>
                                </div>
                            </div>
                            <div class="message-index-actions">
                                <button type="button" class="message-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="message-index-nav-container message-index-fade-in-delay-1">
                        <ul class="message-index-nav-tabs">
                            <li class="message-index-nav-item">
                                <a href="{:U('Message/index')}" class="message-index-nav-link active">
                                    <i class="fa fa-list message-index-nav-icon"></i>
                                    <span>简历沟通管理</span>
                                </a>
                            </li>
                            <li class="message-index-nav-item">
                                <a href="javascript:void(0)" class="message-index-nav-link quick-filter" data-filter="urgent">
                                    <i class="fa fa-exclamation-triangle message-index-nav-icon"></i>
                                    <span>待回复</span>
                                </a>
                            </li>
                            <li class="message-index-nav-item">
                                <a href="javascript:void(0)" class="message-index-nav-link quick-filter" data-filter="recent">
                                    <i class="fa fa-clock-o message-index-nav-icon"></i>
                                    <span>最近24小时</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 搜索面板 - 默认隐藏 -->
                    <div class="message-index-search-panel" id="searchPanel">
                        <div class="message-index-search-header">
                            <div class="message-index-search-title">
                                <div class="message-index-search-icon">
                                    <i class="fa fa-search"></i>
                                </div>
                                <span>搜索筛选</span>
                            </div>
                            <button type="button" class="message-index-search-close" onclick="toggleSearchPanel()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                        <div class="message-index-search-body">
                            <form method="get" class="search-form" role="form">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">搜索条件：</label>
                                            <select class="form-control" name="kw">
                                                <php>foreach($c_kw as $key=>$value){</php>
                                                <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">搜索内容：</label>
                                            <input class="form-control" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容" />
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">回复状态：</label>
                                            <select name="is_reply" class='form-control'>
                                                <option value="">全部状态</option>
                                                <option value="1" <php>if($_get['is_reply'] === '1') echo 'selected';</php>>待回复</option>
                                                <option value="0" <php>if($_get['is_reply'] === '0') echo 'selected';</php>>已回复</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">服务站：</label>
                                            <select name="service_station_id" class='form-control'>
                                                <option value="">全部服务站</option>
                                                <php>if(isset($allServiceStationList)) foreach($allServiceStationList as $stationId => $stationName) {</php>
                                                <option value="{$stationId}" <php>if($_get['service_station_id'] == $stationId) echo 'selected';</php>>{$stationId}-{$stationName}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row" style="margin-top: 15px;">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">最后沟通时间：</label>
                                            <div class="input-group">
                                                <input type="text" name="time[start]" value="{$_get.time.start}" placeholder="开始时间" class="form-control datetimepicker" autocomplete="off">
                                                <span class="input-group-addon">至</span>
                                                <input type="text" name="time[end]" value="{$_get.time.end}" placeholder="结束时间" class="form-control datetimepicker" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">最后消息类型：</label>
                                            <select name="last_message_type" class='form-control'>
                                                <option value="">全部类型</option>
                                                <option value="1" <php>if($_get['last_message_type'] === '1') echo 'selected';</php>>服务站消息</option>
                                                <option value="2" <php>if($_get['last_message_type'] === '2') echo 'selected';</php>>平台回复</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fa fa-search"></i> 搜索
                                                </button>
                                                <a href="{:U('message/index')}" class="btn btn-default">
                                                    <i class="fa fa-refresh"></i> 重置
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 消息列表 -->
                    <div class="message-index-fade-in-delay-2">
                        <php>foreach($list as $v) {</php>
                        <div class="message-card">
                            <!-- 卡片头部 -->
                            <div class="message-header">
                                <h4>
                                    <i class="fa fa-user"></i> {$v.name}
                                    <php>if($v['is_reply']) {</php>
                                    <span class="urgent-indicator">
                                        <i class="fa fa-bell"></i> 待回复
                                    </span>
                                    <php>}</php>
                                </h4>
                                <div class="job-id">
                                    简历ID: #{$v.id} | 服务站: {:$serviceStationList[$v['service_station_id']]}
                                    <php>if(isset($recentMessagesList[$v['id']])) {</php>
                                    | 最后沟通: {:date('m-d H:i', $recentMessagesList[$v['id']][0]['create_time'])}
                                    <php>}</php>
                                </div>
                            </div>

                        <!-- 卡片主体 -->
                        <div class="message-body">
                            <!-- 简历信息 -->
                            <div class="resume-info">
                                <h5><i class="fa fa-file-text"></i> 简历信息</h5>

                                <!-- 求职诉求高亮显示 -->
                                <php>if($v['remark']) { </php>
                                <div class="job-demand">
                                    <strong><i class="fa fa-star"></i> 求职诉求：</strong>{$v.remark}
                                </div>
                                <php>}</php>

                                <div class="info-row">
                                    <div class="info-item">
                                        <span class="info-label">姓名：</span>
                                        <span class="info-value">{$v.name}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">电话：</span>
                                        <span class="info-value">{$v.phone}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">性别：</span>
                                        <span class="info-value">{$v.gender}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">学历：</span>
                                        <span class="info-value">{$v.education_level}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">应聘职位：</span>
                                        <span class="info-value">{$v.applied_position}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">添加时间：</span>
                                        <span class="info-value">{:date('Y-m-d H:i', $v['create_time'])}</span>
                                    </div>
                                </div>

                                <div style="margin-top: 15px;">
                                    <a target="_blank" href="http://we.zhongcaiguoke.com/{:$userJobDocList[$v['id']]['content']}" class="btn btn-sm btn-info">
                                        <i class="fa fa-file-pdf-o"></i> 简历文件
                                    </a>
                                    <php>if ($userJobDocList[$v['id']]['is_html'] == 3){</php>
                                    <a target="_blank" href="{:U('userjob/h5', ['id' => $v['id']])}" class="btn btn-sm btn-primary">
                                        <i class="fa fa-mobile"></i> H5简历
                                    </a>
                                    <php>}</php>
                                    <a href="{:U('userjob/edit', ['id' => $v['id']])}" class="btn btn-sm btn-warning">
                                        <i class="fa fa-edit"></i> 编辑简历
                                    </a>
                                </div>
                            </div>

                            <!-- 沟通记录 -->
                            <div class="communication-section">
                                <div class="communication-header">
                                    <span class="communication-title">
                                        <i class="fa fa-comments"></i> 最近沟通记录
                                    </span>
                                    <php>if(isset($recentMessagesList[$v['id']])) {</php>
                                    <span class="message-count">最近 3 条</span>
                                    <php>}</php>
                                </div>

                                <!-- 最近3条消息 -->
                                <php>if(isset($recentMessagesList[$v['id']]) && !empty($recentMessagesList[$v['id']])) {</php>
                                <div class="communication-body">
                                    <php>foreach(array_slice($recentMessagesList[$v['id']], 0, 3) as $msg) {</php>
                                    <php>
                                    // 获取发送者身份信息
                                    $senderInfo = '';
                                    $senderShort = '';
                                    $avatarClass = '';

                                    if ($msg['type'] == 2) {
                                        // 平台发送的消息
                                        $senderInfo = '平台回复';
                                        $senderShort = '平';
                                        $avatarClass = 'avatar-platform';
                                    } else {
                                        // 服务站或招就办发送的消息
                                        if (isset($messageServiceStationMap[$msg['service_station_id']])) {
                                            $station = $messageServiceStationMap[$msg['service_station_id']];
                                            if ($station['zsb_type'] == 1) {
                                                $senderInfo = '服务站消息';
                                                $senderShort = '站';
                                                $avatarClass = 'avatar-station';
                                            } else {
                                                $senderInfo = '招就办消息';
                                                $senderShort = '招';
                                                $avatarClass = 'avatar-zjb';
                                            }
                                        } else {
                                            $senderInfo = '未知消息';
                                            $senderShort = '?';
                                            $avatarClass = 'avatar-unknown';
                                        }
                                    }
                                    </php>
                                    <div class="message-item" data-message-id="{$msg.id}">
                                        <button class="message-delete-btn" onclick="deleteMessageIndexMessage('{$msg.id}', '{$v.id}')" title="删除消息">
                                            <i class="fa fa-times"></i>
                                        </button>
                                        <div class="message-avatar {:$avatarClass}">
                                            {:$senderShort}
                                        </div>
                                        <div class="message-content">
                                            <div class="message-meta">
                                                <span class="sender-name">
                                                    {:$senderInfo}
                                                </span>
                                                <span class="message-time">
                                                    {:date('m-d H:i', $msg['create_time'])}
                                                </span>
                                            </div>
                                            <div class="message-text">
                                                {:strip_tags(htmlspecialchars_decode($msg['content']))}
                                            </div>
                                        </div>
                                    </div>
                                    <php>}</php>
                                </div>

                                <php>if($v['need_reply']) {</php>
                                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 10px; margin-top: 15px; color: #856404;">
                                    <i class="fa fa-bell"></i> <strong>已提醒服务站回复信息</strong>
                                </div>
                                <php>}</php>

                                <!-- 快速回复区域 -->
                                <div class="quick-reply-section">
                                    <button type="button" class="quick-reply-toggle" onclick="toggleQuickReply('{$v.id}')">
                                        <i class="fa fa-reply"></i>
                                        <span>快速回复</span>
                                    </button>

                                    <div class="quick-reply-form" id="quickReplyForm_{$v.id}">
                                        <textarea class="quick-reply-textarea" id="quickReplyContent_{$v.id}" placeholder="请输入回复内容..."></textarea>
                                        <div class="quick-reply-actions">
                                            <div class="quick-reply-options">
                                                <div class="quick-reply-checkbox-container">
                                                    <input type="checkbox" class="quick-reply-checkbox" id="quickReplyNeed_{$v.id}">
                                                    <label for="quickReplyNeed_{$v.id}">提醒对方回复</label>
                                                </div>
                                                <div class="quick-reply-checkbox-container">
                                                    <input type="checkbox" class="quick-reply-checkbox" id="quickReplyWechat_{$v.id}" checked>
                                                    <label for="quickReplyWechat_{$v.id}">发送微信通知</label>
                                                </div>
                                            </div>
                                            <button type="button" class="quick-reply-submit" onclick="submitQuickReply('{$v.id}')">
                                                <i class="fa fa-send"></i>
                                                <span>发送回复</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <php>} else {</php>
                                <div style="text-align: center; color: #6c757d; padding: 20px;">
                                    <i class="fa fa-comment-o"></i> 暂无沟通记录
                                </div>

                                <!-- 快速回复区域 - 无沟通记录时 -->
                                <div class="quick-reply-section">
                                    <button type="button" class="quick-reply-toggle" onclick="toggleQuickReply('{$v.id}')">
                                        <i class="fa fa-reply"></i>
                                        <span>快速回复</span>
                                    </button>

                                    <div class="quick-reply-form" id="quickReplyForm_{$v.id}">
                                        <textarea class="quick-reply-textarea" id="quickReplyContent_{$v.id}" placeholder="请输入回复内容..."></textarea>
                                        <div class="quick-reply-actions">
                                            <div class="quick-reply-options">
                                                <div class="quick-reply-checkbox-container">
                                                    <input type="checkbox" class="quick-reply-checkbox" id="quickReplyNeed_{$v.id}">
                                                    <label for="quickReplyNeed_{$v.id}">提醒对方回复</label>
                                                </div>
                                                <div class="quick-reply-checkbox-container">
                                                    <input type="checkbox" class="quick-reply-checkbox" id="quickReplyWechat_{$v.id}" checked>
                                                    <label for="quickReplyWechat_{$v.id}">发送微信通知</label>
                                                </div>
                                            </div>
                                            <button type="button" class="quick-reply-submit" onclick="submitQuickReply('{$v.id}')">
                                                <i class="fa fa-send"></i>
                                                <span>发送回复</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <php>}</php>

                                <!-- 查看完整对话 -->
                                <div style="text-align: center; margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
                                    <a href="{:U('message/reply', ['user_job_id' => $v['id']])}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fa fa-external-link"></i> 查看完整对话
                                    </a>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="action-buttons">
                                <a href="{:U('message/reply', ['user_job_id' => $v['id'], 'service_station_id' => $v['service_station_id']])}" class="btn btn-success">
                                    <i class="fa fa-reply"></i>
                                    <php>echo $v['is_reply'] ? '立即回复' : '查看对话';</php>
                                </a>
                                <a href="{:U('userjob/index', ['kw' => 'id', 'val' => $v['id']])}" class="btn btn-info">
                                    <i class="fa fa-eye"></i> 查看简历详情
                                </a>
                            </div>
                        </div>
                        </div>
                        <php>}</php>

                        <!-- 分页 -->
                        <div class="message-index-fade-in-delay-3" style="text-align: center; margin-top: 30px;">
                            {$page}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
    require(["daterangepicker", "datetimepicker"], function($){
        // 初始化日期时间选择器
        $(".datetimepicker").each(function(){
            var opt = {
                language: "zh-CN",
                format: "yyyy-mm-dd hh:ii",
                autoclose: true,
                minuteStep: 1,
                todayBtn: true,
                todayHighlight: true
            };
            $(this).datetimepicker(opt);
        });

        // 搜索表单优化
        $('.search-form').on('submit', function() {
            // 移除空值参数
            $(this).find('input, select').each(function() {
                if ($(this).val() === '') {
                    $(this).prop('disabled', true);
                }
            });
        });
    });

    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
        } else {
            panel.classList.add('show');
        }
    }

    // 快速筛选功能
    $('.quick-filter').click(function(e) {
        e.preventDefault();
        var filter = $(this).data('filter');
        var currentUrl = window.location.href.split('?')[0];
        var newUrl = currentUrl;

        // 立即更新视觉状态
        $('.message-index-nav-link').removeClass('active');
        $(this).addClass('active');

        // 添加加载状态
        var originalHtml = $(this).html();
        $(this).html('<i class="fa fa-spinner fa-spin message-index-nav-icon"></i><span>加载中...</span>');

        // 获取当前的搜索参数
        var urlParams = new URLSearchParams(window.location.search);
        var searchParams = new URLSearchParams();

        // 保留搜索关键词等其他参数
        if (urlParams.get('kw')) {
            searchParams.set('kw', urlParams.get('kw'));
        }
        if (urlParams.get('val')) {
            searchParams.set('val', urlParams.get('val'));
        }
        if (urlParams.get('service_station_id')) {
            searchParams.set('service_station_id', urlParams.get('service_station_id'));
        }

        // 添加筛选参数
        switch(filter) {
            case 'urgent':
                searchParams.set('is_reply', '1');
                break;
            case 'recent':
                var yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1);
                var yesterdayStr = yesterday.getFullYear() + '-' +
                    String(yesterday.getMonth() + 1).padStart(2, '0') + '-' +
                    String(yesterday.getDate()).padStart(2, '0') + ' 00:00';
                searchParams.set('time[start]', yesterdayStr);
                break;
            default:
                // 不添加筛选参数，显示全部
                break;
        }

        // 构建最终URL
        var paramString = searchParams.toString();
        if (paramString) {
            newUrl += '?' + paramString;
        }

        // 延迟跳转，让用户看到加载状态
        setTimeout(function() {
            window.location.href = newUrl;
        }, 300);
    });

    // 设置导航标签active状态
    function setActiveNavTab() {
        // 移除所有active状态
        $('.message-index-nav-link').removeClass('active');

        // 获取当前URL参数
        var urlParams = new URLSearchParams(window.location.search);
        var isReply = urlParams.get('is_reply');
        var timeStart = urlParams.get('time[start]');

        // 根据参数设置对应的active状态
        if (isReply === '1') {
            // 待回复状态
            $('.quick-filter[data-filter="urgent"]').addClass('active');
        } else if (timeStart) {
            // 最近24小时
            $('.quick-filter[data-filter="recent"]').addClass('active');
        } else {
            // 默认状态（全部）
            $('.message-index-nav-link').first().addClass('active');
        }
    }

    // 页面加载完成后设置active状态
    $(document).ready(function() {
        setTimeout(function() {
            setActiveNavTab();
        }, 100);
    });



    // 快速回复功能
    function toggleQuickReply(userJobId) {
        var form = document.getElementById('quickReplyForm_' + userJobId);
        if (form.classList.contains('show')) {
            form.classList.remove('show');
        } else {
            form.classList.add('show');
            // 聚焦到文本框
            document.getElementById('quickReplyContent_' + userJobId).focus();
        }
    }

    // 提交快速回复
    function submitQuickReply(userJobId) {
        var content = document.getElementById('quickReplyContent_' + userJobId).value.trim();
        var needReply = document.getElementById('quickReplyNeed_' + userJobId).checked ? '1' : '0';
        var sendWechat = document.getElementById('quickReplyWechat_' + userJobId).checked ? '1' : '0';
        var submitBtn = document.querySelector('#quickReplyForm_' + userJobId + ' .quick-reply-submit');

        console.log('提交快速回复:', {
            userJobId: userJobId,
            content: content,
            needReply: needReply,
            sendWechat: sendWechat
        });

        // 输入验证
        if (!content) {
            layer.msg('请输入回复内容', {icon: 2});
            return;
        }

        if (content.length > 1000) {
            layer.msg('回复内容不能超过1000个字符', {icon: 2});
            return;
        }

        // 显示加载状态
        var originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 发送中...';

        // 发送AJAX请求
        $.ajax({
            url: '{:U("Message/quickReply")}',
            type: 'POST',
            data: {
                user_job_id: userJobId,
                content: content,
                needreply: needReply,
                send_wechat: sendWechat
            },
            timeout: 30000,
            success: function(response) {
                console.log('快速回复成功:', response);

                // 检查响应内容
                var responseText = typeof response === 'string' ? response : JSON.stringify(response);

                if (responseText.includes('成功') || responseText.includes('success')) {
                    layer.msg('回复发送成功！', {icon: 1, time: 2000});
                    // 清空输入框
                    document.getElementById('quickReplyContent_' + userJobId).value = '';
                    document.getElementById('quickReplyNeed_' + userJobId).checked = false;
                    // 隐藏快速回复表单
                    document.getElementById('quickReplyForm_' + userJobId).classList.remove('show');
                    // 延迟刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else if (responseText.includes('错误') || responseText.includes('失败')) {
                    layer.msg('回复失败，请重试', {icon: 2});
                } else {
                    // 默认认为成功
                    layer.msg('回复发送成功！', {icon: 1, time: 2000});
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                }
            },
            error: function(xhr, status, error) {
                console.error('快速回复失败:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });

                var errorMsg = '网络错误，请重试';
                if (status === 'timeout') {
                    errorMsg = '请求超时，请重试';
                } else if (xhr.status === 404) {
                    errorMsg = '页面不存在，请联系管理员';
                } else if (xhr.status === 500) {
                    errorMsg = '服务器错误，请重试';
                }

                layer.msg(errorMsg, {icon: 2});
            },
            complete: function() {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        });
    }

    // 删除消息索引页面消息功能
    window.deleteMessageIndexMessage = function(messageId, userJobId) {
        console.log('准备删除消息索引页面消息:', messageId, userJobId);

        if (!messageId) {
            layer.msg('消息ID无效', {icon: 2});
            return;
        }

        // 确认删除
        layer.confirm('确定要删除这条消息吗？删除后无法恢复。', {
            icon: 3,
            title: '确认删除',
            btn: ['确定删除', '取消']
        }, function(index) {
            // 确定删除
            layer.close(index);

            // 显示加载状态
            var loadingIndex = layer.load(1, {
                shade: [0.3, '#000']
            });

            // 发送删除请求
            $.ajax({
                url: '{:U("Message/deleteMessage")}',
                type: 'POST',
                data: {
                    message_id: messageId
                },
                timeout: 15000,
                success: function(response) {
                    console.log('删除响应:', response);

                    try {
                        // 尝试解析JSON响应
                        var result = typeof response === 'string' ? JSON.parse(response) : response;

                        if (result.status === 1 || result.success === true) {
                            layer.msg('消息删除成功', {icon: 1, time: 1500});

                            // 从页面中移除消息元素
                            var $messageItem = $('[data-message-id="' + messageId + '"]');
                            if ($messageItem.length > 0) {
                                $messageItem.fadeOut(300, function() {
                                    $(this).remove();

                                    // 检查该简历是否还有其他消息
                                    var $communicationBody = $messageItem.closest('.communication-body');
                                    if ($communicationBody.find('.message-item').length === 0) {
                                        // 如果没有消息了，显示无消息状态
                                        $communicationBody.replaceWith(
                                            '<div style="text-align: center; color: #6c757d; font-style: italic; padding: 20px;">' +
                                            '<i class="fa fa-comment-o" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>' +
                                            '暂无沟通记录' +
                                            '</div>'
                                        );
                                    }
                                });
                            }
                        } else {
                            var errorMsg = result.message || result.msg || result.info || '删除失败，请重试';
                            layer.msg(errorMsg, {icon: 2});
                        }
                    } catch (e) {
                        // 如果不是JSON格式，检查响应文本
                        var responseText = response.toString();
                        if (responseText.includes('成功') || responseText.includes('success')) {
                            layer.msg('消息删除成功', {icon: 1, time: 1500});

                            // 从页面中移除消息元素
                            var $messageItem = $('[data-message-id="' + messageId + '"]');
                            if ($messageItem.length > 0) {
                                $messageItem.fadeOut(300, function() {
                                    $(this).remove();
                                });
                            }
                        } else {
                            layer.msg('删除失败，请重试', {icon: 2});
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('删除请求失败:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });

                    var errorMsg = '网络错误，请重试';
                    if (status === 'timeout') {
                        errorMsg = '请求超时，请重试';
                    } else if (xhr.status === 404) {
                        errorMsg = '删除接口不存在，请联系管理员';
                    } else if (xhr.status === 500) {
                        errorMsg = '服务器错误，请重试';
                    } else if (xhr.status === 403) {
                        errorMsg = '没有权限删除此消息';
                    }

                    layer.msg(errorMsg, {icon: 2});
                },
                complete: function() {
                    layer.close(loadingIndex);
                }
            });
        }, function(index) {
            // 取消删除
            layer.close(index);
        });
    };
</script>

<include file="block/footer" />
