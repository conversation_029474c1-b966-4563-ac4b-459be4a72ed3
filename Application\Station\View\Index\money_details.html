﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <title>金额详情</title>
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <style>
        :root {
            --primary-color: #00bf80;
            --warning-color: #e67e22;
            --secondary-color: #666;
            --card-bg: #fff;
            --border-color: #eee;
        }
 

        .container {
            max-width: 600px;
            margin: 10px;
        }

        /* 金额卡片优化 */
        .amount-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
        }

        /* 金额并排布局 */
        .amount-row {
            display: flex;
            gap: 20px;
            margin-bottom: 24px;
            flex-wrap: wrap;
        }
        .amount-group {
            flex: 1;
            min-width: 200px;
        }

        /* 金额显示优化 */
        .amount-label {
            font-size: 14px;
            color: var(--secondary-color);
            margin-bottom: 6px;
        }
        .amount-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-color);
            line-height: 1.2;
        }
        .warning-value {
            color: var(--warning-color);
        }

 
       

        /* 标题样式 */
        .section-title {
            font-size: 20px;
            color: #000;
            margin-bottom: 24px;
        }

       

        /* 限制说明 */
        .limit-section {
            border-top: 1px solid var(--border-color);
            padding-top: 20px;
        }
        .limit-title {
            color: var(--warning-color);
            font-size: 14px;
            margin-bottom: 16px;
        }
        .limit-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }
        .limit-item:last-child {
            border-bottom: none;
        }
        .limit-name {
            color: var(--secondary-color);
        }
        .limit-amount {
            color: #000;
            font-weight: 500;
        }

        /* 客服提示 */
        .notice-text {
            color: var(--secondary-color);
            font-size: 14px;
            margin-top: 24px;
            text-align: center;
        }
        .notice-text a {
            color: var(--primary-color);
            text-decoration: none;
        }

        /* 响应式优化 */

               /* 移动端优化 */
               @media (max-width: 480px) {
            .amount-row {
                flex-direction: column;
                gap: 16px;
            }
            .amount-group {
                min-width: auto;
            }
            .amount-value {
                font-size: 20px;
            }
            .amount-card {
                padding: 12px;
            }
        }
     /* 按钮组优化 */
     .action-btns {
            display: grid;
            gap: 12px;
            margin: 10px;
        }
        .btn {
            padding: 14px;
            border-radius: 6px;
            border: 1px solid var(--primary-color);
            background: #59c567;
            color: #fff;
            font-size: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            position: relative;
        }
        .btn::after {
            position: absolute;
            right: 14px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,191,128,0.1);
            color: var(--primary-color);
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 2px;
        }

         /* 新增弹窗样式 */
         .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            position: relative;
            animation: modalSlide 0.3s ease-out;
        }

        @keyframes modalSlide {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            cursor: pointer;
            font-size: 24px;
            color: #666;
            line-height: 1;
        }

        .modal-body {
            padding: 20px;
            line-height: 1.6;
            max-height: 60vh;
            overflow-y: auto;
        }

        /* 问号按钮样式 */
        .info-icon {
            display: inline-block;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--secondary-color);
            color: white;
            text-align: center;
            line-height: 18px;
            font-size: 12px;
            margin-left: 8px;
            cursor: help;
            transition: all 0.2s;
        }

        .info-icon:hover {
            background: var(--primary-color);
            transform: scale(1.1);
        }
         /* 返回箭头样式 */
.back-arrow {
    position: fixed;
    top: 115px;
    left: 15px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.2s;
}

.back-arrow::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    width: 12px;
    height: 12px;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: translateY(-50%) rotate(45deg);
}

/* 点击反馈效果 */
.back-arrow:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.7);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .back-arrow {
        top: 140px;
        left: 10px;
        width: 36px;
        height: 36px;
    }
    
    .back-arrow::before {
        left: 13px;
        width: 10px;
        height: 10px;
    }
}
    </style>
</head>
<body>
            <!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
    <include file="headers"/>
    <div class="container">
        <div class="amount-card">

         
            <!-- 修改后的金额并排布局 -->
            <div class="amount-row">
                <div class="amount-group">
                    <div class="amount-label">总金额</div>
                    <div class="amount-value">¥{:$serviceStationRow['total_price']}</div>
                </div>
                <div class="amount-group">
                    <div class="amount-label">可提现余额</div>
                    <div class="amount-value warning-value">¥{:$serviceStationRow['price'] ?:0}</div>
                </div>
            </div>

            <div class="limit-section">
                <div class="limit-title">资金限制（限制总额 ¥{:$serviceStationRow['freeze_price']+0}）</div>
                
                <!-- 在原有代码的limit-item中添加问号按钮 -->
    <div class="limit-item">
        <span class="limit-name">
            保护期冻结
            <span class="info-icon" onclick="showRuleModal()">?</span>
        </span>
        <span class="limit-amount">¥{:$serviceStationRow['freeze_price']}</span>
    </div>

                <div class="limit-item">
                    <span class="limit-name">服务售后投诉</span>
                    <span class="limit-amount">¥0</span>
                </div>
            </div>

            <p class="notice-text">
                如有疑问，请<a href="https://work.weixin.qq.com/kfid/kfc0f3ecae7d5279996">联系客服</a>
            </p>
        </div>
    </div>
     <!-- 新增弹窗结构 -->
     <div class="modal-overlay" id="ruleModal" onclick="closeRuleModal(event)">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">保护期冻结规则</h3>
                <span class="modal-close" onclick="closeRuleModal(event)">&times;</span>
            </div>
            <div class="modal-body">
                <p>1. 学员培训及入企实习未签正式合同之前，处于保护期，对应培训费将处于冻结状态</p>
                <p>2. 冻结期间资金不可提现，用于保障可能发生的售后服务</p>
                <p>2. 学员入职正式签订劳动后，冻结金额将在保护期结束后自动释放</p>
                <p>3. 如无售后纠纷，冻结金额将在保护期结束后自动释放</p>
                <p>4. 若发生售后纠纷，冻结金额将根据处理结果进行相应扣除</p>
                <p>5. 公益实习返费补贴在学员工资发放之后数据上传即实时到账余额</p>
            </div>
        </div>
    </div>
    <!--div class="action-btns" style="margin-top: 28px;">
        <a href="{:U('index/money')}">
        <button class="btn"  style="width: 100%;">返回余额管理</button>
    </a>
    </div-->
    <script>
        // 弹窗控制逻辑
        function showRuleModal() {
            const modal = document.getElementById('ruleModal')
            modal.style.display = 'flex'
            document.body.style.overflow = 'hidden'
        }

        function closeRuleModal(event) {
            // 阻止事件冒泡
            event.stopPropagation()
            const modal = document.getElementById('ruleModal')
            modal.style.display = 'none'
            document.body.style.overflow = 'auto'
        }

        // 点击弹窗内容区域不关闭
        document.querySelector('.modal-content').addEventListener('click', e => {
            e.stopPropagation()
        })
        var noticeSwiper = new Swiper('.noticeSwiper', {
            direction: 'vertical',
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            }
        })
        var noticeSwiper = new Swiper('.page0106Swiper', {
            pagination: {
                el: ".swiper-pagination",
            },

            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            }
        });

        // 返回逻辑（优先返回历史记录，失败则跳转首页）
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/'; // 修改为你的默认回退地址
            }
        }
    </script>
</body>
</html>