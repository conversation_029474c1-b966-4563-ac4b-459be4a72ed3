﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>简历登记</title>
    <!-- 精简版Flatpickr样式 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        body {
            background: #f8f8f8;
            color: #333;
            font-size: 16px;
            line-height: 1.6;
            max-width: 750px;
        }

        .container {
            padding: 15px;
        }

        .weui-cells__title {
            color: #999;
            font-size: 14px;
            padding: 15px 15px 5px;
        }

        .weui-cell {
            background: #fff;
            padding: 12px 15px;
            display: flex;
            align-items: center;
            margin-top: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.03);
        }

        .weui-label {
            color: #333;
            font-size: 16px;
        }

        .weui-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: 0 10px;
            background: transparent;
        }

        .weui-uploader {
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            margin-top: 10px;
        }

        .weui-uploader__hd {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .weui-uploader__input-box {
            width: 100px;
            height: 100px;
            border: 1px dashed #d9d9d9;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .weui-uploader__input {
            opacity: 0;
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .weui-btn {
            width: 100%;
            height: 48px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            margin: 30px 0;
        }

        .weui-btn_primary {
            background: #07c160;
            color: #fff;
        }

        .dynamic-add {
            color: #576b95;
            font-size: 14px;
            padding: 10px 0;
            text-align: center;
        }

        .weui-check__label {
            display: flex;
            align-items: center;
            padding: 15px;
            font-size: 14px;
        }

        .weui-agree__checkbox {
            margin-right: 8px;
        }

        select.weui-input {
            appearance: none;
            background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNNyAxMGw1IDUgNS01eiIvPjwvc3ZnPg==) no-repeat right 10px center/12px;
        }
        /* 移动端优化样式 */
        .date-picker {
            height: 38px;
            padding: 0 26px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 15px;
            background: #fff url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTkgM2gtMVYxaC0ydjJIOFYxSDZ2Mkg1Yy0xLjEgMC0yIC45LTIgMnYxNmMwIDEuMS45IDIgMiAyaDE0YzEuMSAwIDItLjkgMi0yVjVjMC0xLjEtLjktMi0yLTJ6bTAgMThINVY4aDE0djEzem0tNy0zbDQtNGgtM1Y5aC0ydjVIOHoiLz48L3N2Zz4=') no-repeat left 2px center/20px;
            color: #333;
        }
        
        /* 移动端全屏弹窗 */
        .flatpickr-calendar {
            width: 100% !important;
            max-width: 100% !important;
            left: 0 !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
        }
    </style>
</head>
<body>
    <form action="{:U('/index/userjob')}" method="post" enctype="multipart/form-data" onsubmit="return validateForm()">

    <div class="container">
        <h2 class="weui-cells__title">基本信息</h2>
        <div class="weui-cell">
            <label class="weui-label">证件照</label>
            <div class="weui-uploader">
                <div class="weui-uploader__input-box">
                    <input class="weui-uploader__input" name="photo_path" type="file" accept="image/*" required>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="#999">
                        <path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-8 12h2v-4h4v-2h-4V5h-2v4H7v2h4v4z"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="weui-cell">
            <label class="weui-label">姓名</label>
            <input class="weui-input" name="name" type="text" placeholder="请输入姓名" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">身份证</label>
            <input class="weui-input" name="id_number" type="text" placeholder="请输入身份证" required>
        </div>

        <div class="weui-cell">
            <label class="weui-label">性别</label>
            <select class="weui-input" name="gender">
                <option value="">请选择</option>
                <option>男</option>
                <option>女</option>
            </select>
        </div>

        <div class="weui-cell">
            <label class="weui-label" style="width: 90px;">出生日期</label>
             <!-- 日期输入框 -->
            <input type="text" class="date-picker" name="birthdate" placeholder="请选择出生年月日" readonly>
        </div>
        <div class="weui-cell">
            <label class="weui-label">婚姻状况</label>
            <select class="weui-input" name="marital_status">
                <option value="">请选择</option>
                <option>已婚</option>
                <option>未婚</option>
            </select>
        </div>
        <div class="weui-cell">
            <label class="weui-label">民族</label>
            <input class="weui-input" name="nation" type="text" placeholder="请输入民族" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">政治面貌</label>
            <input class="weui-input" name="political_status" type="text" placeholder="请输入政治面貌" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">手机号码</label>
            <input class="weui-input" name="phone" type="text" placeholder="请输入手机号码" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">邮箱</label>
            <input class="weui-input" name="email" type="text" placeholder="请输入邮箱" required>
        </div>

        <div class="weui-cell">
            <label class="weui-label">健康状况</label>
            <input class="weui-input" name="health_status" type="text" placeholder="请输入健康状态" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">身高(cm)</label>
            <input class="weui-input" name="height" type="text" placeholder="请输入体重" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">体重(kg)</label>
            <input class="weui-input" name="weight" type="text" placeholder="请输入身高" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">视力</label>
            <input class="weui-input" name="vision" type="text" placeholder="请输入视力" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">是否恐高</label>
            <select class="weui-input" name="is_afraid_heights">
                <option value="">请选择</option>
                <option>否</option>
                <option>是</option>
            </select>
        </div>
        <div class="weui-cell">
            <label class="weui-label">学历</label>
            <input class="weui-input" name="education_level" type="text" placeholder="请输入学历" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">毕业院校</label>
            <input class="weui-input" name="graduate_school" type="text" placeholder="请输入毕业院校" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">专业</label>
            <input class="weui-input" name="major[]" type="text" placeholder="请输入专业" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">工作年限</label>
            <input class="weui-input" name="work_experience_years" type="text" placeholder="请输入工作年限" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">紧急电话</label>
            <input class="weui-input" name="emergency_contact" type="text" placeholder="请输入紧急联系人电话" required>
        </div>
        <div class="weui-cell">
            <label class="weui-label">应聘职位</label>
            <input class="weui-input" name="applied_position" type="text" placeholder="请输入应聘职位" required>
        </div>

        <!-- 其他字段结构类似 -->

        <h2 class="weui-cells__title">教育经历（从高中开始）</h2>
        <div id="education-list">
            <div class="weui-cell dynamic-item">
                <div style="width: 100%">
                    &nbsp;&nbsp;&nbsp;入学时间 <input type="text" name="start_date" class="date-picker" placeholder="入学时间" readonly>
                    <BR><BR>
                    &nbsp;&nbsp;&nbsp;毕业时间 <input class="date-picker" type="date" name="end_date" placeholder="毕业时间" readonly>
                    <input class="weui-input" type="text" name="school_name" style="margin-top: 12px;" placeholder="学校名称">
                    <input class="weui-input" type="text" name="major" style="margin-top: 12px;" placeholder="专业">
                    <input class="weui-input" type="text" name="witness" style="margin-top: 12px;" placeholder="证明人">
                </div>
            </div>
        </div>
        <div class="dynamic-add" onclick="addEducation()">
            + 添加教育经历
        </div>

        <!-- 工作经历等类似模块 -->
        <h2 class="weui-cells__title">工作经历</h2>
        <div id="work-list">
            <div class="weui-cell dynamic-item2">
                
                <div style="width: 100%">
                    &nbsp;&nbsp;&nbsp;入职时间 <input type="text" name="work_start_date" class="date-picker" placeholder="入职时间" readonly>
                    <BR><BR>
                    &nbsp;&nbsp;&nbsp;离职时间 <input class="date-picker" type="date" name="work_end_date" placeholder="离职时间" readonly>
                    <input class="weui-input" type="text" name="company_name" style="margin-top: 12px;" placeholder="公司名称">
                    <input class="weui-input" type="text" name="position" style="margin-top: 12px;" placeholder="岗位">
                    <input class="weui-input" type="text" name="leave_reason" style="margin-top: 12px;" placeholder="离职原因">
                </div>
           
        </div>
        </div>
        <div class="dynamic-add" onclick="addWork_experience()">
            + 添加工作经历
        </div>

                <!-- 家庭成员等类似模块 -->
                <h2 class="weui-cells__title">家庭成员</h2>
                <div id="family-list">
                <span class="weui-cell dynamic-item_family">
                <div class="container" style="width: 100%;"> 
                    <div  class="weui-cell" style="margin-left: -25px;">
                        <label class="weui-label" style="width: 90px;">亲属关系</label>
                        <input class="weui-input"  name="relationship" type="text" placeholder="请输入亲属关系" required>
                    </div>
                    <div class="weui-cell" style="margin-left: -25px;">
                        <label class="weui-label" style="width: 90px;text-align: right;padding-right: 14px;">姓 名</label>
                        <input class="weui-input" name="full_name" type="text" placeholder="请输入亲属姓名" required>
                    </div>
                    <div class="weui-cell" style="margin-left: -25px;">
                        <label class="weui-label" style="width: 90px;">工作单位</label>
                        <input class="weui-input" name="work_unit" type="text" placeholder="请输入亲属工作单位" required>
                    </div>
                    <div class="weui-cell" style="margin-left: -25px;">
                        <label class="weui-label" style="width: 90px;text-align: right;padding-right: 14px;">职 务</label>
                        <input class="weui-input" name="relationship_position" type="text" placeholder="请输入亲属职务" required>
                    </div>
                    <div class="weui-cell" style="margin-left: -25px;">
                        <label class="weui-label" style="width: 90px;">联系电话</label>
                        <input class="weui-input" name="contact" type="text" placeholder="请输入亲属联系电话" required>
                    </div>
                </div>
            </span>
        </div>
                <div class="dynamic-add" onclick="addfamily()">
                    + 添加家庭成员
                </div>
                
        <!-- 技能证书表等类似模块 -->
        <h2 class="weui-cells__title">技能证书</h2>
        <div id="certificates-list">
        <span class="weui-cell dynamic-item_certificates">
        <div class="container" style="width: 100%;"> 
            <div class="weui-cell" style="margin-left: -25px;">
                <label class="weui-label">技能类型</label>
                <select class="weui-input" name="is_afraid_heights" style="padding-left: 30px;">
                    <option value="">请选择</option>
                    <option>外语</option>
                    <option>计算机</option>
                    <option>职业资格</option>
                </select>
            </div>
            <div class="weui-cell"  style="margin-left: -25px;">
                <label class="weui-label" style="width: 90px;">证书名称</label>
                <input class="weui-input" name="certificate_name" type="text" placeholder="请输入证书名称" required>
            </div>
            <div class="weui-cell"  style="margin-left: -25px;">
                <label class="weui-label" style="width: 90px;">证书等级</label>
                <input class="weui-input" name="level" type="text" placeholder="请输入证书等级" required>
            </div>
            <div class="weui-cell"  style="margin-left: -25px;">
                <label class="weui-label" style="width: 90px;">取证日期</label>
                <input class="weui-input" name="get_date" type="date" placeholder="请选择取证日期" required>
            </div>
        </div>
        </span>
        </div>
        <div class="dynamic-add" onclick="addfcertificates()">
            + 添加技能证书
        </div>

        <div class="weui-check__label">
            <input type="checkbox" class="weui-agree__checkbox" required>
            本人承诺所填信息真实有效
        </div>

        <button class="weui-btn weui-btn_primary">提交简历</button>
    </div>
    </form>
    <script>
        var num = 1;
        function addEducation() {
            num++;
            console.log(num)
            const div = document.createElement('div');
            div.className = 'weui-cell dynamic-item';
            div.innerHTML = `
                <div style="width: 100%">
                    &nbsp;&nbsp;&nbsp;入学时间 <input type="text" name="start_date`+num+`" class="date-picker" placeholder="入学时间" readonly>
                    <BR><BR>
                    &nbsp;&nbsp;&nbsp;毕业时间 <input class="date-picker" type="date" name="end_date`+num+`" placeholder="毕业时间" readonly>
                    <input class="weui-input" type="text" name="school_name`+num+`" style="margin-top: 12px;" placeholder="学校名称">
                    <input class="weui-input" type="text" name="major`+num+`" style="margin-top: 12px;" placeholder="专业">
                    <input class="weui-input" type="text" name="witness`+num+`" style="margin-top: 12px;" placeholder="证明人">
                </div>`;
            console.log(div)
            document.getElementById('education-list').appendChild(div);
        }
        var addWordNum = 1;
        function addWork_experience() {
            const div = document.createElement('div');
            addWordNum ++;
            div.className = 'weui-cell dynamic-item2';
            div.innerHTML = `
                <div style="width: 100%">
                    &nbsp;&nbsp;&nbsp;入职时间 <input type="text" name="work_start_date`+addWordNum+`" class="date-picker" placeholder="入职时间" readonly>
                    <BR><BR>
                    &nbsp;&nbsp;&nbsp;离职时间 <input class="date-picker" type="date" name="work_end_date`+addWordNum+`" placeholder="离职时间" readonly>
                    <input class="weui-input" type="text" name="company_name`+addWordNum+`" style="margin-top: 12px;" placeholder="公司名称">
                    <input class="weui-input" type="text" name="position`+addWordNum+`" style="margin-top: 12px;" placeholder="岗位">
                    <input class="weui-input" type="text" name="leave_reason`+addWordNum+`" style="margin-top: 12px;" placeholder="离职原因">
                </div>`;
            document.getElementById('work-list').appendChild(div);
        }

        var addfamilyNum = 1;
        function addfamily() {
            const div = document.createElement('span');
            div.className = 'weui-cell dynamic-item_family';
            addfamilyNum++;
            div.innerHTML = `
                <div class="container" style="width: 100%;"> 
                    <div  class="weui-cell" style="margin-left: -25px;">
                        <label class="weui-label" style="width: 90px;">亲属关系</label>
                        <input class="weui-input"  name="relationship`+addfamilyNum+`" type="text" placeholder="请输入亲属关系" required>
                    </div>
                    <div class="weui-cell" style="margin-left: -25px;">
                        <label class="weui-label" style="width: 90px;text-align: right;padding-right: 14px;">姓 名</label>
                        <input class="weui-input" name="full_name`+addfamilyNum+`" type="text" placeholder="请输入亲属姓名" required>
                    </div>
                    <div class="weui-cell" style="margin-left: -25px;">
                        <label class="weui-label" style="width: 90px;">工作单位</label>
                        <input class="weui-input" name="work_unit`+addfamilyNum+`" type="text" placeholder="请输入亲属工作单位" required>
                    </div>
                    <div class="weui-cell" style="margin-left: -25px;">
                        <label class="weui-label" style="width: 90px;text-align: right;padding-right: 14px;">职 务</label>
                        <input class="weui-input" name="relationship_position`+addfamilyNum+`" type="text" placeholder="请输入亲属职务" required>
                    </div>
                    <div class="weui-cell" style="margin-left: -25px;">
                        <label class="weui-label" style="width: 90px;">联系电话</label>
                        <input class="weui-input" name="contact`+addfamilyNum+`" type="text" placeholder="请输入亲属联系电话" required>
                    </div>
                </div>`;
            document.getElementById('family-list').appendChild(div);
        }

        var addfcertificatesNum = 1;
        function addfcertificates() {
            const div = document.createElement('span');
            div.className = 'weui-cell dynamic-item_certificates';
            addfcertificatesNum++;
            div.innerHTML = `
                     <div class="container" style="width: 100%;"> 
            <div class="weui-cell" style="margin-left: -25px;">
                <label class="weui-label">技能类型</label>
                <select class="weui-input" name="is_afraid_heights`+addfcertificatesNum+`" style="padding-left: 30px;">
                    <option value="">请选择</option>
                    <option>外语</option>
                    <option>计算机</option>
                    <option>职业资格</option>
                </select>
            </div>
            <div class="weui-cell"  style="margin-left: -25px;">
                <label class="weui-label" style="width: 90px;">证书名称</label>
                <input class="weui-input" name="certificate_name`+addfcertificatesNum+`" type="text" placeholder="请输入证书名称" required>
            </div>
            <div class="weui-cell"  style="margin-left: -25px;">
                <label class="weui-label" style="width: 90px;">证书等级</label>
                <input class="weui-input" name="level`+addfcertificatesNum+`" type="text" placeholder="请输入证书等级" required>
            </div>
            <div class="weui-cell"  style="margin-left: -25px;">
                <label class="weui-label" style="width: 90px;">取证日期</label>
                <input class="weui-input" name="get_date`+addfcertificatesNum+`" type="date" placeholder="请选择取证日期" required>
            </div>
        </div>`;
            document.getElementById('certificates-list').appendChild(div);
        }

        function validateForm() {
            const requiredFields = document.querySelectorAll('[required]');
            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    alert(field.placeholder || '请填写完整信息！');
                    field.focus();
                    return false;
                }
            }
            return true;
        }

        // 文件上传预览
document.querySelector('.weui-uploader__input').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(event) {
            const img = document.createElement('img');
            img.src = event.target.result;
            img.style.width = '100%';
            document.querySelector('.weui-uploader__input-box').innerHTML = '';
            document.querySelector('.weui-uploader__input-box').appendChild(img);
        }
        reader.readAsDataURL(file);
    }
});
document.querySelector('input[type="date"]').addEventListener('focus', function() {
  if(!this.value) this.type = 'date';
});
document.querySelector('input[type="date"]').addEventListener('blur', function() {
  if(!this.value) this.type = 'text';
});
    </script>
    <!-- 生产环境建议使用本地文件 -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>

    <script>
        // 纯日期配置
        flatpickr(".date-picker", {
            locale: "zh",
            dateFormat: "Y-m-d",
            minDate: "1950-01-01",
            maxDate: new Date(),
            enableTime: false, // 禁用时间选择
            static: true,      // 移动端全屏模式
            monthSelectorType: "static",
            showMonths: 1,     // 单月显示
            disableMobile: false
        });
    </script>
</body>
</html>