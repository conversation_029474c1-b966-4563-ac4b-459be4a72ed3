<ul class="nav nav-tabs">

    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='index' ) echo 'active'</php>"><a href="{:U('userjob/index')}">简历列表管理</a></li>
    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='edit') echo 'active'</php>"><a href="{:U('userjob/edit')}"><php>echo !I('get.id') ? '添加' : '编辑'</php>简历</a></li>
    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='upjob') echo 'active'</php>"><a href="{:U('userjob/upjob')}">添加简历文件</a></li>

    <php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='education'){</php>
    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='education') echo 'active'</php>"><a href="javascript:void(0);">查看教育信息</a></li>
    <php>}</php>
    <php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='educationedit'){</php>
    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='educationedit') echo 'active'</php>"><a href="javascript:void(0);">编辑教育信息</a></li>
    <php>}</php>



    <php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='familymembers'){</php>
    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='familymembers') echo 'active'</php>"><a href="javascript:void(0);">查看家庭成员</a></li>
    <php>}</php>
    <php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='familymembersedit'){</php>
    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='familymembersedit') echo 'active'</php>"><a href="javascript:void(0);">编辑家庭成员</a></li>
    <php>}</php>

    <php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='workexperience'){</php>
    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='workexperience') echo 'active'</php>"><a href="javascript:void(0);">查看工作经历</a></li>
    <php>}</php>
    <php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='workexperienceedit'){</php>
    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='workexperienceedit') echo 'active'</php>"><a href="javascript:void(0);">编辑工作经历</a></li>
    <php>}</php>


    <php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='skillscertificates'){</php>
    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='skillscertificates') echo 'active'</php>"><a href="javascript:void(0);">查看技能证书</a></li>
    <php>}</php>
    <php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='skillscertificatesedit'){</php>
    <li class="<php>if(CONTROLLER_NAME=='Userjob' && ACTION_NAME=='skillscertifedit') echo 'active'</php>"><a href="javascript:void(0);">编辑技能证书</a></li>
    <php>}</php>



</ul>