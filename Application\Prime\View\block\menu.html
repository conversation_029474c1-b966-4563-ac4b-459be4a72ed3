<div class="col-xs-12 col-sm-3 col-lg-2 modern-sidebar">
	<style>
		/* 现代化侧边栏样式 */
		.modern-sidebar {
			background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
			min-height: 100vh;
			padding: 20px 15px;
			border-right: 1px solid #dee2e6;
		}

		.menu-section {
			margin-bottom: 25px;
			background: #fff;
			border-radius: 12px;
			box-shadow: 0 2px 8px rgba(0,0,0,0.08);
			overflow: hidden;
			transition: all 0.3s ease;
		}

		.menu-section:hover {
			box-shadow: 0 4px 16px rgba(0,0,0,0.12);
			transform: translateY(-2px);
		}

		.menu-header {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 15px 20px;
			cursor: pointer;
			position: relative;
			transition: all 0.3s ease;
		}

		.menu-header:hover {
			background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
		}

		.menu-title {
			margin: 0;
			font-size: 16px;
			font-weight: 600;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.menu-icon {
			font-size: 14px;
			transition: transform 0.3s ease;
		}

		.menu-header.collapsed .menu-icon {
			transform: rotate(-90deg);
		}

		.menu-items {
			background: #fff;
			border: none;
			padding: 0;
			margin: 0;
			list-style: none;
			max-height: 500px;
			overflow: hidden;
			transition: max-height 0.3s ease;
		}

		.menu-items.collapsed {
			max-height: 0;
		}

		.menu-item {
			border-bottom: 1px solid #f1f3f4;
			transition: all 0.3s ease;
		}

		.menu-item:last-child {
			border-bottom: none;
		}

		.menu-link {
			display: block;
			padding: 15px 20px;
			color: #495057;
			text-decoration: none;
			font-size: 14px;
			font-weight: 500;
			position: relative;
			transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			border-radius: 0 25px 25px 0;
			margin: 2px 0;
		}

		.menu-link:before {
			content: '';
			position: absolute;
			left: 0;
			top: 0;
			bottom: 0;
			width: 4px;
			background: linear-gradient(135deg, #667eea 0%, #db861e 100%);
			transform: scaleY(0);
			transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			border-radius: 0 2px 2px 0;
		}

		.menu-link:hover {
			background: linear-gradient(90deg, rgba(102, 126, 234, 0.08) 0%, rgba(102, 126, 234, 0.04) 100%);
			color: #667eea;
			text-decoration: none;
			padding-left: 25px;
			transform: translateX(5px);
		}

		.menu-link:hover:before {
			transform: scaleY(1);
		}

		.menu-link:focus {
			outline: none;
			background: linear-gradient(90deg, rgba(102, 126, 234, 0.12) 0%, rgba(102, 126, 234, 0.06) 100%);
			color: #667eea;
			text-decoration: none;
			box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
		}

		.menu-link:active {
			background: linear-gradient(90deg, rgba(207, 138, 41, 0.15) 0%, rgba(184, 120, 41, 0.08)100%);
			transform: translateX(2px);
			transition: all 0.1s ease;
		}

		.menu-link.active {
			background: linear-gradient(90deg, #e89a26 0%, #764ba2 100%);
			color: white;
			font-weight: 600;
			box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
		}

		.menu-link.active:before {
			transform: scaleY(1);
			background: rgba(255, 255, 255, 0.8);
		}

		.menu-link.active:hover {
			background: linear-gradient(90deg, #e07d26 0%, #667eea 100%);
			color: white;
			padding-left: 20px;
			transform: translateX(3px);
		}

		.menu-link.active:focus {
			box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4), 0 0 0 2px rgba(255, 255, 255, 0.3);
		}

		.menu-item-icon {
			margin-right: 10px;
			width: 16px;
			text-align: center;
			font-size: 14px;
		}

		/* 响应式设计 */
		@media (max-width: 768px) {
			.modern-sidebar {
				min-height: auto;
				padding: 15px 10px;
			}

			.menu-section {
				margin-bottom: 15px;
			}

			.menu-header {
				padding: 12px 15px;
			}

			.menu-link {
				padding: 12px 15px;
				font-size: 13px;
			}
		}

		/* 动画效果 */
		@keyframes slideIn {
			from {
				opacity: 0;
				transform: translateX(-20px);
			}
			to {
				opacity: 1;
				transform: translateX(0);
			}
		}

		.menu-section {
			animation: slideIn 0.5s ease forwards;
		}

		.menu-section:nth-child(2) {
			animation-delay: 0.1s;
		}

		.menu-section:nth-child(3) {
			animation-delay: 0.2s;
		}

		.menu-section:nth-child(4) {
			animation-delay: 0.3s;
		}
	</style>

	<?php
	// 菜单图标映射
	function getMenuIcon($menuName) {
		$iconMap = [
			'系统管理' => 'cogs',
			'用户管理' => 'users',
			'简历管理' => 'file-text',
			'项目管理' => 'briefcase',
			'消息管理' => 'comments',
			'培训管理' => 'graduation-cap',
			'服务站管理' => 'building',
			'财务管理' => 'money',
			'统计分析' => 'bar-chart',
			'内容管理' => 'edit',
			'设置' => 'gear',
			'工具' => 'wrench',
			'报表' => 'file-excel-o',
			'审核' => 'check-circle',
			'权限' => 'shield',
		];
		return isset($iconMap[$menuName]) ? $iconMap[$menuName] : 'folder';
	}

	// 子菜单图标映射
	function getSubMenuIcon($menuName) {
		$iconMap = [
			// 简历相关
			'简历列表管理' => 'list',
			'添加简历' => 'plus',
			'编辑简历' => 'edit',
			'添加简历文件' => 'upload',
			'简历审核' => 'check',

			// 消息相关
			'消息列表' => 'envelope',
			'回复消息' => 'reply',
			'消息设置' => 'cog',

			// 用户相关
			'用户列表' => 'user',
			'用户组' => 'users',
			'权限管理' => 'key',
			'角色管理' => 'user-circle',

			// 项目相关
			'项目列表' => 'tasks',
			'项目添加' => 'plus-circle',
			'项目编辑' => 'pencil',
			'项目统计' => 'pie-chart',

			// 系统相关
			'系统设置' => 'gear',
			'菜单管理' => 'sitemap',
			'日志管理' => 'file-text-o',
			'数据备份' => 'database',

			// 服务站相关
			'服务站列表' => 'building-o',
			'服务站添加' => 'plus-square',
			'服务站统计' => 'line-chart',

			// 财务相关
			'收支管理' => 'calculator',
			'账单管理' => 'file-o',
			'财务报表' => 'table',

			// 培训相关
			'培训课程' => 'book',
			'培训记录' => 'history',
			'证书管理' => 'certificate',
		];
		return isset($iconMap[$menuName]) ? $iconMap[$menuName] : 'circle-o';
	}
	?>

	<?php foreach ($main_menus[$cur_menu['boot_id']]['items'] as $k => $menu):  ?>
	<div class="menu-section">
		<div class="menu-header" data-toggle="collapse" data-target="#menu-<?= $k ?>" aria-expanded="true">
			<h4 class="menu-title">
				<span>
					<i class="menu-item-icon fa fa-<?= getMenuIcon($menu['name']) ?>"></i>
					<?= $menu['name'] ?>
				</span>
				<i class="menu-icon fa fa-chevron-down"></i>
			</h4>
		</div>
		<ul class="menu-items collapse in" id="menu-<?= $k ?>">
			<?php foreach ($menu['items'] as $link): ?>
			<li class="menu-item">
				<a class="menu-link<?php if ($cur_menu['parentid'] == $link['id'] || $cur_menu['id'] == $link['id']) echo ' active'; ?>" href="<?= $link['url'] ?>">
					<i class="menu-item-icon fa fa-<?= getSubMenuIcon($link['name']) ?>"></i>
					<?= $link['name'] ?>
				</a>
			</li>
			<?php endforeach; ?>
		</ul>
	</div>
	<?php endforeach; ?>

	<script>
		$(document).ready(function() {
			// 菜单折叠功能 - 只在点击标题区域时触发，避免影响链接
			$('.menu-header').click(function(e) {
				// 确保点击的不是链接
				if ($(e.target).closest('a').length === 0) {
					var target = $(this).data('target');
					var $target = $(target);
					var $icon = $(this).find('.menu-icon');

					if ($target.hasClass('in')) {
						$target.removeClass('in').addClass('collapsed');
						$(this).addClass('collapsed');
					} else {
						$target.addClass('in').removeClass('collapsed');
						$(this).removeClass('collapsed');
					}
				}
			});

			// 菜单项悬停效果 - 更温和的动画
			$('.menu-link').hover(
				function() {
					$(this).find('.menu-item-icon').addClass('animated pulse');
				},
				function() {
					$(this).find('.menu-item-icon').removeClass('animated pulse');
				}
			);

			// 菜单链接点击效果
			$('.menu-link').click(function(e) {
				// 添加点击动画
				$(this).addClass('clicking');
				setTimeout(() => {
					$(this).removeClass('clicking');
				}, 150);

				// 更新活跃状态
				$('.menu-link').removeClass('active');
				$(this).addClass('active');
			});

			// 阻止菜单头部点击事件冒泡到链接
			$('.menu-header').on('click', 'a', function(e) {
				e.stopPropagation();
			});
		});
	</script>

	<style>
		/* 添加点击动画效果 */
		.menu-link.clicking {
			transform: scale(0.98) translateX(2px);
			transition: all 0.1s ease;
		}

		/* 温和的脉冲动画 */
		.animated.pulse {
			animation: gentlePulse 1.5s ease-in-out infinite;
		}

		@keyframes gentlePulse {
			0% { transform: scale(1); }
			50% { transform: scale(1.05); }
			100% { transform: scale(1); }
		}

		/* 优化菜单头部的点击区域 */
		.menu-header {
			cursor: pointer;
			user-select: none;
		}

		.menu-header:hover {
			background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
		}

		/* 确保链接在菜单头部中正常工作 */
		.menu-header a {
			position: relative;
			z-index: 10;
			pointer-events: auto;
		}
	</style>
</div>