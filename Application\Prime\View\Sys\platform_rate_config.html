<include file="block/hat" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>平台服务费抽成比例管理</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            
            <div class="main">
                <!-- 当前费率信息 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <i class="fa fa-info-circle"></i> 当前费率信息
                        </h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="info-box">
                                    <h3 class="text-primary" id="currentRateDisplay">{$currentRate|default=0.30}</h3>
                                    <p class="text-muted">当前平台费率</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-box">
                                    <p><strong>最后修改时间：</strong></p>
                                    <p class="text-muted" id="lastModifyTime">
                                        <php>if($lastLog) {</php>
                                        {:date('Y-m-d H:i:s', $lastLog['execute_time'])}
                                        <php>} else {</php>
                                        暂无记录
                                        <php>}</php>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-box">
                                    <p><strong>修改人：</strong></p>
                                    <p class="text-muted" id="lastOperator">
                                        <php>if($lastLog) {</php>
                                        {$lastLog.operator_name}
                                        <php>} else {</php>
                                        --
                                        <php>}</php>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- 费率修改表单 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <i class="fa fa-edit"></i> 费率修改设置
                        </h4>
                    </div>
                    <div class="panel-body">
                        <form id="rateConfigForm" class="form-horizontal">
                            <!-- 新费率设置 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">新费率设置：</label>
                                <div class="col-sm-4">
                                    <div class="input-group">
                                        <input type="number" id="newRateInput" class="form-control" 
                                               placeholder="请输入0-100之间的数值" min="0" max="100" step="0.1"
                                               oninput="updateRatePreview()">
                                        <span class="input-group-addon">%</span>
                                    </div>
                                    <p class="help-block">
                                        <small class="text-muted">
                                            支持小数，如：30.5% 或直接输入 0.305
                                        </small>
                                    </p>
                                </div>
                                <div class="col-sm-6">
                                    <div class="well well-sm">
                                        <strong>小数格式：</strong> <span id="decimalFormat">0.30</span><br>
                                        <strong>百分比格式：</strong> <span id="percentFormat">30%</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 影响范围预览 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">影响范围预览：</label>
                                <div class="col-sm-10">
                                    <div class="well" id="impactPreview">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <p><strong>受影响价格配置：</strong></p>
                                                <p class="text-info"><span id="affectedCount">{$affectedCount|default=0}</span> 条</p>
                                            </div>
                                            <div class="col-md-4">
                                                <p><strong>预估佣金变化：</strong></p>
                                                <p class="text-success" id="commissionChange">请输入新费率</p>
                                            </div>
                                            <div class="col-md-4">
                                                <p><strong>预估执行时间：</strong></p>
                                                <p class="text-warning" id="estimatedTime">--</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 执行方式：仅支持立即执行 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">执行方式：</label>
                                <div class="col-sm-10">
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="executeType" value="1" checked>
                                            <i class="fa fa-flash"></i> 立即执行
                                        </label>
                                    </div>
                                    <p class="help-block">
                                        <small class="text-muted">费率修改将立即生效并执行批量价格重新计算</small>
                                    </p>
                                </div>
                            </div>

                            <!-- 通知选项 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">通知选项：</label>
                                <div class="col-sm-10">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" id="notifyUsers" checked>
                                            <i class="fa fa-bell"></i> 通知相关服务站用户（默认勾选）
                                        </label>
                                        <p class="help-block">
                                            <small class="text-muted">
                                                价格配置修改完成后，系统将自动推送通知给受影响的服务站用户
                                            </small>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <button type="button" class="btn btn-primary btn-lg" onclick="confirmRateUpdate()">
                                        <i class="fa fa-save"></i> 确认修改
                                    </button>
                                    <button type="button" class="btn btn-default btn-lg" onclick="resetForm()">
                                        <i class="fa fa-refresh"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 执行进度显示 -->
                <div class="panel panel-default" id="progressPanel" style="display: none;">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <i class="fa fa-spinner fa-spin"></i> 执行进度
                        </h4>
                    </div>
                    <div class="panel-body">
                        <div class="progress">
                            <div id="progressBar" class="progress-bar progress-bar-info progress-bar-striped active" 
                                 role="progressbar" style="width: 0%">
                                <span id="progressText">0%</span>
                            </div>
                        </div>
                        <div id="progressDetails">
                            <p id="progressMessage">准备执行...</p>
                            <div id="progressStats" style="display: none;">
                                <div class="row">
                                    <div class="col-md-4">
                                        <p><strong>已处理：</strong><span id="processedCount">0</span> 条</p>
                                    </div>
                                    <div class="col-md-4">
                                        <p><strong>成功：</strong><span id="successCount">0</span> 条</p>
                                    </div>
                                    <div class="col-md-4">
                                        <p><strong>失败：</strong><span id="errorCount">0</span> 条</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作历史 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <i class="fa fa-history"></i> 操作历史
                            <button type="button" class="btn btn-sm btn-default pull-right" onclick="refreshHistory()">
                                <i class="fa fa-refresh"></i> 刷新
                            </button>
                        </h4>
                    </div>
                    <div class="panel-body">
                        <div id="operationHistory">
                            <p class="text-muted">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />

<script>
// 全局变量
var currentLogId = 0;
var progressTimer = null;
var currentRate = {$currentRate|default=0.30};

$(document).ready(function() {
    // 初始化页面
    initializePage();

    // 加载操作历史
    loadOperationHistory();
});

// 初始化页面
function initializePage() {
    updateCurrentRateDisplay();
    updateRatePreview();
}

// 更新当前费率显示
function updateCurrentRateDisplay() {
    $('#currentRateDisplay').text((currentRate * 100).toFixed(1) + '%');
}

// 更新费率预览
function updateRatePreview() {
    var inputValue = $('#newRateInput').val();

    if (!inputValue || inputValue === '') {
        $('#decimalFormat').text('--');
        $('#percentFormat').text('--');
        $('#commissionChange').text('请输入新费率');
        $('#estimatedTime').text('--');
        return;
    }

    var newRate = parseFloat(inputValue);

    // 验证输入值
    if (isNaN(newRate) || newRate < 0 || newRate > 100) {
        $('#decimalFormat').text('无效输入');
        $('#percentFormat').text('无效输入');
        $('#commissionChange').text('请输入有效费率');
        $('#estimatedTime').text('--');
        return;
    }

    // 转换为小数格式
    var decimalRate = newRate > 1 ? newRate / 100 : newRate;

    // 更新格式显示
    $('#decimalFormat').text(decimalRate.toFixed(4));
    $('#percentFormat').text((decimalRate * 100).toFixed(1) + '%');

    // 如果费率有变化，获取影响预览
    if (Math.abs(decimalRate - currentRate) > 0.0001) {
        getImpactPreview(decimalRate);
    } else {
        $('#commissionChange').text('费率无变化');
        $('#estimatedTime').text('--');
    }
}

// 获取影响预览
function getImpactPreview(newRate) {
    $.ajax({
        url: '{:U("sys/preview_rate_change_impact")}',
        type: 'GET',
        data: { new_rate: newRate },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                var impact = response.impact;

                // 更新影响范围显示
                $('#affectedCount').text(impact.affected_count);

                // 更新佣金变化显示
                var commissionChange = impact.commission_change;
                var changeText = commissionChange >= 0 ?
                    '+¥' + Math.abs(commissionChange).toFixed(2) :
                    '-¥' + Math.abs(commissionChange).toFixed(2);
                var changeClass = commissionChange >= 0 ? 'text-success' : 'text-danger';
                $('#commissionChange').html('<span class="' + changeClass + '">' + changeText + '</span>');

                // 更新预估执行时间
                var estimatedSeconds = Math.ceil(impact.affected_count / 50);
                var estimatedTime = estimatedSeconds > 60 ?
                    Math.ceil(estimatedSeconds / 60) + '分钟' :
                    estimatedSeconds + '秒';
                $('#estimatedTime').text(estimatedTime);
            } else {
                $('#commissionChange').text('获取预览失败');
                $('#estimatedTime').text('--');
            }
        },
        error: function() {
            $('#commissionChange').text('网络错误');
            $('#estimatedTime').text('--');
        }
    });
}



// 确认费率修改
function confirmRateUpdate() {
    var inputValue = $('#newRateInput').val();

    if (!inputValue || inputValue === '') {
        alert('请输入新的费率值');
        return;
    }

    var newRate = parseFloat(inputValue);

    // 验证输入值
    if (isNaN(newRate) || newRate < 0 || newRate > 100) {
        alert('请输入0-100之间的有效费率值');
        return;
    }

    // 转换为小数格式
    var decimalRate = newRate > 1 ? newRate / 100 : newRate;

    // 检查费率是否有变化
    if (Math.abs(decimalRate - currentRate) < 0.0001) {
        alert('新费率与当前费率相同，无需修改');
        return;
    }

    var executeType = 1; // 固定为立即执行
    var notifyUsers = $('#notifyUsers').is(':checked') ? 1 : 0;
    var scheduledTime = 0;

    // 确认对话框
    var confirmMessage = '确认要将平台费率从 ' + (currentRate * 100).toFixed(1) + '% 修改为 ' + (decimalRate * 100).toFixed(1) + '% 吗？\n\n';
    confirmMessage += '执行方式：立即执行\n';
    confirmMessage += '通知用户：' + (notifyUsers ? '是' : '否') + '\n';
    confirmMessage += '受影响配置：' + $('#affectedCount').text() + ' 条';

    if (!confirm(confirmMessage)) {
        return;
    }

    // 提交费率修改
    submitRateUpdate(decimalRate, executeType, notifyUsers, scheduledTime);
}

// 提交费率修改
function submitRateUpdate(newRate, executeType, notifyUsers, scheduledTime) {
    // 禁用提交按钮
    $('button').prop('disabled', true);

    $.ajax({
        url: '{:U("sys/platform_rate_update")}',
        type: 'POST',
        data: {
            new_rate: newRate,
            execute_type: executeType,
            notify_users: notifyUsers,
            scheduled_time: scheduledTime
        },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                currentLogId = response.log_id;

                // 立即执行批量计算
                showProgressPanel();
                startBatchRecalculate(currentLogId);
            } else {
                alert('操作失败：' + response.msg);
                $('button').prop('disabled', false);
            }
        },
        error: function() {
            alert('网络错误，请重试');
            $('button').prop('disabled', false);
        }
    });
}

// 显示进度面板
function showProgressPanel() {
    $('#progressPanel').show();
    $('#progressBar').css('width', '0%');
    $('#progressText').text('0%');
    $('#progressMessage').text('正在执行批量计算...');
    $('#progressStats').hide();
}

// 开始批量重新计算
function startBatchRecalculate(logId) {
    $.ajax({
        url: '{:U("sys/batch_recalculate")}',
        type: 'POST',
        data: { log_id: logId },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                // 计算完成
                updateProgressBar(100, '计算完成');
                $('#progressMessage').text('批量计算完成');
                $('#progressStats').show();
                $('#processedCount').text(response.affected_count);
                $('#successCount').text(response.success_count);
                $('#errorCount').text(response.error_count);

                // 更新当前费率
                currentRate = parseFloat($('#newRateInput').val());
                if (currentRate > 1) currentRate = currentRate / 100;
                updateCurrentRateDisplay();

                // 重新加载页面状态
                setTimeout(function() {
                    location.reload();
                }, 3000);

                alert('批量计算完成！\n成功：' + response.success_count + ' 条\n失败：' + response.error_count + ' 条');
            } else {
                updateProgressBar(0, '计算失败');
                $('#progressMessage').text('计算失败：' + response.msg);
                alert('批量计算失败：' + response.msg);
                $('button').prop('disabled', false);
            }
        },
        error: function() {
            updateProgressBar(0, '网络错误');
            $('#progressMessage').text('网络错误');
            alert('网络错误，请重试');
            $('button').prop('disabled', false);
        }
    });
}

// 更新进度条
function updateProgressBar(progress, text) {
    $('#progressBar').css('width', progress + '%');
    $('#progressText').text(text || (progress + '%'));

    if (progress >= 100) {
        $('#progressBar').removeClass('progress-bar-info active')
                        .addClass('progress-bar-success');
    }
}



// 加载操作历史
function loadOperationHistory() {
    $.ajax({
        url: '{:U("sys/get_operation_history")}',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                displayOperationHistory(response.data);
            } else {
                $('#operationHistory').html('<p class="text-muted">暂无操作历史</p>');
            }
        },
        error: function() {
            $('#operationHistory').html('<p class="text-danger">加载失败</p>');
        }
    });
}

// 显示操作历史
function displayOperationHistory(historyData) {
    if (!historyData || historyData.length === 0) {
        $('#operationHistory').html('<p class="text-muted">暂无操作历史</p>');
        return;
    }

    var html = '<div class="table-responsive">' +
               '<table class="table table-striped table-hover">' +
               '<thead>' +
               '<tr>' +
               '<th>操作时间</th>' +
               '<th>操作人</th>' +
               '<th>费率变化</th>' +
               '<th>执行方式</th>' +
               '<th>执行状态</th>' +
               '<th>影响记录数</th>' +
               '<th>备注</th>' +
               '</tr>' +
               '</thead>' +
               '<tbody>';

    for (var i = 0; i < historyData.length; i++) {
        var item = historyData[i];
        var statusClass = getStatusClass(item.execute_status);

        html += '<tr>' +
                '<td>' + item.create_time_text + '</td>' +
                '<td>' + item.operator_name + '</td>' +
                '<td>' + item.old_rate_percent + ' → ' + item.new_rate_percent + '</td>' +
                '<td>' + item.execute_type_text + '</td>' +
                '<td><span class="label ' + statusClass + '">' + item.execute_status_text + '</span></td>' +
                '<td>' + (item.affected_count || '--') + '</td>' +
                '<td>' + (item.remark || '--') + '</td>' +
                '</tr>';
    }

    html += '</tbody></table></div>';

    $('#operationHistory').html(html);
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case 0: return 'label-warning';  // 待执行
        case 1: return 'label-info';     // 执行中
        case 2: return 'label-success';  // 成功
        case 3: return 'label-danger';   // 失败
        case 4: return 'label-default';  // 已取消
        default: return 'label-default';
    }
}

// 刷新历史记录
function refreshHistory() {
    loadOperationHistory();
}

// 重置表单
function resetForm() {
    $('#newRateInput').val('');
    $('#notifyUsers').prop('checked', true);
    updateRatePreview();
    $('button').prop('disabled', false);
}
</script>
