<?php
/**
 * 微信好友二维码模型
 */
namespace Common\Model;

use Think\Model;

class WechatQrcodeModel extends CommonModel
{
    // 权重等级
    public $weightLevels = [
        '1' => ['text' => '高', 'style' => 'danger'],   // 权重高，被选中概率大
        '2' => ['text' => '中', 'style' => 'warning'],  // 权重中
        '3' => ['text' => '低', 'style' => 'default'],  // 权重低，被选中概率小
    ];
    
    // 获取随机二维码（带权重）
    public function getRandomQrcode()
    {
        // 获取所有启用状态的二维码
        $qrcodes = $this->where(['status' => 1])->select();
        
        if (empty($qrcodes)) {
            return null;
        }
        
        // 根据权重分组
        $grouped = [];
        foreach ($qrcodes as $qrcode) {
            $weight = $qrcode['weight'] ?: 2; // 默认中等权重
            if (!isset($grouped[$weight])) {
                $grouped[$weight] = [];
            }
            $grouped[$weight][] = $qrcode;
        }
        
        // 计算权重反比例，权重越小概率越大
        $weights = [];
        $totalWeight = 0;
        
        // 权重反转：1->3, 2->2, 3->1，并计算总权重
        foreach ($grouped as $weight => $group) {
            $reverseWeight = 4 - $weight; // 反转权重（1->3, 2->2, 3->1）
            $groupWeight = $reverseWeight * count($group);
            $weights[$weight] = $groupWeight;
            $totalWeight += $groupWeight;
        }
        
        // 随机选择权重组
        $randomValue = mt_rand(1, $totalWeight);
        $currentWeight = 0;
        $selectedWeight = null;
        
        foreach ($weights as $weight => $value) {
            $currentWeight += $value;
            if ($randomValue <= $currentWeight) {
                $selectedWeight = $weight;
                break;
            }
        }
        
        // 在选中的权重组中随机选择一个二维码
        $selectedGroup = $grouped[$selectedWeight];
        $randomIndex = mt_rand(0, count($selectedGroup) - 1);
        
        return $selectedGroup[$randomIndex];
    }
    
    // 获取指定二维码
    public function getQrcode($id)
    {
        return $this->where(['id' => $id, 'status' => 1])->find();
    }
    
    // 更新或添加二维码
    public function saveQrcode($data)
    {
        if (isset($data['id']) && $data['id']) {
            $data['update_time'] = time();
            return $this->save($data);
        } else {
            $data['create_time'] = $data['update_time'] = time();
            return $this->add($data);
        }
    }
} 