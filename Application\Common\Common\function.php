<?php

/**
 * 检查权限
 * @param name string|array  需要验证的规则列表,支持逗号分隔的权限规则或索引数组
 * @param uid  int           认证用户的id
 * @param relation string    如果为 'or' 表示满足任一条规则即通过验证;如果为 'and'则表示需满足所有规则才能通过验证
 * @return boolean           通过验证返回true;失败返回false
 */
function authCheck($uid, $name = null, $relation = 'or') {
	if(empty($uid)) return false;
	$iauth = new \Common\Lib\iAuth();
	if(empty($name)) {
		$name = strtolower(MODULE_NAME."/".CONTROLLER_NAME."/".ACTION_NAME);
	}
	return $iauth->check($uid, $name, $relation);
}

function comparePwd($pwd_input, $pwd, $salt = '') {
    return $pwd == makePwd($pwd_input, $salt);
}

function makeSalt() {
    return substr(uniqid(rand()), 0, 6);
}

function _ip2long($ip) {
    return bindec(decbin(ip2long($ip)));
}

/**
 * 密码串算法
 */
function makePwd($password, $salt) {
    if (empty($password)) return '';
    return md5(md5($password) . $salt);
}

/*
 * 过滤指定字符
 */
function stripChars($str, $chars = '?<*.>\'\"') {
    return preg_replace('/['.$chars.']/is', '', $str);
}

function isMobile($mobile) {
    return preg_match("/^1(3|4|5|7|8)\d{9}$/", $mobile);
}

function isQQ($val) {
    return preg_match("/^\d{5,11}$/", $val);
}

function isWord($value) {
    if (preg_match('/[^\w\x{4e00}-\x{9fa5}]/u', $value)) {
        return false;
    }
    return true;
}

/**
 * 图片文件命名规则
 */
function md5Fname($md5_code = null) {
    if (!$md5_code)
        return false;
    return substr(md5($md5_code), 8, 16);
}

/**
 * 获取md5图片路径
 * @param string $filename 图片md5文件名
 * @param int $size 图片尺寸
 * @param bool $noimg 图片不存在时默认noimg
 */
function imgPath($filename, $size = 0, $noimg = true)
{
    if($filename == '') {
        if($noimg) {
            return __ROOT__.'/static/images/nopic.jpg';
        }
        return '';
    }
    if ($size > 0) {
        list($md5, $ext) = explode('.', $filename);
        $filename = $md5.'_'.$size.'.'.$ext;
    }
    if (strpos($filename, '/data/') !== false) {
        $file = $filename;
    } else {
        $file = C("UPLOADPATH").'/'.$filename;
    }
    if (! is_file(SITE_PATH.$file)) {
        if ($noimg) {
            return __ROOT__.'/static/images/nopic.jpg';
        } else {
            return '';
        }
    }
    return __ROOT__.$file;
}

/**
 * 以字段 $field 的值为键,聚焦处理
 */
function focus($arr, $field = 'id') {
    if (!$field) {
        return $arr;
    }
    foreach ($arr as $val) {
        $ret[$val[$field]] = $val;
    }
    return $ret;
}

/**
 * 是否微信浏览器
 * return bool
 */
function isWX() {
    return strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false;
}

/**
 * 检测是否为开发者模式
 * @return bool
 */
function isDeveloperMode() {
    // 获取配置的开发者密钥
    $validKey = C('DEVELOPER_MODE_KEY');
    if (empty($validKey)) {
        return false;
    }

    // 检查开发者模式Cookie
    $cookieName = 'dev_mode_' . md5($validKey);
    if (empty($_COOKIE[$cookieName])) {
        return false;
    }

    // 生产环境额外安全检查
    $host = $_SERVER['HTTP_HOST'] ?? '';
    $productionHosts = [
        'we.zhongcaiguoke.com',
        'station.zhongcaiguoke.com',
        'zhongcaiguoke.com',
        'www.zhongcaiguoke.com'
    ];

    if (in_array($host, $productionHosts)) {
        // 生产环境需要IP白名单验证
        $allowedIPs = C('DEVELOPER_MODE_ALLOWED_IPS');
        if (!empty($allowedIPs)) {
            $clientIP = get_client_ip();
            if (!in_array($clientIP, $allowedIPs)) {
                return false;
            }
        } else {
            // 没有配置IP白名单则禁用
            return false;
        }

        // 生产环境需要时间限制验证
        $timeLimit = C('DEVELOPER_MODE_TIME_LIMIT');
        if (!empty($timeLimit)) {
            $currentHour = (int)date('H');
            if ($currentHour < $timeLimit['start'] || $currentHour > $timeLimit['end']) {
                return false;
            }
        }
    }

    return true;
}

/**
 * 检测是否应该跳过微信检测
 * 包括本地调试模式和开发者模式
 * @return bool
 */
function shouldSkipWechatCheck() {
    // 本地调试模式
    if (C('LOCAL_DEBUG')) {
        return true;
    }

    // 开发者模式
    if (isDeveloperMode()) {
        return true;
    }

    return false;
}

/**
 * 日志快捷调用
 */
function dolog($path, $data, $title = "", $logtime = true, $sep = "\t")
{
	if (!$path || !$data) return false;

    import('Util.Simplelog');
    $log = new Simplelog();

	$log->setPath($path);
	if ($title) {
		$log->setTitle($title);
	}
	if ($logtime === false) {
		$log->setNoTime($logtime);
	}
	$log->setData($data);
	$flag = $log->write();
	return $flag;
}

// from discuz
function authCode($string, $operation = 'DECODE', $fix = 1, $key = '', $expiry = 0)
{
    $ckey_length = 4;
    $key = md5($key . C("AUTH_KEY"));
    $keya = md5(substr($key, 0, 16));
    $keyb = md5(substr($key, 16, 16));
    $tstr = $fix ? $string : microtime();
    $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length): substr(md5($tstr), -$ckey_length)) : '';

    $cryptkey = $keya.md5($keya.$keyc);
    $key_length = strlen($cryptkey);

    $string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry : 0).substr(md5($string.$keyb), 0, 16).$string;
    $string_length = strlen($string);

    $result = '';
    $box = range(0, 255);

    $rndkey = array();
    for($i = 0; $i <= 255; $i++) {
        $rndkey[$i] = ord($cryptkey[$i % $key_length]);
    }

    for($j = $i = 0; $i < 256; $i++) {
        $j = ($j + $box[$i] + $rndkey[$i]) % 256;
        $tmp = $box[$i];
        $box[$i] = $box[$j];
        $box[$j] = $tmp;
    }

    for($a = $j = $i = 0; $i < $string_length; $i++) {
        $a = ($a + 1) % 256;
        $j = ($j + $box[$a]) % 256;
        $tmp = $box[$a];
        $box[$a] = $box[$j];
        $box[$j] = $tmp;
        $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
    }

    if($operation == 'DECODE') {
        if((substr($result, 0, 10) == 0 || substr($result, 0, 10) > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26).$keyb), 0, 16)) {
            return substr($result, 26);
        } else {
            return '';
        }
    } else {
        return $keyc.str_replace('=', '', base64_encode($result));
    }
}

/**
 * 对数字ID进行编码（60进制编码）,0-9/a-z/A-Z 除去O/o以外共60个字符参与编码，顺序是预置好的
 *
 * @param <int> $id 待编码的数字ID
 * @param <int> $length 补齐长度返回，0为不补齐
 * @return <string>
 */
function numIdEncode($id, $length = 0)
{
    $a = array(0 => 'v', 1 => 'C', 2 => 'p', 3 => 'F', 4 => 'B', 5 => 'S', 6 => '3', 7 => 'q', 8 => 'J', 9 => 'P', 10 => 'g', 11 => '9', 12 => 'T', 13 => 'h', 14 => 'x', 15 => '7', 16 => 's', 17 => 'u', 18 => 'D', 19 => '5', 20 => 'N', 21 => 'R', 22 => 'K', 23 => 'j', 24 => 'z', 25 => 'L', 26 => 'l', 27 => '4', 28 => 'n', 29 => 'm', 30 => 'y', 31 => 'c', 32 => 'G', 33 => 'M', 34 => 'H', 35 => 'e', 36 => 'a', 37 => 'i', 38 => 'k', 39 => 'Q', 40 => 'b', 41 => '8', 42 => 'X', 43 => '1', 44 => 'A', 45 => 'E', 46 => 'U', 47 => 'w', 48 => '0', 49 => 'f', 50 => '6', 51 => 'Z', 52 => '2', 53 => 'Y', 54 => 'd', 55 => 'I', 56 => 't', 57 => 'V', 58 => 'W', 59 => 'r');
    $ret = '';
    while($id) {
        $key = $id % 60;
        $id = (int) ($id / 60);
        $ret = $a[$key] . $ret;
        $length -= 1;
    }
    if($length > 0) $ret = str_repeat($a[0], $length) . $ret;
    return $ret;
}

/**
 * 对编码（60进制编码）的数字ID进行解码
 *
 * @param <string> $str 已编码的ID
 * @return <int>
 */
function numIdDecode($str)
{
    $a = array('v' => 0, 'C' => 1, 'p' => 2, 'F' => 3, 'B' => 4, 'S' => 5, 3 => 6, 'q' => 7, 'J' => 8, 'P' => 9, 'g' => 10, 9 => 11, 'T' => 12, 'h' => 13, 'x' => 14, 7 => 15, 's' => 16, 'u' => 17, 'D' => 18, 5 => 19, 'N' => 20, 'R' => 21, 'K' => 22, 'j' => 23, 'z' => 24, 'L' => 25, 'l' => 26, 4 => 27, 'n' => 28, 'm' => 29, 'y' => 30, 'c' => 31, 'G' => 32, 'M' => 33, 'H' => 34, 'e' => 35, 'a' => 36, 'i' => 37, 'k' => 38, 'Q' => 39, 'b' => 40, 8 => 41, 'X' => 42, 1 => 43, 'A' => 44, 'E' => 45, 'U' => 46, 'w' => 47, 0 => 48, 'f' => 49, 6 => 50, 'Z' => 51, 2 => 52, 'Y' => 53, 'd' => 54, 'I' => 55, 't' => 56, 'V' => 57, 'W' => 58, 'r' => 59);
    $chars = str_split($str);
    $ret = 0;
    $weight = 1;
    while($chars) {
        $key = array_pop($chars);
        //存在非编码字符则直接返回false
        if(!isset($a[$key])) return false;
        $ret += $a[$key] * $weight;
        $weight *= 60;
    }
    return $ret;
}


/**
 * @param $_file $_FILES
 * @return stirng
 */
function uploadImg($_file,$thumb_info = null,$type=1)
{
    $config = [ 'rootPath' => SITE_PATH.C("UPLOADPATH").'/',
        'maxSize' => 2*1024*1024,
        'replace' => true,
        'exts' => ['jpg', 'gif', 'png', 'jpeg'],
    ];
    empty($thumb_info) && $thumb_info = C('THUMB_SIZE');
    $upload = new \Think\Uploady($config);
    $info = $upload->uploadOne($_file);
    if (!$info) {
        $errmsg = $upload->error;
        return ['err'=>$errmsg];
    } else {
        foreach($thumb_info as $v) {
            $thumb_name = substr($info['savename'],0,16).'_'.$v['width'].'.'.$info['ext'];
            $save_path = SITE_PATH.C("UPLOADPATH").'/'.$info['savepath'];
            $source_img = $save_path.$info['savename'];
            thumb($source_img,$v['width'],$v['height'],$thumb_name,$save_path,$type);
        }
        return $info['savepath'].$info['savename'];
    }
}

/**
 * @param $source_img string 原图片路径
 * @param $width int 缩略图宽度
 * @param $height int 缩略图高度
 * @param $save_name string 缩略图保存名
 * @param $save_path string 缩略图保存路径
 */
function thumb($source_img, $width, $height, $save_name, $save_path, $type=1)
{
    $thumb = new \Think\Image();

    $thumb->open($source_img);

    $thumb->thumb($width,$height,$type);
    if(!file_exists($save_path)) {
        mkdir($save_path);
    }
    $thumb->save($save_path.$save_name);
}

/**
 * 获取排序的url
 * @param string $field
 * @return string
 */
function getSortUrl($field)
{
    $sort_flag = C('VAR_SORT');
    //$sort = I('get.'.$sort_flag, $field.'_asc');
    //$sort = ($sort == $field.'_asc') ? $field.'_desc' : $field.'_asc';
    $sort = $field.'_desc';
    $arr = [$sort_flag => $sort];
    $plink = $_SERVER["REDIRECT_URL"];
    foreach ($_GET as $key => $val) {
        if (!in_array($key, ['p', $sort_flag])) $arg[$key] = $val;
    }
    if (! empty($arg)) {
        $arr = array_merge($arr, $arg);
    }
    $url = empty($arr) ? $plink : $plink.'?'.http_build_query($arr);
    return $url;
}

function sortParam($field, $order)
{
    $sort_filed = $field;
    $sort_order = $order;
    if(preg_match('/^(\w*)_(asc|desc)$/i', I('get.'.C('VAR_SORT')), $items)) {
        $sort_filed = $items[1];
        $sort_order = $items[2];
    }
    return ['field' => $sort_filed, 'order' => $sort_order];
}

/**
 * 根据指定的get参数重建url
 * @param mixed  $key
 * @param string $value
 * @return string
 */
function rebulid_querystr($key, $value)
{
    $query = I('get.');
    if (is_array($key)) {
        foreach ($key as $k => $v) {
            if ($v === false) {
                unset($query[$k]);
            } else {
                $query[$k] = $v;
            }
        }
    } else {
        if ($value === false) {
            unset($query[$key]);
        } else {
            $query[$key] = $value;
        }
    }
   
    $segment = strtolower(MODULE_NAME . '/' . CONTROLLER_NAME . '/' . ACTION_NAME.'/');
    return U($segment).'?'.  http_build_query($query);
}

/**
 * 清除指定目录下的文件
 * @param string $dir 目录路径
 * @return void
 */
function clearDir($dir,$is_del_dir = false)
{
	if(!file_exists($dir)) return;
	$directory = dir($dir);
	while($entry = $directory->read()) {
		if($entry != '.' && $entry != '..') {
			$filename = $dir.'/'.$entry;
            if(is_dir($filename)) {
                clearDir($filename,$is_del_dir);
            } elseif (is_file($filename)) {
                @unlink($filename);
            }
		}
	}

	$directory->close();
	if($is_del_dir) @rmdir($dir);
}

/**
 * 获取微信jsapi_ticket
 */
function jsapiTicket($conf = null)
{
    $ticket = "";
    do {
        vendor('LaneWeChat.lanewechat');
        if ($conf) \LaneWeChat\Core\Base::init($conf);
        $ticket_path = SITE_PATH.'/data/jsapi_ticket_'.\LaneWeChat\Core\Base::$APPID;
        $ticket = file_get_contents($ticket_path);
        
        $ticket = json_decode($ticket, 1);
        if (!empty($ticket) && ($ticket['created'] + $ticket['expires_in'] > time())) break;
        $ticket = \LaneWeChat\Core\WeChatOAuth::getJsapiTicket();
    } while(false);
    return $ticket;
}

/**
 * 获取微信jsconfig数据
 */
function getWxConfig($url = null, $conf = null)
{
    if (empty($url)) $url = __HOST__.$_SERVER['REQUEST_URI'];
    $time = time();
    $ticket = jsapiTicket($conf);
    if (empty($ticket)) return false;
    $noncestr = createNoncestr();
    $tic = $ticket['ticket'];
    $str = sprintf("jsapi_ticket=%s&noncestr=%s&timestamp=%s&url=%s", $tic, $noncestr , $time, $url);
    $cstr = sha1($str);
    return ['timestamp'=>$time,'noncestr'=>$noncestr,'ticket'=>$tic,'signature'=>$cstr];
}

/**
 * cvs简单输出
 */
function csvOutput($title, $data, $filename)
{
    $content = '"' . implode('","', $title) . "\"\r\n";
    $keys = array_keys($title);
    foreach($data as $arr) {
        $_arr = [];
        foreach($keys as $key)
            if(isset($arr[$key])) $_arr[] = $arr[$key];
        $content .= '"' . implode('","', $_arr) . "\"\r\n";
    }
    $content = mb_convert_encoding(trim($content), 'gbk', 'utf-8');
    header('Last-Modified: '.date('D, d M Y H:i:s').' GMT');
    header('Cache-control: no-cache');
    header('Content-Encoding: none');
    header('Content-Disposition: attachment; filename="'.$filename.'_'.date('Ymdhis').'.csv"');
    header('Content-type: csv');
    header('Content-Length: '.strlen($content));
    echo $content;
    exit;
}

/**
 * 	作用：产生随机字符串，不长于32位
 */
function createNoncestr($length = 32) 
{
    $chars = "abcdefghijklmnopqrstuvwxyz0123456789";  
    $str ="";
    for ( $i = 0; $i < $length; $i++ )  {  
        $str.= substr($chars, mt_rand(0, strlen($chars)-1), 1);  
    }  
    return $str;
} 

// csv文件解析成数组
function inputCsv($file)
{
    $handle = fopen($file, 'r'); 
    $out = [];
    $n = 0;
    while ($data = fgetcsv($handle, 10000)) {
        $num = count($data);
        for ($i = 0; $i < $num; $i++) {
            $out[$n][$i] = mb_convert_encoding($data[$i], 'utf-8', 'gbk');
        }
        $n++;
    }
    return $out;
}


/*
 * 获取客户IP归属地 淘宝接口
 */
function tb2region($ip)
{
    if (is_numeric($ip)) $ip = long2ip($ip);
    if (!$ip) $ip = get_client_ip();
    $url = "http://ip.taobao.com/service/getIpInfo.php?ip=".$ip;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 2);
    $output = curl_exec($ch);
    $json = json_decode($output, true);
    // 失败或非国内地址，统一返回false
    if($json['code'] == 1 || $json['data']['country'] != '中国') return false;
    return $json['data'];
}

/*
 * 写入日志
 * */
function dblog($obj_id,$remark, $path = '', $admin_id = 0) {
    $data = [
        'remark' => $remark,
        'path1' => MODULE_NAME,
        'path2' => CONTROLLER_NAME,
        'path3' => ACTION_NAME,
        'obj_id' => $obj_id,
        'path4' => '',
    ];
    if(!empty($path)) {

        $strArray  = explode('/',$path);
        $n = 1;
        foreach($strArray as $val) {
            if(!empty($val)){
                $data['path'.$n] = strtolower($val);
            }
            $n++;
        }
    }
    $obj = D('Log');
    $data['admin_id'] = $admin_id ? $admin_id : session('admin_id');
    if(!$obj->create($data)) {
        $this->error($obj->getError());
    }
    return $obj->add() ? true : false;
}


/**
 * 获取微信代理授权uri
 */
function proxy_uri($dm = '')
{
    $dm = $dm ? : __HOST__;
    return 'http://'.$dm.'/pub/util/proxyauth';
}

function noCodeJump()
{
    $strs = __HOST__.$_SERVER['REQUEST_URI'];
    $str = preg_replace('/code=\w+/', '', $strs);
    if ($str != $strs) $goto = 1;
    $str = str_replace(['?&', '&&'], ['?', '&'], $str);
    $str = preg_replace('/(\?|&)$/', '', $str);
    if ($goto) redirect($str);
}