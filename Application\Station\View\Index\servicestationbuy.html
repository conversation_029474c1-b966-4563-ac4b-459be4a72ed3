﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <title>服务站·资源包说明</title>
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css?v={:time()}" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <style>
        :root {
            --primary-color: #00bf80;
            --secondary-color: #666;
            --card-bg: #fff;
            --border-color: #eee;
        }
 
        .container {
            margin: 12px;
        }

        /* 卡片式布局优化 */
        .price-card, .guide-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            overflow-x: auto;
            line-height: 25px;
        }

        /* 表格移动端优化 */
        .price-table, .data-table {
            width: 100%;
            min-width: 560px; /* 保证最小可读宽度 */
            border-collapse: collapse;
        }

        .price-table th, .data-table th {
            background: rgba(0,191,128,0.1);
            color: var(--primary-color);
            padding: 12px 8px;
            font-size: 15px;
            white-space: nowrap;
        }

        .price-table td, .data-table td {
            padding: 10px 8px;
            font-size: 15px;
            border-bottom: 1px solid var(--border-color);
            color: var(--secondary-color);
        }

        /* 标题优化 */
        .section-title {
            color: var(--primary-color);
            font-size: 18px;
            margin: 16px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid;
        }

        /* 列表优化 */
        .notice-list {
            padding-left:0;
            margin: 12px 0;
        }

        .notice-list li {
            margin-bottom: 8px;
            font-size: 15px;
        }
      /* 引用样式 */
      blockquote {
            background: rgba(0,191,128,0.05);
            border-left: 4px solid var(--primary-color);
            padding: 16px;
            margin: 16px 0;
            border-radius: 4px;
        }
 
        /* 移动端专属优化 */
        @media (max-width: 480px) {
     
            
            .price-table th, 
            .data-table th,
            .price-table td,
            .data-table td {
                padding: 8px 6px;
                font-size: 15px;
            }
            
            .section-title {
                font-size: 16px;
            }
            
            blockquote {
                font-size: 14px;
            }
        }

        /* 横向滚动提示 */
        .table-scroll-hint {
            color: var(--secondary-color);
            font-size: 15px;
            text-align: center;
            padding: 8px 0;
            display: none;
        }

        @media (max-width: 768px) {
            .table-scroll-hint {
                display: block;
            }
        }

/* 返回箭头样式 */
.back-arrow {
    position: fixed;
    top: 115px;
    left: 15px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.2s;
}

.back-arrow::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    width: 12px;
    height: 12px;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: translateY(-50%) rotate(45deg);
}

/* 点击反馈效果 */
.back-arrow:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.7);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .back-arrow {
        top: 163px;
        left: 10px;
        width: 36px;
        height: 36px;
    }
    
    .back-arrow::before {
        left: 13px;
        width: 10px;
        height: 10px;
    }
}

    /* 金额区域升级版 */
    .amount-section {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            text-align: center;
        }
        .amount-group {
            margin-bottom: 16px;
        }
        .total-amount {
            font-size: 36px;
            color: var(--primary-color);
            font-weight: 600;
            line-height: 1.3;
        }
        .available-amount {
            font-size: 16px;
            color: #999696;
            margin-top: 4px;
        }
        .time-tip {
            color: var(--secondary-color);
            font-size: 14px;
            margin: 12px 0 24px;
        }

    </style>
</head>
<body>
<!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
    <include file="headers"/>
 
    <div class="container">
        <div class="amount-section">
            <div class="amount-group">
                <H1>可用服务站资源包数量</H1>
                <div class="total-amount" style="margin: 12px;">{:$serviceStationRow['open_num']}</div>
            </div>
            <div style="font-size: 14px;color: #000;">服务站技术服务采购包(下称“服务站资源包”)</div>
            <div class="time-tip">一个服务站资源包对应一个服务站授权资格</div>
 
        </div>
    </div>
    <div class="container">
        <!-- 初始费用表格 -->
        <div class="price-card">
            <h2 class="section-title">服务站开通费用明细（签约后3日内支付）
                <div style="font-size: 14px;color: #000;">服务站资源包所含项目如下：</div>
            </h2>
            
          </div>
        </div>

        <div class="container">
            
            <div class="price-card">

            <table class="price-table">
                <thead>
                    <tr>
                        <th>项目</th>
                        <th>金额（元）</th>
                        <th>续签/年</th>
                        <th>费用说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>加盟费</td>
                        <td>0</td>
                        <td>-</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td>宣传册材料费</td>
                        <td>1,600</td>
                        <td>-</td>
                        <td>公司宣传资料</td>
                    </tr>
                    <tr>
                        <td>公告栏等材料费</td>
                        <td>300</td>
                        <td>-</td>
                        <td>公告栏/易拉宝展架等材料</td>
                    </tr>
                    <tr>
                        <td>设计版权费</td>
                        <td>500</td>
                        <td>-</td>
                        <td>设计版权使用费</td>
                    </tr>
                    <tr>
                        <td>平台使用年费</td>
                        <td>2,400</td>
                        <td>2,400</td>
                        <td>平台使用费</td>
                    </tr>
                    <tr>
                        <td>云助理服务年费</td>
                        <td>24,000</td>
                        <td>7,000</td>
                        <td>助理客服服务费</td>
                    </tr>
                    <tr>
                        <td>培训服务费</td>
                        <td>3,000</td>
                        <td>-</td>
                        <td>标准化运营培训费</td>
                    </tr>
                    <tr>
                        <td>门店指导服务费</td>
                        <td>8,000</td>
                        <td>-</td>
                        <td>门店选店开立指导</td>
                    </tr>
                    <tr>
                        <td>管理费</td>
                        <td>5,000</td>
                        <td>5,000</td>
                        <td>合作事务行为管理费</td>
                    </tr>
                    <tr>
                        <td>宣传费</td>
                        <td>2,000</td>
                        <td>2,000</td>
                        <td>官网、线上线下宣传费</td>
                    </tr>
                    <tr class="highlight">
                        <td>费用总计</td>
                        <td>46,800</td>
                        <td>16,400</td>
                        <td>以上费用合计</td>
                    </tr>
                </tbody>
            </table>
                
              <!-- 新增横向滚动提示 -->
              <div class="table-scroll-hint" style="color: red;">← 左右滑动查看完整表格 →</div>
        </div>
  
 
    
            <!-- 开通说明 -->
            <div class="guide-card">
                <h2 class="section-title" style="text-align: center;">服务站"熟带熟"模式运营规范</h2>  
                <p style="color: red;font-size: 14px;font-weight: bold;">
                   <span style="color: #000;">· </span>本模式采用封闭式邀请加盟机制，服务站仅可通过系统内邀请渠道完成加盟准入。无推荐人不能加盟！
                </p>
                <p style="color: red;font-size: 14px;font-weight: bold;padding:12px 0;">
                    <span style="color: #000;">· </span>“熟带熟”私域服务模式要求推荐人义务完成对被邀方关于公司、平台、业务及模式培训讲解，在被邀方充分了解基础上才能加盟成为服务站。
            </p>
            <HR>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 14px;font-weight: bold;">【层级关系】</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 6px;">所有服务站均为平行合作关系，无上下级隶属关系。</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 10px;font-weight: bold;">【邀请机制】</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 6px;">平台设立邀请备案机制用于：</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 6px;">(1) 记录邀请方在"熟带熟"私域服务模式中的推荐作用；</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 6px;">(2) 确认服务关系中的介绍人角色；</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 6px;">(3) 作为服务奖励发放的对应依据。</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 10px;font-weight: bold;">【合作规范】</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 6px;">服务站均需与中才国科直接签订合作协议，完成官方资质认证，后期将在官方渠道必要位置展示服务站信息以方便求职者联系。</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 10px;font-weight: bold;">【收益规则】</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 6px;">全平台执行统一收益核算标准，邀请方不参与被邀请方基础收益分配，确保收益独立性原则。</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 10px;font-weight: bold;">【特别声明】</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 6px;">(1) 无层级，所有服务站为平行合作关系；</p>
                <p style="color: rgb(20, 2, 2);font-size: 14px;padding-top: 6px;">(2) 推荐人无推荐提成奖励、无介绍费分成。</p>
            </div>
        </div>
        <div class="container">
            
            <div class="price-card">
                <h2 class="section-title">服务站开通方式</h2>
                <ol class="notice-list">
                    <li>1. 联系推荐人使用资源包开通，自由交易，续签16400/年；</li>
                    <li>2. 直接汇款至公司账户开通，16800元/首年，续签16400/年；</li>
                    <li>3. 使用资源包开通方式推荐人可自行向被邀方收款，并为其开具收款发票。《服务站合作协议》费用细则中标准收费为46800/首年；</li>
                    <li>4. 无论何种方式开通服务站，服务站均与平台直签《服务站合作协议》。续签与邀请人无关，直接汇款至平台公司账户。</li>
                </ol>
            </div>
        </div>
        <div class="container">
            
            <div class="price-card">
                <h3 class="section-title">服务站资源包批发说明</h3>
                <p style="font-size:14px;color: green;">1、联系推荐人批发。双方自由交易；</p>
                <p style="font-size:14px;color: green;">2、直接转账公司批发。批发信息如下表所示。</p>
                <p style="font-size:14px;padding-top: 8px;padding-bottom: 12px;">
                    <B style="font-size: 15px;">公司回购推荐方资源包说明：</B><BR>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;当推荐方服务站资源包数量 >= 被推荐服务站本次批发数量，平台按被推荐服务站批发数量及价格回购推荐方服务站对应资源包数量。举个例子：如推荐方服务站剩余可有资源包数量是10个，本次被推荐服务站批发5个服务站资源包，按照下表所示5个是13000元/个，总价为65000元，因推荐方服务站剩余可用资源包 > 被推荐服务站本次批发数量，当被推荐服务站转账65000元至平台公司账户后，平台会按照13000元/个回购5个推荐方服务站资源包，即转账65000元给推荐方服务站。<BR>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>注意，</B>当推荐方服务站可用资源包数量 < 被推荐服务站本次批发数量 或 被推荐服务站本次批发 >= 10个时则不适用回购条件。</p>
                            <HR style="padding: 3px;">
                <p style="font-size:13px;padding-top: 12px;"><B style="font-size: 15px;">服务站级别</B>是根据推荐服务站数量+批发的资源包数量累加计算。比如，推荐了2个服务站，之后批发了5个服务包资源，那么总数量就是7个。属于V2级别，当后续累加 >= 10个时，就变成V3级别。级别只升不降。</p>
                <p style="font-size:14px;color: green;padding-top: 6px;">批发资源包后才有使用资源包为他人开通服务站的权限，之前推荐的服务站不能使用后批发的资源包抵扣。</p>
            </div>
        </div>
        <div class="container">

            <div class="price-card">
                <div style="text-align: center;margin: 12px 0;">以下为每次批发数量及对应价目表</div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>数量</th>
                            <th>单价/元</th>
                            <th>总价/元</th>
                            <th>级别</th>
                            <th>公益补贴</th>
                            <th>服务补贴</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2</td>
                            <td>16,800</td>
                            <td>33,600</td>
                            <td>普通</td>
                            <td>无</td>
                            <td>无</td>
                        </tr>
                        <tr class="highlight-row">
                            <td>3</td>
                            <td>15,000</td>
                            <td>45,000</td>
                            <td>V1级别</td>
                            <td>10%</td>
                            <td>2%</td>
                        </tr>
                        <tr class="highlight-row">
                            <td>5</td>
                            <td>13,000</td>
                            <td>65,000</td>
                            <td>V2级别</td>
                            <td>20%</td>
                            <td>3%</td>
                        </tr>
                        <tr class="highlight-row">
                            <td>10</td>
                            <td>10,000</td>
                            <td>100,000</td>
                            <td>V3级别</td>
                            <td>30%</td>
                            <td>5%</td>
                        </tr>
                    </tbody>
                </table>
                                
              <!-- 新增横向滚动提示 -->
              <div class="table-scroll-hint" style="color: red;">← 左右滑动查看完整表格 →</div>

            </div>
            <div class="guide-card">
                <blockquote>
                    <strong>公益补贴：</strong>是指您邀请的服务站参与新能源或其他公益实习获得政策补贴返费后，平台额外给予您的补贴奖励，不影响被邀方基础收益。
                    <p style="color:#00bf80;padding-top: 8px;">举个例子，您推荐的所有服务站有1000人参与公益实习并领取工资，平台在获得国家政策补贴之后，您推荐的服务站总获得1000*100=100,000元奖励，您作为推荐人，按照您的服务站级别对应获得平台额外给予补贴奖励为（V1级别：10,000元；V2级别：20,000元；V3级别：30,000元）。</p>
                </blockquote>
                <blockquote>
                    <strong>服务补贴：</strong>是指平台额外给予您的多种形式服务奖励，不影响被邀方基础收益。
                </blockquote>
              <ol class="notice-list">
                <li><B style="color:red;">所有补贴奖励均属于平台额外奖励，不影响被邀方基础收益。所有服务站都是平行合作关系。</B>推荐方在推荐服务站过程中做了“熟带熟”相应的介绍解释和培训、协助服务工作，<U>公益补贴和服务补贴属于平台额外给予推荐方的服务补贴奖励</U>，所有奖励均以余额形式显示在服务站管理中心。</li>
              </ol>
            </div>
               <!-- 平台提现说明 -->
               <div class="guide-card">
                <h2 class="section-title">平台提现说明</h2>
                <ol class="notice-list">
                    <li>公益实习返费、招生培训课程奖励及其他形式的奖励补贴均通过平台发放。</li>
                    <li>具体流程为：<BR>
                        1、服务站提交余额提现申请；<BR>
                        2、财务审核；<BR>
                        3、服务站开票；<BR>
                        4、平台对公付款给服务站。</li>
                </ol>
            </div>
            <!-- 特别说明 -->
            <div class="guide-card">
                <h2 class="section-title">特别说明</h2>
                <ol class="notice-list">
                    <li>· 推荐仅为服务站加盟固定机制！无上下级之分，所有服务站均为平行合作关系；</li>
                    <li>· 所有服务站与平台直签《服务站合作协议》； </li>
                    <li>· 选择直接汇款到平台公司账户开通方式会在服务站合作协议相应位置写明（本次开通收取X元）；</li>
                    <li>· 通过资源包开通方式会在服务站合作协议相应位置写明（本次使用服务站资源包开通）。</li>
                </li>
                </ol>
    
                <blockquote>
                    <strong>发票说明：</strong>汇款至平台公司账户的都会开具服务费发票，除此之外任何情况无发票。
                </blockquote>
                <blockquote>
                    <strong>本页内容最后更新时间：</strong>2025年3月15日
                </blockquote>
            </div>
         

    </div>
    <script type="text/javascript">
        var kwd = '{$kwd}';
        var url = "{:U('index/joblist')}"
        var sortShowTime = '{:$show_time}';
        var sortShowNum = '{:$show_num}';
        $(document).ready(function(){
        $(".tabs-hd02 ul li").click(function(){
          $(this).siblings().removeClass("on");
          $(this).addClass("on");
        });
        $(".tabs-hd02 ul li").click(function(){
          $(this).siblings().removeClass("on");
          $(this).addClass("on");
        });
        
            $(".screen01 li").click(function(){
                var typeOnOrUp = 0;
                var dataType = $(this).attr('data-type');
                if($(this).is('.ondown')){
                    $(this).removeClass('ondown');
                    $(this).siblings().removeClass("ondown");
                    $(this).siblings().removeClass('onup');
                    $(this).addClass('onup');
                    typeOnOrUp = 1;
                } else {
                    $(this).removeClass('onup');
                    $(this).siblings().removeClass("ondown");
                    $(this).siblings().removeClass('onup');
                    $(this).addClass('ondown');
                    typeOnOrUp = 2;
                }
                if (dataType == 'sortShowTime') {
                    sortShowNum = 0;
                    sortShowTime = typeOnOrUp;
                } else {
                    sortShowTime = 0;
                    sortShowNum = typeOnOrUp;
                }
                window.location.href = url + '?kwd='+kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum;
                return
            });
            $('.js_kwd').blur(function () {
                var  new_kwd = $(this).val();
                if (new_kwd != kwd) {
                    window.location.href = url + '?kwd='+new_kwd+"&show_time="+sortShowTime+"&show_num="+sortShowNum;
                    return
                }
            })
        
            var noticeSwiper = new Swiper('.noticeSwiper', {
                direction: 'vertical',
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                }
            })
            var noticeSwiper = new Swiper('.page0106Swiper', {
                pagination: {
                    el: ".swiper-pagination",
                },
        
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                }
            });
         
        });

                // 返回逻辑（优先返回历史记录，失败则跳转首页）
                function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/'; // 修改为你的默认回退地址
            }
        }
        </script>
</body>
</html>