<include file="block/hat" />
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<style type='text/css'>
				/* 现代化问答管理页面样式 */

				/* 现代化页面标题 */
				.question-index-header {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 2rem;
					overflow: hidden;
					position: relative;
					width: 100%;
				}

				.question-index-header::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4px;
					background: linear-gradient(90deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
				}

				.question-index-header-content {
					padding: 2rem;
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex-wrap: wrap;
					gap: 1rem;
				}

				.question-index-title {
					display: flex;
					align-items: center;
					gap: 1rem;
					margin: 0;
				}

				.question-index-title-icon {
					width: 3rem;
					height: 3rem;
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
					border-radius: 0.75rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.5rem;
				}

				.question-index-title-text {
					display: flex;
					flex-direction: column;
				}

				.question-index-title-main {
					font-size: 2rem;
					font-weight: 700;
					color: #1a202c;
					margin: 0;
					line-height: 1.2;
				}

				.question-index-title-sub {
					font-size: 1.5rem;
					color: #718096;
					margin: 0;
					font-weight: 400;
				}

				.question-index-actions {
					display: flex;
					align-items: center;
					gap: 1rem;
				}

				.question-index-search-toggle {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
					color: white;
					border: none;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-weight: 600;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
					text-decoration: none;
				}

				.question-index-search-toggle:hover {
					transform: translateY(-2px);
					box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
					color: white;
					text-decoration: none;
				}

				.question-index-add-btn {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
					color: white;
					border: none;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-weight: 600;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.3);
					text-decoration: none;
				}

				.question-index-add-btn:hover {
					transform: translateY(-2px);
					box-shadow: 0 8px 15px -3px rgba(245, 158, 11, 0.4);
					color: white;
					text-decoration: none;
				}

				/* 现代化搜索面板 */
				.question-index-search-panel {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 2rem;
					overflow: hidden;
					max-height: 0;
					opacity: 0;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					width: 100%;
				}

				.question-index-search-panel.show {
					max-height: 500px;
					opacity: 1;
				}

				.question-index-search-header {
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
					color: white;
					padding: 1.5rem 2rem;
					position: relative;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.question-index-search-header::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					height: 1px;
					background: rgba(255, 255, 255, 0.2);
				}

				.question-index-search-title {
					font-size: 1.75rem;
					font-weight: 600;
					margin: 0;
				}

				.question-index-search-icon {
					width: 2.5rem;
					height: 2.5rem;
					background: rgba(255, 255, 255, 0.2);
					border-radius: 0.5rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.25rem;
				}

				.question-index-search-body {
					padding: 2rem;
				}

				.question-index-search-form {
					display: grid;
					grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
					gap: 1.5rem;
					align-items: end;
				}

				.question-index-form-group {
					display: flex;
					flex-direction: column;
					gap: 0.5rem;
				}

				.question-index-form-label {
					font-size: 1.5rem;
					font-weight: 600;
					color: #374151;
				}

				.question-index-form-select,
				.question-index-form-input {
					padding: 0.75rem 1rem;
					border: 2px solid #e2e8f0;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-family: inherit;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					background: #f8fafc;
				}

				.question-index-form-select:focus,
				.question-index-form-input:focus {
					outline: none;
					border-color: #f59e0b;
					box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
					background: white;
				}

				.question-index-search-actions {
					display: flex;
					gap: 1rem;
					margin-top: 1rem;
				}

				.question-index-search-btn {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-weight: 600;
					text-decoration: none;
					border: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				}

				.question-index-search-btn:hover {
					transform: translateY(-2px);
					box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
					text-decoration: none;
				}

				.question-index-search-btn.btn-primary {
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
					color: white;
				}

				.question-index-search-btn.btn-primary:hover {
					color: white;
				}

				.question-index-search-btn.btn-secondary {
					background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
					color: white;
				}

				.question-index-search-btn.btn-secondary:hover {
					color: white;
				}

				/* 现代化导航标签 */
				.question-index-nav-container {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 2rem;
					overflow: hidden;
					width: 100%;
				}

				.question-index-nav-tabs {
					display: flex;
					background: #f8fafc;
					border-bottom: 1px solid #e2e8f0;
					margin: 0;
					padding: 0;
					list-style: none;
					overflow-x: auto;
				}

				.question-index-nav-item {
					flex: 1;
					min-width: 0;
				}

				.question-index-nav-link {
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 0.5rem;
					padding: 1.25rem 1.5rem;
					color: #718096;
					text-decoration: none;
					font-size: 1.5rem;
					font-weight: 600;
					border-bottom: 3px solid transparent;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					white-space: nowrap;
				}

				.question-index-nav-link:hover {
					color: #f59e0b;
					background: rgba(245, 158, 11, 0.05);
					text-decoration: none;
				}

				.question-index-nav-link.active {
					color: #f59e0b !important;
					background: white !important;
					border-bottom-color: #f59e0b !important;
					box-shadow: 0 -2px 4px rgba(245, 158, 11, 0.1) !important;
					font-weight: 700 !important;
				}

				.question-index-nav-link.active::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 3px;
					background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
					z-index: 1;
				}

				.question-index-nav-link.active .question-index-nav-icon {
					color: #f59e0b !important;
					transform: scale(1.1);
				}

				.question-index-nav-icon {
					font-size: 1.25rem;
				}

				/* 现代化列表布局 */
				.question-index-list-container {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					overflow: hidden;
					margin-bottom: 2rem;
					width: 100%;
				}

				.question-list-header {
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
					color: white;
					padding: 1.5rem 2rem;
					position: relative;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.question-list-header::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					height: 1px;
					background: rgba(255, 255, 255, 0.2);
				}

				.question-list-title {
					font-size: 1.75rem;
					font-weight: 600;
					margin: 0;
				}

				.question-list-icon {
					width: 2.5rem;
					height: 2.5rem;
					background: rgba(255, 255, 255, 0.2);
					border-radius: 0.5rem;
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 1.25rem;
				}

				.question-list-body {
					padding: 0;
				}

				.question-list-item {
					display: flex;
					align-items: center;
					padding: 1.5rem 2rem;
					border-bottom: 1px solid #f1f5f9;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
				}

				.question-list-item:last-child {
					border-bottom: none;
				}

				.question-list-item:hover {
					background: #f8fafc;
					transform: translateX(4px);
				}

				.question-list-item::before {
					content: '';
					position: absolute;
					left: 0;
					top: 0;
					bottom: 0;
					width: 4px;
					background: transparent;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.question-list-item:hover::before {
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
				}

				.question-item-id {
					width: 80px;
					flex-shrink: 0;
					font-size: 1.5rem;
					font-weight: 700;
					color: #f59e0b;
					text-align: center;
					background: #fef3c7;
					padding: 0.5rem;
					border-radius: 0.5rem;
					border: 2px solid #fde68a;
				}

				.question-item-content {
					flex: 1;
					margin-left: 2rem;
					min-width: 0;
				}

				.question-item-title {
					font-size: 1.75rem;
					font-weight: 600;
					color: #1f2937;
					margin: 0 0 0.5rem 0;
					line-height: 1.4;
					word-break: break-word;
				}

				.question-item-meta {
					display: flex;
					align-items: center;
					gap: 1.5rem;
					flex-wrap: wrap;
				}

				.question-item-category {
					display: flex;
					align-items: center;
					gap: 0.5rem;
					font-size: 1.25rem;
					color: #6b7280;
				}

				.question-item-status {
					display: flex;
					align-items: center;
					gap: 0.5rem;
				}

				.question-status-badge {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.25rem 0.75rem;
					border-radius: 1rem;
					font-size: 1.25rem;
					font-weight: 600;
					color: white;
				}

				.question-status-badge.label-success {
					background: linear-gradient(135deg, #10b981 0%, #059669 100%);
				}

				.question-status-badge.label-warning {
					background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
				}

				.question-status-badge.label-danger {
					background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
				}

				.question-status-badge.label-default {
					background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
				}

				.question-item-sort {
					display: flex;
					align-items: center;
					gap: 0.5rem;
					font-size: 1.25rem;
					color: #6b7280;
				}

				.question-item-time {
					display: flex;
					align-items: center;
					gap: 0.5rem;
					font-size: 1.25rem;
					color: #6b7280;
				}

				.question-item-actions {
					margin-left: 2rem;
					flex-shrink: 0;
					display: flex;
					gap: 0.75rem;
					flex-wrap: wrap;
				}

				.question-item-actions .btn {
					display: inline-flex;
					align-items: center;
					gap: 0.5rem;
					padding: 0.5rem 1rem;
					border-radius: 0.5rem;
					font-size: 1.25rem;
					font-weight: 600;
					text-decoration: none;
					border: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
					white-space: nowrap;
				}

				.question-item-actions .btn:hover {
					transform: translateY(-2px);
					box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
					text-decoration: none;
				}

				/* 响应式设计 */
				@media (max-width: 768px) {
					.question-index-container {
						padding: 0.5rem;
					}

					.question-index-header-content {
						flex-direction: column;
						align-items: flex-start;
					}

					.question-index-actions {
						width: 100%;
						justify-content: flex-start;
					}

					.question-list-item {
						flex-direction: column;
						align-items: flex-start;
						gap: 1rem;
						padding: 1.5rem 1rem;
					}

					.question-item-content {
						margin-left: 0;
						width: 100%;
					}

					.question-item-meta {
						flex-direction: column;
						align-items: flex-start;
						gap: 0.75rem;
					}

					.question-item-actions {
						margin-left: 0;
						width: 100%;
						justify-content: flex-start;
					}

					.question-item-actions .btn {
						flex: 1;
						justify-content: center;
						min-width: 0;
					}
				}

				/* 动画效果 */
				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(20px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}

				.question-index-fade-in {
					animation: fadeInUp 0.6s ease-out;
				}

				.question-index-fade-in-delay-1 {
					animation: fadeInUp 0.6s ease-out 0.1s both;
				}

				.question-index-fade-in-delay-2 {
					animation: fadeInUp 0.6s ease-out 0.2s both;
				}

				.question-index-fade-in-delay-3 {
					animation: fadeInUp 0.6s ease-out 0.3s both;
				}
			</style>

			<!-- 现代化页面标题 -->
			<div class="question-index-header question-index-fade-in">
				<div class="question-index-header-content">
					<div class="question-index-title">
						<div class="question-index-title-icon">
							<i class="fa fa-question-circle"></i>
						</div>
						<div class="question-index-title-text">
							<h1 class="question-index-title-main">问答管理</h1>
							<p class="question-index-title-sub">Question Management</p>
						</div>
					</div>
					<div class="question-index-actions">
						<button type="button" class="question-index-search-toggle" onclick="toggleSearchPanel()">
							<i class="fa fa-search"></i>
							<span>搜索筛选</span>
						</button>
						<button type="button" class="question-index-add-btn" onclick="showAddQuestionModal()">
							<i class="fa fa-plus"></i>
							<span>问答添加</span>
						</button>
					</div>
				</div>
			</div>

			<!-- 现代化搜索面板 -->
			<div class="question-index-search-panel question-index-fade-in-delay-1" id="searchPanel">
				<div class="question-index-search-header">
					<div class="question-index-search-icon">
						<i class="fa fa-search"></i>
					</div>
					<h3 class="question-index-search-title">搜索筛选</h3>
				</div>
				<div class="question-index-search-body">
					<form method="get" class="question-index-search-form" role="form">
						<div class="question-index-form-group">
							<label class="question-index-form-label">搜索字段</label>
							<select class="question-index-form-select" name="kw">
								<php>foreach($c_kw as $key=>$value){</php>
								<option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
								<php>}</php>
							</select>
						</div>
						<div class="question-index-form-group">
							<label class="question-index-form-label">搜索内容</label>
							<input class="question-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
						</div>
						<div class="question-index-form-group">
							<label class="question-index-form-label">上架状态</label>
							<select name="status" class="question-index-form-select">
								<option value="">全部状态</option>
								<php>foreach($statusList as $key => $status) {</php>
								<option value="{$key}" {:$_get['status'] != '' && $_get['status'] == $key ? "selected" : ''}>{$status.text}</option>
								<php>} </php>
							</select>
						</div>
						<div class="question-index-search-actions">
							<button type="submit" class="question-index-search-btn btn-primary">
								<i class="fa fa-search"></i>
								<span>搜索</span>
							</button>
							<a href="{:U('Question/index')}" class="question-index-search-btn btn-secondary">
								<i class="fa fa-refresh"></i>
								<span>重置</span>
							</a>
						</div>
					</form>
				</div>
			</div>

				<!-- 现代化导航标签 -->
				<div class="question-index-nav-container question-index-fade-in-delay-2">
					<ul class="question-index-nav-tabs">
						<li class="question-index-nav-item">
							<a href="{:U('Question/index')}" class="question-index-nav-link active">
								<i class="fa fa-list question-index-nav-icon"></i>
								<span>全部问答</span>
							</a>
						</li>
						<li class="question-index-nav-item">
							<a href="javascript:void(0)" class="question-index-nav-link quick-filter" data-filter="published">
								<i class="fa fa-check-circle question-index-nav-icon"></i>
								<span>已上架</span>
							</a>
						</li>
						<li class="question-index-nav-item">
							<a href="javascript:void(0)" class="question-index-nav-link quick-filter" data-filter="draft">
								<i class="fa fa-edit question-index-nav-icon"></i>
								<span>草稿</span>
							</a>
						</li>
						<li class="question-index-nav-item">
							<a href="javascript:void(0)" class="question-index-nav-link quick-filter" data-filter="disabled">
								<i class="fa fa-times-circle question-index-nav-icon"></i>
								<span>已禁用</span>
							</a>
						</li>
					</ul>
				</div>

				<!-- 现代化列表容器 -->
				<form action="" method="post">
					<div class="question-index-list-container question-index-fade-in-delay-3">
						<div class="question-list-header">
							<div class="question-list-icon">
								<i class="fa fa-list"></i>
							</div>
							<h3 class="question-list-title">问答列表</h3>
						</div>
						<div class="question-list-body">
							<php>foreach($list as $v) { </php>
							<div class="question-list-item">
								<!-- ID显示 -->
								<div class="question-item-id">
									#{$v.id}
								</div>

								<!-- 内容区域 -->
								<div class="question-item-content">
									<!-- 问答名称 -->
									<h4 class="question-item-title">
										{$v.title}
									</h4>

									<!-- 元信息 -->
									<div class="question-item-meta">
										<!-- 所属分类 -->
										<div class="question-item-category">
											<i class="fa fa-folder-open"></i>
											<span>{$v.category_title}</span>
										</div>

										<!-- 状态 -->
										<div class="question-item-status">
											<span class="question-status-badge label-{:D('Question')->status[$v['status']]['style']}">
												<i class="fa fa-circle"></i>
												<span>{:D("Question")->status[$v['status']]['text']}</span>
											</span>
										</div>

										<!-- 排序 -->
										<div class="question-item-sort">
											<i class="fa fa-sort-numeric-asc"></i>
											<span>排序: {$v.sort}</span>
										</div>

										<!-- 创建时间 -->
										<div class="question-item-time">
											<i class="fa fa-clock-o"></i>
											<span>{:date("Y-m-d H:i:s", $v['created'])}</span>
										</div>
									</div>
								</div>

								<!-- 操作按钮 -->
								<div class="question-item-actions">
									<php>echo QuestionStatBtn($v['id'], $v['status']);</php>
								</div>
							</div>
							<php>}</php>
						</div>
					</div>

					<!-- 分页信息 -->
					<div class="question-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem;">
						{$page}
					</div>
				</form>
		</div>
	</div>
</div>

<!-- 添加问答模态框 -->
<div class="modal fade" id="addQuestionModal" tabindex="-1" role="dialog" aria-labelledby="addQuestionModalLabel">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white;">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title" id="addQuestionModalLabel">
					<i class="fa fa-plus"></i> 添加问答
				</h4>
			</div>
			<div class="modal-body" style="padding: 2rem;">
				<form id="addQuestionForm" class="question-add-form">
					<!-- 问答名称 -->
					<div class="question-add-form-group">
						<label class="question-add-form-label">
							<div class="question-add-form-label-icon">
								<i class="fa fa-question-circle"></i>
							</div>
							问答名称
						</label>
						<input type="text" name="title" class="question-add-form-input" placeholder="请输入问答名称..." />
						<div class="question-add-form-help">
							<i class="fa fa-info-circle"></i>
							请输入简洁明了的问答名称，建议不超过100个字符
						</div>
					</div>

					<!-- 所属分类 -->
					<div class="question-add-form-group">
						<label class="question-add-form-label">
							<div class="question-add-form-label-icon">
								<i class="fa fa-folder-open"></i>
							</div>
							所属问答分类
						</label>
						<div class="question-add-category-options" id="categoryOptions">
							<!-- 这里将动态加载分类选项 -->
						</div>
						<div class="question-add-form-help">
							<i class="fa fa-info-circle"></i>
							可以选择多个分类，至少选择一个分类
						</div>
					</div>

					<!-- 排序 -->
					<div class="question-add-form-group">
						<label class="question-add-form-label">
							<div class="question-add-form-label-icon">
								<i class="fa fa-sort-numeric-asc"></i>
							</div>
							排序
						</label>
						<input type="number" name="sort" class="question-add-form-input" value="1" min="1" max="999" />
						<div class="question-add-form-help">
							<i class="fa fa-info-circle"></i>
							数字越小排序越靠前，建议设置为1-999之间的整数
						</div>
					</div>

					<!-- 问答内容 -->
					<div class="question-add-form-group">
						<label class="question-add-form-label">
							<div class="question-add-form-label-icon">
								<i class="fa fa-file-text"></i>
							</div>
							问答内容
						</label>
						<div class="question-add-editor-container">
							<textarea name="content" id="addQuestionEditor" class="question-add-form-textarea" placeholder="请输入问答内容..."></textarea>
						</div>
						<div class="question-add-form-help">
							<i class="fa fa-info-circle"></i>
							支持富文本编辑，可以插入图片、链接等多媒体内容
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer" style="padding: 1.5rem 2rem; border-top: 1px solid #e2e8f0;">
				<button type="button" class="btn btn-default" data-dismiss="modal">
					<i class="fa fa-times"></i> 取消
				</button>
				<button type="button" class="btn btn-success" onclick="submitAddQuestion()">
					<i class="fa fa-save"></i> 保存问答
				</button>
			</div>
		</div>
	</div>
</div>

<style>
	/* 添加问答表单样式 */
	.question-add-form {
		max-width: 100%;
	}

	.question-add-form-group {
		margin-bottom: 2rem;
	}

	.question-add-form-group:last-child {
		margin-bottom: 0;
	}

	.question-add-form-label {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-size: 1.5rem;
		font-weight: 600;
		color: #374151;
		margin-bottom: 0.75rem;
	}

	.question-add-form-label-icon {
		width: 1.5rem;
		height: 1.5rem;
		background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
		border-radius: 0.25rem;
		display: flex;
		align-items: center;
		justify-content: center;
		color: white;
		font-size: 0.75rem;
	}

	.question-add-form-input {
		width: 100%;
		padding: 1rem 1.25rem;
		border: 2px solid #e2e8f0;
		border-radius: 0.75rem;
		font-size: 1.5rem;
		font-family: inherit;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		background: #f8fafc;
		color: #374151;
	}

	.question-add-form-input:focus {
		outline: none;
		border-color: #f59e0b;
		box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
		background: white;
	}

	.question-add-form-textarea {
		width: 100%;
		padding: 1rem 1.25rem;
		border: 2px solid #e2e8f0;
		border-radius: 0.75rem;
		font-size: 1.5rem;
		font-family: inherit;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		background: #f8fafc;
		color: #374151;
		line-height: 1.5;
		min-height: 200px;
		resize: vertical;
	}

	.question-add-form-textarea:focus {
		outline: none;
		border-color: #f59e0b;
		box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
		background: white;
	}

	.question-add-form-help {
		font-size: 1.25rem;
		color: #6b7280;
		margin-top: 0.5rem;
		display: flex;
		align-items: center;
		gap: 0.25rem;
	}

	.question-add-form-help i {
		color: #f59e0b;
	}

	.question-add-category-options {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
		gap: 1rem;
		padding: 1rem;
		border: 2px solid #e2e8f0;
		border-radius: 0.75rem;
		background: #f8fafc;
		max-height: 200px;
		overflow-y: auto;
	}

	.question-add-category-option {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.75rem;
		border: 2px solid #e2e8f0;
		border-radius: 0.5rem;
		background: white;
		cursor: pointer;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.question-add-category-option:hover {
		border-color: #f59e0b;
		background: rgba(245, 158, 11, 0.05);
	}

	.question-add-category-option.selected {
		border-color: #f59e0b !important;
		background: rgba(245, 158, 11, 0.1) !important;
		color: #b45309 !important;
	}

	.question-add-category-option input[type="checkbox"] {
		display: none;
	}

	.question-add-category-label {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-size: 1.25rem;
		font-weight: 600;
		color: #374151;
		cursor: pointer;
		margin: 0;
		width: 100%;
	}

	.question-add-category-option input[type="checkbox"]:checked + .question-add-category-label {
		color: #b45309;
	}

	/* 富文本编辑器容器样式 */
	.question-add-editor-container {
		border: 2px solid #e2e8f0;
		border-radius: 0.75rem;
		overflow: hidden;
		background: white;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.question-add-editor-container:focus-within {
		border-color: #f59e0b;
		box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
	}

	.modal-content {
		border-radius: 1rem !important;
		border: none !important;
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
	}

	.modal-header {
		border-radius: 1rem 1rem 0 0 !important;
		border-bottom: none !important;
	}

	.modal-footer {
		border-radius: 0 0 1rem 1rem !important;
	}

	.modal-footer .btn {
		padding: 0.75rem 1.5rem;
		font-size: 1.5rem;
		font-weight: 600;
		border-radius: 0.75rem;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.modal-footer .btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
	}

	.modal-footer .btn-success {
		background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
		border: none;
		color: white;
	}

	.modal-footer .btn-success:hover {
		color: white;
	}
</style>

<include file="block/footer" />
<script>
	$(document).ready(function() {
		// 设置导航标签active状态的函数
		function setActiveNavTab() {
			// 移除所有active状态
			$('.question-index-nav-link').removeClass('active');

			// 获取当前URL参数
			var urlParams = new URLSearchParams(window.location.search);
			var status = urlParams.get('status');

			// 根据status参数设置对应的active状态
			if (status === '1') {
				// 已上架状态
				$('.quick-filter[data-filter="published"]').addClass('active');
			} else if (status === '0') {
				// 草稿状态
				$('.quick-filter[data-filter="draft"]').addClass('active');
			} else if (status === '2') {
				// 已禁用状态
				$('.quick-filter[data-filter="disabled"]').addClass('active');
			} else {
				// 默认状态（全部）
				$('.question-index-nav-link').first().addClass('active');
			}
		}

		// 快速筛选功能
		$('.quick-filter').click(function(e) {
			e.preventDefault();
			var $this = $(this);
			var filter = $this.data('filter');
			var currentUrl = window.location.href.split('?')[0];
			var newUrl = currentUrl;

			// 立即更新视觉状态，提供即时反馈
			$('.question-index-nav-link').removeClass('active');
			$this.addClass('active');

			// 添加加载状态
			var originalHtml = $this.html();
			$this.html('<i class="fa fa-spinner fa-spin question-index-nav-icon"></i><span>加载中...</span>');

			// 获取当前的搜索参数
			var urlParams = new URLSearchParams(window.location.search);
			var searchParams = new URLSearchParams();

			// 保留搜索关键词等其他参数
			if (urlParams.get('kw')) {
				searchParams.set('kw', urlParams.get('kw'));
			}
			if (urlParams.get('val')) {
				searchParams.set('val', urlParams.get('val'));
			}

			// 添加状态参数
			switch(filter) {
				case 'published':
					searchParams.set('status', '1');
					break;
				case 'draft':
					searchParams.set('status', '0');
					break;
				case 'disabled':
					searchParams.set('status', '2');
					break;
				default:
					// 不添加status参数，显示全部
					break;
			}

			// 构建最终URL
			var paramString = searchParams.toString();
			if (paramString) {
				newUrl += '?' + paramString;
			}

			// 延迟跳转，让用户看到加载状态
			setTimeout(function() {
				window.location.href = newUrl;
			}, 300);
		});

		// 处理"全部问答"标签的点击事件
		$('.question-index-nav-link').not('.quick-filter').click(function(e) {
			var $this = $(this);
			var href = $this.attr('href');

			// 如果是指向当前页面的链接，处理active状态
			if (href && href.indexOf('Question/index') !== -1) {
				e.preventDefault();

				// 立即更新视觉状态
				$('.question-index-nav-link').removeClass('active');
				$this.addClass('active');

				// 添加加载状态
				var originalHtml = $this.html();
				$this.html('<i class="fa fa-spinner fa-spin question-index-nav-icon"></i><span>加载中...</span>');

				// 延迟跳转到不带参数的页面
				setTimeout(function() {
					window.location.href = href;
				}, 300);
			}
		});

		// 列表项悬停效果增强
		$('.question-list-item').hover(
			function() {
				$(this).addClass('hover-effect');
			},
			function() {
				$(this).removeClass('hover-effect');
			}
		);

		// 空状态处理
		var totalItems = $('.question-list-item').length;
		if (totalItems === 0) {
			var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-question-circle"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无问答数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的问答。</p><button onclick="showAddQuestionModal()" class="btn btn-primary" style="padding: 0.75rem 1.5rem; font-size: 1.5rem;"><i class="fa fa-plus"></i> 添加问答</button></div>');
			$('.question-index-list-container .question-list-body').append($emptyState);
		}

		// 延迟执行active状态设置，确保DOM完全加载
		setTimeout(function() {
			setActiveNavTab();
		}, 100);

		// 保持原有的状态切换功能
		$('.js_status').click(function() {
			var that = $(this),
				url = that.attr('url');
			$.get(url, function(data) {
				window.location.reload();
			});
		});
	});

	// 搜索面板切换功能
	function toggleSearchPanel() {
		var panel = document.getElementById('searchPanel');
		if (panel.classList.contains('show')) {
			panel.classList.remove('show');
		} else {
			panel.classList.add('show');
		}
	}

	// 显示添加问答模态框
	function showAddQuestionModal() {
		// 重置表单
		$('#addQuestionForm')[0].reset();

		// 重置分类选择
		$('.question-add-category-option').removeClass('selected');
		$('.question-add-category-option input[type="checkbox"]').prop('checked', false);

		// 加载问答分类选项
		loadQuestionCategories();

		// 显示模态框
		$('#addQuestionModal').modal('show');
	}

	// 加载问答分类选项
	function loadQuestionCategories() {
		var $container = $('#categoryOptions');
		$container.html('<div style="text-align: center; padding: 2rem; color: #6b7280;"><i class="fa fa-spinner fa-spin"></i> 加载分类中...</div>');

		// 这里可以通过AJAX加载分类列表，暂时使用静态数据
		// 如果需要动态加载，可以使用以下代码：
		/*
		$.ajax({
			url: '{:U("Question/getCategoryList")}',
			type: 'GET',
			dataType: 'json',
			success: function(data) {
				var html = '';
				if (data && data.length > 0) {
					$.each(data, function(index, item) {
						html += '<div class="question-add-category-option">';
						html += '<input type="checkbox" name="category_id[]" value="' + item.id + '" id="cat_' + item.id + '">';
						html += '<label for="cat_' + item.id + '" class="question-add-category-label">';
						html += '<i class="fa fa-folder-open"></i>';
						html += '<span>' + item.title + '</span>';
						html += '</label>';
						html += '</div>';
					});
				} else {
					html = '<div style="text-align: center; padding: 2rem; color: #6b7280;">暂无分类数据</div>';
				}
				$container.html(html);

				// 绑定分类选择事件
				bindCategoryEvents();
			},
			error: function() {
				$container.html('<div style="text-align: center; padding: 2rem; color: #ef4444;">加载分类失败</div>');
			}
		});
		*/

		// 暂时使用静态数据演示
		setTimeout(function() {
			var html = '';
			html += '<div class="question-add-category-option">';
			html += '<input type="checkbox" name="category_id[]" value="1" id="cat_1">';
			html += '<label for="cat_1" class="question-add-category-label">';
			html += '<i class="fa fa-folder-open"></i>';
			html += '<span>常见问题</span>';
			html += '</label>';
			html += '</div>';

			html += '<div class="question-add-category-option">';
			html += '<input type="checkbox" name="category_id[]" value="2" id="cat_2">';
			html += '<label for="cat_2" class="question-add-category-label">';
			html += '<i class="fa fa-folder-open"></i>';
			html += '<span>技术支持</span>';
			html += '</label>';
			html += '</div>';

			html += '<div class="question-add-category-option">';
			html += '<input type="checkbox" name="category_id[]" value="3" id="cat_3">';
			html += '<label for="cat_3" class="question-add-category-label">';
			html += '<i class="fa fa-folder-open"></i>';
			html += '<span>使用指南</span>';
			html += '</label>';
			html += '</div>';

			$container.html(html);

			// 绑定分类选择事件
			bindCategoryEvents();
		}, 500);
	}

	// 绑定分类选择事件
	function bindCategoryEvents() {
		$('.question-add-category-option').on('click', function() {
			var $input = $(this).find('input[type="checkbox"]');
			$input.prop('checked', !$input.prop('checked'));

			// 更新选中状态样式
			if ($input.prop('checked')) {
				$(this).addClass('selected');
			} else {
				$(this).removeClass('selected');
			}
		});
	}

	// 提交添加问答表单
	function submitAddQuestion() {
		var title = $('input[name="title"]').val().trim();
		var categoryIds = [];
		$('input[name="category_id[]"]:checked').each(function() {
			categoryIds.push($(this).val());
		});
		var sort = $('input[name="sort"]').val();
		var content = $('textarea[name="content"]').val().trim();

		// 清除之前的错误状态
		$('.question-add-form-input, .question-add-form-textarea').removeClass('error');
		$('.question-add-form-help').removeClass('error');

		// 表单验证
		if (!title) {
			showQuestionFieldError('input[name="title"]', '请输入问答名称');
			return false;
		}

		if (title.length > 200) {
			showQuestionFieldError('input[name="title"]', '问答名称不能超过200个字符');
			return false;
		}

		if (categoryIds.length === 0) {
			layer.msg('请至少选择一个问答分类', {icon: 2});
			return false;
		}

		if (!sort || sort < 1 || sort > 999) {
			showQuestionFieldError('input[name="sort"]', '排序必须是1-999之间的整数');
			return false;
		}

		if (!content) {
			showQuestionFieldError('textarea[name="content"]', '请输入问答内容');
			return false;
		}

		// 显示加载状态
		var $submitBtn = $('.modal-footer .btn-success');
		var originalText = $submitBtn.html();
		$submitBtn.prop('disabled', true);
		$submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 保存中...');

		// 提交表单数据
		$.ajax({
			url: '{:U("Question/edit")}',
			type: 'POST',
			data: {
				title: title,
				'category_id[]': categoryIds,
				sort: sort,
				content: content,
				submit: '提交'
			},
			timeout: 30000,
			success: function(response) {
				console.log('添加问答成功:', response);

				// 检查响应内容
				var responseText = typeof response === 'string' ? response : JSON.stringify(response);

				if (responseText.includes('成功') || responseText.includes('success')) {
					layer.msg('添加问答成功！', {icon: 1, time: 2000});
					setTimeout(function() {
						$('#addQuestionModal').modal('hide');
						window.location.reload();
					}, 2000);
				} else if (responseText.includes('错误') || responseText.includes('失败')) {
					layer.msg('添加问答失败，请重试', {icon: 2});
				} else {
					// 默认认为成功
					layer.msg('添加问答成功！', {icon: 1, time: 2000});
					setTimeout(function() {
						$('#addQuestionModal').modal('hide');
						window.location.reload();
					}, 2000);
				}
			},
			error: function(xhr, status, error) {
				console.error('添加问答失败:', {
					status: status,
					error: error,
					responseText: xhr.responseText
				});

				var errorMsg = '网络错误，请重试';
				if (status === 'timeout') {
					errorMsg = '请求超时，请重试';
				} else if (xhr.status === 404) {
					errorMsg = '页面不存在，请联系管理员';
				} else if (xhr.status === 500) {
					errorMsg = '服务器错误，请重试';
				}

				layer.msg(errorMsg, {icon: 2});
			},
			complete: function() {
				// 恢复按钮状态
				$submitBtn.prop('disabled', false);
				$submitBtn.html(originalText);
			}
		});
	}

	// 显示字段错误
	function showQuestionFieldError(selector, message) {
		var $field = $(selector);
		var $help = $field.siblings('.question-add-form-help');

		$field.addClass('error');
		$help.addClass('error');
		$help.html('<i class="fa fa-exclamation-triangle"></i> ' + message);
		$field.focus();

		layer.msg(message, {icon: 2});

		// 3秒后恢复正常状态
		setTimeout(function() {
			$field.removeClass('error');
			$help.removeClass('error');
			if (selector === 'input[name="title"]') {
				$help.html('<i class="fa fa-info-circle"></i> 请输入简洁明了的问答名称，建议不超过100个字符');
			} else if (selector === 'input[name="sort"]') {
				$help.html('<i class="fa fa-info-circle"></i> 数字越小排序越靠前，建议设置为1-999之间的整数');
			} else if (selector === 'textarea[name="content"]') {
				$help.html('<i class="fa fa-info-circle"></i> 支持富文本编辑，可以插入图片、链接等多媒体内容');
			}
		}, 3000);
	}

	// 输入框焦点效果
	$('.question-add-form-input, .question-add-form-textarea').on('focus', function() {
		$(this).closest('.question-add-form-group').addClass('focused');
	}).on('blur', function() {
		$(this).closest('.question-add-form-group').removeClass('focused');
	});

	// 字符计数
	$('input[name="title"]').on('input', function() {
		var length = $(this).val().length;
		var maxLength = 200;
		var $help = $(this).siblings('.question-add-form-help');

		if (length > maxLength * 0.8) {
			$help.html('<i class="fa fa-exclamation-triangle" style="color: #f59e0b;"></i> 问答名称长度: ' + length + '/' + maxLength + ' 字符');
		} else {
			$help.html('<i class="fa fa-info-circle"></i> 请输入简洁明了的问答名称，建议不超过100个字符');
		}
	});

	// 添加自定义样式
	$('<style>')
		.prop('type', 'text/css')
		.html(`
			.question-add-form-group.focused .question-add-form-label {
				color: #f59e0b !important;
			}

			.question-add-form-group.focused .question-add-form-label-icon {
				background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
				transform: scale(1.1);
			}

			/* 表单验证错误状态 */
			.question-add-form-input.error,
			.question-add-form-textarea.error {
				border-color: #ef4444 !important;
				box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
			}

			.question-add-form-help.error {
				color: #ef4444 !important;
			}

			.question-add-form-help.error i {
				color: #ef4444 !important;
			}
		`)
		.appendTo('head');
</script>
