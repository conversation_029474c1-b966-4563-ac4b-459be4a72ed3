<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <title>京东京灵平台灵工绑定</title>
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <style>
        :root {
            --primary-color: #00bf80;
            --secondary-color: #666;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, system-ui, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 22px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        
        .form-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        
        .form-section {
            margin-bottom: 15px;
        }
        
        .form-section label {
            display: block;
            margin-bottom: 8px;
            font-size: 16px;
            color: #333;
        }
        
        .form-section input {
            width: 100%;
            height: 45px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 15px;
            font-size: 16px;
        }
        
        .form-section input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .note {
            font-size: 14px;
            color: #999;
            margin-top: 6px;
        }
        
        .submit-btn {
            width: 100%;
            height: 50px;
            background: #59c567;
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
        }
        
        .submit-btn:hover {
            opacity: 0.9;
        }
        
        .form-card .status-badge {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .status-badge.pending {
            background: #fdf6ec;
            color: #e67e22;
        }
        
        .status-badge.approved {
            background: #f0f9eb;
            color: #67c23a;
        }
        
        .status-badge.rejected {
            background: #fef0f0;
            color: #f56c6c;
        }
        
        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: #666;
            text-decoration: none;
        }
        
        /* 返回箭头样式 */
        .back-arrow {
            position: fixed;
            top: 20px;
            left: 15px;
            width: 40px;
            height: 40px;
            z-index: 9999;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            transition: all 0.2s;
        }

        .back-arrow::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 50%;
            width: 12px;
            height: 12px;
            border-left: 2px solid #333;
            border-bottom: 2px solid #333;
            transform: translateY(-50%) rotate(45deg);
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="{:U('Index/money')}" class="back-arrow"></a>
    
    <div class="container">
        <div class="header">
            <h1>京东京灵平台灵工绑定</h1>
            <div class="subtitle">请填写您的京东京灵平台灵工信息用于审核</div>
        </div>
        
        <div class="form-card">
            <if condition="$bindingInfo">
                <if condition="$bindingInfo['status'] eq 0">
                    <div class="status-badge pending">当前状态：待审核</div>
                <elseif condition="$bindingInfo['status'] eq 1" />
                    <div class="status-badge approved">当前状态：已通过</div>
                <elseif condition="$bindingInfo['status'] eq 2" />
                    <div class="status-badge rejected">当前状态：已拒绝 (原因：{$bindingInfo.remark})</div>
                </if>
            </if>
            
            <form action="{:U('Index/submitJinglingBinding')}" method="post">
                <div class="form-section">
                    <label for="worker_name">灵工姓名</label>
                    <input type="text" id="worker_name" name="worker_name" placeholder="请输入灵工姓名" value="{$bindingInfo.worker_name}" required>
                </div>
                
                <div class="form-section">
                    <label for="id_card">身份证号</label>
                    <input type="text" id="id_card" name="id_card" placeholder="请输入18位身份证号" maxlength="18" value="{$bindingInfo.id_card}" required>
                </div>
                
                <div class="form-section">
                    <label for="phone">手机号</label>
                    <input type="tel" id="phone" name="phone" placeholder="请输入手机号" maxlength="11" value="{$bindingInfo.phone}" required>
                </div>
                
                <div class="form-section">
                    <label for="bank_name">结算银行</label>
                    <input type="text" id="bank_name" name="bank_name" placeholder="请输入结算银行名称" value="{$bindingInfo.bank_name}" required>
                    <div class="note">例如：中国工商银行、中国建设银行等</div>
                </div>
                
                <div class="form-section">
                    <label for="bank_account_last4">银行账号后四位</label>
                    <input type="text" id="bank_account_last4" name="bank_account_last4" placeholder="请输入银行账号后四位" maxlength="4" value="{$bindingInfo.bank_account_last4}" required>
                </div>
                
                <button type="submit" class="submit-btn">提交绑定信息</button>
            </form>
        </div>
        
        <a href="{:U('Index/money')}" class="back-link">返回余额管理</a>
    </div>
    
    <script>
        // 表单验证
        $(function() {
            $('form').on('submit', function(e) {
                // 身份证号验证
                var idCard = $('#id_card').val().trim();
                if (idCard.length !== 18) {
                    alert('请输入正确的18位身份证号');
                    e.preventDefault();
                    return false;
                }
                
                // 手机号验证
                var phone = $('#phone').val().trim();
                if (!/^1[3-9]\d{9}$/.test(phone)) {
                    alert('请输入正确的11位手机号');
                    e.preventDefault();
                    return false;
                }
                
                // 银行账号后四位验证
                var bankAccountLast4 = $('#bank_account_last4').val().trim();
                if (!/^\d{4}$/.test(bankAccountLast4)) {
                    alert('请输入正确的银行账号后四位');
                    e.preventDefault();
                    return false;
                }
            });
        });
    </script>
</body>
</html> 