<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化服务站编辑页面样式 */
                .servicestation-edit-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .servicestation-edit-container {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .servicestation-edit-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .servicestation-edit-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .servicestation-edit-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .servicestation-edit-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .servicestation-edit-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .servicestation-edit-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .servicestation-edit-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .servicestation-edit-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .servicestation-edit-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .servicestation-edit-back-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .servicestation-edit-back-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .servicestation-edit-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .servicestation-edit-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .servicestation-edit-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .servicestation-edit-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .servicestation-edit-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .servicestation-edit-nav-link.active {
                    color: #667eea;
                    background: white;
                    border-bottom-color: #667eea;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1);
                }

                .servicestation-edit-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .servicestation-edit-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化表单卡片 */
                .servicestation-edit-form-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .servicestation-edit-form-section {
                    margin-bottom: 2rem;
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }

                .servicestation-edit-section-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .servicestation-edit-section-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .servicestation-edit-section-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .servicestation-edit-section-body {
                    padding: 2rem;
                }

                /* 现代化表单组 */
                .servicestation-edit-form-group {
                    margin-bottom: 2rem;
                }

                .servicestation-edit-form-group:last-child {
                    margin-bottom: 0;
                }

                .servicestation-edit-form-label {
                    display: block;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 0.75rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .servicestation-edit-form-label .required {
                    color: #ef4444;
                    font-size: 1.25rem;
                }

                .servicestation-edit-form-input {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    line-height: 1.5;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                }

                .servicestation-edit-form-input:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .servicestation-edit-form-select {
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    line-height: 1.5;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                    cursor: pointer;
                }

                .servicestation-edit-form-select:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .servicestation-edit-form-textarea {
                    width: 100%;
                    min-height: 120px;
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    line-height: 1.5;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                    resize: vertical;
                }

                .servicestation-edit-form-textarea:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .servicestation-edit-help-text {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-top: 0.5rem;
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;
                }

                /* 文件上传区域 */
                .servicestation-edit-upload-section {
                    background: #f8fafc;
                    border: 2px dashed #d1d5db;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    text-align: center;
                    transition: all 0.3s ease;
                }

                .servicestation-edit-upload-section:hover {
                    border-color: #667eea;
                    background: #f0f9ff;
                }

                /* 提交按钮区域 */
                .servicestation-edit-submit-section {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    padding: 2rem;
                    text-align: center;
                    margin-top: 2rem;
                }

                .servicestation-edit-submit-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 1rem 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                    min-width: 200px;
                }

                .servicestation-edit-submit-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                }

                .servicestation-edit-submit-btn:active {
                    transform: translateY(0);
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .servicestation-edit-container {
                        padding: 1.5rem;
                    }

                    .servicestation-edit-header-content {
                        flex-direction: column;
                        text-align: center;
                    }
                }

                @media (max-width: 768px) {
                    .servicestation-edit-container {
                        padding: 1rem;
                    }

                    .servicestation-edit-nav-tabs {
                        flex-direction: column;
                    }

                    .servicestation-edit-nav-item {
                        flex: none;
                    }

                    .servicestation-edit-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .servicestation-edit-nav-link.active {
                        border-bottom: none;
                        border-right-color: #667eea;
                    }

                    .servicestation-edit-title-main {
                        font-size: 1.75rem;
                    }

                    .servicestation-edit-title-sub {
                        font-size: 1.25rem;
                    }

                    .servicestation-edit-section-body {
                        padding: 1.5rem;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .servicestation-edit-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .servicestation-edit-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .servicestation-edit-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .servicestation-edit-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }

                /* 获取群成员按钮样式 */
                .get-members-btn {
                    margin-left: auto;
                    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
                    border: none;
                    color: white;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 500;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
                }

                .get-members-btn:hover {
                    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);
                    color: white;
                }

                .get-members-btn:active {
                    transform: translateY(0);
                }

                /* 群成员弹出层样式 */
                .wechat-members-modal {
                    display: none;
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 9999;
                    animation: fadeIn 0.3s ease-out;
                }

                .wechat-members-modal.show {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 20px;
                    box-sizing: border-box;
                }

                .wechat-members-content {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    overflow: hidden;
                    animation: slideInUp 0.3s ease-out;
                    margin: auto;
                    position: relative;
                    transform: translateY(0);
                }

                .wechat-members-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .wechat-members-title {
                    font-size: 1.25rem;
                    font-weight: 600;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .wechat-members-close {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.25rem;
                    border-radius: 0.25rem;
                    transition: background 0.2s ease;
                }

                .wechat-members-close:hover {
                    background: rgba(255, 255, 255, 0.2);
                }

                .wechat-members-body {
                    padding: 1.5rem;
                    max-height: 400px;
                    overflow-y: auto;
                }

                .wechat-members-loading {
                    text-align: center;
                    padding: 2rem;
                    color: #666;
                }

                .wechat-members-list {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                    gap: 1rem;
                    padding: 0.5rem 0;
                }

                .wechat-member-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    background: #f8f9fa;
                    min-height: 60px;
                }

                .wechat-member-item:hover {
                    border-color: #667eea;
                    background: #f0f4ff;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.15);
                }

                .wechat-member-item.selected {
                    border-color: #667eea;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                }

                .wechat-member-checkbox {
                    width: 1.25rem;
                    height: 1.25rem;
                    accent-color: #667eea;
                    flex-shrink: 0;
                }

                .wechat-member-avatar {
                    width: 2.5rem;
                    height: 2.5rem;
                    border-radius: 50%;
                    object-fit: cover;
                    flex-shrink: 0;
                    border: 2px solid #e2e8f0;
                }

                .wechat-member-item.selected .wechat-member-avatar {
                    border-color: rgba(255, 255, 255, 0.5);
                }

                .wechat-member-name {
                    flex: 1;
                    min-width: 0;
                }

                .wechat-member-nickname {
                    font-size: 1rem;
                    font-weight: 600;
                    margin-bottom: 0.25rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .wechat-member-username {
                    font-size: 0.75rem;
                    opacity: 0.7;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .wechat-member-item.selected .wechat-member-username {
                    opacity: 0.9;
                }

                .wechat-members-footer {
                    padding: 1rem 1.5rem;
                    border-top: 1px solid #e2e8f0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    gap: 1rem;
                }

                .wechat-members-selected-count {
                    color: #667eea;
                    font-weight: 500;
                }

                .wechat-members-actions {
                    display: flex;
                    gap: 0.75rem;
                }

                .wechat-members-confirm {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 0.5rem 1.5rem;
                    border-radius: 0.5rem;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }

                .wechat-members-confirm:hover {
                    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
                    transform: translateY(-1px);
                }

                .wechat-members-cancel {
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 0.5rem 1.5rem;
                    border-radius: 0.5rem;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }

                .wechat-members-cancel:hover {
                    background: #5a6268;
                    transform: translateY(-1px);
                }

                @keyframes slideInUp {
                    from {
                        transform: translateY(30px);
                        opacity: 0;
                    }
                    to {
                        transform: translateY(0);
                        opacity: 1;
                    }
                }

                /* 群列表弹出层样式 */
                .chatroom-list-modal {
                    display: none;
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 9999;
                    animation: fadeIn 0.3s ease-out;
                }

                .chatroom-list-modal.show {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 20px;
                    box-sizing: border-box;
                }

                .chatroom-list-content {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    overflow: hidden;
                    animation: slideInUp 0.3s ease-out;
                    margin: auto;
                    position: relative;
                    transform: translateY(0);
                }

                .chatroom-list-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border-radius: 1rem 1rem 0 0;
                }

                .chatroom-list-header h3 {
                    margin: 0;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .chatroom-list-close {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 50%;
                    transition: background-color 0.2s;
                }

                .chatroom-list-close:hover {
                    background: rgba(255, 255, 255, 0.2);
                }

                .chatroom-list-body {
                    padding: 1.5rem;
                    max-height: 60vh;
                    overflow-y: auto;
                }

                .chatroom-list-loading,
                .chatroom-list-error {
                    text-align: center;
                    padding: 2rem;
                    color: #666;
                }

                .chatroom-list-loading i {
                    font-size: 2rem;
                    margin-bottom: 1rem;
                    color: #667eea;
                }

                .chatroom-list-error i {
                    font-size: 2rem;
                    margin-bottom: 1rem;
                    color: #ef4444;
                }

                .chatroom-list-container {
                    display: grid;
                    gap: 0.75rem;
                }

                .chatroom-item {
                    display: flex;
                    align-items: center;
                    padding: 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.75rem;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    background: #f9fafb;
                }

                .chatroom-item:hover {
                    border-color: #667eea;
                    background: #f0f4ff;
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
                }

                .chatroom-item.selected {
                    border-color: #667eea;
                    background: #667eea;
                    color: white;
                }

                .chatroom-avatar {
                    width: 3rem;
                    height: 3rem;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                    font-size: 1.25rem;
                    margin-right: 1rem;
                    flex-shrink: 0;
                }

                .chatroom-info {
                    flex: 1;
                    min-width: 0;
                }

                .chatroom-name {
                    font-weight: 600;
                    font-size: 1rem;
                    margin-bottom: 0.25rem;
                    word-break: break-all;
                }

                .chatroom-id {
                    font-size: 0.875rem;
                    opacity: 0.7;
                    word-break: break-all;
                }

                .chatroom-item.selected .chatroom-avatar {
                    background: rgba(255, 255, 255, 0.2);
                }

                .chatroom-list-footer {
                    padding: 1rem 1.5rem;
                    border-top: 1px solid #e5e7eb;
                    display: flex;
                    justify-content: flex-end;
                    gap: 1rem;
                    background: #f9fafb;
                    border-radius: 0 0 1rem 1rem;
                }

                .chatroom-list-btn {
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    font-size: 0.875rem;
                }

                .chatroom-list-btn-cancel {
                    background: #6b7280;
                    color: white;
                }

                .chatroom-list-btn-cancel:hover {
                    background: #4b5563;
                }

                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
            </style>

            <div class="servicestation-edit-wrapper">
                <div class="servicestation-edit-container">
                    <!-- 现代化页面标题 -->
                    <div class="servicestation-edit-header servicestation-edit-fade-in">
                        <div class="servicestation-edit-header-content">
                            <div class="servicestation-edit-title">
                                <div class="servicestation-edit-title-icon">
                                    <php>if($row) {</php>
                                    <i class="fa fa-edit"></i>
                                    <php>} else {</php>
                                    <i class="fa fa-plus"></i>
                                    <php>}</php>
                                </div>
                                <div class="servicestation-edit-title-text">
                                    <h1 class="servicestation-edit-title-main">服务站{:$row ? '编辑' : '添加'}</h1>
                                    <p class="servicestation-edit-title-sub">Service Station {:$row ? 'Edit' : 'Add'}</p>
                                </div>
                            </div>
                            <div class="servicestation-edit-actions">
                                <a href="{:U('Servicestation/index')}" class="servicestation-edit-back-btn">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="servicestation-edit-nav-container servicestation-edit-fade-in-delay-1">
                        <ul class="servicestation-edit-nav-tabs">
                            <li class="servicestation-edit-nav-item">
                                <a href="{:U('Servicestation/index')}" class="servicestation-edit-nav-link">
                                    <i class="fa fa-list servicestation-edit-nav-icon"></i>
                                    <span>服务站管理</span>
                                </a>
                            </li>
                            <li class="servicestation-edit-nav-item">
                                <a href="#" class="servicestation-edit-nav-link active">
                                    <php>if($row) {</php>
                                    <i class="fa fa-edit servicestation-edit-nav-icon"></i>
                                    <span>编辑服务站</span>
                                    <php>} else {</php>
                                    <i class="fa fa-plus servicestation-edit-nav-icon"></i>
                                    <span>添加服务站</span>
                                    <php>}</php>
                                </a>
                            </li>
                            <li class="servicestation-edit-nav-item">
                                <a href="javascript:void(0)" class="servicestation-edit-nav-link">
                                    <i class="fa fa-cog servicestation-edit-nav-icon"></i>
                                    <span>配置管理</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 现代化表单 -->
                    <form action="" method="post" class="form" enctype="multipart/form-data" id="form1">

                        <!-- 公司基本信息区域 -->
                        <div class="servicestation-edit-form-section servicestation-edit-fade-in-delay-2">
                            <div class="servicestation-edit-section-header">
                                <div class="servicestation-edit-section-icon">
                                    <i class="fa fa-building"></i>
                                </div>
                                <h3 class="servicestation-edit-section-title">公司基本信息</h3>
                            </div>
                            <div class="servicestation-edit-section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-building-o"></i>
                                                公司名称
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" name="enterprise_name" class="servicestation-edit-form-input" value="{$row.enterprise_name}" placeholder="请输入公司名称" required />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-id-card-o"></i>
                                                统一社会信用代码
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" name="credit_code" class="servicestation-edit-form-input" value="{$row.credit_code}" placeholder="请输入统一社会信用代码" required />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-envelope"></i>
                                                电子邮件
                                                <span class="required">*</span>
                                            </label>
                                            <input type="email" name="email" class="servicestation-edit-form-input" value="{$row.email}" placeholder="请输入电子邮件" required />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-map-marker"></i>
                                                通讯地址
                                            </label>
                                            <input type="text" name="mail_address" class="servicestation-edit-form-input" value="{$row.mail_address}" placeholder="请输入通讯地址" />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-sitemap"></i>
                                                所属上级
                                            </label>
                                            <input type="text" name="pid" class="servicestation-edit-form-input" value="{$row.pid}" placeholder="请输入所属上级ID（可选）" />
                                            <div class="servicestation-edit-help-text">
                                                <i class="fa fa-info-circle"></i>
                                                留空则归属总部管理
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-file-text-o"></i>
                                                合同编号
                                            </label>
                                            <input type="text" name="contract_number" class="servicestation-edit-form-input" value="{$row.contract_number}" placeholder="请输入合同编号" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 联系人信息区域 -->
                        <div class="servicestation-edit-form-section servicestation-edit-fade-in-delay-2">
                            <div class="servicestation-edit-section-header">
                                <div class="servicestation-edit-section-icon">
                                    <i class="fa fa-user"></i>
                                </div>
                                <h3 class="servicestation-edit-section-title">联系人信息</h3>
                            </div>
                            <div class="servicestation-edit-section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-user-o"></i>
                                                联络人姓名
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" name="contract_name" class="servicestation-edit-form-input" value="{$row.contract_name}" placeholder="请输入联络人姓名" required />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-phone"></i>
                                                手机号码
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" name="mobile" class="servicestation-edit-form-input" value="{$row.mobile}" placeholder="请输入手机号码" required />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-id-card"></i>
                                                联络人身份证号
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" name="contract_card" class="servicestation-edit-form-input" value="{$row.contract_card}" placeholder="请输入身份证号" required />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-users"></i>
                                                内部用户
                                            </label>
                                            <select name="users_id" class="servicestation-edit-form-select">
                                                <option value="">请选择内部用户</option>
                                                <php>foreach($usersList as $key => $val) {</php>
                                                <option value="{$key}" {: $key == $row['users_id'] ? 'selected' : ''}>{$val}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 服务站配置区域 -->
                        <div class="servicestation-edit-form-section servicestation-edit-fade-in-delay-3">
                            <div class="servicestation-edit-section-header">
                                <div class="servicestation-edit-section-icon">
                                    <i class="fa fa-cogs"></i>
                                </div>
                                <h3 class="servicestation-edit-section-title">服务站配置</h3>
                            </div>
                            <div class="servicestation-edit-section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-building"></i>
                                                服务站名称
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" name="service_name" class="servicestation-edit-form-input" value="{$row.service_name}" placeholder="请输入服务站名称" required />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-barcode"></i>
                                                服务站编号
                                            </label>
                                            <input type="text" name="service_number" class="servicestation-edit-form-input" value="{$row.service_number}" placeholder="请输入服务站编号" />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-star"></i>
                                                服务站级别
                                                <span class="required">*</span>
                                            </label>
                                            <select name="level" class="servicestation-edit-form-select" required>
                                                <option value="">请选择服务站级别</option>
                                                <php>foreach($levelList as $key => $val) {</php>
                                                <option value="{$key}" {: $key == $row['level'] ? 'selected' : ''}>{$val.text}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-tag"></i>
                                                服务站类型
                                                <span class="required">*</span>
                                            </label>
                                            <select name="type" class="servicestation-edit-form-select" required>
                                                <option value="">请选择服务站类型</option>
                                                <php>foreach($typeList as $key => $val) {</php>
                                                <option value="{$key}" {: $key == $row['type'] ? 'selected' : ''}>{$val.text}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-cubes"></i>
                                                资源包介绍页面
                                            </label>
                                            <select name="resources" class="servicestation-edit-form-select">
                                                <option value="">请选择资源包类型</option>
                                                <php>foreach($resourcesList as $key => $val) {</php>
                                                <option value="{$key}" {: $key == $row['resources'] ? 'selected' : ''}>{$val.text}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-share"></i>
                                                资源包对外划拨
                                            </label>
                                            <select name="is_out" class="servicestation-edit-form-select">
                                                <option value="">请选择划拨设置</option>
                                                <php>foreach($isoutList as $key => $val) {</php>
                                                <option value="{$key}" {: $key == $row['is_out'] ? 'selected' : ''}>{$val.text}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-eye"></i>
                                                外部是否显示
                                            </label>
                                            <select name="show" class="servicestation-edit-form-select">
                                                <option value="">请选择显示设置</option>
                                                <php>foreach($isshowList as $key => $val) {</php>
                                                <option value="{$key}" {: $key == $row['show'] ? 'selected' : ''}>{$val.text}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 对接群信息区域 -->
                        <div class="servicestation-edit-form-section servicestation-edit-fade-in-delay-2">
                            <div class="servicestation-edit-section-header">
                                <div class="servicestation-edit-section-icon">
                                    <i class="fa fa-user"></i>
                                </div>
                                <h3 class="servicestation-edit-section-title">服务站微信对接群信息</h3>
                                
                            </div>
                            <div class="servicestation-edit-section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-user-o"></i>
                                                对接群@chatroomID
                                                <span class="required">*</span>

                                            </label>
                                            <div style="display: flex; gap: 10px; align-items: center;">
                                                <input type="text" name="chatroom" class="servicestation-edit-form-input" value="{$row.chatroom}" placeholder="对接群@chatroomID，如无先填无" required style="flex: 1;" />
                                                <button type="button" onclick="getChatroomList()" class="servicestation-edit-btn servicestation-edit-btn-secondary" style="white-space: nowrap;">
                                                    <i class="fa fa-list"></i> 获取群列表
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">      
                                                要@的微信用户ID
                                                <span class="required">*</span>
                                                <button type="button" class="btn btn-info btn-sm get-members-btn" onclick="getWechatMembers()">
                                    <i class="fa fa-users"></i> 获取群成员
                                </button>
                                            </label>
                                            <input type="text" name="wxatuserlist" class="servicestation-edit-form-input" value="{$row.wxatuserlist}" placeholder="要@的微信用户ID，如无先填无" required />
                                            <!-- 调试信息 -->
                                            <small style="color: #999; font-size: 0.75rem;">
                                                调试: wxatUserList = "{$row.wxatuserlist}" |
                                                atUserList = "{$row.atUserList}" |
                                                数据类型: {$row.wxatuserlist|gettype}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 证件资料区域 -->
                        <div class="servicestation-edit-form-section servicestation-edit-fade-in-delay-3">
                            <div class="servicestation-edit-section-header">
                                <div class="servicestation-edit-section-icon">
                                    <i class="fa fa-file-text-o"></i>
                                </div>
                                <h3 class="servicestation-edit-section-title">证件资料</h3>
                            </div>
                            <div class="servicestation-edit-section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-file-pdf-o"></i>
                                                合作协议
                                                <span class="required">*</span>
                                            </label>
                                            <div class="servicestation-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_file('cooperation_agreement', $row['cooperation_agreement'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-certificate"></i>
                                                营业执照
                                                <span class="required">*</span>
                                            </label>
                                            <div class="servicestation-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_image('business_license',  $row['business_license'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-file-text"></i>
                                                申请加盟表
                                                <span class="required">*</span>
                                            </label>
                                            <div class="servicestation-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_image('franchise_list', $row['franchise_list'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 展示图片区域 -->
                        <div class="servicestation-edit-form-section servicestation-edit-fade-in-delay-3">
                            <div class="servicestation-edit-section-header">
                                <div class="servicestation-edit-section-icon">
                                    <i class="fa fa-image"></i>
                                </div>
                                <h3 class="servicestation-edit-section-title">展示图片</h3>
                            </div>
                            <div class="servicestation-edit-section-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-picture-o"></i>
                                                展示图片1
                                                <span class="required">*</span>
                                            </label>
                                            <div class="servicestation-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_image('img_url_one', $row['img_url_one'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-picture-o"></i>
                                                展示图片2
                                                <span class="required">*</span>
                                            </label>
                                            <div class="servicestation-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_image('img_url_two', $row['img_url_two'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="servicestation-edit-form-group">
                                            <label class="servicestation-edit-form-label">
                                                <i class="fa fa-picture-o"></i>
                                                展示图片3
                                                <span class="required">*</span>
                                            </label>
                                            <div class="servicestation-edit-upload-section">
                                                <php>
                                                    echo tpl_form_field_image('img_url_three', $row['img_url_three'], '', ['type'=>4, 'extras' => ['text' => 'readonly'], 'tabs' => ['upload' => 'active']]);
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- 备注说明区域 -->
                        <div class="servicestation-edit-form-section servicestation-edit-fade-in-delay-3">
                            <div class="servicestation-edit-section-header">
                                <div class="servicestation-edit-section-icon">
                                    <i class="fa fa-comment"></i>
                                </div>
                                <h3 class="servicestation-edit-section-title">备注说明</h3>
                            </div>
                            <div class="servicestation-edit-section-body">
                                <div class="servicestation-edit-form-group">
                                    <label class="servicestation-edit-form-label">
                                        <i class="fa fa-edit"></i>
                                        备注内容
                                    </label>
                                    <textarea name="content" rows="6" class="servicestation-edit-form-textarea richtext" placeholder="请输入备注说明...">{$row.content}</textarea>
                                    <div class="servicestation-edit-help-text">
                                        <i class="fa fa-info-circle"></i>
                                        可以添加关于服务站的其他说明信息
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮区域 -->
                        <div class="servicestation-edit-submit-section servicestation-edit-fade-in-delay-3">
                            <input type="hidden" name="id" value="{$row.id}"/>
                            <button type="submit" name="submit" class="servicestation-edit-submit-btn">
                                <php>if($row) {</php>
                                <i class="fa fa-save"></i>
                                <span>保存修改</span>
                                <php>} else {</php>
                                <i class="fa fa-plus"></i>
                                <span>添加服务站</span>
                                <php>}</php>
                            </button>
                            <div style="margin-top: 1rem; color: #6b7280; font-size: 1.25rem;">
                                <i class="fa fa-info-circle"></i>
                                请确保所有必填信息已正确填写
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 微信群成员选择弹出层 -->
<div class="wechat-members-modal" id="wechatMembersModal">
    <div class="wechat-members-content">
        <div class="wechat-members-header">
            <h3 class="wechat-members-title">
                <i class="fa fa-users"></i>
                选择群成员
            </h3>
            <button type="button" class="wechat-members-close" onclick="closeWechatMembersModal()">
                <i class="fa fa-times"></i>
            </button>
        </div>
        <div class="wechat-members-body">
            <div class="wechat-members-loading" id="wechatMembersLoading">
                <i class="fa fa-spinner fa-spin"></i>
                正在获取群成员信息...
            </div>
            <div class="wechat-members-list" id="wechatMembersList" style="display: none;">
                <!-- 群成员列表将在这里动态生成 -->
            </div>
        </div>
        <div class="wechat-members-footer" id="wechatMembersFooter" style="display: none;">
            <div class="wechat-members-selected-count">
                已选择 <span id="selectedCount">0</span> 个成员
            </div>
            <div class="wechat-members-actions">
                <button type="button" class="wechat-members-cancel" onclick="closeWechatMembersModal()">
                    取消
                </button>
                <button type="button" class="wechat-members-confirm" onclick="confirmSelectedMembers()">
                    确认选择
                </button>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />

<script>
    $(document).ready(function() {
        // 表单验证增强
        $('#form1').on('submit', function(e) {
            var isValid = true;
            var firstErrorField = null;

            // 检查必填字段
            $(this).find('input[required], select[required], textarea[required]').each(function() {
                var $field = $(this);
                var value = $field.val().trim();

                if (!value) {
                    isValid = false;
                    $field.css('border-color', '#ef4444');

                    if (!firstErrorField) {
                        firstErrorField = $field;
                    }
                } else {
                    $field.css('border-color', '#e5e7eb');
                }
            });

            if (!isValid) {
                e.preventDefault();

                // 滚动到第一个错误字段
                if (firstErrorField) {
                    $('html, body').animate({
                        scrollTop: firstErrorField.offset().top - 100
                    }, 500);
                    firstErrorField.focus();
                }

                // 显示错误提示
                layer.msg('请填写所有必填字段', {icon: 2, time: 3000});
                return false;
            }

            // 显示提交状态
            var $submitBtn = $('.servicestation-edit-submit-btn');
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

            // 如果验证通过，允许表单提交
            return true;
        });

        // 实时验证
        $('input[required], select[required], textarea[required]').on('blur', function() {
            var $field = $(this);
            var value = $field.val().trim();

            if (!value) {
                $field.css('border-color', '#ef4444');
            } else {
                $field.css('border-color', '#10b981');
            }
        });

        // 手机号格式验证
        $('input[name="mobile"]').on('input', function() {
            var value = $(this).val();
            var phoneRegex = /^1[3-9]\d{9}$/;

            if (value && !phoneRegex.test(value)) {
                $(this).css('border-color', '#f59e0b');
            } else if (value) {
                $(this).css('border-color', '#10b981');
            }
        });

        // 身份证号格式验证
        $('input[name="contract_card"]').on('input', function() {
            var value = $(this).val();
            var idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;

            if (value && !idCardRegex.test(value)) {
                $(this).css('border-color', '#f59e0b');
            } else if (value) {
                $(this).css('border-color', '#10b981');
            }
        });

        // 邮箱格式验证
        $('input[name="email"]').on('input', function() {
            var value = $(this).val();
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (value && !emailRegex.test(value)) {
                $(this).css('border-color', '#f59e0b');
            } else if (value) {
                $(this).css('border-color', '#10b981');
            }
        });

        // 统一社会信用代码验证
        $('input[name="credit_code"]').on('input', function() {
            var value = $(this).val();
            var creditCodeRegex = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;

            if (value && !creditCodeRegex.test(value)) {
                $(this).css('border-color', '#f59e0b');
            } else if (value) {
                $(this).css('border-color', '#10b981');
            }
        });

        // 文件上传区域增强
        $('.servicestation-edit-upload-section').on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('drag-over');
        });

        $('.servicestation-edit-upload-section').on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('drag-over');
        });

        // 添加拖拽样式
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .servicestation-edit-upload-section.drag-over {
                    border-color: #667eea !important;
                    background: #f0f9ff !important;
                    transform: scale(1.02);
                }
            `)
            .appendTo('head');

        // 表单字段动画效果
        $('.servicestation-edit-form-input, .servicestation-edit-form-select, .servicestation-edit-form-textarea').on('focus', function() {
            $(this).parent().addClass('field-focused');
        });

        $('.servicestation-edit-form-input, .servicestation-edit-form-select, .servicestation-edit-form-textarea').on('blur', function() {
            $(this).parent().removeClass('field-focused');
        });

        // 添加聚焦样式
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .field-focused {
                    transform: translateY(-2px);
                    transition: all 0.3s ease;
                }
            `)
            .appendTo('head');

        // 进度指示器
        var totalSections = $('.servicestation-edit-form-section').length;
        var completedSections = 0;

        function updateProgress() {
            var progress = (completedSections / totalSections) * 100;
            // 这里可以添加进度条显示逻辑
        }

        // 检查每个区域的完成状态
        $('.servicestation-edit-form-section').each(function() {
            var $section = $(this);
            var requiredFields = $section.find('input[required], select[required], textarea[required]');
            var filledFields = 0;

            requiredFields.each(function() {
                var value = $(this).val().trim();
                if (value && value !== '0') {
                    filledFields++;
                }
            });

            if (filledFields === requiredFields.length && requiredFields.length > 0) {
                completedSections++;
                $section.addClass('section-completed');
            }
        });

        updateProgress();

        // 添加完成状态样式
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .section-completed .servicestation-edit-section-header {
                    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%) !important;
                }
                .section-completed .servicestation-edit-section-icon {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
                }
            `)
            .appendTo('head');

        // 滚动到顶部功能
        var $scrollToTop = $('<button class="scroll-to-top" style="position: fixed; bottom: 2rem; right: 2rem; width: 3rem; height: 3rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 50%; font-size: 1.25rem; cursor: pointer; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; display: none; transition: all 0.3s ease;"><i class="fa fa-arrow-up"></i></button>');
        $('body').append($scrollToTop);

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $scrollToTop.fadeIn();
            } else {
                $scrollToTop.fadeOut();
            }
        });

        $scrollToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 600);
        });

        // 自动保存功能（可选）
        var autoSaveTimer;
        $('.servicestation-edit-form-input, .servicestation-edit-form-select, .servicestation-edit-form-textarea').on('input change', function() {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(function() {
                // 这里可以添加自动保存逻辑
                console.log('Auto save triggered');
            }, 5000); // 5秒后自动保存
        });
    });

    // 原有的富文本编辑器和图片压缩功能
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
            u.editor($('.richtext1')[0]);
        })
    });

    require(['primeImageCompressor'], function(primeImageCompressor) {
        primeImageCompressor.init();
    });

    // 微信群成员管理功能
    var selectedMembers = [];

    // 获取微信群成员
    function getWechatMembers() {
        var chatroomId = $('input[name="chatroom"]').val().trim();

        if (!chatroomId || chatroomId === '无') {
            layer.msg('请先填写对接群@chatroomID', {icon: 2});
            return;
        }

        // 显示弹出层
        $('#wechatMembersModal').addClass('show');
        $('#wechatMembersLoading').show();
        $('#wechatMembersList').hide();
        $('#wechatMembersFooter').hide();

        // 重置选择状态
        selectedMembers = [];
        updateSelectedCount();

        // 构建POST数据
        var postData = {
            "WxID": "zcgk666",
            "Data": {
                "UserName": chatroomId,
                "RoomVersion": 0,
                "ArgList": ["UserName", "RoomVersion"],
                "AnyCallName": "GetChatRoomMember",
                "TimeOut": -1,
                "CallName": "Any"
            },
            "CallBackUrl": null
        };

        // 添加详细的调试信息
        console.log('发送请求到API:', 'http://43.139.30.237:1080/HTTPManagement.aspx?Item=AddMessage');
        console.log('POST数据:', JSON.stringify(postData, null, 2));
        console.log('群ID:', chatroomId);

        // 使用后端代理接口发送请求，避免跨域问题
        console.log('使用后端代理接口发送请求...');

        $.ajax({
            url: '{:U("getWechatMembers")}',
            type: 'POST',
            data: postData,
            dataType: 'json',
            timeout: 30000,
            beforeSend: function(xhr) {
                console.log('准备发送代理请求...');
            },
            success: function(response) {
                console.log('代理接口响应成功 - 原始响应:', response);
                console.log('响应类型:', typeof response);

                try {
                    var data = response;
                    console.log('解析后的数据:', data);

                    // 检查代理接口是否返回错误
                    if (data && data.ErrCode && data.ErrCode < 0) {
                        console.error('代理接口错误:', data);
                        layer.msg('代理接口错误: ' + (data.ErrMsg || '未知错误'), {icon: 2});
                        closeWechatMembersModal();
                        return;
                    }

                    // 根据实际返回格式解析数据
                    if (data && data.ErrCode === 0 && data.Return && data.Return.Data && data.Return.Data.SendReturn && data.Return.Data.SendReturn.member) {
                        var memberList = data.Return.Data.SendReturn.member;
                        var memberCount = data.Return.Data.SendReturn.count || memberList.length;

                        console.log('解析到群成员数量:', memberCount, '实际成员列表:', memberList);
                        displayWechatMembers(memberList);
                    } else {
                        console.error('数据格式不正确或有错误:', data);
                        var errorMsg = '未获取到群成员信息';

                        // 检查是否有错误信息
                        if (data && data.ErrCode !== 0) {
                            errorMsg = '获取失败，错误代码: ' + data.ErrCode;
                            if (data.Return && data.Return.ErrStr) {
                                errorMsg += ' - ' + data.Return.ErrStr;
                            } else if (data.ErrMsg) {
                                errorMsg += ' - ' + data.ErrMsg;
                            }
                        }

                        layer.msg(errorMsg, {icon: 2});
                        closeWechatMembersModal();
                    }
                } catch (e) {
                    console.error('解析响应数据失败:', e);
                    console.error('原始响应内容:', response);
                    layer.msg('数据解析失败: ' + e.message, {icon: 2});
                    closeWechatMembersModal();
                }
            },
            error: function(xhr, status, error) {
                console.error('代理接口请求失败 - 详细信息:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    readyState: xhr.readyState,
                    statusCode: xhr.status,
                    statusText: xhr.statusText
                });

                var errorMsg = '获取群成员失败';
                var detailMsg = '';

                if (status === 'timeout') {
                    errorMsg = '请求超时';
                    detailMsg = '代理接口响应超时，请稍后重试';
                } else if (status === 'error') {
                    if (xhr.status === 404) {
                        errorMsg = '代理接口不存在';
                        detailMsg = '请检查控制器方法是否正确';
                    } else if (xhr.status === 500) {
                        errorMsg = '服务器内部错误';
                        detailMsg = '代理接口执行失败，请检查服务器日志';
                    } else {
                        errorMsg = 'HTTP错误';
                        detailMsg = '状态码: ' + xhr.status + ' - ' + xhr.statusText;
                    }
                } else if (status === 'parsererror') {
                    errorMsg = '数据解析错误';
                    detailMsg = '代理接口返回的数据格式不正确';
                } else {
                    errorMsg = '未知错误';
                    detailMsg = 'Status: ' + status + ', Error: ' + error;
                }

                // 显示详细错误信息
                console.log('显示错误信息:', errorMsg, detailMsg);
                layer.msg(errorMsg + (detailMsg ? '<br><small>' + detailMsg + '</small>' : ''), {
                    icon: 2,
                    time: 8000,
                    area: ['450px', 'auto']
                });

                closeWechatMembersModal();
            }
            });
    }

    // 显示群成员列表
    function displayWechatMembers(memberList) {
        $('#wechatMembersLoading').hide();

        if (!memberList || memberList.length === 0) {
            $('#wechatMembersList').html('<div style="text-align: center; padding: 2rem; color: #666;">暂无群成员信息</div>').show();
            return;
        }

        var html = '';
        memberList.forEach(function(member, index) {
            // 根据实际返回格式获取字段
            var userName = member.user_name || '';
            var nickName = member.nick_name || member.chatroom_nick_name || userName;
            var avatarUrl = member.bigHeadImgUrl || '';

            // 转义特殊字符，防止XSS和JavaScript错误
            var escapedUserName = userName.replace(/'/g, "\\'").replace(/"/g, '\\"');
            var escapedNickName = nickName.replace(/'/g, "\\'").replace(/"/g, '\\"');
            var displayNickName = nickName || userName;

            html += '<div class="wechat-member-item" onclick="toggleMemberSelection(\'' + escapedUserName + '\', \'' + escapedNickName + '\')">';
            html += '<input type="checkbox" class="wechat-member-checkbox" id="member_' + index + '" data-username="' + escapedUserName + '" data-nickname="' + escapedNickName + '">';

            // 如果有头像，显示头像
            if (avatarUrl) {
                html += '<img src="' + avatarUrl + '" class="wechat-member-avatar" alt="头像" onerror="this.style.display=\'none\'">';
            }

            html += '<div class="wechat-member-name" title="用户名: ' + userName + '&#10;昵称: ' + displayNickName + '">';
            html += '<div class="wechat-member-nickname">' + displayNickName + '</div>';
            html += '<div class="wechat-member-username">@' + userName + '</div>';
            html += '</div>';
            html += '</div>';
        });

        $('#wechatMembersList').html(html).show();
        $('#wechatMembersFooter').show();

        // 显示获取到的成员数量
        layer.msg('成功获取到 ' + memberList.length + ' 个群成员', {icon: 1, time: 2000});
    }

    // 切换成员选择状态
    function toggleMemberSelection(userName, nickName) {
        var checkbox = $('input[data-username="' + userName + '"]');
        var memberItem = checkbox.closest('.wechat-member-item');

        if (checkbox.prop('checked')) {
            // 取消选择
            checkbox.prop('checked', false);
            memberItem.removeClass('selected');
            selectedMembers = selectedMembers.filter(function(member) {
                return member.userName !== userName;
            });
        } else {
            // 选择
            checkbox.prop('checked', true);
            memberItem.addClass('selected');
            selectedMembers.push({
                userName: userName,
                nickName: nickName
            });
        }

        updateSelectedCount();
    }

    // 更新选择计数
    function updateSelectedCount() {
        $('#selectedCount').text(selectedMembers.length);
    }

    // 确认选择的成员
    function confirmSelectedMembers() {
        if (selectedMembers.length === 0) {
            layer.msg('请至少选择一个群成员', {icon: 2});
            return;
        }

        // 提取用户名并用逗号连接
        var userNames = selectedMembers.map(function(member) {
            return member.userName;
        }).join('|');

        // 填入输入框
        $('input[name="wxatuserlist"]').val(userNames);

        // 显示成功消息
        layer.msg('已选择 ' + selectedMembers.length + ' 个群成员', {icon: 1});

        // 关闭弹出层
        closeWechatMembersModal();
    }

    // 关闭弹出层
    function closeWechatMembersModal() {
        $('#wechatMembersModal').removeClass('show');
        selectedMembers = [];
        updateSelectedCount();
    }

    // 点击弹出层背景关闭
    $('#wechatMembersModal').on('click', function(e) {
        if (e.target === this) {
            closeWechatMembersModal();
        }
    });

    // ESC键关闭弹出层
    $(document).on('keydown', function(e) {
        if (e.keyCode === 27 && $('#wechatMembersModal').hasClass('show')) {
            closeWechatMembersModal();
        }
        if (e.keyCode === 27 && $('#chatroomListModal').hasClass('show')) {
            closeChatroomList();
        }
    });

    // 获取群列表
    function getChatroomList() {
        // 显示弹出层
        $('#chatroomListModal').addClass('show');
        $('#chatroomListLoading').show();
        $('#chatroomListContainer').hide();
        $('#chatroomListError').hide();

        // 构建POST数据
        var postData = {
            "WxID": "zcgk666",
            "Data": {
                "CallName": "GetUserList"
            }
        };

        $.ajax({
            url: '{:U("getChatroomList")}',
            type: 'POST',
            data: postData,
            dataType: 'json',
            timeout: 30000,
            success: function(response) {
                // 检查代理接口是否返回错误
                if (response && response.ErrCode && response.ErrCode < 0) {
                    showChatroomListError();
                    return;
                }

                // 处理正常响应
                if (response && response.ErrCode === 0 && response.Return && response.Return.Data &&
                    response.Return.Data.UserList) {
                    var chatroomList = response.Return.Data.UserList;
                    displayChatroomList(chatroomList);
                } else {
                    showChatroomListError();
                }
            },
            error: function(xhr, status, error) {
                showChatroomListError();
            }
        });
    }

    // 显示群列表
    function displayChatroomList(chatroomList) {
        $('#chatroomListLoading').hide();
        $('#chatroomListError').hide();

        var container = $('#chatroomListContainer');
        container.empty();

        if (chatroomList.length === 0) {
            container.html('<div style="text-align: center; padding: 2rem; color: #666;">暂无群聊数据</div>');
        } else {
            chatroomList.forEach(function(chatroom) {
                var userName = chatroom.UserName || '';
                var nickName = chatroom.NickName || userName;

                // 生成头像字母（取昵称第一个字符）
                var avatarLetter = nickName.charAt(0).toUpperCase();

                var chatroomHtml = `
                    <div class="chatroom-item" onclick="selectChatroom('${userName}', '${nickName}')">
                        <div class="chatroom-avatar">${avatarLetter}</div>
                        <div class="chatroom-info">
                            <div class="chatroom-name">${nickName}</div>
                            <div class="chatroom-id">${userName}</div>
                        </div>
                    </div>
                `;
                container.append(chatroomHtml);
            });
        }

        container.show();
    }

    // 选择群聊
    function selectChatroom(userName, nickName) {
        // 移除其他选中状态
        $('.chatroom-item').removeClass('selected');

        // 添加当前选中状态
        event.currentTarget.classList.add('selected');

        // 填入输入框
        $('input[name="chatroom"]').val(userName);

        // 延迟关闭弹出层，让用户看到选中效果
        setTimeout(function() {
            closeChatroomList();
        }, 300);
    }

    // 显示群列表错误
    function showChatroomListError() {
        $('#chatroomListLoading').hide();
        $('#chatroomListContainer').hide();
        $('#chatroomListError').show();
    }

    // 关闭群列表弹出层
    function closeChatroomList() {
        $('#chatroomListModal').removeClass('show');
    }

    // 点击弹出层背景关闭
    $('#chatroomListModal').on('click', function(e) {
        if (e.target === this) {
            closeChatroomList();
        }
    });


</script>

<!-- 群列表弹出层 -->
<div class="chatroom-list-modal" id="chatroomListModal">
    <div class="chatroom-list-content">
        <div class="chatroom-list-header">
            <h3><i class="fa fa-list"></i> 选择群聊</h3>
            <button class="chatroom-list-close" onclick="closeChatroomList()">
                <i class="fa fa-times"></i>
            </button>
        </div>
        <div class="chatroom-list-body">
            <div class="chatroom-list-loading" id="chatroomListLoading">
                <i class="fa fa-spinner fa-spin"></i>
                <span>正在获取群列表...</span>
            </div>
            <div class="chatroom-list-container" id="chatroomListContainer" style="display: none;">
                <!-- 群列表将在这里动态生成 -->
            </div>
            <div class="chatroom-list-error" id="chatroomListError" style="display: none;">
                <i class="fa fa-exclamation-triangle"></i>
                <span>获取群列表失败，请重试</span>
            </div>
        </div>
        <div class="chatroom-list-footer">
            <button class="chatroom-list-btn chatroom-list-btn-cancel" onclick="closeChatroomList()">
                取消
            </button>
        </div>
    </div>
</div>