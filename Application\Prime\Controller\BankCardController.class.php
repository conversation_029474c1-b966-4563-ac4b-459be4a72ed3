<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 后台银行卡管理
 */
class BankCardController extends PrimeController
{

    /**
     * 银行卡列表
     */
    public function index()
    {
        $bankCardModel = D("BankCard");
        $where = [];

        // 搜索条件
        $serviceStationName = I('get.service_station_name');
        $cardNumber = I('get.card_number');
        $status = I('get.status', ''); // Allow filtering by status (e.g., 1 for enabled, 0 for disabled)
        // 移除卡类型筛选
        // $cardType = I('get.card_type', ''); // 允许按卡类型筛选

        if (!empty($serviceStationName)) {
            $stationIds = D('ServiceStation')->where(['service_name' => ['like', '%' . $serviceStationName . '%']])->getField('id', true);
            if ($stationIds) {
                $where['bc.service_station_id'] = ['in', $stationIds];
            } else {
                $where['bc.service_station_id'] = -1;
            }
        }
        if (!empty($cardNumber)) {
            $where['bc.card_number'] = ['like', '%' . $cardNumber . '%'];
        }
        if ($status !== '') {
            $where['bc.status'] = intval($status);
        }
        // 移除卡类型筛选
        // if ($cardType !== '') {
        //    $where['bc.card_type'] = intval($cardType);
        // }

        $count = $bankCardModel->alias('bc')->where($where)->count();
        $page = $this->page($count, 20);

        $list = $bankCardModel->alias('bc')
            ->join('LEFT JOIN __SERVICE_STATION__ ss ON bc.service_station_id = ss.id')
            ->join('LEFT JOIN __USERS__ admin ON bc.added_by_admin_id = admin.id')
            ->field('bc.*, ss.service_name, admin.username as added_by_admin_name')
            ->where($where)
            ->limit($page->firstRow . ',' . $page->listRows)
            ->order('bc.id DESC')
            ->select();

         // 卡号脱敏
         if ($list) {
             foreach ($list as &$item) {
                 if (!empty($item['card_number'])) {
                     $item['card_number_masked'] = substr($item['card_number'], 0, 4) . ' **** **** ' . substr($item['card_number'], -4);
                 } else {
                      $item['card_number_masked'] = 'N/A';
                  }
             }
             unset($item);
         }

        $this->assign('list', $list);
        $this->assign('statusList', $bankCardModel->status);
        $this->assign('page', $page->show());

        // 解决左侧菜单问题：手动设置当前菜单
        $menuModel = D("Menu");

        // 查找BankCard/index菜单项
        $menuItem = $menuModel->where(['app' => 'Prime', 'model' => 'BankCard', 'action' => 'index'])->find();
        \Think\Log::write('Searching for menu: app=Prime, model=BankCard, action=index. Result: '.($menuItem ? 'Found ID='.$menuItem['id'] : 'Not Found'), 'INFO');

        if ($menuItem) {
            // 手动构建完整的菜单信息
            $parentId = $menuItem['parentid'];
            $bootId = 0;

            \Think\Log::write('Menu item parent ID: '.$parentId, 'INFO');

            // 向上查找父级菜单，直到找到根菜单（parentid=0的菜单）
            if ($parentId > 0) {
                $bootId = $menuModel->getBoot($parentId);
                \Think\Log::write('Menu bootId for BankCard/index: '.$bootId, 'INFO');
            } else {
                $bootId = $menuItem['id']; // 如果当前就是顶级菜单
                \Think\Log::write('Menu item is top level, using its ID as bootId: '.$bootId, 'INFO');
            }

            // 构建完整的菜单信息
            $menuInfo = [
                'id' => $menuItem['id'],
                'parentid' => $parentId,
                'boot_id' => $bootId ? $bootId : $menuItem['id'] // 如果bootId为0，则使用当前菜单ID
            ];

            \Think\Log::write('Setting menu info: '.json_encode($menuInfo), 'INFO');

            // 重新分配菜单变量到模板
            $this->assign('cur_menu', $menuInfo);

            // 确保main_menus变量中包含这个boot_id
            $mainMenus = session('main_menus');
            if ($mainMenus && !isset($mainMenus[$menuInfo['boot_id']])) {
                \Think\Log::write('Warning: boot_id '.$menuInfo['boot_id'].' not found in main_menus session variable', 'WARN');
                // 获取菜单树并重新设置session
                $mainMenus = $menuModel->menu_json();
                session('main_menus', $mainMenus);
                \Think\Log::write('Refreshed main_menus in session', 'INFO');
            }
        } else {
            \Think\Log::write('Failed to find menu item for BankCard/index', 'WARN');
        }

        $this->display();
    }

    /**
     * 添加银行卡
     */
    public function add()
    {
        if (IS_POST) {
            $bankCardModel = D("BankCard");

            // 调试：记录原始POST数据
            \Think\Log::write('BankCard/add POST data: ' . json_encode($_POST), 'DEBUG');

            // 手动处理表单数据
            $bankName = I('post.bank_name');
            $bankSelect = I('post.bank_select');
            $bankNameConfirm = I('post.bank_name_confirm');

            // 记录所有POST数据，便于调试
            \Think\Log::write('所有POST数据: ' . json_encode($_POST), 'DEBUG');
            \Think\Log::write('银行名称字段: bank_name=' . $bankName . ', bank_select=' . $bankSelect . ', bank_name_confirm=' . $bankNameConfirm, 'DEBUG');

            // 如果bank_name为空但bank_name_confirm有值，则使用bank_name_confirm
            if (empty($bankName) && !empty($bankNameConfirm)) {
                $bankName = $bankNameConfirm;
                \Think\Log::write('使用bank_name_confirm作为银行名称: ' . $bankName, 'DEBUG');
            }
            // 如果bank_name仍为空但bank_select有值且不是"other"，则使用bank_select
            else if (empty($bankName) && !empty($bankSelect) && $bankSelect !== 'other') {
                $bankName = $bankSelect;
                \Think\Log::write('使用bank_select作为银行名称: ' . $bankName, 'DEBUG');
            }

            // 确保银行名称不为空
            if (empty($bankName)) {
                $this->error("请选择或输入银行名称");
            }

            $data = [
                'service_station_id' => I('post.service_station_id'),
                'bank_name' => $bankName,
                'card_number' => str_replace(' ', '', I('post.card_number')), // 移除空格
                'account_holder' => I('post.account_holder'),
                'bank_branch' => I('post.bank_branch'),
                'is_default' => I('post.is_default', 0, 'intval'),
                'status' => I('post.status', 1, 'intval'),
                'added_by_admin_id' => session('admin_id'),
                'created_at' => time(),
                'updated_at' => time()
            ];

            // 调试：记录处理后的数据
            \Think\Log::write('BankCard/add processed data: ' . json_encode($data), 'DEBUG');

            // 验证必填字段
            if (empty($data['service_station_id'])) {
                $this->error("请选择关联服务站");
            }
            if (empty($data['bank_name'])) {
                $this->error("请选择或输入银行名称");
            }
            if (empty($data['card_number'])) {
                $this->error("请输入银行卡号");
            }
            if (empty($data['account_holder'])) {
                $this->error("请输入开户人姓名");
            }

            // 检查是否已存在相同的卡号
            $existing = $bankCardModel->where(['card_number' => $data['card_number']])->find();
            if ($existing) {
                $this->error("该银行卡号已存在！");
            }

            // 检查关联的服务站是否存在
            $stationExists = D('ServiceStation')->where(['id' => $data['service_station_id']])->count();
            if (!$stationExists) {
                $this->error("关联的服务站不存在！");
            }

            // 直接使用add方法添加数据
            $insertId = $bankCardModel->add($data);
            if ($insertId) {
                // 如果设置为默认卡，取消该服务站其他卡的默认设置
                if ($data['is_default']) {
                    $bankCardModel->where([
                        'service_station_id' => $data['service_station_id'],
                        'id' => ['neq', $insertId]
                    ])->save(['is_default' => 0]);
                }
                $this->success("添加成功", U('index'));
            } else {
                $this->error("添加失败: " . $bankCardModel->getError());
            }
        } else {
            // 获取服务站列表供选择
            $stationList = D('ServiceStation')->where(['status' => 1])->field('id, service_name')->order('id desc')->select();
            $this->assign('stationList', $stationList);
            $this->assign('statusList', D("BankCard")->status);

            // 解决左侧菜单问题：手动设置当前菜单
            $menuModel = D("Menu");

            // 查找BankCard/index菜单项（这里使用index作为基础，因为add可能没有单独的菜单项）
            $menuItem = $menuModel->where(['app' => 'Prime', 'model' => 'BankCard', 'action' => 'index'])->find();
            \Think\Log::write('Searching for menu: app=Prime, model=BankCard, action=index. Result: '.($menuItem ? 'Found ID='.$menuItem['id'] : 'Not Found'), 'INFO');

            if ($menuItem) {
                // 手动构建完整的菜单信息
                $parentId = $menuItem['parentid'];
                $bootId = 0;

                \Think\Log::write('Menu item parent ID: '.$parentId, 'INFO');

                // 向上查找父级菜单，直到找到根菜单（parentid=0的菜单）
                if ($parentId > 0) {
                    $bootId = $menuModel->getBoot($parentId);
                    \Think\Log::write('Menu bootId for BankCard/index: '.$bootId, 'INFO');
                } else {
                    $bootId = $menuItem['id']; // 如果当前就是顶级菜单
                    \Think\Log::write('Menu item is top level, using its ID as bootId: '.$bootId, 'INFO');
                }

                // 构建完整的菜单信息
                $menuInfo = [
                    'id' => $menuItem['id'],
                    'parentid' => $parentId,
                    'boot_id' => $bootId ? $bootId : $menuItem['id'] // 如果bootId为0，则使用当前菜单ID
                ];

                \Think\Log::write('Setting menu info: '.json_encode($menuInfo), 'INFO');

                // 重新分配菜单变量到模板
                $this->assign('cur_menu', $menuInfo);

                // 确保main_menus变量中包含这个boot_id
                $mainMenus = session('main_menus');
                if ($mainMenus && !isset($mainMenus[$menuInfo['boot_id']])) {
                    \Think\Log::write('Warning: boot_id '.$menuInfo['boot_id'].' not found in main_menus session variable', 'WARN');
                    // 获取菜单树并重新设置session
                    $mainMenus = $menuModel->menu_json();
                    session('main_menus', $mainMenus);
                    \Think\Log::write('Refreshed main_menus in session', 'INFO');
                }
            } else {
                \Think\Log::write('Failed to find menu item for BankCard/index', 'WARN');
            }

            $this->display();
        }
    }

    /**
     * 编辑银行卡
     */
    public function edit()
    {
        $id = I('request.id', 0, 'intval');
         if (!$id) {
             $this->error('参数错误:缺少ID');
         }
         $bankCardModel = D("BankCard");

        if (IS_POST) {
            // 调试：记录原始POST数据
            \Think\Log::write('BankCard/edit POST data: ' . json_encode($_POST), 'DEBUG');

            // 手动处理表单数据
            $bankName = I('post.bank_name');
            $bankSelect = I('post.bank_select');
            $bankNameConfirm = I('post.bank_name_confirm');

            // 记录所有POST数据，便于调试
            \Think\Log::write('所有POST数据(编辑): ' . json_encode($_POST), 'DEBUG');
            \Think\Log::write('银行名称字段(编辑): bank_name=' . $bankName . ', bank_select=' . $bankSelect . ', bank_name_confirm=' . $bankNameConfirm, 'DEBUG');

            // 如果bank_name为空但bank_name_confirm有值，则使用bank_name_confirm
            if (empty($bankName) && !empty($bankNameConfirm)) {
                $bankName = $bankNameConfirm;
                \Think\Log::write('使用bank_name_confirm作为银行名称(编辑): ' . $bankName, 'DEBUG');
            }
            // 如果bank_name仍为空但bank_select有值且不是"other"，则使用bank_select
            else if (empty($bankName) && !empty($bankSelect) && $bankSelect !== 'other') {
                $bankName = $bankSelect;
                \Think\Log::write('使用bank_select作为银行名称(编辑): ' . $bankName, 'DEBUG');
            }

            // 确保银行名称不为空
            if (empty($bankName)) {
                $this->error("请选择或输入银行名称");
            }

            $data = [
                'id' => $id,
                'service_station_id' => I('post.service_station_id'),
                'bank_name' => $bankName,
                'card_number' => str_replace(' ', '', I('post.card_number')), // 移除空格
                'account_holder' => I('post.account_holder'),
                'bank_branch' => I('post.bank_branch'),
                'is_default' => I('post.is_default', 0, 'intval'),
                'status' => I('post.status', 1, 'intval'),
                'updated_at' => time()
            ];

            // 调试：记录处理后的数据
            \Think\Log::write('BankCard/edit processed data: ' . json_encode($data), 'DEBUG');

            // 验证必填字段
            if (empty($data['service_station_id'])) {
                $this->error("请选择关联服务站");
            }
            if (empty($data['bank_name'])) {
                $this->error("请选择或输入银行名称");
            }
            if (empty($data['card_number'])) {
                $this->error("请输入银行卡号");
            }
            if (empty($data['account_holder'])) {
                $this->error("请输入开户人姓名");
            }

            // 检查卡号唯一性 (排除自身)
            $existing = $bankCardModel->where(['card_number' => $data['card_number'], 'id' => ['neq', $id]])->find();
            if ($existing) {
                $this->error("该银行卡号已被其他记录使用！");
            }

            // 检查关联的服务站是否存在
            $stationExists = D('ServiceStation')->where(['id' => $data['service_station_id']])->count();
            if (!$stationExists) {
                $this->error("关联的服务站不存在！");
            }

            // 直接使用save方法更新数据
            if ($bankCardModel->save($data) !== false) {
                // 如果设置为默认卡，取消该服务站其他卡的默认设置
                if ($data['is_default']) {
                    $bankCardModel->where([
                        'service_station_id' => $data['service_station_id'],
                        'id' => ['neq', $id]
                    ])->save(['is_default' => 0]);
                }
                $this->success("保存成功", U('index'));
            } else {
                $this->error("保存失败: " . $bankCardModel->getError());
            }
        } else {
            $info = $bankCardModel->find($id);
             if (!$info) {
                 $this->error('记录不存在');
             }
            // 获取服务站列表
             $stationList = D('ServiceStation')->where(['status' => 1])->field('id, service_name')->order('id desc')->select();
             $this->assign('stationList', $stationList);
            $this->assign('info', $info);
             $this->assign('statusList', D("BankCard")->status);

            // 解决左侧菜单问题：手动设置当前菜单
            $menuModel = D("Menu");

            // 查找BankCard/index菜单项（这里使用index作为基础，因为edit可能没有单独的菜单项）
            $menuItem = $menuModel->where(['app' => 'Prime', 'model' => 'BankCard', 'action' => 'index'])->find();
            \Think\Log::write('Searching for menu: app=Prime, model=BankCard, action=index. Result: '.($menuItem ? 'Found ID='.$menuItem['id'] : 'Not Found'), 'INFO');

            if ($menuItem) {
                // 手动构建完整的菜单信息
                $parentId = $menuItem['parentid'];
                $bootId = 0;

                \Think\Log::write('Menu item parent ID: '.$parentId, 'INFO');

                // 向上查找父级菜单，直到找到根菜单（parentid=0的菜单）
                if ($parentId > 0) {
                    $bootId = $menuModel->getBoot($parentId);
                    \Think\Log::write('Menu bootId for BankCard/index: '.$bootId, 'INFO');
                } else {
                    $bootId = $menuItem['id']; // 如果当前就是顶级菜单
                    \Think\Log::write('Menu item is top level, using its ID as bootId: '.$bootId, 'INFO');
                }

                // 构建完整的菜单信息
                $menuInfo = [
                    'id' => $menuItem['id'],
                    'parentid' => $parentId,
                    'boot_id' => $bootId ? $bootId : $menuItem['id'] // 如果bootId为0，则使用当前菜单ID
                ];

                \Think\Log::write('Setting menu info: '.json_encode($menuInfo), 'INFO');

                // 重新分配菜单变量到模板
                $this->assign('cur_menu', $menuInfo);

                // 确保main_menus变量中包含这个boot_id
                $mainMenus = session('main_menus');
                if ($mainMenus && !isset($mainMenus[$menuInfo['boot_id']])) {
                    \Think\Log::write('Warning: boot_id '.$menuInfo['boot_id'].' not found in main_menus session variable', 'WARN');
                    // 获取菜单树并重新设置session
                    $mainMenus = $menuModel->menu_json();
                    session('main_menus', $mainMenus);
                    \Think\Log::write('Refreshed main_menus in session', 'INFO');
                }
            } else {
                \Think\Log::write('Failed to find menu item for BankCard/index', 'WARN');
            }

            $this->display();
        }
    }

    /**
     * 删除银行卡 (真实删除)
     */
    public function delete()
    {
        $id = I('get.id', 0, 'intval');
        if ($id) {
            $bankCardModel = D("BankCard");
            // 真实删除
            if ($bankCardModel->delete($id) !== false) {
                $this->success("删除成功！", U('index'));
            } else {
                $this->error("删除失败！");
            }

            // 改状态为禁用 - 已移除
            // $result = $bankCardModel->where(['id' => $id])->save(['status' => 0]);
            // if ($result !== false) {
            //     $this->success("禁用成功！", U('index'));
            // } else {
            //     $this->error("操作失败！");
            // }
        } else {
            $this->error('参数错误:缺少ID');
        }
    }

     /**
      * 启用银行卡
      */
     public function enable() {
         $id = I('get.id', 0, 'intval');
         if ($id) {
             $result = D("BankCard")->where(['id' => $id])->save(['status' => 1]);
             if ($result !== false) {
                 $this->success("启用成功！", U('index'));
             } else {
                 $this->error("操作失败！");
             }
         } else {
             $this->error('参数错误:缺少ID');
         }
     }
}