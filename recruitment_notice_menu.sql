-- 招聘公告智能筛选系统菜单配置
-- 请在数据库中执行以下SQL语句来添加菜单

-- 1. 添加招聘公告管理主菜单（父级为项目管理 ID=262）
INSERT INTO z_menu (parentid, app, model, action, data, type, status, name, icon, remark, sort) VALUES
(262, 'Prime', 'RecruitmentNotice', 'index', '', 1, 1, '招聘公告管理', '', '智能招聘公告筛选系统', 8);

-- 获取刚插入的主菜单ID
SET @main_menu_id = LAST_INSERT_ID();

-- 2. 添加招聘公告编辑菜单（隐藏）
INSERT INTO z_menu (parentid, app, model, action, data, type, status, name, icon, remark, sort) VALUES
(@main_menu_id, 'Prime', 'RecruitmentNotice', 'edit', '', 0, 0, '编辑招聘公告', '', '添加和编辑招聘公告', 1);

-- 3. 添加岗位管理菜单（隐藏）
INSERT INTO z_menu (parentid, app, model, action, data, type, status, name, icon, remark, sort) VALUES
(@main_menu_id, 'Prime', 'RecruitmentNotice', 'posts', '', 0, 0, '岗位管理', '', '管理招聘公告下的岗位', 2);

-- 4. 添加岗位要求设置菜单（隐藏）
INSERT INTO z_menu (parentid, app, model, action, data, type, status, name, icon, remark, sort) VALUES
(@main_menu_id, 'Prime', 'RecruitmentNotice', 'requirements', '', 0, 0, '岗位要求设置', '', '设置岗位筛选要求', 3);

-- 5. 添加批量要求设置菜单（隐藏）
INSERT INTO z_menu (parentid, app, model, action, data, type, status, name, icon, remark, sort) VALUES
(@main_menu_id, 'Prime', 'RecruitmentNotice', 'batchRequirements', '', 0, 0, '批量要求设置', '', '批量设置多个岗位的要求', 4);

-- 6. 添加匹配结果菜单（隐藏）
INSERT INTO z_menu (parentid, app, model, action, data, type, status, name, icon, remark, sort) VALUES
(@main_menu_id, 'Prime', 'RecruitmentNotice', 'matchResults', '', 0, 0, '匹配结果', '', '查看简历匹配结果', 5);

-- 7. 添加执行匹配菜单（隐藏）
INSERT INTO z_menu (parentid, app, model, action, data, type, status, name, icon, remark, sort) VALUES
(@main_menu_id, 'Prime', 'RecruitmentNotice', 'executeMatch', '', 0, 0, '执行匹配', '', '执行简历智能匹配', 6);

-- 注意：执行完成后，请清除菜单缓存或重新登录以使菜单生效
