<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Userjob/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="panel panel-info">
                    <div class="panel-body">
                       简历人：{:$userJobRow['name']}
                    </div>
                </div>
                <form action="" method="post" >
                    <div class="panel panel-default">
                        <div class="panel-body table-responsive">
                            <table class="table table-hover">
                                <thead class="navbar-inner">
                                    <tr>
                                        <th width="75px">#ID</th>
                                        <th>开始时间</th>
                                        <th>结束时间</th>
                                        <th>学校</th>
                                        <th>专业</th>
                                        <th>证明人</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($list as $v) { </php>
                                    <tr>
                                        <td>{$v.id}</td>
                                        <td>{:$v['start_date']}</td>
                                        <td>{:$v['end_date']}</td>
                                        <td>{:$v['school_name']}</td>
                                        <td>{:$v['major']}</td>
                                        <td>{:$v['witness']}</td>
                                        <td>
                                            <a href="{:U('userjob/educationedit', ['id' => $v['id']])}" class="btn btnc btn-primary">编辑</a><br>
                                        </td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                            {$page}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />

<script>
	require(["daterangepicker"], function($){

		$('.js_status').click(function() {
			var that = $(this),
				url = that.attr('url');
			$.get(url, function(data) {
				window.location.reload();
			});

		})
	});
</script>
