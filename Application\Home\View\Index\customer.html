﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>关注公众号</title>
    <style>
        * {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
        }

        .container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            box-sizing: border-box;
        }

        .qrcode-card {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(20px, 5vw, 40px);
            box-shadow: 0 6px 30px rgba(0, 0, 0, 0.08);
            text-align: center;
            width: min(90%, 400px);
            margin: 20px;
            position: relative;
        }
        .qrcode-card:active {
            transform: scale(0.98);
            transition: transform 0.1s;
        }
        .qrcode-img {
            width: 100%;
            aspect-ratio: 1/1;
            margin: 0 auto;
            position: relative;
        }

        .qrcode-img img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .instruction {
            color: #666;
            font-size: clamp(14px, 3.5vw, 16px);
            margin: 25px 0 15px;
            line-height: 1.6;
            padding: 0 10px;
        }

        .tips {
            color: #999;
            font-size: clamp(12px, 3vw, 14px);
            margin-top: 30px;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.9; }
            50% { opacity: 0.6; }
            100% { opacity: 0.9; }
        }

        .wechat-footer {
            color: #999;
            font-size: 12px;
            text-align: center;
            padding: 20px;
        }
        
        /* 新增弹窗样式 */
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 999;
            display: flex;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(3px);
        }

        .alert-box {
            background: #FF6B00;
            border-radius: 20px;
            padding: 25px;
            width: 85%;
            max-width: 400px;
            text-align: center;
            position: relative;
            color: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .close-btn {
            position: absolute;
            right: 15px;
            top: 15px;
            width: 24px;
            height: 24px;
            opacity: 0.8;
            cursor: pointer;
        }

        .alert-title {
            font-size: 20px;
            font-weight: bold;
            margin: 10px 0 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .alert-title img {
            width: 24px;
            margin-right: 8px;
        }

        .alert-qrcode {
            background: white;
            padding: 15px;
            border-radius: 12px;
            margin: 15px 0;
        }

        .alert-qrcode img {
            width: 180px;
            height: 180px;
            border: 2px solid #FF6B00;
            border-radius: 8px;
        }

        .alert-tips {
            font-size: 14px;
            line-height: 1.6;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="qrcode-card">
            <div class="qrcode-img">
                <!-- 替换为你的二维码URL -->
                <img src="https://example.com/wechat-qrcode.jpg" 
                     alt="微信公众号二维码"
                     crossorigin="anonymous">
            </div>
            
            <div class="instruction">
                ▲ 长按识别图中二维码 ▲<br>
                立即添加云助理企业微信
            </div>

            <div class="tips">
                （关注后按提示填写确认保存简历）
            </div>
        </div>
    </div>

      <!-- 新增弹窗层 -->
    <div class="overlay" id="overlay">
        <div class="alert-box">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMTIgMkM2LjQ4IDIgMiA2LjQ4IDIgMTJzNC40OCAxMCAxMCAxMCAxMC00LjQ4IDEwLTEwUzE3LjUyIDIgMTIgMm0zIDE1Ljc5aC0xLjc1VjE1SDEzdjJoLTJ2LTJIOS44NXYtMS43NUgxMVY5aDJ2NC4wNEgxMXYxLjc1aDJ2Mk0xMiA2Ljc1Yy0uNjkgMC0xLjI1LjU2LTEuMjUgMS4yNVMxMS4zMSA5LjI1IDEyIDkuMjVzMS4yNS0uNTYgMS4yNS0xLjI1UzEyLjY5IDYuNzUgMTIgNi43NVoiLz48L3N2Zz4=" 
                 alt="关闭" 
                 class="close-btn"
                 onclick="closeAlert()">

            <div class="alert-title">
                <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTIgMkM2LjQ4IDIgMiA2LjQ4IDIgMTJzNC40OCAxMCAxMCAxMCAxMC00LjQ4IDEwLTEwUzE3LjUyIDIgMTIgMm0wIDE4Yy00LjQxIDAtOC0zLjU5LTgtOHMzLjU5LTggOC04IDggMy41OSA4IDgtMy41OSA4LTggOHptMy0xMy41SDF2MTQuMjVIMjBWNi41eiIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik0xNiA5djZoMXYtNnptLTQgMHY2aDF2LTZ6IiBmaWxsPSIjZmZmIi8+PC9zdmc+" 
                     alt="提示">
                简历暂存成功！暂存5分钟
            </div>

            <div class="alert-tips">
                请务必添加云助理企微<br>
                按提示确认保存
            </div>

            <div class="alert-qrcode">
                <!-- 替换为企业微信二维码 -->
                <img src="{:$customerRow['qrcode']}"
                     alt="企业微信客服"
                     crossorigin="anonymous">
            </div>

            <div class="alert-tips">
                ▼ 长按识别二维码 ▼<br>
                立即添加企业微信
            </div>
        </div>
    </div>

    <script>
        // 显示弹窗
        window.onload = function() {
            document.getElementById('overlay').style.display = 'flex';
        }

        // 关闭弹窗
        function closeAlert() {
            document.getElementById('overlay').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 点击遮罩层关闭
        document.getElementById('overlay').addEventListener('click', function(e) {
            if(e.target === this) closeAlert();
        });

        // 禁止背景滚动
        document.body.style.overflow = 'hidden';

    document.addEventListener('WeixinJSBridgeReady', function() {
        WeixinJSBridge.call('hideToolbar');
    });
</script>
</body>
</html>