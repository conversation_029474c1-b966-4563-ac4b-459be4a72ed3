<include file="block/hat" />
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<ul class="nav nav-tabs">
				<li ><a href="{:U('/prime/user/index')}">用户列表</a></li>
				<li class="active"><a href="{:U('/prime/user/jobuser')}">简历用户</a></li>
			</ul>
			<style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
<div class="main">
	<div class="panel panel-info">
	<!--<div class="panel-heading">筛选</div>-->
	<div class="panel-body">
		<form method="get" class="form-inline" role="form" id="form1">
			<div class="form-group">
				<select class="form-control" name="kw">
					<php>foreach($c_kw as $key=>$value){</php>
						<option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
					<php>}</php>
				</select>=<input class="form-control" type="text" name="val" value="{$_get.val}" /> 
			</div>




			<button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> 搜索</button>
			<a href="{:U('prime/user/index')}" class="btn btn-warning" >重置</a>
		</form>
	</div>
</div>
<form action="" method="post" >
<div class="panel panel-default">
	<div class="panel-body table-responsive">
		<table class="table table-hover">
			<thead class="navbar-inner">
				<tr>
					<th width="75px">#ID</th>
					<th >微信昵称</th>
					<th >头像</th>
                    <th>所属服务站</th>
                    <th>Service ID</th>
                    <th >创建时间</th>
					<th >操作</th>
				</tr>
			</thead>
			<tbody>
				<php>foreach($list as $v) { </php>
				<tr>
                    <td>{$v.id} </td>
                    <td>{$v.nickname} <br /></td>
					<td ><img src="<php>$head = $v['headimgurl']; echo $head ? $head.'2' :'/static/images/nopic.jpg';</php> " width="64px" /></td>
					<td>
						{:$v['service_station_id'] ? $serviceStationList[$v['service_station_id']] : '未绑定'}<br>
					</td>
					<td>
						<span class="label label-<php>if($v['service_id'] == 1) echo 'success'; elseif($v['service_id'] == 2) echo 'warning'; else echo 'default';</php>">
							{$v.service_id}
							<php>if($v['service_id'] == 1) echo ' (服务站和招就办)'; elseif($v['service_id'] == 2) echo ' (简历用户)'; else echo ' (未分类)';</php>
						</span>
						<br>
						<button type="button" class="btn btn-xs btn-info" onclick="changeServiceId({$v.id}, {$v.service_id})" style="margin-top: 5px;">
							<i class="fa fa-edit"></i> 修改
						</button>
					</td>
					<td><php>echo date('Y-m-d H:i:s', $v['created']);</php></td>
				</tr>
				<php>}</php>
			</tbody>
		</table>
        {$page}
	</div>
	</div>
</div>
</form>

		</div>
	</div>
</div>
<script>
	require(["daterangepicker"], function($){
		$(function(){
			$(".daterange.daterange-time").each(function(){
				var elm = this;
				$(this).daterangepicker({
					startDate: $(elm).prev().prev().val(),
					endDate: $(elm).prev().val(),
					format: "YYYY-MM-DD HH:mm",
					timePicker: false,
					timePicker12Hour : false,
					timePickerIncrement: 1,
					minuteStep: 1
				}, function(start, end){
					$(elm).find(".date-title").html(start.toDateTimeStr() + " 至 " + end.toDateTimeStr());
					$(elm).prev().prev().val(start.toDateTimeStr());
					$(elm).prev().val(end.toDateTimeStr());
				});
			});
			$('[data-toggle="tooltip"]').tooltip();
		});
	});

	// 修改用户service_id
	function changeServiceId(userId, currentServiceId) {
		var serviceOptions = {
			0: '未分类',
			1: '服务站和招就办',
			2: '简历用户'
		};

		var optionsHtml = '';
		for (var id in serviceOptions) {
			var selected = (id == currentServiceId) ? 'selected' : '';
			optionsHtml += '<option value="' + id + '" ' + selected + '>' + id + ' - ' + serviceOptions[id] + '</option>';
		}

		var html = '<div style="padding: 20px;">' +
			'<h4 style="margin-bottom: 15px; color: #333;">修改用户 Service ID</h4>' +
			'<div style="margin-bottom: 15px;">' +
			'<label style="display: block; margin-bottom: 5px; font-weight: bold;">选择新的 Service ID：</label>' +
			'<select id="newServiceId" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">' +
			optionsHtml +
			'</select>' +
			'</div>' +
			'<div style="text-align: right;">' +
			'<button type="button" onclick="layer.closeAll()" style="margin-right: 10px; padding: 8px 16px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 4px;">取消</button>' +
			'<button type="button" onclick="submitServiceIdChange(' + userId + ')" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px;">确认修改</button>' +
			'</div>' +
			'</div>';

		layer.open({
			type: 1,
			title: false,
			closeBtn: 1,
			area: ['400px', 'auto'],
			content: html
		});
	}

	// 提交service_id修改
	function submitServiceIdChange(userId) {
		var newServiceId = document.getElementById('newServiceId').value;

		$.ajax({
			url: '{:U("user/changeServiceId")}',
			type: 'POST',
			data: {
				id: userId,
				service_id: newServiceId
			},
			dataType: 'json',
			success: function(response) {
				if (response.status == 1) {
					layer.msg('修改成功', {icon: 1});
					// 动态更新页面显示，避免刷新导致session问题
					updateUserServiceIdDisplay(userId, newServiceId);
				} else {
					layer.msg(response.msg, {icon: 2});
					// 如果是登录失效，提示用户刷新页面
					if (response.msg && response.msg.indexOf('登录') !== -1) {
						setTimeout(function() {
							layer.confirm('登录已失效，是否刷新页面重新登录？', {
								btn: ['刷新页面', '取消']
							}, function() {
								location.reload();
							});
						}, 1500);
					}
				}
			},
			error: function(xhr, status, error) {
				// 检查是否是登录失效的错误
				if (xhr.status === 200 && xhr.responseJSON && xhr.responseJSON.info && xhr.responseJSON.info.indexOf('登录') !== -1) {
					layer.msg('登录已失效', {icon: 2});
					setTimeout(function() {
						layer.confirm('登录已失效，是否刷新页面重新登录？', {
							btn: ['刷新页面', '取消']
						}, function() {
							location.reload();
						});
					}, 1500);
				} else {
					layer.msg('请求失败，请重试', {icon: 2});
				}
			}
		});

		layer.closeAll();
	}

	// 动态更新用户service_id显示
	function updateUserServiceIdDisplay(userId, newServiceId) {
		var serviceOptions = {
			0: '未分类',
			1: '服务站和招就办',
			2: '简历用户'
		};

		var labelClass = '';
		if (newServiceId == 1) labelClass = 'success';
		else if (newServiceId == 2) labelClass = 'warning';
		else labelClass = 'default';

		// 找到对应用户的service_id显示元素并更新
		$('tbody tr').each(function() {
			var $row = $(this);
			var itemUserId = $row.find('td:first').text().trim();
			if (itemUserId == userId) {
				var $serviceIdCell = $row.find('td').eq(3); // Service ID列是第4列
				var $label = $serviceIdCell.find('.label');
				$label.removeClass('label-success label-warning label-default').addClass('label-' + labelClass);
				$label.text(newServiceId + ' (' + serviceOptions[newServiceId] + ')');

				// 更新修改按钮的参数
				var $modifyBtn = $serviceIdCell.find('button[onclick*="changeServiceId"]');
				$modifyBtn.attr('onclick', 'changeServiceId(' + userId + ', ' + newServiceId + ')');
				return false; // 找到后退出循环
			}
		});
	}
</script>
<include file="block/footer" />