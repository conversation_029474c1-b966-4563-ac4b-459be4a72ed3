
<php>if ($list) { foreach($list as $v) {</php>
<li>
    <div class="a">
        <a class="title">
            <h3>{:$v['service_name']}</h3>
        </a>
        <div class="prices" style="margin-top: 8px;margin-bottom: 12px;">
            <div class="ing">地址：{:$v['mail_address']}</div>
        </div>
        <div class="prices" style="margin-top: 8px;margin-bottom: 12px;">
            <div class="commission">
                联络人信息：{:$v['contract_name']}
                <a class="commission" href="tel:{:$v['mobile']}">{:$v['mobile']}  <i class="iconfont icon-dianhua"></i></a>
            </div></div>

        <!-- 招就办专用照片显示区域 -->
        <php>if (!empty($v['zsb_zj_pic']) || !empty($v['zsb_sfz_rx']) || !empty($v['zsb_sfz_gh'])) {</php>
        <div class="zjb-photos-section" style="margin-bottom: 10px;">
            <div class="imgs clearfix">
                <!-- 证件照 -->
                <php>if (!empty($v['zsb_zj_pic'])) {</php>
                <a href="javascript:void(0);" class="zjb-photo-item">
                    <img class="thumbnail" style="height: 104px;" src="http://we.zhongcaiguoke.com{:$v['zsb_zj_pic']}" alt="证件照">
                </a>
                <php>} else {</php>
                <a href="javascript:void(0);" class="zjb-photo-item">
                    <div class="zjb-photo-placeholder" style="height: 104px; background: #f0f0f0; border-radius: 5px; display: flex; align-items: center; justify-content: center; color: #999; font-size: 12px;">
                        暂无证件照
                    </div>
                </a>
                <php>}</php>

                <!-- 身份证人像面 -->
                <php>if (!empty($v['zsb_sfz_rx'])) {</php>
                <a href="javascript:void(0);" class="zjb-photo-item">
                    <img class="thumbnail" style="height: 104px;" src="http://we.zhongcaiguoke.com{:$v['zsb_sfz_rx']}" alt="身份证人像面">
                </a>
                <php>} else {</php>
                <a href="javascript:void(0);" class="zjb-photo-item">
                    <div class="zjb-photo-placeholder" style="height: 104px; background: #f0f0f0; border-radius: 5px; display: flex; align-items: center; justify-content: center; color: #999; font-size: 12px;">
                        暂无人像面
                    </div>
                </a>
                <php>}</php>

                <!-- 身份证国徽面 -->
                <php>if (!empty($v['zsb_sfz_gh'])) {</php>
                <a href="javascript:void(0);" class="zjb-photo-item">
                    <img class="thumbnail" style="height: 104px;" src="http://we.zhongcaiguoke.com{:$v['zsb_sfz_gh']}" alt="身份证国徽面">
                </a>
                <php>} else {</php>
                <a href="javascript:void(0);" class="zjb-photo-item">
                    <div class="zjb-photo-placeholder" style="height: 104px; background: #f0f0f0; border-radius: 5px; display: flex; align-items: center; justify-content: center; color: #999; font-size: 12px;">
                        暂无国徽面
                    </div>
                </a>
                <php>}</php>
            </div>
        </div>
        <php>}</php>

        <!-- 保留原有的通用图片显示（如果存在） -->
        <php>if (!empty($v['img_url_one']) || !empty($v['img_url_two']) || !empty($v['img_url_three'])) {</php>
        <div class="imgs clearfix">
            <php>if (!empty($v['img_url_one'])) {</php>
            <a href="javascript:void(0);"><img class="thumbnail" style="height: 104px;" src="http://we.zhongcaiguoke.com{:$v['img_url_one']}" alt=""></a>
            <php>}</php>
            <php>if (!empty($v['img_url_two'])) {</php>
            <a href="javascript:void(0);"><img class="thumbnail" style="height: 104px;" src="http://we.zhongcaiguoke.com{:$v['img_url_two']}" alt=""></a>
            <php>}</php>
            <php>if (!empty($v['img_url_three'])) {</php>
            <a href="javascript:void(0);"><img class="thumbnail" style="height: 104px;" src="http://we.zhongcaiguoke.com{:$v['img_url_three']}" alt=""></a>
            <php>}</php>
        </div>
        <php>}</php>
        <div class="states">
            <div class="state">
                <php>if ($v['status']!='1'){</php>
                <span style="color: red;">资质审核中...</span>
                <php>} elseif ($v['is_disabled'] == 1) {</php>
                <span style="color: #ff6b6b; background: #ffe0e0; padding: 2px 8px; border-radius: 4px; font-size: 12px;">已禁用</span>
                <php>} else {</php>
                <span style="color: #28a745; background: #e8f5e8; padding: 2px 8px; border-radius: 4px; font-size: 12px;">正常</span>
                <php>}</php>
            </div>
            <div class="righta">
                <php>if (($serviceStationRow['open_num'] ) > 0 && $serviceStationRow['is_out'] == 1) {</php>
                <a href="{:U('index/to_station', ['id' => $v['id'] ])}" class="a2"><i class="iconfont icon-bianji"></i>划拨资源包</a>
                <php>}</php>
                <a href="" class="a2"><i class="iconfont icon-yunying"></i>简历数据</a>
            </div>
        </div>
        <div class="sr">
            <div class="p"><span>总有 {:$v['job_num'] ? : 0} 份简历</span>，<span>服务中 {:$v['service_num'] ? : 0} 人</span>，<span>已服务 {:$v['succ_service_num'] ? : 0} 人</span></div>
            <php>if ($v['lastactive_time'] > 0){</php>
            <div style="margin-top: 8px;margin-bottom: 12px;font-size: 13px;">最后活动时间：
                {:date("Y-m-d H:i:s", $v['lastactive_time'])}
            </div>
            <php>}</php>
            
        </div>
    </div>
</li>
<php>}}</php>