
<php>if ($list) { foreach($list as $v) {</php>
<li>
    <div class="a">
        <a class="title">
            <h3>{:$v['service_name']}</h3>
        </a>
        <div class="prices" style="margin-top: 8px;margin-bottom: 12px;">
            <div class="ing">地址：{:$v['mail_address']}</div>
        </div>
        <div class="prices" style="margin-top: 8px;margin-bottom: 12px;">
            <div class="commission">
                联络人信息：{:$v['contract_name']}
                <a class="commission" href="tel:{:$v['mobile']}">{:$v['mobile']}  <i class="iconfont icon-dianhua"></i></a>
            </div></div>
        <php>if (!empty($v['img_url_one']) || !empty($v['img_url_two']) || !empty($v['img_url_three'])) {</php>
        <div class="imgs clearfix">
            <php>if (!empty($v['img_url_one'])) {</php>
            <a href="javascript:void(0);"><img class="thumbnail" style="height: 104px;" src="http://we.zhongcaiguoke.com{:$v['img_url_one']}" alt=""></a>
            <php>}</php>
            <php>if (!empty($v['img_url_two'])) {</php>
            <a href="javascript:void(0);"><img class="thumbnail" style="height: 104px;" src="http://we.zhongcaiguoke.com{:$v['img_url_two']}" alt=""></a>
            <php>}</php>
            <php>if (!empty($v['img_url_three'])) {</php>
            <a href="javascript:void(0);"><img class="thumbnail" style="height: 104px;" src="http://we.zhongcaiguoke.com{:$v['img_url_three']}" alt=""></a>
            <php>}</php>
        </div>
        <php>}</php>
        <div class="states">
            <div class="state">
                <php>if ($v['status']!='1'){</php>
                <span style="color: red;">资质审核中...</span>
                <php>}</php>
            </div>
            <div class="righta">
                <php>if (($serviceStationRow['open_num'] ) > 0 && $serviceStationRow['is_out'] == 1) {</php>
                <a href="{:U('index/to_station', ['id' => $v['id'] ])}" class="a2"><i class="iconfont icon-bianji"></i>划拨资源包</a>
                <php>}</php>
                <a href="" class="a2"><i class="iconfont icon-yunying"></i>简历数据</a>
            </div>
        </div>
        <div class="sr">
            <div class="p"><span>总有 {:$v['job_num'] ? : 0} 份简历</span>，<span>服务中 {:$v['service_num'] ? : 0} 人</span>，<span>已服务 {:$v['succ_service_num'] ? : 0} 人</span></div>
            <php>if ($v['lastactive_time'] > 0){</php>
            <div style="margin-top: 8px;margin-bottom: 12px;font-size: 13px;">最后活动时间：
                {:date("Y-m-d H:i:s", $v['lastactive_time'])}
            </div>
            <php>}</php>
            
        </div>
    </div>
</li>
<php>}}</php>