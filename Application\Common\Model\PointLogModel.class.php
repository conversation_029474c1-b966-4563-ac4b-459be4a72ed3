<?php

namespace Common\Model;

use Think\Model;

class PointLogModel extends Model
{
    protected $_auto = [ 
        ['created', 'time', 1, 'function'],
    ];

    /**
     * 类型
     */
    public $type = [1 => 'all'];

    /**
     * 添加积分记录
     * @param int $user_id  用户ID
     * @param int $point  积分
     * @param int $type  类型
     * @param int $relation_id 与type相对应的id
     */
    public function addLog($user_id, $point, $type = 1, $relation_id = 0)
    {
        if (!$user_id || !$point) return false;
        if (in_array($type, [2,3]) && !$relation_id) return false;
        $data = [
            'user_id' => $user_id,
            'type' => $type,
            'relation_id' => $relation_id,
            ];
        if ($type == 2 && $this->where($data)->getField('id')) {
            return false; // 已邀请过
        }
        $data['point'] = $point;
        $data['created'] = time();
        return $this->add($data);
    }

    public function count()
    {
        if (empty($this->options['where'])) {
            $sql = "select TABLE_ROWS from information_schema.`TABLES` WHERE TABLE_NAME = '{$this->getTableName()}'";
            $res = M()->query($sql);
            return $res[0]['table_rows'];
        } else {
            return parent::count();
        }
    }

}
