<?php

/** 
 * 系统权限配置，用户角色管理
 */
namespace Prime\Controller;
use Common\Controller\PrimeController;

class RbacController extends PrimeController
{

    public function _initialize() 
    {
        parent::_initialize();
    }

    /**
     * 角色管理，有add添加，edit编辑，delete删除
     */
    public function index()
    {
        $list= M("Role")->order(["sort"=>"asc","id"=>"desc"])->select();
        $this->assign("list", $list);
        $this->display();
    }

    /**
     *  添加/编辑 角色
     */
    public function edit()
    {
        C("TOKEN_ON", true);
        $id = intval(I("get.id"));
        $act = 'add';
        if ($id) {
            if ($id == 1) $this->error("超级管理员角色不能被修改！");
            $row = D("Role")->where(array("id" => $id))->find();
            if (!$row) $this->error("该角色不存在！");
            $act = 'save';
        }

        if (IS_POST) {
    		if (D("Role")->create()) {
    			if (D("Role")->$act() !== false) {
    				$this->success("编辑角色成功", U("rbac/index"));
    			} else {
    				$this->error("操作失败！");
    			}
    		} else {
    			$this->error(D("Role")->getError());
    		}
        }
        $pidList = D("Role")->getField('id,name', true);
        $this->assign('pidList', $pidList);
        $this->assign("row", $row);
        $this->display();
    }
    
    /**
     * 删除角色
     */
    public function del() {
        $id = intval(I("get.id"));
        if ($id == 1) {
            $this->error("超级管理员角色不能被删除！");
        }
        $count = M('Users')->where("role_id=$id")->count();
        if($count) {
        	$this->error("该角色已经有用户！");
        } else {
        	$status = D("Role")->delete($id);
        	if ($status!==false) {
        		$this->success("删除成功！", U('rbac/index'));
        	} else {
        		$this->error("删除失败！");
        	}
        }
        
    }

    /**
     * 角色授权
     */
    public function auth()
    {
       //角色ID
        $roleid = intval(I("get.id"));
        if (!$roleid) $this->error("参数错误！");
        if ($roleid == 1) $this->error("超级管理员角色不能操作！");
        import("Util.Tree");
        $menu = new \Tree();
        $menu->icon = array('│ ', '├─ ', '└─ ');
        $menu->nbsp = '&nbsp;&nbsp;&nbsp;';
        $result = $this->initMenu();
        $newmenus=array();
        $priv_data= D('AuthAccess')->where(array("role_id"=>$roleid))->getField("rule_name",true);//获取权限表数据
        foreach ($result as $m){
        	$newmenus[$m['id']]=$m;
        }
        foreach ($result as $n => $t) {
        	$result[$n]['checked'] = ($this->_is_checked($t, $roleid, $priv_data)) ? ' checked' : '';
        	$result[$n]['level'] = $this->_get_level($t['id'], $newmenus);
        	$result[$n]['parentid_node'] = ($t['parentid']) ? ' class="child-of-node-' . $t['parentid'] . '"' : '';
        }
        $str = "<tr id='node-\$id' \$parentid_node>
                       <td style='padding-left:30px;'>\$spacer<input type='checkbox' name='menuid[]' value='\$id' level='\$level' \$checked onclick='javascript:checknode(this);'> \$name</td>
	    			</tr>";
        $menu->init($result);
        $categorys = $menu->get_tree(0, $str);
        
        $this->assign("categorys", $categorys);
        $this->assign("roleid", $roleid);
        $this->display();
    }
    
    /**
     * 角色授权
     */
    public function auth_post()
    {
    	if (IS_POST) {
    		$roleid = intval(I("post.roleid"));
    		if(!$roleid) {
    			$this->error("需要授权的角色不存在！");
    		}
    		if (is_array($_POST['menuid']) && count($_POST['menuid']) > 0) {
    			$menu_model = M("Menu");
    			D('AuthAccess')->where(array("role_id"=>$roleid,'type'=>'prime_url'))->delete();
    			foreach ($_POST['menuid'] as $menuid) {
    				$menu = $menu_model->where(array("id"=>$menuid))->field("app,model,action")->find();
    				if($menu) {
    					$app=$menu['app'];
    					$model=$menu['model'];
    					$action=$menu['action'];
    					$name=strtolower("$app/$model/$action");
    					D('AuthAccess')->add(array("role_id"=>$roleid,"rule_name"=>$name,'type'=>'prime_url'));
    				}
    			}
    			$this->success("授权成功！", U("rbac/index"));
    		} else {
    			//当没有数据时，清除当前角色授权
    			D('AuthAccess')->where(array("role_id" => $roleid))->delete();
    			$this->error("没有接收到数据，执行清除授权成功！");
    		}
    	}
    }

    /**
     *  检查指定菜单是否有权限
     * @param array $menu menu表中数组
     * @param int $roleid 需要检查的角色ID
     */
    private function _is_checked($menu, $roleid, $priv_data)
    {
    	$app=$menu['app'];
    	$model=$menu['model'];
    	$action=$menu['action'];
    	$name=strtolower("$app/$model/$action");
    	if($priv_data) {
	    	if (in_array($name, $priv_data)) {
	    		return true;
	    	} else {
	    		return false;
	    	}
    	} else {
    		return false;
    	}
    }

    /**
     * 获取菜单深度
     * @param $id
     * @param $array
     * @param $i
     */
    protected function _get_level($id, $array = array(), $i = 0)
    {
        if ($array[$id]['parentid']==0 || empty($array[$array[$id]['parentid']]) || $array[$id]['parentid']==$id) {
            return  $i;
        } else {
            $i++;
            return $this->_get_level($array[$id]['parentid'],$array,$i);
        }
    }
    
}

