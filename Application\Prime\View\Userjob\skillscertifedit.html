<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Userjob/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">技能证书编辑</div>
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">技能类型</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="certificate_type" class="form-control sp_input">
                                    <option value="外语" {:  $row['certificate_type'] == '外语' ? 'selected' : ''} >外语</option>
                                    <option value="计算机" {:  $row['certificate_type'] == '计算机' ? 'selected' : ''} >计算机</option>
                                    <option value="职业资格" {:  $row['certificate_type'] == '职业资格' ? 'selected' : ''} >职业资格</option>
                                    <option value="奖励" {:  $row['certificate_type'] == '奖励' ? 'selected' : ''} >奖励</option>
                                    <option value="处罚" {:  $row['certificate_type'] == '处罚' ? 'selected' : ''} >处罚</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>证书名称</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="certificate_name" class="form-control sp_input" value="{$row.certificate_name}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label"><span
                                    style="color:red">*</span>证书等级</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="level" class="form-control sp_input" value="{$row.level}"/>
                                <span class="help-block sp_span">*</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">取证日期</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="get_date"    value="{: $row['get_date']}" data-withtime="true" placeholder="请选择时间" autocomplete="off" class="datetimepicker form-control sp_input">
                            </div>
                        </div>


                    </div>
                </div>
                <div class="form-group col-sm-12">
					<input type="hidden" name="id" value="{$row.id}"/>
					<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			    </div>
            </div>
            </form>
        </div>
    </div>
</div>
<include file="block/footer" />
<script>
    require(["datetimepicker", 'layer', 'util'], function($, layer, util){

        $(function () {
            $('.form input[name="type"]').change(function () {
                var type = $(this).val();
                $('.js_type').hide();
                $('.js_type'+type).show();
            });
        })

        $(".datetimepicker").each(function(){
            var opt = {
                language: "zh-CN",
                format: "yyyy-mm-dd",
                minView: 2,
                autoclose: true,
                format : "yyyy-mm-dd",
                minView : 2,
                minuteStep:1,
            };
            $(this).datetimepicker(opt);
        });
    });
    //function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
            u.editor($('.richtext1')[0]);

        })
    });
</script>