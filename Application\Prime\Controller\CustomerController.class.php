<?php
/**
 * 渠道
 * Created by PhpStorm.
 * User: yangbin
 * Date: 2019/5/20
 * Time: 17:24
 */
namespace Prime\Controller;

use Common\Controller\PrimeController;

class CustomerController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    public function index() {
        $c_kw = [
            'id' => 'ID',
            'name' => '姓名',
            'mobile' => '手机号',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);
        if ($s_status != '') $where['status'] = $s_status;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['name', 'mobile', 'contact', 'contact_tel']))  {
                $where[$s_kw] = ['like', "%$s_val%"];
            } else {
                $where[$s_kw] = $s_val;
            }
        }
        //公用搜索
        $contions = I('get.');
        $contionsWhere = ['group', 'is_out'];
        foreach ($contions as $whereKey => $row) {
            if (in_array($whereKey, $contionsWhere) && $row != '') {
                $where[$whereKey] = $row;
            }
        }

        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['create_time'] = ['between', [$s_start, $s_end]];
        }

        $obj = D("Customer");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();

        $this->assign('typeList', $obj->type);
        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '0');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '0');
        $this->assign('groupList', $obj->group);
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('statusList', $obj->status);
        $this->assign('isOutList', $obj->is_out);
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 添加编辑
     */
    public function edit() {
        $id = intval(I('get.id'));
        $obj = D("Customer");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        }
        $isAdmin = session('admin_id') == 1 ? true : false;
        if (IS_POST) {
            if ($data = $obj->create()) {
                $data['qrcode'] = I('post.qrcode');
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("customer/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->assign('typeList', $obj->type);
        $this->display();
    }

    /**
     * 状态变更
     */
    public function cgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("Customer");
        if (!array_key_exists($status, $obj->status)) $this->error('参数错误 ');
        $obj->save(['id' => $id, 'status' => $status]);
        $this->success('修改成功');
    }


    /**
     * 合成图片
     */
    public function mergePoster($bg_img, $qr_url, $size, $x, $y, $toPath = null)
    {
        vendor('phpqrcode.phpqrcode');
        ob_start();
        \QRcode::png($qr_url, false, QR_ECLEVEL_L, 10, 1);
        $qr_img = ob_get_contents();
        ob_end_clean();

        $bkim_size = getimagesize($bg_img);
        $type = $bkim_size['mime'];
        $f_map = ['image/png' => ['from' => 'imagecreatefrompng', 'img' => 'imagepng'], 'image/jpeg' => ['from' => 'imagecreatefromjpeg', 'img' => 'imagejpeg']];
        $bkim = $f_map[$type]['from']($bg_img);

        $avtim = imagecreatefromstring($qr_img);
        $avtimx = imagesx($avtim);
        $avtimy = imagesy($avtim);

        import('Util.easyphpthumbnail');
        $obj = new \easyphpthumbnail();
        // $obj->Backgroundcolor = '#0000FF';
        // $obj->Clipcorner = array(1,1,0,1,1,1,1);
        // $obj->Maketransparent = array(1,1,'#0000FF',1);
        $obj->im = $avtim;
        $obj->size = [0 => $avtimx, 1 => $avtimy];
        $obj->Thumbsize = $size;
        $obj->thumbmaker();
        $avtim = $obj->thumb;

        imagecopymerge($bkim, $avtim, $x, $y, 0, 0, $size, $size, 100);

        if ($toPath) {
            $toPath = SITE_PATH . $toPath;
            $f_map[$type]['img']($bkim, $toPath);
            return $toPath;
        } else {
            $f_map[$type]['img']($bkim);
        }

    }
}