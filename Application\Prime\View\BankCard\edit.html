<include file="block/hat" />
<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化银行卡编辑页面样式 */
                .bankcard-edit-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .bankcard-edit-container {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .bankcard-edit-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .bankcard-edit-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .bankcard-edit-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .bankcard-edit-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .bankcard-edit-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .bankcard-edit-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .bankcard-edit-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .bankcard-edit-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .bankcard-edit-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .bankcard-edit-back-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .bankcard-edit-back-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .bankcard-edit-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .bankcard-edit-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .bankcard-edit-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .bankcard-edit-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .bankcard-edit-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .bankcard-edit-nav-link.active {
                    color: #667eea !important;
                    background: white !important;
                    border-bottom-color: #667eea !important;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1) !important;
                    font-weight: 700 !important;
                }

                .bankcard-edit-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .bankcard-edit-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化表单卡片 */
                .bankcard-edit-form-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    position: relative;
                }

                .bankcard-edit-form-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .bankcard-edit-form-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .bankcard-edit-form-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .bankcard-edit-form-body {
                    padding: 2rem;
                }

                /* 步骤指示器 */
                .bankcard-edit-steps {
                    display: flex;
                    justify-content: center;
                    margin-bottom: 2rem;
                    padding: 1.5rem;
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 1rem;
                    backdrop-filter: blur(10px);
                }

                .bankcard-edit-step {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 2rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    position: relative;
                }

                .bankcard-edit-step:not(:last-child)::after {
                    content: '';
                    position: absolute;
                    right: -1rem;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 2rem;
                    height: 2px;
                    background: #e2e8f0;
                }

                .bankcard-edit-step.active {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    box-shadow: 0 4px 6px rgba(102, 126, 234, 0.3);
                }

                .bankcard-edit-step.completed {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                }

                .bankcard-edit-step.completed::after {
                    background: #10b981;
                }

                .bankcard-edit-step-icon {
                    width: 1.5rem;
                    height: 1.5rem;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.2);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 0.875rem;
                }

                /* 现代化表单组 */
                .bankcard-edit-form-group {
                    margin-bottom: 2rem;
                }

                .bankcard-edit-form-section {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 2rem;
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                }

                .bankcard-edit-form-section-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1.5rem 0;
                }

                .bankcard-edit-form-section-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .bankcard-edit-form-row {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 1.5rem;
                    margin-bottom: 1.5rem;
                }

                .bankcard-edit-form-field {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .bankcard-edit-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .bankcard-edit-form-required {
                    color: #ef4444;
                    font-size: 1.25rem;
                }

                .bankcard-edit-form-input {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s ease;
                    background: white;
                }

                .bankcard-edit-form-input:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .bankcard-edit-form-input.error {
                    border-color: #ef4444;
                    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
                }

                .bankcard-edit-form-input.success {
                    border-color: #10b981;
                    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
                }

                .bankcard-edit-form-select {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s ease;
                    background: white;
                    cursor: pointer;
                }

                .bankcard-edit-form-select:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .bankcard-edit-form-help {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-top: 0.5rem;
                }

                .bankcard-edit-form-help.error {
                    color: #ef4444;
                    font-weight: 600;
                }

                .bankcard-edit-form-help.success {
                    color: #10b981;
                    font-weight: 600;
                }

                /* 现代化单选按钮 */
                .bankcard-edit-radio-group {
                    display: flex;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                .bankcard-edit-radio-item {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    background: white;
                    font-size: 1.5rem;
                    font-weight: 600;
                }

                .bankcard-edit-radio-item:hover {
                    border-color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                }

                .bankcard-edit-radio-item.selected {
                    border-color: #667eea;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .bankcard-edit-radio-input {
                    display: none;
                }

                .bankcard-edit-radio-icon {
                    width: 1.25rem;
                    height: 1.25rem;
                    border: 2px solid currentColor;
                    border-radius: 50%;
                    position: relative;
                    transition: all 0.3s ease;
                }

                .bankcard-edit-radio-item.selected .bankcard-edit-radio-icon::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 0.5rem;
                    height: 0.5rem;
                    background: currentColor;
                    border-radius: 50%;
                }

                /* 银行卡号特殊样式 */
                .bankcard-edit-card-number {
                    font-family: 'Courier New', monospace;
                    font-size: 1.75rem;
                    letter-spacing: 0.1em;
                    text-align: center;
                    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
                    border: 2px solid #0ea5e9;
                    color: #0c4a6e;
                    font-weight: 700;
                }

                .bankcard-edit-card-number:focus {
                    border-color: #0284c7;
                    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
                }

                /* 操作按钮区域 */
                .bankcard-edit-actions {
                    display: flex;
                    gap: 1rem;
                    margin-top: 2rem;
                    padding-top: 2rem;
                    border-top: 1px solid #e2e8f0;
                    justify-content: center;
                    flex-wrap: wrap;
                }

                .bankcard-edit-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 1rem 2rem;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    min-width: 120px;
                    justify-content: center;
                }

                .bankcard-edit-action-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .bankcard-edit-action-btn.btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .bankcard-edit-action-btn.btn-primary:hover {
                    color: white;
                }

                .bankcard-edit-action-btn.btn-secondary {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                }

                .bankcard-edit-action-btn.btn-secondary:hover {
                    color: white;
                }

                .bankcard-edit-action-btn:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                    transform: none;
                }

                .bankcard-edit-action-btn:disabled:hover {
                    transform: none;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .bankcard-edit-container {
                        padding: 1.5rem;
                    }

                    .bankcard-edit-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .bankcard-edit-form-row {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .bankcard-edit-container {
                        padding: 1rem;
                    }

                    .bankcard-edit-nav-tabs {
                        flex-direction: column;
                    }

                    .bankcard-edit-nav-item {
                        flex: none;
                    }

                    .bankcard-edit-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .bankcard-edit-nav-link.active {
                        border-bottom: none !important;
                        border-right-color: #667eea !important;
                    }

                    .bankcard-edit-title-main {
                        font-size: 1.75rem;
                    }

                    .bankcard-edit-title-sub {
                        font-size: 1.25rem;
                    }

                    .bankcard-edit-actions {
                        flex-direction: column;
                    }

                    .bankcard-edit-steps {
                        flex-direction: column;
                        gap: 0.5rem;
                    }

                    .bankcard-edit-step:not(:last-child)::after {
                        display: none;
                    }

                    .bankcard-edit-radio-group {
                        flex-direction: column;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .bankcard-edit-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .bankcard-edit-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .bankcard-edit-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .bankcard-edit-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="bankcard-edit-wrapper">
                <div class="bankcard-edit-container">
                    <!-- 现代化页面标题 -->
                    <div class="bankcard-edit-header bankcard-edit-fade-in">
                        <div class="bankcard-edit-header-content">
                            <div class="bankcard-edit-title">
                                <div class="bankcard-edit-title-icon">
                                    <i class="fa fa-edit"></i>
                                </div>
                                <div class="bankcard-edit-title-text">
                                    <h1 class="bankcard-edit-title-main">编辑银行卡</h1>
                                    <p class="bankcard-edit-title-sub">Edit Bank Card - ID: {$info.id}</p>
                                </div>
                            </div>
                            <div class="bankcard-edit-actions">
                                <a href="{:U('BankCard/index')}" class="bankcard-edit-back-btn">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="bankcard-edit-nav-container bankcard-edit-fade-in-delay-1">
                        <ul class="bankcard-edit-nav-tabs">
                            <li class="bankcard-edit-nav-item">
                                <a href="{:U('BankCard/index')}" class="bankcard-edit-nav-link">
                                    <i class="fa fa-list bankcard-edit-nav-icon"></i>
                                    <span>银行卡列表</span>
                                </a>
                            </li>
                            <li class="bankcard-edit-nav-item">
                                <a href="{:U('BankCard/add')}" class="bankcard-edit-nav-link">
                                    <i class="fa fa-plus bankcard-edit-nav-icon"></i>
                                    <span>添加银行卡</span>
                                </a>
                            </li>
                            <li class="bankcard-edit-nav-item">
                                <a href="#" class="bankcard-edit-nav-link active">
                                    <i class="fa fa-edit bankcard-edit-nav-icon"></i>
                                    <span>编辑银行卡</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 步骤指示器 -->
                    <div class="bankcard-edit-steps bankcard-edit-fade-in-delay-2">
                        <div class="bankcard-edit-step active" data-step="1">
                            <div class="bankcard-edit-step-icon">
                                <i class="fa fa-building"></i>
                            </div>
                            <span>服务站信息</span>
                        </div>
                        <div class="bankcard-edit-step" data-step="2">
                            <div class="bankcard-edit-step-icon">
                                <i class="fa fa-university"></i>
                            </div>
                            <span>银行信息</span>
                        </div>
                        <div class="bankcard-edit-step" data-step="3">
                            <div class="bankcard-edit-step-icon">
                                <i class="fa fa-credit-card"></i>
                            </div>
                            <span>卡片信息</span>
                        </div>
                        <div class="bankcard-edit-step" data-step="4">
                            <div class="bankcard-edit-step-icon">
                                <i class="fa fa-cog"></i>
                            </div>
                            <span>设置选项</span>
                        </div>
                    </div>
                    <!-- 现代化表单卡片 -->
                    <div class="bankcard-edit-form-card bankcard-edit-fade-in-delay-3">
                        <div class="bankcard-edit-form-header">
                            <div class="bankcard-edit-form-title">
                                <div class="bankcard-edit-form-icon">
                                    <i class="fa fa-edit"></i>
                                </div>
                                <span>银行卡信息编辑</span>
                            </div>
                        </div>

                        <div class="bankcard-edit-form-body">
                            <form method="post" class="js-ajax-form" action="{:U('BankCard/edit')}" id="bankCardForm">
                                <input type="hidden" name="id" value="{$info.id}"/>

                                <!-- 第一步：服务站信息 -->
                                <div class="bankcard-edit-form-section" data-step="1">
                                    <h3 class="bankcard-edit-form-section-title">
                                        <div class="bankcard-edit-form-section-icon">
                                            <i class="fa fa-building"></i>
                                        </div>
                                        服务站信息
                                    </h3>

                                    <div class="bankcard-edit-form-row">
                                        <div class="bankcard-edit-form-field">
                                            <label class="bankcard-edit-form-label">
                                                <span class="bankcard-edit-form-required">*</span>
                                                关联服务站
                                            </label>
                                            <select name="service_station_id" class="bankcard-edit-form-select" required>
                                                <option value="">请选择服务站</option>
                                                <foreach name="stationList" item="vo">
                                                <option value="{$vo.id}" <eq name="vo.id" value="$info.service_station_id">selected</eq>>{$vo.service_name} (ID:{$vo.id})</option>
                                                </foreach>
                                            </select>
                                            <div class="bankcard-edit-form-help">选择银行卡所属的服务站</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 第二步：银行信息 -->
                                <div class="bankcard-edit-form-section" data-step="2">
                                    <h3 class="bankcard-edit-form-section-title">
                                        <div class="bankcard-edit-form-section-icon">
                                            <i class="fa fa-university"></i>
                                        </div>
                                        银行信息
                                    </h3>

                                    <div class="bankcard-edit-form-row">
                                        <div class="bankcard-edit-form-field">
                                            <label class="bankcard-edit-form-label">
                                                <span class="bankcard-edit-form-required">*</span>
                                                银行名称
                                            </label>
                                            <select name="bank_select" id="bank_select" class="bankcard-edit-form-select" onchange="checkBankSelection()">
                                                <option value="">请选择银行</option>
                                                <option value="中国工商银行" {$info.bank_name=='中国工商银行'?'selected':''}>中国工商银行</option>
                                                <option value="中国农业银行" {$info.bank_name=='中国农业银行'?'selected':''}>中国农业银行</option>
                                                <option value="中国银行" {$info.bank_name=='中国银行'?'selected':''}>中国银行</option>
                                                <option value="中国建设银行" {$info.bank_name=='中国建设银行'?'selected':''}>中国建设银行</option>
                                                <option value="交通银行" {$info.bank_name=='交通银行'?'selected':''}>交通银行</option>
                                                <option value="中国邮政储蓄银行" {$info.bank_name=='中国邮政储蓄银行'?'selected':''}>中国邮政储蓄银行</option>
                                                <option value="招商银行" {$info.bank_name=='招商银行'?'selected':''}>招商银行</option>
                                                <option value="中信银行" {$info.bank_name=='中信银行'?'selected':''}>中信银行</option>
                                                <option value="中国光大银行" {$info.bank_name=='中国光大银行'?'selected':''}>中国光大银行</option>
                                                <option value="华夏银行" {$info.bank_name=='华夏银行'?'selected':''}>华夏银行</option>
                                                <option value="中国民生银行" {$info.bank_name=='中国民生银行'?'selected':''}>中国民生银行</option>
                                                <option value="广发银行" {$info.bank_name=='广发银行'?'selected':''}>广发银行</option>
                                                <option value="平安银行" {$info.bank_name=='平安银行'?'selected':''}>平安银行</option>
                                                <option value="浦发银行" {$info.bank_name=='浦发银行'?'selected':''}>浦发银行</option>
                                                <option value="兴业银行" {$info.bank_name=='兴业银行'?'selected':''}>兴业银行</option>
                                                <option value="other">其他银行</option>
                                            </select>
                                            <input type="text" class="bankcard-edit-form-input" id="other_bank" value="{$info.bank_name}" placeholder="请输入银行名称" style="margin-top:10px;display:none;" onchange="updateBankName(this.value)" onkeyup="updateBankName(this.value)">
                                            <input type="hidden" name="bank_name" id="selected_bank_name" value="{$info.bank_name}">
                                            <div class="bankcard-edit-form-help">选择银行或输入其他银行名称</div>
                                        </div>

                                        <div class="bankcard-edit-form-field">
                                            <label class="bankcard-edit-form-label">开户支行</label>
                                            <input type="text" class="bankcard-edit-form-input" name="bank_branch" value="{$info.bank_branch}" placeholder="例如：xx银行xx市xx支行">
                                            <div class="bankcard-edit-form-help">填写详细的开户支行信息有助于提高打款成功率</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 第三步：卡片信息 -->
                                <div class="bankcard-edit-form-section" data-step="3">
                                    <h3 class="bankcard-edit-form-section-title">
                                        <div class="bankcard-edit-form-section-icon">
                                            <i class="fa fa-credit-card"></i>
                                        </div>
                                        卡片信息
                                    </h3>

                                    <div class="bankcard-edit-form-row">
                                        <div class="bankcard-edit-form-field">
                                            <label class="bankcard-edit-form-label">
                                                <span class="bankcard-edit-form-required">*</span>
                                                银行卡号
                                            </label>
                                            <input type="text" class="bankcard-edit-form-input bankcard-edit-card-number" name="card_number" id="card_number" value="{$info.card_number}" required placeholder="请输入完整的银行卡号" maxlength="23">
                                            <div class="bankcard-edit-form-help card-number-validation"></div>
                                        </div>

                                        <div class="bankcard-edit-form-field">
                                            <label class="bankcard-edit-form-label">
                                                <span class="bankcard-edit-form-required">*</span>
                                                开户人姓名
                                            </label>
                                            <input type="text" class="bankcard-edit-form-input" name="account_holder" value="{$info.account_holder}" required placeholder="请输入银行卡对应的开户人真实姓名">
                                            <div class="bankcard-edit-form-help">请输入与银行卡绑定的真实姓名</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 第四步：设置选项 -->
                                <div class="bankcard-edit-form-section" data-step="4">
                                    <h3 class="bankcard-edit-form-section-title">
                                        <div class="bankcard-edit-form-section-icon">
                                            <i class="fa fa-cog"></i>
                                        </div>
                                        设置选项
                                    </h3>

                                    <div class="bankcard-edit-form-row">
                                        <div class="bankcard-edit-form-field">
                                            <label class="bankcard-edit-form-label">是否默认银行卡</label>
                                            <div class="bankcard-edit-radio-group">
                                                <label class="bankcard-edit-radio-item <eq name='info.is_default' value='1'>selected</eq>">
                                                    <input type="radio" name="is_default" value="1" class="bankcard-edit-radio-input" <eq name="info.is_default" value="1">checked</eq>>
                                                    <div class="bankcard-edit-radio-icon"></div>
                                                    <span>是</span>
                                                </label>
                                                <label class="bankcard-edit-radio-item <eq name='info.is_default' value='0'>selected</eq>">
                                                    <input type="radio" name="is_default" value="0" class="bankcard-edit-radio-input" <eq name="info.is_default" value="0">checked</eq>>
                                                    <div class="bankcard-edit-radio-icon"></div>
                                                    <span>否</span>
                                                </label>
                                            </div>
                                            <div class="bankcard-edit-form-help">每个服务站只能有一个默认提现银行卡</div>
                                        </div>

                                        <div class="bankcard-edit-form-field">
                                            <label class="bankcard-edit-form-label">状态</label>
                                            <select name="status" class="bankcard-edit-form-select" required>
                                                <foreach name="statusList" item="vo">
                                                <option value="{$key}" <eq name="key" value="$info.status">selected</eq>>{$vo.text}</option>
                                                </foreach>
                                            </select>
                                            <div class="bankcard-edit-form-help">设置银行卡的使用状态</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="bankcard-edit-actions">
                                    <button type="submit" class="bankcard-edit-action-btn btn-primary" id="submitBtn">
                                        <i class="fa fa-save"></i>
                                        <span>保存修改</span>
                                    </button>
                                    <a href="{:U('BankCard/index')}" class="bankcard-edit-action-btn btn-secondary">
                                        <i class="fa fa-arrow-left"></i>
                                        <span>返回列表</span>
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            </div>
            <!-- Main content end -->
        </div>
	</div>
</div>

<include file="block/footer" />

<script>
    $(document).ready(function() {
        // 现代化单选按钮交互
        $('.bankcard-edit-radio-item').click(function() {
            var $group = $(this).closest('.bankcard-edit-radio-group');
            $group.find('.bankcard-edit-radio-item').removeClass('selected');
            $(this).addClass('selected');
            $(this).find('input[type="radio"]').prop('checked', true);
        });

        // 步骤指示器交互
        function updateStepIndicator() {
            var $sections = $('.bankcard-edit-form-section');
            var $steps = $('.bankcard-edit-step');

            $sections.each(function(index) {
                var $section = $(this);
                var $step = $steps.eq(index);
                var isValid = validateSection($section);

                if (isValid) {
                    $step.addClass('completed').removeClass('active');
                } else {
                    $step.removeClass('completed');
                }
            });

            // 设置当前活动步骤
            var currentStep = getCurrentStep();
            $steps.removeClass('active');
            $steps.eq(currentStep - 1).addClass('active').removeClass('completed');
        }

        function getCurrentStep() {
            // 根据表单验证状态确定当前步骤
            var $sections = $('.bankcard-edit-form-section');
            for (var i = 0; i < $sections.length; i++) {
                if (!validateSection($sections.eq(i))) {
                    return i + 1;
                }
            }
            return $sections.length;
        }

        function validateSection($section) {
            var isValid = true;
            var step = $section.data('step');

            switch(step) {
                case 1:
                    // 验证服务站选择
                    var serviceStation = $section.find('select[name="service_station_id"]').val();
                    isValid = serviceStation && serviceStation !== '';
                    break;
                case 2:
                    // 验证银行信息
                    var bankName = $('#selected_bank_name').val();
                    isValid = bankName && bankName.trim() !== '';
                    break;
                case 3:
                    // 验证卡片信息
                    var cardNumber = $section.find('#card_number').val().replace(/\s/g, '');
                    var accountHolder = $section.find('input[name="account_holder"]').val();
                    isValid = cardNumber && accountHolder && isValidCardNumber(cardNumber);
                    break;
                case 4:
                    // 验证设置选项
                    var isDefault = $section.find('input[name="is_default"]:checked').val();
                    var status = $section.find('select[name="status"]').val();
                    isValid = isDefault !== undefined && status !== '';
                    break;
            }

            return isValid;
        }

        // 表单字段变化时更新步骤指示器
        $('.bankcard-edit-form-section input, .bankcard-edit-form-section select').on('change input', function() {
            setTimeout(updateStepIndicator, 100);
        });

        // 银行选择处理
        $('#bank_select').on('change', function() {
            var $otherBank = $('#other_bank');
            var $selectedBankName = $('#selected_bank_name');

            if ($(this).val() === 'other') {
                $otherBank.show().attr('required', 'required').addClass('bankcard-edit-form-input');
                $selectedBankName.val($otherBank.val());
                setTimeout(function() {
                    $otherBank.focus();
                }, 100);
            } else {
                $otherBank.hide().removeAttr('required').removeClass('bankcard-edit-form-input');
                $selectedBankName.val($(this).val());
            }
            updateStepIndicator();
        });

        // 其他银行输入框变化时更新隐藏字段
        $('#other_bank').on('input', function() {
            if ($('#bank_select').val() === 'other') {
                var value = $(this).val().trim();
                $('#selected_bank_name').val(value);
                updateStepIndicator();
            }
        });

        // 其他银行输入框失去焦点时检查
        $("#other_bank").on("blur", function () {
            if ($("#bank_select").val() === "other") {
                var value = $(this).val().trim();
                var $this = $(this);
                if (!value) {
                    $this.addClass('error');
                    $this.next('.bankcard-edit-form-help').addClass('error').text('请输入银行名称');
                    setTimeout(function() {
                        $this.focus();
                    }, 100);
                } else {
                    $this.removeClass('error').addClass('success');
                    $this.next('.bankcard-edit-form-help').removeClass('error').addClass('success').text('银行名称已设置');
                    $("#selected_bank_name").val(value);
                }
                updateStepIndicator();
            }
        });

        // 银行卡号验证
        var cardNumberInput = $('#card_number');
        var validationMsg = $('.card-number-validation');
        var submitBtn = $('#submitBtn');

        // Luhn算法验证银行卡号
        function isValidCardNumber(cardNo) {
            if (!cardNo || !/^\d{13,19}$/.test(cardNo)) {
                return false;
            }

            // Luhn 算法校验
            var sum = 0;
            var alt = false;
            for (var i = cardNo.length - 1; i >= 0; i--) {
                var n = parseInt(cardNo.charAt(i), 10);
                if (alt) {
                    n *= 2;
                    if (n > 9) {
                        n -= 9;
                    }
                }
                sum += n;
                alt = !alt;
            }
            return (sum % 10 === 0);
        }

        // 银行卡输入格式化与验证
        cardNumberInput.on('input', function() {
            var $this = $(this);
            var cardNo = $this.val().replace(/\s/g, '');

            // 限制只能输入数字，去除所有非数字字符
            cardNo = cardNo.replace(/\D/g, '');

            // 格式化显示（每4位添加空格）
            var formattedCardNo = '';
            for (var i = 0; i < cardNo.length; i++) {
                if (i > 0 && i % 4 === 0) {
                    formattedCardNo += ' ';
                }
                formattedCardNo += cardNo.charAt(i);
            }

            $this.val(formattedCardNo);

            // 验证并显示提示信息
            if (cardNo.length >= 13) {
                if (isValidCardNumber(cardNo)) {
                    $this.removeClass('error').addClass('success');
                    validationMsg.removeClass('error').addClass('success').text('✓ 银行卡号格式正确');
                    submitBtn.prop('disabled', false);
                } else {
                    $this.removeClass('success').addClass('error');
                    validationMsg.removeClass('success').addClass('error').text('✗ 无效的银行卡号！');
                    submitBtn.prop('disabled', true);
                }
            } else if (cardNo.length > 0) {
                $this.removeClass('success').addClass('error');
                validationMsg.removeClass('success').addClass('error').text('✗ 银行卡号长度不足（至少13位）');
                submitBtn.prop('disabled', true);
            } else {
                $this.removeClass('error success');
                validationMsg.removeClass('error success').text('请输入完整的银行卡号');
                submitBtn.prop('disabled', false);
            }

            updateStepIndicator();
        });

        // 处理粘贴事件，即时清除非法字符
        cardNumberInput.on('paste', function(e) {
            // 使用setTimeout确保在粘贴内容被插入后处理
            setTimeout(function() {
                var pastedText = cardNumberInput.val();
                // 去除所有非数字字符
                var cleanNumber = pastedText.replace(/\D/g, '');

                // 格式化显示（每4位添加空格）
                var formattedCardNo = '';
                for (var i = 0; i < cleanNumber.length; i++) {
                    if (i > 0 && i % 4 === 0) {
                        formattedCardNo += ' ';
                    }
                    formattedCardNo += cleanNumber.charAt(i);
                }

                cardNumberInput.val(formattedCardNo);
                // 触发input事件进行验证
                cardNumberInput.trigger('input');
            }, 10);
        });

        // 键盘按下时进行限制
        cardNumberInput.on('keypress', function(e) {
            // 只允许数字和控制键
            var key = String.fromCharCode(e.which);
            if (!/^\d$/.test(key) && !e.ctrlKey && !e.metaKey && e.which !== 8 && e.which !== 9) {
                e.preventDefault();
            }
        });

        // 表单提交前验证
        $('#bankCardForm').on('submit', function(e) {
            var $form = $(this);
            var $submitBtn = $('#submitBtn');

            // 显示提交状态
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> <span>保存中...</span>');

            var cardNo = cardNumberInput.val().replace(/\s/g, '');
            if (cardNo.length > 0 && !isValidCardNumber(cardNo)) {
                e.preventDefault();
                validationMsg.removeClass('success').addClass('error').text('✗ 无效的银行卡号！');
                cardNumberInput.addClass('error').focus();
                $submitBtn.prop('disabled', false).html('<i class="fa fa-save"></i> <span>保存修改</span>');
                return false;
            }

            // 处理银行名称
            if ($('#bank_select').val() === 'other') {
                var otherBankName = $('#other_bank').val();
                if (!otherBankName || otherBankName.trim() === '') {
                    e.preventDefault();
                    layer.msg('请输入银行名称', {icon: 2});
                    $('#other_bank').addClass('error').focus();
                    $submitBtn.prop('disabled', false).html('<i class="fa fa-save"></i> <span>保存修改</span>');
                    return false;
                }
                $('#selected_bank_name').val(otherBankName.trim());
            } else {
                var selectedBank = $('#bank_select').val();
                if (!selectedBank || selectedBank === '') {
                    e.preventDefault();
                    layer.msg('请选择银行', {icon: 2});
                    $('#bank_select').focus();
                    $submitBtn.prop('disabled', false).html('<i class="fa fa-save"></i> <span>保存修改</span>');
                    return false;
                }
                $('#selected_bank_name').val(selectedBank);
            }

            // 最后检查一次隐藏字段的值
            var finalBankName = $('#selected_bank_name').val();
            if (!finalBankName || finalBankName.trim() === '') {
                e.preventDefault();
                layer.msg('请选择或输入银行名称', {icon: 2});
                $submitBtn.prop('disabled', false).html('<i class="fa fa-save"></i> <span>保存修改</span>');
                return false;
            }

            // 在表单中添加一个临时字段，确保bank_name被正确传递
            $("<input>")
                .attr({
                    type: "hidden",
                    name: "bank_name_confirm",
                    value: finalBankName
                })
                .appendTo("#bankCardForm");

            // 显示成功提示
            layer.msg('正在保存银行卡信息...', {icon: 16, time: 0});

            return true;
        });

        // 表单字段实时验证
        $('input[required], select[required]').on('blur', function() {
            var $this = $(this);
            var value = $this.val();

            if (!value || value.trim() === '') {
                $this.addClass('error');
            } else {
                $this.removeClass('error').addClass('success');
            }
            updateStepIndicator();
        });

        // 初始检查已有卡号
        cardNumberInput.trigger('input');

        // 初始化其他银行选项
        checkInitialBankSelection();

        // 初始化步骤指示器
        updateStepIndicator();

        // 添加表单动画效果
        $('.bankcard-edit-form-section').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });
    });

    // 更新银行名称函数
    function updateBankName(value) {
        var selectedBankName = document.getElementById('selected_bank_name');
        if (value && value.trim() !== '') {
            selectedBankName.value = value.trim();
            console.log("更新银行名称: " + value.trim());
        }
    }

    // 处理其他银行选项
    function checkBankSelection() {
        var bankSelect = document.getElementById('bank_select');
        var otherBank = document.getElementById('other_bank');
        var selectedBankName = document.getElementById('selected_bank_name');

        if (bankSelect.value === 'other') {
            // 显示其他银行输入框
            otherBank.style.display = 'block';
            otherBank.setAttribute('required', 'required');
            $(otherBank).addClass('bankcard-edit-form-input');

            // 如果其他银行输入框有值，则使用它
            if (otherBank.value && otherBank.value.trim() !== '') {
                selectedBankName.value = otherBank.value;
                $(otherBank).removeClass('error').addClass('success');
            } else {
                selectedBankName.value = "";
                $(otherBank).removeClass('success error');
            }

            // 立即聚焦到其他银行输入框
            setTimeout(function() {
                otherBank.focus();
            }, 100);
        } else {
            // 隐藏其他银行输入框
            otherBank.style.display = 'none';
            otherBank.removeAttribute('required');
            $(otherBank).removeClass('bankcard-edit-form-input error success');

            // 使用选择的银行名称
            if (bankSelect.value && bankSelect.value !== '') {
                selectedBankName.value = bankSelect.value;
                $(bankSelect).removeClass('error').addClass('success');
            } else {
                selectedBankName.value = "";
                $(bankSelect).removeClass('success error');
            }
        }
    }

    // 页面加载时检查银行选择
    function checkInitialBankSelection() {
        var bankSelect = document.getElementById('bank_select');
        var otherBank = document.getElementById('other_bank');
        var selectedBankName = document.getElementById('selected_bank_name');
        var currentBank = '{$info.bank_name}';
        var found = false;

        // 检查当前银行名称是否在下拉列表中
        for (var i = 0; i < bankSelect.options.length; i++) {
            if (bankSelect.options[i].value === currentBank) {
                found = true;
                break;
            }
        }

        // 如果银行名称不在预设列表中，选择"其他银行"并显示输入框
        if (!found && currentBank !== '') {
            for (var i = 0; i < bankSelect.options.length; i++) {
                if (bankSelect.options[i].value === 'other') {
                    bankSelect.selectedIndex = i;
                    otherBank.style.display = 'block';
                    otherBank.setAttribute('required', 'required');
                    $(otherBank).addClass('bankcard-edit-form-input success');
                    otherBank.value = currentBank;
                    selectedBankName.value = currentBank;
                    break;
                }
            }
        } else if (found) {
            // 如果在预设列表中，确保隐藏字段值正确
            selectedBankName.value = currentBank;
            $(bankSelect).addClass('success');
        }

        // 触发一次change事件以确保所有状态正确
        $(bankSelect).trigger('change');
    }

    // 添加自定义样式增强
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .bankcard-edit-form-section {
                animation: fadeInUp 0.6s ease-out both;
            }

            .bankcard-edit-step {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .bankcard-edit-step:hover {
                transform: translateY(-2px);
            }

            .bankcard-edit-form-input:focus,
            .bankcard-edit-form-select:focus {
                transform: scale(1.02);
            }

            .bankcard-edit-radio-item {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .bankcard-edit-radio-item:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }

            .bankcard-edit-action-btn {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .bankcard-edit-action-btn:active {
                transform: scale(0.98);
            }
        `)
        .appendTo('head');
</script>
</script>