<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Servicestation/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="panel panel-info">
                    <div class="panel-body">
                        <form method="get" class="form-inline" role="form" id="">
                            <div class="form-group">
                                <select class="form-control" name="kw">
                                    <php>foreach($c_kw as $key=>$value){</php>
                                    <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                    <php>}</php>
                                </select>=<input class="form-control" type="text" name="val" value="{$_get.val}" />
                            </div>
                            <button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> 搜索</button>
                            <a href="{:U('servicestation/index')}" class="btn btn-warning" >重置</a>
                        </form>

                        <span style="margin-top: 15px; display: block">所属服务站：{:$serviceStationlRow['service_name']}</span>
                    </div>
                </div>
                <form action="" method="post" >
                    <div class="panel panel-default">
                        <div class="panel-body table-responsive">
                            <table class="table table-hover">
                                <thead class="navbar-inner">
                                    <tr>
                                        <th width="75px">#ID</th>
                                        <th>文件名称</th>
                                        <th>文件地址</th>
                                        <th>时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($list as $v) { </php>
                                    <tr>
                                        <td>{$v.id}</td>
                                        <td>{$v.file_name}</td>
                                        <td><a target="_blank" href="/{$v.file_url}">{$v.file_url}</a></td>
                                        <td>{:str_replace(' ', '<br>', date('Y-m-d H:i:s', $v['create_time']))}</td>
                                        <td>
                                            <span class="label label-{:$statusList[$v['status']]['style']}">{:$statusList[$v['status']]['text']}</span><br>
                                        </td>
                                        <td><php>echo servicefileStatBtn($v['id'], I('get.service_station_id'), $v['status']);</php></td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                            {$page}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />

<script>
	require(["daterangepicker"], function($){

	});
</script>
