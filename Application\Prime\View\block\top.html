<php>
/*
$cur_menu = D("Menu")->curMenu(1);
$main_menus = D("Menu")->menu_json();
if(!$cur_menu) {
    $cur_menu['boot_id'] = key($main_menus);
}
	*/

</php>
<div class="navbar navbar-inverse navbar-static-top" role="navigation" style="position:static;">
	<div class="container-fluid">	
		<ul class="nav navbar-nav">
			<li><h4 style="margin-top:16px;margin-bottom:0;padding-right:20px;color:#fff"><php>echo C('PROJ_NAME');</php>管理后台</h4></li>
            <!--<li><a href="{:U('/prime/index')}"><i class="fa fa-home"></i>系统首页</a></li>-->
            <?php foreach($main_menus as $menu) { ?>
            <li<?php if ($cur_menu['boot_id'] == $menu['id']) echo ' class="active"'; ?>><a href="<?= $menu['url'] ?>"><i class="fa fa-<?= $menu['icon'] ?>"></i><?= $menu['name'] ?></a></li>
            <?php } ?>
		</ul>
		<ul class="nav navbar-nav navbar-right">
			<li class="dropdown">
			<a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" style="display:block; max-width:185px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; "><i class="fa fa-user"></i><?=session('admin_name')?> (<?=session('admin_role')?>) <b class="caret"></b></a>
			<ul class="dropdown-menu">
				<li><a href="{:U('sys/chpwd')}" target="_blank"><i class="fa fa-weixin fa-fw"></i> 修改密码</a></li>
				<li class="divider"></li>
				<li><a href="{:U('sys/refresh')}" target="_blank"><i class="fa fa-refresh fa-fw"></i> 更新缓存</a></li>
				<li class="divider"></li>
				<li><a href="{:U('public/logout')}"><i class="fa fa-sign-out fa-fw"></i> 退出系统</a></li>
			</ul>
			</li>
		</ul>
	</div>
</div>