<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化页面容器 */
                .userjob-edit-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .userjob-edit-container {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .userjob-edit-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .userjob-edit-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .userjob-edit-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .userjob-edit-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .userjob-edit-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .userjob-edit-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .userjob-edit-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .userjob-edit-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .userjob-edit-breadcrumb {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    color: #718096;
                }

                .userjob-edit-breadcrumb a {
                    color: #667eea;
                    text-decoration: none;
                    transition: color 0.3s ease;
                }

                .userjob-edit-breadcrumb a:hover {
                    color: #5a67d8;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .userjob-edit-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .userjob-edit-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .userjob-edit-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .userjob-edit-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .userjob-edit-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .userjob-edit-nav-link.active {
                    color: #667eea;
                    background: white;
                    border-bottom-color: #667eea;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1);
                }

                .userjob-edit-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .userjob-edit-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化表单容器 */
                .userjob-edit-form-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }

                .userjob-edit-form-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .userjob-edit-form-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin: 0;
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                }

                .userjob-edit-form-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .userjob-edit-form-body {
                    padding: 2rem;
                    background: white;
                }

                /* 现代化表单样式 */
                .userjob-edit-form {
                    display: flex;
                    flex-direction: column;
                    gap: 2rem;
                }

                .userjob-edit-form-section {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    position: relative;
                }

                .userjob-edit-section-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0 0 1.5rem 0;
                    padding-bottom: 0.75rem;
                    border-bottom: 2px solid #667eea;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .userjob-edit-section-icon {
                    width: 1.5rem;
                    height: 1.5rem;
                    color: #667eea;
                }

                .userjob-edit-form-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 1.5rem;
                }

                .userjob-edit-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .userjob-edit-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;
                }

                .userjob-edit-required {
                    color: #ef4444;
                    font-size: 1.25rem;
                }

                .userjob-edit-form-control {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                }

                .userjob-edit-form-control:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .userjob-edit-form-control::placeholder {
                    color: #9ca3af;
                }

                .userjob-edit-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                .userjob-edit-help-text {
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin: 0;
                }

                .userjob-edit-help-text.required {
                    color: #ef4444;
                }

                /* 图片上传区域 */
                .userjob-edit-image-upload {
                    border: 2px dashed #d1d5db;
                    border-radius: 0.5rem;
                    padding: 1.5rem;
                    text-align: center;
                    background: #f9fafb;
                    transition: all 0.3s ease;
                }

                .userjob-edit-image-upload:hover {
                    border-color: #667eea;
                    background: #f0f4ff;
                }

                /* 提交按钮区域 */
                .userjob-edit-submit-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    padding: 2rem;
                    margin-top: 2rem;
                    text-align: center;
                }

                .userjob-edit-submit-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 1rem 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .userjob-edit-submit-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                }

                .userjob-edit-submit-btn:active {
                    transform: translateY(0);
                }

                .userjob-edit-submit-btn:disabled {
                    background: #9ca3af;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: none;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .userjob-edit-container {
                        padding: 1.5rem;
                    }

                    .userjob-edit-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .userjob-edit-form-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .userjob-edit-container {
                        padding: 1rem;
                    }

                    .userjob-edit-nav-tabs {
                        flex-direction: column;
                    }

                    .userjob-edit-nav-item {
                        flex: none;
                    }

                    .userjob-edit-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .userjob-edit-nav-link.active {
                        border-bottom: none;
                        border-right-color: #667eea;
                    }

                    .userjob-edit-title-main {
                        font-size: 1.75rem;
                    }

                    .userjob-edit-title-sub {
                        font-size: 1.25rem;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .userjob-edit-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .userjob-edit-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .userjob-edit-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .userjob-edit-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="userjob-edit-wrapper">
                <div class="userjob-edit-container">
                    <!-- 现代化页面标题 -->
                    <div class="userjob-edit-header userjob-edit-fade-in">
                        <div class="userjob-edit-header-content">
                            <div class="userjob-edit-title">
                                <div class="userjob-edit-title-icon">
                                    <i class="fa fa-<php>echo !I('get.id') ? 'plus' : 'edit';</php>"></i>
                                </div>
                                <div class="userjob-edit-title-text">
                                    <h1 class="userjob-edit-title-main"><php>echo !I('get.id') ? '添加' : '编辑';</php>简历</h1>
                                    <p class="userjob-edit-title-sub"><php>echo !I('get.id') ? 'Add New Resume' : 'Edit Resume';</php></p>
                                </div>
                            </div>
                            <div class="userjob-edit-breadcrumb">
                                <a href="{:U('userjob/index')}">
                                    <i class="fa fa-list"></i>
                                    简历列表
                                </a>
                                <i class="fa fa-chevron-right"></i>
                                <span><php>echo !I('get.id') ? '添加' : '编辑';</php>简历</span>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="userjob-edit-nav-container userjob-edit-fade-in-delay-1">
                        <ul class="userjob-edit-nav-tabs">
                            <li class="userjob-edit-nav-item">
                                <a href="{:U('userjob/index')}" class="userjob-edit-nav-link">
                                    <i class="fa fa-list userjob-edit-nav-icon"></i>
                                    <span>简历列表管理</span>
                                </a>
                            </li>
                            <li class="userjob-edit-nav-item">
                                <a href="{:U('userjob/edit')}" class="userjob-edit-nav-link active">
                                    <i class="fa fa-<php>echo !I('get.id') ? 'plus' : 'edit';</php> userjob-edit-nav-icon"></i>
                                    <span><php>echo !I('get.id') ? '添加' : '编辑';</php>简历</span>
                                </a>
                            </li>
                            <li class="userjob-edit-nav-item">
                                <a href="{:U('userjob/upjob')}" class="userjob-edit-nav-link">
                                    <i class="fa fa-upload userjob-edit-nav-icon"></i>
                                    <span>添加简历文件</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 现代化表单 -->
                    <form action="" method="post" class="userjob-edit-form" enctype="multipart/form-data" id="form1">
                        <div class="userjob-edit-form-container userjob-edit-fade-in-delay-2">
                            <div class="userjob-edit-form-header">
                                <div class="userjob-edit-form-title">
                                    <div class="userjob-edit-form-icon">
                                        <i class="fa fa-user"></i>
                                    </div>
                                    <span>简历信息编辑</span>
                                </div>
                            </div>
                            <div class="userjob-edit-form-body">
                                <!-- 基本信息 -->
                                <div class="userjob-edit-form-section">
                                    <h3 class="userjob-edit-section-title">
                                        <i class="fa fa-user userjob-edit-section-icon"></i>
                                        基本信息
                                    </h3>
                                    <div class="userjob-edit-form-grid">
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                姓名
                                            </label>
                                            <input type="text" name="name" class="userjob-edit-form-control" value="{$row.name}" placeholder="请输入姓名" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                求职诉求
                                            </label>
                                            <input type="text" name="remark" class="userjob-edit-form-control" value="{$row.remark}" placeholder="请输入求职诉求" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">简历状态</label>
                                            <select name="job_state" class="userjob-edit-form-control userjob-edit-select">
                                                <option value="0" {:  $row['job_state'] == '0' ? 'selected' : ''}>沟通中</option>
                                                <option value="1" {:  $row['job_state'] == '1' ? 'selected' : ''}>培训中</option>
                                                <option value="2" {:  $row['job_state'] == '2' ? 'selected' : ''}>已入职</option>
                                                <option value="3" {:  $row['job_state'] == '3' ? 'selected' : ''}>服务终止</option>
                                            </select>
                                            <p class="userjob-edit-help-text">选择当前简历状态</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">服务说明</label>
                                            <input type="text" name="job_state_text" class="userjob-edit-form-control" value="{$row.job_state_text}" placeholder="请输入服务说明"/>
                                            <p class="userjob-edit-help-text">详细的服务说明</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">所属服务站</label>
                                            <if condition="$hasTrainingOrder">
                                                <select name="service_station_id" class="userjob-edit-form-control userjob-edit-select" disabled>
                                                    <php>foreach($serviceStationList as $serviceStationId => $serviceName) {</php>
                                                    <option value="{:$serviceStationId}" {:  $row['service_station_id'] == $serviceStationId ? 'selected' : ''}>{:$serviceName}</option>
                                                    <php>}</php>
                                                </select>
                                                <!-- 添加隐藏字段保持原值 -->
                                                <input type="hidden" name="service_station_id" value="{$row.service_station_id}" />
                                                <p class="userjob-edit-help-text" style="color: #e74c3c;">
                                                    <i class="fa fa-lock"></i> 该简历已有培训订单，不允许修改所属服务站
                                                </p>
                                            <else/>
                                                <select name="service_station_id" class="userjob-edit-form-control userjob-edit-select">
                                                    <php>foreach($serviceStationList as $serviceStationId => $serviceName) {</php>
                                                    <option value="{:$serviceStationId}" {:  $row['service_station_id'] == $serviceStationId ? 'selected' : ''}>{:$serviceName}</option>
                                                    <php>}</php>
                                                </select>
                                                <p class="userjob-edit-help-text">选择所属服务站</p>
                                            </if>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">性别</label>
                                            <select name="gender" class="userjob-edit-form-control userjob-edit-select">
                                                <option value="男" {:  $row['gender'] == '男' ? 'selected' : ''}>男</option>
                                                <option value="女" {:  $row['gender'] == '女' ? 'selected' : ''}>女</option>
                                            </select>
                                            <p class="userjob-edit-help-text">选择性别</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 证件照片 -->
                                <div class="userjob-edit-form-section">
                                    <h3 class="userjob-edit-section-title">
                                        <i class="fa fa-camera userjob-edit-section-icon"></i>
                                        证件照片
                                    </h3>
                                    <div class="userjob-edit-form-grid">
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">证件照</label>
                                            <div class="userjob-edit-image-upload">
                                                <php>echo tpl_form_field_image('photo_path', $row['photo_path'], '', ['type'=>4,'extras' => ['text' => 'readonly']])</php>
                                            </div>
                                            <p class="userjob-edit-help-text">上传清晰的证件照片</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">出生日期</label>
                                            <input type="text" name="birthdate" value="{: $row['birthdate']}" data-withtime="true" placeholder="请选择出生日期" autocomplete="off" class="datetimepicker userjob-edit-form-control">
                                            <p class="userjob-edit-help-text">选择出生日期</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 身份信息 -->
                                <div class="userjob-edit-form-section">
                                    <h3 class="userjob-edit-section-title">
                                        <i class="fa fa-id-card userjob-edit-section-icon"></i>
                                        身份信息
                                    </h3>
                                    <div class="userjob-edit-form-grid">
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                身份证号
                                            </label>
                                            <input type="text" name="id_number" class="userjob-edit-form-control" value="{$row.id_number}" placeholder="请输入身份证号" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                民族
                                            </label>
                                            <input type="text" name="nation" class="userjob-edit-form-control" value="{$row.nation}" placeholder="请输入民族" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                籍贯
                                            </label>
                                            <input type="text" name="Registered" class="userjob-edit-form-control" value="{$row.Registered}" placeholder="请输入籍贯" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                政治面貌
                                            </label>
                                            <input type="text" name="political_status" class="userjob-edit-form-control" value="{$row.political_status}" placeholder="请输入政治面貌" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                婚姻状况
                                            </label>
                                            <input type="text" name="marital_status" class="userjob-edit-form-control" value="{$row.marital_status}" placeholder="已婚/未婚" required/>
                                            <p class="userjob-edit-help-text required">请填写已婚或未婚</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 身体状况 -->
                                <div class="userjob-edit-form-section">
                                    <h3 class="userjob-edit-section-title">
                                        <i class="fa fa-heartbeat userjob-edit-section-icon"></i>
                                        身体状况
                                    </h3>
                                    <div class="userjob-edit-form-grid">
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                健康状况
                                            </label>
                                            <input type="text" name="health_status" class="userjob-edit-form-control" value="{$row.health_status}" placeholder="请输入健康状况" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                身高(cm)
                                            </label>
                                            <input type="number" name="height" class="userjob-edit-form-control" value="{$row.height}" placeholder="请输入身高" required/>
                                            <p class="userjob-edit-help-text required">必填项，单位：厘米</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                体重(kg)
                                            </label>
                                            <input type="number" name="weight" class="userjob-edit-form-control" value="{$row.weight}" placeholder="请输入体重" required/>
                                            <p class="userjob-edit-help-text required">必填项，单位：公斤</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                视力
                                            </label>
                                            <input type="text" name="vision" class="userjob-edit-form-control" value="{$row.vision}" placeholder="请输入视力情况" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                听力
                                            </label>
                                            <input type="text" name="hearing" class="userjob-edit-form-control" value="{$row.hearing}" placeholder="请输入听力情况" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">是否恐高</label>
                                            <select name="is_afraid_heights" class="userjob-edit-form-control userjob-edit-select">
                                                <option value="" {:  $row['is_afraid_heights'] != 1 ? $row['is_afraid_heights'] != 2 ? 'selected' : '' : ''}>未选择</option>
                                                <option value="1" {:  $row['is_afraid_heights'] == 1 ? 'selected' : ''}>恐高</option>
                                                <option value="2" {:  $row['is_afraid_heights'] ==  2 ? 'selected' : ''}>不恐高</option>
                                            </select>
                                            <p class="userjob-edit-help-text">选择是否恐高</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 教育信息 -->
                                <div class="userjob-edit-form-section">
                                    <h3 class="userjob-edit-section-title">
                                        <i class="fa fa-graduation-cap userjob-edit-section-icon"></i>
                                        教育信息
                                    </h3>
                                    <div class="userjob-edit-form-grid">
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                学历
                                            </label>
                                            <input type="text" name="education_level" class="userjob-edit-form-control" value="{$row.education_level}" placeholder="请输入学历" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                专业技术职称
                                            </label>
                                            <input type="text" name="Professional" class="userjob-edit-form-control" value="{$row.Professional}" placeholder="请输入专业技术职称" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                毕业院校
                                            </label>
                                            <input type="text" name="graduate_school" class="userjob-edit-form-control" value="{$row.graduate_school}" placeholder="请输入毕业院校" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                所学专业
                                            </label>
                                            <input type="text" name="major" class="userjob-edit-form-control" value="{$row.major}" placeholder="请输入所学专业" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">毕业时间</label>
                                            <input type="text" name="graduation_time" value="{: $row['graduation_time']}" data-withtime="true" placeholder="请选择毕业时间" autocomplete="off" class="datetimepicker userjob-edit-form-control">
                                            <p class="userjob-edit-help-text">选择毕业时间</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 工作信息 -->
                                <div class="userjob-edit-form-section">
                                    <h3 class="userjob-edit-section-title">
                                        <i class="fa fa-briefcase userjob-edit-section-icon"></i>
                                        工作信息
                                    </h3>
                                    <div class="userjob-edit-form-grid">
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">
                                                <span class="userjob-edit-required">*</span>
                                                求职意向
                                            </label>
                                            <input type="text" name="applied_position" class="userjob-edit-form-control" value="{$row.applied_position}" placeholder="请输入求职意向" required/>
                                            <p class="userjob-edit-help-text required">必填项</p>
                                        </div>
                                        <div class="userjob-edit-form-group">
                                            <label class="userjob-edit-form-label">参加工作时间</label>
                                            <input type="text" name="Time_to_work" value="{: $row['Time_to_work']}" data-withtime="true" placeholder="请选择参加工作时间" autocomplete="off" class="datetimepicker userjob-edit-form-control">
                                            <p class="userjob-edit-help-text">选择参加工作时间</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="userjob-edit-submit-container userjob-edit-fade-in-delay-3">
                            <input type="hidden" name="id" value="{$row.id}"/>
                            <button type="submit" name="submit" class="userjob-edit-submit-btn">
                                <i class="fa fa-save"></i>
                                <span><php>echo !I('get.id') ? '添加' : '保存';</php>简历</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />

<script>
    require(["datetimepicker", 'layer', 'util'], function($, layer, util){
        $(function () {
            // 表单类型切换功能
            $('.userjob-edit-form input[name="type"]').change(function () {
                var type = $(this).val();
                $('.js_type').hide();
                $('.js_type'+type).show();
            });

            // 表单验证增强
            $('.userjob-edit-form').on('submit', function(e) {
                var $form = $(this);
                var $submitBtn = $form.find('.userjob-edit-submit-btn');
                var originalText = $submitBtn.html();

                // 验证必填字段
                var isValid = true;
                $form.find('input[required], select[required]').each(function() {
                    var $field = $(this);
                    var $group = $field.closest('.userjob-edit-form-group');

                    if (!$field.val().trim()) {
                        isValid = false;
                        $field.css('border-color', '#ef4444');
                        $group.find('.userjob-edit-help-text').css('color', '#ef4444');

                        // 聚焦到第一个错误字段
                        if (isValid === false) {
                            $field.focus();
                        }
                    } else {
                        $field.css('border-color', '#e5e7eb');
                        $group.find('.userjob-edit-help-text').css('color', '#6b7280');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    layer.msg('请填写所有必填字段', {icon: 2});
                    return false;
                }

                // 显示提交状态
                $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

                // 如果验证通过，允许表单提交
                return true;
            });

            // 实时验证
            $('.userjob-edit-form-control').on('blur', function() {
                var $field = $(this);
                var $group = $field.closest('.userjob-edit-form-group');

                if ($field.prop('required') && !$field.val().trim()) {
                    $field.css('border-color', '#ef4444');
                    $group.find('.userjob-edit-help-text').css('color', '#ef4444');
                } else {
                    $field.css('border-color', '#e5e7eb');
                    $group.find('.userjob-edit-help-text').css('color', '#6b7280');
                }
            });

            // 输入时恢复正常状态
            $('.userjob-edit-form-control').on('input', function() {
                var $field = $(this);
                var $group = $field.closest('.userjob-edit-form-group');

                if ($field.val().trim()) {
                    $field.css('border-color', '#667eea');
                    $group.find('.userjob-edit-help-text').css('color', '#6b7280');
                }
            });

            // 页面加载动画
            setTimeout(function() {
                $('.userjob-edit-header').addClass('userjob-edit-fade-in');
                setTimeout(function() {
                    $('.userjob-edit-nav-container').addClass('userjob-edit-fade-in-delay-1');
                    setTimeout(function() {
                        $('.userjob-edit-form-container').addClass('userjob-edit-fade-in-delay-2');
                        setTimeout(function() {
                            $('.userjob-edit-submit-container').addClass('userjob-edit-fade-in-delay-3');
                        }, 100);
                    }, 100);
                }, 100);
            }, 100);
        });

        // 日期时间选择器配置
        $(".datetimepicker").each(function(){
            var opt = {
                language: "zh-CN",
                format: "yyyy-mm-dd",
                minView: 2,
                autoclose: true,
                minuteStep: 1,
                todayBtn: true,
                todayHighlight: true
            };
            $(this).datetimepicker(opt);
        });

        // 身份证号验证
        $('input[name="id_number"]').on('blur', function() {
            var idNumber = $(this).val().trim();
            var $group = $(this).closest('.userjob-edit-form-group');
            var $helpText = $group.find('.userjob-edit-help-text');

            if (idNumber && !validateIdNumber(idNumber)) {
                $(this).css('border-color', '#ef4444');
                $helpText.text('身份证号格式不正确').css('color', '#ef4444');
            } else if (idNumber) {
                $(this).css('border-color', '#10b981');
                $helpText.text('身份证号格式正确').css('color', '#10b981');
            }
        });

        // 手机号验证（如果有的话）
        $('input[name="mobile"]').on('blur', function() {
            var mobile = $(this).val().trim();
            var $group = $(this).closest('.userjob-edit-form-group');
            var $helpText = $group.find('.userjob-edit-help-text');

            if (mobile && !validateMobile(mobile)) {
                $(this).css('border-color', '#ef4444');
                $helpText.text('手机号格式不正确').css('color', '#ef4444');
            } else if (mobile) {
                $(this).css('border-color', '#10b981');
                $helpText.text('手机号格式正确').css('color', '#10b981');
            }
        });
    });

    // 身份证号验证函数
    function validateIdNumber(idNumber) {
        var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        return reg.test(idNumber);
    }

    // 手机号验证函数
    function validateMobile(mobile) {
        var reg = /^1[3-9]\d{9}$/;
        return reg.test(mobile);
    }

    // 富文本编辑器初始化
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
            u.editor($('.richtext1')[0]);
        });
    });
</script>