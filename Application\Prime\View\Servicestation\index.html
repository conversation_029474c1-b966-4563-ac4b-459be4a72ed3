<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化服务站管理页面样式 */
                .servicestation-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .servicestation-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .servicestation-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .servicestation-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .servicestation-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .servicestation-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .servicestation-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .servicestation-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .servicestation-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .servicestation-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .servicestation-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .servicestation-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                    text-decoration: none;
                }

                .servicestation-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .servicestation-index-add-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
                    text-decoration: none;
                }

                .servicestation-index-add-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .servicestation-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .servicestation-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .servicestation-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .servicestation-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .servicestation-index-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .servicestation-index-nav-link.active {
                    color: #667eea !important;
                    background: white !important;
                    border-bottom-color: #667eea !important;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1) !important;
                    font-weight: 700 !important;
                }

                .servicestation-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                    z-index: 1;
                }

                .servicestation-index-nav-link.active .servicestation-index-nav-icon {
                    color: #667eea !important;
                    transform: scale(1.1);
                }

                .servicestation-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 搜索面板 - 默认隐藏 */
                .servicestation-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    display: none;
                    animation: slideDown 0.3s ease-out;
                }

                .servicestation-index-search-panel.show {
                    display: block;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .servicestation-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .servicestation-index-search-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .servicestation-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .servicestation-index-search-close {
                    background: none;
                    border: none;
                    color: #718096;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                }

                .servicestation-index-search-close:hover {
                    background: #f1f5f9;
                    color: #374151;
                }

                .servicestation-index-search-body {
                    padding: 2rem;
                }

                /* 现代化服务站卡片 */
                .servicestation-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .servicestation-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .servicestation-card-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .servicestation-card-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .servicestation-card-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin: 0;
                }

                .servicestation-card-id {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .servicestation-card-name {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .servicestation-card-enterprise {
                    font-size: 1.25rem;
                    color: rgba(255, 255, 255, 0.9);
                    margin: 0.25rem 0 0 0;
                    font-weight: 400;
                }

                .servicestation-card-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .servicestation-status-badge {
                    padding: 0.5rem 1rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                }

                .servicestation-status-badge.status-pending {
                    background: rgba(251, 191, 36, 0.9);
                    border-color: rgba(251, 191, 36, 0.5);
                }

                .servicestation-status-badge.status-approved {
                    background: rgba(16, 185, 129, 0.9);
                    border-color: rgba(16, 185, 129, 0.5);
                }

                .servicestation-status-badge.status-rejected {
                    background: rgba(239, 68, 68, 0.9);
                    border-color: rgba(239, 68, 68, 0.5);
                }

                .servicestation-card-body {
                    padding: 2rem;
                }

                .servicestation-info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                .servicestation-info-section {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    border: 1px solid #e2e8f0;
                }

                .servicestation-info-section-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1rem 0;
                }

                .servicestation-info-section-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .servicestation-info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                }

                .servicestation-info-item:last-child {
                    margin-bottom: 0;
                }

                .servicestation-info-label {
                    color: #6b7280;
                    font-weight: 500;
                    min-width: 100px;
                    flex-shrink: 0;
                }

                .servicestation-info-value {
                    color: #374151;
                    font-weight: 600;
                    flex: 1;
                    word-break: break-all;
                }

                .servicestation-level-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .servicestation-type-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .servicestation-resume-link {
                    color: #3b82f6;
                    text-decoration: none;
                    font-weight: 600;
                    transition: all 0.3s ease;
                }

                .servicestation-resume-link:hover {
                    color: #2563eb;
                    text-decoration: underline;
                }

                /* 资源包统计区域 */
                .servicestation-resources-section {
                    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
                    border: 1px solid #10b981;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    margin-bottom: 2rem;
                }

                .servicestation-resources-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #065f46;
                    margin: 0 0 1rem 0;
                }

                .servicestation-resources-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 1rem;
                }

                .servicestation-resource-item {
                    text-align: center;
                    padding: 1rem;
                    background: rgba(16, 185, 129, 0.1);
                    border-radius: 0.5rem;
                }

                .servicestation-resource-label {
                    font-size: 1.25rem;
                    color: #065f46;
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                }

                .servicestation-resource-value {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #047857;
                    margin: 0;
                }

                /* 操作按钮区域 */
                .servicestation-actions {
                    display: flex;
                    gap: 0.75rem;
                    margin-top: 2rem;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                    flex-wrap: wrap;
                }

                .servicestation-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .servicestation-action-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .servicestation-action-btn.btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .servicestation-action-btn.btn-primary:hover {
                    color: white;
                }

                .servicestation-action-btn.btn-success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                }

                .servicestation-action-btn.btn-success:hover {
                    color: white;
                }

                .servicestation-action-btn.btn-warning {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    color: white;
                }

                .servicestation-action-btn.btn-warning:hover {
                    color: white;
                }

                .servicestation-action-btn.btn-danger {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    color: white;
                }

                .servicestation-action-btn.btn-danger:hover {
                    color: white;
                }

                .servicestation-action-btn.btn-info {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                }

                .servicestation-action-btn.btn-info:hover {
                    color: white;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .servicestation-index-container {
                        padding: 1.5rem;
                    }

                    .servicestation-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .servicestation-info-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .servicestation-index-container {
                        padding: 1rem;
                    }

                    .servicestation-index-nav-tabs {
                        flex-direction: column;
                    }

                    .servicestation-index-nav-item {
                        flex: none;
                    }

                    .servicestation-index-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .servicestation-index-nav-link.active {
                        border-bottom: none;
                        border-right-color: #667eea;
                    }

                    .servicestation-index-title-main {
                        font-size: 1.75rem;
                    }

                    .servicestation-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .servicestation-actions {
                        flex-direction: column;
                    }

                    .servicestation-card-header {
                        flex-direction: column;
                        gap: 1rem;
                        text-align: center;
                    }

                    .servicestation-resources-grid {
                        grid-template-columns: repeat(2, 1fr);
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .servicestation-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .servicestation-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .servicestation-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .servicestation-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="servicestation-index-wrapper">
                <div class="servicestation-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="servicestation-index-header servicestation-index-fade-in">
                        <div class="servicestation-index-header-content">
                            <div class="servicestation-index-title">
                                <div class="servicestation-index-title-icon">
                                    <i class="fa fa-building"></i>
                                </div>
                                <div class="servicestation-index-title-text">
                                    <h1 class="servicestation-index-title-main">服务站管理</h1>
                                    <p class="servicestation-index-title-sub">Service Station Management</p>
                                </div>
                            </div>
                            <div class="servicestation-index-actions">
                                <button type="button" class="servicestation-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>
                                <a href="{:U('Servicestation/edit')}" class="servicestation-index-add-btn">
                                    <i class="fa fa-plus"></i>
                                    <span>添加服务站</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="servicestation-index-nav-container servicestation-index-fade-in-delay-1">
                        <ul class="servicestation-index-nav-tabs">
                            <li class="servicestation-index-nav-item">
                                <a href="{:U('Servicestation/index')}" class="servicestation-index-nav-link active">
                                    <i class="fa fa-list servicestation-index-nav-icon"></i>
                                    <span>服务站管理</span>
                                </a>
                            </li>
                            <li class="servicestation-index-nav-item">
                                <a href="javascript:void(0)" class="servicestation-index-nav-link quick-filter" data-filter="approved">
                                    <i class="fa fa-check servicestation-index-nav-icon"></i>
                                    <span>已审核</span>
                                </a>
                            </li>
                            <li class="servicestation-index-nav-item">
                                <a href="javascript:void(0)" class="servicestation-index-nav-link quick-filter" data-filter="pending">
                                    <i class="fa fa-clock-o servicestation-index-nav-icon"></i>
                                    <span>待审核</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 搜索面板 - 默认隐藏 -->
                    <div class="servicestation-index-search-panel" id="searchPanel">
                        <div class="servicestation-index-search-header">
                            <div class="servicestation-index-search-title">
                                <div class="servicestation-index-search-icon">
                                    <i class="fa fa-search"></i>
                                </div>
                                <span>搜索筛选</span>
                            </div>
                            <button type="button" class="servicestation-index-search-close" onclick="toggleSearchPanel()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                        <div class="servicestation-index-search-body">
                            <form method="get" class="search-form" role="form">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">搜索条件：</label>
                                            <select class="form-control" name="kw">
                                                <php>foreach($c_kw as $key=>$value){</php>
                                                <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">搜索内容：</label>
                                            <input class="form-control" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容" />
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">等级：</label>
                                            <select name="level" class='form-control'>
                                                <option value="">全部等级</option>
                                                <php> foreach($levelList as $k => $v) {</php>
                                                <option value="{$k}" <php>if($_get['level'] != '' && $_get['level']==$k) echo 'selected';</php> >{$v.text}</option>
                                                <php> }</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">审核状态：</label>
                                            <select name="status" class='form-control'>
                                                <option value="">全部状态</option>
                                                <php> foreach($statusList as $k => $v) {</php>
                                                <option value="{$k}" <php>if($_get['status'] != '' && $_get['status']==$k) echo 'selected';</php> >{$v.text}</option>
                                                <php> }</php>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-top: 15px;">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">类型：</label>
                                            <select name="type" class='form-control'>
                                                <option value="">全部类型</option>
                                                <php> foreach($typeList as $k => $v) {</php>
                                                <option value="{$k}" <php>if($_get['type'] != '' && $_get['type']==$k) echo 'selected';</php> >{$v.text}</option>
                                                <php> }</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">排序方式：</label>
                                            <select name="sort" class='form-control'>
                                                <option value="">默认排序</option>
                                                <option value="id" <php>if($_get['sort'] != '' && $_get['sort']=='id') echo 'selected';</php> >ID排序</option>
                                                <option value="lastactive_time" <php>if($_get['sort'] != '' && $_get['sort']=='lastactive_time') echo 'selected';</php> >活动时间排序</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fa fa-search"></i> 搜索
                                                </button>
                                                <a href="{:U('servicestation/index')}" class="btn btn-default">
                                                    <i class="fa fa-refresh"></i> 重置
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- 服务站列表 -->
                    <div class="servicestation-index-fade-in-delay-2">
                        <php>foreach($list as $v) {</php>
                        <div class="servicestation-card">
                            <!-- 卡片头部 -->
                            <div class="servicestation-card-header">
                                <div class="servicestation-card-title">
                                    <span class="servicestation-card-id">#{$v.id}</span>
                                    <div>
                                        <h3 class="servicestation-card-name">{$v.service_name}</h3>
                                        <p class="servicestation-card-enterprise">{$v.enterprise_name}</p>
                                    </div>
                                </div>
                                <div class="servicestation-card-status">
                                    <php>
                                        $statusInfo = isset($statusList[$v['status']]) ? $statusList[$v['status']] : ['text' => '未知状态', 'style' => 'default'];
                                        $statusClass = '';
                                        switch($v['status']) {
                                            case 0:
                                                $statusClass = 'status-pending';
                                                break;
                                            case 1:
                                                $statusClass = 'status-approved';
                                                break;
                                            case 2:
                                                $statusClass = 'status-rejected';
                                                break;
                                            default:
                                                $statusClass = 'status-pending';
                                        }
                                    </php>
                                    <span class="servicestation-status-badge {$statusClass}">
                                        <i class="fa fa-circle"></i>
                                        {$statusInfo.text}
                                    </span>
                                </div>
                            </div>

                            <!-- 卡片主体 -->
                            <div class="servicestation-card-body">
                                <div class="servicestation-info-grid">
                                    <!-- 服务站信息 -->
                                    <div class="servicestation-info-section">
                                        <h4 class="servicestation-info-section-title">
                                            <div class="servicestation-info-section-icon">
                                                <i class="fa fa-building"></i>
                                            </div>
                                            服务站信息
                                        </h4>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">信用代码：</span>
                                            <span class="servicestation-info-value">{$v.credit_code}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">合同编号：</span>
                                            <span class="servicestation-info-value">{$v.contract_number}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">服务站编号：</span>
                                            <span class="servicestation-info-value">{$v.service_number}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">类型：</span>
                                            <span class="servicestation-type-badge">
                                                <i class="fa fa-tag"></i>
                                                {:$typeList[$v['type']]['text']}
                                            </span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">等级：</span>
                                            <span class="servicestation-level-badge">
                                                <i class="fa fa-star"></i>
                                                {:$levelList[$v['level']]['text']}
                                            </span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">所属上级：</span>
                                            <span class="servicestation-info-value">{:$pidList[$v['pid']] ? : '总部'}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">开通方式：</span>
                                            <span class="servicestation-info-value">{:$opentypeList[$v['open_type']]['text']}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">资源包页面：</span>
                                            <span class="servicestation-info-value">{:$resourcesList[$v['resources']]['text']}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">对外划拨：</span>
                                            <span class="servicestation-info-value">{:$isoutList[$v['is_out']]['text']}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">外部显示：</span>
                                            <span class="servicestation-info-value">{:$isshowListList[$v['show']]['text']}</span>
                                        </div>
                                    </div>

                                    <!-- 联系信息 -->
                                    <div class="servicestation-info-section">
                                        <h4 class="servicestation-info-section-title">
                                            <div class="servicestation-info-section-icon">
                                                <i class="fa fa-user"></i>
                                            </div>
                                            联系信息
                                        </h4>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">联络人：</span>
                                            <span class="servicestation-info-value">{$v.contract_name}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">手机号码：</span>
                                            <span class="servicestation-info-value">{$v.mobile}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">身份证号：</span>
                                            <span class="servicestation-info-value">{$v.contract_card}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">电子邮件：</span>
                                            <span class="servicestation-info-value">{$v.email}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">通讯地址：</span>
                                            <span class="servicestation-info-value">{$v.mail_address}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">添加时间：</span>
                                            <span class="servicestation-info-value">{:date('Y-m-d H:i:s', $v['create_time'])}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">最后活动：</span>
                                            <span class="servicestation-info-value">{:date('Y-m-d H:i:s', $v['lastactive_time'])}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">微信对接群信息：</span>
                                            <span class="servicestation-info-value" style="font-weight:normal;">{$v.chatroom} @{$v.wxatuserlist}</span>
                                        </div>
                                        <div class="servicestation-info-item">
                                            <span class="servicestation-info-label">简历数量：</span>
                                            <a href="{:U('userjob/index')}?kw=service_station_id&val={$v.id}" class="servicestation-resume-link">
                                                <i class="fa fa-file-text-o"></i>
                                                {$jobCounts[$v[id]]|default=0} 份
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- 资源包统计 -->
                                <div class="servicestation-resources-section">
                                    <h4 class="servicestation-resources-title">
                                        <i class="fa fa-cubes"></i>
                                        资源包统计
                                    </h4>
                                    <div class="servicestation-resources-grid">
                                        <div class="servicestation-resource-item">
                                            <div class="servicestation-resource-label">资源包总数</div>
                                            <div class="servicestation-resource-value">{$v.all_open_num}</div>
                                        </div>
                                        <div class="servicestation-resource-item">
                                            <div class="servicestation-resource-label">剩余可用数</div>
                                            <div class="servicestation-resource-value">{$v.open_num}</div>
                                        </div>
                                        <div class="servicestation-resource-item">
                                            <div class="servicestation-resource-label">推荐开通数</div>
                                            <div class="servicestation-resource-value">{$v.succ_tj_open_num}</div>
                                        </div>
                                        <div class="servicestation-resource-item">
                                            <div class="servicestation-resource-label">资源包开通数</div>
                                            <div class="servicestation-resource-value">{$v.succ_open_num}</div>
                                        </div>
                                        <div class="servicestation-resource-item">
                                            <div class="servicestation-resource-label">已划拨数量</div>
                                            <div class="servicestation-resource-value">{$v.divide_open_num}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="servicestation-actions">
                                    <php>echo servicestationStatBtn($v['id'], $v['status']);</php>

                                    <a title="该二维码为服务站专用二维码，适用于发给就业者" class="servicestation-action-btn btn-primary qrcode"
                                       data-desc="服务号：{:$serviceList[2]} | 场景值：{:$qrcodeList[$v['id']]['scene_id']}"
                                       data-ticket="{:urldecode($qrcodeList[$v['id']]['ticket'])}">
                                        <i class="fa fa-qrcode"></i>
                                        <span>服务站二维码</span>
                                    </a>

                                    <php>if ($v['status'] == 1) {</php>
                                    <a title="该二维码为服务平台专用二维码，适用于发给意向加盟服务站" class="servicestation-action-btn btn-info qrcode"
                                       data-desc="服务号：{:$serviceList[1]} | 场景值：{:$qrcodeStationList[$v['id']]['scene_id']}"
                                       data-ticket="{:urldecode($qrcodeStationList[$v['id']]['ticket'])}">
                                        <i class="fa fa-qrcode"></i>
                                        <span>服务平台二维码</span>
                                    </a>
                                    <php>}</php>
                                </div>
                            </div>
                        </div>
                        <php>}</php>

                        <!-- 分页 -->
                        <div class="servicestation-index-fade-in-delay-3" style="text-align: center; margin-top: 30px;">
                            {$page}
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />

<script>
    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
            setTimeout(function() {
                panel.style.display = 'none';
            }, 300);
        } else {
            panel.style.display = 'block';
            setTimeout(function() {
                panel.classList.add('show');
            }, 10);
        }
    }

    require(["daterangepicker", "layer"], function ($) {
        $(function () {
            // 原有的日期范围选择器功能
            $(".daterange.daterange-time").each(function () {
                var elm = this;
                $(this).daterangepicker({
                    startDate: $(elm).prev().prev().val(),
                    endDate: $(elm).prev().val(),
                    format: "YYYY-MM-DD HH:mm",
                    timePicker: false,
                    timePicker12Hour: false,
                    timePickerIncrement: 1,
                    minuteStep: 1
                }, function (start, end) {
                    $(elm).find(".date-title").html(start.toDateTimeStr() + " 至 " + end.toDateTimeStr());
                    $(elm).prev().prev().val(start.toDateTimeStr());
                    $(elm).prev().val(end.toDateTimeStr());
                });
            });

            // 工具提示
            $('[data-toggle="tooltip"]').tooltip();

            // 二维码显示功能
            $('.qrcode').click(function () {
                var ticket = $(this).data('ticket');
                var desc = $(this).data('desc');

                layer.open({
                    type: 1,
                    title: desc,
                    closeBtn: 1,
                    area: ['450px', '500px'],
                    shadeClose: true,
                    skin: 'qrcode-modal',
                    content: '<div style="text-align: center; padding: 20px;"><img width="400" style="border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" src="https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=' + ticket + '" /></div>'
                });
            });

            // 设置导航标签active状态的函数
            function setActiveNavTab() {
                // 移除所有active状态
                $('.servicestation-index-nav-link').removeClass('active');

                // 获取当前URL参数
                var urlParams = new URLSearchParams(window.location.search);
                var status = urlParams.get('status');

                // 根据status参数设置对应的active状态
                if (status === '1') {
                    // 已审核状态
                    $('.quick-filter[data-filter="approved"]').addClass('active');
                } else if (status === '0') {
                    // 待审核状态
                    $('.quick-filter[data-filter="pending"]').addClass('active');
                } else {
                    // 默认状态（全部）
                    $('.servicestation-index-nav-link').first().addClass('active');
                }
            }

            // 快速筛选功能
            $('.quick-filter').click(function(e) {
                e.preventDefault();
                var $this = $(this);
                var filter = $this.data('filter');
                var currentUrl = window.location.href.split('?')[0];
                var newUrl = currentUrl;

                // 立即更新视觉状态，提供即时反馈
                $('.servicestation-index-nav-link').removeClass('active');
                $this.addClass('active');

                // 添加加载状态
                var originalHtml = $this.html();
                $this.html('<i class="fa fa-spinner fa-spin servicestation-index-nav-icon"></i><span>加载中...</span>');

                // 获取当前的搜索参数
                var urlParams = new URLSearchParams(window.location.search);
                var searchParams = new URLSearchParams();

                // 保留搜索关键词等其他参数
                if (urlParams.get('kw')) {
                    searchParams.set('kw', urlParams.get('kw'));
                }
                if (urlParams.get('sort')) {
                    searchParams.set('sort', urlParams.get('sort'));
                }
                if (urlParams.get('order')) {
                    searchParams.set('order', urlParams.get('order'));
                }

                // 添加状态参数
                switch(filter) {
                    case 'approved':
                        searchParams.set('status', '1');
                        break;
                    case 'pending':
                        searchParams.set('status', '0');
                        break;
                    default:
                        // 不添加status参数，显示全部
                        break;
                }

                // 构建最终URL
                var paramString = searchParams.toString();
                if (paramString) {
                    newUrl += '?' + paramString;
                }

                // 延迟跳转，让用户看到加载状态
                setTimeout(function() {
                    window.location.href = newUrl;
                }, 300);
            });

            // 处理"全部"标签的点击事件
            $('.servicestation-index-nav-link').not('.quick-filter').click(function(e) {
                var $this = $(this);
                var href = $this.attr('href');

                // 如果是指向当前页面的链接，处理active状态
                if (href && href.indexOf('Servicestation/index') !== -1) {
                    e.preventDefault();

                    // 立即更新视觉状态
                    $('.servicestation-index-nav-link').removeClass('active');
                    $this.addClass('active');

                    // 添加加载状态
                    var originalHtml = $this.html();
                    $this.html('<i class="fa fa-spinner fa-spin servicestation-index-nav-icon"></i><span>加载中...</span>');

                    // 延迟跳转到不带参数的页面
                    setTimeout(function() {
                        window.location.href = href;
                    }, 300);
                }
            });

            // 卡片悬停效果增强
            $('.servicestation-card').hover(
                function() {
                    $(this).find('.servicestation-card-header').css('transform', 'scale(1.02)');
                },
                function() {
                    $(this).find('.servicestation-card-header').css('transform', 'scale(1)');
                }
            );

            // 搜索表单增强
            $('.search-form').on('submit', function() {
                var $submitBtn = $(this).find('button[type="submit"]');
                var originalText = $submitBtn.html();
                $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 搜索中...');

                // 如果是AJAX提交，这里可以添加相应的处理
                // 对于普通表单提交，浏览器会自动跳转，所以这里的状态重置可能不会被看到
                setTimeout(function() {
                    $submitBtn.prop('disabled', false).html(originalText);
                }, 3000);
            });

            // 状态按钮样式增强
            $('.servicestation-action-btn').each(function() {
                var $btn = $(this);
                var text = $btn.text().trim();

                // 根据按钮文本添加相应的图标和样式
                if (text.includes('审核通过') || text.includes('通过')) {
                    $btn.addClass('btn-success').prepend('<i class="fa fa-check"></i> ');
                } else if (text.includes('审核拒绝') || text.includes('拒绝')) {
                    $btn.addClass('btn-danger').prepend('<i class="fa fa-times"></i> ');
                } else if (text.includes('编辑') || text.includes('修改')) {
                    $btn.addClass('btn-warning').prepend('<i class="fa fa-edit"></i> ');
                } else if (text.includes('删除')) {
                    $btn.addClass('btn-danger').prepend('<i class="fa fa-trash"></i> ');
                } else if (text.includes('查看') || text.includes('详情')) {
                    $btn.addClass('btn-info').prepend('<i class="fa fa-eye"></i> ');
                }
            });

            // 资源包数据可视化增强
            $('.servicestation-resource-value').each(function() {
                var value = parseInt($(this).text());
                var $item = $(this).closest('.servicestation-resource-item');

                // 根据数值添加不同的视觉效果
                if (value > 100) {
                    $item.css('background', 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)');
                } else if (value > 50) {
                    $item.css('background', 'linear-gradient(135deg, #fefce8 0%, #fef3c7 100%)');
                } else if (value > 0) {
                    $item.css('background', 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)');
                }
            });

            // 滚动到顶部功能
            var $scrollToTop = $('<button class="scroll-to-top" style="position: fixed; bottom: 2rem; right: 2rem; width: 3rem; height: 3rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 50%; font-size: 1.25rem; cursor: pointer; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; display: none; transition: all 0.3s ease;"><i class="fa fa-arrow-up"></i></button>');
            $('body').append($scrollToTop);

            $(window).scroll(function() {
                if ($(this).scrollTop() > 300) {
                    $scrollToTop.fadeIn();
                } else {
                    $scrollToTop.fadeOut();
                }
            });

            $scrollToTop.on('click', function() {
                $('html, body').animate({scrollTop: 0}, 600);
            });

            // 添加加载动画
            $('.servicestation-card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
            });

            // 搜索结果统计
            var totalCards = $('.servicestation-card').length;
            if (totalCards > 0) {
                var $statsInfo = $('<div class="search-stats" style="background: white; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; color: #6b7280; font-size: 1.25rem;"><i class="fa fa-info-circle"></i> 共找到 ' + totalCards + ' 个服务站</div>');
                $('.servicestation-card').first().before($statsInfo);
            }

            // 空状态处理
            if (totalCards === 0) {
                var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-search"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无服务站数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的服务站。</p><a href="' + '{:U("Servicestation/edit")}' + '" class="btn btn-primary" style="padding: 0.75rem 1.5rem; font-size: 1.5rem;"><i class="fa fa-plus"></i> 添加服务站</a></div>');
                $('.servicestation-index-container').append($emptyState);
            }

            // 延迟执行active状态设置，确保DOM完全加载
            setTimeout(function() {
                setActiveNavTab();
            }, 100);
        });
    });

    // 添加自定义样式
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .qrcode-modal .layui-layer-title {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-weight: 600;
            }

            .search-stats {
                animation: fadeInUp 0.6s ease-out;
            }

            .empty-state {
                animation: fadeInUp 0.6s ease-out;
            }

            .servicestation-card {
                animation: fadeInUp 0.6s ease-out both;
            }

            .scroll-to-top:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 15px rgba(0,0,0,0.2);
            }

            .servicestation-action-btn:not(.qrcode) {
                margin-right: 0.5rem;
                margin-bottom: 0.5rem;
            }
        `)
        .appendTo('head');
</script>