<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化关注回复页面样式 */
                .subreply-wrapper {
                    width: 100%;
                    padding: 1.5rem;
                    background: #f7fafc;
                    min-height: 100vh;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.5;
                }

                /* 现代化页面标题 */
                .subreply-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                    width: 100%;
                }

                .subreply-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .subreply-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .subreply-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .subreply-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .subreply-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .subreply-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .subreply-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .subreply-service-info {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    border-radius: 0.75rem;
                    border: 1px solid #e2e8f0;
                }

                .subreply-service-icon {
                    width: 2rem;
                    height: 2rem;
                    background: #667eea;
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .subreply-service-name {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                /* 配置卡片样式 */
                .subreply-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .subreply-card-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .subreply-card-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .subreply-card-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .subreply-card-body {
                    padding: 2rem;
                }

                /* 表单样式 */
                .subreply-form-group {
                    margin-bottom: 2rem;
                }

                .subreply-form-label {
                    display: block;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 0.75rem;
                }

                .subreply-form-textarea {
                    width: 100%;
                    min-height: 200px;
                    padding: 1rem 1.25rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    line-height: 1.6;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                    font-family: inherit;
                    resize: vertical;
                }

                .subreply-form-textarea:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .subreply-form-textarea::placeholder {
                    color: #9ca3af;
                }

                .subreply-form-help {
                    margin-top: 0.75rem;
                    padding: 1rem;
                    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                    border: 1px solid #f59e0b;
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    color: #92400e;
                    line-height: 1.5;
                }

                .subreply-form-help-icon {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-weight: 600;
                    margin-bottom: 0.5rem;
                }

                .subreply-form-help-list {
                    margin: 0;
                    padding-left: 1.5rem;
                }

                .subreply-form-help-list li {
                    margin-bottom: 0.25rem;
                }

                /* 提交按钮样式 */
                .subreply-submit-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    padding: 2rem;
                    text-align: center;
                    margin-top: 2rem;
                }

                .subreply-submit-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 1rem 2rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    font-family: inherit;
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .subreply-submit-btn:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .subreply-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .subreply-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .subreply-header-content {
                        flex-direction: column;
                        text-align: center;
                    }
                }

                @media (max-width: 768px) {
                    .subreply-wrapper {
                        padding: 1rem;
                    }

                    .subreply-title-main {
                        font-size: 1.75rem;
                    }

                    .subreply-title-sub {
                        font-size: 1.25rem;
                    }

                    .subreply-card-body {
                        padding: 1rem;
                    }

                    .subreply-form-textarea {
                        min-height: 150px;
                        font-size: 1.25rem;
                        padding: 0.75rem 1rem;
                    }

                    .subreply-form-help {
                        font-size: 1.125rem;
                        padding: 0.75rem;
                    }
                }
            </style>

            <div class="subreply-wrapper">
            <!-- 现代化页面标题 -->
            <div class="subreply-header subreply-fade-in">
                <div class="subreply-header-content">
                    <div class="subreply-title">
                        <div class="subreply-title-icon">
                            <i class="fa fa-reply"></i>
                        </div>
                        <div class="subreply-title-text">
                            <h1 class="subreply-title-main">关注回复配置</h1>
                            <p class="subreply-title-sub">Subscribe Reply Configuration</p>
                        </div>
                    </div>
                    <div class="subreply-service-info">
                        <div class="subreply-service-icon">
                            <i class="fa fa-wechat"></i>
                        </div>
                        <h3 class="subreply-service-name">{$row.name}</h3>
                    </div>
                </div>
            </div>

            <form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
                <!-- 关注回复配置卡片 -->
                <div class="subreply-card subreply-fade-in-delay-1">
                    <div class="subreply-card-header">
                        <div class="subreply-card-icon">
                            <i class="fa fa-edit"></i>
                        </div>
                        <h3 class="subreply-card-title">关注回复内容</h3>
                    </div>
                    <div class="subreply-card-body">
                        <div class="subreply-form-group">
                            <label class="subreply-form-label">回复内容</label>
                            <textarea class="subreply-form-textarea" name="sub_content" placeholder="请输入用户关注时的自动回复内容...">{$row.sub_content}</textarea>
                            <div class="subreply-form-help">
                                <div class="subreply-form-help-icon">
                                    <i class="fa fa-info-circle"></i>
                                    配置说明
                                </div>
                                <ul class="subreply-form-help-list">
                                    <li>所有链接地址请添加 <strong>http://</strong> 或 <strong>https://</strong> 前缀</li>
                                    <li>显示渠道地址格式：<strong>%渠道名称%</strong></li>
                                    <li>示例：欢迎您通过 <strong>%渠道名称%</strong> 关注中才国科</li>
                                    <li>支持换行和基本文本格式</li>
                                    <li>建议内容长度控制在500字以内，确保良好的用户体验</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="subreply-submit-container">
                    <input type="hidden" name="id" value="{$row.id}"/>
                    <button type="submit" name="submit" class="subreply-submit-btn">
                        <i class="fa fa-save"></i>
                        <span>保存配置</span>
                    </button>
                </div>
            </form>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />
<script>
    $(function() {
        // 页面动画效果
        $('.subreply-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });

        // 文本区域增强功能
        var $textarea = $('.subreply-form-textarea');
        var $charCount = $('<div class="char-count" style="text-align: right; margin-top: 0.5rem; font-size: 1.125rem; color: #6b7280;"></div>');
        $textarea.after($charCount);

        // 字符计数功能
        function updateCharCount() {
            var length = $textarea.val().length;
            var maxLength = 500;
            var remaining = maxLength - length;

            $charCount.html('已输入 <strong>' + length + '</strong> 字符' +
                (remaining < 100 ? ' | 还可输入 <strong style="color: #f59e0b;">' + remaining + '</strong> 字符' : ''));

            if (length > maxLength) {
                $charCount.css('color', '#ef4444');
                $textarea.css('border-color', '#ef4444');
            } else if (remaining < 100) {
                $charCount.css('color', '#f59e0b');
                $textarea.css('border-color', '#f59e0b');
            } else {
                $charCount.css('color', '#6b7280');
                $textarea.css('border-color', '#e5e7eb');
            }
        }

        // 绑定输入事件
        $textarea.on('input keyup', updateCharCount);
        updateCharCount(); // 初始化

        // 输入框焦点效果
        $textarea.on('focus', function() {
            $(this).css('border-color', '#667eea');
            $(this).css('box-shadow', '0 0 0 3px rgba(102, 126, 234, 0.1)');
        });

        $textarea.on('blur', function() {
            var length = $(this).val().length;
            if (length > 500) {
                $(this).css('border-color', '#ef4444');
                $(this).css('box-shadow', '0 0 0 3px rgba(239, 68, 68, 0.1)');
            } else if (length > 400) {
                $(this).css('border-color', '#f59e0b');
                $(this).css('box-shadow', '0 0 0 3px rgba(245, 158, 11, 0.1)');
            } else {
                $(this).css('border-color', '#e5e7eb');
                $(this).css('box-shadow', 'none');
            }
        });

        // 表单提交增强
        $('#form1').on('submit', function(e) {
            var $submitBtn = $('.subreply-submit-btn');
            var originalText = $submitBtn.html();
            var content = $textarea.val().trim();

            // 验证内容
            if (!content) {
                e.preventDefault();
                $textarea.focus();
                $textarea.css('border-color', '#ef4444');
                $textarea.css('box-shadow', '0 0 0 3px rgba(239, 68, 68, 0.1)');

                // 显示错误提示
                if (typeof layer !== 'undefined') {
                    layer.msg('请输入关注回复内容', {icon: 2});
                } else {
                    alert('请输入关注回复内容');
                }
                return false;
            }

            // 验证长度
            if (content.length > 500) {
                e.preventDefault();
                $textarea.focus();

                if (typeof layer !== 'undefined') {
                    layer.msg('回复内容不能超过500字符', {icon: 2});
                } else {
                    alert('回复内容不能超过500字符');
                }
                return false;
            }

            // 提交状态
            $submitBtn.html('<i class="fa fa-spinner fa-spin"></i> <span>保存中...</span>');
            $submitBtn.prop('disabled', true);

            // 如果需要阻止默认提交并使用Ajax，可以在这里添加
            // e.preventDefault();
            // 进行Ajax提交...

            // 模拟提交延迟（实际使用时可以删除）
            setTimeout(function() {
                $submitBtn.html(originalText);
                $submitBtn.prop('disabled', false);
            }, 2000);
        });

        // 快捷插入功能
        var shortcuts = [
            { text: '%渠道名称%', desc: '插入渠道名称变量' },
            { text: 'http://', desc: '插入HTTP链接前缀' },
            { text: 'https://', desc: '插入HTTPS链接前缀' }
        ];

        // 创建快捷插入按钮
        var $shortcutContainer = $('<div class="shortcut-container" style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;"></div>');

        shortcuts.forEach(function(shortcut) {
            var $btn = $('<button type="button" class="shortcut-btn" style="padding: 0.25rem 0.75rem; background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 1rem; color: #374151; cursor: pointer; transition: all 0.2s;" title="' + shortcut.desc + '">' + shortcut.text + '</button>');

            $btn.hover(
                function() {
                    $(this).css('background', '#e5e7eb');
                    $(this).css('border-color', '#9ca3af');
                },
                function() {
                    $(this).css('background', '#f3f4f6');
                    $(this).css('border-color', '#d1d5db');
                }
            );

            $btn.click(function() {
                var cursorPos = $textarea[0].selectionStart;
                var textBefore = $textarea.val().substring(0, cursorPos);
                var textAfter = $textarea.val().substring(cursorPos);

                $textarea.val(textBefore + shortcut.text + textAfter);
                $textarea.focus();
                $textarea[0].setSelectionRange(cursorPos + shortcut.text.length, cursorPos + shortcut.text.length);

                updateCharCount();
            });

            $shortcutContainer.append($btn);
        });

        $textarea.after($shortcutContainer);

        // 卡片悬停效果
        $('.subreply-card').hover(
            function() {
                $(this).css('transform', 'translateY(-2px)');
                $(this).css('box-shadow', '0 8px 15px -3px rgba(0, 0, 0, 0.1)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
                $(this).css('box-shadow', '0 4px 6px -1px rgba(0, 0, 0, 0.1)');
            }
        );

        // 工具提示
        $('[title]').each(function() {
            $(this).attr('data-toggle', 'tooltip');
        });

        if (typeof $().tooltip === 'function') {
            $('[data-toggle="tooltip"]').tooltip();
        }
    });
</script>