﻿<php>if (empty($list)) {</php>
<div style="text-align: center; padding: 40px; color: #888;">暂无资金明细记录</div>
<php>} else { foreach($list as $v) {</php>
<div class="detail-item">
    <div class="detail-info">
        <div class="detail-title">{:$typeList[$v['type']]['text']}</div>
        <div class="detail-time">{:date('Y-m-d H:i:s', $v['create_time'])}</div>
        <php>if(!empty($v['remark'])) {</php>
        <div class="detail-remark" style="color:#e74c3c;font-size:12px;margin-top:4px;">{:$v['remark']}</div>
        <php>}</php>
    </div>
    <div class="detail-amount <php>
        if($v['type'] == 1 || $v['type'] == 10 || $v['type'] == 11) {
            echo 'amount-expense';  // 提现、培训奖励扣除、取消报名奖励回退：红色减号
        } elseif($v['type'] == 6) {
            echo 'amount-transfer';  // 解冻转移：蓝色转移符号
        } else {
            echo 'amount-income';    // 其他：绿色加号
        }
    </php>">
        <php>
        if($v['type'] == 1) {
            echo '- ¥' . $v['money'];  // 提现：减号
        } elseif($v['type'] == 10 || $v['type'] == 11) {
            echo '- ¥' . abs($v['money']);  // 培训奖励扣除、取消报名奖励回退：减号（使用绝对值确保显示正确）
        } elseif($v['type'] == 6) {
            echo '→ ¥' . $v['money'];   // 解冻转移：转移符号
        } else {
            echo '+ ¥' . $v['money'];   // 其他：加号
        }
        </php>
    </div>
</div>
<php>}}</php>