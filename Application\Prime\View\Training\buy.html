<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <include file="Servicestation/nav" />
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <form action="" method="post" class="form-horizontal form" id="form1">
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">{$serviceStationlRow.service_name} 购买资源包</div>
                    <div class="panel-body">

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">服务站名称</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="service_name" readonly class="form-control" value="{$serviceStationlRow.service_name}" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">购买数量</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="buy_open_num" class="form-control" value="0" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label ">付款方式</label>
                            <div class="col-sm-9 col-xs-12">
                                <select name="buy_type" class="form-control sp_input ">
                                    <option value=" " >==请选择==</option>
                                    <php>foreach($buyList as $key => $val) {</php>
                                    <option value="{$key}">{$val.text}</option>
                                    <php>}</php>
                                </select>
                                <span style="padding-left: 38px;">上级推荐服务站剩余可用资源包数量：<b style="color: red;">{:$pidnum}</b></span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">购买总价</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="money" class="form-control" value="0" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">实际支付</label>
                            <div class="col-sm-9 col-xs-12">
                                <input type="text" name="pay_money" class="form-control" value="0" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-2 control-label">付款凭证<br>备注说明</label>
                            <div class="col-sm-9 col-xs-12">
                                <textarea name="remark" rows="2" class="col-sm-9 col-xs-12 richtext" ></textarea>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="form-group col-sm-12">
					<input type="hidden" name="id" value="{$serviceStationlRow.id}"/>
					<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			    </div>
            </div>
            </form>
        </div>
    </div>
</div>
<include file="block/footer" />
<script>

    //function _alert(msg, n) {if(!n) n= 2; layer.msg(msg, {icon: n});}
    require(["layer", 'util'], function(layer, u){
        $(function () {
            u.editor($('.richtext')[0]);
            u.editor($('.richtext1')[0]);

        })
    });
</script>