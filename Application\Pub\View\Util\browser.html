<div class="clearfix file-browser">
	<style type="text/css">
		.modal-dialog .file-browser .breadcrumb i{ font-size:1em;}
	</style>
	<ol class="breadcrumb">
		<li><a herf="javascript:;" onclick="imageBrowser.browser('');"><i class="fa fa-home">&nbsp;</i></a></li>
		<php>if($year) {</php>
		<li><a herf="javascript:;" onclick="imageBrowser.browser('{$year}');">{$year}</a></li>
		<php>}</php>
		<php>if($month) {</php>
		<li><a herf="javascript:;" onclick="imageBrowser.browser('{$year}/{$month}');">{$month}</a></li>
		<php>}</php>
	</ol>
	<php>foreach($files as $file) {</php>
		<php>if($file['is_dir']) {</php>
			<php>if($file['filename'] == '..') {</php>
			<php>} else {</php>
			<div title="{$file.filename}" onclick="{$callback}.browser('<php> echo $browser.'/'.$file['filename'];</php>');" class="thumbnail">
				<i class="fa fa-folder"></i>
				<span class="text-center">{$file.filename}</span>
			</div>
			<php>}</php>
		<php>} else {</php>
			<div title="{$file.filename}" class="thumbnail<php>if($currentimage==$file['filename']){</php> active<php>}</php>">
				<em><img src="{$file.url}" onclick="{$callback}.select({$file.entry});" class="img-responsive"></em>
				<span class="text-center">{$file.filename}</span>
			</div>
		<php>}</php>
	<php>}</php>
</div>
