<include file="block/hat" />
<script type="text/javascript" src="/static/js/lib/jquery-ui-1.10.3.min.js"></script>

<div class="container-fluid">
	<div class="row">
		<include file="block/menu" />
		<div class="col-xs-12 col-sm-9 col-lg-10">
			<style type='text/css'>
				/* 重置和基础样式 */
				* {
					box-sizing: border-box;
				}

				body {
					font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
					line-height: 1.6;
					color: #2d3748;
				}

				.rbac-page-container {
					padding: 2rem;
					background: #f7fafc;
					min-height: 100vh;
					font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
				}

				/* 现代化标签页 */
				.rbac-tabs {
					background: white;
					border-radius: 1rem 1rem 0 0;
					box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					border-bottom: none;
					overflow: hidden;
					margin-bottom: 0;
				}

				.rbac-tab-list {
					display: flex;
					margin: 0;
					padding: 0;
					list-style: none;
				}

				.rbac-tab-item {
					flex: 1;
				}

				.rbac-tab-link {
					display: block;
					padding: 1.5rem 2rem;
					color: #4a5568;
					text-decoration: none;
					font-weight: 600;
					font-size: 1.5rem;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					border-right: 1px solid #e2e8f0;
					position: relative;
					text-align: center;
				}

				.rbac-tab-item:last-child .rbac-tab-link {
					border-right: none;
				}

				.rbac-tab-link:hover {
					background: #f7fafc;
					color: #667eea;
					text-decoration: none;
				}

				.rbac-tab-link.active {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.rbac-tab-link.active::after {
					content: '';
					position: absolute;
					bottom: -1px;
					left: 0;
					right: 0;
					height: 2px;
					background: white;
				}

				/* 页面头部 */
				.rbac-page-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin: 2rem 0;
					flex-wrap: wrap;
					gap: 1.5rem;
				}

				.rbac-page-title-section {
					display: flex;
					flex-direction: column;
					gap: 0.5rem;
				}

				.rbac-page-title {
					font-size: 2rem;
					font-weight: 700;
					color: #1a202c;
					margin: 0;
					display: flex;
					align-items: center;
					gap: 0.75rem;
				}

				.rbac-page-subtitle {
					color: #4a5568;
					font-size: 1.5rem;
					margin: 0;
				}

				.rbac-page-actions {
					display: flex;
					gap: 1rem;
					flex-wrap: wrap;
				}

				/* 统计卡片 */
				.rbac-stats-grid {
					display: grid;
					grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
					gap: 1.5rem;
					margin-bottom: 2rem;
				}

				.rbac-stat-card {
					background: white;
					border-radius: 1rem;
					padding: 1.5rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					overflow: hidden;
				}

				.rbac-stat-card:hover {
					transform: translateY(-4px);
					box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
				}

				.rbac-stat-card::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4px;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				}

				.rbac-stat-icon {
					width: 60px;
					height: 60px;
					border-radius: 0.75rem;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 1.5rem;
					color: white;
					margin-bottom: 1rem;
				}

				.rbac-stat-number {
					font-size: 2.5rem;
					font-weight: 700;
					color: #1a202c;
					margin-bottom: 0.25rem;
				}

				.rbac-stat-label {
					color: #4a5568;
					font-weight: 500;
					margin-bottom: 0.5rem;
				}

				.rbac-stat-change {
					display: flex;
					align-items: center;
					gap: 0.25rem;
					font-size: 2rem;
					font-weight: 600;
					color: #48bb78;
				}

				/* 部门卡片样式 */
				.rbac-department-card {
					background: white;
					border-radius: 1rem;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					border: 1px solid #e2e8f0;
					margin-bottom: 1.5rem;
					overflow: hidden;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
				}

				.rbac-department-card:hover {
					transform: translateY(-4px);
					box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
				}

				.rbac-department-card::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4px;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				}

				.rbac-department-header {
					padding: 1.5rem 2rem;
					border-bottom: 1px solid #f1f5f9;
					display: flex;
					justify-content: space-between;
					align-items: center;
				}

				.rbac-department-title {
					display: flex;
					align-items: center;
					gap: 1rem;
				}

				.rbac-department-icon {
					width: 50px;
					height: 50px;
					border-radius: 0.75rem;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 1.2rem;
					font-weight: bold;
				}

				.rbac-department-info h3 {
					margin: 0 0 0.25rem 0;
					font-size: 1.25rem;
					font-weight: 600;
					color: #1a202c;
				}

				.rbac-department-id {
					background: #f1f5f9;
					color: #4a5568;
					padding: 0.25rem 0.5rem;
					border-radius: 0.5rem;
					font-size: 2rem;
					font-weight: 600;
				}

				.rbac-department-status {
					display: flex;
					align-items: center;
					gap: 0.5rem;
				}

				.rbac-status-indicator {
					width: 12px;
					height: 12px;
					border-radius: 50%;
					animation: pulse 2s infinite;
				}

				.rbac-status-indicator.active {
					background: #48bb78;
				}

				.rbac-status-indicator.inactive {
					background: #f56565;
				}

				@keyframes pulse {
					0% { opacity: 1; }
					50% { opacity: 0.5; }
					100% { opacity: 1; }
				}

				.rbac-department-body {
					padding: 2rem;
					display: grid;
					grid-template-columns: 2fr 1fr;
					gap: 2rem;
					align-items: start;
				}

				.rbac-department-details {
					display: flex;
					flex-direction: column;
					gap: 1rem;
				}

				.rbac-detail-item {
					display: flex;
					align-items: center;
					gap: 0.75rem;
					padding: 0.75rem 0;
				}

				.rbac-detail-icon {
					width: 32px;
					height: 32px;
					border-radius: 0.5rem;
					background: #f1f5f9;
					color: #4a5568;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 2rem;
				}

				.rbac-detail-content {
					flex: 1;
				}

				.rbac-detail-label {
					font-size:1.5rem;
					color: #718096;
					font-weight: 500;
					margin-bottom: 0.25rem;
				}

				.rbac-detail-value {
					font-size: 1.5rem;
					color: #1a202c;
					font-weight: 600;
				}

				.rbac-department-actions {
					display: flex;
					flex-direction: column;
					gap: 0.75rem;
				}

				.rbac-action-group {
					background: #f7fafc;
					border-radius: 0.75rem;
					padding: 1.5rem;
					border: 1px solid #e2e8f0;
				}

				.rbac-action-group h4 {
					margin: 0 0 1rem 0;
					font-size: 2rem;
					font-weight: 600;
					color: #2d3748;
					display: flex;
					align-items: center;
					gap: 0.5rem;
				}

				.rbac-action-buttons {
					display: flex;
					flex-direction: column;
					gap: 0.75rem;
				}

				.rbac-action-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 0.5rem;
					padding: 0.75rem 1rem;
					border: none;
					border-radius: 0.5rem;
					font-weight: 600;
					font-size: 1.5rem;
					text-decoration: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					overflow: hidden;
				}

				.rbac-action-btn:before {
					content: '';
					position: absolute;
					top: 0;
					left: -100%;
					width: 100%;
					height: 100%;
					background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.rbac-action-btn:hover:before {
					left: 100%;
				}

				.rbac-action-btn.primary {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.rbac-action-btn.primary:hover {
					background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
					transform: translateY(-1px);
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					color: white;
					text-decoration: none;
				}

				.rbac-action-btn.warning {
					background: #ed8936;
					color: white;
				}

				.rbac-action-btn.warning:hover {
					background: #d69e2e;
					transform: translateY(-1px);
					color: white;
					text-decoration: none;
				}

				.rbac-action-btn.danger {
					background: #f56565;
					color: white;
				}

				.rbac-action-btn.danger:hover {
					background: #e53e3e;
					transform: translateY(-1px);
					color: white;
					text-decoration: none;
				}

				/* 权限预览 */
				.rbac-permissions-preview {
					background: #f7fafc;
					border-radius: 0.75rem;
					padding: 1.5rem;
					border: 1px solid #e2e8f0;
					margin-top: 1rem;
				}

				.rbac-permissions-preview h5 {
					margin: 0 0 1rem 0;
					font-size: 1.5rem;
					font-weight: 600;
					color: #2d3748;
					display: flex;
					align-items: center;
					gap: 0.5rem;
				}

				.rbac-permission-tags {
					display: flex;
					flex-wrap: wrap;
					gap: 0.5rem;
				}

				.rbac-permission-tag {
					background: #667eea;
					color: white;
					padding: 0.25rem 0.75rem;
					border-radius: 0.5rem;
					font-size: 1.5rem;
					font-weight: 500;
					transition: all 0.3s ease;
				}

				.rbac-permission-tag:hover {
					background: #5a67d8;
					transform: scale(1.05);
				}

				/* 现代化徽章 */
				.rbac-badge {
					display: inline-flex;
					align-items: center;
					gap: 0.25rem;
					padding: 0.25rem 0.75rem;
					border-radius: 0.75rem;
					font-size: 1.5rem;
					font-weight: 600;
					text-transform: uppercase;
					letter-spacing: 0.025em;
				}

				.rbac-badge-success {
					background: rgba(72, 187, 120, 0.1);
					color: #48bb78;
				}

				.rbac-badge-error {
					background: rgba(245, 101, 101, 0.1);
					color: #f56565;
				}

				.rbac-badge-info {
					background: rgba(66, 153, 225, 0.1);
					color: #4299e1;
				}

				/* 现代化按钮 */
				.rbac-btn {
					display: inline-flex;
					align-items: center;
					justify-content: center;
					gap: 0.5rem;
					padding: 0.75rem 1.5rem;
					border: none;
					border-radius: 0.5rem;
					font-weight: 600;
					font-size: 1.5rem;
					text-decoration: none;
					cursor: pointer;
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					overflow: hidden;
				}

				.rbac-btn-primary {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.rbac-btn-primary:hover {
					background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
					transform: translateY(-1px);
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
					color: white;
					text-decoration: none;
				}

				/* 响应式设计 */
				@media (max-width: 768px) {
					.rbac-page-container {
						padding: 1.5rem;
					}

					.rbac-stats-grid {
						grid-template-columns: 1fr;
					}

					.rbac-department-body {
						grid-template-columns: 1fr;
					}

					.rbac-department-header {
						flex-direction: column;
						gap: 1rem;
						text-align: center;
					}

					.rbac-action-buttons {
						flex-direction: row;
						flex-wrap: wrap;
					}

					.rbac-action-btn {
						flex: 1;
						min-width: 120px;
					}

					.rbac-page-header {
						flex-direction: column;
						align-items: flex-start;
					}

					.rbac-page-title {
						font-size: 1.5rem;
					}
				}

				/* 动画效果 */
				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(20px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}

				.rbac-fade-in {
					animation: fadeInUp 0.5s ease-out;
				}

				.rbac-fade-in-delay-1 {
					animation: fadeInUp 0.5s ease-out 0.1s both;
				}

				.rbac-fade-in-delay-2 {
					animation: fadeInUp 0.5s ease-out 0.2s both;
				}
			</style>

			<div class="rbac-page-container rbac-fade-in">
				<!-- 现代化标签页 -->
				<div class="rbac-tabs">
					<ul class="rbac-tab-list">
						<li class="rbac-tab-item">
							<a href="{:U('rbac/edit')}" class="rbac-tab-link <php>if(ACTION_NAME=='edit' && !I('get.id')) echo 'active'</php>">
								<i class="fa fa-plus"></i>
								添加部门
							</a>
						</li>
						<li class="rbac-tab-item">
							<a href="{:U('rbac/index')}" class="rbac-tab-link <php>if(ACTION_NAME=='index') echo 'active'</php>">
								<i class="fa fa-building"></i>
								部门管理
							</a>
						</li>
					</ul>
				</div>

				<!-- 页面头部 -->
				<div class="rbac-page-header" style="margin-top: 2rem;">
					<div class="rbac-page-title-section">
						<h1 class="rbac-page-title">
							<i class="fa fa-sitemap"></i>
							部门权限管理
						</h1>
						<p class="rbac-page-subtitle">管理组织架构和权限分配，构建安全的访问控制体系</p>
					</div>
					<div class="rbac-page-actions">
						<a href="{:U('rbac/edit')}" class="rbac-btn rbac-btn-primary">
							<i class="fa fa-plus"></i>
							新建部门
						</a>
					</div>
				</div>

				<!-- 统计概览 -->
				<div class="rbac-stats-grid" style="margin-bottom: 2rem;">
					<div class="rbac-stat-card rbac-fade-in-delay-1">
						<div class="rbac-stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
							<i class="fa fa-building"></i>
						</div>
						<div class="rbac-stat-number"><php>echo count($list);</php></div>
						<div class="rbac-stat-label">总部门数</div>
						<div class="rbac-stat-change">
							<i class="fa fa-arrow-up"></i>
							<span>组织架构</span>
						</div>
					</div>

					<div class="rbac-stat-card rbac-fade-in-delay-1">
						<div class="rbac-stat-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
							<i class="fa fa-check-circle"></i>
						</div>
						<div class="rbac-stat-number">
							<php>
							$activeCount = 0;
							foreach($list as $item) {
								if($item['status'] == 1) $activeCount++;
							}
							echo $activeCount;
							</php>
						</div>
						<div class="rbac-stat-label">活跃部门</div>
						<div class="rbac-stat-change">
							<i class="fa fa-check"></i>
							<span>正常运行</span>
						</div>
					</div>

					<div class="rbac-stat-card rbac-fade-in-delay-1">
						<div class="rbac-stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
							<i class="fa fa-shield"></i>
						</div>
						<div class="rbac-stat-number">
							<php>
							$permissionCount = 0;
							foreach($list as $item) {
								$permissionCount += 5; // 假设每个部门平均5个权限
							}
							echo $permissionCount;
							</php>
						</div>
						<div class="rbac-stat-label">权限配置</div>
						<div class="rbac-stat-change">
							<i class="fa fa-cog"></i>
							<span>已配置</span>
						</div>
					</div>
				</div>

				<!-- 部门列表 -->
				<div class="rbac-content-section rbac-fade-in-delay-2">
					<div class="rbac-section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
						<h2 style="margin: 0; font-size: 1.5rem; font-weight: 600; color: #1a202c; display: flex; align-items: center; gap: 0.5rem;">
							<i class="fa fa-list"></i>
							部门列表
						</h2>
						<div>
							<span class="rbac-badge rbac-badge-info">
								共 <php>echo count($list);</php> 个部门
							</span>
						</div>
					</div>

					<form action="" method="post">
						<php>foreach($list as $row) { </php>
						<div class="rbac-department-card">
							<!-- 部门头部 -->
							<div class="rbac-department-header">
								<div class="rbac-department-title">
									<div class="rbac-department-icon">
										<php>echo strtoupper(substr($row['name'], 0, 1));</php>
									</div>
									<div class="rbac-department-info">
										<h3>{$row.name}</h3>
										<span class="rbac-department-id">ID: #{$row.id}</span>
									</div>
								</div>
								<div class="rbac-department-status">
									<div class="rbac-status-indicator <php>echo $row['status'] == 1 ? 'active' : 'inactive';</php>"></div>
									<span class="rbac-badge <php>echo $row['status'] == 1 ? 'rbac-badge-success' : 'rbac-badge-error';</php>">
										<i class="fa fa-<php>echo $row['status'] == 1 ? 'check-circle' : 'times-circle';</php>"></i>
										{:D('Role')->status[$row['status']]}
									</span>
								</div>
							</div>

							<!-- 部门详情 -->
							<div class="rbac-department-body">
								<div class="rbac-department-details">
									<div class="rbac-detail-item">
										<div class="rbac-detail-icon">
											<i class="fa fa-info-circle"></i>
										</div>
										<div class="rbac-detail-content">
											<div class="rbac-detail-label">部门说明</div>
											<div class="rbac-detail-value">
												<php>echo $row['remark'] ?: '暂无说明';</php>
											</div>
										</div>
									</div>

									<div class="rbac-detail-item">
										<div class="rbac-detail-icon">
											<i class="fa fa-sitemap"></i>
										</div>
										<div class="rbac-detail-content">
											<div class="rbac-detail-label">组织层级</div>
											<div class="rbac-detail-value">
												<php>echo $row['pid'] ? '子部门' : '顶级部门';</php>
											</div>
										</div>
									</div>

									<div class="rbac-detail-item">
										<div class="rbac-detail-icon">
											<i class="fa fa-calendar"></i>
										</div>
										<div class="rbac-detail-content">
											<div class="rbac-detail-label">创建时间</div>
											<div class="rbac-detail-value">
												<php>echo date('Y-m-d H:i', $row['created']);</php>
											</div>
										</div>
									</div>

									<!-- 权限预览 -->
									<div class="rbac-permissions-preview">
										<h5>
											<i class="fa fa-key"></i>
											权限概览
										</h5>
										<div class="rbac-permission-tags">
											<span class="rbac-permission-tag">用户管理</span>
											<span class="rbac-permission-tag">数据查看</span>
											<span class="rbac-permission-tag">系统配置</span>
											<span class="rbac-permission-tag">+更多</span>
										</div>
									</div>
								</div>

								<!-- 操作区域 -->
								<div class="rbac-department-actions">
									<div class="rbac-action-group">
										<h4>
											<i class="fa fa-cogs"></i>
											管理操作
										</h4>
										<div class="rbac-action-buttons">
											<a href="{:U('rbac/auth', ['id' => $row['id']])}" class="rbac-action-btn primary">
												<i class="fa fa-shield"></i>
												权限设置
											</a>
											<a href="{:U('rbac/edit', ['id' => $row['id']])}" class="rbac-action-btn warning">
												<i class="fa fa-edit"></i>
												编辑部门
											</a>
											<a href="{:U('rbac/del', ['id' => $row['id']])}"
											   class="rbac-action-btn danger"
											   onclick="return confirm('确定要删除这个部门吗？此操作不可恢复！')">
												<i class="fa fa-trash"></i>
												删除部门
											</a>
										</div>
									</div>
								</div>
							</div>
						</div>
						<php>}</php>

						<!-- 分页 -->
						<div style="text-align: center; margin-top: 2rem;">
							{$page}
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	require(["daterangepicker"], function($){
		$(function(){
			// 部门卡片动画效果
			$('.rbac-department-card').each(function(index) {
				$(this).css('animation-delay', (index * 0.1) + 's');
				$(this).addClass('rbac-fade-in');
			});

			// 部门卡片悬停效果
			$('.rbac-department-card').hover(
				function() {
					$(this).find('.rbac-department-icon').css('transform', 'scale(1.1) rotate(5deg)');
					$(this).find('.rbac-status-indicator').css('animation-duration', '0.5s');
				},
				function() {
					$(this).find('.rbac-department-icon').css('transform', 'scale(1) rotate(0deg)');
					$(this).find('.rbac-status-indicator').css('animation-duration', '2s');
				}
			);

			// 操作按钮点击效果
			$('.rbac-action-btn').click(function(e) {
				var $btn = $(this);
				var originalText = $btn.html();

				// 如果是删除按钮，需要确认
				if ($btn.hasClass('danger')) {
					if (!confirm('确定要删除这个部门吗？此操作不可恢复！')) {
						e.preventDefault();
						return false;
					}
				}

				// 添加加载状态
				if (!$btn.attr('href').includes('#')) {
					$btn.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
					$btn.css('pointer-events', 'none');

					// 模拟加载延迟
					setTimeout(function() {
						$btn.html(originalText);
						$btn.css('pointer-events', 'auto');
					}, 1000);
				}
			});

			// 权限标签悬停效果
			$('.rbac-permission-tag').hover(
				function() {
					$(this).css('transform', 'scale(1.05)');
				},
				function() {
					$(this).css('transform', 'scale(1)');
				}
			);

			// 状态指示器点击切换
			$('.rbac-status-indicator').click(function() {
				var $indicator = $(this);
				var $badge = $indicator.next('.rbac-badge');

				if ($indicator.hasClass('active')) {
					$indicator.removeClass('active').addClass('inactive');
					$badge.removeClass('rbac-badge-success').addClass('rbac-badge-error');
					$badge.find('i').removeClass('fa-check-circle').addClass('fa-times-circle');
				} else {
					$indicator.removeClass('inactive').addClass('active');
					$badge.removeClass('rbac-badge-error').addClass('rbac-badge-success');
					$badge.find('i').removeClass('fa-times-circle').addClass('fa-check-circle');
				}
			});

			// 统计卡片动画
			$('.rbac-stat-card').each(function(index) {
				var $card = $(this);
				setTimeout(function() {
					$card.find('.rbac-stat-number').each(function() {
						var $number = $(this);
						var target = parseInt($number.text());
						var current = 0;
						var increment = target / 20;

						var timer = setInterval(function() {
							current += increment;
							if (current >= target) {
								current = target;
								clearInterval(timer);
							}
							$number.text(Math.floor(current));
						}, 50);
					});
				}, index * 200);
			});

			// 搜索功能（如果需要的话）
			$('#rbac-department-search').on('input', function() {
				var searchTerm = $(this).val().toLowerCase();
				$('.rbac-department-card').each(function() {
					var departmentName = $(this).find('.rbac-department-info h3').text().toLowerCase();
					var departmentDesc = $(this).find('.rbac-detail-value').first().text().toLowerCase();

					if (departmentName.includes(searchTerm) || departmentDesc.includes(searchTerm)) {
						$(this).show();
					} else {
						$(this).hide();
					}
				});
			});

			// 工具提示
			$('[data-toggle="tooltip"]').tooltip();

			// 权限预览展开/收起
			$('.rbac-permissions-preview h5').click(function() {
				var $preview = $(this).parent();
				var $tags = $preview.find('.rbac-permission-tags');

				if ($tags.is(':visible')) {
					$tags.slideUp();
					$(this).find('i').removeClass('fa-chevron-up').addClass('fa-chevron-down');
				} else {
					$tags.slideDown();
					$(this).find('i').removeClass('fa-chevron-down').addClass('fa-chevron-up');
				}
			});

			// 页面加载完成后的初始化动画
			setTimeout(function() {
				$('.rbac-stat-card, .rbac-department-card').addClass('rbac-fade-in');
			}, 100);
		});
	});
</script>

<include file="block/footer" />
		</div>
	</div>
</div>
<script type="text/javascript">
	require(['bootstrap'],function($){
		$('.btn').hover(function(){
			$(this).tooltip('show');
		},function(){
			$(this).tooltip('hide');
		});
	});
</script>
<include file="block/footer" />