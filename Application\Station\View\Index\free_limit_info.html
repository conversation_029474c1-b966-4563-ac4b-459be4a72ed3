<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
    <!-- Title from original seo block -->
    <title>免服务费额度 - {$Think.config.WEB_SITE_TITLE}</title>
    <!-- Common CSS from money.html -->
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <!-- Common JS from money.html -->
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <!-- Styles from original css-style block -->
    <style>
        :root {
            --primary-color: #00bf80;
            --secondary-color: #666;
            --card-bg: #fff;
            --border-color: #eee;
        }
        body {
            background-color: #f5f5f5; /* Match background */
        }
        /* Container padding - Adjust top padding for main header only */
        .container { 
            padding-top: 60px; /* Adjust as needed based on main header height */
            padding-left: 15px;
            padding-right: 15px;
        }

        /* Floating back arrow styles (copied from money.html) */
        .back-arrow {
            position: fixed;
            top: 55px; /* Adjusted slightly based on typical header height */
            left: 15px;
            width: 40px;
            height: 40px;
            z-index: 999;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            transition: all 0.2s;
        }
        .back-arrow::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 50%;
            width: 12px;
            height: 12px;
            border-left: 2px solid #333;
            border-bottom: 2px solid #333;
            transform: translateY(-50%) rotate(45deg);
        }
        .back-arrow:active {
            transform: scale(0.95);
            background: rgba(255, 255, 255, 0.7);
        }
        @media (max-width: 768px) {
            .back-arrow {
                top: 55px; /* Keep consistent? */
                left: 10px;
                width: 36px;
                height: 36px;
            }
            .back-arrow::before {
                left: 13px;
                width: 10px;
                height: 10px;
            }
        }
        /* End of back arrow styles */

        /* Styles copied/adapted from quota_usage.html */
        .quota-card {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        .card-title {
            font-size: 20px;
            color: #333;
            font-weight: 600;
        }
        .quota-details {
            margin: 20px 0; /* Adjusted margin */
        }
        .quota-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }
        .quota-info-item:last-child {
            border-bottom: none;
        }
        .quota-label {
            font-size: 16px;
            color: var(--secondary-color);
        }
        .quota-value {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }
        .quota-desc {
            margin-top: 30px;
        }
        .desc-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
            font-weight: 500;
        }
        .desc-list {
            list-style-position: inside;
            padding-left: 5px;
        }
        .desc-list li {
            margin-bottom: 12px;
            color: var(--secondary-color);
            line-height: 1.5;
            position: relative;
            padding-left: 20px;
        }
        .desc-list li:before {
            content: "";
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--primary-color);
            position: absolute;
            left: 5px;
            top: 8px;
        }
        /* End of copied styles */
    </style>
</head>
<body>
    <!-- Include the common header -->
    <include file="headers"/>

    <!-- Add floating back arrow -->
    <div class="back-arrow" onclick="goBack()"></div>

    <!-- Content from original main-content block -->
    <div class="container"> <!-- Use container class with adjusted padding -->
        <div class="quota-card"> 
            <div class="card-header"> 
                 <h2 class="card-title">免服务费额度详情</h2> 
            </div>

            <div class="quota-details"> 
                <div class="quota-info-item">
                    <span class="quota-label">服务站名称</span>
                    <span class="quota-value">{$serviceStationRow.service_name}</span>
                </div>
                <div class="quota-info-item">
                    <span class="quota-label">当前剩余免服务费额度</span>
                    <span class="quota-value">¥ {:number_format($freeLimitValue, 2)}</span>
                </div>
            </div>

            <div class="quota-desc"> 
                <h3 class="desc-title">说明</h3>
                <ul class="desc-list">
                    <li>在此额度内的提现将免除技术服务费。</li>
                    <li>额度在提现申请提交时扣除，在提现驳回或打款失败时返还。</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- JS from original js-script block (if any) -->
    <script type="text/javascript">
        // Add goBack function
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // Fallback redirection if no history
                window.location.href = '{:U("index/money")}'; 
            }
        }
        // Add any other specific JS if needed
    </script>
</body>
</html> 