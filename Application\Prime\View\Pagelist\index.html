<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化单页列表管理页面样式 */
                .pagelist-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .pagelist-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .pagelist-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .pagelist-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
                }

                .pagelist-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .pagelist-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .pagelist-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .pagelist-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .pagelist-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .pagelist-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .pagelist-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .pagelist-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                    text-decoration: none;
                }

                .pagelist-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .pagelist-index-add-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
                    text-decoration: none;
                }

                .pagelist-index-add-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(59, 130, 246, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化搜索面板 */
                .pagelist-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    max-height: 0;
                    opacity: 0;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .pagelist-index-search-panel.show {
                    max-height: 500px;
                    opacity: 1;
                }

                .pagelist-index-search-header {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .pagelist-index-search-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .pagelist-index-search-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .pagelist-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .pagelist-index-search-body {
                    padding: 2rem;
                }

                .pagelist-index-search-form {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 1.5rem;
                    align-items: end;
                }

                .pagelist-index-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .pagelist-index-form-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                }

                .pagelist-index-form-select,
                .pagelist-index-form-input {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e2e8f0;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-family: inherit;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background: #f8fafc;
                }

                .pagelist-index-form-select:focus,
                .pagelist-index-form-input:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                    background: white;
                }

                .pagelist-index-search-actions {
                    display: flex;
                    gap: 1rem;
                    margin-top: 1rem;
                }

                .pagelist-index-search-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .pagelist-index-search-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .pagelist-index-search-btn.btn-primary {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                }

                .pagelist-index-search-btn.btn-primary:hover {
                    color: white;
                }

                .pagelist-index-search-btn.btn-secondary {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                    color: white;
                }

                .pagelist-index-search-btn.btn-secondary:hover {
                    color: white;
                }

                /* 现代化导航标签 */
                .pagelist-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .pagelist-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .pagelist-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .pagelist-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .pagelist-index-nav-link:hover {
                    color: #3b82f6;
                    background: rgba(59, 130, 246, 0.05);
                    text-decoration: none;
                }

                .pagelist-index-nav-link.active {
                    color: #3b82f6 !important;
                    background: white !important;
                    border-bottom-color: #3b82f6 !important;
                    box-shadow: 0 -2px 4px rgba(59, 130, 246, 0.1) !important;
                    font-weight: 700 !important;
                }

                .pagelist-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
                    z-index: 1;
                }

                .pagelist-index-nav-link.active .pagelist-index-nav-icon {
                    color: #3b82f6 !important;
                    transform: scale(1.1);
                }

                .pagelist-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化列表布局 */
                .pagelist-index-list-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    margin-bottom: 2rem;
                }

                .pagelist-list-header {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .pagelist-list-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .pagelist-list-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .pagelist-list-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .pagelist-list-body {
                    padding: 0;
                }

                .pagelist-list-item {
                    display: flex;
                    align-items: center;
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #f1f5f9;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .pagelist-list-item:last-child {
                    border-bottom: none;
                }

                .pagelist-list-item:hover {
                    background: #f8fafc;
                    transform: translateX(4px);
                }

                .pagelist-list-item::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 4px;
                    background: transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .pagelist-list-item:hover::before {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                }

                .pagelist-item-id {
                    width: 80px;
                    flex-shrink: 0;
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: #3b82f6;
                    text-align: center;
                    background: #f0f9ff;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    border: 2px solid #dbeafe;
                }

                .pagelist-item-content {
                    flex: 1;
                    margin-left: 2rem;
                    min-width: 0;
                }

                .pagelist-item-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1f2937;
                    margin: 0 0 0.5rem 0;
                    line-height: 1.4;
                    word-break: break-word;
                }

                .pagelist-item-meta {
                    display: flex;
                    align-items: center;
                    gap: 1.5rem;
                    flex-wrap: wrap;
                }

                .pagelist-item-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .pagelist-status-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    color: white;
                }

                .pagelist-status-badge.label-success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                }

                .pagelist-status-badge.label-warning {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                }

                .pagelist-status-badge.label-danger {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                }

                .pagelist-status-badge.label-default {
                    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                }

                .pagelist-item-time {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.25rem;
                    color: #6b7280;
                }

                .pagelist-item-actions {
                    margin-left: 2rem;
                    flex-shrink: 0;
                    display: flex;
                    gap: 0.75rem;
                    flex-wrap: wrap;
                }

                .pagelist-item-actions .btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    white-space: nowrap;
                }

                .pagelist-item-actions .btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .pagelist-index-container {
                        padding: 1rem;
                    }

                    .pagelist-list-item {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 1rem;
                        padding: 1.5rem 1rem;
                    }

                    .pagelist-item-content {
                        margin-left: 0;
                        width: 100%;
                    }

                    .pagelist-item-meta {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 0.75rem;
                    }

                    .pagelist-item-actions {
                        margin-left: 0;
                        width: 100%;
                        justify-content: flex-start;
                    }

                    .pagelist-item-actions .btn {
                        flex: 1;
                        justify-content: center;
                        min-width: 0;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .pagelist-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .pagelist-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .pagelist-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .pagelist-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="pagelist-index-wrapper">
                <div class="pagelist-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="pagelist-index-header pagelist-index-fade-in">
                        <div class="pagelist-index-header-content">
                            <div class="pagelist-index-title">
                                <div class="pagelist-index-title-icon">
                                    <i class="fa fa-file-text-o"></i>
                                </div>
                                <div class="pagelist-index-title-text">
                                    <h1 class="pagelist-index-title-main">单页列表管理</h1>
                                    <p class="pagelist-index-title-sub">Page List Management</p>
                                </div>
                            </div>
                            <div class="pagelist-index-actions">
                                <button type="button" class="pagelist-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>
                                <a href="{:U('Pagelist/edit')}" class="pagelist-index-add-btn">
                                    <i class="fa fa-plus"></i>
                                    <span>添加单页</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化搜索面板 -->
                    <div class="pagelist-index-search-panel pagelist-index-fade-in-delay-1" id="searchPanel">
                        <div class="pagelist-index-search-header">
                            <div class="pagelist-index-search-icon">
                                <i class="fa fa-search"></i>
                            </div>
                            <h3 class="pagelist-index-search-title">搜索筛选</h3>
                        </div>
                        <div class="pagelist-index-search-body">
                            <form method="get" class="pagelist-index-search-form" role="form">
                                <div class="pagelist-index-form-group">
                                    <label class="pagelist-index-form-label">搜索字段</label>
                                    <select class="pagelist-index-form-select" name="kw">
                                        <php>foreach($c_kw as $key=>$value){</php>
                                        <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                        <php>}</php>
                                    </select>
                                </div>
                                <div class="pagelist-index-form-group">
                                    <label class="pagelist-index-form-label">搜索内容</label>
                                    <input class="pagelist-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
                                </div>
                                <div class="pagelist-index-form-group">
                                    <label class="pagelist-index-form-label">单页状态</label>
                                    <select name="status" class="pagelist-index-form-select">
                                        <option value="">全部状态</option>
                                        <php> foreach($statusList as  $k => $v) { </php>
                                        <option value="{$k}" <php>if(is_numeric($_get['status']) && $_get['status']==$k) echo 'selected';</php>>{$v.text}</option>
                                        <php> } </php>
                                    </select>
                                </div>
                                <div class="pagelist-index-search-actions">
                                    <button type="submit" class="pagelist-index-search-btn btn-primary">
                                        <i class="fa fa-search"></i>
                                        <span>搜索</span>
                                    </button>
                                    <a href="{:U('Pagelist/index')}" class="pagelist-index-search-btn btn-secondary">
                                        <i class="fa fa-refresh"></i>
                                        <span>重置</span>
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="pagelist-index-nav-container pagelist-index-fade-in-delay-2">
                        <ul class="pagelist-index-nav-tabs">
                            <li class="pagelist-index-nav-item">
                                <a href="{:U('Pagelist/index')}" class="pagelist-index-nav-link active">
                                    <i class="fa fa-list pagelist-index-nav-icon"></i>
                                    <span>全部单页</span>
                                </a>
                            </li>
                            <li class="pagelist-index-nav-item">
                                <a href="javascript:void(0)" class="pagelist-index-nav-link quick-filter" data-filter="published">
                                    <i class="fa fa-check-circle pagelist-index-nav-icon"></i>
                                    <span>已发布</span>
                                </a>
                            </li>
                            <li class="pagelist-index-nav-item">
                                <a href="javascript:void(0)" class="pagelist-index-nav-link quick-filter" data-filter="draft">
                                    <i class="fa fa-edit pagelist-index-nav-icon"></i>
                                    <span>草稿</span>
                                </a>
                            </li>
                            <li class="pagelist-index-nav-item">
                                <a href="javascript:void(0)" class="pagelist-index-nav-link quick-filter" data-filter="disabled">
                                    <i class="fa fa-times-circle pagelist-index-nav-icon"></i>
                                    <span>已禁用</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 现代化列表容器 -->
                    <form action="" method="post">
                        <div class="pagelist-index-list-container pagelist-index-fade-in-delay-3">
                            <div class="pagelist-list-header">
                                <div class="pagelist-list-icon">
                                    <i class="fa fa-list"></i>
                                </div>
                                <h3 class="pagelist-list-title">单页列表</h3>
                            </div>
                            <div class="pagelist-list-body">
                                <php>foreach($list as $v) { </php>
                                <div class="pagelist-list-item">
                                    <!-- ID显示 -->
                                    <div class="pagelist-item-id">
                                        #{$v.id}
                                    </div>

                                    <!-- 内容区域 -->
                                    <div class="pagelist-item-content">
                                        <!-- 单页标题 -->
                                        <h4 class="pagelist-item-title">
                                            {$v.title}
                                        </h4>

                                        <!-- 元信息 -->
                                        <div class="pagelist-item-meta">
                                            <!-- 状态 -->
                                            <div class="pagelist-item-status">
                                                <span class="pagelist-status-badge label-{:$statusList[$v['status']]['style']}">
                                                    <i class="fa fa-circle"></i>
                                                    <span>{:$statusList[$v['status']]['text']}</span>
                                                </span>
                                            </div>

                                            <!-- 创建时间 -->
                                            <div class="pagelist-item-time">
                                                <i class="fa fa-clock-o"></i>
                                                <span>{:date('Y-m-d H:i:s', $v['create_time'])}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="pagelist-item-actions">
                                        <php>echo PagelistStatBtn($v['id'], $v['status']);</php>
                                    </div>
                                </div>
                                <php>}</php>
                            </div>
                        </div>

                        <!-- 分页信息 -->
                        <div class="pagelist-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem;">
                            {$page}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // 设置导航标签active状态的函数
        function setActiveNavTab() {
            // 移除所有active状态
            $('.pagelist-index-nav-link').removeClass('active');

            // 获取当前URL参数
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');

            // 根据status参数设置对应的active状态
            if (status === '1') {
                // 已发布状态
                $('.quick-filter[data-filter="published"]').addClass('active');
            } else if (status === '0') {
                // 草稿状态
                $('.quick-filter[data-filter="draft"]').addClass('active');
            } else if (status === '2') {
                // 已禁用状态
                $('.quick-filter[data-filter="disabled"]').addClass('active');
            } else {
                // 默认状态（全部）
                $('.pagelist-index-nav-link').first().addClass('active');
            }
        }

        // 快速筛选功能
        $('.quick-filter').click(function(e) {
            e.preventDefault();
            var $this = $(this);
            var filter = $this.data('filter');
            var currentUrl = window.location.href.split('?')[0];
            var newUrl = currentUrl;

            // 立即更新视觉状态，提供即时反馈
            $('.pagelist-index-nav-link').removeClass('active');
            $this.addClass('active');

            // 添加加载状态
            var originalHtml = $this.html();
            $this.html('<i class="fa fa-spinner fa-spin pagelist-index-nav-icon"></i><span>加载中...</span>');

            // 获取当前的搜索参数
            var urlParams = new URLSearchParams(window.location.search);
            var searchParams = new URLSearchParams();

            // 保留搜索关键词等其他参数
            if (urlParams.get('kw')) {
                searchParams.set('kw', urlParams.get('kw'));
            }
            if (urlParams.get('val')) {
                searchParams.set('val', urlParams.get('val'));
            }

            // 添加状态参数
            switch(filter) {
                case 'published':
                    searchParams.set('status', '1');
                    break;
                case 'draft':
                    searchParams.set('status', '0');
                    break;
                case 'disabled':
                    searchParams.set('status', '2');
                    break;
                default:
                    // 不添加status参数，显示全部
                    break;
            }

            // 构建最终URL
            var paramString = searchParams.toString();
            if (paramString) {
                newUrl += '?' + paramString;
            }

            // 延迟跳转，让用户看到加载状态
            setTimeout(function() {
                window.location.href = newUrl;
            }, 300);
        });

        // 处理"全部单页"标签的点击事件
        $('.pagelist-index-nav-link').not('.quick-filter').click(function(e) {
            var $this = $(this);
            var href = $this.attr('href');

            // 如果是指向当前页面的链接，处理active状态
            if (href && href.indexOf('Pagelist/index') !== -1) {
                e.preventDefault();

                // 立即更新视觉状态
                $('.pagelist-index-nav-link').removeClass('active');
                $this.addClass('active');

                // 添加加载状态
                var originalHtml = $this.html();
                $this.html('<i class="fa fa-spinner fa-spin pagelist-index-nav-icon"></i><span>加载中...</span>');

                // 延迟跳转到不带参数的页面
                setTimeout(function() {
                    window.location.href = href;
                }, 300);
            }
        });

        // 列表项悬停效果增强
        $('.pagelist-list-item').hover(
            function() {
                $(this).addClass('hover-effect');
            },
            function() {
                $(this).removeClass('hover-effect');
            }
        );

        // 空状态处理
        var totalItems = $('.pagelist-list-item').length;
        if (totalItems === 0) {
            var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-file-text-o"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无单页数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的单页内容。</p><a href="' + '{:U("Pagelist/edit")}' + '" class="btn btn-primary" style="padding: 0.75rem 1.5rem; font-size: 1.5rem;"><i class="fa fa-plus"></i> 添加单页</a></div>');
            $('.pagelist-index-list-container .pagelist-list-body').append($emptyState);
        }

        // 延迟执行active状态设置，确保DOM完全加载
        setTimeout(function() {
            setActiveNavTab();
        }, 100);

        // 保持原有的状态切换功能
        $('.js_status').click(function() {
            var that = $(this),
                url = that.attr('url');
            $.get(url, function(data) {
                window.location.reload();
            });
        });
    });

    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
        } else {
            panel.classList.add('show');
        }
    }
</script>

<include file="block/footer" />
