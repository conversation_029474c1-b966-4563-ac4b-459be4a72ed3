<?php
/**
 * 聚合话费充值api
 */
namespace Api\Juhe;

class Mobile 
{
    public $appkey = '';

    public $http;

    public function __construct($appkey = '')
    {
        if ($appkey) {
            $this->appkey = $appkey;
        } else {
            $this->appkey = C('JUHE.M_APPKEY');
        }
        $this->http = \Vendor\Curl::getInstance();
    }

    /**
     * 检测号码能否充值
     * @return bool
       数据样本: {
            "reason": "允许充值的手机号码及金额",
            "result": null,
            "error_code": 0
       }
     */
    public function telCheck($phone, $cardnum)
    {
        $ret = false;
        if (empty($phone)) return $ret;
        $apiurl = "http://op.juhe.cn/ofpay/mobile/telcheck";
        $params = ['phoneno' => $phone, 'cardnum' => $cardnum, 'key' => $this->appkey];
        $result = $this->http->post($apiurl, $params);
        $res = json_decode($result, true);
        if ($res && $res['error_code'] == '0') {
            $ret = true;
        }
        if (! $ret) dolog('error/juhe/mobiletelcheck', $result);
        return $ret;
    }

    /**
     * 根据手机号和面值查询商品
       数据样本: {
            "reason": "成功",
            "result": {
                "cardid": "191404", 卡类ID
                "cardname": "江苏电信话费100元直充",  卡类名称
                "inprice": 98.4,  购买价格
                "game_area": "江苏苏州电信"  手机号码归属地
            },
            "error_code": 0
        }
     */
    public function telQuery($phone, $cardnum)
    {
        $ret = false;
        if (empty($phone)) return $ret;
        $apiurl = "http://op.juhe.cn/ofpay/mobile/telquery";
        $params = ['phoneno' => $phone, 'cardnum' => $cardnum, 'key' => $this->appkey];
        $result = $this->http->post($apiurl, $params);
        $res = json_decode($result, true);
        if ($res && $res['error_code'] == '0') {
            $ret = $res['result'];
        }
        if (! $ret) dolog('error/juhe/mobiletelquery', $result);
        return $ret;
    }
    
    /**
     * 提交流量充值
       数据样本: {
            "reason": "订单提交成功，等待充值",
            "result": {
                "cardid": "1900212", 充值的卡类ID
                "cardnum": "1", 数量
                "ordercash": 49.25, 进货价格
                "cardname": "江苏电信话费50元直充", 充值名称
                "sporder_id": "20141120174602882", 聚合订单号
                "uorderid":"2014123115121",商户自定的订单号
                "game_userid": "18913515122", 充值的手机号码
                "game_state": "0" 充值状态:0充值中 1成功 9撤销，刚提交都返回0
            },
            "error_code": 0
        }
     */
    public function recharge($phone, $cardnum, $orderid)
    {
        $ret = false;
        $apiurl = "http://op.juhe.cn/ofpay/mobile/onlineorder";
        $params = [
              "phoneno" => $phone,//需要充值流量的手机号码
              "cardnum" => $cardnum,//充值金额
              "orderid" => $orderid,//自定义订单号，8-32字母数字组合
              "key" => $this->appkey,//应用APPKEY
        ];
        $params['sign'] = md5(sprintf('%s%s%s%s%s', C('JUHE.OPENID'), $this->appkey, $phone, $cardnum, $orderid));
        $result = $this->http->post($apiurl, $params);
        $res = json_decode($result, true);
        if ($res && $res['error_code'] == '0') {
            $ret = $res['result'];
        }
        if (! $ret) dolog('error/juhe/mobilerecharge', $result);
        return $ret;
    }

    /**
     * 订单状态查询
        {
            "reason": "查询成功",
            "result": {
                "uordercash": "5.000", 订单扣除金额
                "sporder_id": "20150511163237508", 聚合订单号
                "game_state": "1" /*状态 1:成功 9:失败 0：充值中
            },
            "error_code": 0
        }
     */
    public function orderSta($orderid)
    {
        $ret = false;
        $apiurl = "http://op.juhe.cn/ofpay/mobile/ordersta";
        $params = ['orderid' => $orderid, 'key' => $this->appkey];
        $result = $this->http->post($apiurl, $params);
        $res = json_decode($result, true);
        if ($res && $res['error_code'] == '0') {
            $ret = $res['result'];
        }
        if (! $ret) dolog('error/juhe/mobileordersta', $result);
        return $ret;
    }

    /**
     * 检查回调签名
     */
    public function chkCallback($sporder_id, $orderid, $sign)
    {
        $sign_ = md5($this->appkey.$sporder_id.$orderid);
        return $sign == $sign_;
    }

}