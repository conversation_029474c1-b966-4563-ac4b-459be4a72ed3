<?php

namespace Prime\Controller;
use Common\Controller\PrimeController;

class UserController extends PrimeController
{
    
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 列表
     */
    public function index()
    {

        $c_kw = [
            'nickname' => '微信昵称',
            'id' => 'ID',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_service_id = I("get.service_id");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);
        if ($s_status != '') $where['status'] = $s_status;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['name']))  {
                $where[$s_kw] = ['like', "%$s_val%"];
            }  else {
                $where[$s_kw] = $s_val;
            }
        }
        //公用搜索
        $contions = I('get.');
        $contionsWhere = ['group', 'is_out'];
        foreach ($contions as $whereKey => $row) {
            if (in_array($whereKey, $contionsWhere) && $row != '') {
                $where[$whereKey] = $row;
            }
        }

        // service_id筛选
        if ($s_service_id !== '') {
            $where['service_id'] = $s_service_id;
        }

        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['create_time'] = ['between', [$s_start, $s_end]];
        }

        $obj = D("User");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        $serviceStationList = [];
        if ($list) {
            $service_station_arr_id = array_unique(array_column($list, 'service_station_id'));
            $self_service_station_id = array_unique(array_column($list, 'self_service_station_id'));

            $serviceStationArrId = array_unique(array_merge($service_station_arr_id, $self_service_station_id));
            if ($serviceStationArrId) {
               $serviceStationList =  D("ServiceStation")->where(['id' => ['in', $serviceStationArrId]])->getField('id,service_name', true);
            }
        }
        $this->assign('serviceStationList', $serviceStationList);
        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '0');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '0');
        $this->assign('s_service_id', $s_service_id);
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('statusList', $obj->status);
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    public function loginsout()  {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误!!');
        $selfUserRow = D("User")->where(['id' => $id])->find();
        if ($selfUserRow) {
            $boolean = D("User")->save(['id' => $selfUserRow['id'], 'self_service_station_id' => 0, 'is_service_station' => 0, 'is_first_wechat' => 0]);
            if ($boolean) {
                return $this->success('退出登录成功!!', U('user/index'));
            }
        }
        $this->error('退出错误');
    }


    /**
     * 列表
     */
    public function jobuser()
    {

        $c_kw = [
            'nickname' => '微信昵称',
            'id' => 'ID',
        ];
        $where = [
            'service_id' => 2,
        ];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);
        if ($s_status != '') $where['status'] = $s_status;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['name']))  {
                $where[$s_kw] = ['like', "%$s_val%"];
            }  else {
                $where[$s_kw] = $s_val;
            }
        }
        //公用搜索
        $contions = I('get.');
        $contionsWhere = ['group', 'is_out'];
        foreach ($contions as $whereKey => $row) {
            if (in_array($whereKey, $contionsWhere) && $row != '') {
                $where[$whereKey] = $row;
            }
        }

        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['create_time'] = ['between', [$s_start, $s_end]];
        }

        $obj = D("User");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        $serviceStationList = [];
        if ($list) {
            $service_station_arr_id = array_unique(array_column($list, 'service_station_id'));
            $self_service_station_id = array_unique(array_column($list, 'self_service_station_id'));

            $serviceStationArrId = array_unique(array_merge($service_station_arr_id, $self_service_station_id));
            if ($serviceStationArrId) {
                $serviceStationList =  D("ServiceStation")->where(['id' => ['in', $serviceStationArrId]])->getField('id,service_name', true);
            }
        }
        $this->assign('serviceStationList', $serviceStationList);
        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '0');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '0');
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('statusList', $obj->status);
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 修改用户service_id
     */
    public function changeServiceId()
    {
        if (IS_POST) {
            $id = I('post.id', 0, 'intval');
            $service_id = I('post.service_id', 0, 'intval');

            if (!$id) {
                $this->ajaxReturn(['status' => 0, 'msg' => '用户ID不能为空']);
            }

            if (!in_array($service_id, [0, 1, 2])) {
                $this->ajaxReturn(['status' => 0, 'msg' => 'service_id只能是0、1或2']);
            }

            $userModel = D('User');
            $user = $userModel->where(['id' => $id])->find();
            if (!$user) {
                $this->ajaxReturn(['status' => 0, 'msg' => '用户不存在']);
            }

            $result = $userModel->where(['id' => $id])->save(['service_id' => $service_id]);
            if ($result !== false) {
                $this->ajaxReturn(['status' => 1, 'msg' => 'service_id修改成功']);
            } else {
                $this->ajaxReturn(['status' => 0, 'msg' => '修改失败']);
            }
        }

        $this->ajaxReturn(['status' => 0, 'msg' => '非法请求']);
    }

}