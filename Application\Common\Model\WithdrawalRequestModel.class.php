<?php
namespace Common\Model;

use Think\Model;

class WithdrawalRequestModel extends Model
{
    protected $_auto = [
        ['created_at', 'time', self::MODEL_INSERT, 'function'],
        ['updated_at', 'time', self::MODEL_BOTH, 'function'],
        ['request_time', 'time', self::MODEL_INSERT, 'function'],
        ['service_fee', 'calculateServiceFee', self::MODEL_INSERT, 'callback'],
        ['actual_amount', 'calculateActualAmount', self::MODEL_INSERT, 'callback'],
    ];

    // 可以根据需要添加验证规则
    protected $_validate = [
        ['amount', 'require', '提现金额不能为空！'],
        ['amount', 'currency', '提现金额格式不正确！'],
        ['amount', 'checkAmount', '提现金额必须大于0！', self::MUST_VALIDATE, 'callback'],
        ['platform_type', 'require', '必须选择提现平台类型！'],
        ['bank_card_id', 'checkBankCardId', '必须选择提现银行卡！', self::EXISTS_VALIDATE, 'callback'],
        ['linggong_binding_id', 'checkLinggongBindingId', '必须选择灵工平台账户！', self::EXISTS_VALIDATE, 'callback'],
    ];

    // 提现状态
    public $status = [
        '1' => ['text' => '待审核', 'style' => 'warning'],      // 用户提交申请
        '2' => ['text' => '待打款', 'style' => 'info'],         // 财务审核通过
        '3' => ['text' => '已驳回', 'style' => 'danger'],       // 财务审核拒绝
        '4' => ['text' => '打款完成', 'style' => 'success'],    // 财务确认打款
        '5' => ['text' => '打款失败', 'style' => 'danger'],     // 财务打款失败
        '6' => ['text' => '待开票', 'style' => 'primary'],    // 特殊流程状态 (如果需要)
    ];
    
    // 添加税费处理方式定义
    public $tax_handling = [
        '1' => ['text' => '平台负责完税', 'style' => 'success'],
        '2' => ['text' => '需用户开票', 'style' => 'warning'],
        '3' => ['text' => '混合处理', 'style' => 'info'],
    ];
    
    // 提现平台类型
    public $platform_types = [
        'bank' => '银行卡',
        'linggong' => '灵工平台'
    ];

    /**
     * 验证金额是否大于0
     */
    protected function checkAmount($value){
        return $value > 0;
    }
    
    /**
     * 验证银行卡ID
     * 如果platform_type为bank，则必须提供bank_card_id
     */
    protected function checkBankCardId($value){
        if ($this->data['platform_type'] == 'bank') {
            return !empty($value) && $value > 0;
        }
        return true; // 其他平台类型不验证银行卡
    }
    
    /**
     * 验证灵工平台绑定ID
     * 如果platform_type为linggong，则必须提供linggong_binding_id
     */
    protected function checkLinggongBindingId($value){
        if ($this->data['platform_type'] == 'linggong') {
            return !empty($value) && $value > 0;
        }
        return true; // 其他平台类型不验证灵工平台
    }
    
    /**
     * 获取提现手续费率
     * @return float 手续费率，小数形式（如0.08表示8%）
     */
    public static function getServiceFeeRate() {
        // 从配置表获取费率，如果没有配置则使用默认值0.08
        $rate = M('Conf')->where(['name' => 'withdrawal_service_fee_rate'])->getField('value');
        return $rate ? floatval($rate) : 0.08;
    }
    
    /**
     * 自动计算服务费
     * @param mixed $value 当前值(忽略)
     * @return float 计算后的服务费
     */
    protected function calculateServiceFee($value) {
        // 如果 $this->data 中已经显式设置了 service_fee，则直接使用该值
        if (isset($this->data['service_fee']) && is_numeric($this->data['service_fee'])) { // 增加检查确保值有效
            return floatval($this->data['service_fee']);
        }
        // 否则，执行原有的计算逻辑
        $amount = isset($this->data['amount']) ? floatval($this->data['amount']) : 0; // 确保 amount 是浮点数
        $rate = self::getServiceFeeRate();
        return round($amount * $rate, 2);
    }
    
    /**
     * 自动计算实际到账金额(扣除服务费)
     * @param mixed $value 当前值(忽略)
     * @return float 计算后的实际到账金额
     */
    protected function calculateActualAmount($value) {
        $amount = isset($this->data['amount']) ? floatval($this->data['amount']) : 0; // 确保 amount 是浮点数
        // 假设 calculateServiceFee 已经执行并填充了 $this->data['service_fee']
        // 如果 service_fee 未设置或无效，则默认为0，防止计算错误
        $serviceFee = isset($this->data['service_fee']) && is_numeric($this->data['service_fee']) ? floatval($this->data['service_fee']) : 0;
        return $amount - $serviceFee;
    }
    
    /**
     * 计算税费处理分配金额
     * @param float $amount 提现金额
     * @param array $quotaInfo 额度信息
     * @return array [税费处理方式, 平台负责完税金额, 用户需开票金额]
     */
    public static function calculateTaxDistribution($amount, $quotaInfo) {
        $remaining = $quotaInfo['remaining_quota'];
        
        // 情况1: 额度充足，全部平台完税
        if ($remaining >= $amount) {
            return [1, $amount, 0];
        }
        
        // 情况2: 额度为0，全部需开票
        if ($remaining <= 0) {
            return [2, 0, $amount];
        }
        
        // 情况3: 混合情况，部分平台完税，部分用户开票
        return [3, $remaining, $amount - $remaining];
    }
} 