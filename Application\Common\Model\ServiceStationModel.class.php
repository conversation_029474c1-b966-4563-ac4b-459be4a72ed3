<?php
namespace Common\Model;

use Think\Model;

class ServiceStationModel extends Model
{
    protected $_auto = [ 
        ['create_time', 'time', 1, 'function'],
        ['upadte_time', 'time', 2, 'function'],
    ];

    public $status = [
        '0' => ['text' => '待审核', 'style' => 'info'],
        '1' => ['text' => '审核成功', 'style' => 'success'],
        '2' => ['text' => '审核失败', 'style' => 'danger'],
    ];

    /**
     * 禁用状态定义
     */
    public $disabledStatus = [
        '0' => ['text' => '正常', 'style' => 'success'],
        '1' => ['text' => '已禁用', 'style' => 'danger'],
    ];

    public $type = [
        '0' => ['text' => '未设置', 'style' => 'info'],
        '1' => ['text' => '外部', 'style' => 'info'],
        '2' => ['text' => '招就办', 'style' => 'success'],

    ];

    public $level = [
        '0' => ['text' => '普通', 'style' => 'info'],
        '1' => ['text' => 'V1', 'style' => 'info'],
        '2' => ['text' => 'V2', 'style' => 'info'],
        '3' => ['text' => 'V3', 'style' => 'info'],
    ];


    public $resources = [
        '0' => ['text' => '不显示', 'style' => 'info'],
        '1' => ['text' => '显示', 'style' => 'success'],
    ];

    public $is_out = [
        '0' => ['text' => '不允许', 'style' => 'info'],
        '1' => ['text' => '允许', 'style' => 'success'],
    ];

    public $showList = [
        '0' => ['text' => '不显示', 'style' => 'info'],
        '1' => ['text' => '显示', 'style' => 'success'],
    ];


    public $opentype = [
        '0' => ['text' => '推荐', 'style' => 'info'],
        '1' => ['text' => '资源包', 'style' => 'info'],
        ];

    public $buyList = [
        '0' => ['text' => '已付款', 'style' => 'info'],
        '1' => ['text' => '预付款', 'style' => 'info'],
        '2' => ['text' => '部分付款', 'style' => 'info'],
        '3' => ['text' => '推荐服务站资源包抵扣', 'style' => 'info'],
        ];

    /**
     * 招就办类型定义
     */
    public $zsbType = [
        '1' => ['text' => '服务站', 'style' => 'primary'],
        '2' => ['text' => '招就办', 'style' => 'success'],
    ];

    /**
     * 验证招就办是否属于指定服务站
     * @param int $zsbId 招就办ID
     * @param int $stationId 服务站ID
     * @return bool
     */
    public function validateZsbBelongsToStation($zsbId, $stationId)
    {
        $zsb = $this->where([
            'id' => $zsbId,
            'zsb_ref_station' => $stationId,
            'zsb_type' => 2,
            'status' => 1,
            'is_disabled' => 0  // 添加禁用状态检查
        ])->find();

        return !empty($zsb);
    }

    /**
     * 获取服务站下的招就办列表
     * @param int $stationId 服务站ID
     * @param array $condition 额外查询条件
     * @return array
     */
    public function getZsbListByStation($stationId, $condition = [])
    {
        $where = [
            'zsb_ref_station' => $stationId,
            'zsb_type' => 2,
            'status' => 1,
            'is_disabled' => 0  // 默认只获取未禁用的招就办
        ];

        if (!empty($condition)) {
            $where = array_merge($where, $condition);
        }

        return $this->where($where)
            ->field('id, service_name, contract_name, mobile, mail_address, create_time, is_disabled')
            ->order('id DESC')
            ->select();
    }

    /**
     * 验证招就办基本信息
     * @param array $data 招就办数据
     * @return bool
     */
    public function validateZsbData($data)
    {
        // 必填字段验证
        $requiredFields = ['service_name', 'contract_name', 'mobile', 'mail_address'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $this->error = "字段 {$field} 不能为空";
                return false;
            }
        }

        // 手机号验证
        if (!preg_match('/^1[3-9]\d{9}$/', $data['mobile'])) {
            $this->error = '手机号格式不正确';
            return false;
        }

        // 检查手机号是否已存在（排除当前记录）
        $where = ['mobile' => $data['mobile'], 'zsb_type' => 2];
        if (isset($data['id']) && $data['id']) {
            $where['id'] = ['neq', $data['id']];
        }

        $exists = $this->where($where)->find();
        if ($exists) {
            $this->error = '该手机号已被其他招就办使用';
            return false;
        }

        return true;
    }

    /**
     * 添加服务站余额
     * @param int $stationId 服务站ID
     * @param int $amount 金额（分单位）
     * @param bool $withdrawable 是否可提现，true=可提现，false=不可提现
     * @param string $remark 备注
     * @return bool
     */
    public function addBalance($stationId, $amount, $withdrawable = true, $remark = '')
    {
        if ($amount <= 0) {
            \Think\Log::write('添加余额失败：金额必须大于0 station_id=' . $stationId . ', amount=' . $amount, 'ERROR');
            return false;
        }

        try {
            $this->startTrans();

            // 获取服务站信息
            $station = $this->where(['id' => $stationId])->find();
            if (!$station) {
                \Think\Log::write('添加余额失败：服务站不存在 station_id=' . $stationId, 'ERROR');
                $this->rollback();
                return false;
            }

            // 转换为元单位进行余额操作
            $amountYuan = round($amount / 100, 2);

            if ($withdrawable) {
                // 可提现余额：更新price字段
                $updateResult = $this->where(['id' => $stationId])->setInc('price', $amountYuan);
                $balanceType = '可提现余额';
            } else {
                // 不可提现余额：更新total_price字段，但不更新price字段
                $updateResult = $this->where(['id' => $stationId])->setInc('total_price', $amountYuan);
                $balanceType = '不可提现余额';
            }

            if ($updateResult === false) {
                \Think\Log::write('添加余额失败：数据库更新失败 station_id=' . $stationId . ', amount=' . $amountYuan, 'ERROR');
                $this->rollback();
                return false;
            }

            // 记录资金流水
            $moneyData = [
                'service_station_id' => $stationId,
                'type' => 2, // 2表示收入
                'money' => $amountYuan, // 使用元单位
                'create_time' => time(),
                'remark' => $remark ?: ($balanceType . '增加'),
                'status' => 4, // 4表示成功
            ];

            $moneyResult = D('StationMoney')->add($moneyData);
            if (!$moneyResult) {
                \Think\Log::write('添加余额失败：资金流水记录失败 station_id=' . $stationId, 'ERROR');
                $this->rollback();
                return false;
            }

            $this->commit();

            \Think\Log::write('添加余额成功 station_id=' . $stationId . ', amount=' . $amountYuan . '元(' . $balanceType . ')', 'INFO');
            return true;

        } catch (\Exception $e) {
            $this->rollback();
            \Think\Log::write('添加余额异常：' . $e->getMessage() . ' station_id=' . $stationId, 'ERROR');
            return false;
        }
    }

    /**
     * 获取服务站余额信息
     * @param int $stationId 服务站ID
     * @return array|false
     */
    public function getBalanceInfo($stationId)
    {
        $station = $this->where(['id' => $stationId])
            ->field('id, service_name, price, freeze_price, total_price')
            ->find();

        if (!$station) {
            return false;
        }

        return [
            'station_id' => $station['id'],
            'station_name' => $station['service_name'],
            'available_balance' => floatval($station['price']), // 可提现余额（元）
            'frozen_balance' => floatval($station['freeze_price']), // 冻结余额（元）
            'total_balance' => floatval($station['total_price']), // 总余额（元）
            'non_withdrawable_balance' => floatval($station['total_price']) - floatval($station['price']), // 不可提现余额（元）
        ];
    }

    /**
     * 检查招就办是否被禁用
     * @param int $zsbId 招就办ID
     * @return bool true=已禁用，false=正常
     */
    public function isZsbDisabled($zsbId)
    {
        $zsb = $this->where([
            'id' => $zsbId,
            'zsb_type' => 2
        ])->field('is_disabled')->find();

        return $zsb ? (bool)$zsb['is_disabled'] : true;
    }

    /**
     * 设置招就办禁用状态
     * @param int $zsbId 招就办ID
     * @param int $disabled 禁用状态 0=正常 1=禁用
     * @return bool
     */
    public function setZsbDisabledStatus($zsbId, $disabled)
    {
        $result = $this->where([
            'id' => $zsbId,
            'zsb_type' => 2
        ])->save(['is_disabled' => $disabled]);

        return $result !== false;
    }

}