<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化页面容器 */
                .message-reply-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .message-reply-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .message-reply-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .message-reply-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .message-reply-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .message-reply-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .message-reply-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .message-reply-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .message-reply-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .message-reply-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .message-reply-breadcrumb {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    color: #718096;
                }

                .message-reply-breadcrumb a {
                    color: #667eea;
                    text-decoration: none;
                    transition: color 0.3s ease;
                }

                .message-reply-breadcrumb a:hover {
                    color: #5a67d8;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .message-reply-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .message-reply-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .message-reply-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .message-reply-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .message-reply-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .message-reply-nav-link.active {
                    color: #667eea;
                    background: white;
                    border-bottom-color: #667eea;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1);
                }

                .message-reply-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .message-reply-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化聊天容器 */
                .message-reply-chat-container {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                .message-reply-chat-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;
                    height: 600px;
                }

                .message-reply-panel-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .message-reply-panel-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .message-reply-panel-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .message-reply-panel-body {
                    flex: 1;
                    padding: 1.5rem;
                    overflow-y: auto;
                    background: #f8fafc;
                }

                /* 现代化聊天记录样式 */
                .message-reply-chat-list {
                    display: flex;
                    flex-direction: column;
                    gap: 1rem;
                    padding: 0.5rem;
                }

                .message-reply-message-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .message-reply-message-time {
                    text-align: center;
                    font-size: 1.25rem;
                    color: #9ca3af;
                    margin: 1rem 0;
                    position: relative;
                }

                .message-reply-message-time::before,
                .message-reply-message-time::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    width: 30%;
                    height: 1px;
                    background: #e5e7eb;
                }

                .message-reply-message-time::before {
                    left: 0;
                }

                .message-reply-message-time::after {
                    right: 0;
                }

                .message-reply-message-item {
                    display: flex;
                    gap: 0.75rem;
                    max-width: 80%;
                    animation: messageSlideIn 0.3s ease-out;
                }

                .message-reply-message-left {
                    align-self: flex-start;
                }

                .message-reply-message-right {
                    align-self: flex-end;
                    flex-direction: row-reverse;
                }

                .message-reply-avatar {
                    width: 2.5rem;
                    height: 2.5rem;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1rem;
                    font-weight: 600;
                    color: white;
                    flex-shrink: 0;
                }

                .message-reply-avatar-platform {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .message-reply-avatar-station {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                }

                .message-reply-avatar-zjb {
                    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
                }

                .message-reply-avatar-unknown {
                    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
                }

                .message-reply-message-content {
                    display: flex;
                    flex-direction: column;
                    gap: 0.25rem;
                }

                .message-reply-sender-name {
                    font-size: 1.25rem;
                    color: #6b7280;
                    font-weight: 500;
                }

                /* 不同身份的发送者名称样式 */
                .sender-platform {
                    color: #e74c3c !important;
                    font-weight: 600 !important;
                    background: #ffeaea;
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 1.1rem !important;
                    display: inline-block;
                }

                .sender-station {
                    color: #3498db !important;
                    font-weight: 600 !important;
                    background: #e8f4fd;
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 1.1rem !important;
                    display: inline-block;
                }

                .sender-zjb {
                    color: #f39c12 !important;
                    font-weight: 600 !important;
                    background: #fef9e7;
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 1.1rem !important;
                    display: inline-block;
                }

                .sender-unknown {
                    color: #95a5a6 !important;
                    font-weight: 500 !important;
                    background: #f8f9fa;
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 1.1rem !important;
                    display: inline-block;
                }

                .message-reply-message-bubble {
                    padding: 0.75rem 1rem;
                    border-radius: 1rem;
                    font-size: 1.5rem;
                    line-height: 1.5;
                    word-wrap: break-word;
                    position: relative;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                /* 消息删除按钮 */
                .message-delete-btn {
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    width: 20px;
                    height: 20px;
                    background: #ef4444;
                    color: white;
                    border: none;
                    border-radius: 50%;
                    font-size: 12px;
                    cursor: pointer;
                    display: none;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.2s ease;
                    z-index: 10;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                }

                .message-delete-btn:hover {
                    background: #dc2626;
                    transform: scale(1.1);
                }

                .message-reply-message-item:hover .message-delete-btn {
                    display: flex;
                }

                .message-reply-bubble-left {
                    background: white;
                    color: #374151;
                    border: 1px solid #e5e7eb;
                    border-bottom-left-radius: 0.25rem;
                }

                .message-reply-bubble-right {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-bottom-right-radius: 0.25rem;
                }

                .message-reply-need-reply-indicator {
                    background: #fef3c7;
                    border: 1px solid #f59e0b;
                    color: #92400e;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    text-align: center;
                    margin: 1rem 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                }

                /* 现代化回复表单 */
                .message-reply-form-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }

                .message-reply-form-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .message-reply-form-body {
                    padding: 2rem;
                }

                .message-reply-form {
                    display: flex;
                    flex-direction: column;
                    gap: 1.5rem;
                }

                .message-reply-textarea-container {
                    position: relative;
                }

                .message-reply-textarea {
                    width: 100%;
                    min-height: 120px;
                    padding: 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    line-height: 1.5;
                    resize: vertical;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                }

                .message-reply-textarea:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .message-reply-form-actions {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                .message-reply-form-options {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .message-reply-checkbox-container {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    color: #374151;
                }

                .message-reply-checkbox {
                    width: 1.25rem;
                    height: 1.25rem;
                    accent-color: #667eea;
                }

                .message-reply-submit-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .message-reply-submit-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                }

                .message-reply-submit-btn:active {
                    transform: translateY(0);
                }

                .message-reply-submit-btn:disabled {
                    background: #9ca3af;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: none;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .message-reply-container {
                        padding: 1.5rem;
                    }

                    .message-reply-chat-container {
                        grid-template-columns: 1fr;
                        gap: 1.5rem;
                    }

                    .message-reply-header-content {
                        flex-direction: column;
                        text-align: center;
                    }
                }

                @media (max-width: 768px) {
                    .message-reply-container {
                        padding: 1rem;
                    }

                    .message-reply-nav-tabs {
                        flex-direction: column;
                    }

                    .message-reply-nav-item {
                        flex: none;
                    }

                    .message-reply-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .message-reply-nav-link.active {
                        border-bottom: none;
                        border-right-color: #667eea;
                    }

                    .message-reply-title-main {
                        font-size: 1.75rem;
                    }

                    .message-reply-title-sub {
                        font-size: 1.25rem;
                    }

                    .message-reply-form-actions {
                        flex-direction: column;
                        align-items: stretch;
                    }

                    .message-reply-chat-panel {
                        height: 400px;
                    }
                }

                /* 动画效果 */
                @keyframes messageSlideIn {
                    from {
                        opacity: 0;
                        transform: translateY(10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .message-reply-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .message-reply-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .message-reply-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .message-reply-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="message-reply-wrapper">
                <div class="message-reply-container">
                    <!-- 现代化页面标题 -->
                    <div class="message-reply-header message-reply-fade-in">
                        <div class="message-reply-header-content">
                            <div class="message-reply-title">
                                <div class="message-reply-title-icon">
                                    <i class="fa fa-comments"></i>
                                </div>
                                <div class="message-reply-title-text">
                                    <h1 class="message-reply-title-main">消息回复</h1>
                                    <p class="message-reply-title-sub">Message Reply System</p>
                                </div>
                            </div>
                            <div class="message-reply-breadcrumb">
                                <a href="{:U('Message/index')}">
                                    <i class="fa fa-list"></i>
                                    留言管理
                                </a>
                                <i class="fa fa-chevron-right"></i>
                                <span>消息回复</span>
                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="message-reply-nav-container message-reply-fade-in-delay-1">
                        <ul class="message-reply-nav-tabs">
                            <li class="message-reply-nav-item">
                                <a href="{:U('Message/index')}" class="message-reply-nav-link">
                                    <i class="fa fa-list message-reply-nav-icon"></i>
                                    <span>留言管理</span>
                                </a>
                            </li>
                            <li class="message-reply-nav-item">
                                <a href="javascript:void(0)" class="message-reply-nav-link">
                                    <i class="fa fa-edit message-reply-nav-icon"></i>
                                    <span>编辑</span>
                                </a>
                            </li>
                            <li class="message-reply-nav-item">
                                <a href="{:U('Message/reply', ['user_job_id' => $userJobId])}" class="message-reply-nav-link active">
                                    <i class="fa fa-reply message-reply-nav-icon"></i>
                                    <span>消息回复</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 现代化聊天界面 -->
                    <div class="message-reply-chat-container message-reply-fade-in-delay-2">
                        <!-- 聊天记录面板 -->
                        <div class="message-reply-chat-panel">
                            <div class="message-reply-panel-header">
                                <div class="message-reply-panel-icon">
                                    <i class="fa fa-history"></i>
                                </div>
                                <h3 class="message-reply-panel-title">{:$serviceStationRow['service_name']} - {:$jobRow['name']} 沟通记录</h3>
                            </div>
                            <div class="message-reply-panel-body">
                                <!-- 提醒指示器 -->
                                <php>if($jobRow['need_reply']) {</php>
                                <div class="message-reply-need-reply-indicator">
                                    <i class="fa fa-bell"></i>
                                    <span>已提醒服务站回复信息</span>
                                </div>
                                <php>}</php>

                                <!-- 聊天记录列表 -->
                                <div class="message-reply-chat-list" id="recordList">
                                    <php>
                                    $lastDate = '';
                                    foreach ($jobList as $jobRows) {
                                        $currentDate = date("Y年m月d日", $jobRows['create_time']);
                                        if ($currentDate !== $lastDate) {
                                            echo '<div class="message-reply-message-time">' . $currentDate . '</div>';
                                            $lastDate = $currentDate;
                                        }

                                        // 获取发送者身份信息
                                        $senderInfo = '';
                                        $senderClass = '';
                                        $avatarIcon = '';
                                        $avatarClass = '';

                                        if ($jobRows['type'] == 2) {
                                            // 平台发送的消息
                                            $senderInfo = '平台';
                                            $senderClass = 'sender-platform';
                                            $avatarIcon = 'fa-desktop';
                                            $avatarClass = 'message-reply-avatar-platform';
                                        } else {
                                            // 服务站或招就办发送的消息
                                            if (isset($serviceStationMap[$jobRows['service_station_id']])) {
                                                $station = $serviceStationMap[$jobRows['service_station_id']];
                                                if ($station['zsb_type'] == 1) {
                                                    $senderInfo = '服务站-' . $station['service_name'];
                                                    $senderClass = 'sender-station';
                                                    $avatarIcon = 'fa-building';
                                                    $avatarClass = 'message-reply-avatar-station';
                                                } else {
                                                    $senderInfo = '招就办-' . $station['contract_name'];
                                                    $senderClass = 'sender-zjb';
                                                    $avatarIcon = 'fa-graduation-cap';
                                                    $avatarClass = 'message-reply-avatar-zjb';
                                                }
                                            } else {
                                                $senderInfo = '未知服务站';
                                                $senderClass = 'sender-unknown';
                                                $avatarIcon = 'fa-question';
                                                $avatarClass = 'message-reply-avatar-unknown';
                                            }
                                        }
                                    </php>

                                    <php>if ($jobRows['type'] == 2) {</php>
                                        <!-- 平台消息（右侧） -->
                                        <div class="message-reply-message-item message-reply-message-right" data-message-id="{$jobRows.id}">
                                            <div class="message-reply-avatar {:$avatarClass}">
                                                <i class="fa {:$avatarIcon}"></i>
                                            </div>
                                            <div class="message-reply-message-content">
                                                <div class="message-reply-sender-name {:$senderClass}">{:$senderInfo}</div>
                                                <div class="message-reply-message-bubble message-reply-bubble-right">
                                                    {:htmlspecialchars_decode($jobRows['content'])}
                                                    <button class="message-delete-btn" onclick="deleteMessage({:$jobRows['id']})" title="删除消息">
                                                        <i class="fa fa-times"></i>
                                                    </button>
                                                </div>
                                                <div class="message-reply-message-time" style="text-align: right; font-size: 1rem; color: #9ca3af; margin-top: 0.25rem;">
                                                    {:date("H:i", $jobRows['create_time'])}
                                                </div>
                                            </div>
                                        </div>
                                    <php>} else {</php>
                                        <!-- 服务站/招就办消息（左侧） -->
                                        <div class="message-reply-message-item message-reply-message-left" data-message-id="{$jobRows.id}">
                                            <div class="message-reply-avatar {:$avatarClass}">
                                                <i class="fa {:$avatarIcon}"></i>
                                            </div>
                                            <div class="message-reply-message-content">
                                                <div class="message-reply-sender-name {:$senderClass}">{:$senderInfo}</div>
                                                <div class="message-reply-message-bubble message-reply-bubble-left">
                                                    {:htmlspecialchars_decode($jobRows['content'])}
                                                    <button class="message-delete-btn" onclick="deleteMessage({:$jobRows['id']})" title="删除消息">
                                                        <i class="fa fa-times"></i>
                                                    </button>
                                                </div>
                                                <div class="message-reply-message-time" style="text-align: left; font-size: 1rem; color: #9ca3af; margin-top: 0.25rem;">
                                                    {:date("H:i", $jobRows['create_time'])}
                                                </div>
                                            </div>
                                        </div>
                                    <php>}</php>
                                    <php>}</php>
                                </div>
                            </div>
                        </div>

                        <!-- 回复表单面板 -->
                        <div class="message-reply-form-container">
                            <div class="message-reply-form-header">
                                <div class="message-reply-panel-icon">
                                    <i class="fa fa-reply"></i>
                                </div>
                                <h3 class="message-reply-panel-title">回复留言</h3>
                            </div>
                            <div class="message-reply-form-body">
                                <form action="{:U('Message/reply', ['user_job_id' => $userJobId])}" method="post" class="message-reply-form" enctype="multipart/form-data" id="form1">
                                    <div class="message-reply-textarea-container">
                                        <textarea name="content" class="message-reply-textarea richtext" placeholder="请输入回复内容..." required></textarea>
                                    </div>

                                    <div class="message-reply-form-actions">
                                        <div class="message-reply-form-options">
                                            <div class="message-reply-checkbox-container">
                                                <input type="checkbox" name="needreply" value="1" class="message-reply-checkbox" id="needReplyCheckbox">
                                                <label for="needReplyCheckbox">提醒对方回复</label>
                                            </div>
                                            <div class="message-reply-checkbox-container">
                                                <input type="checkbox" name="send_wechat" value="1" class="message-reply-checkbox" id="sendWechatCheckbox" checked>
                                                <label for="sendWechatCheckbox">发送微信通知</label>
                                            </div>
                                        </div>

                                        <div>
                                            <button type="button" onclick="sendReply()" class="message-reply-submit-btn" id="sendReplyBtn">
                                                <i class="fa fa-send"></i>
                                                <span>发送回复</span>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />

<script>
    require(["layer", 'util'], function(layer, u){
        $(function () {
            // 初始化富文本编辑器
            try {
                window.editor = u.editor($('.richtext')[0]);
            } catch (err) {
                // 如果富文本编辑器初始化失败，移除 richtext 类，使用普通文本框
                $('.richtext').removeClass('richtext');
            }

            // 发送回复函数 - 完全独立的实现
            window.sendReply = function() {
                console.log('开始发送回复...');

                // 获取内容 - 多种方式确保获取到内容
                var content = '';

                // 方法1：尝试从富文本编辑器获取内容
                try {
                    if (window.editor) {
                        // 尝试同步编辑器内容
                        if (window.editor.sync) {
                            window.editor.sync();
                        }
                        // 尝试获取编辑器内容
                        if (window.editor.getContent) {
                            content = window.editor.getContent();
                            console.log('从富文本编辑器获取内容:', content);
                        } else if (window.editor.html) {
                            content = window.editor.html();
                            console.log('从富文本编辑器html()获取内容:', content);
                        }
                    }
                } catch (err) {
                    console.log('富文本编辑器获取内容失败:', err);
                }

                // 方法2：如果富文本编辑器没有内容，从textarea获取
                if (!content || content.trim() === '') {
                    content = $('textarea[name="content"]').val();
                    console.log('从textarea获取内容:', content);
                }

                // 方法3：如果还是没有内容，尝试从可能的编辑器容器获取
                if (!content || content.trim() === '') {
                    var editorContent = $('.richtext').val() || $('.richtext').text() || $('.richtext').html();
                    if (editorContent) {
                        content = editorContent;
                        console.log('从编辑器容器获取内容:', content);
                    }
                }

                // 清理HTML标签（如果是富文本内容）
                if (content && content.includes('<')) {
                    // 简单的HTML标签清理
                    content = content.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
                }

                content = content.trim();
                var needReply = $('#needReplyCheckbox').is(':checked') ? '1' : '0';
                var sendWechat = $('#sendWechatCheckbox').is(':checked') ? '1' : '0';
                var formAction = $('#form1').attr('action');

                console.log('表单数据:', {
                    content: content,
                    needreply: needReply,
                    send_wechat: sendWechat,
                    action: formAction,
                    content_length: content.length
                });

                // 输入验证
                if (!content) {
                    layer.msg('请输入回复内容', {icon: 2});
                    return false;
                }

                if (content.length > 1000) {
                    layer.msg('回复内容不能超过1000个字符', {icon: 2});
                    return false;
                }

                // 显示加载状态
                var $submitBtn = $('#sendReplyBtn');
                var originalText = $submitBtn.html();
                $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 发送中...');

                // 发送AJAX请求
                $.ajax({
                    url: formAction,
                    type: 'POST',
                    data: {
                        content: content,
                        needreply: needReply,
                        send_wechat: sendWechat,
                        submit: '发送回复'
                    },
                    timeout: 30000,
                    success: function(response) {
                        console.log('发送成功，响应:', response);

                        // 检查响应内容
                        var responseText = typeof response === 'string' ? response : JSON.stringify(response);

                        if (responseText.includes('成功') || responseText.includes('success')) {
                            layer.msg('回复发送成功！', {icon: 1, time: 2000});
                            // 清空输入框
                            $('textarea[name="content"]').val('');
                            $('#needReplyCheckbox').prop('checked', false);
                            $('#sendWechatCheckbox').prop('checked', true); // 微信通知默认选中
                            // 延迟刷新页面
                            setTimeout(function() {
                                window.location.reload();
                            }, 2000);
                        } else if (responseText.includes('错误') || responseText.includes('失败')) {
                            layer.msg('回复失败，请重试', {icon: 2});
                        } else {
                            // 默认认为成功
                            layer.msg('回复发送成功！', {icon: 1, time: 2000});
                            setTimeout(function() {
                                window.location.reload();
                            }, 2000);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('发送失败:', {
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });

                        var errorMsg = '网络错误，请重试';
                        if (status === 'timeout') {
                            errorMsg = '请求超时，请重试';
                        } else if (xhr.status === 404) {
                            errorMsg = '页面不存在，请联系管理员';
                        } else if (xhr.status === 500) {
                            errorMsg = '服务器错误，请重试';
                        }

                        layer.msg(errorMsg, {icon: 2});
                    },
                    complete: function() {
                        // 恢复按钮状态
                        $submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            };

            // 删除消息功能
            window.deleteMessage = function(messageId) {
                console.log('准备删除消息:', messageId);

                if (!messageId) {
                    layer.msg('消息ID无效', {icon: 2});
                    return;
                }

                // 确认删除
                layer.confirm('确定要删除这条消息吗？删除后无法恢复。', {
                    icon: 3,
                    title: '确认删除',
                    btn: ['确定删除', '取消']
                }, function(index) {
                    // 确定删除
                    layer.close(index);

                    // 显示加载状态
                    var loadingIndex = layer.load(1, {
                        shade: [0.3, '#000']
                    });

                    // 发送删除请求
                    $.ajax({
                        url: '{:U("Message/deleteMessage")}',
                        type: 'POST',
                        data: {
                            message_id: messageId
                        },
                        timeout: 15000,
                        success: function(response) {
                            console.log('删除响应:', response);

                            try {
                                // 尝试解析JSON响应
                                var result = typeof response === 'string' ? JSON.parse(response) : response;

                                if (result.status === 1 || result.success === true) {
                                    layer.msg('消息删除成功', {icon: 1, time: 1500});

                                    // 从页面中移除消息元素
                                    var $messageItem = $('[data-message-id="' + messageId + '"]');
                                    if ($messageItem.length > 0) {
                                        $messageItem.fadeOut(300, function() {
                                            $(this).remove();

                                            // 检查是否还有消息
                                            if ($('.message-reply-message-item').length === 0) {
                                                $('.message-reply-chat-list').append(
                                                    '<div class="no-messages" style="text-align: center; color: #9ca3af; padding: 2rem; font-size: 1.25rem;">' +
                                                    '<i class="fa fa-comments-o" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>' +
                                                    '暂无消息记录' +
                                                    '</div>'
                                                );
                                            }
                                        });
                                    }
                                } else {
                                    var errorMsg = result.message || result.msg || result.info || '删除失败，请重试';
                                    layer.msg(errorMsg, {icon: 2});
                                }
                            } catch (e) {
                                // 如果不是JSON格式，检查响应文本
                                var responseText = response.toString();
                                if (responseText.includes('成功') || responseText.includes('success')) {
                                    layer.msg('消息删除成功', {icon: 1, time: 1500});

                                    // 从页面中移除消息元素
                                    var $messageItem = $('[data-message-id="' + messageId + '"]');
                                    if ($messageItem.length > 0) {
                                        $messageItem.fadeOut(300, function() {
                                            $(this).remove();
                                        });
                                    }
                                } else {
                                    layer.msg('删除失败，请重试', {icon: 2});
                                }
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('删除请求失败:', {
                                status: status,
                                error: error,
                                responseText: xhr.responseText
                            });

                            var errorMsg = '网络错误，请重试';
                            if (status === 'timeout') {
                                errorMsg = '请求超时，请重试';
                            } else if (xhr.status === 404) {
                                errorMsg = '删除接口不存在，请联系管理员';
                            } else if (xhr.status === 500) {
                                errorMsg = '服务器错误，请重试';
                            } else if (xhr.status === 403) {
                                errorMsg = '没有权限删除此消息';
                            }

                            layer.msg(errorMsg, {icon: 2});
                        },
                        complete: function() {
                            layer.close(loadingIndex);
                        }
                    });
                }, function(index) {
                    // 取消删除
                    layer.close(index);
                });
            };

            // 自动滚动到聊天记录底部
            function scrollToBottom() {
                var chatList = document.querySelector('.message-reply-chat-list');
                if (chatList) {
                    chatList.scrollTop = chatList.scrollHeight;
                }
            }

            // 页面加载完成后滚动到底部
            setTimeout(scrollToBottom, 500);

            // 阻止表单默认提交行为
            $('#form1').on('submit', function(e) {
                e.preventDefault();
                // 调用发送回复函数
                sendReply();
                return false;
            });

            // 键盘快捷键支持
            $(document).on('keydown', function(e) {
                // Ctrl+Enter 快速发送
                if (e.ctrlKey && e.keyCode === 13) {
                    e.preventDefault();
                    sendReply();
                }

                // ESC 清空输入框
                if (e.keyCode === 27) {
                    $('textarea[name="content"]').val('').focus();
                }
            });

            // 实时字符计数
            var $textarea = $('textarea[name="content"]');
            var $counter = $('<div class="message-reply-char-counter" style="text-align: right; font-size: 1.25rem; color: #9ca3af; margin-top: 0.5rem;">0 / 1000</div>');
            $textarea.parent().append($counter);

            $textarea.on('input', function() {
                var length = $(this).val().length;
                $counter.text(length + ' / 1000');

                if (length > 1000) {
                    $counter.css('color', '#ef4444');
                } else if (length > 800) {
                    $counter.css('color', '#f59e0b');
                } else {
                    $counter.css('color', '#9ca3af');
                }
            });

            // 消息时间格式化
            function formatMessageTime() {
                $('.message-reply-message-time').each(function() {
                    var $this = $(this);
                    var text = $this.text();
                    if (text.includes('年') && text.includes('月') && text.includes('日')) {
                        // 日期分隔符，保持不变
                        return;
                    }

                    // 时间格式化
                    if (text.match(/^\d{1,2}:\d{2}$/)) {
                        var now = new Date();
                        var today = now.toDateString();
                        var yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000).toDateString();

                        // 这里可以添加更复杂的时间格式化逻辑
                    }
                });
            }

            formatMessageTime();

            // 添加消息发送成功的提示
            if (window.location.search.includes('success=1')) {
                layer.msg('回复发送成功！', {icon: 1});
                // 清除URL参数
                if (history.replaceState) {
                    history.replaceState(null, null, window.location.pathname);
                }
            }

            // 响应式处理
            function handleResize() {
                if (window.innerWidth <= 768) {
                    $('.message-reply-chat-container').addClass('mobile-layout');
                } else {
                    $('.message-reply-chat-container').removeClass('mobile-layout');
                }
            }

            $(window).on('resize', handleResize);
            handleResize();
        });
    });
</script>

<style>
    /* 移动端布局调整 */
    .mobile-layout {
        grid-template-columns: 1fr !important;
    }

    .mobile-layout .message-reply-chat-panel {
        height: 300px !important;
    }

    /* 字符计数器样式 */
    .message-reply-char-counter {
        transition: color 0.3s ease;
    }

    /* 加载状态样式 */
    .message-reply-submit-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    /* 滚动条美化 */
    .message-reply-panel-body::-webkit-scrollbar {
        width: 6px;
    }

    .message-reply-panel-body::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 3px;
    }

    .message-reply-panel-body::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
    }

    .message-reply-panel-body::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* 消息气泡动画 */
    .message-reply-message-bubble {
        transition: all 0.2s ease;
    }

    .message-reply-message-bubble:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    /* 表单焦点状态 */
    .message-reply-textarea:focus {
        transform: translateY(-2px);
    }

    /* 提醒指示器动画 */
    .message-reply-need-reply-indicator {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.8;
        }
    }
</style>