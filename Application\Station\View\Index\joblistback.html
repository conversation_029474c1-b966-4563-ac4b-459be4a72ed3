<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="/static/job/styles/mui.picker.all.css" />
    <link rel="stylesheet" href="/static/job/styles/swiper.min.css">
    <link rel="stylesheet" href="/static/job/styles/main.css">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="/static/job/js/jquery.min.js"></script>
</head>

<body>
<header>
    <div class="header-box">
        <a href="{:U('index/index')}" class="btn-back"></a>
        <h3>简历管理</h3>
    </div>
</header>
<div class="g-header-space">
    <div class="header-space"></div>
</div>
<div class="container">
    <div class="list-head">
        <div class="list-item li1">姓名</div>
        <div class="list-item li2">电话</div>
        <div class="list-item li3">地址</div>
        <div class="list-item li4">邮箱</div>
    </div>
    <include file="list-joblist"/>

</div>
<script src="/static/job/js/swiper.min.js"></script>
<script src="/static/job/js/mui.min.js"></script>
<script src="/static/job/js/mui.picker.min.js"></script>
<script src="/static/job/js/main.js"></script>
<script>
    $(function() {})
    $('ul.list').on('click', '.btn-top', function(e) {
        e.preventDefault();
        let $li = $(this).closest('li');
        let $prev = $li.prev();
        if ($prev.length) {
            $li.insertBefore($prev);
        }
    });
    $('ul.list').on('click', '.btn-down', function(e) {
        e.preventDefault();
        let $li = $(this).closest('li');
        let $next = $li.next();
        if ($next.length) {
            $li.insertAfter($next);
        }
    });
</script>
</body>

</html>