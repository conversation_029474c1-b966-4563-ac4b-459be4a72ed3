<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化招就办管理页面样式 */
                .serviceambassador-index-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .serviceambassador-index-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .serviceambassador-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .serviceambassador-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .serviceambassador-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .serviceambassador-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .serviceambassador-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .serviceambassador-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .serviceambassador-index-title-main {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .serviceambassador-index-title-sub {
                    font-size: 1.5rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .serviceambassador-index-actions {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .serviceambassador-index-search-toggle {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                    text-decoration: none;
                }

                .serviceambassador-index-search-toggle:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .serviceambassador-index-config-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.3);
                    text-decoration: none;
                }

                .serviceambassador-index-config-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(245, 158, 11, 0.4);
                    color: white;
                    text-decoration: none;
                }

                .serviceambassador-index-add-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
                    text-decoration: none;
                }

                .serviceambassador-index-add-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
                    color: white;
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .serviceambassador-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .serviceambassador-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .serviceambassador-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .serviceambassador-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .serviceambassador-index-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .serviceambassador-index-nav-link.active {
                    color: #667eea !important;
                    background: white !important;
                    border-bottom-color: #667eea !important;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1) !important;
                    font-weight: 700 !important;
                }

                .serviceambassador-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .serviceambassador-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 搜索面板 - 默认隐藏 */
                .serviceambassador-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    display: none;
                    animation: slideDown 0.3s ease-out;
                }

                .serviceambassador-index-search-panel.show {
                    display: block;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .serviceambassador-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .serviceambassador-index-search-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .serviceambassador-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .serviceambassador-index-search-close {
                    background: none;
                    border: none;
                    color: #718096;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                }

                .serviceambassador-index-search-close:hover {
                    background: #f1f5f9;
                    color: #374151;
                }

                .serviceambassador-index-search-body {
                    padding: 2rem;
                }

                /* 现代化卡片样式 */
                .serviceambassador-card {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                }

                .serviceambassador-card:hover {
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }

                .serviceambassador-card-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem 2rem;
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .serviceambassador-card-header::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                }

                .serviceambassador-card-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin: 0;
                }

                .serviceambassador-card-id {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .serviceambassador-card-name {
                    font-size: 1.75rem;
                    font-weight: 600;
                    margin: 0;
                }

                .serviceambassador-card-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .serviceambassador-status-badge {
                    padding: 0.5rem 1rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                }

                .serviceambassador-status-badge.status-pending {
                    background: rgba(251, 191, 36, 0.9);
                    border-color: rgba(251, 191, 36, 0.5);
                }

                .serviceambassador-status-badge.status-approved {
                    background: rgba(16, 185, 129, 0.9);
                    border-color: rgba(16, 185, 129, 0.5);
                }

                .serviceambassador-status-badge.status-rejected {
                    background: rgba(239, 68, 68, 0.9);
                    border-color: rgba(239, 68, 68, 0.5);
                }

                .serviceambassador-card-body {
                    padding: 2rem;
                }

                .serviceambassador-info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 2rem;
                    margin-bottom: 2rem;
                }

                .serviceambassador-info-section {
                    background: #f8fafc;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                    border: 1px solid #e2e8f0;
                }

                .serviceambassador-info-section-title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 1rem 0;
                }

                .serviceambassador-info-section-icon {
                    width: 2rem;
                    height: 2rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                }

                .serviceambassador-info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 0.75rem;
                    font-size: 1.5rem;
                }

                .serviceambassador-info-item:last-child {
                    margin-bottom: 0;
                }

                .serviceambassador-info-label {
                    color: #6b7280;
                    font-weight: 500;
                    min-width: 100px;
                    flex-shrink: 0;
                }

                .serviceambassador-info-value {
                    color: #374151;
                    font-weight: 600;
                    flex: 1;
                    word-break: break-all;
                }

                .serviceambassador-level-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .serviceambassador-station-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                /* 操作按钮区域 */
                .serviceambassador-actions {
                    display: flex;
                    gap: 0.75rem;
                    margin-top: 2rem;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                    flex-wrap: wrap;
                }

                .serviceambassador-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .serviceambassador-action-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                .serviceambassador-action-btn.btn-edit {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .serviceambassador-action-btn.btn-edit:hover {
                    color: white;
                }

                .serviceambassador-action-btn.btn-price {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                }

                .serviceambassador-action-btn.btn-price:hover {
                    color: white;
                }

                .serviceambassador-action-btn.btn-approve {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                }

                .serviceambassador-action-btn.btn-approve:hover {
                    color: white;
                }

                .serviceambassador-action-btn.btn-reject {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    color: white;
                }

                .serviceambassador-action-btn.btn-reject:hover {
                    color: white;
                }

                .serviceambassador-action-btn.btn-delete {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    color: white;
                }

                .serviceambassador-action-btn.btn-delete:hover {
                    color: white;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .serviceambassador-index-container {
                        padding: 1.5rem;
                    }

                    .serviceambassador-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .serviceambassador-info-grid {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .serviceambassador-index-container {
                        padding: 1rem;
                    }

                    .serviceambassador-index-nav-tabs {
                        flex-direction: column;
                    }

                    .serviceambassador-index-nav-item {
                        flex: none;
                    }

                    .serviceambassador-index-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .serviceambassador-index-nav-link.active {
                        border-bottom: none !important;
                        border-right-color: #667eea !important;
                    }

                    .serviceambassador-index-title-main {
                        font-size: 1.75rem;
                    }

                    .serviceambassador-index-title-sub {
                        font-size: 1.25rem;
                    }

                    .serviceambassador-actions {
                        flex-direction: column;
                    }

                    .serviceambassador-card-header {
                        flex-direction: column;
                        gap: 1rem;
                        text-align: center;
                    }
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .serviceambassador-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .serviceambassador-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .serviceambassador-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .serviceambassador-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
            </style>

            <div class="serviceambassador-index-wrapper">
                <div class="serviceambassador-index-container">
                    <!-- 现代化页面标题 -->
                    <div class="serviceambassador-index-header serviceambassador-index-fade-in">
                        <div class="serviceambassador-index-header-content">
                            <div class="serviceambassador-index-title">
                                <div class="serviceambassador-index-title-icon">
                                    <i class="fa fa-users"></i>
                                </div>
                                <div class="serviceambassador-index-title-text">
                                    <h1 class="serviceambassador-index-title-main">招就办管理</h1>
                                    <p class="serviceambassador-index-title-sub">Service Ambassador Management</p>
                                </div>
                            </div>
                            <div class="serviceambassador-index-actions">
                                    <a href="{:U('Serviceambassador/edit')}" class="serviceambassador-index-add-btn">
                                    <i class="fa fa-plus"></i>
                                    <span>添加招就办</span>
                                </a>
                                <a href="{:U('Sys/platform_rate_config')}" class="serviceambassador-index-config-btn">
                                    <i class="fa fa-cog"></i>
                                    <span>平台费率配置</span>
                                </a>
                                <button type="button" class="serviceambassador-index-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-search"></i>
                                    <span>搜索筛选</span>
                                </button>

                            </div>
                        </div>
                    </div>

                    <!-- 现代化导航标签 -->
                    <div class="serviceambassador-index-nav-container serviceambassador-index-fade-in-delay-1">
                        <ul class="serviceambassador-index-nav-tabs">
                            <li class="serviceambassador-index-nav-item">
                                <a href="{:U('Serviceambassador/index')}" class="serviceambassador-index-nav-link active">
                                    <i class="fa fa-list serviceambassador-index-nav-icon"></i>
                                    <span>招就办管理</span>
                                </a>
                            </li>
                            <li class="serviceambassador-index-nav-item">
                                <a href="javascript:void(0)" class="serviceambassador-index-nav-link quick-filter" data-filter="pending">
                                    <i class="fa fa-clock-o serviceambassador-index-nav-icon"></i>
                                    <span>待审核</span>
                                </a>
                            </li>
                            <li class="serviceambassador-index-nav-item">
                                <a href="javascript:void(0)" class="serviceambassador-index-nav-link quick-filter" data-filter="approved">
                                    <i class="fa fa-check serviceambassador-index-nav-icon"></i>
                                    <span>已通过</span>
                                </a>
                            </li>
                            <li class="serviceambassador-index-nav-item">
                                <a href="javascript:void(0)" class="serviceambassador-index-nav-link quick-filter" data-filter="rejected">
                                    <i class="fa fa-times serviceambassador-index-nav-icon"></i>
                                    <span>已拒绝</span>
                                </a>
                            </li>

                        </ul>
                    </div>

                    <!-- 搜索面板 - 默认隐藏 -->
                    <div class="serviceambassador-index-search-panel" id="searchPanel">
                        <div class="serviceambassador-index-search-header">
                            <div class="serviceambassador-index-search-title">
                                <div class="serviceambassador-index-search-icon">
                                    <i class="fa fa-search"></i>
                                </div>
                                <span>搜索筛选</span>
                            </div>
                            <button type="button" class="serviceambassador-index-search-close" onclick="toggleSearchPanel()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                        <div class="serviceambassador-index-search-body">
                            <form method="get" class="search-form" role="form">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">搜索条件：</label>
                                            <select class="form-control" name="kw">
                                                <php>foreach($c_kw as $key=>$value){</php>
                                                <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                                <php>}</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">搜索内容：</label>
                                            <input class="form-control" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容" />
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">审核状态：</label>
                                            <select name="status" class='form-control'>
                                                <option value="">全部状态</option>
                                                <php> foreach($statusList as $k => $v) {</php>
                                                <option value="{$k}" <php>if($_get['status'] != '' && $_get['status']==$k) echo 'selected';</php> >{$v.text}</option>
                                                <php> }</php>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">所属服务站：</label>
                                            <select name="station" class='form-control'>
                                                <option value="">全部服务站</option>
                                                <option value="0" <php>if($_get['station'] != '' && $_get['station']=='0') echo 'selected';</php> >总部直属</option>
                                                <php> foreach($stationList as $station) {</php>
                                                <option value="{$station.id}" <php>if($_get['station'] != '' && $_get['station']==$station['id']) echo 'selected';</php> >{$station.service_name}</option>
                                                <php> }</php>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label">排序方式：</label>
                                            <select name="sort" class='form-control'>
                                                <option value="">默认排序</option>
                                                <option value="id" <php>if($_get['sort'] != '' && $_get['sort']=='id') echo 'selected';</php> >ID排序</option>
                                                <option value="create_time" <php>if($_get['sort'] != '' && $_get['sort']=='create_time') echo 'selected';</php> >创建时间排序</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-9"></div> <!-- 占位列 -->
                                </div>
                                <div class="row" style="margin-top: 15px;">
                                    <div class="col-md-12 text-right">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-search"></i> 搜索
                                        </button>
                                        <a href="{:U('serviceambassador/index')}" class="btn btn-default">
                                            <i class="fa fa-refresh"></i> 重置
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- 招就办列表 -->
                    <div class="serviceambassador-index-fade-in-delay-2">
                        <php>foreach($list as $v) {</php>
                        <div class="serviceambassador-card">
                            <!-- 卡片头部 -->
                            <div class="serviceambassador-card-header">
                                <div class="serviceambassador-card-title">
                                    <span class="serviceambassador-card-id">#{$v.id}</span>
                                    <h3 class="serviceambassador-card-name">{$v.contract_name}</h3>
                                </div>
                                <div class="serviceambassador-card-status">
                                    <php>
                                        $statusInfo = isset($statusList[$v['status']]) ? $statusList[$v['status']] : ['text' => '未知状态', 'style' => 'default'];
                                        $statusClass = '';
                                        switch($v['status']) {
                                            case 0:
                                                $statusClass = 'status-pending';
                                                break;
                                            case 1:
                                                $statusClass = 'status-approved';
                                                break;
                                            case 2:
                                                $statusClass = 'status-rejected';
                                                break;
                                            default:
                                                $statusClass = 'status-pending';
                                        }
                                    </php>
                                    <span class="serviceambassador-status-badge {$statusClass}">
                                        <i class="fa fa-circle"></i>
                                        {$statusInfo.text}
                                    </span>
                                </div>
                            </div>

                            <!-- 卡片主体 -->
                            <div class="serviceambassador-card-body">
                                <div class="serviceambassador-info-grid">
                                    <!-- 基本信息 -->
                                    <div class="serviceambassador-info-section">
                                        <h4 class="serviceambassador-info-section-title">
                                            <div class="serviceambassador-info-section-icon">
                                                <i class="fa fa-user"></i>
                                            </div>
                                            基本信息
                                        </h4>
                                        <div class="serviceambassador-info-item">
                                            <span class="serviceambassador-info-label">招就办名称：</span>
                                            <span class="serviceambassador-info-value">{$v.service_name}</span>
                                        </div>
                                        <div class="serviceambassador-info-item">
                                            <span class="serviceambassador-info-label">等级：</span>
                                            <span class="serviceambassador-level-badge">
                                                <i class="fa fa-star"></i>
                                                {:$levelList[$v['level']]['text']}
                                            </span>
                                        </div>
                                        <div class="serviceambassador-info-item">
                                            <span class="serviceambassador-info-label">状态：</span>
                                            <php>if (!isset($v['is_disabled']) || $v['is_disabled'] == 0) {</php>
                                            <span style="color: #28a745; background: #e8f5e8; padding: 2px 8px; border-radius: 4px; font-size: 12px;">
                                                <i class="fa fa-check-circle"></i> 正常
                                            </span>
                                            <php>} else {</php>
                                            <span style="color: #dc3545; background: #f8d7da; padding: 2px 8px; border-radius: 4px; font-size: 12px;">
                                                <i class="fa fa-ban"></i> 已禁用
                                            </span>
                                            <php>}</php>
                                        </div>
                                        <div class="serviceambassador-info-item">
                                            <span class="serviceambassador-info-label">所属服务站：</span>
                                            <span class="serviceambassador-station-badge">
                                                <i class="fa fa-building"></i>
                                                {:$v['ref_station_name'] ? $v['ref_station_name'] : '总部'}
                                            </span>
                                        </div>
                                        <div class="serviceambassador-info-item">
                                            <span class="serviceambassador-info-label">添加时间：</span>
                                            <span class="serviceambassador-info-value">{:date('Y-m-d H:i:s', $v['create_time'])}</span>
                                        </div>
                                    </div>

                                    <!-- 联系信息 -->
                                    <div class="serviceambassador-info-section">
                                        <h4 class="serviceambassador-info-section-title">
                                            <div class="serviceambassador-info-section-icon">
                                                <i class="fa fa-phone"></i>
                                            </div>
                                            联系信息
                                        </h4>
                                        <div class="serviceambassador-info-item">
                                            <span class="serviceambassador-info-label">手机号码：</span>
                                            <span class="serviceambassador-info-value">{$v.mobile}</span>
                                        </div>
                                        <div class="serviceambassador-info-item">
                                            <span class="serviceambassador-info-label">身份证号：</span>
                                            <span class="serviceambassador-info-value">{$v.contract_card}</span>
                                        </div>
                                        <div class="serviceambassador-info-item">
                                            <span class="serviceambassador-info-label">电子邮件：</span>
                                            <span class="serviceambassador-info-value">{$v.email}</span>
                                        </div>
                                        <div class="serviceambassador-info-item">
                                            <span class="serviceambassador-info-label">通讯地址：</span>
                                            <span class="serviceambassador-info-value">{$v.mail_address}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="serviceambassador-actions">
                                    <a href="{:U('serviceambassador/edit', ['id' => $v['id']])}" class="serviceambassador-action-btn btn-edit">
                                        <i class="fa fa-edit"></i>
                                        <span>编辑</span>
                                    </a>

                                    <!-- 招就办禁用/启用按钮 -->
                                    <php>if (!isset($v['is_disabled']) || $v['is_disabled'] == 0) {</php>
                                    <a href="javascript:void(0)" onclick="setZsbDisabled({$v.id}, 1)"
                                       class="serviceambassador-action-btn btn-reject" title="禁用招就办">
                                        <i class="fa fa-ban"></i>
                                        <span>禁用</span>
                                    </a>
                                    <php>} else {</php>
                                    <a href="javascript:void(0)" onclick="setZsbDisabled({$v.id}, 0)"
                                       class="serviceambassador-action-btn btn-approve" title="启用招就办">
                                        <i class="fa fa-check-circle"></i>
                                        <span>启用</span>
                                    </a>
                                    <php>}</php>

                                    <php>if($v['status'] == 1) {</php>
                                    <a href="{:U('serviceambassador/price_config', ['zsb_id' => $v['id']])}" class="serviceambassador-action-btn btn-price">
                                        <i class="fa fa-cog"></i>
                                        <span>价格配置</span>
                                    </a>
                                    <php>}</php>

                                    <php>if($v['status'] == 0 || $v['status'] == 2) {</php>
                                    <a href="{:U('serviceambassador/cgstat', ['id' => $v['id'], 'status' => 1])}"
                                       class="serviceambassador-action-btn btn-approve"
                                       onclick="return confirm('确认审核通过？')">
                                        <i class="fa fa-check"></i>
                                        <span>审核通过</span>
                                    </a>
                                    <php>}</php>

                                    <php>if($v['status'] == 0) {</php>
                                    <a href="{:U('serviceambassador/cgstat', ['id' => $v['id'], 'status' => 2])}"
                                       class="serviceambassador-action-btn btn-reject"
                                       onclick="return confirm('确认审核失败？')">
                                        <i class="fa fa-times"></i>
                                        <span>审核失败</span>
                                    </a>
                                    <php>}</php>

                                    <a href="{:U('serviceambassador/delete', ['id' => $v['id']])}"
                                       class="serviceambassador-action-btn btn-delete"
                                       onclick="return confirm('确认删除？')">
                                        <i class="fa fa-trash"></i>
                                        <span>删除</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <php>}</php>

                        <!-- 分页 -->
                        <div class="serviceambassador-index-fade-in-delay-3" style="text-align: center; margin-top: 30px;">
                            {$page}
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
        } else {
            panel.classList.add('show');
        }
    }

    // 页面加载完成后的初始化
    $(document).ready(function() {
        // 搜索表单优化
        $('.search-form').on('submit', function() {
            // 移除空值参数
            $(this).find('input, select').each(function() {
                if ($(this).val() === '') {
                    $(this).prop('disabled', true);
                }
            });
        });

        // 卡片悬停效果增强
        $('.serviceambassador-card').hover(
            function() {
                $(this).find('.serviceambassador-card-header').css('background', 'linear-gradient(135deg, #5a67d8 0%, #667eea 100%)');
            },
            function() {
                $(this).find('.serviceambassador-card-header').css('background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
            }
        );

        // 状态徽章动画效果
        $('.serviceambassador-status-badge').each(function() {
            var $this = $(this);
            if ($this.hasClass('status-pending')) {
                setInterval(function() {
                    $this.find('.fa-circle').fadeOut(500).fadeIn(500);
                }, 2000);
            }
        });

        // 操作按钮确认增强
        $('.serviceambassador-action-btn[onclick*="confirm"]').on('click', function(e) {
            var $this = $(this);
            var originalText = $this.html();

            // 添加加载状态
            setTimeout(function() {
                if ($this.attr('href').indexOf('cgstat') > -1) {
                    $this.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
                } else if ($this.attr('href').indexOf('delete') > -1) {
                    $this.html('<i class="fa fa-spinner fa-spin"></i> 删除中...');
                }
            }, 100);
        });

        // 根据当前URL参数设置active状态
        function setActiveNavTab() {
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');

            // 移除所有active类
            $('.serviceambassador-index-nav-link').removeClass('active');

            // 根据status参数设置对应的active类
            if (!status || status === '') {
                // 全部招就办
                $('.serviceambassador-index-nav-link').first().addClass('active');
            } else {
                var filterMap = {
                    '0': 'pending',    // 待审核
                    '1': 'approved',   // 审核成功
                    '2': 'rejected'    // 审核失败
                };

                var filterType = filterMap[status];
                if (filterType) {
                    $('.quick-filter[data-filter="' + filterType + '"]').addClass('active');
                }
            }
        }

        // 页面加载时设置active状态
        setActiveNavTab();

        // 调试信息 - 显示当前URL参数
        var urlParams = new URLSearchParams(window.location.search);
        var currentStatus = urlParams.get('status');
        console.log('当前状态参数:', currentStatus);
        console.log('当前URL:', window.location.href);

        // 快速筛选功能
        $('.quick-filter').click(function(e) {
            e.preventDefault();
            var filter = $(this).data('filter');
            var currentUrl = window.location.href.split('?')[0];
            var newUrl = currentUrl + '?';

            // 移除当前active类并添加到点击的元素
            $('.serviceambassador-index-nav-link').removeClass('active');
            $(this).addClass('active');

            switch(filter) {
                case 'pending':
                    newUrl += 'status=0'; // 待审核状态
                    break;
                case 'approved':
                    newUrl += 'status=1'; // 审核成功状态
                    break;
                case 'rejected':
                    newUrl += 'status=2'; // 审核失败状态
                    break;
                default:
                    newUrl = currentUrl;
            }

            window.location.href = newUrl;
        });

        // "招就办管理"链接点击处理
        $('.serviceambassador-index-nav-link').first().click(function(e) {
            if ($(this).attr('href') !== 'javascript:void(0)') {
                $('.serviceambassador-index-nav-link').removeClass('active');
                $(this).addClass('active');
            }
        });

        // 其他导航链接处理
        $('.serviceambassador-index-nav-link').on('click', function(e) {
            if ($(this).attr('href') === '#' || $(this).attr('href') === 'javascript:void(0)') {
                e.preventDefault();
            }
        });

        // 添加键盘快捷键支持
        $(document).on('keydown', function(e) {
            // Ctrl+F 打开搜索面板
            if (e.ctrlKey && e.keyCode === 70) {
                e.preventDefault();
                toggleSearchPanel();
                setTimeout(function() {
                    $('#searchPanel input[name="val"]').focus();
                }, 300);
            }

            // ESC 关闭搜索面板
            if (e.keyCode === 27) {
                var panel = document.getElementById('searchPanel');
                if (panel.classList.contains('show')) {
                    panel.classList.remove('show');
                }
            }
        });

        // 响应式表格处理
        function handleResponsive() {
            var windowWidth = $(window).width();
            if (windowWidth < 768) {
                $('.serviceambassador-info-grid').addClass('mobile-layout');
            } else {
                $('.serviceambassador-info-grid').removeClass('mobile-layout');
            }
        }

        // 初始化响应式处理
        handleResponsive();
        $(window).resize(handleResponsive);

        // 添加滚动到顶部功能
        var $scrollToTop = $('<button class="scroll-to-top" style="position: fixed; bottom: 2rem; right: 2rem; width: 3rem; height: 3rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 50%; font-size: 1.25rem; cursor: pointer; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; display: none; transition: all 0.3s ease;"><i class="fa fa-arrow-up"></i></button>');
        $('body').append($scrollToTop);

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $scrollToTop.fadeIn();
            } else {
                $scrollToTop.fadeOut();
            }
        });

        $scrollToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 600);
        });

        // 添加数据统计显示 - 使用后端提供的真实统计数据
        var totalCount = {$stateCounts['total']|default=0};
        var pendingCount = {$stateCounts['pending']|default=0};
        var approvedCount = {$stateCounts['approved']|default=0};
        var rejectedCount = {$stateCounts['rejected']|default=0};

        // 在页面标题下方添加统计信息
        var statsHtml = '<div class="serviceambassador-stats" style="background: white; border-radius: 0.75rem; padding: 1rem 2rem; margin-bottom: 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-around; flex-wrap: wrap; gap: 1rem;">' +
            '<div class="stat-item" style="text-align: center; cursor: pointer;" onclick="filterByStatus(\'all\')">' +
                '<div style="font-size: 2rem; font-weight: bold; color: #667eea;">' + totalCount + '</div>' +
                '<div style="color: #6b7280; font-size: 1.25rem;">总数</div>' +
            '</div>' +
            '<div class="stat-item" style="text-align: center; cursor: pointer;" onclick="filterByStatus(\'pending\')">' +
                '<div style="font-size: 2rem; font-weight: bold; color: #f59e0b;">' + pendingCount + '</div>' +
                '<div style="color: #6b7280; font-size: 1.25rem;">待审核</div>' +
            '</div>' +
            '<div class="stat-item" style="text-align: center; cursor: pointer;" onclick="filterByStatus(\'approved\')">' +
                '<div style="font-size: 2rem; font-weight: bold; color: #10b981;">' + approvedCount + '</div>' +
                '<div style="color: #6b7280; font-size: 1.25rem;">已通过</div>' +
            '</div>' +
            '<div class="stat-item" style="text-align: center; cursor: pointer;" onclick="filterByStatus(\'rejected\')">' +
                '<div style="font-size: 2rem; font-weight: bold; color: #ef4444;">' + rejectedCount + '</div>' +
                '<div style="color: #6b7280; font-size: 1.25rem;">已拒绝</div>' +
            '</div>' +
        '</div>';

        $('.serviceambassador-index-nav-container').after(statsHtml);

        // 延迟执行active状态设置，确保DOM完全加载
        setTimeout(function() {
            setActiveNavTab();
        }, 100);

        // 添加统计项点击筛选功能
        window.filterByStatus = function(status) {
            var currentUrl = window.location.href.split('?')[0];
            var newUrl = currentUrl;

            switch(status) {
                case 'all':
                    newUrl = currentUrl; // 显示全部，不添加status参数
                    break;
                case 'pending':
                    newUrl += '?status=0'; // 待审核
                    break;
                case 'approved':
                    newUrl += '?status=1'; // 审核成功
                    break;
                case 'rejected':
                    newUrl += '?status=2'; // 审核失败
                    break;
            }

            window.location.href = newUrl;
        };
    });

    // 添加自定义样式增强active状态
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            /* 增强active状态的视觉效果 */
            .serviceambassador-index-nav-link.active {
                background: white !important;
                color: #667eea !important;
                border-bottom-color: #667eea !important;
                box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1) !important;
                font-weight: 700 !important;
            }

            .serviceambassador-index-nav-link.active .serviceambassador-index-nav-icon {
                color: #667eea !important;
            }

            /* 确保active状态在hover时保持 */
            .serviceambassador-index-nav-link.active:hover {
                background: white !important;
                color: #667eea !important;
            }

            /* 统计信息动画 */
            .serviceambassador-stats {
                animation: fadeInUp 0.6s ease-out;
            }

            .serviceambassador-stats .stat-item {
                transition: all 0.3s ease;
                border-radius: 0.5rem;
                padding: 0.5rem;
            }

            .serviceambassador-stats .stat-item:hover {
                background: rgba(102, 126, 234, 0.1) !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `)
        .appendTo('head');

        // 招就办禁用/启用功能
        window.setZsbDisabled = function(id, disabled) {
            var actionText = disabled ? '禁用' : '启用';
            var confirmText = '确定要' + actionText + '这个招就办吗？';

            if (confirm(confirmText)) {
                $.get('{:U("Serviceambassador/setZsbDisabled")}', {
                    id: id,
                    disabled: disabled
                }, function(data) {
                    if (data.status == 1) {
                        alert(data.info);
                        location.reload();
                    } else {
                        alert(data.info || '操作失败');
                    }
                }, 'json').fail(function() {
                    alert('网络错误，请稍后重试');
                });
            }
        };
</script>
<include file="block/footer" />

<script>
    require(["daterangepicker", "layer"], function ($) {
        $(function () {
            $('[data-toggle="tooltip"]').tooltip();
        });
    });
</script>
