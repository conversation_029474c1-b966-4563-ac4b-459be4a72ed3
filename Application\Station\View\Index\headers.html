<php>if($serviceStationRow['zsb_type']==2){</php>
<div class="bg01">
<div class="topbar">
  <div class="box">
    <div class="weap">
      <div class="logo"><a href="javascript:void(0);"><img src="{:$userRow['headimgurl']."2"}" alt=""></a></div>
      <div style="margin-top: 5px;">
        <php>if(mb_strlen($serviceStationRow['service_name'], 'utf-8') <= 8){</php>
          <h3 style="font-weight: bold;padding-left: 5px;">{:$serviceStationRow['service_name']}</h3>
        <php>}else{</php>
            <h1 style="font-weight: bold;padding-left: 5px;">{:$serviceStationRow['service_name']}</h1>
        <php>}</php>

        <script src="/static/js/layer/layer.m.js"></script>
        <div style="display: flex; align-items: center; gap: 10px; margin-top: 5px;">
          <div class="grade"><a href="javascript:void(0);">招就办主任</a></div>
          <php>if(in_array($userRow['id'], [2, 68, 75])){</php>
          <button onclick="showStationSwitcher()" class="station-switch-btn" style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">
            <i class="iconfont icon-qiehuan" style="margin-right: 3px;"></i>切换身份
          </button>
          <php>}</php>
        </div>
      </div>

    </div>
    <div style="display: flex; align-items: center; gap: 10px;">
      <a href="{:U('index/law', ['id' => '13'])}" class="gohome" style="color: red;margin-top: 10px;">特别声明<i class="iconfont icon-jinrujiantouxiao"></i></a>
    </div>
  </div>
</div>
<div class="nav">
  <div class="weap">
    <ul class="clearfix">
      <li>
        <a href="{:U('index/index')}">
          <div class="ico"><img src="/static/stations/images/ico-a0.png" alt=""></div>
          <div class="title">
           管理中心
          </div>
        </a>
      </li>
      <li>
        <a href="{:U('index/joblist')}">
          <div class="ico"><img src="/static/stations/images/ico-a03.png" alt="">{:$jobmsgcount ? '<i></i>' : ''}</div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="/index/joblist"){</php> style="font-weight:bold;"<php>}</php>>
            简历管理
          </div>
        </a>
      </li>
            <li>
        <a href="{:U('training/index')}">
          <div class="ico"><img src="/static/stations/images/bm.png" alt=""></div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="training/index"){</php> style="font-weight:bold;"<php>}</php>>
            培训管理
          </div>
        </a>
      </li>
    </ul>
  </div>

  <php>}else{</php>
<div class="bg01">
<div class="topbar">
  <div class="box">
    <div class="weap">
      <div class="logo"><a href="javascript:void(0);"><img src="{:$userRow['headimgurl']."2"}" alt=""></a></div>
      <div style="margin-top: 5px;">
        <php>if(mb_strlen($serviceStationRow['service_name'], 'utf-8') <= 8){</php>
          <h3 style="font-weight: bold;padding-left: 5px;">{:$serviceStationRow['service_name']}
           <php>if(in_array($userRow['id'], [2, 68, 75])){</php>
          <a class="cjz" href="javascript:void(0);" style="font-weight: normal;color:rgb(229, 122, 14);"  onclick="showStationSwitcher()"><i class="iconfont icon-zuoyoujiantou"></i></a>
          <php>}</php>
          </h3>
        <php>}else{</php>
            <h1 style="font-weight: bold;padding-left: 5px;">{:$serviceStationRow['service_name']}
           <php>if(in_array($userRow['id'], [2, 68, 75])){</php>
          <a class="cjz" href="javascript:void(0);" style="font-weight: normal;color:rgb(229, 122, 14);"  onclick="showStationSwitcher()"><i class="iconfont icon-zuoyoujiantou"></i></a>
          <php>}</php>

            </h1>
        <php>}</php>
        <script src="/static/js/layer/layer.m.js"></script>
        <div style="display: flex; align-items: center; gap: 10px; margin-top: 5px;">
          <div class="grade"><a href="javascript:void(0);">服务站</a>
             <php>if ($userRow['is_first_wechat'] == 1 && $serviceStationRow['zsb_type']==1){</php> 
            <a href="{:U('index/loginaccount')}" style="color:#666;border-color: #000;">登录管理</a>  
            <php>}</php>
        </div>
        </div>
      </div>
    </div>
    <div style="display: flex; align-items: center; gap: 10px;">
      <a href="{:U('index/law', ['id' => '13'])}" class="gohome" style="color: red;margin-top: 10px;">特别声明<i class="iconfont icon-jinrujiantouxiao"></i></a>
    </div>
  </div>
</div>
<div class="nav">
  <div class="weap">
    <ul class="clearfix">
      <li>
        <a href="{:U('index/index')}">
          <div class="ico"><img src="/static/stations/images/ico-a0.png" alt=""></div>
          <div class="title">
           管理中心
          </div>
        </a>
      </li>
      <li>
        <a href="{:U('index/servicestation')}">
          <div class="ico"><img src="/static/stations/images/ico-a04.png" alt=""></div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="/index/servicestation"){</php> style="font-weight:bold;"<php>}</php>>
            服务站
          </div>
        </a>
      </li>
      <li>
        <a href="{:U('index/zjb')}" id="zjbNavBtn">
          <div class="ico"><img src="/static/stations/images/tgy.png" alt=""><i id="zjbNavNotifyDot" style="display: none;"></i></div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="/index/zjb"){</php> style="font-weight:bold;"<php>}</php>>
            招就办
          </div>
        </a>
      </li>
      <li>
        <a href="{:U('index/joblist')}">
          <div class="ico"><img src="/static/stations/images/ico-a03.png" alt="">{:$jobmsgcount ? '<i></i>' : ''}</div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="/index/joblist"){</php> style="font-weight:bold;"<php>}</php>>
            简历管理
          </div>
        </a>
      </li>
            <li>
        <a href="{:U('training/index')}">
          <div class="ico"><img src="/static/stations/images/bm.png" alt=""></div>
          <div class="title" <php>if($_SERVER['REQUEST_URI']=="training/index"){</php> style="font-weight:bold;"<php>}</php>>
            培训管理
          </div>
        </a>
      </li>
    </ul>
  </div>
      <php>}</php>
</div>

<php>if ($adList && $serviceStationRow['zsb_type']==1) {</php>
<div class="notice mb10">
  <div class="weap">
    <div class="swiper noticeSwiper">
      <i class="iconfont icon-gonggao"></i>
      <div class="swiper-wrapper">
        <php>foreach($adList as $adRow) {</php>
        <div class="swiper-slide"><a class="a" href="{:U('index/ad', ['id' => $adRow['id']])}">{:$adRow['title']}<i class="iconfont icon-jinrujiantouxiao"></i></a></div>
        <php>}</php>
      </div>
    </div>
  </div>
</div>
<php>}</php>

<script>
  // 任务问好
function workwenhao() {

layer.closeAll();
layer.open({
    style : "width:80%",
    shadeClose : false,
    content: "<div class='t-container'><h1>推荐服务站数量对应服务站等级</h1><div class='table-container'><table><thead><tr><th>数量</th><th>级别</th><th>公益补贴</th><th>服务补贴</th></tr></thead><tbody><tr><td>1个</td><td>普通</td><td>-</td><td>-</td></tr><tr><td>2个</td><td class='highlight'>V1级别</td><td class='highlight'>10%</td><td class='highlight'>2%</td></tr><tr><td>5个</td><td class='highlight'>V2级别</td><td class='highlight'>20%</td><td class='highlight'>3%</td></tr><tr><td>10个</td><td class='highlight'>V3级别</td><td class='highlight'>30%</td><td class='highlight'>5%</td></tr></tbody></table></div><h2><strong>公益补贴：</strong>是指您推荐的服务站参与新能源或其他公益实习获得政策补贴返费后，平台额外给予您的补贴奖励，不影响被您推荐的服务站基础收益。<p style='color:#00bf80;padding-top: 8px;'>举个例子，您推荐的所有服务站有1000人参与公益实习并领取工资，平台在获得国家政策补贴之后，您推荐的服务站总获得1000*100=100,000元奖励，您作为推荐人，按照您的服务站级别对应获得平台额外给予补贴奖励为（V1级别：10,000元；V2级别：20,000元；V3级别：30,000元）。</p></h2><h2><strong>服务补贴：</strong>是指您推荐的服务站在平台有且不限于产生业绩时，平台额外给予您的服务奖励，不影响被您推荐的服务站基础收益。</h2><h2><strong>特别声明：</strong>所有服务站为平行合作关系，无上下层级；推荐人无推荐提成、无介绍费。平台仅接受“熟带熟”推荐方式才能加盟成为服务站。</h2></div>",
    btn: ['我知道了'],
    yes: function (index) {
    layer.closeAll();
    window.location.reload();
    },                  
    });                   
}

// 检查价格变化通知（导航栏）
function checkPriceChangeNotifyForNav() {
    <php>if ($serviceStationRow && $serviceStationRow['zsb_type'] == 1) {</php>
    var stationId = {$serviceStationRow.id};
    console.log('检查价格变化通知 - 服务站ID:', stationId);

    $.ajax({
        url: '/index/get_price_change_notify_status',
        type: 'GET',
        data: {},
        dataType: 'json',
        success: function(response) {
            console.log('通知状态查询成功:', response);

            if (response.status == 1 && response.has_notify) {
                // 显示导航栏红点提醒
                console.log('显示红点提醒');
                $('#zjbNavNotifyDot').show();
            } else {
                // 隐藏导航栏红点提醒
                console.log('隐藏红点提醒');
                $('#zjbNavNotifyDot').hide();
            }
        },
        error: function(xhr, status, error) {
            console.log('检查导航栏价格变化通知失败:', {
                status: status,
                error: error,
                response: xhr.responseText
            });
        }
    });
    <php>} else {</php>
    console.log('不满足检查通知条件 - 服务站类型:', <php>echo $serviceStationRow ? $serviceStationRow['zsb_type'] : 'null';</php>);
    <php>}</php>
}

// 动态获取API基础路径（适配开发和生产环境）
function getApiBasePath() {
    // 检查当前路径是否包含 /station，如果包含说明是开发环境
    if (window.location.pathname.indexOf('/station') === 0) {
        return '/station';
    }
    // 生产环境域名直接映射到station，不需要前缀
    return '';
}

// 页面加载时检查导航栏通知
$(document).ready(function() {
    checkPriceChangeNotifyForNav();
});

// 显示服务站切换弹窗
function showStationSwitcher() {
    // 显示加载动画 - 兼容移动端和桌面端layer
    var loadingIndex;
    if (typeof layer.load === 'function') {
        // 桌面端layer
        loadingIndex = layer.load(2, {shade: [0.3, '#000']});
    } else {
        // 移动端layer
        loadingIndex = layer.open({
            type: 2,
            content: '正在加载...',
            time: 0
        });
    }

    var apiBasePath = getApiBasePath();

    // 获取可切换的服务站列表
    $.ajax({
        url: apiBasePath + '/index/get_switchable_stations',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (typeof layer.close === 'function') {
                layer.close(loadingIndex);
            }

            if (response.status == 1) {
                var stationList = response.data;
                var currentStationId = response.current_station_id;

                // 构建服务站列表HTML
                var listHtml = '';
                for (var i = 0; i < stationList.length; i++) {
                    var station = stationList[i];
                    var isCurrentClass = station.is_current ? 'current-station' : '';
                    var checkIcon = station.is_current ? '<i class="iconfont icon-duigou" style="color: #00bf80; margin-left: auto;"></i>' : '';

                    listHtml += '<div class="station-item ' + isCurrentClass + '" data-station-id="' + station.id + '">' +
                        '<div class="station-avatar">' +
                            '<img src="' + station.avatar + '" alt="" onerror="this.src=\'/static/stations/images/default-avatar.svg\'">' +
                        '</div>' +
                        '<div class="station-info">' +
                            '<div class="station-name">' + station.name + '</div>' +
                            '<div class="station-type">' + station.type_text + '</div>' +
                        '</div>' +
                        '<div class="station-badge">' + station.type + '</div>' +
                        checkIcon +
                    '</div>';
                }

                var content = '<div class="station-switcher-container">' +
                    '<div class="switcher-header">' +
                        '<h3>切换身份</h3>' +
                        '<div class="close-btn" onclick="layer.closeAll();">×</div>' +
                    '</div>' +
                    '<div class="search-container">' +
                        '<input type="text" id="stationSearchInput" placeholder="搜索服务站或招就办..." class="search-input">' +
                        '<div class="search-icon">🔍</div>' +
                    '</div>' +
                    '<div class="station-list" id="stationList">' + listHtml + '</div>' +
                '</div>';

                // 显示弹窗 - 兼容移动端和桌面端layer
                var layerConfig = {
                    content: content,
                    shadeClose: true,
                    success: function(layero, index) {
                        // 绑定点击事件
                        $('.station-item:not(.current-station)').click(function() {
                            var stationId = $(this).data('station-id');
                            var stationName = $(this).find('.station-name').text();
                            switchToStation(stationId, stationName, index);
                        });

                        // 绑定搜索功能
                        $('#stationSearchInput').on('input', function() {
                            var searchTerm = $(this).val().toLowerCase().trim();
                            var $stationItems = $('.station-item');

                            if (searchTerm === '') {
                                // 显示所有项目
                                $stationItems.show();
                            } else {
                                // 过滤项目
                                $stationItems.each(function() {
                                    var $item = $(this);
                                    var stationName = $item.find('.station-name').text().toLowerCase();
                                    var stationType = $item.find('.station-badge').text().toLowerCase();

                                    if (stationName.indexOf(searchTerm) !== -1 || stationType.indexOf(searchTerm) !== -1) {
                                        $item.show();
                                    } else {
                                        $item.hide();
                                    }
                                });
                            }
                        });

                        // 搜索框获得焦点
                        setTimeout(function() {
                            $('#stationSearchInput').focus();
                        }, 100);
                    }
                };

                if (typeof layer.load === 'function') {
                    // 桌面端layer配置
                    layerConfig.type = 1;
                    layerConfig.title = false;
                    layerConfig.closeBtn = 0;
                    layerConfig.skin = 'station-switcher-layer';
                    layerConfig.area = ['90%', 'auto'];
                    layerConfig.maxHeight = '80%';
                } else {
                    // 移动端layer配置
                    layerConfig.type = 1;
                    layerConfig.title = '切换身份';
                    layerConfig.className = 'station-switcher-mobile';
                    layerConfig.style = 'max-height: 80vh; overflow-y: auto;';
                }

                layer.open(layerConfig);

            } else {
                if (typeof layer.msg === 'function') {
                    layer.msg(response.msg || '获取服务站列表失败', {icon: 2});
                } else {
                    layer.open({
                        content: response.msg || '获取服务站列表失败',
                        time: 2
                    });
                }
            }
        },
        error: function() {
            if (typeof layer.close === 'function') {
                layer.close(loadingIndex);
            }
            if (typeof layer.msg === 'function') {
                layer.msg('网络错误，请重试', {icon: 2});
            } else {
                layer.open({
                    content: '网络错误，请重试',
                    time: 2
                });
            }
        }
    });
}

// 切换到指定服务站
function switchToStation(stationId, stationName, layerIndex) {
    var confirmConfig = {
        content: '确定要切换到 "' + stationName + '" 吗？',
        btn: ['确定', '取消']
    };

    if (typeof layer.confirm === 'function') {
        // 桌面端layer
        confirmConfig.icon = 3;
        confirmConfig.title = '确认切换';
        layer.confirm(confirmConfig.content, confirmConfig, function(confirmIndex) {
            performSwitch(stationId, confirmIndex, layerIndex);
        });
    } else {
        // 移动端layer
        confirmConfig.title = '确认切换';
        confirmConfig.yes = function(confirmIndex) {
            performSwitch(stationId, confirmIndex, layerIndex);
        };
        layer.open(confirmConfig);
    }
}

// 执行切换操作
function performSwitch(stationId, confirmIndex, layerIndex) {
    // 显示切换中的加载动画
    var loadingIndex;
    if (typeof layer.load === 'function') {
        loadingIndex = layer.load(2, {shade: [0.3, '#000']});
    } else {
        loadingIndex = layer.open({
            type: 2,
            content: '正在切换...',
            time: 0
        });
    }
    var apiBasePath = getApiBasePath();

    $.ajax({
        url: apiBasePath + '/index/switch_station',
        type: 'POST',
        data: {
            station_id: stationId
        },
        dataType: 'json',
        success: function(response) {
            if (typeof layer.close === 'function') {
                layer.close(loadingIndex);
                layer.close(confirmIndex);
                layer.close(layerIndex);
            }

            if (response.status == 1) {
                if (typeof layer.msg === 'function') {
                    layer.msg('切换成功！正在刷新页面...', {icon: 1, time: 1500}, function() {
                        window.location.reload();
                    });
                } else {
                    layer.open({
                        content: '切换成功！正在刷新页面...',
                        time: 2,
                        end: function() {
                            window.location.reload();
                        }
                    });
                }
            } else {
                if (typeof layer.msg === 'function') {
                    layer.msg(response.msg || '切换失败', {icon: 2});
                } else {
                    layer.open({
                        content: response.msg || '切换失败',
                        time: 2
                    });
                }
            }
        },
        error: function() {
            if (typeof layer.close === 'function') {
                layer.close(loadingIndex);
                layer.close(confirmIndex);
            }
            if (typeof layer.msg === 'function') {
                layer.msg('网络错误，请重试', {icon: 2});
            } else {
                layer.open({
                    content: '网络错误，请重试',
                    time: 2
                });
            }
        }
    });
}
</script>

<!-- 服务站切换弹窗样式 -->
<style>
.station-switcher-container {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    max-height: 70vh;
}

.switcher-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.switcher-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.close-btn {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #e9ecef;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    color: #666;
    transition: all 0.2s;
}

.close-btn:hover {
    background: #dee2e6;
    color: #333;
}

.search-container {
    position: relative;
    padding: 10px 20px;
    border-bottom: 1px solid #f1f3f4;
    background: #fafafa;
}

.search-input {
    width: 100%;
    padding: 8px 35px 8px 12px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.search-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.search-input::placeholder {
    color: #999;
}

.search-icon {
    position: absolute;
    right: 32px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    pointer-events: none;
    font-size: 14px;
}

.station-list {
    max-height: 45vh;
    overflow-y: auto;
    padding: 10px 0;
}

.station-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid #f1f3f4;
}

.station-item:hover {
    background-color: #f8f9fa;
}

.station-item.current-station {
    background-color: #e8f5e8;
    cursor: default;
}

.station-item.current-station:hover {
    background-color: #e8f5e8;
}

.station-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    flex-shrink: 0;
}

.station-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.station-info {
    flex: 1;
    min-width: 0;
}

.station-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.station-type {
    font-size: 12px;
    color: #666;
}

.station-badge {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    margin-left: 8px;
    flex-shrink: 0;
}

.station-item.current-station .station-badge {
    background: #00bf80;
}

/* 移动端适配 */
@media (max-width: 480px) {
    .station-switcher-container {
        border-radius: 8px;
    }

    .switcher-header {
        padding: 12px 15px;
    }

    .search-container {
        padding: 8px 15px;
    }

    .search-input {
        padding: 6px 30px 6px 10px;
        font-size: 13px;
    }

    .search-icon {
        right: 25px;
        font-size: 13px;
    }

    .station-item {
        padding: 10px 15px;
    }

    .station-avatar {
        width: 36px;
        height: 36px;
        margin-right: 10px;
    }

    .station-name {
        font-size: 13px;
    }

    .station-type {
        font-size: 11px;
    }
}

/* Layer弹窗样式覆盖 */
.station-switcher-layer .layui-layer-content {
    padding: 0 !important;
    border-radius: 12px;
    overflow: hidden;
}

.station-switcher-layer .layui-layer-shade {
    background-color: rgba(0, 0, 0, 0.4);
}
</style>