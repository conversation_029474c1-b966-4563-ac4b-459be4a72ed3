<include file="block/hat" />
<div class="container-fluid">
  <div class="row">
    <include file="block/menu" />
    <div class="col-xs-12 col-sm-9 col-lg-10">
      <div class="main">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title">确认收款</h3>
          </div>
          <div class="panel-body">
            <a
              href="{:U('training/detail', ['id' => $order['id']])}"
              class="btn btn-primary"
              >返回详情</a
            >
          </div>
        </div>

        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">订单信息</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>订单ID：</label>
                  <span>{$order.id}</span>
                </div>
                <div class="form-group">
                  <label>报名费：</label>
                  <span>{:number_format($order['fee_amount'] / 100, 2)} 元</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">确认收款信息</h3>
          </div>
          <div class="panel-body">
            <form
              class="form-horizontal"
              action="{:U('training/confirmPayment', ['id' => $order['id']])}"
              method="post"
            >
              <div class="form-group">
                <label class="col-sm-2 control-label">支付渠道：</label>
                <div class="col-sm-6">
                  <select name="pay_channel" class="form-control" required>
                    <option value="">请选择支付渠道</option>
                    <foreach name="pay_channel" item="channel" key="key">
                      <option value="{$key}">{$channel}</option>
                    </foreach>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">报名费：</label>
                <div class="col-sm-6">
                  <p class="form-control-static">
                    {:number_format($order['fee_amount'] / 100, 2)} 元
                  </p>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">已支付：</label>
                <div class="col-sm-6">
                  <p class="form-control-static">
                    {:number_format($order['paid_amount_yuan'], 2)} 元
                  </p>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">本次收款金额：</label>
                <div class="col-sm-6">
                  <p class="form-control-static" style="color: #d9534f; font-weight: bold;">
                    {:number_format($order['remaining_amount_yuan'], 2)} 元
                  </p>
                  <p class="help-block">系统将按剩余应付金额进行收款</p>
                </div>
              </div>
              <div class="form-group">
                <div class="col-sm-offset-2 col-sm-6">
                  <button type="submit" class="btn btn-primary">
                    确认收款
                  </button>
                  <a
                    href="{:U('training/detail', ['id' => $order['id']])}"
                    class="btn btn-default"
                    >取消</a
                  >
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<include file="block/footer" />
