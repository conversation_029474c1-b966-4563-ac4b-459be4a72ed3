<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="/static/job/styles/mui.picker.all.css" />
    <link rel="stylesheet" href="/static/job/styles/swiper.min.css">
    <link rel="stylesheet" href="/static/job/styles/main.css">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="/static/job/js/jquery.min.js"></script>
</head>

<body>

    <header>
        <div class="header-box">
            <a href="{:U('job/index')}" class="btn-back"></a>
            <h3>{:$typeList[$type]}</h3>
        </div>
    </header>
    <div class="g-header-space">
        <div class="header-space"></div>
    </div>
    <form method="post">
    <section class="uc-wrap uc-experienceForm">
        <php>if ($type == 1) {</php>
        <!-- 教育 -->
        <ul class="form">
            <li>
                <div class="txt">时间</div>
                <div class="con">
                    <input type="text" class="uc-input" name="start_date" placeholder="请选择（选填）" value="{:$userJobRow['start_date'] ?:''}" readonly id="useDateA001" />
                    <div class="line">——</div>
                    <input type="text" class="uc-input" name="end_date" placeholder="请选择（选填）" value="{:$userJobRow['end_date'] ?:''}" readonly id="useDateA002" />
                    <a href="" class="btn-delete"><img src="/static/job/images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
            </li>
            <li>
                <div class="txt">学校名称</div>
                <input type="text" class="uc-input" name="school_name" placeholder="请输入（选填）" value="{:$userJobRow['school_name'] ?:''}" />
            </li>
            <li>
                <div class="txt">专业</div>
                <input type="text" class="uc-input" name="major" placeholder="请选择" value="{:$userJobRow['major'] ?:''}" />
            </li>

            <li>
                <div class="txt">证明人</div>
                <input type="text" class="uc-input" name="witness"  placeholder="请输入（选填）" value="{:$userJobRow['major'] ?:''}" />
            </li>
        </ul>
        <php>} else if ($type ==2) {</php>

        <!-- 家庭 -->
        <ul class="form">
            <li>
                <div class="txt">亲属关系</div>
                <input type="text" class="uc-input" name="relationship" placeholder="请输入"  value="{:$userJobRow['relationship'] ?:''}" />
            </li>
            <li>
                <div class="txt">姓名</div>
                <input type="text" class="uc-input" name="full_name" placeholder="请输入" value="{:$userJobRow['full_name'] ?:''}" />
            </li>
            <li>
                <div class="txt">工作单位</div>
                <input type="text" class="uc-input" name="work_unit" placeholder="请输入"  value="{:$userJobRow['work_unit'] ?:''}" />
            </li>
            <li>
                <div class="txt">职务</div>
                <input type="text" class="uc-input" name="position" placeholder="请输入" value="{:$userJobRow['position'] ?:''}" />
            </li>
            <li>
                <div class="txt">联系电话</div>
                <input type="text" class="uc-input" name="contact" placeholder="请输入" value="{:$userJobRow['contact'] ?:''}" />
            </li>

        </ul>
        <php>} else if ($type ==3) {</php>

        <!-- 工作经历 -->

        <ul class="form">
            <li>
                <div class="txt">时间</div>
                <div class="con">
                    <input type="text" class="uc-input" name="start_date" placeholder="请选择（选填）" value="{:$userJobRow['start_date'] ?:''}" readonly id="useDateA001" />
                    <div class="line">——</div>
                    <input type="text" class="uc-input" name="end_date" placeholder="请选择（选填）" value="{:$userJobRow['end_date'] ?:''}" readonly id="useDateA002" />
                    <a href="" class="btn-delete"><img src="/static/job/images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
            </li>
            <li>
                <div class="txt">公司名称</div>
                <input type="text" class="uc-input" placeholder="请输入" name="company_name" value="{:$userJobRow['company_name'] ?:''}" />
            </li>
            <li>
                <div class="txt">岗位</div>
                <input type="text" class="uc-input"  placeholder="请输入 最多15字符" name="position" value="{:$userJobRow['position'] ?:''}" />
            </li>
            <li class="noBorder">
                <div class="txt">离职内容</div>
                <div class="edit-box">
                     <textarea cols="5"  name="leave_reason" style="border:solid 1px #ccc">{:$userJobRow['leave_reason'] ?:''}</textarea>
                </div>
            </li>
        </ul>
        <php>} else if ($type ==4){</php>

        <!-- 技能 -->
        <ul class="form">
            <li>
                <div class="txt">技能类型</div>
                <div class="con">
                    <input type="text" class="uc-input" name="certificate_type" placeholder="请输入" value="{:$userJobRow['certificate_type'] ?:''}" readonly id="useData001" />
                    <a href="" class="btn-delete"><img src="/static/job/images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
            </li>
            <li>
                <div class="txt">证书名称</div>
                <input type="text" class="uc-input" placeholder="请输入 最多15字符" name="certificate_name" value="{:$userJobRow['certificate_name'] ?:''}" />
            </li>
            <li>
                <div class="txt">证书等级</div>
                <input type="text" class="uc-input" placeholder="请输入 最多15字符" name="level" value="{:$userJobRow['level'] ?:''}" />
            </li>
            <li>
                <div class="txt">取证日期</div>
                <div class="con">
                    <input type="text" class="uc-input" name="get_date" placeholder="请选择" value="{:$userJobRow['get_date'] ?:''}" readonly id="useDate001" />
                    <a href="" class="btn-delete"><img src="/static/job/images/delete.png" class="uc-icon36" alt="" /></a>
                </div>
            </li>
        </ul>
        <php>}</php>
        <div class="g-fixedOperate">
            <button type="submit" class="uc-btn blue">保存信息</button>
            <a href="" class="uc-btn gray">删除</a>
        </div>


    </section>
    </form>
    <script src="/static/job/js/swiper.min.js"></script>
    <script src="/static/job/js/mui.min.js"></script>
    <script src="/static/job/js/mui.picker.min.js"></script>
    <script src="/static/job/js/main.js"></script>
    <script>
        $(function() {})
        var start_time_picker = new mui.DtPicker({
            "type": "month",
            "beginYear": 1960,
            "endYear": 2025
        });
        $("body").on("click", "#useDate001", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDate001").val(items.text);
                });
            }, 200);
        });


        var userPicker001 = new mui.PopPicker();
        userPicker001.setData([{
            value: '外语',
            text: '外语'
        },
            {
                value: '计算机',
                text: '计算机'
            },
            {
                value: '职业资格',
                text: '职业资格'
            },
            {
                value: '奖励',
                text: '奖励'
            },
            {
                value: '处罚',
                text: '处罚'
            },
        ]);
        $("body").on("click", "#useData001", function() {
            setTimeout(function() {
                userPicker001.show(function(items) {
                    console.log(items[0]);
                    $("#useData001").val(items[0].text);
                });
            }, 200);
        });



        // 具体

        // 教育
        $("body").on("click", "#useDateA001", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDateA001").val(items.text);
                });
            }, 200);
        });
        $("body").on("click", "#useDateA002", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDateA002").val(items.text);
                });
            }, 200);
        });

        var userPickerA001 = new mui.PopPicker();
        userPickerA001.setData([{
                value: 'ywj',
                text: '学历1'
            },
            {
                value: 'aaa',
                text: '学历2'
            },
            {
                value: 'ljb',
                text: '学历3'
            },
            {
                value: 'ymt',
                text: '学历4'
            },
            {
                value: 'shq',
                text: '学历5'
            },
            {
                value: 'zbh',
                text: '学历6'
            },
            {
                value: 'zhy',
                text: '学历7'
            }
        ]);
        $("body").on("click", "#useDataA001", function() {
            setTimeout(function() {
                userPickerA001.show(function(items) {
                    console.log(items[0]);
                    $("#useDataA001").val(items[0].text);
                });
            }, 200);
        });

        // 在校
        $("body").on("click", "#useDateB001", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDateB001").val(items.text);
                });
            }, 200);
        });
        $("body").on("click", "#useDateB002", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDateB002").val(items.text);
                });
            }, 200);
        });

        // 实习
        $("body").on("click", "#useDateC001", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDateC001").val(items.text);
                });
            }, 200);
        });
        $("body").on("click", "#useDateC002", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDateC002").val(items.text);
                });
            }, 200);
        });
        // 工作

        $("body").on("click", "#useDateD001", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDateD001").val(items.text);
                });
            }, 200);
        });
        $("body").on("click", "#useDateD002", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDateD002").val(items.text);
                });
            }, 200);
        });
        // 项目

        $("body").on("click", "#useDateE001", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDateE001").val(items.text);
                });
            }, 200);
        });
        $("body").on("click", "#useDateE002", function() {
            setTimeout(function() {
                start_time_picker.show(function(items) {
                    $("#useDateE002").val(items.text);
                });
            }, 200);
        });



        $("body").on('click', '.btn-delete', function(event) {
            event.preventDefault();
            $(this).siblings('.uc-input').val('')
        });
    </script>
</body>

</html>