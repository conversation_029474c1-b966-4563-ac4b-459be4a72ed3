<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title>{:$row['name']}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="/static/postcontent/styles/swiper.min.css">
    <link rel="stylesheet" href="/static/postcontent/styles/main.css?t=<%=now()%>">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="/static/postcontent/js/jquery.min.js"></script>
</head>
<body>



    <header class="header-phone d-md-none">
        <a href="{:U('index/index')}" class="btn-back"></a>
        <h3>{:$row['name']}介绍</h3>
        <a href="" class="btn-menu"></a>
    </header>
    <div class="alert-menu">
        <div class="over-close"></div>
        <div class="box">
            <ul class="list" style="font-size: 16px;">
                <php>foreach($projectList as $projectRow) {</php>
                <li><a href="{:U('index/postcontent', ['id' => $projectRow['id']])}">{:$projectRow['name']}</a></li>
                <php>}</php>
            </ul>
        </div>
    </div>
    <div style="margin:20px 5px;border: 1px solid #000;">
        <div style="padding-left:6px;padding-right:10px;">
            {:htmlspecialchars_decode($row['content'])}
        </div>
    </div>
    <section class="uc-wrap uc-home">
        <div class="cpt">
            * 以上信息仅供参考，具体最新文件为准 *
        </div>
    </section>
    <script src="/static/postcontent/js/swiper.min.js"></script>
    <script src="/static/postcontent/js/main.js"></script>
    <script>
        $(function() {})

        new Swiper('.swiper-container1', {
            loop: true,
            observeParents: true,
            observer: true,
            pagination: {
                el: '.swiper-container1 .swiper-pagination',
                clickable: true,
            },
            autoplay: {
                delay: 2500,
                disableOnInteraction: false,
            },
        });

        new Swiper('.swiper-container2', {
            loop: true,
            observeParents: true,
            observer: true,
            direction: 'vertical',
            pagination: {
                el: '.swiper-container2 .swiper-pagination',
                clickable: true,
            },
            autoplay: {
                delay: 2500,
                disableOnInteraction: false,
            },
        });
    </script>
    <script>
        wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端console.log出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印
            appId: '{$appid}',
            timestamp: {:$wxconf['timestamp']?:0},
        nonceStr: '{$wxconf.noncestr}',
            signature: '{$wxconf.signature}',
            jsApiList: ['updateAppMessageShareData', 'shareAppMessage'],// 需要使用的JS接口列表填，需要使用的JS接口列表
        });
        wx.ready(function () {
            var title = '{:$row["name"]}';
            var img = 'http://station.zhongcaiguoke.com{:$row["img_url_one"]}';
            var desc =  '{:$row["desc"]}';
            var url = window.location.href;
            wx.updateAppMessageShareData({
                title: title,
                desc: desc,
                link: url,
                imgUrl: img,
                success: function () {
                    console.log('分享内容设置成功');
                },
                fail: function (err) {
                    console.error('分享设置失败:', err);
                    // alert("分享设置失败"+ JSON.stringify(err))
                }
            });
        });
        //配置成功之后的函数，按钮生成成功
        // wx.ready(function () {
        //     var title = '{:$row["title"]}';
        //     var img = 'http://station.zhongcaiguoke.com{:$row["img_url_one"]}';
        //     var desc =  '{:$row["desc"]}';
        //     var url = window.location.href;
        //     alert(window.location.href)
        //     wx.updateAppMessageShareData({
        //         title: title,
        //         desc: desc,
        //         link: url,
        //         imgUrl: img,
        //         success: function () {
        //             // 设置成功
        //             // alert('分享页面设置成功，请点击右上角三个点分享给好友！')
        //         }
        //     })
        // });
        wx.error(function (res) {
            // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名
        });
    </script>


</body>

</html>