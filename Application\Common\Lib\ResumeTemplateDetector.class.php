<?php
/**
 * 简历模板检测器
 * 实现双重水印检测机制（元数据水印 + 零宽度字符水印）
 * 采用OR逻辑：任一水印检测通过即认定为官方模板
 */

class ResumeTemplateDetector {
    
    // 水印标识符常量
    const METADATA_WATERMARK = 'ZCGK_TEMPLATE_WATERMARK';
    const ZERO_WIDTH_WATERMARK = 'ZCGK_TEMPLATE_2024';
    const TEMPLATE_VERSION = '1.0';
    
    // 检测结果常量
    const DETECTION_METHOD_METADATA = 'metadata';
    const DETECTION_METHOD_ZERO_WIDTH = 'zero_width';
    const DETECTION_METHOD_NONE = 'none';
    
    // 验证级别常量
    const VALIDATION_LEVEL_MILITARY = 'MILITARY';
    const VALIDATION_LEVEL_HIGH = 'HIGH';
    const VALIDATION_LEVEL_MEDIUM = 'MEDIUM';
    const VALIDATION_LEVEL_NONE = 'NONE';
    
    private $wordProcessor;
    private $filePath;
    private $structureDetectionDetails;
    
    /**
     * 构造函数
     * @param string $filePath Word文档路径
     */
    public function __construct($filePath) {
        $this->filePath = $filePath;
        $this->wordProcessor = new \WordProcessor($filePath);
    }
    
    /**
     * 检测简历是否使用官方模板
     * @return array 检测结果
     */
    public function detect() {
        $result = [
            'is_official_template' => false,
            'detection_method' => self::DETECTION_METHOD_NONE,
            'validation_level' => self::VALIDATION_LEVEL_NONE,
            'validation_score' => 0,
            'details' => []
        ];
        
        // 检查文件是否存在且为docx格式
        if (!$this->isValidDocxFile()) {
            $result['details'][] = '文件不是有效的docx格式';
            return $result;
        }
        
        // 打开文档
        if (!$this->wordProcessor->open()) {
            $result['details'][] = '无法打开Word文档';
            return $result;
        }
        
        try {
            // 优先检测元数据水印
            if ($this->detectMetadataWatermark()) {
                $result['is_official_template'] = true;
                $result['detection_method'] = self::DETECTION_METHOD_METADATA;
                $result['validation_level'] = self::VALIDATION_LEVEL_MILITARY;
                $result['validation_score'] = 100;
                $result['details'][] = '检测到元数据水印：' . self::METADATA_WATERMARK;

                // 检查模板版本
                $version = $this->getTemplateVersion();
                if ($version) {
                    $result['details'][] = '模板版本：' . $version;
                }

            } else if ($this->detectZeroWidthWatermark()) {
                // 如果元数据水印不存在，检测零宽度字符水印
                $result['is_official_template'] = true;
                $result['detection_method'] = self::DETECTION_METHOD_ZERO_WIDTH;
                $result['validation_level'] = self::VALIDATION_LEVEL_HIGH;
                $result['validation_score'] = 85;
                $result['details'][] = '检测到零宽度字符水印：' . self::ZERO_WIDTH_WATERMARK;

            } else if ($this->detectDocumentStructure()) {
                // 如果水印都不存在，检测文档结构特征（抗复制粘贴）
                $result['is_official_template'] = true;
                $result['detection_method'] = 'structure';
                $result['validation_level'] = self::VALIDATION_LEVEL_MEDIUM;
                $result['validation_score'] = 70;

                // 添加详细的字段匹配信息
                if (isset($this->structureDetectionDetails)) {
                    $details = $this->structureDetectionDetails;
                    $result['details'][] = sprintf(
                        '检测到官方模板文档结构特征：匹配 %d/%d 个字段 (%.2f%%, 阈值: %d%%)',
                        $details['found_fields'],
                        $details['total_fields'],
                        $details['match_rate'],
                        $details['threshold']
                    );

                    // 添加字段匹配详情到结果中
                    $result['structure_details'] = $details;
                } else {
                    $result['details'][] = '检测到官方模板文档结构特征';
                }

            } else {
                // 添加未匹配时的详细信息
                if (isset($this->structureDetectionDetails)) {
                    $details = $this->structureDetectionDetails;
                    $result['details'][] = sprintf(
                        '字段匹配不足：仅匹配 %d/%d 个字段 (%.2f%%, 需要: %d%%)',
                        $details['found_fields'],
                        $details['total_fields'],
                        $details['match_rate'],
                        $details['threshold']
                    );
                    $result['structure_details'] = $details;
                } else {
                    $result['details'][] = '未检测到任何官方模板标识';
                }
            }
            
        } catch (Exception $e) {
            $result['details'][] = '检测过程中发生错误：' . $e->getMessage();
        } finally {
            $this->wordProcessor->close();
        }
        
        return $result;
    }
    
    /**
     * 检查文件是否为有效的docx文件
     * @return bool
     */
    private function isValidDocxFile() {
        if (!file_exists($this->filePath)) {
            return false;
        }

        // 检查文件扩展名
        $extension = strtolower(pathinfo($this->filePath, PATHINFO_EXTENSION));
        if ($extension !== 'docx') {
            return false;
        }

        // 检查文件大小（避免空文件）
        if (filesize($this->filePath) < 1024) {
            return false;
        }

        // 检查文件是否为有效的ZIP文件（docx本质是ZIP）
        $zip = new ZipArchive();

        // 首先尝试严格检查
        $result = $zip->open($this->filePath, ZipArchive::CHECKCONS);
        if ($result === TRUE) {
            $zip->close();
            return true;
        }

        // 如果严格检查失败，尝试宽松检查（兼容性更好）
        $result = $zip->open($this->filePath);
        if ($result === TRUE) {
            // 验证是否包含docx必需的文件结构
            $hasDocumentXml = $zip->locateName('word/document.xml') !== false;
            $hasContentTypes = $zip->locateName('[Content_Types].xml') !== false;
            $zip->close();

            // 如果包含基本的docx文件结构，认为是有效的
            if ($hasDocumentXml && $hasContentTypes) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * 检测元数据水印
     * @return bool
     */
    private function detectMetadataWatermark() {
        $customProperties = $this->wordProcessor->getCustomProperties();
        
        // 检查是否存在官方模板标识
        if (isset($customProperties[self::METADATA_WATERMARK])) {
            return true;
        }
        
        // 检查其他可能的元数据标识
        foreach ($customProperties as $name => $value) {
            if (strpos($name, 'zcgk_template') !== false || 
                strpos($value, 'zcgk_official') !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检测零宽度字符水印
     * @return bool
     */
    private function detectZeroWidthWatermark() {
        return $this->wordProcessor->detectZeroWidthWatermark(self::ZERO_WIDTH_WATERMARK);
    }

    /**
     * 检测文档结构特征（抗复制粘贴）
     * @return bool
     */
    private function detectDocumentStructure() {
        $content = $this->wordProcessor->getDocumentContent();
        if ($content === false) {
            return false;
        }

        // 定义官方模板的特征字段（按顺序）
        $requiredFields = [
            '应聘人员报名表', '姓名', '性别', '出生年月', '民族', '籍贯', '政治面貌',
            '婚否', '健康状况', '身高', '体重', '视力', '听力', '学历', '专业技术职称',
            '是否恐高', '毕业院校', '所学专业', '毕业时间', '参加工作时间', '身份证号码',
            '户籍地', '求职意向', '现工作单位及职务', '电子邮箱', '联系电话', '教育经历',
            '工作经历', '家庭成员', '是否服从调剂', '外语水平', '计算机水平', '普通话水平',
            '专业特长', '获奖证书及技能证书', '受过的奖励', '受过的处罚',
            '主要工作业绩及自我评价', '本人签字', '个人签名'
        ];

        $foundFields = 0;
        $totalFields = count($requiredFields);
        $foundFieldsList = [];
        $foundFieldsWithText = [];
        $missingFieldsList = [];

        // 检查每个必需字段是否存在（兼容字段中的空格和换行）
        foreach ($requiredFields as $field) {
            // 将字段转换为正则表达式，允许字符之间有空白字符
            $pattern = $this->createFlexibleFieldPattern($field);
            if (preg_match($pattern, $content, $matches)) {
                $foundFields++;
                $foundFieldsList[] = $field;

                // 记录匹配到的实际文本
                $matchedText = isset($matches[0]) ? trim($matches[0]) : $field;
                $foundFieldsWithText[] = [
                    'expected' => $field,
                    'matched_text' => $matchedText,
                    'position' => strpos($content, $matches[0])
                ];
            } else {
                $missingFieldsList[] = $field;
            }
        }

        // 计算匹配率
        $matchRate = $foundFields / $totalFields;
        $matchPercentage = round($matchRate * 100, 2);

        // 存储详细检测信息供日志使用
        $this->structureDetectionDetails = [
            'total_fields' => $totalFields,
            'found_fields' => $foundFields,
            'missing_fields' => count($missingFieldsList),
            'match_rate' => $matchPercentage,
            'threshold' => 80,
            'found_fields_list' => $foundFieldsList,
            'found_fields_with_text' => $foundFieldsWithText,
            'missing_fields_list' => $missingFieldsList,
            'is_match' => $matchRate >= 0.8
        ];

        // 如果找到80%以上的字段，认为是官方模板结构
        return $matchRate >= 0.8;
    }

    /**
     * 创建灵活的字段匹配模式，允许字符之间有空白字符
     * @param string $field 字段名
     * @return string 正则表达式模式
     */
    private function createFlexibleFieldPattern($field) {
        // 将字段中的每个字符之间插入可选的空白字符匹配
        $chars = $this->mbStrSplit($field);
        $pattern = '';

        foreach ($chars as $i => $char) {
            // 转义正则表达式特殊字符
            $escapedChar = preg_quote($char, '/');
            $pattern .= $escapedChar;

            // 在字符之间添加可选的空白字符匹配（除了最后一个字符）
            if ($i < count($chars) - 1) {
                $pattern .= '\s*';
            }
        }

        return '/' . $pattern . '/u';
    }

    /**
     * 兼容的UTF-8字符串分割函数（兼容PHP < 7.4）
     * @param string $string 要分割的字符串
     * @return array 字符数组
     */
    private function mbStrSplit($string) {
        // 如果mb_str_split函数存在，直接使用
        if (function_exists('mb_str_split')) {
            return mb_str_split($string, 1, 'UTF-8');
        }

        // 兼容方案：使用preg_split分割UTF-8字符
        $chars = preg_split('//u', $string, -1, PREG_SPLIT_NO_EMPTY);
        return $chars ? $chars : array();
    }
    
    /**
     * 获取模板版本
     * @return string|null
     */
    private function getTemplateVersion() {
        $customProperties = $this->wordProcessor->getCustomProperties();
        
        if (isset($customProperties['template_version'])) {
            return $customProperties['template_version'];
        }
        
        return null;
    }
    
    /**
     * 记录检测结果到日志
     * @param array $result 检测结果
     * @param int $userId 用户ID
     * @param string $fileName 文件名
     */
    public function logDetectionResult($result, $userId, $fileName) {
        // 引入日志记录器
        import('Common.Lib.WatermarkLogger');

        $additionalData = [
            'file_size' => file_exists($this->filePath) ? filesize($this->filePath) : 0,
            'file_path' => $this->filePath,
            'details' => $result['details']
        ];

        // 如果是结构检测，添加详细的字段匹配信息
        if ($result['detection_method'] === 'structure' && isset($result['structure_details'])) {
            $structureDetails = $result['structure_details'];
            $additionalData['structure_analysis'] = [
                'total_fields' => $structureDetails['total_fields'],
                'found_fields' => $structureDetails['found_fields'],
                'missing_fields' => $structureDetails['missing_fields'],
                'match_rate' => $structureDetails['match_rate'],
                'threshold' => $structureDetails['threshold'],
                'found_fields_list' => $structureDetails['found_fields_list'],
                'found_fields_with_text' => $structureDetails['found_fields_with_text'],
                'missing_fields_list' => $structureDetails['missing_fields_list']
            ];
        }

        \WatermarkLogger::logDetectionResult($result, $userId, $fileName, $additionalData);
    }
    
    /**
     * 静态方法：快速检测文件
     * @param string $filePath 文件路径
     * @return bool
     */
    public static function quickDetect($filePath) {
        $detector = new self($filePath);
        $result = $detector->detect();
        return $result['is_official_template'];
    }
    
    /**
     * 静态方法：批量检测文件
     * @param array $filePaths 文件路径数组
     * @return array
     */
    public static function batchDetect($filePaths) {
        $results = [];
        
        foreach ($filePaths as $filePath) {
            $detector = new self($filePath);
            $results[$filePath] = $detector->detect();
        }
        
        return $results;
    }
}
?>
