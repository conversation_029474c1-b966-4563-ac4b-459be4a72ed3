﻿<php>if (empty($list)):</php>
<div style="text-align: center; padding: 40px; color: #888;">暂无提现记录</div>
<php>else: foreach($list as $v):</php>
<!-- Add class based on status for styling -->
<div class="record-card status-{$v.status}">
    <div class="info-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
        <span style="font-weight: bold; font-size: 18px; color: #333;">提现 ¥ {$v.amount}</span>
        <!-- Use status definition from WithdrawalRequestModel -->
        <span class="status-label status-{$v.status}" style="padding: 3px 8px; border-radius: 4px; background-color: #eee; font-size: 12px;">
            {:$statusList[$v['status']]['text']}
        </span>
    </div>
    <!-- 添加服务费和实际到账金额信息 -->
    <div class="fee-info" style="background-color: #f8f8f8; padding: 8px; margin-bottom: 8px; border-radius: 4px;">
        <div style="display: flex; justify-content: space-between; font-size: 13px; color: #666;">
            <span>技术服务费(8%)</span>
            <span>
                <if condition="isset($v['service_fee']) and floatval($v['service_fee']) === 0.0">
                    <span style="color: green;">免服务费</span>
                <else />
                    ¥ {:number_format(isset($v['service_fee']) ? floatval($v['service_fee']) : ($v['amount'] * 0.08), 2)}
                </if>
            </span>
        </div>
        <div style="display: flex; justify-content: space-between; font-size: 14px; font-weight: bold; color: #ff6b00; margin-top: 4px;">
            <span>实际到账金额</span>
            <span>¥ {:isset($v['actual_amount']) ? $v['actual_amount'] : number_format($v['amount'] * 0.92, 2)}</span>
        </div>
    </div>
    <div class="bank-info" style="font-size: 14px; color: #555; margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center;">
        <span> <!-- 包裹账户信息 -->
            <if condition="$v['platform_type'] eq 'linggong'">
                到账平台: {$v.linggong_bank_name} (**** {$v.linggong_last4})
            <elseif condition="$v['platform_type'] eq 'bank'" />
                到账卡: {$v.bank_name} ({$v.card_number_masked})
            <else />
                到账账户: N/A
            </if>
        </span>
        <span class="platform-tag" style="padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-left: 10px; background-color: {$v.platform_type == 'linggong' ? '#E0F2F7' : '#FFF9C4'}; color: {$v.platform_type == 'linggong' ? '#007B8E' : '#795548'}; border: 1px solid {$v.platform_type == 'linggong' ? '#B3E5FC' : '#FFF59D'}; white-space: nowrap;">
             <if condition="$v['platform_type'] eq 'linggong'">
                 <!-- 使用 linggongPlatformTypes 映射显示具体平台名称 -->
                {$linggongPlatformTypes[$v['linggong_platform_type']] ?? '灵工平台'}
            <elseif condition="$v['platform_type'] eq 'bank'" />
                银行卡
            <else />
                未知渠道
            </if>
        </span>
    </div>
    <div class="tax-handling" style="font-size: 14px; color: #555; margin-bottom: 8px;">
        <php>if(isset($v['tax_handling'])):</php>
        <span style="display: inline-block; padding: 2px 6px; border-radius: 3px; background-color: {$v.tax_handling == 1 ? '#e8f5e9' : '#fff3e0'}; color: {$v.tax_handling == 1 ? '#2e7d32' : '#e65100'}; font-size: 12px;">
            <php>if($v['tax_handling'] == 1):</php>
            平台负责完税
            <php>else:</php>
            需开具发票
            <php>endif;</php>
        </span>
        <php>endif;</php>
    </div>
    <div class="time-status" style="font-size: 12px; color: #888; display: flex; justify-content: space-between;">
        <span>申请时间: {:date("Y-m-d H:i:s", $v['request_time'])}</span> <!-- Use request_time -->
        <!-- Optionally display review/payment time -->
        <php> if ($v['status'] == 3 || $v['status'] == 5): // Show review time for rejected/failed </php>
             <span style="margin-left:10px;">处理时间: {:date("Y-m-d H:i", $v['review_time'] ?: $v['payment_time'])}</span>
        <php> elseif ($v['status'] == 4): // Show payment time for completed </php>
             <span style="margin-left:10px;">到账时间: {:date("Y-m-d H:i", $v['payment_time'])}</span>
        <php> endif; </php>
    </div>
    <!-- Show remark if status is rejected or payment failed -->
    <php> if (($v['status'] == 3 && !empty($v['review_remark'])) || ($v['status'] == 5 && !empty($v['payment_remark']))): </php>
    <div class="remark-text">
        备注: {:($v['status'] == 3 ? $v['review_remark'] : $v['payment_remark'])}
    </div>
    <php> endif; </php>
</div>
<php>endforeach; endif;</php>