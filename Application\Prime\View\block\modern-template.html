<!-- 现代化页面模板 -->
<include file="block/hat" />
<link rel="stylesheet" href="/static/css/modern-admin.css">

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                .modern-page-container {
                    padding: var(--spacing-xl);
                    background: var(--gray-50);
                    min-height: 100vh;
                }
                
                .modern-page-header {
                    margin-bottom: var(--spacing-2xl);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: var(--spacing-lg);
                }
                
                .page-title-section {
                    display: flex;
                    flex-direction: column;
                    gap: var(--spacing-sm);
                }
                
                .page-title {
                    font-size: 2rem;
                    font-weight: 700;
                    color: var(--gray-800);
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-sm);
                }
                
                .page-subtitle {
                    color: var(--gray-600);
                    font-size: 1rem;
                    margin: 0;
                }
                
                .page-actions {
                    display: flex;
                    gap: var(--spacing-md);
                    flex-wrap: wrap;
                }
                
                .breadcrumb-modern {
                    background: white;
                    border-radius: var(--radius-lg);
                    padding: var(--spacing-md) var(--spacing-lg);
                    box-shadow: var(--shadow-sm);
                    border: 1px solid var(--gray-200);
                    margin-bottom: var(--spacing-xl);
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-sm);
                }
                
                .breadcrumb-modern a {
                    color: var(--primary-color);
                    text-decoration: none;
                    font-weight: 500;
                    transition: var(--transition-normal);
                }
                
                .breadcrumb-modern a:hover {
                    color: var(--primary-dark);
                }
                
                .breadcrumb-separator {
                    color: var(--gray-400);
                }
                
                .content-section {
                    margin-bottom: var(--spacing-xl);
                }
                
                .section-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: var(--spacing-lg);
                }
                
                .section-title {
                    font-size: 1.25rem;
                    font-weight: 600;
                    color: var(--gray-800);
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-sm);
                }
                
                .section-actions {
                    display: flex;
                    gap: var(--spacing-sm);
                }
                
                /* 响应式设计 */
                @media (max-width: 768px) {
                    .modern-page-container {
                        padding: var(--spacing-lg);
                    }
                    
                    .modern-page-header {
                        flex-direction: column;
                        align-items: flex-start;
                    }
                    
                    .page-title {
                        font-size: 1.5rem;
                    }
                    
                    .page-actions {
                        width: 100%;
                        justify-content: stretch;
                    }
                    
                    .page-actions .modern-btn {
                        flex: 1;
                        justify-content: center;
                    }
                }
                
                /* 动画效果 */
                .modern-page-container > * {
                    animation: fadeInUp 0.5s ease-out;
                }
                
                .modern-page-container > *:nth-child(2) {
                    animation-delay: 0.1s;
                }
                
                .modern-page-container > *:nth-child(3) {
                    animation-delay: 0.2s;
                }
            </style>
            
            <div class="modern-page-container">
                <!-- 面包屑导航 -->
                <nav class="breadcrumb-modern">
                    <a href="{:U('index/index')}">
                        <i class="fa fa-home"></i>
                        首页
                    </a>
                    <span class="breadcrumb-separator">
                        <i class="fa fa-chevron-right"></i>
                    </span>
                    <span>{{CURRENT_PAGE_NAME}}</span>
                </nav>
                
                <!-- 页面头部 -->
                <div class="modern-page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">
                            <i class="fa fa-{{PAGE_ICON}}"></i>
                            {{PAGE_TITLE}}
                        </h1>
                        <p class="page-subtitle">{{PAGE_SUBTITLE}}</p>
                    </div>
                    <div class="page-actions">
                        {{PAGE_ACTIONS}}
                    </div>
                </div>
                
                <!-- 页面内容 -->
                {{PAGE_CONTENT}}
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />
