<?php

/**
 * 用户状态与之对应的操作项
 */
function userStatBtn($id, $status,$mobile)
{
    $modify = '';
    $where = ['id' => $id];
    if(empty($mobile)) {
        $where['stat'] = '0';
    } else {
        $where['stat'] = '1';
    }

    $up = '<a href="' . U('prime/user/cgstat', $where) . '" class="btn btnc btn-success" onclick="return confirm(\'确定？\')" >正常</a> ';
    $down = '<a href="' . U('prime/user/cgstat', ['id' => $id, 'stat' => '-1']) . '" class="btn btnc btn-warning" onclick="return confirm(\'确定？\')">禁用</a> ';
    switch ($status) {
        case -1://禁用
            $output = $modify.$up;
            break;
        case 0://未激活
        case 1://正常
            $output = $modify.$down;
            break;
    }
    return $output;
}


/**
 * 项目按钮
 * @param $id
 * @param $status
 * @return string
 */
function projectStatBtn($id, $status) {
    $modify = '<a href="' . U('project/edit', ['id' => $id]) . '" class="btn btnc btn-primary">编辑</a><br>';
    $up = '<span confirm ="true" url="' . U('project/cgstat', ['id' => $id, 'status' => '1']) . '" class="btn btnc btn-success js_ajax" >上架</span><br>';
    $down = '<span confirm ="true" url="' . U('project/cgstat', ['id' => $id, 'status' => '0']) . '" class="btn btnc btn-danger js_ajax" >下架</span><br> ';
    switch ($status) {
        case (0)://停止
            $output = $modify.$up;
            break;
        case (1) : //上架
            $output = $modify.$down;
            break;
    }
    return $output;
}

function UserjobStatBtn($id, $status, $is_html) {
    $modify = '<a href="' . U('userjob/edit', ['id' => $id]) . '" class="btn btnc btn-primary">编辑</a><br>';
    $upjobdoc = '<a href="' . U('userjob/upjob', ['id' => $id]) . '" class="btn btnc btn-primary" style="margin-top: 10px">更新简历文件</a><br>';
    $delmsg = '<span confirm ="true" url="' . U('userjob/deljob', ['id' => $id]) . '" class="btn btnc btn-danger js_ajax" style="margin-top: 10px">删除简历</span><br>';
    $showallinfo = '<a href="' . U('userjob/joballinfo', ['id' => $id]) . '" class="btn btnc btn-success" style="margin-top: 10px">查看更多信息</a><br>';

//    $delmsg = '<span confirm ="true" href="' . U('userjob/deljob', ['id' => $id]) . '" class="btn btnc btn-danger js_ajax" style="margin-top: 10px">删除简历</span><br>';
//    $showeducation = '<a href="' . U('userjob/education', ['job_id' => $id]) . '" class="btn btnc btn-primary" style="margin-top: 10px">查看教育经历</a><br>';
//    $showfamilymembers = '<a href="' . U('userjob/familymembers', ['job_id' => $id]) . '" class="btn btnc btn-primary" style="margin-top: 10px">查看家庭成员</a><br>';
//    $showworkexperience = '<a href="' . U('userjob/workexperience', ['job_id' => $id]) . '" class="btn btnc btn-primary" style="margin-top: 10px">查看工作经历</a><br>';
//    $showskillscertificates = '<a href="' . U('userjob/skillscertificates', ['job_id' => $id]) . '" class="btn btnc btn-primary" style="margin-top: 10px">查看技能证书</a><br>';

    $showskillscertificates = '<a href="' . U('userjob/upstatus', ['job_id' => $id]) . '" class="btn btnc btn-primary" style="margin-top: 10px">重新处理简历</a><br>';
    $showskillscertificates .= '<a href="' . U('userjob/uph5', ['job_id' => $id]) . '" class="btn btnc btn-primary" style="margin-top: 10px">重新h5</a><br>';
    $showskillscertificates .= '<a href="' . U('message/reply',['user_job_id' => $id]).'" class="btn btnc btn-primary" style="margin-top: 10px">沟通记录</a><br>';


    return $modify.$showeducation.$showfamilymembers.$showworkexperience.$showallinfo.$upjobdoc.$showskillscertificates.$delmsg;
}


/**
 * 项目按钮
 * @param $id
 * @param $status
 * @return string
 */
function quotationStatBtn($id) {
    $modify = '<a href="' . U('project/setquotation', ['project_post_id' => $id]) . '" class="btn btnc btn-primary">设置成本</a><br>';
    return $modify;
}

function projectidentityStatBtn($id, $status) {
    $modify = '<a href="' . U('projectidentity/edit', ['id' => $id]) . '" class="btn btnc btn-primary">编辑</a><br>';
    $up = '<span confirm ="true" url="' . U('projectidentity/cgstat', ['id' => $id, 'status' => '1']) . '" class="btn btnc btn-success js_ajax" >上架</span><br>';
    $down = '<span confirm ="true" url="' . U('projectidentity/cgstat', ['id' => $id, 'status' => '0']) . '" class="btn btnc btn-danger js_ajax" >下架</span><br> ';
    switch ($status) {
        case (0)://停止
            $output = $modify.$up;
            break;
        case (1) : //上架
            $output = $modify.$down;
            break;
    }
    return $output;
}


function projectpostStatBtn($id, $status, $isTop) {
    $modify = '<a href="' . U('projectpost/edit', ['id' => $id]) . '" class="btn btnc btn-primary">编辑</a><br>';
    $up = '<span confirm ="true" url="' . U('projectpost/cgstat', ['id' => $id, 'status' => '1']) . '" class="btn btnc btn-success js_ajax" >上架</span><br>';
    $down = '<span confirm ="true" url="' . U('projectpost/cgstat', ['id' => $id, 'status' => '0']) . '" class="btn btnc btn-danger js_ajax" >下架</span><br> ';
    switch ($status) {
        case (0)://停止
            $output = $modify.$up;
            break;
        case (1) : //上架
            $output = $modify.$down;
            break;
    }

    $uptop = '<span confirm ="true" url="' . U('projectpost/cgtop', ['id' => $id, 'is_top' => '1']) . '" class="btn btnc btn-success js_ajax" >置顶</span><br>';
    $downtop = '<span confirm ="true" url="' . U('projectpost/cgtop', ['id' => $id, 'is_top' => '0']) . '" class="btn btnc btn-danger js_ajax" >取消置顶</span><br> ';
    switch ($isTop) {
        case (0)://停止
            $output .= $uptop;
            break;
        case (1) : //上架
            $output .= $downtop;
            break;
    }
    return $output;
}

function momentStatBtn($id, $status) {
    $modify = '<a href="' . U('moment/edit', ['id' => $id]) . '" class="btn btnc btn-primary">编辑</a><br><br>';
    $up = '<span confirm ="true" url="' . U('moment/cgstat', ['id' => $id, 'status' => '1']) . '" class="btn btnc btn-success js_ajax" >上架</span><br><br>';
    $down = '<span confirm ="true" url="' . U('moment/cgstat', ['id' => $id, 'status' => '0']) . '" class="btn btnc btn-danger js_ajax" >下架</span><br><br>';
    $del = '<span confirm ="true" url="' . U('moment/del', ['id' => $id]) . '" class="btn btnc btn-danger js_ajax" >删除</span><br> ';
    
    switch ($status) {
        case (0)://停止
            $output = $modify.$up;
            break;
        case (1) : //上架
            $output = $modify.$down;
            break;
        default:
            $output = $modify;
            break;
    }
    
    return $output.$del;
}

/**
 * 问答分类
 * @param $id
 * @param $status
 * @return string
 */

function QuestioncategoryStatBtn($id, $status)
{
    $modify = '<a href="' . U('questioncategory/edit', ['id' => $id]) . '" class="btn btnc btn-primary">修改</a> ';
    $up = '<a href="' . U('questioncategory/cgstat', ['id' => $id, 'status' => 1]) . '" class="btn btnc btn-success ">上架</a>';
    $down = '<a href="' . U('questioncategory/cgstat', ['id' => $id, 'status' => 0]) . '" class="btn btnc btn-danger ">下架</a>';
    switch ($status) {
        case 0://状态
            $output = $modify . $up;
            break;
        case 1://状态
            $output = $modify . $down;
            break;
    }
    //返回
    return $output;
}



/**
 * 问答
 * @param $id
 * @param $status
 * @return string
 */
function QuestionStatBtn($id, $status)
{
    $modify = '<a href="' . U('question/edit', ['id' => $id]) . '" class="btn btnc btn-primary">修改</a> ';
    $up = '<a href="' . U('question/cgstat', ['id' => $id, 'status' => 1]) . '" class="btn btnc btn-success ">上架</a>';
    $down = '<a href="' . U('question/cgstat', ['id' => $id, 'status' => 2]) . '" class="btn btnc btn-danger ">下架</a>';
    switch ($status) {
        case 2://状态
            $output = $modify . $up;
            break;
        case 1://状态
            $output = $modify . $down;
            break;
    }
    //返回
    return $output;
}

function PagelistStatBtn($id, $status) {
    $modify = '<a href="' . U('pagelist/edit', ['id' => $id]) . '" class="btn btnc btn-primary">编辑</a><br>';
    $up = '<span confirm ="true" url="' . U('pagelist/cgstat', ['id' => $id, 'status' => '1']) . '" class="btn btnc btn-success js_ajax" >上架</span><br>';
    $down = '<span confirm ="true" url="' . U('pagelist/cgstat', ['id' => $id, 'status' => '0']) . '" class="btn btnc btn-danger js_ajax" >下架</span><br> ';
    switch ($status) {
        case (0)://停止
            $output = $modify.$up;
            break;
        case (1) : //上架
            $output = $modify.$down;
            break;
    }
    return $output;
}

function adStatBtn($id, $status) {
    $modify = '<a href="' . U('pagead/edit', ['id' => $id]) . '" class="btn btnc btn-primary">编辑</a><br>';
    $up = '<span confirm ="true" url="' . U('pagead/cgstat', ['id' => $id, 'status' => '1']) . '" class="btn btnc btn-success js_ajax" >上架</span><br>';
    $down = '<span confirm ="true" url="' . U('pagead/cgstat', ['id' => $id, 'status' => '0']) . '" class="btn btnc btn-danger js_ajax" >下架</span><br> ';
    switch ($status) {
        case (0)://停止
            $output = $modify.$up;
            break;
        case (1) : //上架
            $output = $modify.$down;
            break;
    }
    return $output;
}


/*
* 服务号
* */
function serviceStatBtn($id, $status)
{
    //<a href="' . U('service/edit', ['id' => $id]) . '" class="btn btnc btn-primary">编辑</a><br>
    $modify = '<br>';
    $up = '<span confirm ="true" url="' . U('service/cgstat', ['id' => $id, 'status' => '1']) . '" class="btn btnc btn-success js_ajax" >使用</span><br>';
    $down = '<span confirm ="true" url="' . U('service/cgstat', ['id' => $id, 'status' => '0']) . '" class="btn btnc btn-danger js_ajax" >停止</span><br> ';
    $sub = '<a href="' . U('service/subreply', ['id' => $id]) . '" class="btn btnc btn-primary">关注回复</a><br>';
    $menu = '<a href="' . U('service/menus', ['id' => $id]) . '" class="btn btnc btn-primary">菜单设置</a><br>';
    $taskmenu = '<a href="' . U('service/taskmenu', ['id' => $id]) . '" class="btn btnc btn-primary">推送菜单</a><br>';
    $delmenu =  '<a href="' . U('service/delmenu', ['id' => $id]) . '" class="btn btnc btn-primary">删除菜单</a><br>';

    switch ($status) {

        case (0)://停止
            $output = $modify.$up;
            break;
        case (1) : //上架
            $output = $modify.$down;
            break;
    }

    return $output.$sub.$menu.$taskmenu.$delmenu;
}

function servicestationStatBtn($id, $status) {
    $modify = '<a href="' . U('servicestation/edit', ['id' => $id]) . '" class="btn btnc btn-primary">编辑</a><br>';
    $tj = '<span confirm ="true" url="' . U('servicestation/tjstat', ['id' => $id, 'status' => '1']) . '" title="本方式开通不扣除推荐服务站资源包数量" class="btn btnc btn-success js_ajax" >推荐开通</span><br>';
    $up = '<span confirm ="true" url="' . U('servicestation/cgstat', ['id' => $id, 'status' => '1']) . '" title="使用推荐服务站资源包开通" class="btn btnc btn-success js_ajax" >资源包开通</span><br>';
    $error = '<span confirm ="true" url="' . U('servicestation/cgstat', ['id' => $id, 'status' => '2']) . '" class="btn btnc btn-danger js_ajax" >审核失败</span><br>';
    $down = '<span confirm ="true" url="' . U('servicestation/cgstat', ['id' => $id, 'status' => '0']) . '" class="btn btnc btn-info js_ajax" >待审核</span><br> ';
    switch ($status) {
        case (0)://停止
            $output = $modify.$tj.$up.$error;
            break;
        case (1) : //上架
            $output = $modify;
            break;
        case (2) :
            $output = $modify.$tj.$up.$down;
            break;
    }
    $othera = '<a href="' . U('servicestation/buy', ['service_station_id' => $id]) . '" class="btn btnc btn-primary">购买资源包</a><br>';
    $otherb = '<a href="' . U('servicestation/buylog', ['service_station_id' => $id]) . '" class="btn btnc btn-primary">资源包购买记录</a><br>';
    $other = '<a href="' . U('servicestation/other', ['service_station_id' => $id]) . '" class="btn btnc btn-primary">其他文件管理</a><br>';
    return $output.$othera.$otherb.$other;
}

function servicefileStatBtn($id,$service_station_id, $status) {
    $modify = '<a href="' . U('servicestation/otheredit', ['service_station_id' => $service_station_id, 'id' => $id]) . '" class="btn btnc btn-primary">编辑</a><br>';
    $up = '<span confirm ="true" url="' . U('servicestation/othercgstat', ['service_station_id' => $service_station_id, 'id' => $id, 'status' => '1']) . '" class="btn btnc btn-success js_ajax" >上架</span><br>';
    $down = '<span confirm ="true" url="' . U('servicestation/othercgstat', ['service_station_id' => $service_station_id, 'id' => $id, 'status' => '0']) . '" class="btn btnc btn-danger js_ajax" >下架</span><br> ';
    switch ($status) {
        case (0)://停止
            $output = $modify.$up;
            break;
        case (1) : //上架
            $output = $modify.$down;
            break;
    }
    return $output;
}

/**
 * 格式化时间戳
 */
function formatTime($time,$format='Y-m-d H:i:s',$text='')
{
    echo empty($time) ? $text : date($format,$time);
}
/**
 *
 * */
function ckRole($roleId) {

    $roleArray = explode(',' ,session('admin_role_id'));
    return in_array($roleId, $roleArray) ? true : false;
}



/*
* 多图上传
* */
function tpl_form_field_file($name, $value = array(), $options = array()) {
    $s = '';
    $op = [];
    if(empty($options['tabs'])){
        $options['tabs'] = array('browser'=>'', 'upload'=>'active');
        $op['tabs'] = array('browser'=>'', 'upload'=>'active');
    }

    $arrays = $op ? $op : $options;

    if (!defined('TPL_INIT_MULTI_File')) {
        $s = '
<script type="text/javascript">
	function uploadMultiFile(elm) {
		require(["util"], function(utils){
			utils.audio( "", function(url){
                var btn = $(elm);
                    var ipt = btn.parent().prev();
                    var val = ipt.val();
                    var img = ipt.parent().next().children();
			   if(url.url){
                  
                    ipt.val(url.filename);
                    ipt.attr("filename",url.filename);
                    ipt.attr("url",url.url);
                }
			}, "", '.json_encode($arrays).');
		});
	}
	function deleteMultiImage(elm){
		require(["jquery"], function($){
			$(elm).parent().remove();
		});
	}
</script>';
        define('TPL_INIT_MULTI_File', true);
    }

    $s .= '
<div class="input-group">
	<input type="text" class="form-control" name="'.$name.'" readonly="readonly" value="'.$value.'" placeholder="上传文件" autocomplete="off">
	<span class="input-group-btn">
		<button class="btn btn-default" type="button" onclick="uploadMultiFile(this);">选择文件</button>
	</span>
</div>';

    return $s;
}

/**
 *
 */
function customerStatBtn($id) {
    $modify = '<a href="'.U('customer/edit',['id'=>$id]).'" class="btn btnc btn-primary">修改</a> ';
    return $modify;
}