<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;
use Common\Model\ThreadLockModel;

/**
 * 线程锁管理控制器
 */
class ThreadLockController extends PrimeController
{
    /**
     * 线程锁管理页面
     */
    public function index()
    {
        $threadLockModel = D("Common/ThreadLock");
        $where = [];
        
        // 清理过期锁
        $cleaned = $threadLockModel->clean();
        
        // 获取锁列表
        $count = $threadLockModel->where($where)->count();
        $page = $this->page($count, 20);
        
        $locks = $threadLockModel->where($where)
                    ->order('id DESC')
                    ->limit($page->firstRow, $page->listRows)
                    ->select();
        
        foreach ($locks as &$lock) {
            $lock['is_expired'] = $lock['expire_time'] < time();
            $lock['lock_time_format'] = date('Y-m-d H:i:s', $lock['lock_time']);
            $lock['expire_time_format'] = date('Y-m-d H:i:s', $lock['expire_time']);
            $lock['created_at_format'] = date('Y-m-d H:i:s', $lock['created_at']);
        }
        
        $this->assign('locks', $locks);
        $this->assign('cleaned', $cleaned);
        $this->assign('page', $page->show());
        $this->display();
    }
    
    /**
     * 手动清理过期锁
     */
    public function clean()
    {
        $threadLockModel = D("Common/ThreadLock");
        $cleaned = $threadLockModel->clean();
        
        $this->success("成功清理 {$cleaned} 个过期锁", U('index'));
    }
    
    /**
     * 手动释放特定锁
     */
    public function release()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误');
        }
        
        $threadLockModel = D("Common/ThreadLock");
        $lock = $threadLockModel->find($id);
        if (!$lock) {
            $this->error('锁不存在');
        }
        
        $result = $threadLockModel->where(['id' => $id])->delete();
        if ($result === false) {
            $this->error('释放锁失败');
        }
        
        $this->success('成功释放锁: ' . $lock['lock_name'], U('index'));
    }
    
    /**
     * 命令行清理过期锁（可通过定时任务调用）
     */
    public function cleanExpired()
    {
        // 检查是否命令行调用
        if (!IS_CLI) {
            echo "This command can only be executed in CLI mode.";
            exit;
        }
        
        $threadLockModel = D("Common/ThreadLock");
        $cleaned = $threadLockModel->clean();
        
        echo "Cleaned {$cleaned} expired locks.\n";
        exit;
    }
} 