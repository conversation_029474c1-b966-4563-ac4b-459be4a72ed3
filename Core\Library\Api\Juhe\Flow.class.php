<?php
/**
 * 聚合流量api
 */
namespace Api\Juhe;

class Flow
{
    public $appkey = '';

    public $http;

    public function __construct($appkey = '')
    {
        if ($appkey) {
            $this->appkey = $appkey;
        } else {
            $this->appkey = C('JUHE.F_APPKEY');
        }
        $this->http = \Vendor\Curl::getInstance();
    }

    /**
     * 全部流量套餐列表
     */
    public function getList()
    {
        $ret = false;
        $apiurl = "http://v.juhe.cn/flow/list";

        $params = ['key' => $this->appkey];
        $result = $this->http->post($apiurl, $params);
        $res = json_decode($result, true);
        if ($res && $res['error_code'] == '0') {
            $ret = $res['result'];
        }
        if (! $ret) dolog('error/juhe/flowgetlist', $result);
        return $ret;
    }


    /**
     * 检测号码支持的流量套餐
        { "reason": "success",
          "result": [{ "city": "全国",
                    "company": "中国移动",
                    "companytype": "2",
                    "name": "中国移动全国流量套餐",
                    "type": "1",
                    "flows": [
                        { "id": "3", "p": "10M", "v": "10", "inprice": "2.90" },
                        { "id": "4", "p": "30M", "v": "30", "inprice": "4.84" },
                        { "id": "5", "p": "70M", "v": "70", "inprice": "9.50" },
                        { "id": "6", "p": "150M", "v": "150", "inprice": "19.00" },
                        { "id": "7", "p": "500M", "v": "500", "inprice": "29.04" },
                        { "id": "26", "p": "1G", "v": "1024", "inprice": "48.40" },
                        { "id": "27", "p": "2048M", "v": "2048", "inprice": "66.50" },
                        { "id": "33", "p": "700M", "v": "700", "inprice": "38.72" }
                    ] } ],
          "error_code": 0
        }
     */
    public function telCheck($phone)
    {
        $ret = false;
        if (empty($phone)) return $ret;
        $apiurl = "http://v.juhe.cn/flow/telcheck";
        $params = ['phone' => $phone, 'key' => $this->appkey];
        $result = $this->http->post($apiurl, $params);
        $res = json_decode($result, true);
        if ($res && $res['error_code'] == '0') {
            $ret = $res['result'];
        }
        if (! $ret) dolog('error/juhe/flowtelcheck', $result);
        return $ret;
    }
    
    /**
     * 提交流量充值
        {   "reason": "提交充值成功",
            "result": {
                "ordercash": "2.10",
                "cardname": "中国电信省内流量套餐10M",
                "sporder_id": "143212457209780362",
                "orderid": "a1122111ds1",
                "phone": "18913513535"
            },
            "error_code": 0
        }
     */
    public function recharge($phone, $pid, $orderid)
    {
        $ret = false;
        $apiurl = "http://v.juhe.cn/flow/recharge";
        $params = [
              "phone" => $phone,//需要充值流量的手机号码
              "pid" => $pid,//流量套餐ID
              "orderid" => $orderid,//自定义订单号，8-32字母数字组合
              "key" => $this->appkey,//应用APPKEY(应用详细页查询)
        ];
        $params['sign'] = md5(sprintf('%s%s%s%s%s', C('JUHE.OPENID'), $this->appkey, $phone, $pid, $orderid));
        $result = $this->http->post($apiurl, $params);
        $res = json_decode($result, true);
        if ($res && $res['error_code'] == '0') {
            $ret = $res['result'];
        }
        if (! $ret) dolog('error/juhe/flowrecharge', $result);
        return $ret;
    }

    /**
     * 订单状态查询
        {
            "reason": "查询成功",
            "result": {
                "uordercash": "1.860",
                "sporder_id": "146944199833560022",
                "game_state": "9" 0:充值中 1:成功 9:失败
            },
            "error_code": 0
        }
     */
    public function orderSta($orderid)
    {
        $ret = false;
        $apiurl = "http://v.juhe.cn/flow/ordersta";
        $params = ['orderid' => $orderid, 'key' => $this->appkey];
        $result = $this->http->post($apiurl, $params);
        $res = json_decode($result, true);
        if ($res && $res['error_code'] == '0') {
            $ret = $res['result'];
        }
        if (! $ret) dolog('error/juhe/flowordersta', $result);
        return $ret;
    }

    /**
     * 运营商状态查询
        {
            "reason": "查询成功",
            "result": [
                { "yunying": 1, "state": 1 },
                { "yunying": 2, "state": 1 },
                { "yunying": 3, "state": 1 }
            ],
            "error_code": 0
        }
     */
    public function operatorstate()
    {
        $ret = false;
        $apiurl = "http://v.juhe.cn/flow/operatorstate";
        $params = ['key' => $this->appkey];
        $result = $this->http->post($apiurl, $params);
        $res = json_decode($result, true);
        if ($res && $res['error_code'] == '0') {
            $ret = $res['result'];
        }
        if (! $ret) dolog('error/juhe/flowoperatorstate', $result);
        return $ret;
    }

    /**
     * 检查回调签名
     */
    public function chkCallback($sporder_id, $orderid, $sign)
    {
        $sign_ = md5($this->appkey.$sporder_id.$orderid);
        return $sign == $sign_;
    }
}