<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 项目管理
 * Class ProjectController
 * @package Prime\Controller
 */
class ProjectController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    public function index() {
        $c_kw = [
            'name' => '项目名称',
            'id' => 'ID',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);
        if ($s_status != '') $where['status'] = $s_status;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['name']))  {
                $where[$s_kw] = ['like', "%$s_val%"];
            }  else {
                $where[$s_kw] = $s_val;
            }
        }
        //公用搜索
        $contions = I('get.');
        $contionsWhere = ['group', 'is_out'];
        foreach ($contions as $whereKey => $row) {
            if (in_array($whereKey, $contionsWhere) && $row != '') {
                $where[$whereKey] = $row;
            }
        }

        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['create_time'] = ['between', [$s_start, $s_end]];
        }

        $obj = D("Project");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();

        $categoryList = [
            '1' => '央企',
            '2' => '国企',
            '3' => '事业单位',
            '4' => '上市公司',
            '5' => '民营企业',
            '6' => '出国',
            '7' => '其他',
        ];
        $this->assign('categoryList', $categoryList);
        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '0');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '0');
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('statusList', $obj->status);
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 添加编辑
     */
    public function edit() {
        $id = intval(I('get.id'));
        $obj = D("Project");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        }

        $this->assign('categoryList', $obj->category);
        if (IS_POST) {
            if ($data = $obj->create()) {
                if ($data['category'] == 0) {
                    $this->error('请选择正确的项目分类');
                }
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("project/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->display();
    }

    /**
     * 状态变更
     */
    public function cgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("Project");
        if (!array_key_exists($status, $obj->status)) $this->error('参数错误 ');
        $obj->save(['id' => $id, 'status' => $status]);
        if ($status == 0) {
            D("ProjectPost")->where(['project_id' => $id, 'status' => 1])->save(['status' => 0]);
        }
        $this->success('修改成功');
    }

    public function quotation() {

        $c_kw = [
            'name' => '项目名称',
            'id' => 'ID',
        ];
        $where = [];
        $obj = D("ProjectPost");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        if ($list) {
            $projectArrId = array_unique(array_column($list, 'project_id'));
            $projectList = [];
            if ($projectArrId) {
                $projectList = D("Project")->where(['id' => ['in', $projectArrId]])->getField('id,name', true);
            }
            $this->assign('projectList', $projectList);
            $projectPostArrId = array_unique(array_column($list, 'id'));
            $projectJoinIdentity = [];
            if ($projectPostArrId) {
                $projectJoinIdentityList = D("ProjectJoinIdentity")->where(['project_post_id' => ['in', $projectPostArrId]])->select();
                $this->assign('projectJoinIdentity', $projectJoinIdentity);
                foreach ($projectJoinIdentityList as $projectJoinIdentityRow) {
                    $projectJoinIdentity[$projectJoinIdentityRow['project_post_id']][$projectJoinIdentityRow['project_identity_id']] = $projectJoinIdentityRow['cost'];
                }
            }
            $this->assign('projectJoinIdentity', $projectJoinIdentity);
        }
        $projectIdentityList = D("ProjectIdentity")->where(['status' => 1])->Field('id,name')->select();
        $this->assign('projectIdentityList', $projectIdentityList);
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 设置报价
     */
    public function setquotation() {
        $projectPostId = I('get.project_post_id', 0);
        if (!$projectPostId) $this->error('参数错误数!!!');
        $obj = D("ProjectPost");
        $projectPostRow = $obj->where(['id' => $projectPostId])->find();
        $this->assign('row', $projectPostRow);

        $projectList = [];
        if ($projectPostRow) {
            $projectList = D("Project")->where(['id' => $projectPostRow['project_id']])->getField('id,name', true);
        }
        $this->assign('projectList', $projectList);
        $projectIdentityList = D("ProjectIdentity")->where(['status' => 1])->Field('id,name')->select();
        $this->assign('projectIdentityList', $projectIdentityList);

        if (IS_POST) {
            \Think\Log::write('开始处理项目报价设置: project_post_id=' . $projectPostId . ', project_id=' . $projectPostRow['project_id'], 'INFO');

            $data = I('post.');
            $costChanged = false;
            $costChanges = []; // 记录成本变化的详细信息

            // 获取原有的成本配置
            $oldCosts = [];
            $oldJoinIdentities = D("ProjectJoinIdentity")->where(['project_post_id' => $projectPostId])->select();
            foreach ($oldJoinIdentities as $item) {
                $oldCosts[$item['project_identity_id']] = $item['cost'];
            }

            \Think\Log::write('获取原有成本配置: project_post_id=' . $projectPostId . ', 配置数量=' . count($oldCosts), 'DEBUG');

            foreach ($data['projectidentity'] as $projectidentityId => $val) {
                $projectJoinIdentityRow = [];
                $projectJoinIdentityRow = D("ProjectJoinIdentity")->where([
                    'project_post_id' => $projectPostId,
                    'project_id' => $projectPostRow['project_id'],
                    'project_identity_id' => $projectidentityId,
                ])->find();

                // 检查成本是否变动（只有在已有配置的基础上发生变化才触发）
                $oldCost = isset($oldCosts[$projectidentityId]) ? $oldCosts[$projectidentityId] : null;
                if ($oldCost !== null && $oldCost != $val) {
                    $costChanged = true;
                    // 记录具体的成本变化信息
                    $costChanges[] = [
                        'identity_id' => $projectidentityId,
                        'old' => $oldCost,
                        'new' => $val
                    ];
                    \Think\Log::write('检测到成本变动: project_post_id=' . $projectPostId . ', identity_id=' . $projectidentityId . ', old_cost=' . $oldCost . ', new_cost=' . $val, 'INFO');
                } elseif ($oldCost === null) {
                    \Think\Log::write('首次配置成本，不触发价格变动: project_post_id=' . $projectPostId . ', identity_id=' . $projectidentityId . ', new_cost=' . $val, 'DEBUG');
                }

                if (!$projectJoinIdentityRow) {
                    $insertId = D("ProjectJoinIdentity")->add([
                        'project_id' => $projectPostRow['project_id'],
                        'project_post_id' => $projectPostId,
                        'project_identity_id' => $projectidentityId,
                        'cost' => $val,
                        'create_time' => time(),
                    ]);
                } else {
                    D("ProjectJoinIdentity")->save([
                        'id' => $projectJoinIdentityRow['id'],
                        'cost' => $val,
                    ]);
                }
            }

            // 如果成本发生变动，触发价格变动处理
            if ($costChanged) {
                \Think\Log::write('触发服务站成本变动处理: project_post_id=' . $projectPostId, 'INFO');

                // 构建包含新旧值的变动信息
                $changeInfo = [
                    'type' => 'identity_cost',
                    'identity_cost' => [
                        'old' => $costChanges[0]['old'], // 取第一个变动的旧值
                        'new' => $costChanges[0]['new'], // 取第一个变动的新值
                        'changes' => $costChanges // 完整的变动列表
                    ]
                ];

                $priceChangeService = new \Common\Service\PostPriceChangeService();

                $result = $priceChangeService->handlePriceChange($projectPostId, $changeInfo);
                \Think\Log::write('服务站成本变动处理结果: project_post_id=' . $projectPostId . ', result=' . ($result ? 'success' : 'failed'), 'INFO');
            } else {
                \Think\Log::write('服务站成本无变动: project_post_id=' . $projectPostId, 'DEBUG');
            }

            \Think\Log::write('项目报价设置完成: project_post_id=' . $projectPostId, 'INFO');
            return $this->success('更新报价成功', U("project/quotation"));
        }
        $projectJoinIdentity = [];
        $projectJoinIdentityList = D("ProjectJoinIdentity")->where(['project_post_id' => $projectPostId])->select();
        foreach ($projectJoinIdentityList as $projectJoinIdentityRow) {
            $projectJoinIdentity[$projectJoinIdentityRow['project_post_id']][$projectJoinIdentityRow['project_identity_id']] = $projectJoinIdentityRow['cost'];
        }
        $this->assign('projectJoinIdentity', $projectJoinIdentity);
        $this->display();
    }
}