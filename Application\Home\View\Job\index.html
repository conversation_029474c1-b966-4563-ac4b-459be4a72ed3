<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="stylesheet" href="/static/job/styles/mui.picker.all.css" />
    <link rel="stylesheet" href="/static/job/styles/swiper.min.css">
    <link rel="stylesheet" href="/static/job/styles/main.css">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="imagemode" content="force">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="author" content="">
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="apple-touch-icon-precomposed" href="">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <script src="/static/job/js/jquery.min.js"></script>
</head>

<body>

    <header>
        <div class="header-box">
            <h3>简历信息</h3>
        </div>
    </header>
    <div class="g-header-space">
        <div class="header-space"></div>
    </div>

    <section class="uc-wrap uc-home">
        <div class="panel">
            <h3>欢迎使用，</h3>
            <div class="con">HI，根据自身需求填写简历，简历模板只显示已填写内容，不填写不显示。填写完成后选择模板生成简历。</div>
        </div>

        <ul class="form">
<!--            <li>-->
<!--                <div class="upload-box">-->
<!--                    &lt;!&ndash; <img src="images/face.png" alt=""/> &ndash;&gt;-->
<!--                    <div class="over">-->
<!--                        <i class="uc-font uc-add"></i>-->
<!--                        添加<br>证件照-->
<!--                    </div>-->
<!--                    <input type="file" name="" id="">-->
<!--                </div>-->
<!--                <a href="" class="con">-->
<!--                    <h3>您的姓名</h3>-->
<!--                    <p>性别1年龄1生日</p>-->
<!--                </a>-->
<!--            </li>-->
           <!-- <li>
                <a href="" class="con">
                    <h3>求职意向</h3>
                    <p>列出期望职位、工作地点、薪资等</p>
                </a>
            </li>
            <li>
                <a href="" class="con">
                    <h3>教育经历</h3>
                    <p>2025.02-2025.02</p>
                    <p class="uc-b">aaaa</p>
                    <p class="uc-b">bbbb</p>
                    <p>cccc</p>
                </a>
                <div class="line"></div>
                <a href="" class="con">
                    <h3>教育经历</h3>
                    <p>2025.02-2025.02</p>
                    <p class="uc-b">aaaa</p>
                    <p class="uc-b">bbbb</p>
                    <p>cccc</p>
                </a>
            </li>-->
        </ul>

        <div class="title">—— 添加更多模块 ——</div>
        <div class="list">
            <php>if ($userJobRow) {</php>
            <a href="{:U('/job/setjob')}" class="item"><i class="uc-font uc-add"></i>修改简历</a>
            <a href="{:U('/job/addmodulesbox', ['type' => 1])}" class="item"><i class="uc-font uc-add"></i>教育经历</a>
            <a href="{:U('/job/addmodulesbox', ['type' => 2])}" class="item"><i class="uc-font uc-add"></i>家庭成员</a>
            <a href="{:U('/job/addmodulesbox', ['type' => 3])}" class="item"><i class="uc-font uc-add"></i>工作经历</a>
            <a href="{:U('/job/addmodulesbox',['type' => 4])}" class="item"><i class="uc-font uc-add"></i>职业技能</a>
            <php>} else {</php>
            <a href="{:U('/job/setjob')}" class="item"><i class="uc-font uc-add"></i>个人信息</a>
            <php>}</php>
        </div>
    </section>
    <section class="uc-wrap uc-modules">
        <ul class="list">
            <li>
                <div class="name">个人信息</div>
                <a href="{:U('job/setjob')}" class="btn"><img src="/static/job/images/modules-icon-1.png" alt="" class="uc-icon50"></a>
            </li>
            <php>foreach ($userModulesList as $userModulesRow) {</php>
            <li>
                <div class="name">{:$typeList[$userModulesRow['modules_type']]}-{:$userModulesRow['content1']}</div>
                <a href="{:U('job/addmodulesbox', ['type' => $userModulesRow['modules_type'], 'id' => $userModulesRow['relation_id']])}" class="btn"><img src="/static/job/images/modules-icon-1.png" alt="" class="uc-icon50"></a>
            </li>
            <php>}</php>
        </ul>
    </section>
    <script src="/static/job/js/swiper.min.js"></script>
    <script src="/static/job/js/mui.min.js"></script>
    <script src="/static/job/js/mui.picker.min.js"></script>
    <script src="/static/job/js/main.js"></script>
    <script>
        $(function() {})
    </script>
</body>

</html>