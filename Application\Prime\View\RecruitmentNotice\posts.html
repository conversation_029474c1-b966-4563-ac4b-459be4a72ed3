<include file="block/hat" />
<include file="RecruitmentNotice/common_styles" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="recruitment-page-wrapper">
                <div class="recruitment-page-container">
                    <!-- 现代化页面标题 -->
                    <div class="recruitment-page-header">
                        <div class="recruitment-header-content">
                            <div class="recruitment-page-title">
                                <div class="recruitment-title-icon">
                                    <i class="fa fa-users"></i>
                                </div>
                                <div class="recruitment-title-text">
                                    <h1 class="recruitment-title-main">岗位管理</h1>
                                    <p class="recruitment-title-sub">{$notice.title}</p>
                                </div>
                            </div>
                            <div class="recruitment-header-actions">
                                <a href="{:U('index')}" class="recruitment-header-btn btn-secondary">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                                <if condition="!empty($linkedPosts)">
                                    <a href="{:U('batchRequirements', ['notice_id' => $id])}" class="recruitment-header-btn btn-warning">
                                        <i class="fa fa-cogs"></i>
                                        <span>批量配置要求</span>
                                    </a>
                                </if>
                            </div>
                        </div>
                    </div>

                    <!-- 岗位选择容器 -->
                    <div class="recruitment-posts-container">
                        <form method="post" class="recruitment-form js-ajax-form">
                            <div class="form-section">
                                <div class="form-section-title">
                                    <i class="fa fa-check-square-o"></i>
                                    <span>选择岗位</span>
                                </div>

                                <div class="posts-grid">
                                    <volist name="allPosts" id="post">
                                        <div class="post-option">
                                            <label class="post-checkbox">
                                                <input type="checkbox" name="post_ids[]" value="{$post.id}"
                                                    <if condition="in_array($post.id, $linkedPostIds)">checked</if>>
                                                <span class="checkbox-custom"></span>
                                                <div class="post-info">
                                                    <div class="post-name">{$post.job_name}</div>
                                                    <div class="post-price">{$post.service_price}</div>
                                                </div>
                                            </label>
                                        </div>
                                    </volist>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fa fa-save"></i>
                                    <span>保存岗位关联</span>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- 已关联岗位列表 -->
                    <if condition="!empty($linkedPosts)">
                        <div class="recruitment-linked-posts">
                            <div class="linked-posts-header">
                                <div class="linked-posts-title">
                                    <i class="fa fa-list"></i>
                                    <span>已关联岗位</span>
                                    <span class="posts-count">({:count($linkedPosts)})</span>
                                </div>
                            </div>
                    
                            <div class="linked-posts-grid">
                                <volist name="linkedPosts" id="post">
                                    <div class="linked-post-card">
                                        <div class="post-card-header">
                                            <div class="post-card-title">
                                                <i class="fa fa-briefcase"></i>
                                                <span>{$post.job_name}</span>
                                            </div>
                                            <div class="post-card-status">
                                                <if condition="$post.status eq 1">
                                                    <span class="status-badge status-active">
                                                        <i class="fa fa-check"></i> 上架
                                                    </span>
                                                <else />
                                                    <span class="status-badge status-inactive">
                                                        <i class="fa fa-times"></i> 下架
                                                    </span>
                                                </if>
                                            </div>
                                        </div>

                                        <div class="post-card-body">
                                            <div class="post-info-grid">
                                                <div class="post-info-item">
                                                    <div class="info-label">
                                                        <i class="fa fa-money"></i>
                                                        <span>服务价格</span>
                                                    </div>
                                                    <div class="info-value">{$post.service_price}</div>
                                                </div>

                                                <div class="post-info-item">
                                                    <div class="info-label">
                                                        <i class="fa fa-calendar"></i>
                                                        <span>年龄要求</span>
                                                    </div>
                                                    <div class="info-value">
                                                        <if condition="$post.min_age || $post.max_age">
                                                            {$post.min_age|default="不限"} - {$post.max_age|default="不限"}岁
                                                        <else />
                                                            不限
                                                        </if>
                                                    </div>
                                                </div>

                                                <div class="post-info-item">
                                                    <div class="info-label">
                                                        <i class="fa fa-user"></i>
                                                        <span>性别要求</span>
                                                    </div>
                                                    <div class="info-value">
                                                        <switch name="post.sex">
                                                            <case value="1">男</case>
                                                            <case value="2">女</case>
                                                            <default />不限
                                                        </switch>
                                                    </div>
                                                </div>

                                                <div class="post-info-item">
                                                    <div class="info-label">
                                                        <i class="fa fa-graduation-cap"></i>
                                                        <span>学历要求</span>
                                                    </div>
                                                    <div class="info-value">
                                                        <switch name="post.qualification">
                                                            <case value="1">中专及以上</case>
                                                            <case value="2">大专及以上</case>
                                                            <case value="3">本科及以上</case>
                                                            <case value="4">研究生及以上</case>
                                                            <default />不限
                                                        </switch>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="post-card-actions">
                                            <a href="{:U('requirements', ['notice_id' => $id, 'post_id' => $post['id']])}"
                                               class="post-action-btn btn-config">
                                                <i class="fa fa-cog"></i>
                                                <span>配置要求</span>
                                            </a>
                                        </div>
                                    </div>
                                </volist>
                            </div>
                        </div>
                    </if>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__ROOT__/static/js/prime/common.js"></script>
<script>
$(function() {
    // Ajax表单提交
    $('.js-ajax-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        var originalText = $submitBtn.html();
        
        // 检查是否选择了岗位
        var checkedPosts = $form.find('input[name="post_ids[]"]:checked');
        if (checkedPosts.length === 0) {
            layer.msg('请至少选择一个岗位', {icon: 2});
            return;
        }
        
        // 禁用提交按钮
        $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 保存中...');
        
        $.ajax({
            url: $form.attr('action') || window.location.href,
            type: 'POST',
            data: $form.serialize(),
            dataType: 'json',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.status == 1) {
                    layer.msg(response.info, {icon: 1}, function() {
                        if (response.url) {
                            window.location.href = response.url;
                        } else {
                            window.location.reload();
                        }
                    });
                } else {
                    layer.msg(response.info, {icon: 2});
                    $submitBtn.prop('disabled', false).html(originalText);
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
                $submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // 全选/反选
    $('#selectAll').on('change', function() {
        var checked = $(this).prop('checked');
        $('input[name="post_ids[]"]').prop('checked', checked);
    });
});
</script>

<style>
/* 岗位管理页面样式 */
.recruitment-posts-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
    overflow: hidden;
}

.recruitment-form {
    padding: 2rem;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.375rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e2e8f0;
}

.form-section-title i {
    color: #667eea;
    font-size: 1.25rem;
}

/* 岗位选择网格 */
.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.post-option {
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.post-option:hover {
    border-color: #667eea;
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.15);
}

.post-checkbox {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    cursor: pointer;
    margin: 0;
    width: 100%;
}

.post-checkbox input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid #d1d5db;
    border-radius: 0.375rem;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.post-checkbox input[type="checkbox"]:checked + .checkbox-custom {
    background: #667eea;
    border-color: #667eea;
}

.post-checkbox input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
}

.post-info {
    flex: 1;
}

.post-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.post-price {
    font-size: 1rem;
    color: #667eea;
    font-weight: 500;
}

.post-checkbox input[type="checkbox"]:checked ~ .post-info .post-name {
    color: #667eea;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 2rem;
    border-top: 2px solid #e2e8f0;
    margin-top: 2rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-height: 3rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

/* 已关联岗位样式 */
.recruitment-linked-posts {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.linked-posts-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem 2rem;
    border-bottom: 2px solid #e2e8f0;
}

.linked-posts-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.375rem;
    font-weight: 600;
    color: #374151;
}

.linked-posts-title i {
    color: #667eea;
    font-size: 1.25rem;
}

.posts-count {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.linked-posts-grid {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
}

/* 岗位卡片样式 */
.linked-post-card {
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
}

.linked-post-card:hover {
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.post-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.25rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-card-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.post-card-title i {
    font-size: 1.125rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.status-active {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-inactive {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.post-card-body {
    padding: 1.5rem;
}

.post-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.post-info-item {
    background: #f8fafc;
    border-radius: 0.5rem;
    padding: 1rem;
}

.info-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.info-label i {
    color: #667eea;
    width: 1rem;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
}

.post-card-actions {
    padding: 1rem 1.5rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
}

.post-action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-config {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-config:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .posts-grid {
        grid-template-columns: 1fr;
    }

    .linked-posts-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .post-info-grid {
        grid-template-columns: 1fr;
    }

    .post-card-header {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }

    .recruitment-form {
        padding: 1.5rem;
    }

    .linked-posts-header {
        padding: 1rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .post-checkbox {
        padding: 1rem;
    }

    .post-card-header {
        padding: 1rem;
    }

    .post-card-body {
        padding: 1rem;
    }

    .post-card-actions {
        padding: 1rem;
    }

    .recruitment-form {
        padding: 1rem;
    }
}
</style>
